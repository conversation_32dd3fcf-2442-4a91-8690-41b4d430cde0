// Shader created with Shader Forge v1.38 
// Shader Forge (c) <PERSON><PERSON> - http://www.acegikmo.com/shaderforge/
// Note: Manually altering this data may prevent you from opening it in Shader Forge
/*SF_DATA;ver:1.38;sub:START;pass:START;ps:flbk:,iptp:0,cusa:False,bamd:0,cgin:,lico:1,lgpr:1,limd:0,spmd:1,trmd:0,grmd:0,uamb:True,mssp:True,bkdf:False,hqlp:False,rprd:False,enco:False,rmgx:True,imps:True,rpth:0,vtps:0,hqsc:True,nrmq:1,nrsp:0,vomd:0,spxs:False,tesm:0,olmd:1,culm:2,bsrc:0,bdst:0,dpts:2,wrdp:False,dith:0,atcv:False,rfrpo:True,rfrpn:Refraction,coma:15,ufog:True,aust:True,igpj:True,qofs:0,qpre:3,rntp:2,fgom:False,fgoc:True,fgod:False,fgor:False,fgmd:0,fgcr:0,fgcg:0,fgcb:0,fgca:1,fgde:0.01,fgrn:0,fgrf:300,stcl:False,atwp:False,stva:128,stmr:255,stmw:255,stcp:6,stps:0,stfa:0,stfz:0,ofsf:0,ofsu:0,f2p0:False,fnsp:True,fnfb:True,fsmp:False;n:type:ShaderForge.SFN_Final,id:4795,x:32667,y:33918,varname:node_4795,prsc:2|emission-1066-OUT;n:type:ShaderForge.SFN_Color,id:2411,x:31566,y:34602,ptovrint:False,ptlb:Fresnel_InColor,ptin:_Fresnel_InColor,varname:_Color_copy,prsc:2,glob:False,taghide:False,taghdr:True,tagprd:False,tagnsco:False,tagnrm:False,c1:0.5,c2:0.5,c3:0.5,c4:1;n:type:ShaderForge.SFN_Fresnel,id:544,x:31307,y:34668,varname:node_544,prsc:2;n:type:ShaderForge.SFN_Exp,id:7172,x:31307,y:34816,varname:node_7172,prsc:2,et:0|IN-941-OUT;n:type:ShaderForge.SFN_Power,id:4782,x:31566,y:34787,varname:node_4782,prsc:2|VAL-544-OUT,EXP-7172-OUT;n:type:ShaderForge.SFN_ValueProperty,id:3855,x:31793,y:34528,ptovrint:False,ptlb:Fresnel_OutsideIntensity,ptin:_Fresnel_OutsideIntensity,varname:node_137,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:1;n:type:ShaderForge.SFN_Multiply,id:9361,x:31932,y:34668,varname:node_9361,prsc:2|A-2411-RGB,B-4782-OUT;n:type:ShaderForge.SFN_Multiply,id:1706,x:32123,y:34654,varname:node_1706,prsc:2|A-3583-OUT,B-9361-OUT;n:type:ShaderForge.SFN_Tex2d,id:2093,x:30819,y:32387,ptovrint:False,ptlb:MainTex,ptin:_MainTex,varname:node_4222,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False|UVIN-9308-OUT;n:type:ShaderForge.SFN_Tex2d,id:6483,x:30819,y:33018,ptovrint:False,ptlb:MaskTex,ptin:_MaskTex,varname:_node_4222_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False|UVIN-2346-OUT;n:type:ShaderForge.SFN_Multiply,id:8306,x:32184,y:33075,varname:node_8306,prsc:2|A-2093-RGB,B-6483-RGB,C-6463-RGB,D-7162-OUT,E-3549-RGB;n:type:ShaderForge.SFN_Time,id:2199,x:29729,y:32364,varname:node_2199,prsc:2;n:type:ShaderForge.SFN_TexCoord,id:7263,x:29762,y:32188,varname:node_7263,prsc:2,uv:0,uaff:False;n:type:ShaderForge.SFN_Append,id:9308,x:30402,y:32329,varname:node_9308,prsc:2|A-5101-OUT,B-6201-OUT;n:type:ShaderForge.SFN_ComponentMask,id:9263,x:29967,y:32152,varname:node_9263,prsc:2,cc1:0,cc2:-1,cc3:-1,cc4:-1|IN-7263-U;n:type:ShaderForge.SFN_Add,id:5101,x:30174,y:32213,varname:node_5101,prsc:2|A-9263-OUT,B-5613-OUT;n:type:ShaderForge.SFN_Multiply,id:5613,x:29964,y:32317,varname:node_5613,prsc:2|A-2199-TSL,B-4940-OUT;n:type:ShaderForge.SFN_ValueProperty,id:4940,x:29761,y:32554,ptovrint:False,ptlb:U,ptin:_U,varname:node_860,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_ComponentMask,id:1400,x:29964,y:32453,varname:node_1400,prsc:2,cc1:0,cc2:-1,cc3:-1,cc4:-1|IN-7263-V;n:type:ShaderForge.SFN_Add,id:6201,x:30174,y:32360,varname:node_6201,prsc:2|A-1400-OUT,B-447-OUT;n:type:ShaderForge.SFN_Multiply,id:447,x:29964,y:32592,varname:node_447,prsc:2|A-2199-TSL,B-4255-OUT;n:type:ShaderForge.SFN_ValueProperty,id:4255,x:29769,y:32783,ptovrint:False,ptlb:V,ptin:_V,varname:_node_860_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_VertexColor,id:6463,x:30819,y:32742,varname:node_6463,prsc:2;n:type:ShaderForge.SFN_Time,id:8640,x:29737,y:32983,varname:node_8640,prsc:2;n:type:ShaderForge.SFN_TexCoord,id:3639,x:29770,y:32807,varname:node_3639,prsc:2,uv:0,uaff:False;n:type:ShaderForge.SFN_Append,id:2346,x:30367,y:32878,varname:node_2346,prsc:2|A-8762-OUT,B-965-OUT;n:type:ShaderForge.SFN_ComponentMask,id:6347,x:29989,y:32817,varname:node_6347,prsc:2,cc1:0,cc2:-1,cc3:-1,cc4:-1|IN-3639-U;n:type:ShaderForge.SFN_Add,id:8762,x:30196,y:32878,varname:node_8762,prsc:2|A-6347-OUT,B-5935-OUT;n:type:ShaderForge.SFN_Multiply,id:5935,x:29986,y:32982,varname:node_5935,prsc:2|A-8640-TSL,B-8611-OUT;n:type:ShaderForge.SFN_ValueProperty,id:8611,x:29769,y:33173,ptovrint:False,ptlb:U_mask,ptin:_U_mask,varname:_U_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_ComponentMask,id:2046,x:29986,y:33118,varname:node_2046,prsc:2,cc1:0,cc2:-1,cc3:-1,cc4:-1|IN-3639-V;n:type:ShaderForge.SFN_Add,id:965,x:30196,y:33025,varname:node_965,prsc:2|A-2046-OUT,B-7302-OUT;n:type:ShaderForge.SFN_Multiply,id:7302,x:29986,y:33282,varname:node_7302,prsc:2|A-8640-TSL,B-9838-OUT;n:type:ShaderForge.SFN_ValueProperty,id:9838,x:29759,y:33282,ptovrint:False,ptlb:V_mask,ptin:_V_mask,varname:_V_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_Add,id:1066,x:32375,y:34293,varname:node_1066,prsc:2|A-8306-OUT,B-4322-OUT;n:type:ShaderForge.SFN_Color,id:3549,x:30819,y:32586,ptovrint:False,ptlb:MainColor,ptin:_MainColor,varname:node_3549,prsc:2,glob:False,taghide:False,taghdr:True,tagprd:False,tagnsco:False,tagnrm:False,c1:0.5,c2:0.5,c3:0.5,c4:1;n:type:ShaderForge.SFN_Color,id:6525,x:31775,y:34354,ptovrint:False,ptlb:Fresnel_OutColor,ptin:_Fresnel_OutColor,varname:node_6525,prsc:2,glob:False,taghide:False,taghdr:True,tagprd:False,tagnsco:False,tagnrm:False,c1:0.5,c2:0.5,c3:0.5,c4:1;n:type:ShaderForge.SFN_Multiply,id:3583,x:31976,y:34461,varname:node_3583,prsc:2|A-6525-RGB,B-3855-OUT;n:type:ShaderForge.SFN_ValueProperty,id:941,x:31099,y:34816,ptovrint:False,ptlb:Fresnel_InsideIntensity,ptin:_Fresnel_InsideIntensity,varname:_Fresnel_OutsideIntensity_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:1;n:type:ShaderForge.SFN_TexCoord,id:1540,x:29364,y:33732,varname:node_1540,prsc:2,uv:0,uaff:False;n:type:ShaderForge.SFN_RemapRange,id:7168,x:29560,y:33732,varname:node_7168,prsc:2,frmn:0,frmx:1,tomn:-1,tomx:1|IN-1540-UVOUT;n:type:ShaderForge.SFN_Length,id:4527,x:29770,y:33666,varname:node_4527,prsc:2|IN-7168-OUT;n:type:ShaderForge.SFN_Append,id:3640,x:30259,y:33682,varname:node_3640,prsc:2|A-1386-OUT,B-9932-OUT;n:type:ShaderForge.SFN_ComponentMask,id:5205,x:29650,y:33965,varname:node_5205,prsc:2,cc1:0,cc2:1,cc3:-1,cc4:-1|IN-7168-OUT;n:type:ShaderForge.SFN_ArcTan2,id:9932,x:29896,y:33947,varname:node_9932,prsc:2,attp:2|A-5205-R,B-5205-G;n:type:ShaderForge.SFN_Multiply,id:9952,x:30330,y:34026,varname:node_9952,prsc:2|A-7316-TSL,B-9236-OUT;n:type:ShaderForge.SFN_Time,id:7316,x:30118,y:33957,varname:node_7316,prsc:2;n:type:ShaderForge.SFN_ValueProperty,id:9236,x:30142,y:34142,ptovrint:False,ptlb:Speed,ptin:_Speed,varname:node_4550,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0.5;n:type:ShaderForge.SFN_Add,id:1386,x:30023,y:33666,varname:node_1386,prsc:2|A-4527-OUT,B-9952-OUT;n:type:ShaderForge.SFN_Smoothstep,id:8222,x:31631,y:33737,varname:node_8222,prsc:2|A-8416-OUT,B-6307-OUT,V-3758-OUT;n:type:ShaderForge.SFN_OneMinus,id:8416,x:31287,y:33672,varname:node_8416,prsc:2|IN-6307-OUT;n:type:ShaderForge.SFN_ValueProperty,id:6307,x:31121,y:33876,ptovrint:False,ptlb:Smooth,ptin:_Smooth,varname:_Smooth_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0.5;n:type:ShaderForge.SFN_Add,id:485,x:31121,y:34031,varname:node_485,prsc:2|A-1627-RGB,B-1178-OUT,C-9959-OUT,D-6463-A;n:type:ShaderForge.SFN_Vector1,id:1178,x:30589,y:34069,varname:node_1178,prsc:2,v1:1;n:type:ShaderForge.SFN_ValueProperty,id:692,x:30567,y:34246,ptovrint:False,ptlb:Out,ptin:_Out,varname:_Out_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_OneMinus,id:9959,x:30778,y:34122,varname:node_9959,prsc:2|IN-692-OUT;n:type:ShaderForge.SFN_Clamp01,id:3758,x:31331,y:34167,varname:node_3758,prsc:2|IN-485-OUT;n:type:ShaderForge.SFN_Multiply,id:7162,x:31830,y:33400,varname:node_7162,prsc:2|A-2093-A,B-3549-A,C-8222-OUT,D-6463-A;n:type:ShaderForge.SFN_Tex2d,id:1627,x:30685,y:33777,ptovrint:False,ptlb:DissolveTex,ptin:_DissolveTex,varname:_MainTex_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False|UVIN-3640-OUT;n:type:ShaderForge.SFN_VertexColor,id:1484,x:32087,y:34842,varname:node_1484,prsc:2;n:type:ShaderForge.SFN_Multiply,id:4322,x:32314,y:34688,varname:node_4322,prsc:2|A-1706-OUT,B-1484-RGB,C-1484-A;proporder:3549-2093-4940-4255-6483-8611-9838-1627-6307-692-9236-2411-941-6525-3855;pass:END;sub:END;*/

Shader "Shader Forge/Add_FresnelDissolve" {
    Properties {
        [HDR]_MainColor ("MainColor", Color) = (0.5,0.5,0.5,1)
        _MainTex ("MainTex", 2D) = "white" {}
        _U ("U", Float ) = 0
        _V ("V", Float ) = 0
        _MaskTex ("MaskTex", 2D) = "white" {}
        _U_mask ("U_mask", Float ) = 0
        _V_mask ("V_mask", Float ) = 0
        _DissolveTex ("DissolveTex", 2D) = "white" {}
        _Smooth ("Smooth", Float ) = 0.5
        _Out ("Out", Float ) = 0
        _Speed ("Speed", Float ) = 0.5
        [HDR]_Fresnel_InColor ("Fresnel_InColor", Color) = (0.5,0.5,0.5,1)
        _Fresnel_InsideIntensity ("Fresnel_InsideIntensity", Float ) = 1
        [HDR]_Fresnel_OutColor ("Fresnel_OutColor", Color) = (0.5,0.5,0.5,1)
        _Fresnel_OutsideIntensity ("Fresnel_OutsideIntensity", Float ) = 1
    }
    SubShader {
        Tags {
            "IgnoreProjector"="True"
            "Queue"="Transparent"
            "RenderType"="Transparent"
        }
        Pass {
            Name "FORWARD"
            Tags {
                "LightMode"="ForwardBase"
            }
            Blend One One
            Cull Off
            ZWrite Off
            
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            //#define UNITY_PASS_FORWARDBASE
            #include "UnityCG.cginc"
            #pragma multi_compile_fwdbase
            #pragma multi_compile_fog
            //#pragma only_renderers d3d9 d3d11 glcore gles 
            #pragma target 3.0
            uniform float4 _Fresnel_InColor;
            uniform float _Fresnel_OutsideIntensity;
            uniform sampler2D _MainTex; uniform float4 _MainTex_ST;
            uniform sampler2D _MaskTex; uniform float4 _MaskTex_ST;
            uniform float _U;
            uniform float _V;
            uniform float _U_mask;
            uniform float _V_mask;
            uniform float4 _MainColor;
            uniform float4 _Fresnel_OutColor;
            uniform float _Fresnel_InsideIntensity;
            uniform float _Speed;
            uniform float _Smooth;
            uniform float _Out;
            uniform sampler2D _DissolveTex; uniform float4 _DissolveTex_ST;
            struct VertexInput {
                float4 vertex : POSITION;
                float3 normal : NORMAL;
                float2 texcoord0 : TEXCOORD0;
                float4 vertexColor : COLOR;
            };
            struct VertexOutput {
                float4 pos : SV_POSITION;
                float2 uv0 : TEXCOORD0;
                float4 posWorld : TEXCOORD1;
                float3 normalDir : TEXCOORD2;
                float4 vertexColor : COLOR;
                UNITY_FOG_COORDS(3)
            };
            VertexOutput vert (VertexInput v) {
                VertexOutput o = (VertexOutput)0;
                o.uv0 = v.texcoord0;
                o.vertexColor = v.vertexColor;
                o.normalDir = UnityObjectToWorldNormal(v.normal);
                o.posWorld = mul(unity_ObjectToWorld, v.vertex);
                o.pos = UnityObjectToClipPos( v.vertex );
                UNITY_TRANSFER_FOG(o,o.pos);
                return o;
            }
            float4 frag(VertexOutput i, float facing : VFACE) : COLOR {
                float isFrontFace = ( facing >= 0 ? 1 : 0 );
                float faceSign = ( facing >= 0 ? 1 : -1 );
                i.normalDir = normalize(i.normalDir);
                i.normalDir *= faceSign;
                float3 viewDirection = normalize(_WorldSpaceCameraPos.xyz - i.posWorld.xyz);
                float3 normalDirection = i.normalDir;
////// Lighting:
////// Emissive:
                float4 node_2199 = _Time;
                float2 node_9308 = float2((i.uv0.r.r+(node_2199.r*_U)),(i.uv0.g.r+(node_2199.r*_V)));
                float4 _MainTex_var = tex2D(_MainTex,TRANSFORM_TEX(node_9308, _MainTex));
                float4 node_8640 = _Time;
                float2 node_2346 = float2((i.uv0.r.r+(node_8640.r*_U_mask)),(i.uv0.g.r+(node_8640.r*_V_mask)));
                float4 _MaskTex_var = tex2D(_MaskTex,TRANSFORM_TEX(node_2346, _MaskTex));
                float node_8416 = (1.0 - _Smooth);
                float2 node_7168 = (i.uv0*2.0+-1.0);
                float4 node_7316 = _Time;
                float2 node_5205 = node_7168.rg;
                float2 node_3640 = float2((length(node_7168)+(node_7316.r*_Speed)),((atan2(node_5205.r,node_5205.g)/6.28318530718)+0.5));
                float4 _DissolveTex_var = tex2D(_DissolveTex,TRANSFORM_TEX(node_3640, _DissolveTex));
                float3 emissive = ((_MainTex_var.rgb*_MaskTex_var.rgb*i.vertexColor.rgb*(_MainTex_var.a*_MainColor.a*smoothstep( float3(node_8416,node_8416,node_8416), float3(_Smooth,_Smooth,_Smooth), saturate((_DissolveTex_var.rgb+1.0+(1.0 - _Out)+i.vertexColor.a)) )*i.vertexColor.a)*_MainColor.rgb)+(((_Fresnel_OutColor.rgb*_Fresnel_OutsideIntensity)*(_Fresnel_InColor.rgb*pow((1.0-max(0,dot(normalDirection, viewDirection))),exp(_Fresnel_InsideIntensity))))*i.vertexColor.rgb*i.vertexColor.a));
                float3 finalColor = emissive;
                fixed4 finalRGBA = fixed4(finalColor,1);
                UNITY_APPLY_FOG_COLOR(i.fogCoord, finalRGBA, fixed4(0,0,0,1));
                return finalRGBA;
            }
            ENDCG
        }
    }
    CustomEditor "ShaderForgeMaterialInspector"
}
