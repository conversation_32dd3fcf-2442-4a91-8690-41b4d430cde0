// Shader created with Shader Forge v1.38 
// Shader Forge (c) Neat Corporation / <PERSON> - http://www.acegikmo.com/shaderforge/
// Note: Manually altering this data may prevent you from opening it in Shader Forge
/*SF_DATA;ver:1.38;sub:START;pass:START;ps:flbk:,iptp:0,cusa:False,bamd:0,cgin:,lico:1,lgpr:1,limd:1,spmd:1,trmd:0,grmd:0,uamb:True,mssp:True,bkdf:False,hqlp:False,rprd:False,enco:False,rmgx:True,imps:True,rpth:0,vtps:0,hqsc:True,nrmq:1,nrsp:0,vomd:0,spxs:False,tesm:0,olmd:1,culm:2,bsrc:3,bdst:7,dpts:2,wrdp:False,dith:0,atcv:False,rfrpo:True,rfrpn:Refraction,coma:15,ufog:True,aust:True,igpj:True,qofs:0,qpre:3,rntp:2,fgom:False,fgoc:False,fgod:False,fgor:False,fgmd:0,fgcr:0.5,fgcg:0.5,fgcb:0.5,fgca:1,fgde:0.01,fgrn:0,fgrf:300,stcl:False,atwp:False,stva:128,stmr:255,stmw:255,stcp:6,stps:0,stfa:0,stfz:0,ofsf:0,ofsu:0,f2p0:False,fnsp:False,fnfb:False,fsmp:False;n:type:ShaderForge.SFN_Final,id:4013,x:33506,y:32668,varname:node_4013,prsc:2|emission-9279-OUT,alpha-8021-OUT;n:type:ShaderForge.SFN_Tex2d,id:9451,x:32595,y:32908,ptovrint:False,ptlb:Texture,ptin:_Texture,varname:node_9451,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,ntxv:0,isnm:False|UVIN-5314-OUT;n:type:ShaderForge.SFN_Multiply,id:9279,x:33107,y:32743,varname:node_9279,prsc:2|A-6564-OUT,B-9451-RGB,C-7014-RGB;n:type:ShaderForge.SFN_Max,id:6564,x:32725,y:32612,varname:node_6564,prsc:2|A-8-OUT,B-4615-OUT;n:type:ShaderForge.SFN_ValueProperty,id:8,x:32511,y:32612,ptovrint:False,ptlb:Intensity,ptin:_Intensity,varname:node_8,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:1;n:type:ShaderForge.SFN_Vector1,id:4615,x:32497,y:32737,varname:node_4615,prsc:2,v1:1;n:type:ShaderForge.SFN_Time,id:9418,x:31073,y:32244,varname:node_9418,prsc:2;n:type:ShaderForge.SFN_ValueProperty,id:5352,x:31073,y:32409,ptovrint:False,ptlb:Speed,ptin:_Speed,varname:node_5352,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:1;n:type:ShaderForge.SFN_Multiply,id:4295,x:31261,y:32290,varname:node_4295,prsc:2|A-9418-T,B-5352-OUT;n:type:ShaderForge.SFN_Frac,id:8419,x:31456,y:32307,varname:node_8419,prsc:2|IN-4295-OUT;n:type:ShaderForge.SFN_Get,id:5697,x:31456,y:32235,varname:node_5697,prsc:2|IN-4485-OUT;n:type:ShaderForge.SFN_Multiply,id:4158,x:31653,y:32246,varname:node_4158,prsc:2|A-5697-OUT,B-8419-OUT;n:type:ShaderForge.SFN_SwitchProperty,id:3615,x:31822,y:32178,ptovrint:False,ptlb:Switch,ptin:_Switch,varname:node_3615,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,on:False|A-8347-OUT,B-4158-OUT;n:type:ShaderForge.SFN_ValueProperty,id:8347,x:31434,y:32132,ptovrint:False,ptlb:Slider,ptin:_Slider,varname:node_8347,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:1;n:type:ShaderForge.SFN_ValueProperty,id:7515,x:30299,y:32798,ptovrint:False,ptlb:U,ptin:_U,varname:node_7515,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_ValueProperty,id:4053,x:30203,y:32975,ptovrint:False,ptlb:V,ptin:_V,varname:_node_7515_copy,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_Set,id:5601,x:30564,y:32711,varname:x,prsc:2|IN-7515-OUT;n:type:ShaderForge.SFN_Set,id:4796,x:30544,y:33256,varname:y,prsc:2|IN-4053-OUT;n:type:ShaderForge.SFN_Multiply,id:3399,x:30510,y:32901,varname:node_3399,prsc:2|A-7515-OUT,B-4053-OUT;n:type:ShaderForge.SFN_Set,id:4485,x:30702,y:33056,varname:count,prsc:2|IN-3399-OUT;n:type:ShaderForge.SFN_Add,id:7607,x:32151,y:32853,varname:node_7607,prsc:2|A-1235-OUT,B-1767-OUT,C-3153-OUT;n:type:ShaderForge.SFN_Append,id:1235,x:31857,y:32760,varname:node_1235,prsc:2|A-810-OUT,B-2179-OUT;n:type:ShaderForge.SFN_Vector1,id:810,x:31657,y:32692,varname:node_810,prsc:2,v1:0;n:type:ShaderForge.SFN_Multiply,id:2179,x:31683,y:32781,varname:node_2179,prsc:2|A-115-OUT,B-3753-OUT;n:type:ShaderForge.SFN_Get,id:3753,x:31487,y:32847,varname:node_3753,prsc:2|IN-2960-OUT;n:type:ShaderForge.SFN_Subtract,id:115,x:31407,y:32669,varname:node_115,prsc:2|A-5266-OUT,B-5504-OUT;n:type:ShaderForge.SFN_Get,id:5266,x:31127,y:32669,varname:node_5266,prsc:2|IN-2960-OUT;n:type:ShaderForge.SFN_Vector1,id:5504,x:31196,y:32763,varname:node_5504,prsc:2,v1:1;n:type:ShaderForge.SFN_Divide,id:1767,x:31776,y:33014,varname:node_1767,prsc:2|A-3885-OUT,B-7424-OUT;n:type:ShaderForge.SFN_Append,id:3885,x:31508,y:32908,varname:node_3885,prsc:2|A-1016-U,B-5581-OUT;n:type:ShaderForge.SFN_Multiply,id:5581,x:31212,y:32978,varname:node_5581,prsc:2|A-1016-V,B-6086-OUT;n:type:ShaderForge.SFN_TexCoord,id:1016,x:30745,y:32570,varname:node_1016,prsc:2,uv:0,uaff:False;n:type:ShaderForge.SFN_Append,id:7424,x:30758,y:33182,varname:node_7424,prsc:2|A-7515-OUT,B-4053-OUT;n:type:ShaderForge.SFN_ValueProperty,id:6086,x:30802,y:32962,ptovrint:False,ptlb:Size,ptin:_Size,varname:node_6086,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:1;n:type:ShaderForge.SFN_Abs,id:9291,x:31452,y:33152,varname:node_9291,prsc:2|IN-6086-OUT;n:type:ShaderForge.SFN_Multiply,id:4617,x:31776,y:33180,varname:node_4617,prsc:2|A-9291-OUT,B-1043-OUT,C-7925-OUT;n:type:ShaderForge.SFN_Vector1,id:1043,x:31354,y:33407,varname:node_1043,prsc:2,v1:0.1;n:type:ShaderForge.SFN_ValueProperty,id:7925,x:31397,y:33536,ptovrint:False,ptlb:Offest,ptin:_Offest,varname:node_7925,prsc:2,glob:False,taghide:False,taghdr:False,tagprd:False,tagnsco:False,tagnrm:False,v1:0;n:type:ShaderForge.SFN_Append,id:3153,x:32046,y:33159,varname:node_3153,prsc:2|A-1548-OUT,B-4617-OUT;n:type:ShaderForge.SFN_Vector1,id:1548,x:31836,y:33366,varname:node_1548,prsc:2,v1:0;n:type:ShaderForge.SFN_Add,id:5314,x:32331,y:32737,varname:node_5314,prsc:2|A-9747-OUT,B-7607-OUT;n:type:ShaderForge.SFN_ComponentMask,id:7353,x:32299,y:33073,varname:node_7353,prsc:2,cc1:0,cc2:1,cc3:-1,cc4:-1|IN-7607-OUT;n:type:ShaderForge.SFN_Multiply,id:4380,x:32296,y:33299,varname:node_4380,prsc:2|A-4053-OUT,B-7353-G;n:type:ShaderForge.SFN_Clamp01,id:9491,x:32296,y:33422,varname:node_9491,prsc:2|IN-4380-OUT;n:type:ShaderForge.SFN_Frac,id:3573,x:32296,y:33545,varname:node_3573,prsc:2|IN-9491-OUT;n:type:ShaderForge.SFN_Ceil,id:2523,x:32296,y:33695,varname:node_2523,prsc:2|IN-3573-OUT;n:type:ShaderForge.SFN_Multiply,id:8021,x:33129,y:33169,varname:node_8021,prsc:2|A-9451-A,B-2523-OUT,C-5479-A,D-6177-OUT,E-7014-A;n:type:ShaderForge.SFN_VertexColor,id:5479,x:32641,y:33637,varname:node_5479,prsc:2;n:type:ShaderForge.SFN_Min,id:6177,x:32854,y:33020,varname:node_6177,prsc:2|A-8-OUT,B-4615-OUT;n:type:ShaderForge.SFN_Multiply,id:9712,x:31625,y:32438,varname:node_9712,prsc:2|A-2801-OUT,B-9185-OUT;n:type:ShaderForge.SFN_Get,id:2801,x:31353,y:32477,varname:node_2801,prsc:2|IN-4796-OUT;n:type:ShaderForge.SFN_Get,id:9185,x:31386,y:32552,varname:node_9185,prsc:2|IN-5601-OUT;n:type:ShaderForge.SFN_Fmod,id:9675,x:31988,y:32353,varname:node_9675,prsc:2|A-3615-OUT,B-9712-OUT;n:type:ShaderForge.SFN_Fmod,id:682,x:32193,y:32146,varname:node_682,prsc:2|A-3615-OUT,B-1292-OUT;n:type:ShaderForge.SFN_Get,id:1292,x:31988,y:32239,varname:node_1292,prsc:2|IN-5601-OUT;n:type:ShaderForge.SFN_Divide,id:2385,x:32255,y:32288,varname:node_2385,prsc:2|A-9675-OUT,B-7049-OUT;n:type:ShaderForge.SFN_Get,id:7049,x:32079,y:32477,varname:node_7049,prsc:2|IN-5601-OUT;n:type:ShaderForge.SFN_Floor,id:9434,x:32433,y:32115,varname:node_9434,prsc:2|IN-682-OUT;n:type:ShaderForge.SFN_Floor,id:2095,x:32433,y:32322,varname:node_2095,prsc:2|IN-2385-OUT;n:type:ShaderForge.SFN_Set,id:3853,x:32671,y:32086,varname:x_fomde,prsc:2|IN-9434-OUT;n:type:ShaderForge.SFN_Negate,id:1921,x:32592,y:32271,varname:node_1921,prsc:2|IN-2095-OUT;n:type:ShaderForge.SFN_Append,id:7373,x:32870,y:32185,varname:node_7373,prsc:2|A-9434-OUT,B-1921-OUT;n:type:ShaderForge.SFN_Multiply,id:9747,x:33076,y:32252,varname:node_9747,prsc:2|A-2184-OUT,B-7373-OUT;n:type:ShaderForge.SFN_Append,id:2184,x:32907,y:31904,varname:node_2184,prsc:2|A-666-OUT,B-8298-OUT;n:type:ShaderForge.SFN_Reciprocal,id:666,x:32607,y:31778,varname:node_666,prsc:2|IN-3491-OUT;n:type:ShaderForge.SFN_Reciprocal,id:8298,x:32542,y:31935,varname:node_8298,prsc:2|IN-7812-OUT;n:type:ShaderForge.SFN_Get,id:3491,x:32347,y:31793,varname:node_3491,prsc:2|IN-5601-OUT;n:type:ShaderForge.SFN_Get,id:7812,x:32336,y:31919,varname:node_7812,prsc:2|IN-4796-OUT;n:type:ShaderForge.SFN_Set,id:2960,x:32772,y:31690,varname:x_recip,prsc:2|IN-666-OUT;n:type:ShaderForge.SFN_Set,id:8378,x:32715,y:31981,varname:y_recip,prsc:2|IN-8298-OUT;n:type:ShaderForge.SFN_Color,id:7014,x:32918,y:32865,ptovrint:False,ptlb:Color,ptin:_Color,varname:node_7014,prsc:2,glob:False,taghide:False,taghdr:True,tagprd:False,tagnsco:False,tagnrm:False,c1:0.5,c2:0.5,c3:0.5,c4:1;proporder:7014-8-9451-7515-4053-6086-7925-5352-3615-8347;pass:END;sub:END;*/

Shader "CBB/Xulie_uv_Y" {
    Properties {
        [HDR]_Color ("Color", Color) = (0.5,0.5,0.5,1)
        _Intensity ("Intensity", Float ) = 1
        _Texture ("Texture", 2D) = "white" {}
        _U ("U", Float ) = 0
        _V ("V", Float ) = 0
        _Size ("Size", Float ) = 1
        _Offest ("Offest", Float ) = 0
        _Speed ("Speed", Float ) = 1
        [MaterialToggle] _Switch ("Switch", Float ) = 1
        _Slider ("Slider", Float ) = 1
        [HideInInspector]_Cutoff ("Alpha cutoff", Range(0,1)) = 0.5
    }
    SubShader {
        Tags {
            "IgnoreProjector"="True"
            "Queue"="Transparent"
            "RenderType"="Transparent"
        }
        Pass {
            Name "FORWARD"
            Tags {
                "LightMode"="ForwardBase"
            }
            Blend SrcAlpha OneMinusSrcAlpha
            //Cull Off
            ZWrite Off
            
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #define UNITY_PASS_FORWARDBASE
            #include "UnityCG.cginc"
            //#pragma only_renderers d3d9 d3d11 glcore gles 
            #pragma target 3.0
            uniform sampler2D _Texture; uniform float4 _Texture_ST;
            uniform float _Intensity;
            uniform float _Speed;
            uniform fixed _Switch;
            uniform float _Slider;
            uniform float _U;
            uniform float _V;
            uniform float _Size;
            uniform float _Offest;
            uniform float4 _Color;
            struct VertexInput {
                float4 vertex : POSITION;
                float2 texcoord0 : TEXCOORD0;
                float4 vertexColor : COLOR;
            };
            struct VertexOutput {
                float4 pos : SV_POSITION;
                float2 uv0 : TEXCOORD0;
                float4 vertexColor : COLOR;
            };
            VertexOutput vert (VertexInput v) {
                VertexOutput o = (VertexOutput)0;
                o.uv0 = v.texcoord0;
                o.vertexColor = v.vertexColor;
                o.pos = UnityObjectToClipPos( v.vertex );
                return o;
            }
            float4 frag(VertexOutput i, float facing : VFACE) : COLOR {
                float isFrontFace = ( facing >= 0 ? 1 : 0 );
                float faceSign = ( facing >= 0 ? 1 : -1 );
////// Lighting:
////// Emissive:
                float node_4615 = 1.0;
                float x = _U;
                float node_666 = (1.0 / x);
                float y = _V;
                float node_8298 = (1.0 / y);
                float count = (_U*_V);
                half4 node_9418 = _Time;
                half _Switch_var = lerp( _Slider, (count*frac((node_9418.g*_Speed))), _Switch );
                half node_9434 = floor(fmod(_Switch_var,x));
                float x_recip = node_666;
                float2 node_7607 = (float2(0.0,((x_recip-1.0)*x_recip))+(float2(i.uv0.r,(i.uv0.g*_Size))/float2(_U,_V))+float2(0.0,(abs(_Size)*0.1*_Offest)));
                float2 node_5314 = ((float2(node_666,node_8298)*float2(node_9434,(-1*floor((fmod(_Switch_var,(y*x))/x)))))+node_7607);
                fixed4 _Texture_var = tex2D(_Texture,TRANSFORM_TEX(node_5314, _Texture));
                fixed3 emissive = (max(_Intensity,node_4615)*_Texture_var.rgb*_Color.rgb);
                fixed3 finalColor = emissive;
                fixed4 finalRGBA = fixed4(finalColor,(_Texture_var.a*ceil(frac(saturate((_V*node_7607.rg.g))))*i.vertexColor.a*min(_Intensity,node_4615)*_Color.a));
                return finalRGBA;
            }
            ENDCG
        }
    }
    FallBack "Custom/FallBackDefault"
    CustomEditor "ShaderForgeMaterialInspector"
}
