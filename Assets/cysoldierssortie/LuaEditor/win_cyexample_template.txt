local win_cysoldierssortie_template = bc_Class("win_cysoldierssortie_template") --类名用小游戏名加后缀保证全局唯一
win_cysoldierssortie_template.dataSrc = nil --gameobject 上 gameluabehaviour组件数据

local NeeGame = CS.CasualGame.lib_ChuagnYi.NeeG.NeeGame
local GUILayout =CS.UnityEngine.GUILayout
local EditorGUILayout =CS.UnityEditor.EditorGUILayout

function win_cysoldierssortie_template.__init(self, editor)
    self.editor=editor
end

function win_cysoldierssortie_template:OnEnable()

end

function win_cysoldierssortie_template:OnDisable()

end

function win_cysoldierssortie_template:OnGUI()

end

--function win_cysoldierssortie_template:OnHeaderGUI()
--
--end

return win_cysoldierssortie_template