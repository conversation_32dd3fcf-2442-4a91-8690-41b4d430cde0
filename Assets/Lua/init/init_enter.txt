package.cpath = package.cpath .. ';C:/Users/<USER>/AppData/Roaming/JetBrains/Rider2024.3/plugins/EmmyLua/debugger/emmy/windows/x64/?.dll'
local dbg = require('emmy_core')
dbg.tcpConnect('127.0.0.1', 9969)


g_LuaScriptInitFinished = true

function SetLuaScriptInitFinished()
    if LuaScriptInitFinished then
        print("lua script init finished")
        LuaScriptInitFinished()
    end
end

SetLuaScriptInitFinished()

local initPackage = package.loaded["init"]
local clearMoudles = {
    "containers",
    "decoder",
    "descriptor",
    "encoder",
    "listner",
    "protobuf",
    "text_format",
    "type_checkers",
    "wire_format",
    "error_code_pb",
    "mq_common_pb",
    "platform_pb"
}
local clearMoudle
for _, clearMoudle in ipairs(clearMoudles) do
    package.loaded[clearMoudle] = nil
end
package.loaded["init"] = initPackage

-- local hook_require = require "hook_require"
-- hook_require.hook_require()

if jit then
    jit.off()
    jit.flush()
end



local timer_mgr = require "timer_mgr"
timer_mgr:StartUp()

-- 对基础库的扩展,提前 extern 到 cs_call_function 之前，防止 cs_call_function 中引用的模块未能正确引用 extern 中全局的 dump,print等函数
require "extern"
require "fboard_mgr"
local util = require "util"

local console_util = require "console_util"
console_util:InitRequire()
local require_optimize_mgr = require "require_optimize_mgr"
require_optimize_mgr.Init()
if true or not jit then
    
end

-- require("LuaPanda").start("127.0.0.1",8818)

-- 全局函数C#调用
require "cs_call_function"

-- 业务流程开始

local package = package
local require = require
local print = print
local os = os
local CS = CS
local pairs = pairs
local tonumber = tonumber
local tostring = tostring
local dump = dump
local typeof = typeof
local loadstring = loadstring
local SetLuaScriptInitFinished = SetLuaScriptInitFinished
local g_PuzzleGameModule = g_PuzzleGameModule

local Application = CS.UnityEngine.Application
local GameObject = CS.UnityEngine.GameObject
local QualitySettings = CS.UnityEngine.QualitySettings
local Text = CS.UnityEngine.UI.Text

local AkSoundEngineLua = CS.War.Script.AkSoundEngineLua
local IsInEditor = CS.War.Script.Utility.IsInEditor
local AssetBundleManager = CS.War.Base.AssetBundleManager
local IOSystem = require "iosystem_load"
local asset_loader = require "asset_loader"
local Time = CS.UnityEngine.Time
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
local Dispatcher = CS.U3D.Threading.Dispatcher
-- local SystemInfo = CS.UnityEngine.SystemInfo
local Screen = CS.UnityEngine.Screen
local screen_util = require "screen_util"
local SleepTimeout = CS.UnityEngine.SleepTimeout

local Utility = CS.War.Script.Utility
local VersionIsHigher = Utility.VersionIsHigher
if VersionIsHigher then
    local util = require "util"
    local const = require "const"
    Utility.VersionIsHigher = function(left, right)
        local channel_tag = util.GetChannelTag()
        if channel_tag == const.package_name_set.com_q1_hero then
            return VersionIsHigher(left, right)
        end
        return true
    end
end
local util = require "util"
util.GetUTC0TimeStampPlayerPrefs()
-- util.WriteFile(Application.dataPath..'/package_dump.json',Edump(package), "wb")

Screen.sleepTimeout = SleepTimeout.NeverSleep

local event = require "event"
local const = require "const"
const.InitGameID()

local ReviewingUtil = require "ReviewingUtil"
local log = require "login_log"

require "hook_load"

local driverBehaviour = nil

--local LogDebug = CS.War.Base.LogDebug
module("init")

local game_config = require "game_config"
local util = require "util"
if IsInEditor or game_config.ENABLE_Q1_DEBUG_MODE then
    IOSystem.SetEditor(true)
end

local q1sdk = require "q1sdk"
q1sdk.SetSysMemSizeProperties()
game_config.InitChannelID()

local loadedResourceSet = {
    shader = false,
    gamedata = false,
    language = false
}

local baseGameobject = nil
local openPreload = true --是否开启资源预加载

if IsInEditor and AssetBundleManager.SimulateAssetBundleInEditor then
-- openPreload=false
end

log.DirectWarning("cost time,加载lua组件完成:" .. Time.realtimeSinceStartup)
util.StartWatch("init", "lua启动到登录界面显示")
util.StartWatch("start to lobby", "lua启动到大厅界面显示")

local function Startup()
    util.PeekWatch("init", "开始初始化游戏")
    util.AssetBundleManagerTrackEvent(
            "init_game",
            {
                log_begin = 1
            }
    )
    local const = require "const"
    const.LangInitFinish = true

    --print("Startup")
    --设置画质
    require "quality"

    require "server_time"
    --require 'scene_mgr'
    require "music_contorller"
    require "camera"
    require "sort_order"

    -- util.RequireCount()

    --start your logic here
    QualitySettings.vSyncCount = 0
    -- if not game_config.ENABLE_UWA then
    --     Application.targetFrameRate = 30
    -- end

    --Debug.unityLogger.logEnabled = false;

    --local lang_util = require 'lang_util'
    --lang_util.InitChn2LangID()

    local lang = require "lang"
    lang.SetDefaultLanguage()

    if g_PuzzleGameModule == true then
        local ui_window_mgr = require "ui_window_mgr"
        ui_window_mgr:ShowModule("puzzle_game_test")
    else
        require "login_test"
    end

    require "module_on_off"
    -- 上下适配背景
    -- local ui_background = require "ui_background"
    -- ui_background.Show()

    q1sdk.ChannelSDKReport("sdk_page_game_success")

    --先让ui加载启动
    util.DelayCall(0, DelayInitGame)

    if game_config.ENABLE_UWA then
        require "module_gate"
    end

    util.PeekWatch("init", "游戏初始化完成")
    util.AssetBundleManagerTrackEvent(
            "init_game",
            {
                log_begin = 0
            }
    )
    -- util.RequireCount()

    Dispatcher.Initialize()
end

function DelayInitGame()
    util.StartWatch("InitGame", "开始初始化游戏组件")

    util.StartWatch("LoadMap", "开始加载地图")
    -- require场景相关脚本，进行消息监听注册，防止SCENE_LOADED等消息因未注册未收到通知
    -- local new_scene_mgr = require "new_scene_mgr"
    -- local scene_time_mgr = require "scene_time_mgr"
    -- 预加载lobby场景
    local map = require "map"
    map.LoadAdditiveMap(3)

    --加载默认关卡,非必须资源
    -- if not const.OPEN_NEW_HOOK_SCENE then
    --     local laymain_top_scene = require "laymain_top_scene"
    --     laymain_top_scene.LoadScene()
    -- else
    --     local laymain_mgr = require("laymain_mgr")
    --     laymain_mgr.Show(laymain_mgr.HUNT_SCENE)
    --     laymain_mgr.Close()
    -- end
    require "net_reconnect_module"
    --local virtual_server = require "virtual_server"
    --virtual_server.RegModule("hero_add_card_msg_handler")

    util.RegisterConsole(
        "FPS-OpenClose",
            (Application.isEditor or game_config.ENABLE_Q1_DEBUG_MODE) and 1 or 0,
        function(st)
            local fps = GameObject.Find("/UIRoot/CanvasLoading/FPS")
            if fps then
                fps.gameObject:SetActive(st == 1)
            end
        end
    )

    util.RegisterConsole(
        "LuaMem-OpenClose",
            (Application.isEditor or game_config.ENABLE_Q1_DEBUG_MODE) and 1 or 0,
        function(st)
            local luaMem = GameObject.Find("/UIRoot/CanvasLoading/LuaMem")
            if luaMem then
                luaMem.gameObject:SetActive(st == 1)
            end
        end
    )

    if Application.isEditor or game_config.ENABLE_Q1_DEBUG_MODE then
        -- ui_reload.Show()
        local ui_reload = require "ui_reload"
    else
        --搜集启动数据
        --local now = os.time()
        --local timeStr = os.date("%Y%m%d%H%M%S", now)
        --Adjust.addSessionCallbackParameter("setupTime", timeStr)
        --local StartUpEvent = AdjustEvent("8arhca")
        --Adjust.trackEvent(StartUpEvent)
        --local ui_reload = require "ui_reload"
        --ui_reload.Show()
    end
    require "reporter"

    if game_config.ENABLE_FIREBASE and not Application.isEditor then
        local firebase = require "firebase"
        firebase.Start()
    end

    --启动adjust, adjust android 卸载和重装跟踪功能依赖 firebase 的推送，先初始化 firebase
    --国内版本屏蔽adjust
    if game_config.ENABLE_ADJUST and not game_config.Q1SDK_DOMESTIC then
        local adjust = require "adjust"
        adjust.Start()
        --收集设备信息
        adjust.TrackDeviceInfoEvent()
        --收集网络数据
        adjust.TrackNetTypeEvent()
    end

    if game_config.ENABLE_FACEBOOK then
        local facebook = require "facebook"
        facebook.Start()
    end

    if game_config.ENABLE_HUAWEI then
        local huawei = require "huawei"
        huawei.Init()
    end

    if game_config.ENABLE_AIHELP and not Application.isEditor then
        local aihelp = require "aihelp"
        aihelp.Start()
    end
    -- require "cheater"
    if Application.isEditor then
        require "hotfix"
    end

    --appsflyer
    --local appsflyer = require 'appsflyer'
    --appsflyer.Start()

    local ad_mgr = require "ad_mgr"
    ad_mgr.InitSdk()

    InitLuaBehavoiur()
    ModifyConnectTestProperty()
    util.StopWatch("InitGame", "初始化游戏组件完成")

    local money_type_mgr = require "money_type_mgr"
    money_type_mgr.GetCurrencyInfoBySdk()

    local localWorldid = PlayerPrefs.GetInt("worldid")
    event.RecodeTrigger("init_lua_finish", {localWorldid = localWorldid})

    local ui_input_ctrl = require "ui_input_ctrl"
    ui_input_ctrl.Enable()
    local button_scale = require "button_scale"
    button_scale.Enable()
    local ui_sound = require "ui_sound"
    ui_sound.Enable()

    --显示lua内存和加载absize信息
    if game_config.ENABLE_SHOW_MEMROY_INFO then
        local memory_mgr = require "memory_mgr"
        memory_mgr.ShowMemoryInfo()
    end

    --开启内存定时器
    if game_config.ENABLE_MEMORY_TIMER then
        local memory_mgr = require "memory_mgr"
        memory_mgr.StartMemoryTimer()
    end
end

if not Application.isEditor then
    if game_config.ENABLE_BUGLY then
        local bugly = require "bugly"
        bugly.Start()
    end
else
    --- lua调试入口，默认关闭，可在Unity菜单栏Test/LuaMobDebug中打开
    --[[    local useDebug = PlayerPrefs.GetInt("_LuaMobDebug_", 0) == 1
    if useDebug then
        require("mobdebug").start("localhost", "8172")
    end]]
    --require('mobdebug').coro() --debug coroutine
    --local breakSocketHandle,debugXpCall = require("LuaDebug")("localhost",7003)
end

function InitLuaBehavoiur()
    --如果 /StartupCanvas/UIUpdateNode 绑定的 LuaBehavoiur 存在，则由 LuaBehavoiur 组件驱动创建，否则由此处创建
    if not GameObject.Find("/StartupCanvas/UIUpdateNode") then
        -- IOSystem.LoadAssetAsync("driver/driver_behaviour.txt", nil, function (txt)
        --     if not txt or not txt.text then
        --         return
        --     end
        --
        --     local parentName = "/UIRoot/CanvasWithMesh"
        --     local scriptModule = loadstring(txt.text)()
        --     driverBehaviour = scriptModule:New()
        --     driverBehaviour:CreateScriptLoader(parentName, "driver_UIUpdateNode",
        --      "ui/prefabs/uiupdate.prefab", "driver/ui_update/ui_update.txt")
        -- end, "init")
        local loader = asset_loader("ui/prefabs/drivernode.prefab", "init")
        loader:load(
            function(res)
                if not res then
                    return
                end
                local obj = res.asset
                if not obj then
                    return
                end
                local feedbackDataMono = obj:GetComponent(typeof(ScrollRectItem))
                if feedbackDataMono == nil then
                    log.Error("未找到 feedback 配置")
                end
                local driver_txt = feedbackDataMono:Get("driver_behaviour")
                if driver_txt == nil then
                    log.Error("未找到 driver_behaviour 配置")
                end
                local parentName = "/UIRoot/CanvasWithMesh"
                local scriptModule = loadstring(driver_txt.text, driver_txt.name)()
                driverBehaviour = scriptModule:New()
                driverBehaviour:SetDatabase(feedbackDataMono)
                driverBehaviour:CreateLoader(parentName, "driver_UIUpdateNode", "ui/prefabs/uiupdate.prefab", "ui_update")

                SetLuaScriptInitFinished()
            end
        )
    else
        print("start feedback by script node")
    end

    if driverBehaviour then
        driverBehaviour:LuaScriptInitFinished()
    end

    SetLuaScriptInitFinished()
end

function DestroyLuaBehaviour()
    if driverBehaviour then
        driverBehaviour:OnDestroy()
        driverBehaviour = nil
    end
    g_LuaBehaviourModule = nil
end

function LoadedResource(tag)
    log.DirectWarning("LoadedResource", tag)
    util.AssetBundleManagerTrackEvent(
        "loaded_resource",
        {
            loaded_resource_tag = tag
        }
    )
    loadedResourceSet[tag] = true
    local k, v
    for k, v in pairs(loadedResourceSet) do
        if v == false then
            return
        end
    end
    util.AssetBundleManagerTrackEvent(
        "loaded_resource",
        {
            loaded_resource_tag = "allcomplete"
        }
    )
    Startup()
end

function LoadOther()
    local loader = asset_loader("ui/prefabs/uiloginmain.prefab", "init")
    loader:load(
        function()
            util.PeekWatch("init", "预加载登录界面完成")
        end
    )
    -- IOSystem.LoadAssetAsync("ui/prefabs/uiloginmain.prefab", nil ,function ()
    --     util.PeekWatch("init", "预加载登录界面完成")
    -- end, "init")

    util.PeekWatch("init", "登录前资源检测完成")
    local warmup_shaders = require "warmup_shaders"
    warmup_shaders.WarmUp(
        function()
            LoadedResource("shader")
        end
    )
    util.PeekWatch("init", "shader预加载完成")

    util.PeekWatch("init", "开始加载二进制数据文件")

    local game_scheme = require "game_scheme"
    --加载二进制数据文件
    game_scheme:InitGameDatabase(
        function()
            util.PeekWatch("init", "二进制数据文件加载完成")
            LoadedResource("gamedata")

            local device_level_data = require "device_level_data"
            device_level_data.GetDeviceLevelData(
                function()
                    local idle = require "idle"
                    idle.Init()
                end
            )
        end
    )

    local q1sdk = require "q1sdk"
    -- q1sdk.UserSet("systemMemorySize", SystemInfo.systemMemorySize)
    if game_config.ENABLE_BUGLY then
        local userId = q1sdk.GetUUID()
        local bugly = require "bugly"
        bugly.SetUserIdentifier(userId)
    end

    log.DirectWarning("uid:" .. tostring(q1sdk.GetUUID()))

    util.PeekWatch("init", "开始加载语言配置")
    local ui_setting_data = require "ui_setting_data"
    local use_lang = tonumber(ui_setting_data.LoadConfig())
    print(use_lang)
    if use_lang > 0 then
        ui_setting_data.InitLangScheme(
            use_lang,
            function()
                LoadedResource("language")
                --延迟一帧调用,要不然可能会Loop require
                util.YieldFrame(
                    function()
                        local loginMain = require "ui_login_main"
                        loginMain.InitLoginView()
                        log.DirectWarning("延时调用require,避免loop require!!")
                    end
                )
            end
        )
    else
        LoadedResource("language")
    end

    local screen_util = require "screen_util"
    screen_util.Init()

    --开始预加载资源----------------------------------
    -- if const.OPEN_NEW_WINDOW_MGR then
    --     require 'ui_window_mgr2'
    --     package.loaded["ui_window_mgr"] = require "ui_window_mgr2"
    -- else
    -- end
    require "ui_window_mgr"

    local loginMain = require "ui_login_main"
    -- loginMain.ResetPreloadResource()
    -- loginMain.RegisterRreloadAllResource(
    --     loginMain.PreloadAsset,
    --     loginMain.PreloadLuaTable,
    --     loginMain.PreloadInitResource
    -- )
    -- loginMain.PreloadAsset()
    -- loginMain.PreloadLuaTable()
    --预加载资源处理完成------------------------------
    require "scrollrect_table_report"
    event.RecodeTrigger("finish_preload_game_resources", {})

    local fps_report_mgr = require "fps_report_mgr"
    fps_report_mgr.StartFPSCounter()

    local ui_report_mgr = require "ui_report_mgr"
    ui_report_mgr.StartUICounter()

end

function InitLogDebug()
    local log_debug = require "log_debug"
    log_debug.RegisterServerLuaFnc()

    -- log_debug.Response_GetLocalLogFileList(nil, {"GetFileList", "0"})
    -- log_debug.Response_GetLocalLogFile({
    --    relativeID = "1",
    --}, {"GetFileLog", "0", "BattleRecord/2019_09_06_16_58_18.battle"})
    --LogDebug.CompressFile("E:/BC/tools/2019_09_06_16_58_18.battle", "E:/BC/tools/2019_09_06_16_58_18.battle.zip")
    --LogDebug.DecompressFile("E:/BC/tools/2019_09_06_16_58_18.battle.zip", "E:/BC/MGame/InfiniteWar_Svn/Bin/Client/Log/BattleRecord/2019_09_06_16_58_18_1.battle")

    log_debug:RegisterLunarConsoleAction(true)
    log_debug:EnableLogFile(true)
    log_debug:SetMaxCachedCount(3000)
end

function LoadPreloadResources()
    if openPreload then
        local preload = require "preload_resources"
        local game_scheme = require "game_scheme"
        preload.WarmUp(LoadOther)
    else
        LoadOther()
    end
end

--- 检测是否需要下载res_zip
function LoadPreloadResZip()
    if openPreload then
        local preload = require "preload_resources"
        local game_scheme = require "game_scheme"
        preload.CheckLoadResZip(LoadPreloadResources)
    else
        LoadPreloadResources()
    end
end

InitLogDebug()

function InitFilesVersion()
    local files_version_mgr = require "files_version_mgr"
    files_version_mgr.GetStandAloneIsOpen()
    files_version_mgr.Init()
end

InitFilesVersion()

if not Utility.IsInEditor() then
    require "cs_makeup"
end
if true or not ReviewingUtil.IsReviewing() then
    local update_apk = require "update_apk"
    --update_apk.UpdateApk(LoadPreloadResources)
    update_apk.UpdateApk(LoadPreloadResZip)
else
    LoadOther()
end

--AkSoundEngineLua.LoadSoundBankAsync("Scene")

local lua = GameObject.Find("/UIRoot/CanvasLoading/lua")
if lua then
    local uiUti = require "uiUti"

    local scr = uiUti.GetScript(lua, "Text", nil).text
    if scr then
        local luaScr = loadstring(scr)
        luaScr()
    end
end

--直连代理测速参数
function ModifyConnectTestProperty()
    PlayerPrefs.SetInt("ConnectTest_switch", 0) --开关（1：开 0：关）
    PlayerPrefs.SetInt("ConnectTest_coolDown", 3 * 24 * 3600) --上报冷却时间（单位：秒）

    PlayerPrefs.SetInt("ConnectTest_ParallelismNum", 1) --并发
    PlayerPrefs.SetInt("ConnectTest_totalNum", 5) --统计次数
    PlayerPrefs.SetInt("ConnectTest_validNum", 3) --有效次数
    PlayerPrefs.SetInt("ConnectTest_timeOut", 10000) --超时时间（单位：毫秒）
end

function InitMsgCollection()
    local msg_log = require "msg_log"
    msg_log.Init()
end

InitMsgCollection()

function InitTrackEvent()
    local track_event_mgr = require "track_event_mgr"
    track_event_mgr.Init()
end

InitTrackEvent()

function RegisterActionConsole()
    util.RegisterLunarConsoleOpenHandler(util.ShowDebugWin)
end

RegisterActionConsole()
