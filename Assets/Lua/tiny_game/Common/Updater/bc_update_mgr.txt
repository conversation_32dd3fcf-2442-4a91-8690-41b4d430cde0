local Updater = bc_Class("Updater", bc_BaseModule)

--创建
function Updater:Create()
    self.timerPools = {}
    self.timerRuns = {}
    self.maxTimerHandle = 0
end

--销毁
function Updater:Dispose()
    self:Reset()
end

function Updater:Reset()
    self.timerPools = {}
    self.timerRuns = {}
    self.maxTimerHandle = 0
end

--启动
function Updater:Start()
end

--停止
function Updater:Stop()
end

function Updater:GetCallInfoCount()
    return self.eventCallCountInfo or {}
end

--更新
function Updater:Update()
    for k,timer in pairs(self.timerRuns) do
        if timer.bRun then
            if self.onErrorCallBack ==nil then
                self.onErrorCallBack = function(msg)
                    timer.bRun = false
                    bc_Logger.Error("帧更新回调异常："..msg)
                end
            end
            self.success = xpcall(timer.callback ,self.onErrorCallBack, timer.timerID)
        else
            self:ReleaseTimerNode(timer)
            self.timerRuns[k] = nil
        end
    end
end

function Updater:SetUpdater(callback, timerID, desc)
    return self:SetTimerEx(callback, timerID, desc)
end

--callback形式是 OnTimer(timerID),返回是timerhandle
function Updater:SetTimerEx(callback, timerID, desc)
    if not callback then
        bc_Logger.Error("Updater:SetTimer>> callback is nil. desc="..tostring(desc))
        return 0
    end

    if not desc then
        bc_Logger.Error("Updater:SetTimer>> desc is nil.")
        return 0
    end

    self.maxTimerHandle = self.maxTimerHandle + 1
    local timer = self:CreateTimerNode()
    timer.callback = callback
    timer.timerID = timerID
    timer.desc = desc
    timer.handle = self.maxTimerHandle
    timer.bRun = true
    self.timerRuns[timer.handle] = timer
    return timer.handle
end


--删除定时器
function Updater:KillUpdater(handle)
    local timer = self.timerRuns[handle]
    if nil~=timer then
        timer.bRun = false
    end

end

--获取个数
function Updater:Count()
    local count = 0
    for k,v in pairs(self.timerPools) do
        count = count+1
    end
    return count
end

--创建一个定节点
function Updater:CreateTimerNode()
    for k,v in pairs(self.timerPools) do
        self.timerPools[k] = nil
        return v
    end
    return {}
end

--删除一个定节点
function Updater:ReleaseTimerNode(timer)
    timer.callback = nil
    self.timerPools[timer.handle] = timer
end

--获取正在进行的计时器
function Updater:GetRunningTimers()
    local timers = {}
    for k,timer in pairs(self.timerRuns) do
        if timer.bRun then
            table.insert(timers,timer)
        end
    end
    return timers
end

return Updater
