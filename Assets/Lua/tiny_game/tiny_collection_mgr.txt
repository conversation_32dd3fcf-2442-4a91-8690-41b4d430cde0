-- tiny_collection_mgr.txt ------------------------------------------
-- author:  zhuzidian
-- date:    2023.09.13
-- ver:     1.0
-- desc:    小游戏合集
--------------------------------------------------------------
local print = print
local require = require
local pairs = pairs
local ipairs = ipairs
local table = table
local tostring = tostring

local hashRemote = CS.War.Base.AssetBundleManager.hashRemote

local files_version_mgr = require "files_version_mgr"
local json = require "dkjson"

module("tiny_collection_mgr")

local resKeyInPackage = {}
local standAloneResKeyInPackage = {}
local resKeyInDownload = {}
local standAloneResKeyInDownload = {}
local id = 0

function Init()
    print("zzd____tiny_collection_mgr____Init()")
    GetCollectionConfigByHashRemote()
    print("zzd____tiny_collection_mgr____Init()___")
end

--小游戏合集列表获取
function GetCollectionGameKey(callback)
    local collection_res_key = files_version_mgr.GetCollectionResKey()
    if not collection_res_key then
        print("collection_res_key == null")
        return nil
    end

    callback(id,resKeyInPackage,standAloneResKeyInPackage,resKeyInDownload,standAloneResKeyInDownload)
end

function GetCollectionConfigByHashRemote()
    local collection_res_key = files_version_mgr.GetCollectionResKey()
    if collection_res_key then
        if hashRemote and hashRemote.collection2mark then
            local collectionConfigNotNil,collectionConfig = hashRemote.collection2mark:TryGetValue(collection_res_key);
            if collectionConfigNotNil then
                local idConfigNotNil, idConfig = collectionConfig:TryGetValue("id")
                if idConfigNotNil then
                    id = json.decode(tostring(idConfig))
                    print("zzd____json id",id)
                end

                resKeyInDownload,standAloneResKeyInDownload = GetCollectionResKey(collectionConfig,"download")

                --for i,v in pairs(resKeyInDownload) do
                --    print("zzd____resKeyInDownload",i,v)
                --end
                --for i,v in pairs(standAloneResKeyInDownload) do
                --    print("zzd____standAloneResKeyInDownload",i,v)
                --end
                resKeyInPackage,standAloneResKeyInPackage = GetCollectionResKey(collectionConfig,"package")
                --for i,v in pairs(resKeyInPackage) do
                --    print("zzd____resKeyInPackage",i,v)
                --end
                --for i,v in pairs(standAloneResKeyInPackage) do
                --    print("zzd____standAloneResKeyInPackage",i,v)
                --end
            else
                print("zzd____collectionConfig Error")
            end
        else
            print("zzd____hashRemote or hashRemote.collection2mark is nil")
        end
    end
end

function GetCollectionResKey(curCollectionConfig,downloadOrPackage)
    local normalResKeys = {} --非独立热更小游戏
    local standAlongResKeys = {} --独立热更小游戏
    local downloadConfigNotNil, downloadConfig = curCollectionConfig:TryGetValue(downloadOrPackage)
    if downloadConfigNotNil then
        local downloadResKeyNotNil, downloadResKey = downloadConfig:TryGetValue("res_key")
        if downloadResKeyNotNil then
            --print("zzd____downloadResKey",downloadResKey)
            local resKeys = json.decode(tostring(downloadResKey))
            for i,value in ipairs(resKeys) do
                --print("zzd____reskeys",i,value)
                table.insert(normalResKeys,value)
            end
        end
        local standAlongResKeyNotNil, standAlongResKey = downloadConfig:TryGetValue("stand_alone")
        if standAlongResKeyNotNil then
            local resKeys = json.decode(tostring(standAlongResKey))
            for i,value in ipairs(resKeys) do
                --print("zzd____standAlong ResKey",i,value)
                table.insert(standAlongResKeys,value)
            end
        end
    end
    return normalResKeys,standAlongResKeys
end

function GetResKeyInDownload()
    return resKeyInDownload
end

function GetResKeyInPackage()
    return resKeyInPackage
end

Init()