--- Created by fgy.
--- DateTime: 2024/10/31 12:09
--- Des:城际货车管理类

local require = require
local main_slg_data = require "main_slg_data"
local gw_const = require "gw_const"
local event = require "event"
local sand_ui_event_define = require "sand_ui_event_define"
local ui_window_mgr = require("ui_window_mgr")
local net_sandbox_module = require "net_sandbox_module"
local gw_common_util = require "gw_common_util"
local util = require "util"
local flow_text = require("flow_text")
local lang = require "lang"
local GWG = GWG
local M = {}

---@see 初始化
function M.Init()

end

local function EnterTruckScene(openPage, selectTruck)
    event.Trigger(sand_ui_event_define.GW_CLOSE_SAND_MAIN)
    local net_carriage_module = require "net_carriage_module"
    net_carriage_module.MSG_CARRIAGE_LOOT_DATA_REQ()
    net_carriage_module.MSG_CARRIAGE_MINE_DATA_REQ()
    local intercity_trucks_scene = require("intercity_trucks_scene")
    M.scene = intercity_trucks_scene.new("intercity_trucks_mgr")
    intercity_trucks_scene.SetCurTabAndSelectId(openPage or 1, selectTruck)
    M.scene:InitScene()
    ui_window_mgr:ShowModule("ui_intercity_trucks_main", nil, nil, openPage or 1)
    main_slg_data.SetCurSceneType(gw_const.ESceneType.Truck)
end

---@param openPage number 打开他人还是自己 1/2 他人/自己
---@param selectTruck number 选中第几个货车
function M.OpenTrucksScene(openPage, selectTruck)
    if M.scene then
        return
    end
    local gw_mgr = require "gw_mgr"
    M.lastScene = gw_mgr.GetFsmCurScene()

    gw_common_util.SwitchToTruck(function()
        EnterTruckScene(openPage, selectTruck)
    end)
end

function M.CloseAll()
    if M.scene then
        M.scene:Dispose()
        M.scene = nil
    end
    M.CloseAllUI()
    event.Trigger(sand_ui_event_define.GW_OPEN_SAND_MAIN)

    if M.CloseFunc then
        M.CloseFunc()
        M.CloseFunc = nil
    end
end

function M.CloseAllUI()
    ui_window_mgr:UnloadModule("ui_intercity_trucks_main")
    ui_window_mgr:UnloadModule("ui_intercity_trucks_lit_tip")
    ui_window_mgr:UnloadModule("ui_alliance_train_lit_tip")
    ui_window_mgr:UnloadModule("uintercity_trucks_detail")
    ui_window_mgr:UnloadModule("uintercity_trucks_history")
    ui_window_mgr:UnloadModule("ui_intercity_trucks_departure")
    ui_window_mgr:UnloadModule("ui_chat_main_new")
end

function M.ShowBattle()
    ui_window_mgr:CloseAll()
    event.Trigger(sand_ui_event_define.GW_CLOSE_SAND_MAIN)
end

function M.ShowBattleComplete()
    local sceneType = main_slg_data.GetCurSceneType()
    if sceneType == gw_const.ESceneType.Truck then
        ui_window_mgr:ShowModule("ui_intercity_trucks_main")
    else
        event.Trigger(sand_ui_event_define.GW_OPEN_SAND_MAIN)
    end
end

function M.Dispose(callFunc)
    M.CloseFunc = callFunc
    if M.lastScene == gw_const.ESceneType.Sand then
        gw_common_util.SwitchToSand(nil, nil, nil, true)
    elseif M.lastScene == gw_const.ESceneType.Home then
        gw_common_util.SwitchToHome(nil, true)
    end
end

function M.SwitchToHomeAndJumpScience(callBackFunc)
    --main_slg_data.SetCurSceneType(gw_const.ESceneType.Home)
    gw_common_util.SwitchToHome(callBackFunc, nil)
   -- GWG.GWHomeMgr.EnterHomeScene()
   -- if callBackFunc then
   --     callBackFunc()
   -- end
end

function M.SwitchToSand(unitid, sandboxid)
    net_sandbox_module.MSG_SANDBOX_CARRIAGEPOS_REQ(unitid, sandboxid)
    M.reqUnitId = unitid
    local jumpEvent = function(_, msg)
        if msg.sid == M.reqUnitId then
            if msg.pos then
                gw_common_util.JumpToGrid(msg.pos, function()
                    M.CloseAll()
                    util.DelayCallOnce(0.2, function()
                        net_sandbox_module.MSG_SANDBOX_GET_DETAIL_REQ(msg.sid)
                    end)
                end)
            else
                --提示游荡怪已经死亡
                flow_text.Add(lang.Get(100000 + msg.err))
            end
        end
    end
    event.RegisterOnce(sand_ui_event_define.GW_SAND_SEARCH_CARRIAGE_DATA_Get, jumpEvent)
end

function M.JumpToSandAndToPos(pos)
    gw_common_util.JumpToGrid(pos, nil, function()
        M.CloseAll()
    end)
end

---@param data table {hideTim-特效隐藏时间,callFunc-show回调,callTime-callFunc回调时间}
function M.ShowMoveEff(data)
    if ui_window_mgr:IsUIExist("ui_full_screen_effect_panel") then
        local ui_full_screen_effect_panel = require("ui_full_screen_effect_panel")
        ui_full_screen_effect_panel.OnShow(data.hideTim or 3, data.callFunc, data.callTime or 1)
    else
        ui_window_mgr:ShowModule("ui_full_screen_effect_panel", nil, nil, { showTime = data.hideTim or 3, data.callFunc, data.callTime or 1 })
    end
end

return M