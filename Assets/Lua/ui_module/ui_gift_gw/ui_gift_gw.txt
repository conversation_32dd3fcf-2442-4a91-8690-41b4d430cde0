---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON><PERSON>ua)
--- Created by duyun<PERSON>.
--- DateTime: 2024/10/7 20:35
---
local require = require
local idle = require "idle"
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable
local pairs = pairs
local ipairs = ipairs
local typeof = typeof
local string = string
local tostring = tostring
local tonumber = tonumber

local ScrollRect = CS.UnityEngine.UI.ScrollRect
local net_login_module = require "net_login_module"
local cfg_util = require "cfg_util"
local math = require "math"
local goods_item = require "goods_item_new"
local util = require "util"
local log = require "log"

local game_scheme = require "game_scheme"
local lang = require "lang"

module("ui_gift_gw")
---@class ui_gift_gw
local M = {}

------------------------------------------------- 常量定义 START -------------------------------------------------
local _PREFAB_PATH = "ui/prefabs/gw/gw_sourcesupply/gift_scrollview_gw.prefab"
------------------------------------------------- 常量定义 END -------------------------------------------------
M.widget_table = {
    Scroll_GiftViewport_Content = { path = "GiftViewport/Auto_Content", type = ScrollRectTable, event_name = "" },
}

---构造函数
function M:ctor(selfType)
    self.__base:ctor(selfType)
    self.giftDataTmp = {}
    self.length = 0
    self.scale = 1
    self.goodsId = 0
    self.curOrder = 0

end

function M:InitUI(scale)
    if initStartCallback then
        initStartCallback(self)
    end
    -- 重置
    self:Reset()
    -- 设置缩放
    self:SetItemScale(scale)

    self:RegisterEvent()

    if self.isHide then
        self.gameObject:SetActive(false)
    end

    self:UpdateGiftList(self.giftDataTmp, self.length)
end

--- 设置物品缩放
---@param scale number
function M:SetItemScale(scale)
    if not self.UIRoot then
        return
    end

    self.transform.localScale = { x = scale, y = scale, z = scale }
end

--初始化
function M:Init(parentTrans, scale, goodsId, bgAlpha)
    self.scale = (scale or 1)
    self.goodsId = goodsId

    self.giftDataTmp, self.length = self:GetGiftData(goodsId)

    -- 物品加载成功回调
    local loadCallback = function()
        self:InitUI(self.scale, bgAlpha)
    end
    -- 加载
    self:LoadResource(_PREFAB_PATH, "", loadCallback, idle.ITEM_IO, parentTrans)

    return self
end

--- 重置
function M:Reset()

end

--- 丢弃
function M:Dispose()
    if self:IsValid() then
        self:UnregisterEvent()
        if self.Scroll_GiftViewport_Content then
            self.Scroll_GiftViewport_Content:ItemsDispose()
        end
        if self.countTimer then
            util.RemoveDelayCall(self.countTimer)
        end
    end

    self.giftDataTmp = nil
    self.length = 0
    self.goodsId = 0
    self.curOrder = 0

    self.___prevent = nil
    self.__base:Dispose()
end

--- 注册事件
function M:RegisterEvent()
    -- 礼包列表
    self.Scroll_GiftViewport_Content.onItemRender = function(...)
        self:OnGiftListItemRender(...)
    end
    self.Scroll_GiftViewport_Content.onItemDispose = function(...)
        self:OnGiftListItemDispose(...)
    end
end

--- 反注册事件
function M:UnregisterEvent()

end

--- 启用/禁用
function M:SetActive(isVisible)
    if self.gameObject then
        self.gameObject:SetActive(isVisible)
    else
        self.isHide = not isVisible
    end
end

--- 刷新礼包列表
function M:UpdateGiftList(data, len)
    self.Scroll_GiftViewport_Content:SetData(data, len or #data)
    self.Scroll_GiftViewport_Content:Refresh(0, -1)
    -- 礼包大于2个才开启滑动
    self.gameObject:GetComponent(typeof(ScrollRect)).enabled = (len > 2)
end

--- 获取倒计时文本
---@param giftType string
---@param remainingTime number
---@return string
local function GetCountdownText(giftType, remainingTime)
    -- 剩余时间大于1天
    local template = nil
    if (remainingTime > 86400) then
        -- 天 小时
        template = ("#D " .. lang.Get(36061) .. " #H " .. lang.Get(15801))
    else
        -- 时 分 秒
        template = "#H:#M:#S"
    end
    local text = util.FormatTime2(remainingTime, template)

    -- 特惠礼包显示礼包过期时间
    if (giftType == GiftType.SpecialGift) then
        --return (lang.Get(209) .. text)     -- "剩余时间："
        return text
    end

    -- 日周月礼包显示刷新时间
    --return string.format(lang.Get(80313), text) -- "%s后刷新"
    return text
end

local _GiftItemBgIndex = {
    Yellow = 0,
    Blue = 1,
}

local _CountdownBgIndex = {
    Green = 0,
    Blue = 1,
}

local _CountdownTextColor = {
    Green = { r = 135 / 255, g = 244 / 255, b = 37 / 255, a = 1 },
    Blue = { r = 91 / 255, g = 203 / 255, b = 255 / 255, a = 1 },
}

local _BuyBtnTextColor = {
    Normal = { r = 255 / 255, g = 254 / 255, b = 202 / 255, a = 1 },
    Gray = { r = 255 / 255, g = 255 / 255, b = 255 / 255, a = 1 },
}

local _BuyBtnTextOutlineColor = {
    Normal = { r = 153 / 255, g = 51 / 255, b = 0 / 255, a = 1 },
    Gray = { r = 90 / 255, g = 90 / 255, b = 90 / 255, a = 1 },
}

--- 礼包类型
GiftType = {
    --- 普通礼包（日周月）
    ActivityContent = "ActivityContent",
    --- 特惠礼包
    SpecialGift = "SpecialGift",
    --- 每日印记&材料礼包
    DailyMaterialRewardGift = "DailyMaterialRewardGift",
}

--获取关联礼包剩余倒计时
local function GetRemainingTime(timeStamp)
    local remainingTime = timeStamp - net_login_module.GetServerTime() --获取服务器时间
    remainingTime = math.max(math.floor(remainingTime), 0)
    return remainingTime
end

---OnGiftListItemRender
---礼包列表元素刷新
function M:OnGiftListItemRender(item, index, data)
    --log.Log("ff:OnGiftListItemRender" .. index)

    item.data = item.data or { index = 0, data = nil, goodsItems = {}, countdownTicker = nil }
    item.data.index = index
    item.data.data = data
    -- 组件
    local bgSS = item:Get("BgSS")
    local nameText = item:Get("NameText")
    local goodsListScrollRect = item:Get("GoodsListScrollRect")
    local goodsListContent = item:Get("GoodsListContent")
    local tag = item:Get("Tag")
    local tagText = item:Get("TagText")
    local countdownSS = item:Get("CountdownSS")
    local countdownText = item:Get("CountdownText")
    local limitText = item:Get("LimitText")
    local buyBtn = item:Get("BuyBtn")
    local buyBtnSS = item:Get("BuyBtnSS")
    local buyBtnText = item:Get("BuyBtnText")
    -- 设置
    local contentCfg = data.contentCfg
    local giftType = data.type
    local purchaseLimit = 0
    local purchasedTimes = 0
    local endTime = 0
    if (giftType == GiftType.ActivityContent) then
        -- 日周月礼包
        nameText.text = lang.Get(tonumber(contentCfg.remark))   -- 名称
        tag.gameObject:SetActive(false) -- 标签
        purchaseLimit = contentCfg.LimitNumber  -- 限购次数
        -- 数据
        local contentData = data.contentData
        local activityData = data.activityData
        purchasedTimes = contentData and contentData.getNumber or 0
        endTime = activityData and activityData.endTime or 0
    elseif (giftType == GiftType.DailyMaterialRewardGift) then
        -- 每日印记&材料礼包
        nameText.text = lang.Get(tonumber(contentCfg.remark))   -- 名称
        tag.gameObject:SetActive(false)  -- 标签
        purchaseLimit = contentCfg.LimitNumber  -- 限购次数
        -- 数据
        local contentData = data.contentData
        purchasedTimes = contentData and contentData.getNumber or 0
        endTime = data.endTime
    elseif (giftType == GiftType.SpecialGift) then
        -- 特惠礼包
        local specialGiftCfg = data.specialGiftCfg
        nameText.text = lang.Get(specialGiftCfg.Name)   -- 名称
        purchaseLimit = data.buyLimit or specialGiftCfg.LimitedBuy.data[0]  -- 限购次数
        -- 标签
        local showTag = cfg_util.IsValidString(specialGiftCfg.GiftValue)
        tag.gameObject:SetActive(showTag)
        if showTag then
            tagText.text = string.format("%s\n%s", lang.Get(9185), specialGiftCfg.GiftValue)
            tagText.fontSize = 18
            tagText.transform.sizeDelta = { x = 50, y = 50 }
        end
        -- 数据
        local specialGiftData = data.specialGiftData
        purchasedTimes = specialGiftData and specialGiftData.singlePurchaseNum or 0 --改为当批次的购买次数
        endTime = specialGiftData and specialGiftData.time or 0
    end

    -- 是否可以购买
    local hasLimit = (purchaseLimit <= 15)
    local canBuy = ((not hasLimit) or (purchasedTimes < purchaseLimit))

    -- 背景样式
    if (index == 1) and canBuy then
        bgSS:Switch(_GiftItemBgIndex.Yellow)
    else
        bgSS:Switch(_GiftItemBgIndex.Blue)
    end

    -- 倒计时样式
    if (giftType == GiftType.SpecialGift) then
        countdownSS:Switch(_CountdownBgIndex.Green)
        countdownText.color = _CountdownTextColor.Green
    else
        countdownSS:Switch(_CountdownBgIndex.Blue)
        countdownText.color = _CountdownTextColor.Blue
    end
    -- 倒计时文本大小
    if (lang.USE_LANG == lang.EN) or (lang.USE_LANG == lang.RU) then
        countdownText.fontSize = 14
    else
        countdownText.fontSize = 16
    end

    ---- 取消旧的倒计时
    --if item.data.countdownTicker then
    --    self:RemoveTimer(item.data.countdownTicker)
    --    item.data.countdownTicker = nil
    --end
    -- 设置倒计时
    local remainingTime = 0

    if (endTime > 0) then
        remainingTime = GetRemainingTime(endTime)

    end
    countdownSS.gameObject:SetActive(remainingTime > 0)
    if (remainingTime > 0) then
        countdownText.text = GetCountdownText(giftType, remainingTime)

        self.countTimer = util.IntervalCall(1, function()
            if self:IsValid() then
                remainingTime = GetRemainingTime(endTime)
                countdownText.text = GetCountdownText(giftType, remainingTime)
            else
                return true
            end
        end)
        --item.data.countdownTicker = self:CreateTimer(1,
        --        function()
        --            remainingTime = GetRemainingTime(endTime)
        --            countdownText.text = GetCountdownText(giftType, remainingTime)
        --        end)

    end

    -- 限购次数
    limitText.gameObject:SetActive(hasLimit ~= nil)
    if hasLimit then
        local limitStr = (tostring(purchasedTimes) .. "/" .. tostring(purchaseLimit))
        --limitText.text = string.format(lang.Get(81783), limitStr)
        limitText.text = limitStr
    end

    -- 购买按钮
    buyBtnSS:Switch(canBuy and 0 or 1)
    buyBtnText.color = canBuy and _BuyBtnTextColor.Normal or _BuyBtnTextColor.Gray
    buyBtnText.curOutlineColor = canBuy and _BuyBtnTextOutlineColor.Normal or _BuyBtnTextOutlineColor.Gray
    buyBtnText.text = canBuy and data.MoneyStrByGoodsID or lang.Get(15128)   -- "已售罄"

    -- 物品图标
    local goodsItems = item.data.goodsItems
    local rewardIDs = data.rewardIDs
    local count = math.max(#goodsItems, rewardIDs.count)

    if goodsItems then
        for i, v in ipairs(goodsItems) do
            if v then
                v:Dispose()
            end
        end
    end

    for i = 1, count do
        local goodsItem = goodsItems[i]
        local rewardID = rewardIDs.data[i]
        if rewardID then
            -- 展示/生成物品图标
            if goodsItem and not util.IsObjNull(goodsItem.gameObject) then
                goodsItem.gameObject:SetActive(true)
            else
                goodsItem = goods_item.CGoodsItem()
                goodsItem:Init(goodsListContent, nil, 0.5)
                goodsItems[i] = goodsItem
            end
            -- 物品配置
            local rewardCfg = game_scheme:Reward_0(rewardID)
            if rewardCfg then
                local goodsID = rewardCfg.arrParam[0]
                local goodsNum = rewardCfg.arrParam[1]
                goodsItem:SetGoods(nil, goodsID, goodsNum, data.ShowItemDetailFunc, rewardID)
                goodsItem:SetCountEnable(util.PriceConvertGiftDiamond(goodsNum))
                goodsItem:GoodsEffectEnable(true, self.curOrder + 1, 1, 0.45)
                goodsItem:SetFrameBg(3)
            else
                log.Error("PP*[ui_source_list_v3] 奖励配置异常！| Reward.csv > rewardID: " .. tostring(rewardID))
            end
        elseif goodsItem then
            -- 隐藏物品图标
            goodsItem.gameObject:SetActive(false)
        end
    end
    -- 物品列表滚动
    goodsListScrollRect.enabled = (rewardIDs.count > 4)
    goodsListScrollRect.normalizedPosition = { x = 0, y = 0 }   -- 恢复到初始位置

    -- 按钮回调
    local events = data.BtnFunc
    item.InvokeFunc = function(name)
        local func = events[name]
        if func then
            func()
        end
    end
end

function M:OnGiftListItemDispose(item, index)
    if (not item.data) then
        return
    end

    --if item.data.countdownTicker then
    --    self:RemoveTimer(item.data.countdownTicker)
    --    item.data.countdownTicker = nil
    --end

    if item.data.goodsItems then
        for i, v in pairs(item.data.goodsItems) do
            if v then
                v:Dispose()
            end
        end
        item.data.goodsItems = nil
    end
    item.data = nil
end



local class = require "class"
local base_game_object = require "base_game_object"
---@return ui_gift_gw
CGiftItem = class(base_game_object, nil, M)
