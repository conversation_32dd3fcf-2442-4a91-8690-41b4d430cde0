--- ui_radar_environment_tips_controller.txt
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by .mr
--- DateTime: 
--- desc:    
---
local require = require
local newclass = newclass

local sandbox_ui_mgr = require "sandbox_ui_mgr"
local sand_mark_data = require "sand_mark_data"
local sand_ui_event_define = require "sand_ui_event_define"
local radar_data = require "radar_data"
local lang = require "lang"
local sand_ui_data = require "sand_ui_data"
local module_scroll_list = require "scroll_list"
local controller_base = require "controller_base"
local game_scheme = require "game_scheme"
local windowMgr = require "ui_window_mgr"
module("ui_radar_environment_tips_controller")
local controller = nil
local UIController = newclass("ui_radar_environment_tips_controller", controller_base)

--[[窗口初始化]]
function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self, view_name, controller_name)

    if data == nil then
        return
    end
    self.entity = data

    --region 通用按钮设置
    local curMarkData = sand_mark_data.FindMarkExistByPos(self.entity.pos)
    --local AllianceMgr = require "alliance_mgr"
    --self.isHasAlliancePower = AllianceMgr.CanSetGatherPoint()
    --self.isShowDesc = false     --是否显示描述，默认不显示
    --self:TriggerUIEvent( "SetDesc", self.isShowDesc)
    local isFavorite
    if curMarkData then
        isFavorite = curMarkData.nType ~= 4
    end
    self:TriggerUIEvent("SetFavorite", isFavorite)
    --endregion
    
    --更新位置
    local uiPos = sand_ui_data.GetScreenPosBySid(data.sid)
    self:TriggerUIEvent("SetFollowPosition", uiPos)

    self:InitViewData()
end

--[[界面被显示的时候调用]]
function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close()
    self.__base.Close(self)
    controller = nil
end

function UIController:OnBtn_btnFavoriteClickedProxy()
    sandbox_ui_mgr.OnGW_CLICK_EMPTY()
    local data = {
        entity = self.entity,
        type = 2        --1:联盟 2:玩家
    }
    windowMgr:ShowModule("ui_sand_mark_tip", nil, nil,data)
end

--会基类自动调用
function UIController:AutoSubscribeEvents()
    -- 注意 事件建议使用self:RegisterEvent(event_name,func)来注册，不需要手动维护注销了    
    self:RegisterEvent(sand_ui_event_define.REFRESH_ENTITY_TIP, function()
        local uiPos = sand_ui_data.GetScreenPosBySid(self.entity.sid)
        self:TriggerUIEvent("SetFollowPosition", uiPos)
    end)
end
--会基类自动调用
function UIController:AutoUnsubscribeEvents()

end
---********************功能函数区**********---




function UIController:InitViewData()
    local viewData = {}

    local cfg = self.entity.cfg
    viewData.name = lang.Get(cfg.name)
    viewData.desc = lang.Get(cfg.ResourceDescription)

    local taskSid = self.entity.props.radarTaskSID
    local radarData = radar_data.GetRadarTaskDataToSid(taskSid)

    --消耗体力
    local taskCfg = game_scheme:RadarMission_0(radarData.taskId)
    viewData.cost = taskCfg.physicalConsumption

    self:TriggerUIEvent("RefreshViewUI", viewData)
end

---********************end功能函数区**********---
--region ModuleFunction 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
