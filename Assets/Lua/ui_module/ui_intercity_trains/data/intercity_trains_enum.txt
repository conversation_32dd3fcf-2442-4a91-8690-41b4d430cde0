--- Created by par.
--- DateTime: 2025/02/19
--- Des:

module("intercity_trains_enum")

enTrainType = {
    ---@param Normal number
    Normal = 1, -- 1代表普通列车
    ---@param GOLD number
    GOLD = 2 -- 2代表黄金列车
}
-- 赠与的刷新卷领取标记
enTrainGivenCardFlag = {
    ---@param CanGet number
    CanGet = 0, -- 未领取
    ---@param Getted number
    Getted = 1 -- 已领取
}

-- 战斗记录色号
enTrainBattleColor = {
    ---@param winColor color
    winColor = {r = 179 / 255, g = 231 / 255, b = 188 / 255, a = 1}, -- 胜利色号
    ---@param failColor color
    failColor = {r = 246 / 255, g = 204 / 255, b = 194 / 255, a = 1} -- 失败色号
}
