---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON><PERSON>ua)
--- Created by <PERSON><PERSON><PERSON>.
--- DateTime: 2024/8/8 10:18
---
local require = require

local shop_pb = require "shop_pb"
local msg_pb = require "msg_pb"
local net_route = require "net_route"

module("net_supply_module")

---协议回复的Handler注册
local MessageTable = {
    --{ msg_pb.MSG_SHOP_BUYGOODS_RSP, MSG_SHOP_BUYGOODS_RSP, shop_pb.TMSG_SHOP_BUYGOODS_RSP },
}
net_route.RegisterMsgHandlers(MessageTable)