local require = require
local pairs = pairs     
local table = table
local newClass = newclass
local type = type

local util = require "util"
local event = require "event"
local controller_base = require "controller_base"

--region Controller Life
module("ui_power_up_tip_controller")
local controller = nil
local UIController = newClass("ui_power_up_tip_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self, view_name, controller_name)
    self.CData = {}
    self.num = util.formatNumberWithCommas(data)
    self:TriggerUIEvent("SetNum", self.num)
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close()   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.CData = nil

    self.__base.Close(self)
end

function UIController:AutoSubscribeEvents(_, event_name) 
    local reSetData = function(_, num)
        num = util.formatNumberWithCommas(num)
        self:TriggerUIEvent("SetNum", num)
    end
    self:RegisterEvent(event.ACTOR_NEW_POWER_UP, reSetData)
end

function UIController:AutoUnsubscribeEvents() 
end
--endregion

--region Controller Logic

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
