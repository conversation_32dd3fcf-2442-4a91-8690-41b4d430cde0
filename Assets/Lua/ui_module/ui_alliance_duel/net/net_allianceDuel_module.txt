local require   = require
local event         = require "event"
local xManMsg_pb        = require "xManMsg_pb"
local allianceDuel_pb       = require "allianceDuel_pb"
local net           = require "net"
local net_route     = require "net_route"
local flow_text        = require "flow_text"
local net_leaguepro_module  = require "net_leaguepro_module"
local event_allianceDuel_define = require("event_allianceDuel_define")
local alliance_duel_data = require "alliance_duel_data"
local gw_ed = require("gw_ed")

module("net_allianceDuel_module")

-- 客户端请求同盟对决信息
function MSG_ALLIANCEDUEL_INFO_REQ(data)
    local msg = allianceDuel_pb.TMSG_ALLIANCEDUEL_INFO_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCEDUEL_INFO_REQ, msg)
end

-- 返回同盟对决信息
function MSG_ALLIANCEDUEL_INFO_RSP(msg)
    -- errCode
    -- themeId
    -- themeEndTime
    -- attend
    -- allianceDuelBase
   if msg.errCode ~= 0 then
        flow_text.AddErrorCodeRes(msg.errCode)
    else
       alliance_duel_data.UpdateAllianceDuelInfo(msg)
        event.Trigger(event_allianceDuel_define.TMSG_ALLIANCEDUEL_INFO_RSP,msg)
    end
end

-- 同步同盟对决信息（登录，活动开启, 主题更换）
function MSG_ALLIANCEDUEL_INFO_NTF(msg)
    -- themeId
    -- themeEndTime
    -- attend
    -- allianceDuelBase
    alliance_duel_data.UpdateAllianceDuelInfo(msg)
    --刷新对决入口的时间
    alliance_duel_data.RefreshRightTopTime()
    --自动请求一次对决场次记录
    MSG_ALLIANCE_THEMEDUEL_REQ()
    event.Trigger(event_allianceDuel_define.TMSG_ALLIANCEDUEL_INFO_NTF,msg)
end

-- 客户端请求同盟对决每日主题场次记录
function MSG_ALLIANCE_THEMEDUEL_REQ(data)
    local msg = allianceDuel_pb.TMSG_ALLIANCE_THEMEDUEL_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCE_THEMEDUEL_REQ, msg)
end

-- 返回同盟对决每日主题场次记录
function MSG_ALLIANCE_THEMEDUEL_RSP(msg)
    -- errCode
    -- themeDuelInfo
   if msg.errCode ~= 0 then
        --flow_text.AddErrorCodeRes(msg.errCode)
    else
        alliance_duel_data.UpdateThemeDuelResult(msg)
        event.Trigger(event_allianceDuel_define.TMSG_ALLIANCE_THEMEDUEL_RSP,msg)
    end
end

-- 跨天同盟对决每日主题场次记录
--function MSG_ALLIANCE_THEMEDUEL_NTF(msg)
--    -- themeDuelInfo
--    alliance_duel_data.UpdateThemeDuelResult(msg)
--    --event.Trigger(event_allianceDuel_define.TMSG_ALLIANCE_THEMEDUEL_NTF,msg)
--end

-- 定时更新同盟对决联盟总积分信息
function MSG_ALLIANCEDUEL_ALLIANCEPOINT_NTF(msg)
    -- allianceId
    -- alliancePointDay
    -- atkAllianceId
    -- atkAlliancePointDay
    alliance_duel_data.UpdateAlliancePoint(msg)
    --event.Trigger(event_allianceDuel_define.TMSG_ALLIANCEDUEL_ALLIANCEPOINT_NTF,msg)
end


-- 客户端请求积分对决主题详细信息
function MSG_ALLIANCEDUEL_THEMEINFO_REQ(data)
    -- themeId
    local msg = allianceDuel_pb.TMSG_ALLIANCEDUEL_THEMEINFO_REQ()
    msg.themeId = data.themeId
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCEDUEL_THEMEINFO_REQ, msg)
end

-- 返回积分对决主题详细信息
function MSG_ALLIANCEDUEL_THEMEINFO_RSP(msg)
    -- errCode
    -- themeId
    -- taskPointInfo
    -- winAllianceId
    -- winAllPointDay
    -- failAllianceId
    -- failAllPointDay
   if msg.errCode ~= 0 then
        flow_text.AddErrorCodeRes(msg.errCode)
    else
        event.Trigger(event_allianceDuel_define.TMSG_ALLIANCEDUEL_THEMEINFO_RSP,msg)
    end
end

-- 击败活动推送弹窗（每周五弹窗提示周六同盟突袭开始时间），设置不提醒就不会推送该协议
function MSG_ALLIANCEDUEL_DEFEAT_NTF(msg)
    -- attackOpenTime
    --mc.isShowActivityPop 
    alliance_duel_data.SetActivityPopup(msg)
    event.Trigger(event_allianceDuel_define.TMSG_ALLIANCEDUEL_DEFEAT_NTF,msg)
end

-- 客户端设置击败活动推送不提醒
function MSG_ALLIANCEDUEL_DEFEATNOTICE_REQ(data)
    local msg = allianceDuel_pb.TMSG_ALLIANCEDUEL_DEFEATNOTICE_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCEDUEL_DEFEATNOTICE_REQ, msg)
end

-- 客户端设置击败活动推送不提醒返回
function MSG_ALLIANCEDUEL_DEFEATNOTICE_RSP(msg)
    -- errCode
   if msg.errCode ~= 0 then
        flow_text.AddErrorCodeRes(msg.errCode)
    else
        event.Trigger(event_allianceDuel_define.TMSG_ALLIANCEDUEL_DEFEATNOTICE_RSP,msg)
    end
end

-- 请求跨服过去的位置
function MSG_ALLIANCEDUEL_MOVE_COORDINATE_REQ(data)
    -- worldId
    -- allianceId
    local msg = allianceDuel_pb.TMSG_ALLIANCEDUEL_MOVE_COORDINATE_REQ()
    msg.worldId = data.worldId
    msg.allianceId = data.allianceId
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCEDUEL_MOVE_COORDINATE_REQ, msg)
end

-- 跨服初始位置回包
function MSG_ALLIANCEDUEL_MOVE_COORDINATE_RSP(msg)
    -- errCode
    if msg.errCode ~= 0 then
        flow_text.AddErrorCodeRes(msg.errCode)
    else
        event.Trigger(event_allianceDuel_define.TMSG_ALLIANCEDUEL_MOVE_COORDINATE_RSP,msg)
    end
end

-- 确认迁城请求
function MSG_ALLIANCEDUEL_BATTLE_CITY_MOVE_REQ(data)
    -- sandboxSid
    -- x
    -- y
    local msg = allianceDuel_pb.TMSG_ALLIANCEDUEL_BATTLE_CITY_MOVE_REQ()
    msg.sandboxSid = data.sandboxSid
    msg.x = data.x
    msg.y = data.y
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCEDUEL_BATTLE_CITY_MOVE_REQ, msg)
end

-- 确认迁城回包
function MSG_ALLIANCEDUEL_BATTLE_CITY_MOVE_RSP(msg)
    -- errCode
    -- sandboxSid
    -- x
    -- y
    if msg.errCode ~= 0 then
        --flow_text.AddErrorCodeRes(msg.errCode)
        --gw_ed.mgr:Trigger(gw_ed.GW_SAND_INTERNAL_DATA_CHANGE,"OnSandMoveCityCancel")
    else
        local net_sandbox_module = require "net_sandbox_module"
        local data = {
            errCode = 0,
            pos = {x= msg.x,y=msg.y},
            sandBoxSid = msg.sandboxSid
        }
        net_sandbox_module.SetMoveRspState(data)
        event.Trigger(event_allianceDuel_define.TMSG_ALLIANCEDUEL_BATTLE_CITY_MOVE_RSP,msg)
    end
end

-- 请求跨服返回的位置
function MSG_ALLIANCEDUEL_BACK_COORDINATE_REQ(data)
    -- worldId
    -- allianceId
    local msg = allianceDuel_pb.TMSG_ALLIANCEDUEL_BACK_COORDINATE_REQ()
    msg.worldId = data.worldId
    msg.allianceId = data.allianceId
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCEDUEL_BACK_COORDINATE_REQ, msg)
end

-- 跨服初始位置回包
function MSG_ALLIANCEDUEL_BACK_COORDINATE_RSP(msg)
    -- errCode
    -- sandboxSid
    -- x
    -- y
    if msg.errCode ~= 0 then
        gw_ed.mgr:Trigger(gw_ed.GW_SAND_INTERNAL_DATA_CHANGE,"OnSandMoveCityCancel")
        flow_text.AddErrorCodeRes(msg.errCode)
    else
      
        event.Trigger(event_allianceDuel_define.TMSG_ALLIANCEDUEL_BACK_COORDINATE_RSP,msg)
    end
end

-- 确认迁城请求
function MSG_ALLIANCEDUEL_BATTLE_CITY_BACK_REQ(data)
    -- sandboxSid
    -- x
    -- y
    local msg = allianceDuel_pb.TMSG_ALLIANCEDUEL_BATTLE_CITY_BACK_REQ()
    msg.sandboxSid = data.sandboxSid
    msg.x = data.x
    msg.y = data.y
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCEDUEL_BATTLE_CITY_BACK_REQ, msg)
end

-- 确认迁城回包
function MSG_ALLIANCEDUEL_BATTLE_CITY_BACK_RSP(msg)
    -- errCode
    -- sandboxSid
    -- x
    -- y
    if msg.errCode ~= 0 then
        --gw_ed.mgr:Trigger(gw_ed.GW_SAND_INTERNAL_DATA_CHANGE,"OnSandMoveCityCancel")
        --flow_text.AddErrorCodeRes(msg.errCode)
    else
        local net_sandbox_module = require "net_sandbox_module"
        local data = {
            errCode = 0,
            pos = {x= msg.x,y=msg.y},
            sandBoxSid = msg.sandboxSid
        }
        net_sandbox_module.SetMoveRspState(data)
        ---gw_ed.mgr:Trigger(gw_ed.GW_SAND_NET_EVENT, "OnSandMoveCity", data)

        event.Trigger(event_allianceDuel_define.TMSG_ALLIANCEDUEL_BATTLE_CITY_BACK_RSP,msg)
    end
end

-- 通知玩家基地sid和敌对联盟聚集地sid（登录有联盟对决，玩家加入有对决的联盟）
function MSG_ALLIANCEDUEL_SANDBOXID_NTF(msg)
    -- roleSid
    -- allianceId
    -- allianceSid
    alliance_duel_data.OnInitSandboxSid(msg)
    event.Trigger(event_allianceDuel_define.TMSG_ALLIANCEDUEL_SANDBOXID_NTF,msg)
    
end


-- 客户端定时请求联盟总积分和MVP
function MSG_ALLIANCEDUEL_POINT_MVP_REQ(data)
    local msg = allianceDuel_pb.TMSG_ALLIANCEDUEL_POINT_MVP_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_ALLIANCEDUEL_POINT_MVP_REQ, msg)
end

-- 返回定时请求联盟总积分和MVP
function MSG_ALLIANCEDUEL_POINT_MVP_RSP(msg)
    -- errCode
    -- allianceInfo
    -- attackAllianceInfo
    if msg.errCode ~= 0 then
        flow_text.AddErrorCodeRes(msg.errCode)
    else
        --更新两方mvp数据
        alliance_duel_data.UpdateMvpInfo(msg)
        event.Trigger(event_allianceDuel_define.TMSG_ALLIANCEDUEL_POINT_MVP_RSP,msg)
    end
end


local MessageTable = {
    {xManMsg_pb.MSG_ALLIANCEDUEL_INFO_RSP, MSG_ALLIANCEDUEL_INFO_RSP, allianceDuel_pb.TMSG_ALLIANCEDUEL_INFO_RSP},
    {xManMsg_pb.MSG_ALLIANCEDUEL_INFO_NTF, MSG_ALLIANCEDUEL_INFO_NTF, allianceDuel_pb.TMSG_ALLIANCEDUEL_INFO_NTF},
    {xManMsg_pb.MSG_ALLIANCE_THEMEDUEL_RSP, MSG_ALLIANCE_THEMEDUEL_RSP, allianceDuel_pb.TMSG_ALLIANCE_THEMEDUEL_RSP},
    --{xManMsg_pb.MSG_ALLIANCE_THEMEDUEL_NTF, MSG_ALLIANCE_THEMEDUEL_NTF, allianceDuel_pb.TMSG_ALLIANCE_THEMEDUEL_NTF},
    {xManMsg_pb.MSG_ALLIANCEDUEL_ALLIANCEPOINT_NTF, MSG_ALLIANCEDUEL_ALLIANCEPOINT_NTF, allianceDuel_pb.TMSG_ALLIANCEDUEL_ALLIANCEPOINT_NTF},
    {xManMsg_pb.MSG_ALLIANCEDUEL_THEMEINFO_RSP, MSG_ALLIANCEDUEL_THEMEINFO_RSP, allianceDuel_pb.TMSG_ALLIANCEDUEL_THEMEINFO_RSP},
    {xManMsg_pb.MSG_ALLIANCEDUEL_DEFEAT_NTF, MSG_ALLIANCEDUEL_DEFEAT_NTF, allianceDuel_pb.TMSG_ALLIANCEDUEL_DEFEAT_NTF},
    {xManMsg_pb.MSG_ALLIANCEDUEL_DEFEATNOTICE_RSP, MSG_ALLIANCEDUEL_DEFEATNOTICE_RSP, allianceDuel_pb.TMSG_ALLIANCEDUEL_DEFEATNOTICE_RSP},
    {xManMsg_pb.MSG_ALLIANCEDUEL_MOVE_COORDINATE_RSP, MSG_ALLIANCEDUEL_MOVE_COORDINATE_RSP, allianceDuel_pb.TMSG_ALLIANCEDUEL_MOVE_COORDINATE_RSP},
    {xManMsg_pb.MSG_ALLIANCEDUEL_BATTLE_CITY_MOVE_RSP, MSG_ALLIANCEDUEL_BATTLE_CITY_MOVE_RSP, allianceDuel_pb.TMSG_ALLIANCEDUEL_BATTLE_CITY_MOVE_RSP},
    {xManMsg_pb.MSG_ALLIANCEDUEL_BACK_COORDINATE_RSP, MSG_ALLIANCEDUEL_BACK_COORDINATE_RSP, allianceDuel_pb.TMSG_ALLIANCEDUEL_BACK_COORDINATE_RSP},
    {xManMsg_pb.MSG_ALLIANCEDUEL_BATTLE_CITY_BACK_RSP, MSG_ALLIANCEDUEL_BATTLE_CITY_BACK_RSP, allianceDuel_pb.TMSG_ALLIANCEDUEL_BATTLE_CITY_BACK_RSP},
    {xManMsg_pb.MSG_ALLIANCEDUEL_SANDBOXID_NTF, MSG_ALLIANCEDUEL_SANDBOXID_NTF, allianceDuel_pb.TMSG_ALLIANCEDUEL_SANDBOXID_NTF},
    {xManMsg_pb.MSG_ALLIANCEDUEL_POINT_MVP_RSP, MSG_ALLIANCEDUEL_POINT_MVP_RSP, allianceDuel_pb.TMSG_ALLIANCEDUEL_POINT_MVP_RSP},

}
net_route.RegisterMsgHandlers(MessageTable)