
local Button = CS.UnityEngine.UI.Button
local Transform = CS.UnityEngine.Transform
local Text = CS.UnityEngine.UI.Text
local GameObject = CS.UnityEngine.GameObject


module("ui_bomberman_tips_binding")

UIPath = "ui/prefabs/gw/gw_bomberman/uibombermantips.prefab"

WidgetTable ={
	btn_closeBtn = { path = "closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	tf_Parent = { path = "tf_Parent", type = Transform, },
	txt_Warn = { path = "tf_Parent/txt_Warn", type = Text, },
	txt_Control = { path = "tf_Parent/txt_Control", type = Text, },
	obj_Effect = { path = "obj_Effect", type = GameObject, },

}
