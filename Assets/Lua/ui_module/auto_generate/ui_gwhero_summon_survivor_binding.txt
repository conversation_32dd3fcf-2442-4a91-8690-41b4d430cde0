
local Image = CS.UnityEngine.UI.Image
local RectTransform = CS.UnityEngine.RectTransform
local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text


module("ui_gwhero_summon_survivor_binding")

UIPath = "ui/prefabs/gw/gw_herosummon/uigwherosummonsurvivor.prefab"

WidgetTable ={
	img_Logo = { path = "img_Logo", type = Image, },
	rtf_GiftItem = { path = "rtf_GiftItem", type = RectTransform, },
	btn_Info = { path = "btn_Info", type = Button, event_name = "OnBtnInfoClickedProxy"},
	btn_Slider = { path = "btn_Slider", type = Button, event_name = "OnBtnSliderClickedProxy"},
	img_Slider = { path = "btn_Slider/img_Slider", type = Image, },
	img_Icon = { path = "btn_Slider/img_Icon", type = Image, },
	txt_Slider = { path = "btn_Slider/txt_Slider", type = Text, },
	btn_One = { path = "btnGroup/btn_One", type = Button, event_name = "OnBtnOneClickedProxy"},
	txt_Free = { path = "btnGroup/btn_One/txt_Free", type = Text, },
	txt_Lable = { path = "btnGroup/btn_One/txt_Lable", type = Text, },
	img_One = { path = "btnGroup/btn_One/txt_Lable/img_One", type = Image, },
	txt_CostCount = { path = "btnGroup/btn_One/txt_Lable/img_One/txt_CostCount", type = Text, },
	btn_Ten = { path = "btnGroup/btn_Ten", type = Button, event_name = "OnBtnTenClickedProxy"},
	txt_Free1 = { path = "btnGroup/btn_Ten/txt_Free1", type = Text, },
	txt_Lable1 = { path = "btnGroup/btn_Ten/txt_Lable1", type = Text, },
	img_One1 = { path = "btnGroup/btn_Ten/txt_Lable1/img_One1", type = Image, },
	txt_CostCount1 = { path = "btnGroup/btn_Ten/txt_Lable1/img_One1/txt_CostCount1", type = Text, },
	rtf_Survivor = { path = "Scroll View/Viewport/Content/rtf_Survivor", type = RectTransform, },
	txt_TimeDes = { path = "time/txt_TimeDes", type = Text, },
	txt_Time = { path = "time/txt_Time", type = Text, },
	txt_Count = { path = "txtS/countParent/txt_Count", type = Text, },
	txt_CountDes1 = { path = "txtS/txt_CountDes1", type = Text, },
	txt_CountDes2 = { path = "txtS/txt_CountDes2", type = Text, },

}
