
local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local RectTransform = CS.UnityEngine.RectTransform
local Image = CS.UnityEngine.UI.Image


module("ui_diamond_reward_binding")

UIPath = "ui/prefabs/gw/activity/diamondgift/uidiamondreward.prefab"

WidgetTable ={
	btn_buy = { path = "bg/BtnGroup/btn_buy", type = Button, event_name = "OnBtnBuyClickedProxy"},
	txt_title = { path = "bg/txt_title", type = Text, },
	rtf_hotFlag = { path = "bg/rtf_hotFlag", type = RectTransform, },
	txt_desc = { path = "bg/rtf_hotFlag/txt_desc", type = Text, },
	txt_DiamonNum = { path = "bg/iconPanel/txt_DiamonNum", type = Text, },
	img_icon = { path = "bg/iconPanel/img_icon", type = Image, },
	txt_contentdesc = { path = "bg/txt_contentdesc", type = Text, },
	img_giftImage = { path = "bg/img_giftImage", type = Image, },
	btn_closeBtn = { path = "closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	txt_price={path = "bg/BtnGroup/btn_buy/txt_price", type = Text, },
	iconPanel={path = "bg/iconPanel", type = RectTransform, }
}
