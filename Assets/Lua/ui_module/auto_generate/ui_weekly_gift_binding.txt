
local Text = CS.UnityEngine.UI.Text
local Button = CS.UnityEngine.UI.Button
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable


module("ui_weekly_gift_binding")

UIPath = "ui/prefabs/gw/gw_weeklygift/uiweeklygift.prefab"

WidgetTable ={
	txt_ActiveName = { path = "Active/txt_ActiveName", type = Text, },
	txt_AllTime = { path = "Active/Image/txt_AllTime", type = Text, },
	btn_Detail = { path = "btn_Detail", type = Button, event_name = "OnBtnDetailClickedProxy"},
	srt_reward = { path = "reward/Viewport/srt_reward", type = ScrollRectTable, },

}
