local require = require
local typeof = typeof

local GameObject = CS.UnityEngine.GameObject
local Slider = CS.UnityEngine.UI.Slider
local Text = CS.UnityEngine.UI.Text
local Button = CS.UnityEngine.UI.Button
local RectTransform = CS.UnityEngine.RectTransform
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem
local Toggle = CS.UnityEngine.UI.Toggle


module("ui_tavern_treasure_binding")

UIPath = "ui/prefabs/gw/gw_tavern/uitaverntreasure.prefab"

WidgetTable ={
	obj_dialog = { path = "obj_dialog", type = GameObject, },
	sld_box = { path = "sld_box", type = Slider, value_changed_event = "OnSliderBoxValueChange"},
	txt_progress = { path = "sld_box/txt_progress", type = Text, },
	btn_tip = { path = "sld_box/btn_tip", type = Button, event_name = "OnBtnTipClickedProxy"},
	rtf_dialog = { path = "rtf_dialog", type = RectTransform, },
	txt_dialogTip = { path = "rtf_dialog/txt_dialogTip", type = Text, },
	scrItem_map = { path = "scrItem_map", type = ScrollRectItem, },
	tog_jump = { path = "bottom/jump/tog_jump", type = Toggle, value_changed_event = "OnTogJumpValueChange"},
	btn_dig = { path = "bottom/btn_dig", type = Button, event_name = "OnBtnDigClickedProxy"},
	txt_Title = { path = "bottom/btn_dig/txt_Title", type = Text, },
	rtf_red = { path = "bottom/btn_dig/rtf_red", type = RectTransform, },
	obj_cantDig = { path = "bottom/obj_cantDig", type = GameObject, },
	txt_Title = { path = "bottom/obj_cantDig/txt_Title", type = Text, },
	btn_change = { path = "bottom/btn_change", type = Button, event_name = "OnBtnChangeClickedProxy"},
	btn_reward = { path = "bottom/btn_reward", type = Button, event_name = "OnBtnRewardClickedProxy"},

}
