
local ScrollRect = CS.UnityEngine.UI.ScrollRect
local RectTransform = CS.UnityEngine.RectTransform
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem
local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local Image = CS.UnityEngine.UI.Image
local GameObject = CS.UnityEngine.GameObject


module("ui_alliance_train_main_binding")

UIPath = "ui/prefabs/gw/gw_alliancetrain/uialliancetrainmain.prefab"

WidgetTable ={
	sr_scrollRect = { path = "main/sr_scrollRect", type = ScrollRect, },
	rtf_move = { path = "main/sr_scrollRect/Viewport/rtf_move", type = RectTransform, },
	scrItem_lineupItem1 = { path = "main/sr_scrollRect/Viewport/rtf_move/lineup/scrItem_lineupItem1", type = ScrollRectItem, },
	scrItem_lineupItem2 = { path = "main/sr_scrollRect/Viewport/rtf_move/lineup/scrItem_lineupItem2", type = ScrollRectItem, },
	scrItem_lineupItem3 = { path = "main/sr_scrollRect/Viewport/rtf_move/lineup/scrItem_lineupItem3", type = ScrollRectItem, },
	scrItem_lineupBtn1 = { path = "main/sr_scrollRect/Viewport/rtf_move/lineup/scrItem_lineupItem1", type = Button, event_name = "OnBtnLineUpClickedProxy"},
	scrItem_lineupBtn2 = { path = "main/sr_scrollRect/Viewport/rtf_move/lineup/scrItem_lineupItem2", type = Button, event_name = "OnBtnLineUpClickedProxy"},
	scrItem_lineupBtn3 = { path = "main/sr_scrollRect/Viewport/rtf_move/lineup/scrItem_lineupItem3", type = Button, event_name = "OnBtnLineUpClickedProxy"},
	
	scrItem_trainItem1 = { path = "main/sr_scrollRect/Viewport/rtf_move/trainList/scrItem_trainItem1", type = ScrollRectItem, },
	scrItem_trainItem2 = { path = "main/sr_scrollRect/Viewport/rtf_move/trainList/scrItem_trainItem2", type = ScrollRectItem, },
	scrItem_trainItem3 = { path = "main/sr_scrollRect/Viewport/rtf_move/trainList/scrItem_trainItem3", type = ScrollRectItem, },
	scrItem_trainItem4 = { path = "main/sr_scrollRect/Viewport/rtf_move/trainList/scrItem_trainItem4", type = ScrollRectItem, },
	scrItem_trainItem5 = { path = "main/sr_scrollRect/Viewport/rtf_move/trainList/scrItem_trainItem5", type = ScrollRectItem, },
	btn_share = { path = "main/sr_scrollRect/Viewport/rtf_move/buttomBg/btn_share", type = Button, event_name = "OnBtnShareClickedProxy"},
	btn_refresh = { path = "main/sr_scrollRect/Viewport/rtf_move/buttomBg/btn_refresh", type = Button, event_name = "OnBtnRefreshClickedProxy"},
	txt_Up = { path = "main/sr_scrollRect/Viewport/rtf_move/buttomBg/btn_refresh/txt_Up", type = Text, },
	txt_Down = { path = "main/sr_scrollRect/Viewport/rtf_move/buttomBg/btn_refresh/txt_Down", type = Text, },
	img_Cost = { path = "main/sr_scrollRect/Viewport/rtf_move/buttomBg/btn_refresh/txt_Down/img_Cost", type = Image, },
	btn_passenger = { path = "main/sr_scrollRect/Viewport/rtf_move/buttomBg/btn_passenger", type = Button, event_name = "OnBtnPassengerClickedProxy"},
	item_red = { path = "main/sr_scrollRect/Viewport/rtf_move/buttomBg/btn_passenger/item_red", type = GameObject, },
	rtf_refresh = { path = "main/sr_scrollRect/Viewport/rtf_move/buttomBg/rtf_refresh", type = RectTransform, },
	txt_refreshText = { path = "main/sr_scrollRect/Viewport/rtf_move/buttomBg/rtf_refresh/txt_refreshText", type = Text, },
	txt_title = { path = "main/top/txt_title", type = Text, },
	txt_time = { path = "main/top/time/txt_time", type = Text, },
	btn_top = { path = "main/top/btn_top", type = Button, event_name = "OnBtnTopClickedProxy"},
	rtf_topIcon = { path = "main/top/btn_top/rtf_topIcon", type = RectTransform, },
	btn_close = { path = "main/btn_close", type = Button, event_name = "OnBtnCloseClickedProxy"},
	btn_help = { path = "main/top/btn_help", type = Button, event_name = "OnBtnHelpClickedProxy"},

}
