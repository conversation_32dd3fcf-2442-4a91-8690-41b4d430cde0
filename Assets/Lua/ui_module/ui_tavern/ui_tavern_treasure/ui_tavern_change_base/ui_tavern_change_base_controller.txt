--- Created by cxq.
--- DateTime: 2025/04/12
--- Des:酒馆 隐秘宝藏 交换碎片

local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type

local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"
local e_handler_mgr = require "e_handler_mgr"
local event = require "event"
local tavern_treasure_mgr = require "tavern_treasure_mgr"
local tavern_treasure_data = require "tavern_treasure_data"

--region Controller Life
module("ui_tavern_change_base_controller")
local controller = nil
local UIController = newClass("ui_tavern_change_base_controller", controller_base)

local subModule = {} --子模块
local curSubModule = nil --当前子模块
local subModuleName = {
	[1] = "ui_tavern_change_my",
	[2] = "ui_tavern_change_alliance",
} --子模块名字
local UIType = {
    My = 1, 
    Alliance = 2
}


function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name) 
    self.CData.treasureId = data.treasureId
    subModule[1] = require "ui_tavern_change_my"
    subModule[2] = require "ui_tavern_change_alliance"
end

function UIController:OnShow()
    self.__base.OnShow(self)
    self:OnToggleChange(UIType.My, true)
end

function UIController:Close(data)
    if subModuleName then
        for index, value in ipairs(subModuleName) do
            ui_window_mgr:UnloadModuleImmediate(subModuleName[index])
        end
    end
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents()
end

function UIController:AutoUnsubscribeEvents() 
end
--endregion

--region Controller Logic
function  UIController:OnToggleChange(type, isChangeOn)
    if self.CData.selectedIndex ~= type then
        for i, v in ipairs(subModuleName) do
            if type == i then
                self.viewWnd = self.viewWnd or ui_window_mgr:GetWindowObj(self.view_name)
                ui_window_mgr:ShowModule(subModuleName[type], nil, nil, {
                    uiParent = self.viewWnd.rtf_Content,
                    treasureId = self.CData.treasureId
                })
            else
                --隐藏其他子模块
                if subModule[i] then
                    subModule[i].Hide()
                end
            end
            e_handler_mgr.TriggerHandler(self.view_name, "OnRefreshToggle", type, isChangeOn)
        end
    end
    self.CData.selectedIndex = type
end

function  UIController:OnTogMyValueChange(state)
    if state then
        self:OnToggleChange(UIType.My)
    end
end
function  UIController:OnTogAllianceValueChange(state)
    if state then
        self:OnToggleChange(UIType.Alliance)
    end
end
function  UIController:OnBtnCloseBtnClickedProxy()
	ui_window_mgr:UnloadModule(self.view_name)
end

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
