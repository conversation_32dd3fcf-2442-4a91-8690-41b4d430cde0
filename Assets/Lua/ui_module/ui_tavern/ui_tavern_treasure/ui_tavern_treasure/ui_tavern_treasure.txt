--- Created by cxq.
--- DateTime: 2025/04/10
--- Des:酒馆 隐秘宝藏

local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local UIUtil = CS.Common_Util.UIUtil

local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_tavern_treasure_binding"
local log = require "log"
local red_const = require "red_const"
local event = require "event"

--region View Life
module("ui_tavern_treasure")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.VData = {}
    self.VData.aniTime = 1
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.VData = nil    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic

function UIView:OnRefreshMap(data)
    if not self:IsValid() or not data or not data.data then
        return
    end
    for index, value in ipairs(data.data) do
        local ui = self.scrItem_map:Get("map_"..index)
        local none = ui:Get("none")
        local name = ui:Get("name")
        local count = ui:Get("count")
        local map = ui:Get("map_"..index)
        if map.alphaHitTestMinimumThreshold then
            map.alphaHitTestMinimumThreshold = 1
        end

        name.text = "M" .. value.index--lang.Get(value.nameID)
        count.text = "X" .. util.PriceConvert(value.curCount)
        none:SetActive(value.curCount <= 0)
        local func = {}
        local OnClickChip = function ()-- 商店跳转
            local supplyData = {
                list = {
                    {
                        goodsId = value.id,
                        needNum = 1,
                    }
                }
            }
            local evt_sourceSupply_define = require "evt_sourceSupply_define"
            event.Trigger(evt_sourceSupply_define.Evt_ShowSupplyPanel, supplyData)
        end
        func["OnClickChip"] = OnClickChip
        ui.InvokeFunc = function(funcname)
            if funcname then
                func[funcname]()
            end
        end
    end
    self:BindUIRed(self.rtf_red, red_const.Enum.tavernTreasure, nil, {redPath = red_const.Type.Default})
    local canDig = data.canDigCount > 0
    self.btn_dig.gameObject:SetActive(canDig)
    self.obj_cantDig:SetActive(not canDig)
end

function UIView:OnRefreshDialog(isShow)
    if not self:IsValid() then
        return
    end
    self.rtf_dialog.gameObject:SetActive(isShow)
end

function UIView:OnRefreshJump(isSelect)
    if not self:IsValid() then
        return
    end
    self.tog_jump.isOn = isSelect
    if self.btn_dig.intervalTime then
        self.btn_dig.intervalTime = isSelect and 0.2 or self.VData.aniTime
    end
end

function UIView:OnRefreshProgress(data)
    if not self:IsValid() then
        return
    end
    if not data or not data.curCount or not data.all or data.all <= 0 then
        log.Error("刷新界面进度传入数据异常", data and data.all)
        return
    end
    self.sld_box.value = data.curCount / data.all
    self.txt_progress.text = data.curCount .. "/" .. data.all
    self.txt_dialogTip.text = string.formatL(668077, data.all - data.curCount)
end

function UIView:OnPlayAni(data)
    local callBack = data.callBack
    self.scrItem_map.gameObject:SetActive(false)
    -- 发光特效
    util.DelayCallOnce(self.VData.aniTime, function ()
        if callBack then
            callBack()
        end
        self.scrItem_map.gameObject:SetActive(true)
    end)
end
--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, nil, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil,nil, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
