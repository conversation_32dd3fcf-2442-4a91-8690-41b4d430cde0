--@region FileHead
-- ui_tavern_task_new.txt ---------------------------------
-- author:  无名氏
-- date:    2024/12/11 14:30:57
-- ver:     1.0
-- desc:    Description
-------------------------------------------------
--@endregion 

--@region Require
local require   = require
local pairs     = pairs
local typeof    = typeof
local string    = string

local GameObject    = CS.UnityEngine.GameObject
local Button        = CS.UnityEngine.UI.Button

local class                 = require "class"
local ui_base               = require "ui_base"
local module_scroll_list    = require "scroll_list"
local net_route             = require "net_route"
local ui_tavern_mgr = require "ui_tavern_mgr"
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem
local face_item_new = require "face_item_new"
local CanvasGroup	= CS.UnityEngine.CanvasGroup
local LeanTween		= CS.LeanTween
local const = require "const"
local util 			= require "util"
local windowMgr = require "ui_window_mgr"
local lang = require "lang"
local goods_item            = require "goods_item_new"
local game_scheme = require "game_scheme"
local reward_mgr            = require "reward_mgr"
local ipairs = ipairs
--@endregion 

--@region ModuleDeclare
module("ui_tavern_task_new")
--local interface = require "iui_tavern_task_new"
local window = nil
local UTavern_Task = {}
local serData = {}
--@endregion 

--@region WidgetTable
UTavern_Task.widget_table = {
    Rtsf_PresonListContent = { path = "Main/Personal_UnSelected/ViewPort/Auto_PresonListContent", type = "RectTransform", event_name = "" },
    Img_ListItemPerson = { path = "Main/Personal_UnSelected/ViewPort/Auto_PresonListContent/Auto_ListItemPerson", type = "RectTransform", event_name = "" },
    closeBtn = {path = "closeBtn", type = "Button"},--返回
--@region User
--@endregion 
}
--@endregion 

--@region WindowCtor
function UTavern_Task:ctor(selfType)
	self.__base:ctor(selfType)
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowInit
--[[窗口初始化]]
function UTavern_Task:Init()
    self:SubscribeEvents()
    net_route.RegisterMsgHandlers(MessageTable)
    self:FreshItemUI()
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function UTavern_Task:OnShow()
    self:UpdateUIPage()
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowOnHide
--[[界面隐藏时调用]]
function UTavern_Task:OnHide()
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowSetInputParam
--[[设置窗口的输入参数。该参数通常是由其它模块或者外部设置进来。需要注意的是，
当调用这个函数的时候，窗口资源可能还是没有加载完成的。
@param p 参数表
]]
function UTavern_Task:SetInputParam(p)
	self.inputParam = p

    --如果正在显示，则更新一次窗口
    if self.UIRoot and self.UIRoot.activeSelf == true then
        self:UpdateUIPage()
    end
	
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowBuildUpdateData
--[[构建UI更新数据]]
function UTavern_Task:BuildUpdateData()
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowUpdateUI
--[[资源加载完成，被显示的时候调用]]
function UTavern_Task:UpdateUIPage()
	self:BuildUpdateData()
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowClose
function UTavern_Task:Close()
    net_route.UnregisterMsgHandlers(MessageTable)
    if self.faceItem then
        for k,item in pairs(self.faceItem) do
            item:Dispose()
        end
    end
    self.faceItem = nil
    if self.itemList and self.itemList.goodList then
        for i = 1, #self.itemList.goodList do
            for k,item in pairs(self.itemList.goodList[i]) do
                item:Dispose()
            end
        end
        self.itemList.goodList = nil
        self.itemList = nil
    end
    
    if self:IsValid() then
		self:UnsubscribeEvents()
	end
	self.__base:Close()
    window = nil
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowSubscribeEvents
--[[订阅UI事件]]
function UTavern_Task:SubscribeEvents()
    ----///<<< Button Proxy Line >>>///-----
    self.closeBtnEvent = function()
        if closeEvent then
            closeEvent()
            closeEvent = nil
        end
        if self.UIRoot then
            local canvasGroupObj = self.UIRoot:AddComponent(typeof(CanvasGroup))
            if canvasGroupObj and (not util.IsObjNull(canvasGroupObj.gameObject)) then
                LeanTween.alphaCanvas(canvasGroupObj, 0, const.playHideAniTime)
            end
            windowMgr:UnloadModule("ui_tavern_task_new")
        end
    end
    if self.closeBtn then
        self.closeBtn.onClick:AddListener(self.closeBtnEvent)
    end
end --///<<< function

--@endregion 

--@region WindowUnsubscribeEvents
--[[退订UI事件]]
function UTavern_Task:UnsubscribeEvents()
--@region User
    if self.closeBtn then
        self.closeBtn.onClick:RemoveListener(self.closeBtnEvent)
    end
--@endregion 
end --///<<< function

--region 功能函数区
---********************功能函数区**********---

function UTavern_Task:FreshItemUI()
    self.itemList = self.itemList or {}
    self.itemList.goodList = self.itemList.goodList or {}
    local goodListIndex = 1
    self.faceItem = self.faceItem or {}
    local data = serData.recordData
    local isRobRes = serData.isRobRes
    local taskID = serData.taskID
    for i = 1, #data do
        local isRob = isRobRes and isRobRes or data[i].isRob
        local cfgID = data[i].taskSID and ui_tavern_mgr.GetCfgIDbySID(data[i].taskSID) or taskID
        --获取不到对应配置时候，说明当前任务记录无效，跳过
        if cfgID then
            local tmp = self.itemList[i] or GameObject.Instantiate(self.Img_ListItemPerson.gameObject, self.Img_ListItemPerson.transform.parent)
            local scroll_rect_item = tmp:GetComponent(typeof(ScrollRectItem))
            scroll_rect_item.gameObject:SetActive(true)

            --Title
            local titleText = scroll_rect_item:Get("titleText")
            local allianceShortName = data[i].allianceShortName ~= "" and "[" .. data[i].allianceShortName.."]" or data[i].allianceShortName
            if isRob then
                titleText.text =  "<color=#FF4D2A>"..allianceShortName..data[i].name.."</color>"
            else
                titleText.text =  "<color=#3A6AA9>"..allianceShortName..data[i].name.."</color>"
            end
            
            --contentText
            local helpText = scroll_rect_item:Get("helpText")
            local robText = scroll_rect_item:Get("robText")
            helpText:SetActive(not isRob)
            robText:SetActive(isRob)

            --bg
            local bg = scroll_rect_item:Get("bg")
            bg.color = isRob and {r=255/255, g=143/255, b=135/255, a=0.5} or {r=190/255, g=233/255, b=255/255, a=0.5}
            
            --GoodList
            local Container = scroll_rect_item:Get("Container")
            Container:SetActive(isRob)
            if isRob then
                local goodList = self.itemList.goodList[goodListIndex] or {}
                local GoodsList = scroll_rect_item:Get("GoodsList")
                local rewardList = reward_mgr.GetRewardGoodsList(game_scheme:SecretTask_0(cfgID).SnatchRewards)
                goodList = UTavern_Task.SetTaskRewardListShow(goodList, rewardList, GoodsList,0.48)
                self.itemList.goodList[goodListIndex] = goodList
                goodListIndex = goodListIndex + 1
            end

            --time
            local timeText = scroll_rect_item:Get("timeText")
            local time_util = require "time_util"
            timeText.text = time_util.ConvertStamp3Time(data[i].occurTime)

            --face
            local faceTrans = scroll_rect_item:Get("faceTrans")
            local faceItem = self.faceItem[i] or face_item_new.CFaceItem():Init(faceTrans, nil, 1)
            --适配faceID 2025.4.2 新增faceStr替代原本的faceID
            local faceStr = data[i].faceID
            if data[i].faceStr and not string.IsNullOrEmpty(data[i].faceStr) then
                faceStr = data[i].faceStr
            end
            faceItem:SetFaceInfo(faceStr)
            faceItem:SetFrameID(data[i].frameID,true)
            self.faceItem[i] = faceItem

            self.itemList[i] = scroll_rect_item
        end
    end
end

--设置任务奖励列表
function UTavern_Task.SetTaskRewardListShow(goodList, data, goodListTransform,scale)
    --先隐藏
    if goodList then
        for k, v in ipairs(goodList) do
            if v.gameObject then
                v.gameObject:SetActive(false)
            end
        end
    end
    for k,v in ipairs(data) do
        local item = goodList[k] or goods_item.CGoodsItem("ui_tavern")
        item:SetMultiGain(v.ItemFlag)
        item:Init(goodListTransform, nil, scale)
        item:SetGoods(nil,v.id,v.num, function()
            local iui_item_detail =  require "iui_item_detail"
            local item_data = require "item_data"
            iui_item_detail.Show(v.id,nil,  item_data.Item_Show_Type_Enum.Reward_Interface, v.num,nil, nil, nil)
        end)
        --item.gameObject:SetActive(true)
        goodList[k] = item
    end
    return goodList
end

---********************end功能函数区**********---
--@region WindowInherited
local CUTavern_Task = class(ui_base, nil, UTavern_Task)
--@endregion 

--@region ModuleFunction
function Show(data)
    if window == nil then
        window = CUTavern_Task()
        window._NAME = _NAME
        window:LoadUIResource("ui/prefabs/uitavern_task.prefab", nil, nil, nil)
    end
    serData = data
    window:Show()
    return window
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end

function Close()
    if window ~= nil then
        Hide()
        window:Close()
        window = nil
    end
end

--@endregion 

--@region RegisterMsg
MessageTable =
{ --///<<< tableStart
} --///<<< tableEnd
--@endregion 

