--- ui_alliance_members_controller.txt.txt
--- Generated by Emmy<PERSON>ua(https://github.com/EmmyLua)
--- Created by hans<PERSON><PERSON>.
--- DateTime: 2024/6/27 16:02
--- desc:    创建联盟用户显示
---


local require = require
local ipairs = ipairs
local table = table
local newclass = newclass

local alliance_const = require "alliance_const"
local lang = require "lang"
local module_scroll_list = require "scroll_list"
local controller_base = require "controller_base"
local event_alliance_define = require "event_alliance_define"
local alliance_data = require "alliance_data"
local alliance_pb = require "alliance_pb"
local player_mgr = require "player_mgr"
local flow_text = require "flow_text"
local windowMgr = require "ui_window_mgr"
local alliance_user_data = require "alliance_user_data"
module("ui_alliance_members_controller")

local controller = nil
local UIAllianceMembersController = newclass("ui_alliance_members_controller", controller_base)

local indexR = 0;
local posData = {}
local isCurAlliance = false --是否是自己联盟
local isAdjust = false -- 当前成员是否可以调整等级
local isOffcial = false -- 当前成员是否可以调整官职

local allianceBaseData = { };
local isCurUser = false;

--[[窗口初始化]]
function UIAllianceMembersController:Init(view_name, controller_name, data)
    self.__base.Init(self, view_name, controller_name)
    self:SubscribeEvents()
    --打开界面的时候一定要传值  不传值  会报错
    allianceBaseData = data
    self:InitView(allianceBaseData)
end

--[[界面被显示的时候调用]]
function UIAllianceMembersController:OnShow()
    self.__base.OnShow(self)
end

function UIAllianceMembersController:InitView(data)
    local userViewData = {}
    --当前成员联盟数据
    local userAlliancedata = alliance_data.GetUserAllianceData()
    --当前成员阶段
    userViewData.curstate = 0
    --判断联盟id 是否是自己
    if userAlliancedata and userAlliancedata.allianceId == allianceBaseData.allianceId then
        isCurUser = true
        local userRoleData = alliance_user_data.GetUserRoleData()
        userViewData.curstate = userRoleData.authority
        self:RefreshPermission()
    end
    --联盟官职数据
    posData = alliance_user_data.GetPosData(isCurUser);
    --联盟官职信息赋值
    userViewData.userOffical = posData
    --联盟阶级数量 和 最大数量
    userViewData.userState = {};
    for i = 1, 4 do
        local numData = {}
        numData.num = alliance_user_data.GetOnLineRankCount(i, isCurUser);
        --numData.maxNum = alliance_user_data.GetAuthorityPeopleLimit(i)
        numData.maxNum = alliance_user_data.GetRankCount(i, isCurUser);
        table.insert(userViewData.userState, numData)
    end
    --查看 联盟旗帜ID
    if allianceBaseData then
        local flagData = alliance_data.GetFlagIdData(allianceBaseData.flag)
        if flagData then
            userViewData.allianceIconId = flagData.iconID;
        end
    end
    --当前所在的id
    self:TriggerUIEvent( "ShowAllianceInfo", userViewData)
    self:RefreshBossInfo()
end

function UIAllianceMembersController:Close()
    self.bossData = {}
    self.__base.Close(self)
    controller = nil
    indexR = 0;
    isAdjust = false
    isOffcial = false
    isCurUser = false;
    posData = {}
    allianceBaseData = {};
end

function UIAllianceMembersController:SubscribeEvents()
    self.refreshData = function(eventName)
        self:RefreshPermission()
        self:RefreshRankData(indexR)
        self:RefreshPosInfoData()
        self:RefreshRankNum()
        self:RefreshBossInfo()
        self:RefreshCurState()
    end
    self:RegisterEvent(event_alliance_define.UPDATE_ALLIANCE_ROLE_DATA, self.refreshData)
    self.refreshExpel = function(eventName)
        self:RefreshPermission()
        self:RefreshRankData(indexR)
        self:RefreshPosInfoData()
        self:RefreshRankNum()
        self:RefreshBossInfo()
        self:RefreshCurState()
    end
    self:RegisterEvent(event_alliance_define.UPDATE_ALLIANCE_EXPEL_ROLE, self.refreshExpel)
    self.removePostion = function(eventName, roleId)
        --刷新官职信息
        self:RefreshPosInfoData()
    end
    self:RegisterEvent(event_alliance_define.UPDATE_ALLIANCE_MODIFY_REMOVE_POSITION, self.removePostion)
    self.updateAllianceR5Data = function(eventName)

    end
    self:RegisterEvent(event_alliance_define.UPDATE_ALLIANCE_ROLE_R5_DATA, self.updateAllianceR5Data)
    self.exitAlliance = function(eventName, data)
        windowMgr:UnloadModule(self.view_name)
    end
    self:RegisterEvent(event_alliance_define.EXIT_ALLIANCE, self.exitAlliance)
end

function UIAllianceMembersController:RefreshBossInfo()
    self.bossData = alliance_user_data.GetBossData(isCurUser);
    self:TriggerUIEvent( "RefreshBossInfo", self.bossData)
end

function UIAllianceMembersController:RefreshPermission()
    --是否有权限
    isAdjust = alliance_user_data.HasPermission(alliance_const.PERMISSIONS.ADJUST_MEMBER_CLASS)
    --是否可以调整官职
    isOffcial = alliance_user_data.HasPermission(alliance_const.PERMISSIONS.OFFICE_MANAGE)
end

function UIAllianceMembersController:RefreshCurState()
    local userRoleData = alliance_user_data.GetUserRoleData()
    self:TriggerUIEvent( "SetPlayerInLevel", userRoleData.authority)
end

--region ModuleFunction 
function Show()
    if controller == nil then
        controller = UIAllianceMembersController.new()
    end
    return controller
end

--endregion
function UIAllianceMembersController:ShowRoleInfo(data)
    --[[    if data.roleid ~= player_mgr.GetPlayerRoleID() then
            --打开玩家详情
            local net_arena_module = require "net_arena_module"
            local common_new_pb = require "common_new_pb"
            net_arena_module.Send_PLAYER_DETAILS(data.roleid, common_new_pb.emDetailType_TopCE)
            local ui_player_detail_info_ex = require "ui_player_detail_info_ex"
            ui_player_detail_info_ex.SetHeroInfo(data.faceId, data.roleLv, data.name, data.roleid, nil, nil, nil, data.context, nil, data.avatarFrame)
            ui_player_detail_info_ex.SetShowType(ui_player_detail_info_ex.Show_Type_Enum.Common, nil, true)
            --ui_player_detail_info_ex.ShowCoolieBtn(ui_player_detail_info_ex.Source_Window_Enum.Chat)
            windowMgr:ShowModule("ui_player_detail_info_ex")
            --构建lineUps结构
            local arena_pb = require "arena_pb"
            local arenaLineUp = arena_pb.ArenaLineUp()
            local lineUps = { [1] = arenaLineUp }
            arenaLineUp.roleID = data.roleid
            arenaLineUp.personalCE = data.ce
            local dbid = data.roleid
            ui_player_detail_info_ex.PlayerDetailFunc(nil, lineUps, data.guildname, nil, nil, data.faceId, data.name, data.roleLv, data.passStage, data.worldid, dbid, data.avatarFrame, nil, nil, nil, data.arrTreasure)
        end]]

end

--region 官职点击弹窗
function UIAllianceMembersController:OnBtn_specialHead1ClickedProxy()
    self:OfficalPostionView(alliance_pb.emAlliancePosition_zhanshen)
end
function UIAllianceMembersController:OnBtn_specialHead2ClickedProxy()
    self:OfficalPostionView(alliance_pb.emAlliancePosition_zhaomuguan)
end
function UIAllianceMembersController:OnBtn_specialHead3ClickedProxy()
    self:OfficalPostionView(alliance_pb.emAlliancePosition_nvshen)
end
function UIAllianceMembersController:OnBtn_specialHead4ClickedProxy()
    self:OfficalPostionView(alliance_pb.emAlliancePosition_waijiaoguan)
end
function UIAllianceMembersController:OfficalPostionView(index)
    --[[local data = {}
    data.roleData = posData[index]
    data.posId = index;
    --flow_text.Add("弹出用户信息")
    flow_text.Add(lang.Get(600408))]]
end
--刷新官职信息
function UIAllianceMembersController:RefreshPosInfoData()
    posData = alliance_user_data.GetPosData(isCurUser);
    self:TriggerUIEvent( "RefreshPosInfo", posData)
end
--endregion

--region 阶级Btn事件
--返回按钮
function UIAllianceMembersController:OnBtn_closeClickedProxy()
    windowMgr:UnloadModule("ui_alliance_members");
end
--刷新阶级数量
function UIAllianceMembersController:RefreshRankNum()
    local data = {}
    for i = 1, 4 do
        local numData = {}
        numData.num = alliance_user_data.GetOnLineRankCount(i, isCurUser);
        --numData.maxNum = alliance_user_data.GetAuthorityPeopleLimit(i)
        numData.maxNum = alliance_user_data.GetRankCount(i, isCurUser);
        table.insert(data, numData)
    end
    self:TriggerUIEvent( "RefreshOnlineNumber", data)
end
--展开R4界面
function UIAllianceMembersController:OnBtn_R4ButtonClickedProxy()
    if indexR == alliance_pb.emAllianceAuthority_R4 then
        self:CloseRList()
        indexR = 0;
        return ;
    end
    indexR = alliance_pb.emAllianceAuthority_R4
    self:RButtonEventData();
end
--展开R3界面
function UIAllianceMembersController:OnBtn_R3ButtonClickedProxy()
    if indexR == alliance_pb.emAllianceAuthority_R3 then
        self:CloseRList()
        indexR = 0;
        return
    end
    indexR = alliance_pb.emAllianceAuthority_R3;
    self:RButtonEventData();
end
--展开R2界面
function UIAllianceMembersController:OnBtn_R2ButtonClickedProxy()
    if indexR == alliance_pb.emAllianceAuthority_R2 then
        self:CloseRList()
        indexR = 0;
        return
    end
    indexR = alliance_pb.emAllianceAuthority_R2;
    self:RButtonEventData();
end
--展开R1界面
function UIAllianceMembersController:OnBtn_R1ButtonClickedProxy()
    if indexR == alliance_pb.emAllianceAuthority_R1 then
        self:CloseRList()
        indexR = 0;
        return
    end
    indexR = alliance_pb.emAllianceAuthority_R1;
    self:RButtonEventData();
end
--添加事件
function UIAllianceMembersController:RButtonEventData()
    self:RefreshRankData(indexR)
end

function UIAllianceMembersController:RefreshRankData(index)
    if index >= alliance_pb.emAllianceAuthority_R1 and indexR <= alliance_pb.emAllianceAuthority_R4 then
        local data = alliance_user_data.GetRankSortData(index, isCurUser)
        if data then
            for i, v in ipairs(data) do
                v.ClickMemeberMgr = ClickMemeberMgr;
                v.isAdjust = isAdjust
                v.isCurUser = isCurUser
                v.isCurPlayer = false;
                if v.roleId == player_mgr.GetPlayerRoleID() then
                    v.isCurPlayer = true;
                end
            end
            self:TriggerUIEvent( "ShowData", indexR, data, true)
        end
    end
end

--点击管理按钮
function ClickMemeberMgr(index, dataItem)
    if not isAdjust then
        flow_text.Add(lang.Get(600409))
        return
    end
    windowMgr:ShowModule("ui_member_mgr", nil, nil, dataItem);
end

--关闭之前的列表
function UIAllianceMembersController:CloseRList()
    self:TriggerUIEvent( "CloseRList")
end
--endregion