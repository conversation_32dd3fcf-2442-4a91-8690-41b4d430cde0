--- ui_kick_window_controller.txt
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by .
--- DateTime: 
--- desc:    
---
local require = require
local newclass = newclass

local alliance_const = require "alliance_const"
local module_scroll_list = require "scroll_list"
local controller_base = require "controller_base"
local event_alliance_define = require "event_alliance_define"
local alliance_user_data = require "alliance_user_data"
local net_alliance_module = require "net_alliance_module"
local windowMgr = require "ui_window_mgr"
module("ui_kick_window_controller")

local controller = nil
local UIController = newclass("ui_kick_window_controller", controller_base)

local roleData = {}
--[[窗口初始化]]
function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self,view_name,controller_name)
    self:SubscribeEvents()
    self:InitView(data)
end
function UIController:InitView(data)
    roleData = data;
end

--[[界面被显示的时候调用]]
function UIController:OnShow()
    self.__base.OnShow(self)
end
function UIController:OnBtn_cancelBtnClickedProxy()
    windowMgr:UnloadModule(self.view_name)
end
function UIController:OnBtn_OkBtnClickedProxy()
    local userAllianceData = alliance_user_data.GetUserRoleData()
    --判断权限
    if userAllianceData then
        if alliance_user_data.HasPermission(alliance_const.PERMISSIONS.EXPEL_MEMBER) then
            net_alliance_module.MSG_ALLIANCE_EXPEL_REQ(roleData.roleId)
            windowMgr:UnloadModule("ui_member_mgr")
            windowMgr:UnloadModule("ui_kick_window")
        end
    end
end
function UIController:OnBtn_closeBtnClickedProxy()
    windowMgr:UnloadModule("ui_kick_window")
end

function UIController:Close()
    self.__base.Close(self)
    controller = nil
end

function UIController:SubscribeEvents()
    self.exitAlliance = function(eventName, data)
        windowMgr:UnloadModule(self.view_name)
    end
    self:RegisterEvent(event_alliance_define.EXIT_ALLIANCE,self.exitAlliance)
end
--region ModuleFunction 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end

--endregion
