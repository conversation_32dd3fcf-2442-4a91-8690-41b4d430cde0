--- congress_data.txt  --------------------------------------
--- author: 农勇智
--- Date:   2025/1/4 11:06
--- ver:    1.0
--- desc:   国会数据管理
-------------------------------------------------------------
local require = require
local ipairs = ipairs
local pairs = pairs

local event = require "event"
local game_scheme = require "game_scheme"
local player_mgr = require "player_mgr"
local GWG = GWG
local red_const = require "red_const"
local red_system = require "red_system"
local net_Congress_module = require "net_Congress_module"
local net_sandbox_module = require "net_sandbox_module"
local event_Congress_define = require("event_Congress_define")
local data_mgr = require "data_mgr"
local gw_ed = require("gw_ed")
local flowtext              = require "flow_text"
local player_prefs=require "player_prefs"
local lang = require "lang"

local string = string
local tonumber = tonumber

---@class CongressData
module("congress_data")
local M = {}
local self = M --简化写法，静态类中直接也用Self获取自身
---所有数据存储
local _d = data_mgr:CreateData("congress_data")
---非服务器数据存储
local mc = _d.mde.const
local msg_item_match_funs = {
    --当前协议中没有数组，不需要处理
}

local event_const = event_Congress_define

--module Init方法 指定调用不能修改名称
function M.Init()
    M.RegisterEvents()
    mc.officialList = {} --官职的数据列表
    mc.presidentialDeclaration = "" --总统宣言
    mc.presidentMailData = 0 --总统邮件冷却时间     
    mc.congressQueueUp = {} --官职排队队列
    mc.congress_applyfor = {} --官职的申请列表
    mc.selfApplyforList = {} --个人的申请信息
    mc.awardNumList = {} --嘉奖数量信息
    mc.awardAllowList = {} --嘉奖分配列表
    mc._applyIndex = {}
    mc.QueueSetting = 0 --当前的就任档次
    
    mc.QueueTimeList = {}
    local timeCfg = game_scheme:GWMapConstant_0(106)
    if timeCfg then
        for i = 0,timeCfg.szParam.count - 1 do
            mc.QueueTimeList[i+1] = tonumber(timeCfg.szParam.data[i])
        end
    end

    mc.sandboxSid = 0 --缓存自己当前的沙盘ID
    mc.selfPositionID = 0 --缓存自己的官职
    mc.selfTimeBegin = 0 --缓存自己的上任时间点

    mc.isInit = false;
    mc.OnSetMyPosition = false;
    mc.initDeclaration = false;
end

function M.RegisterEvents()
    event.Register(event_Congress_define.TMSG_CONGRESS_OFFICIAL_NTF, self.OnSetOfficialData)
    event.Register(event_Congress_define.TMSG_CONGRESS_MANIFESTO_NTF, self.OnSetPresidentialDeclaration)
    event.Register(event_Congress_define.TMSG_CONGRESS_MAIL_NTF, self.OnSetPresidentialMailData)
    event.Register(event_Congress_define.TMSG_CONGRESS_APPLYFOR_LIST_NTF, self.OnSetApplyforData)
    event.Register(event_Congress_define.TMSG_CONGRESS_QUEUESETTING_NTF, self.OnSetQueueData)
    event.Register(event_Congress_define.TMSG_CONGRESS_APPLYFOR_SELF_NTF, self.OnSetSelfApplyforData)
    event.Register(event_Congress_define.TMSG_CONGRESS_QUEUEUP_NTF, self.OnSetQueueListData)
    event.Register(event_Congress_define.TMSG_CONGRESS_AWARD_INFO_NTF, self.OnSetAwardInfoData)
    gw_ed.mgr:Register(gw_ed.GW_SAND_NET_EVENT, self.SetSandboxSid)

    red_system.RegisterRedFunc(red_const.Enum.Congress, self.OnGetRedDot)
end

local function GetRequestBase()
    return {sandboxSid = net_sandbox_module.GetZoneSandId()}
end

local function getSandboxSid()
    return net_sandbox_module.GetZoneSandId()
end

local function ReportCongressEvent(eventName, params)
    params = params or {}
    event.Trigger(event.GAME_EVENT_REPORT, eventName, params)
end

function M.SetSandboxSid()
    mc.sandboxSid = getSandboxSid()
    if mc.sandboxSid ~= 0 and not mc.isInit then
        mc.isInit = true
        self.InitConfig()
    end
end

function M.InitConfig()
    net_Congress_module.MSG_CONGRESS_MAINDATA_REQ(GetRequestBase()) --请求官职信息
    net_Congress_module.MSG_CONGRESS_AWARD_INFO_REQ(GetRequestBase()) --请求嘉奖信息
    
end
--region 嘉奖相关

function M.OnGetAwardData()
    net_Congress_module.MSG_CONGRESS_AWARD_INFO_REQ(GetRequestBase()) --请求嘉奖信息
end

function M.OnGetAwardRecord()
    net_Congress_module.MSG_CONGRESS_AWARDRECORD_REQ(GetRequestBase()) --请求嘉奖记录信息
end

---嘉奖分配
function M.OnAwardAllocate(dbid, enType)
    local temp = {
        sandboxSid = getSandboxSid(),
        dbid = dbid,
        enType = enType,
    }
    net_Congress_module.MSG_CONGRESS_AWARD_ALLOCATE_REQ(temp)
end

---设置嘉奖信息
function M.OnSetAwardInfoData(_, msg)
    if msg.numTbl then
        mc.awardNumList = {}
        for i, v in ipairs(msg.numTbl) do
            mc.awardNumList[v.enType] = v;
        end
    end
    if msg.allowTbl then
        mc.awardAllowList = {}
        for i, v in ipairs(msg.allowTbl) do
            mc.awardAllowList[v.dbid] = v
        end
    end
    event.Trigger(event_Congress_define.ON_CONGRESS_AWARD_INFO_UPDATE)
end

---获取嘉奖数
function M.OnGetAwardNumList()
    return mc.awardNumList;
end

function M.OnGetAwardAllowList()
    return mc.awardAllowList;
end

--endregion

--region 官职相关

---请求官职相关信息
function M.OnGetOfficialData()
    net_Congress_module.MSG_CONGRESS_MAINDATA_REQ(GetRequestBase()) --请求官职信息
end

function M.OnGetSelfPositionID()
    return mc.selfPositionID
end

local function ValidatePositionConfig(positionID)
    local cfg = game_scheme:CongressOfficialPosition_0(positionID)
    if not cfg then
        local log = require "log"
        --log.Error("不存在ID为"..positionID.."的官职！")
        return false,nil
    end
    return true,cfg
end

--获得自身的权力
function M.OnGetSelfAuthority()
    local val, cfg = ValidatePositionConfig(mc.selfPositionID)
    if not val then
        return {}
    end
    local temp = {}
    if cfg then
        for i = 0,cfg.PositionAuthority.count - 1 do
            temp[cfg.PositionAuthority.data[i]] = true
        end
    end
    return temp;
end

---官员列表
function M.OnSetOfficialData(_, msg)
    local selfID = player_mgr.GetPlayerRoleID()
    if not mc.OnSetMyPosition then
        mc.selfPositionID = player_prefs.GetCacheData("selfPositionId",0) --拿自己缓存的官职
        mc.selfTimeBegin = player_prefs.GetCacheData("selfTimeBegin",0) --拿自己缓存的上任时间点
        mc.OnSetMyPosition = true
    end

    local selfPositionID = mc.selfPositionID --缓存自己的官职ID
    local selfTimeBegin = mc.selfTimeBegin --缓存自己的上任时间点
    if msg.isAll then
        mc.officialList = {} --全量更新，清空缓存
    end
    if msg.officialList then
        for i, v in ipairs(msg.officialList) do
            mc.officialList[v.positionID] = v; --直接覆盖
        end
    else
        
    end
    local selfPosData = self.OnGetPlayerPosition(selfID)
    mc.selfPositionID = selfPosData and selfPosData.positionID or 0--0
    mc.selfTimeBegin = selfPosData and selfPosData.time_begin or 0
    player_prefs.SetCacheData("selfPositionId",mc.selfPositionID) --缓存自己的官职
    player_prefs.SetCacheData("selfTimeBegin",mc.selfTimeBegin) --缓存自己的上任时间点
    --for i,v in pairs(mc.officialList) do
    --    if v.playerInfo.roleID == selfID then
    --        mc.selfPositionID = v.positionID
    --    end
    --end
    --if mc.OnSetMyPosition then
        if mc.selfPositionID ~= 0 then
            if mc.selfPositionID ~= selfPositionID or mc.selfTimeBegin ~= selfTimeBegin then --官职发生变化，且自己的官职不为0
                local val, cfg = ValidatePositionConfig(mc.selfPositionID)
                if not val then
                    return
                end
                if cfg then
                    flowtext.Add(string.format2(lang.Get(671079),lang.Get(cfg.PositionName)))
                end
            end
        end
    --else
    --    mc.OnSetMyPosition = true
    --end

    event.Trigger(event_Congress_define.ON_CONGRESS_OFFICIAL_UPDATE)
end

function M.OnGetPlayerPosition(roleID)
    for i, v in pairs(mc.officialList) do
        if v.playerInfo.roleID == roleID then
            return v
        end
    end
    return nil
end

---获取官员列表
function M.OnGetOfficialList()
    return mc.officialList or {}
end

---总统宣言设置
function M.OnSetPresidentialDeclaration(_, msg)
    if msg:HasField("default") and msg.default == false then
        local lang = require "lang"
        mc.presidentialDeclaration = lang.Get(671021)
    elseif msg.text then
        mc.presidentialDeclaration = msg.text;
    end
    if mc.initDeclaration then
        ReportCongressEvent( "Kongress_Declaration",{})
    else
        mc.initDeclaration = true
    end
    
    event.Trigger(event_Congress_define.ON_CONGRESS_MANIFESTO_UPDATE, mc.presidentialDeclaration)
end

---获取总统宣言
function M.OnGetPresidentialDeclaration()
    return mc.presidentialDeclaration
end

---总统邮件冷却设置
function M.OnSetPresidentialMailData(_, msg)
    if msg.timeCD then
        mc.presidentMailData = msg.timeCD
    end
    event.Trigger(event_Congress_define.ON_CONGRESS_MAIL_UPDATE)
end

---获取总统邮件冷却
function M.OnGetPresidentialMailData()
    return mc.presidentMailData
end

---官员申请记录
function M.OnSetApplyforData(_, msg)
    if msg.positionID and msg.list then
        mc.congress_applyfor[msg.positionID] = {
            positionID = msg.positionID,
            list = msg.list,
        }
    end
    
    mc._applyIndex = {}
    for posID,applyData in pairs(mc.congress_applyfor) do
        for _,applicant in ipairs(applyData.list) do
            mc._applyIndex[applicant.playerInfo.roleID] = posID
        end
    end
    
    event.Trigger(event_Congress_define.ON_CONGRESS_APPLYFOR_LIST_UPDATE)
    red_system.TriggerRed(red_const.Enum.Congress)
end

--申请队列红点
function M.OnGetRedDot()
    local count = 0
    local selfAuthority = self.OnGetSelfAuthority()
    local isPre = #selfAuthority > 0 and selfAuthority[2] == true
    if isPre then --有任命资格的官职才计算红点数量
        for i,v in pairs(mc.congress_applyfor) do
            count = count + #v.list
        end
    end
    return count
end

---获取官员申请记录
function M.OnGetApplyforData()
    return mc.congress_applyfor;
end

function M.OnGetQueueSetting()
    return mc.QueueSetting
end

function M.OnGetQueueSettingTime()
    return mc.QueueTimeList[mc.QueueSetting] or 0
end

function M.OnGetQueueSettingTimeList()
    return mc.QueueTimeList
end

---就任档次
function M.OnSetQueueData(_, msg)
    mc.QueueSetting = msg.time_index --当前的就任档次

    --event.Trigger(event_Congress_define.ON_CONGRESS_QUEUESETTING_UPDATE)
end

---排队队列
function M.OnSetQueueListData(_, msg)
    if msg.positionID and msg.list then
        mc.congressQueueUp[msg.positionID] = {
            positionID = msg.positionID,
            list = msg.list
        }
    end
    event.Trigger(event_Congress_define.ON_CONGRESS_QUEUEUP_UPDATE)
end

---获取排队队列
function M.OnGetQueueListData()
    return mc.congressQueueUp
end

---获取自己的排队信息
function M.OnGetSelfQueueList()
    local roleID = player_mgr.GetPlayerRoleID()
    for i,v in pairs(mc.congressQueueUp) do
        for j,k in ipairs(v.list) do
            if k.playerInfo.roleID == roleID then
                return i,j;
            end
        end
    end
    return 0,0;
end

---请求总统上任记录
function M.OnGetPresidentRecord()
    net_Congress_module.MSG_CONGRESS_PRESRECORD_REQ(GetRequestBase())
end

---总统宣言修改请求
function M.OnSetPresidentialDeclarationReq(value)
    local temp = {
        sandboxSid = getSandboxSid(),
        text = value,
    }
    net_Congress_module.MSG_CONGRESS_MANIFESTO_EDIT_REQ(temp)
end

---总统邮件发送
function M.OnSentPresidentMail(title, value)
    --ReportCongressEvent( "Kongress_Mail",{Email_Title = title,Email_Content = value})
    local temp = {
        sandboxSid = getSandboxSid(),
        title = title,
        content = value,
    }
    net_Congress_module.MSG_CONGRESS_SENDMAIL_REQ(temp)
end

---任命官职
function M.OnApplyPosition(positionID, dbid)
    --ReportCongressEvent( "Kongress_Appoint",{PositionID = positionID,Appointed_role_ID = dbid}) --打点上报
    local temp = {
        sandboxSid = getSandboxSid(),
        positionID = positionID,
        dbid = dbid,
    }
    net_Congress_module.MSG_CONGRESS_APPOINT_REQ(temp)
end

---罢免官职
function M.OnDissmissPosition(positionID)
    local temp = {
        sandboxSid = getSandboxSid(),
        positionID = positionID,
    }
    net_Congress_module.MSG_CONGRESS_DISMISS_REQ(temp)
end

---请求官职记录
function M.OnGetOfficeRecord(positionID)
    local temp = {
        sandboxSid = getSandboxSid(),
        positionID = positionID,
    }
    net_Congress_module.MSG_CONGRESS_OFFICE_RECORD_REQ(temp)
end

---请求官职申请队列
function M.OnGetApplyforList(positionID)
    local temp = {
        sandboxSid = getSandboxSid(),
        positionID = positionID,
    }
    net_Congress_module.MSG_CONGRESS_APPLYFOR_LIST_REQ(temp)
end

---官职申请列表管理
function M.OnApplyforMgr(positionID, dbid, pass)
    if pass == true then
        ReportCongressEvent( "Kongress_pass",{PositionID = positionID}) --打点上报
    else
        ReportCongressEvent( "Kongress_Reject",{PositionID = positionID}) --打点上报
    end
    local temp = {
        sandboxSid = getSandboxSid(),
        positionID = positionID,
        dbid = dbid,
        pass = pass --是否通过，true通过false拒绝
    }
    net_Congress_module.MSG_CONGRESS_APPLYFOR_MANAGE_REQ(temp)
end

---设置个人申请信息
function M.OnSetSelfApplyforData(_, msg)
    if msg.info then
        mc.selfApplyforList = {}
        for i, v in ipairs(msg.info) do
            mc.selfApplyforList[v.positionID] = v;
        end
    end
    event.Trigger(event_Congress_define.ON_CONGRESS_APPLYFOR_SELF_UPDATE)
end

---获得个人申请信息
function M.OnGetSelfApplyforData()
    return mc.selfApplyforList
end

---获取自己当前申请的官职
function M.OnGetSelfApplyforId()
    if mc.congress_applyfor then
        local selfID = player_mgr.GetPlayerRoleID()
        for i,v in pairs(mc.congress_applyfor) do
            local positionID = i
            for j,k in ipairs(v.list) do
                if k.playerInfo.roleID == selfID then
                    return positionID
                end
            end
        end
        return 0
    else
        return 0
    end

end

---获取自己当前是否正在申请某官职，true是的false不是
function M.SelfInOnApply(positionID)
    return mc._applyIndex[player_mgr.GetPlayerRoleID()] == positionID
    --if mc.congress_applyfor and mc.congress_applyfor[positionID] then
    --    local selfID = player_mgr.GetPlayerRoleID()
    --    for i,v in ipairs(mc.congress_applyfor[positionID].list) do
    --        if v.playerInfo.roleID == selfID then
    --            return true
    --        end
    --    end
    --    return false
    --else
    --    return false
    --end
end

---个人官职申请
function M.OnSelfApplyfor(positionID, operate)
    ReportCongressEvent( "Kongress_Apply",{PositionID = positionID}) --打点上报
    local val, cfg = ValidatePositionConfig(positionID)
    if not val then
        return
    end
    local selfLv = GWG.GWHomeMgr.buildingData.GetBuildingMainLevel()
    if cfg then
        if selfLv >= cfg.LevelCondition then
            local temp = {
                sandboxSid = getSandboxSid(),
                positionID = positionID,
                operate = operate, --true申请false撤销
            }
            net_Congress_module.MSG_CONGRESS_APPLYFOR_REQ(temp)
        else
            flowtext.Add(string.format2(lang.Get(667148),cfg.LevelCondition))
        end
    else
        local log = require "log"
        log.Error("官职信息在CongressOfficialPosition表中不存在！ID："..positionID)
    end

end

---请求上任时间档次
function M.OnSetQueueTime(timeIndex)
    local temp = {
        sandboxSid = getSandboxSid(),
        time_index = timeIndex,
    }
    net_Congress_module.MSG_CONGRESS_SET_TIME_REQ(temp)
end

---请求排队队列
function M.OnGetQueueList(positionID)
    local temp = {
        sandboxSid = getSandboxSid(),
        positionID = positionID,
    }
    net_Congress_module.MSG_CONGRESS_QUEUEUP_LIST_REQ(temp)
end

---取消排队
function M.OnCancelQueue(positionID, dbid,targetAllianceId)
    local queue = mc.congressQueueUp[positionID]
    local len = queue and #queue or 0
    local alliance_data = require("alliance_data")
    local userAllianceData = alliance_data.GetUserAllianceData()
    local allianceId = -1;
    if userAllianceData and userAllianceData.allianceId ~= nil then
        allianceId = userAllianceData.allianceId
    end
    local temp = {
        sandboxSid = getSandboxSid(),
        positionID = positionID,
        dbid = dbid,
    }
    net_Congress_module.MSG_CONGRESS_QUEUEUP_CANCEL_REQ(temp)
end

---获取职位官员名称
function M.OnGetOfficialPlayerInfo(positionType)
    if mc.officialList and mc.officialList[positionType] then
        return mc.officialList[positionType]
    end
    return nil
end

--endregion



--- 数据清理
function M.Dispose()
    mc.officialList = {} --官职的数据列表
    mc.presidentialDeclaration = "" --总统宣言
    mc.presidentMailData = 0 --总统邮件冷却时间
    mc.congressQueueUp = {} --官职排队队列
    mc.selfApplyforList = {} --个人的申请信息
    mc.awardNumList = {} --嘉奖数量信息
    mc.awardAllowList = {} --嘉奖分配列表
    mc.congress_applyfor = {}
    mc.QueueSetting = 0 --当前的就任档次
    mc.OnSetMyPosition = false
    mc.isInit = false
    mc.initDeclaration = false
    mc._applyIndex = {}
    M.UnregisterEvents()

end

function M.UnregisterEvents()
    event.Unregister(event_Congress_define.TMSG_CONGRESS_OFFICIAL_NTF, self.OnSetOfficialData)
    event.Unregister(event_Congress_define.TMSG_CONGRESS_MANIFESTO_NTF, self.OnSetPresidentialDeclaration)
    event.Unregister(event_Congress_define.TMSG_CONGRESS_MAIL_NTF, self.OnSetPresidentialMailData)
    event.Unregister(event_Congress_define.TMSG_CONGRESS_APPLYFOR_LIST_NTF, self.OnSetApplyforData)
    event.Unregister(event_Congress_define.TMSG_CONGRESS_QUEUESETTING_NTF, self.OnSetQueueData)
    event.Unregister(event_Congress_define.TMSG_CONGRESS_APPLYFOR_SELF_NTF, self.OnSetSelfApplyforData)
    event.Unregister(event_Congress_define.TMSG_CONGRESS_QUEUEUP_NTF, self.OnSetQueueListData)
    event.Unregister(event_Congress_define.TMSG_CONGRESS_AWARD_INFO_NTF, self.OnSetAwardInfoData)
    gw_ed.mgr:Unregister(gw_ed.GW_SAND_NET_EVENT, self.SetSandboxSid)
    red_system.UnRegisterRedFunc(red_const.Enum.Congress, self.OnGetRedDot)
end

return M


