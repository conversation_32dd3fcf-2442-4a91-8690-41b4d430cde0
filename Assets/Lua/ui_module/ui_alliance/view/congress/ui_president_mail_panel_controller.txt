local require = require
local pairs = pairs     
local string = string
local table = table
local newClass = newclass
local type = type

local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"
local windowMgr = require "ui_window_mgr"
local congress_data = require "congress_data"
local flow_text = require "flow_text"
local game_scheme = require "game_scheme"
local os = os

--region Controller Life
module("ui_president_mail_panel_controller")
local controller = nil
local UIController = newClass("ui_president_mail_panel_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self, view_name, controller_name)
    self.CData = {}
    self.title = ""
    self.desc = ""
    local maxTitle = game_scheme:GWMapConstant_0(116)
    if maxTitle then
        self.titleMax = maxTitle.szParam.data[0]
    end
    local maxDesc = game_scheme:GWMapConstant_0(117)
    if maxDesc then
        self.descMax = maxDesc.szParam.data[0]
    end
    self:TriggerUIEvent("SetUIPanel")
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close(data)   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents() 
end

function UIController:AutoUnsubscribeEvents() 
end
--endregion

--region Controller Logic

function UIController:OnBtnCloseBtnClickedProxy()
    windowMgr:UnloadModule("ui_president_mail_panel")
end

function UIController:OnInputInputDescValueChange(text)
    --self:TriggerUIEvent( "SetDescTextNum", #text,maxLimit)
end

function  UIController:OnInputInputDescEndEdit(text)
    self.desc = text
end

function UIController:OnInputInputNameValueChange(text)
    --self:TriggerUIEvent( "SetTitleTextNum", #text,maxLimit)
end

function  UIController:OnInputInputNameEndEdit(text)
    self.title = text
end

function  UIController:OnBtnNormalBtnClickedProxy()
    if string.IsNullOrEmpty(self.title) then
        flow_text.Add(lang.Get(671064))
    elseif string.IsNullOrEmpty(self.desc) then
        flow_text.Add(lang.Get(671065))
    else
        local titleLen = #self.title
        local descLen = #self.desc
        if titleLen > self.titleMax then
            titleLen = titleLen - self.titleMax
            flow_text.Add(string.format2(lang.Get(671066),titleLen))
        elseif descLen > self.descMax then
            titleLen = titleLen - self.descMax
            flow_text.Add(string.format2(lang.Get(671066),titleLen))
        else
            congress_data.OnSentPresidentMail(self.title,self.desc)
            windowMgr:UnloadModule("ui_president_mail_panel")
        end
    end
end

function  UIController:OnBtnGrayBtnClickedProxy()
    local selfPositionID = congress_data.OnGetSelfPositionID()
    local GWConst = require "gw_const"
    local player_mgr = require "player_mgr"
    if selfPositionID ~= GWConst.enCongress_PositionType.enCongress_PositionType_Pres then
        flow_text.Add(lang.Get(671091))
        return
    end
    local mailCd = congress_data.OnGetPresidentialMailData()
    local timeLeft = mailCd - os.server_time();
    if timeLeft > 0 then
        flow_text.Add(lang.Get(134284))
        return
    end
    local costCfg = game_scheme:GWMapConstant_0(112)
    if costCfg then
        local diamondCount = player_mgr.GetPlayerOwnNum(costCfg.szParam.data[0])
        local diamondCost = costCfg.szParam.data[1]
        if diamondCount < diamondCost then
            flow_text.Add(lang.Get(75))
        end
    end
    
end

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
