--region FileHead
--- ui_gift_odds.txt
-- author:  马睿
-- ver:     1.0
-- desc:    礼物概率公示
-------------------------------------------------
--endregion 

--region Require
local require = require
local type = type
local pairs = pairs
local typeof = typeof
local string = string

local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem
local enum_define = require "enum_define"
local class = require "class"
local ui_base = require "ui_base"
local module_scroll_list = require "scroll_list"

local multply_item = require "multply_item"
local goods_item = require "goods_item_com"
local iui_item_detail = require "iui_item_detail"
local item_data = require "item_data"
local lang = require "lang"
--endregion 

--region ModuleDeclare
module("ui_gift_odds")
local ui_path = "ui/prefabs/gw/alliancesystem/gift/uigiftodds.prefab"
local window = nil
local UIView = {}
--endregion 

local IconItemGameObjetct = nil
--region WidgetTable
UIView.widget_table = {

    Btn_closeBtn = { path = "Auto_closeBtn", type = "Button", event_name = "OnBtn_closeBtnClickedProxy" },
    Btn_panelBg_closeBtn = { path = "panelBg/Auto_closeBtn", type = "Button", event_name = "OnBtn_panelBg_closeBtnClickedProxy" },
    Text_giftLevelText = { path = "Auto_giftLevelText", type = "Text" },
    rect_table = { path = "InfoArea/rewardList/Viewport/Content", type = ScrollRectTable },

}


--function 设置View-Controller模式的UI
-- return type  ---- 未定义/VC/纯V   
-- 注意，View-Controller模式的ui必须要重写这个接口
function UIView:SetVCTypeUI()
    return self.__base:SetVCTypeUI(enum_define.enum_ui_vc_type.vc)
end
--endregion 

--region WindowInit
--[[窗口初始化]]
function UIView:Init()
    self:SetVCTypeUI()
    self.__base:Init(self)
    self:SubscribeEvents()
    --region User

    self:InitScrollRectTable()
    --endregion 
end --///<<< function

--endregion 

--region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function UIView:OnShow()
    self.__base:OnShow()
end --///<<< function

--endregion 

--region WindowOnHide
--[[界面隐藏时调用]]
function UIView:OnHide()
    self.__base:OnHide()
end --///<<< function

--endregion 

--region WindowClose
function UIView:Close()
    self.__base:Close()
    window = nil
    --region User
    if self.rect_table then
        self.rect_table:ItemsDispose()
    end
    --endregion 
end --///<<< function
--endregion 

function UIView:SubscribeEvents()
    -- 注意 事件建议使用self:RegisterEvent(event_name,func)来注册，不需要手动维护注销了    
end

--region 功能函数区
---********************功能函数区**********---

function UIView:InitScrollRectTable()
    self.rect_table.onItemRender = OnItemRender

    self.rect_table.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data then
            if scroll_rect_item.data.itemIcon ~= nil then
                for i, v in pairs(scroll_rect_item.data.itemIcon) do
                    v:Dispose()
                end
            end
        end

    end
end

--region 原本的代码
function OnItemRender(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or { index, dataItem }
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem

    local UR = scroll_rect_item:Get("UR") --品级
    local oddsText = scroll_rect_item:Get("oddsText") --概率
    local iconParent = scroll_rect_item:Get("iconParent") --商品父物体
    local IconItem = scroll_rect_item:Get("IconItem")

    --UR.text = dataItem.grade  --langId
    UR.text = lang.Get(dataItem.langId)
    --oddsText.text = dataItem.rateInfo
    oddsText.text = string.format("%s%%", dataItem.rate)

    local ItemsData = dataItem.items

    multply_item.Exe(#ItemsData, function(i, item)

        local itemData = ItemsData[i]
        local scr = item:GetComponent(typeof(ScrollRectItem))
        local itemOddsText = scr:Get("itemOddsText")
        local Auto_IconItem = scr:Get("Auto_IconItem")

        if not scroll_rect_item.data["itemIcon"] then
            scroll_rect_item.data["itemIcon"] = {}
        end

        local itemIcon = nil

        if scroll_rect_item.data["itemIcon"][i] == nil then
            scroll_rect_item.data["itemIcon"][i] = goods_item.CGoodsItem():Init(Auto_IconItem, nil, 0.5, goods_item.ComCfgType.PackageType)

        end
        itemIcon = scroll_rect_item.data["itemIcon"][i]

        --local itemIcon = scroll_rect_item.data["icon"][i] or goods_item.CGoodsItem():Init(Auto_IconItem, nil, 0.6, goods_item.ComCfgType.PackageType)

        itemIcon:SetGoods(nil, itemData.id, itemData.num, function()
            iui_item_detail.Show(itemData.id, nil, item_data.Item_Show_Type_Enum.Reward_Interface, itemData.num, nil, nil)
        end)
        --itemOddsText.text = itemData.itemRateInfo
        itemOddsText.text = string.format("%s%%",itemData.rate)
        
        --scroll_rect_item.data["icon"][i]=itemIcon
    end, iconParent, IconItem)
end
--endregion

--设置礼物等级
function UIView:RefreshGiftLevel(level)
    local levelText = lang.Get(600370)
    self.Text_giftLevelText.text = string.format(levelText, level)
end

--刷新这个列表数据
function UIView:RefreshGiftOddsData(data)
    if not data then
        return
    end
    self.rect_table.pageSize = #data
    self.rect_table.renderPerFrames = #data
    self.rect_table.data = data
    self.rect_table:Refresh(-1, -1)
end

---********************end功能函数区**********---
--endregion

--region WindowInherited
local CUIView = class(ui_base, nil, UIView)
--endregion 

--region static ModuleFunction 
-- 特别注意，当前并不是由controller层来驱动ui的生命流程的 
-- 当前因为需要view层 也就是ui_base来驱动ui的init  加载完成，show等流程，所以流程仍然保留，而controller层的流程逻辑受view流程影响，
-- view对应的Init/Show 加载完后，当前会通过事件同步调用controller层的Init/Show流程，controller层的流程逻辑才会执行。
--当前仍然保留了静态的Show，Close接口流程
function Show(data)
    if data and data["uipath"] then
        ui_path = data["uipath"];
    end
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
        window:LoadUIResource(ui_path, nil, nil, nil)
    end
    --这里调用window:Show()  会造成多次调用 但hide后却又需要 uiwindowmgr有问题 TODO
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close()
        window = nil
    end
end
--endregion
