--region FileHead
--- ui_alliance_city_new.txt
-- author:  农勇智
-- ver:     1.0
-- desc:    
-------------------------------------------------
--endregion 

--region Require
local require = require
local type = type
local pairs = pairs
local ipairs = ipairs
local typeof = typeof
local string = string
local table = table

local GameObject = CS.UnityEngine.GameObject
local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable
local enum_define = require "enum_define"
local class = require "class"
local lang = require "lang"
local ui_base = require "ui_base"
local module_scroll_list = require "scroll_list"
local Common_Util = CS.Common_Util.UIUtil
local game_scheme = require "game_scheme"

local windowMgr = require "ui_window_mgr"
local city_siege_activity_data = require "city_siege_activity_data"
local ui_module_item = require "ui_module_item"
local LayoutRebuilder = CS.UnityEngine.UI.LayoutRebuilder
local gw_common_util = require "gw_common_util"
local gw_sand_mgr = require "gw_sand_mgr"
local net_sand_module = require "net_sandbox_module"

--endregion 

--region ModuleDeclare
module("ui_alliance_city_new")
local ui_path = "ui/prefabs/gw/alliancesystem/city/uialliancecitynew.prefab"
local window = nil
local UIView = {}
--endregion 

--region WidgetTable
UIView.widget_table = {
    Btn_cityBuffBtn = { path = "OccupyPanel/AllianceObj/Auto_cityBuffBtn", type = "Button", event_name = "OnBtn_cityBuffBtnClickedProxy" },
    Btn_returnBtn = { path = "Auto_returnBtn", type = "Button", event_name = "OnBtn_returnBtnClickedProxy" },
    Text_haveCityNumText = { path = "OccupyPanel/AllianceObj/Auto_haveCityNumText", type = "Text" },
    
    occupyToggle = {path = "BgArea/ToggleGroup/OccupyToggle",type = "Toggle",value_changed_event = "OnShowOccupyPage"},
    occupyableToggle = {path = "BgArea/ToggleGroup/OccupyableToggle",type = "Toggle",value_changed_event = "OnShowOccupyablePage"},

    OccupyPage = { path = "OccupyPanel", type = GameObject },
    OccupyAllianceObj = {path = "OccupyPanel/AllianceObj",type = GameObject},
    OccupyEmptyTips = {path = "OccupyPanel/obj_ComEmpty",type = GameObject},
    OccupyNoAllianceTips = {path = "OccupyPanel/NoAllianceTips",type = GameObject},
    OccupyJoinAllianceBtn = {path = "OccupyPanel/NoAllianceTips/JoinAlliance",type = "Button", event_name = "OnJoinAlliance"},
    
    OccupyablePage = {path = "CanOccupyPanel", type = GameObject},
    OccupyableAllianceObj = {path = "CanOccupyPanel/AllianceObj",type = GameObject},
    OccupyableEmptyTips = {path = "CanOccupyPanel/obj_ComEmpty",type = GameObject},
    OccupyableNoAllianceTips = {path = "CanOccupyPanel/NoAllianceTips",type = GameObject},
    OccupyableJoinAllianceBtn = {path = "CanOccupyPanel/NoAllianceTips/JoinAlliance",type = "Button", event_name = "OnJoinAlliance"},
    
    CanOccupyCityNumText = {path = "CanOccupyPanel/AllianceObj/haveCityNum",type = "Text"},
    CityScrollTable = {path = "ScrollView/Scroll View/Viewport/content", type = ScrollRectTable,},
    
    CityScrollViewObj = {path = "ScrollView",type = GameObject},
    CityBuffPanel = {path = "CityBuffPanel",type = "Button", event_name = "OnHideCityBuff"},
    CityBuffScrollTable = {path = "CityBuffPanel/bg/buffList/Viewport/content", type = ScrollRectTable,},
    CityBuffList = {path = "CityBuffPanel/bg/buffList",type = GameObject},
    CityNoBuffTips = {path = "CityBuffPanel/bg/NoBuff",type = GameObject},
    

}
--endregion 

--region function 设置View-Controller模式的UI
-- return type  ---- 未定义/VC/纯V   
-- 注意，View-Controller模式的ui必须要重写这个接口
function UIView:SetVCTypeUI()
    return self.__base:SetVCTypeUI(enum_define.enum_ui_vc_type.vc)
end
--endregion 

--region WindowInit
--[[窗口初始化]]
function UIView:Init()
    self:SetVCTypeUI()
    self.__base:Init(self)
    self.showResultTimer = nil;
    self:SubscribeEvents()
    self:InitScrollRectTable()
    --region User
    --endregion 
end --///<<< function

--endregion 


--region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function UIView:OnShow()
    self.__base:OnShow()
end --///<<< function

--endregion 

--region WindowOnHide
--[[界面隐藏时调用]]
function UIView:OnHide()
    self.__base:OnHide()    
end --///<<< function

--endregion 


--region WindowClose
function UIView:Close()
    self.__base:Close()
    self:UnsubscribeEvents() 
    window = nil   
    --region User
    --endregion 
end --///<<< function
--endregion 
--region 事件注册
function UIView:SubscribeEvents()    
    -- 注意 事件建议使用self:RegisterEvent(event_name,func)来注册，不需要手动维护注销了    
end
function UIView:UnsubscribeEvents()    
    
end

--endregion

--region 功能函数区
---********************功能函数区**********---
function UIView:InitScrollRectTable()
    self.CityScrollTable.onItemRender = OnItemRender;
    self.CityScrollTable.onItemDispose = function(scroll_rect_item,index)
        if scroll_rect_item then
            local selfBtn = scroll_rect_item:Get("selfBtn");
            selfBtn.onClick:RemoveAllListeners();
            if scroll_rect_item.data and scroll_rect_item.data[3] then
                scroll_rect_item.data[3]:Dispose()
            end
        end
    end
    self.CityBuffScrollTable.onItemRender = OnItemRender2;
    self.CityBuffScrollTable.onItemDispose = function(scroll_rect_item,index)
        if scroll_rect_item then

        end
    end
end

function OnItemRender(scroll_rect_item,index,dataItem)
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index;
    scroll_rect_item.data[2] = dataItem;
    Common_Util.SetActive(scroll_rect_item.gameObject,true)
    local nameText = scroll_rect_item:Get("nameText");
    nameText.text = "Lv."..dataItem.data.cityLevel.." "..lang.Get(dataItem.data.name);
    local selfBtn = scroll_rect_item:Get("selfBtn");
    selfBtn.onClick:RemoveAllListeners();
    if dataItem.isMyCity then
        selfBtn.onClick:AddListener(
                function()
                    local temp =
                    {
                        isJoin = dataItem.isJoin,
                        canGet = dataItem.bCanGet,
                        data = dataItem.data,
                        nSID = dataItem.nSID,
                    }
                    windowMgr:ShowModule("ui_city_siege_reward_panel", nil, nil, temp)
                    window:OnItemClick(dataItem.nSID,dataItem.data.cityLevel)
                end
        )
    else
        selfBtn.onClick:AddListener(
            function()
                windowMgr:UnloadModule("ui_alliance_city_new")
                windowMgr:UnloadModule("ui_alliance_main")
                windowMgr:UnloadModule("ui_festival_activity_center")
                gw_common_util.JumpToGrid({ x = dataItem.tPos.x, y = dataItem.tPos.y })
            end
        )
    end
    local cityIcon = scroll_rect_item:Get("cityIcon")
    local resCfg = game_scheme:SandMapModelResource_0(dataItem.data.ModelResource)
    if resCfg then
        local path = resCfg.ModelRes
        if not scroll_rect_item.data[3] then
            scroll_rect_item.data[3] = ui_module_item:new()
            scroll_rect_item.data[3]:Init(cityIcon,nil)
        end
        scroll_rect_item.data[3]:SetChangeModel(path)
        scroll_rect_item.data[3]:SetCameraDistance(7)
    end
    
    local posText = scroll_rect_item:Get("posText")
    local EffectArea = scroll_rect_item:Get("EffectArea")
    local effectObj = scroll_rect_item:Get("effectObj")
    if not scroll_rect_item.data[4] then
        scroll_rect_item.data[4] = {}
    end
    for i,v in pairs(scroll_rect_item.data[4]) do
        Common_Util.SetActive(v,false)
    end
    --local centerPos = gw_sand_mgr.GetCurGridPos()
    posText.text = string.format2("[{%s1},{%s2}] {%s3}",dataItem.tPos.x,dataItem.tPos.y,gw_common_util.GetKilometerStrByGrid(dataItem.tPos,gw_common_util.GetSandBasePosition()))
    if dataItem.data.cityBuff.count > 0 then
        for i=0,dataItem.data.cityBuff.count - 1 do
            local cityBuff = game_scheme:GWMapEffect_0(dataItem.data.cityBuff.data[i])
            if cityBuff then
                local cityBuffLang = game_scheme:ProToLang_0(cityBuff.nGroupID)
                if cityBuffLang then
                    if not scroll_rect_item.data[4][i] then
                        scroll_rect_item.data[4][i] = GameObject.Instantiate(effectObj,EffectArea)
                    end
                    Common_Util.SetActive(scroll_rect_item.data[4][i],true)
                    local title = Common_Util.GetComponent(scroll_rect_item.data[4][i].transform,typeof(Text),"")
                    local value = Common_Util.GetComponent(scroll_rect_item.data[4][i].transform,typeof(Text),"value")
                    title.text = lang.Get(cityBuffLang.iLangId)
                    local valueTxt = cityBuff.strParam[0]
                    if cityBuffLang.isPercentage then
                        local strings = {cityBuff.strParam[0] / 10000 * 100,"%"}
                        valueTxt = table.concat(strings);
                    end
                    value.text = valueTxt;
                end
            end
        end
    end
    LayoutRebuilder.ForceRebuildLayoutImmediate(EffectArea)
    
    local nothingState = scroll_rect_item:Get("nothingState")
    local occupiedState = scroll_rect_item:Get("occupiedState")
    local declareWarState = scroll_rect_item:Get("declareWarState")

    Common_Util.SetActive(nothingState,dataItem.nAllianceID == 0)
    Common_Util.SetActive(occupiedState,dataItem.nAllianceID ~= 0 and not dataItem.isAttacked)
    Common_Util.SetActive(declareWarState,false)--dataItem.isAggressor and dataItem.isAttacked)
    local allianceBg = scroll_rect_item:Get("allianceBg")
    local allianceName = scroll_rect_item:Get("allianceName")
    Common_Util.SetActive(allianceBg,dataItem.nAllianceID ~= 0)
    if dataItem.nAllianceID ~= 0 then
        allianceName.text = string.format("[%s]%s", dataItem.sAllianceShortName, dataItem.sAllianceName)
    end

    local NewIcon = scroll_rect_item:Get("NewIcon")
    local receive = scroll_rect_item:Get("receiveBtn")
    Common_Util.SetActive(receive,dataItem.bCanGet)
    Common_Util.SetActive(NewIcon,dataItem.isNew)
    
    local jumpBtn = scroll_rect_item:Get("JumpBtn")
    jumpBtn.onClick:RemoveAllListeners();
    jumpBtn.onClick:AddListener(
        function()
            gw_common_util.JumpToGrid({ x = dataItem.tPos.x, y = dataItem.tPos.y })
            windowMgr:UnloadModule("ui_alliance_city_new")
            windowMgr:UnloadModule("ui_alliance_main")
            windowMgr:UnloadModule("ui_festival_activity_center")
        end
    )
    receive.onClick:RemoveAllListeners();
    if dataItem.bCanGet then
        receive.onClick:AddListener(
                function()
                    local net_sandbox_module = require "net_sandbox_module"
                    city_siege_activity_data.EventReport("SandMapCompetition_GetReward",{})
                    net_sandbox_module.MSG_SANDBOX_NC_GETREWARD_REQ(dataItem.nSID)
                end
        )
    end
end

function OnItemRender2(scroll_rect_item,index,dataItem)
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index;
    scroll_rect_item.data[2] = dataItem;
    Common_Util.SetActive(scroll_rect_item.gameObject,true)
    local buffName = scroll_rect_item:Get("buffName");
    local buffEffect = scroll_rect_item:Get("buffEffect");
    buffName.text = dataItem.title;
    buffEffect.text = dataItem.value;
end

function UIView:ShowPanel()
    local alliance_data = require("alliance_data")
    local userAllianceData = alliance_data.GetUserAllianceData()
    if not userAllianceData or userAllianceData.allianceId == nil then
        Common_Util.SetActive(self.OccupyableAllianceObj,false)
        Common_Util.SetActive(self.OccupyableNoAllianceTips,true)
        Common_Util.SetActive(self.OccupyableEmptyTips,false)
        Common_Util.SetActive(self.OccupyAllianceObj,false)
        Common_Util.SetActive(self.OccupyNoAllianceTips,true)
        Common_Util.SetActive(self.OccupyEmptyTips,false)
        Common_Util.SetActive(self.CityScrollViewObj,false)
    else
        Common_Util.SetActive(self.OccupyableAllianceObj,true)
        Common_Util.SetActive(self.OccupyableNoAllianceTips,false)
        Common_Util.SetActive(self.OccupyAllianceObj,true)
        Common_Util.SetActive(self.OccupyNoAllianceTips,false)
        Common_Util.SetActive(self.CityScrollViewObj,true)
        local cityList = city_siege_activity_data.GetSandCityData();
        self.myCity = {}
        self.otherCity = {}
        for i,v in pairs(cityList) do
            for j,k in pairs(v) do
                if k.isMyCity then
                    table.insert(self.myCity,k)
                else
                    table.insert(self.otherCity,k)
                end
            end
        end
        local myCityCount = #self.myCity
        local otherCityCount = #self.otherCity
        local cityCountCfg = game_scheme:InitBattleProp_0(8018)
        local totalCityCount = cityCountCfg and cityCountCfg.szParam.data[0] or 0;
        Common_Util.SetActive(self.OccupyableEmptyTips,otherCityCount <= 0)
        Common_Util.SetActive(self.OccupyEmptyTips,myCityCount <= 0)
        table.sort(self.otherCity,function(a, b)
            if a.nAllianceID == 0 and b.nAllianceID ~= 0 then
                return true   -- a 来自联盟 -1，排在前面
            elseif a.nAllianceID ~= 0 and b.nAllianceID == 0 then
                return false  -- b 来自联盟 -1，a 排在后面
            else
                -- nAllianceID 都不是 -1，按 cityID 排序
                return a.cityID < b.cityID
            end
        end)
        local buffDataList = {}
        if myCityCount > 0 then
            self.CityScrollTable.data = self.myCity;
            local buffList = {}
            for i,v in ipairs(self.myCity) do
                for j = 0, v.data.cityBuff.count - 1 do
                    local buffCfg = game_scheme:GWMapEffect_0(v.data.cityBuff.data[j])
                    if buffCfg then
                        if not buffList[buffCfg.nGroupID] then
                            buffList[buffCfg.nGroupID] = 0
                        end
                        buffList[buffCfg.nGroupID] = buffList[buffCfg.nGroupID] + buffCfg.strParam[0]
                    end
                end
            end
            for i,v in pairs(buffList) do
                local cfgData = game_scheme:ProToLang_0(i)
                if cfgData then
                    local valueTxt = v / 100
                    if cfgData.isPercentage then
                        local strings = {v / 100,"%"}
                        valueTxt = table.concat(strings);
                    end
                    local tempData =
                    {
                        title = lang.Get(cfgData.iLangId),
                        value = valueTxt
                    }
                    table.insert(buffDataList,tempData)
                end
            end
        end
        local buffLen = #buffDataList
        Common_Util.SetActive(self.CityBuffList,buffLen > 0)
        Common_Util.SetActive(self.CityNoBuffTips,buffLen <= 0)
        if buffLen > 0 then
            self.CityBuffScrollTable.data = buffDataList;
            self.CityBuffScrollTable:Refresh(0,-1)
        end
        self.Text_haveCityNumText.text = lang.Get(600250)..myCityCount .. "/" .. totalCityCount
        self.CanOccupyCityNumText.text = "1."..self.Text_haveCityNumText.text
        
    end
    self:ShowOccupyPage()
end

function UIView:OnUpdateCityData()
    local cityList = city_siege_activity_data.GetSandCityData();
    self.myCity = {}
    self.otherCity = {}
    for i,v in pairs(cityList) do
        for j,k in pairs(v) do
            if k.isMyCity then
                table.insert(self.myCity,k)
            else
                table.insert(self.otherCity,k)
            end
        end
    end
    local myCityCount = #self.myCity
    local otherCityCount = #self.otherCity
    local cityCountCfg = game_scheme:InitBattleProp_0(8018)
    local totalCityCount = cityCountCfg and cityCountCfg.szParam.data[0] or 0;
    Common_Util.SetActive(self.OccupyableEmptyTips,otherCityCount <= 0)
    Common_Util.SetActive(self.OccupyEmptyTips,myCityCount <= 0)
    table.sort(self.otherCity,function(a, b)
        if a.nAllianceID == 0 and b.nAllianceID ~= 0 then
            return true   -- a 来自联盟 -1，排在前面
        elseif a.nAllianceID ~= 0 and b.nAllianceID == 0 then
            return false  -- b 来自联盟 -1，a 排在后面
        else
            -- nAllianceID 都不是 -1，按 cityID 排序
            return a.cityID < b.cityID
        end
    end)
    local buffDataList = {}
    if myCityCount > 0 then
        local buffList = {}
        for i,v in ipairs(self.myCity) do
            for j = 0, v.data.cityBuff.count - 1 do
                local buffCfg = game_scheme:GWMapEffect_0(v.data.cityBuff.data[j])
                if buffCfg then
                    if not buffList[buffCfg.nGroupID] then
                        buffList[buffCfg.nGroupID] = 0
                    end
                    buffList[buffCfg.nGroupID] = buffList[buffCfg.nGroupID] + buffCfg.strParam[0]
                end
            end
        end
        for i,v in pairs(buffList) do
            local cfgData = game_scheme:ProToLang_0(i)
            if cfgData then
                local valueTxt = v / 100
                if cfgData.isPercentage then
                    local strings = {v / 100,"%"}
                    valueTxt = table.concat(strings);
                end
                local tempData =
                {
                    title = lang.Get(cfgData.iLangId),
                    value = valueTxt
                }
                table.insert(buffDataList,tempData)
            end
        end
    end
    local buffLen = #buffDataList
    Common_Util.SetActive(self.CityBuffList,buffLen > 0)
    Common_Util.SetActive(self.CityNoBuffTips,buffLen <= 0)
    if buffLen > 0 then
        self.CityBuffScrollTable.data = buffDataList;
        self.CityBuffScrollTable:Refresh(0,-1)
    end
    self.Text_haveCityNumText.text = lang.Get(600250)..myCityCount .. "/" .. totalCityCount
    self.CanOccupyCityNumText.text = "1."..self.Text_haveCityNumText.text
    self.CityScrollTable:Refresh(0,-1)
end

function UIView:OnItemClick(sid,lv)
    for i,v in ipairs(self.myCity) do
        if v.nSID == sid then
            v.isNew = false
            break;
        end
    end
    city_siege_activity_data.SetItemNewState(sid,lv)
    self.CityScrollTable:Refresh(0,-1)
end

function UIView:OnUpdateCityList(msg)
    if msg.nSID then
        for i,v in ipairs(self.myCity) do
            if v.nSID == msg.nSID then
                v.bCanGet = false
                break;
            end
        end
    end
    self.CityScrollTable:Refresh(0,-1)
end

function UIView:ShowOccupyPage()
    Common_Util.SetActive(self.OccupyPage,true)
    Common_Util.SetActive(self.OccupyablePage,false)
    self.CityScrollTable.data = self.myCity;
    self.CityScrollTable:Refresh(0,-1)
    --查看已占领城市列表 上报
    city_siege_activity_data.EventReport("SandMapCompetition_OccupiedCheck", {})
end

function UIView:ShowOccupyablePage()
    Common_Util.SetActive(self.OccupyPage,false)
    Common_Util.SetActive(self.OccupyablePage,true)
    self.CityScrollTable.data = self.otherCity;
    self.CityScrollTable:Refresh(0,-1)
    --查看可占领城市列表 上报
    city_siege_activity_data.EventReport("SandMapCompetition_OccupyCheck", {})
end

function UIView:ShowCityBuff()
    Common_Util.SetActive(self.CityBuffPanel,true)
    --查看城市增益 上报
    city_siege_activity_data.EventReport("SandMapCompetition_Gain", {})
end

function UIView:HideCityBuff()
    Common_Util.SetActive(self.CityBuffPanel,false)
end

---********************end功能函数区**********---
--endregion
--region WindowInherited
local CUIView = class(ui_base, nil, UIView)
--endregion 

--region static ModuleFunction 
-- 特别注意，当前并不是由controller层来驱动ui的生命流程的 
-- 当前因为需要view层 也就是ui_base来驱动ui的init  加载完成，show等流程，所以流程仍然保留，而controller层的流程逻辑受view流程影响，
-- view对应的Init/Show 加载完后，当前会通过事件同步调用controller层的Init/Show流程，controller层的流程逻辑才会执行。
--当前仍然保留了静态的Show，Close接口流程
function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
        if data and type(data) == "table" then
            local uiPath = data.uiPath or ui_path
            local uiParent = data.uiParent or nil
            window:LoadUIResource(uiPath, nil, uiParent, nil)
        else
            window:LoadUIResource(ui_path, nil, nil, nil)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close()
        window = nil
    end
end
--endregion
