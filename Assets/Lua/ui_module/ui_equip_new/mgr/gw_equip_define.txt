
module("gw_equip_define")

HERO_EQUIP_WEAR = "HERO_EQUIP_WEAR"

--装备更新
HERO_EQUIP_UPDATE = "HERO_EQUIP_UPDATE"


--装备升级
HERO_EQUIP_LEVEL = "HERO_EQUIP_LEVEL"

HERO_EQUIP_COMPOUND = "HERO_EQUIP_COMPOUND"
HERO_EQUIP_DECOMPOSE = "HERO_EQUIP_DECOMPOSE"

--装备制造
HERO_EQUIP_COMPOUND_BUILD = "HERO_EQUIP_COMPOUND_BUILD"
--材料合成
HERO_EQUIP_COMPOUND_MATERIAL = "HERO_EQUIP_COMPOUND_MATERIAL"

--装备拆解
HERO_EQUIP_DECOMPOSE_BUILD = "HERO_EQUIP_DECOMPOSE_BUILD"
--材料分解
HERO_EQUIP_DECOMPOSE_MATERIAL = "HERO_EQUIP_DECOMPOSE_MATERIAL"
--切页
HERO_EQUIP_COMPOUND_PAGE = "HERO_EQUIP_COMPOUND_PAGE"
--装备制造时间变化，加速等
HERO_EQUIP_BUILD_CHANGE = "HERO_EQUIP_BUILD_CHANGE"
--领取装备
HERO_EQUIP_GET = "HERO_EQUIP_GET"

--装备强化界面，点击切换装备
HERO_LVEL_CHANGE = "HERO_LVEL_CHANGE"