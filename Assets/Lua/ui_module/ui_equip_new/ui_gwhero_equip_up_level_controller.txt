
local require = require
local pairs = pairs     
local newClass = newclass
local type = type

local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"
local gw_equip_define = require "gw_equip_define"
local gw_equip_mgr = require "gw_equip_mgr"
local gw_equip_data = require "gw_equip_data"
--region Controller Life
module("ui_gwhero_equip_up_level_controller")
local controller = nil
local UIController = newClass("ui_gwhero_equip_up_level_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self, view_name, controller_name)
    self.CData = {}
    self.goodsEntity = data.goodsEntity
    self.heroSid = data.heroSid
    self.equipPos = data.equipPos
    self.enterType = data.enterType
    self:SetEquipView()
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close()   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.CData = nil
    controller = nil
    self.__base.Close(self)
end

function UIController:AutoSubscribeEvents() 
    self.equipUpdateLevel = function()
        if self.goodsEntity and self.goodsEntity.equipEntity then
            if self.goodsEntity.equipEntity:IsMax() then
                self:OnBtnCloseBtnClickedProxy()
                return
            end
        end
        self:SetEqupLevel()
    end
    self:RegisterEvent(gw_equip_define.HERO_EQUIP_LEVEL, self.equipUpdateLevel)
    self.equipWear = function()
        --local skepID = self.goodsEntity.goodsEntity.numProp.skepID
        --if skep_mgr.GetSkepType(skepID) ~= prop_pb.SKEP_TYPE_PALEQUIP then
            --local heroSid = player_mgr.GetHeroOfEquipSkep(skepID)
            --if not heroSid then
                self:OnBtnCloseBtnClickedProxy()
            --end
        --end
    end
    self:RegisterEvent(gw_equip_define.HERO_EQUIP_WEAR, self.equipWear)
end

function UIController:AutoUnsubscribeEvents() 
end
--endregion

--region Controller Logic
function  UIController:OnBtnCloseBtnClickedProxy()
	ui_window_mgr:UnloadModule(self.view_name)
end
function  UIController:OnBtnOffClickedProxy()
    if self.heroSid and self.goodsEntity then
        local data = {
            bTakeOff = true,
            newEquipmentSid = 0,
            oldEquipmentSid = self.goodsEntity.equipEntity:GetGoodsSid(),
            pos = self.equipPos
        }
        gw_equip_mgr.C2SChangeEquipment(self.heroSid,{data})
    end
end
function  UIController:OnBtnUpLevelClickedProxy()
    local canReturn = false
    local showBack = function()
        local util = require "util"
        util.DelayCallOnce(0, function()
            self:OnBtnCloseBtnClickedProxy()
        end)
    end
    if self.enterType == "iui_item_detail" then
        --背包打开的
        -- if not self.heroSid then
        --     --显示装备,英雄列表
        --     local gw_hero_mgr = require "gw_hero_mgr"
        --     gw_hero_mgr.OpenHeroBagWindow(showBack)
        --     canReturn = true
        -- else
        --     if not self.goodsEntity.equipEntity:CanUpgrade() then
        --         --绿色不能升级，显示装备
        --         local gw_hero_mgr = require "gw_hero_mgr"
        --         gw_hero_mgr.OpenHeroDetailUIBySid(self.heroSid,showBack)
        --         canReturn = true
        --     else
        --         local isMax = self.goodsEntity.equipEntity:IsMax()
        --         if isMax then
        --             --已装备
        --             local gw_hero_mgr = require "gw_hero_mgr"
        --             gw_hero_mgr.OpenHeroDetailUIBySid(self.heroSid,showBack)
        --             canReturn = true
        --         else
        --             --强化
        --             --打开强化界面
        --             --self:OnBtnCloseBtnClickedProxy()
        --         end
        --     end
        -- end


        if false and not self.goodsEntity.equipEntity:CanUpgrade() then
            -- --绿色不能升级，显示装备
            -- if self.heroSid then
            --     local gw_hero_mgr = require "gw_hero_mgr"
            --     gw_hero_mgr.OpenHeroDetailUIBySid(self.heroSid,showBack)
            --     canReturn = true
            -- else
            --     --英雄背包
            --     local gw_hero_mgr = require "gw_hero_mgr"
            --     gw_hero_mgr.OpenHeroBagWindow(showBack)
            --     canReturn = true
            -- end
        else
            local isMax = self.goodsEntity.equipEntity:IsMax()
            if isMax then
                --已装备
                if self.heroSid then
                    --单个英雄界面
                    local gw_hero_mgr = require "gw_hero_mgr"
                    gw_hero_mgr.OpenHeroDetailUIBySid(self.heroSid,showBack)
                    canReturn = true
                else
                    --英雄背包
                    local gw_hero_mgr = require "gw_hero_mgr"
                    gw_hero_mgr.OpenHeroBagWindow(showBack)
                    canReturn = true
                end
            else
                --强化
                --打开强化界面
            end
        end
    end
    if canReturn then
        return
    end
    if not self.goodsEntity.equipEntity:CanUpgrade() then
        local flow_text = require "flow_text"
        flow_text.Add(lang.Get(606050))
        return
    end

    local isLevelEx = false --晋升，建筑等级未达到
    if self.goodsEntity.equipEntity:CheckShowStar() then
        local function_open_mgr = require "function_open_mgr"
        local isOpen = function_open_mgr.CheckFunctionIsOpen(function_open_mgr.OpenIdEnum.EquipmentPromotion)
        if not isOpen then
            isLevelEx = true
        end
    end
    if isLevelEx then
        --跳转到升级
        local GWConst = require "gw_const"
        ui_window_mgr:CloseAll(GWConst.EHomeEnterCullLua)
        local module_jumping = require "module_jumping"
        module_jumping.Jump("slg_aaf", "27") 
       
        -- local GWAdmin = require "gw_admin"
        -- GWAdmin.HomeCameraUtil.DoCameraToBuildMove(27000, nil, GWConst.CameraMoveTime, true,false,nil,2)
        showBack()
        return
    end
    
    --self:OnBtnCloseBtnClickedProxy()
    local goodsEntityList,equipIndex = gw_equip_mgr.GetHeroEquipList(self.heroSid,self.goodsEntity)
    ui_window_mgr:ShowModule("ui_gwhero_equip_upgrade_base", showBack, nil, {goodsEntityList = goodsEntityList,equipIndex = equipIndex,heroSid = self.heroSid,type = gw_equip_data.UpgradeType.Level})
end

function  UIController:OnBtnReplaceClickedProxy()
    local goodsEntityList = gw_equip_mgr.GetEquipPosList(self.equipPos,self.goodsEntity.goodsSid)
    -- --没有装备 打开穿戴
    ui_window_mgr:ShowModule("ui_gwhero_equip_wear", nil, nil, {goodsEntityList = goodsEntityList,goodsEntity = self.goodsEntity,heroSid = self.heroSid,equipPos = self.equipPos})
end
function  UIController:SetEquipView()
    self:SetEqupBase()
    self:SetEqupLevel()
end
function  UIController:SetEqupBase()
    self:TriggerUIEvent( "SetEqupBase",self.goodsEntity,self.heroSid)
end
function  UIController:SetEqupLevel()
    self:TriggerUIEvent( "SetEqupLevel",self.goodsEntity,self.enterType,self.heroSid)
end
--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
