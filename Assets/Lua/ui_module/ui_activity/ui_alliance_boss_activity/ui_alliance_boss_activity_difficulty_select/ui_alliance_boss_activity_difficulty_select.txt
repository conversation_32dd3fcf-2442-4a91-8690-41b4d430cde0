local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local type = type
local log =  log   
local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local class = require "class"
local time_util = require "time_util"
local UIUtil = UIUtil
local e_handler_mgr = require "e_handler_mgr"
local OptionData = CS.UnityEngine.UI.Dropdown.OptionData
local binding = require "ui_alliance_boss_activity_difficulty_select_binding"

--region View Life
module("ui_alliance_boss_activity_difficulty_select")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base:SetVCTypeUI(enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base:Init(self)

    self.VData = {}
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base:OnHide()
end

function UIView:Close()   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.VData = nil

    self.__base.Close(self)
end
--endregion

--region View Logic

function UIView:InitDropDown(dropdown,options, onValueChangedFunc)
    dropdown.options:Clear()
    for i, v in ipairs(options) do
        local option = OptionData()
        option.text = v
        dropdown.options:Add(option)
    end
    dropdown.onValueChanged:RemoveAllListeners()
    if onValueChangedFunc then
        dropdown.onValueChanged:AddListener(onValueChangedFunc)        
    end
end
---@public 设置日期
function UIView:SetDate(month,day,startTimeStamp)
    if not month or not day then
        log.Error("传入时间错误，请检查month，",month," day=",day)
        return
    end
    self:SetText(self.txt_dates,string.format("%d-%d",month,day))
end

---@public 设置难度下拉条
function UIView:SetDifficultyDropDownData(difficulties,selectDifficulty,unlockLv)
    local func = function(value)
        e_handler_mgr.TriggerHandler(self.controller_name, "OnSetDifficulty",value)
    end
    self:InitDropDown(self.drop_difficult,difficulties,func)
    self.drop_difficult.value = selectDifficulty
end

function UIView:SetUnlockDifficulty(unlockLv)
   --如果没有解锁，则锁住   
    self.drop_difficult:Show()
    local children = UIUtil.GetChildren(self.drop_difficult,"Dropdown List/Viewport/Content")
    --从1开始
    local len = children.Count 
    local lockClickFunc = function()
        local flow_text  = require "flow_text"
        flow_text.Add(lang.Get(653571))
    end
    local locked;
    for i = 1, len-1 do
        locked = i > unlockLv
        local btn = self:GetComponent(children[i],"Button","LockedBg")
        btn.onClick:RemoveAllListeners()
        self:SetActive(btn,locked)
        --隐藏这俩个是因为按钮会默认缩放，改这边快一点了
        self:SetActive(children[i],not locked,"Item Background")
        self:SetActive(children[i],not locked,"Item Checkmark")
        if locked then
            btn.onClick:AddListener(lockClickFunc)
        end
        self:GetComponent(children[i],"Toggle").enabled =  not locked        
    end   
end


---@public 设置小时下拉条
function UIView:SetHoursDropDownData(hours,defaultIndex)
    local hourFunc = function(value)
        e_handler_mgr.TriggerHandler(self.controller_name, "OnSetHours",value)
    end    
    self:InitDropDown(self.drop_hour,hours,hourFunc)
    self.drop_hour.value = defaultIndex
end

---@public 设置分钟下拉条
function UIView:SetMinutesDropDownData(minutes,defaultIndex)   
    local secondFunc = function(value)
        e_handler_mgr.TriggerHandler(self.controller_name, "OnSetMinutes",value)
    end  
    self:InitDropDown(self.drop_second,minutes,secondFunc)
    self.drop_second.value = defaultIndex
end

---@public 设置时区时间
function UIView:SetCurZoneTime(targetTimeStamp)
    --设置当前时区时间   
    self:SetText(self.txt_self_zone_time, time_util.ConvertStamp2Time(targetTimeStamp))
end
--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
		window.isBlurBg = true

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil)
        else
			window:LoadUIResource(ui_path, nil, nil, nil)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close()
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
