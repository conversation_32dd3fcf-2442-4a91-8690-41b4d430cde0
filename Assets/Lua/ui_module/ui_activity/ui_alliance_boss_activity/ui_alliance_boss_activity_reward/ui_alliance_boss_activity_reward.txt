local require = require
local pairs = pairs     
local string = string
local table = table
local type = type
local string_util = require "string_util"
local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local class = require "class"
local binding = require "ui_alliance_boss_activity_reward_binding"
local item_reward_template2 = require "item_reward_template2"
local item_rank_template2 = require "item_rank_template2"
local reward_mgr = require "reward_mgr"
--region View Life
module("ui_alliance_boss_activity_reward")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base:SetVCTypeUI(enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base:Init(self)

    self.VData = {}
    self:InitScrollRectTable()
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base:OnHide()
end

function UIView:Close()   
    if self.VData then
        if self.VData.goodsItems then
            for i, v in pairs(self.VData.goodsItems) do
                v:Dispose()
            end
            self.VData.goodsItems = nil
        end
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    --滑动列表的ItemsDispose
    if  self.srt_content then
        self.srt_content:ItemsDispose()
    end
    --滑动列表的ItemsDispose
    if  self.srt_content1 then
        self.srt_content1:ItemsDispose()
    end
    if  self.srt_content2 then
        self.srt_content2:ItemsDispose()
    end
    self.VData = nil

    self.__base.Close(self)
end
--endregion

--region View Logic
--region 处理SrollRectTable
function UIView:InitScrollRectTable()
    --当前换页的时候采用直接换了一个滑动组件  --测试对比每次重新生成
    self.srt_content.onItemRender = OnItemRender
    self.srt_content.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data  then
            if scroll_rect_item.data["templateItem"] then
                scroll_rect_item.data["templateItem"]:Dispose()
            end
        end
    end
    self.srt_content1.onItemRender = OnItemRender1
    self.srt_content1.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data  then
            if scroll_rect_item.data["templateItem"] then
                scroll_rect_item.data["templateItem"]:Dispose()
            end
        end
    end
    self.srt_content2.onItemRender = OnItemRender2
    self.srt_content2.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data  then
            if scroll_rect_item.data["templateItem"] then
                --
            end
        end
    end
end
--当前这个是联盟
function OnItemRender(scroll_rect_item,index,dataItem)
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem
    --绑定上点击等事件，主要是为了抛给controller层进行分离   
    scroll_rect_item.InvokeFunc =  function(funcname,obj)
        --注意  这里的事件是存在多种的clickItemEvent 
        if dataItem[funcname] then
            dataItem[funcname](index,dataItem)
        end
    end
    if scroll_rect_item.data["templateItem"] then
        scroll_rect_item.data["templateItem"]:Dispose()
    end   
    scroll_rect_item.data["templateItem"] = item_rank_template2.NewItem(scroll_rect_item.transform,dataItem.rankInfo,dataItem.isSelf,dataItem.rankContext,dataItem.isInRank)
end
function OnItemRender1(scroll_rect_item,index,dataItem)
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem
    --绑定上点击等事件，主要是为了抛给controller层进行分离   
    scroll_rect_item.InvokeFunc =  function(funcname,obj)
        --注意  这里的事件是存在多种的clickItemEvent 
        if dataItem[funcname] then
            dataItem[funcname](index,dataItem)
        end
    end
    if scroll_rect_item.data["templateItem"] then
        scroll_rect_item.data["templateItem"]:Dispose()
    end
    local range1 = string_util.ToScientificNotation(dataItem.range1)
    local range2 = string_util.ToScientificNotation(dataItem.range2)
    local title = string.format("%s-%s",range1,range2)
    if range2 == "nil" then
        title = range1.."-..."
    end
    scroll_rect_item.data["templateItem"] = item_reward_template2.NewItem(scroll_rect_item.transform,title,dataItem.rewardId,dataItem.isSelf,false)
end

function OnItemRender2(scroll_rect_item,index,dataItem)
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem
    --绑定上点击等事件，主要是为了抛给controller层进行分离   
    scroll_rect_item.InvokeFunc =  function(funcname,obj)
        --注意  这里的事件是存在多种的clickItemEvent 
        if dataItem[funcname] then
            dataItem[funcname](index,dataItem)
        end
    end   
    local bg = scroll_rect_item:Get("bg")
    local titleText = scroll_rect_item:Get("titleText")
    local desText = scroll_rect_item:Get("desText")
    if  dataItem.range2 <= 0 then
        window:SetText(titleText, string_util.ToScientificNotation(dataItem.range1).."-...")
    else
        window:SetText(titleText, string_util.ToScientificNotation(dataItem.range1).."-"..string_util.ToScientificNotation(dataItem.range2))
    end
    window:SetText(desText, "X"..dataItem.rate)
    if dataItem.isCurHurtLevel then
        window:SetColor(bg, "#D3F2C8")
        window:SetColor(titleText, "#0A9713")
        window:SetColor(desText, "#0A9713")
    else
        window:SetColor(bg, "#D9E2E8")
        window:SetColor(titleText, "#283D4B")
        window:SetColor(desText, "#283D4B")
    end
end

function UIView:UpdateScrollList(data,len)
    if  not data then
        return
    end
    self.srt_content:SetData(data,len or #data)
    self.srt_content:Refresh(0, -1)
    --这个renderPerFrames 可以传过来，也可以直接v层自己设置
    self.srt_content.renderPerFrames = 10
end

function UIView:UpdateScrollList1(data,len)
    if  not data then
        return
    end
    self.srt_content1:SetData(data,len or #data)
    self.srt_content1:Refresh(0, -1)
    --这个renderPerFrames 可以传过来，也可以直接v层自己设置
    self.srt_content1.renderPerFrames = 10
end
function UIView:UpdateScrollList2(data,len)
    if  not data then
        return
    end
    self.srt_content2:SetData(data,len or #data)
    self.srt_content2:Refresh(0, -1)
    --这个renderPerFrames 可以传过来，也可以直接v层自己设置
    self.srt_content2.renderPerFrames = 10
end
---@public 设置分页
---@param type 1为同盟 0为个人
function UIView:SetTablePage(type,difficulty)   
    self:SetActive(self.tf_alliance,type == 1)
    self:SetActive(self.tf_self,type == 0)
    self:SetActive(self.tf_mvp_tips,false)
end

---@public 设置排名SHow
function UIView:SetRankInfoActive(active)
    if active then
        self:SetActive(self.item_rank_template2_first.transform,true)
        self:SetActive(self.obj_ComEmpty.transform,false)
        self:SetActive(self.txt_other_rank,true)
        self:SetText(self.txt_other_rank,lang.GetFormat(653550,"2~20"))
    else
        self:SetActive(self.item_rank_template2_first.transform,false)
        self:SetActive(self.obj_ComEmpty.transform,true)
        self:SetActive(self.txt_other_rank,false)
    end
end

function UIView:SetMVPRankInfo(data)
    self.VData.first_rank_item = self.VData.first_rank_item or item_rank_template2.NewItem(self.item_rank_template2_first,data.rankInfo,data.isSelf,data.rankContext,data.isInRank)
end

---@public 设置活动必要的数据
function UIView:SetActivityInfo(reward,level,nextHurt,totalDamage)
    --设置奖励
    self.VData.goodsItems = reward_mgr.GetRewardItemList(reward, self.tf_reward_content,
            function(id,number)
                local iui_item_detail =  require "iui_item_detail"
                local item_data = require "item_data"
                iui_item_detail.Show(id,nil,  item_data.Item_Show_Type_Enum.Reward_Interface, number,nil, nil, nil)
            end)    
    self.sld_x_green.value = totalDamage/nextHurt
    if totalDamage <= 0 then
        self:SetText(self.txt_slider_progress,lang.Get(653613))       
    else
        self:SetText(self.txt_slider_progress,string.format("%s/%s",totalDamage,nextHurt))
    end
end

function UIView:SetMvpAddition(curMvpRate)
    if curMvpRate == 0 then        
        self:SetText(self.txt_tips_mvp_text_1,lang.Get(653614))
    else
        self:SetText(self.txt_tips_mvp_text_1,lang.GetFormat(653548,curMvpRate))
    end
end

function UIView:OnBtnTips1ClickedProxy()
    self.VData.clickTips1 = true
    self:SetActive(self.tog_1,false)
    self:SetActive(self.tog_2,false)
    self:SetTablePage(0)
end

function  UIView:OnBtnMvp_tipsClickedProxy(mvpRates)
    if not mvpRates then
        return
    end    
    self:SetActive(self.tf_mvp_tips,true)
    self:UpdateScrollList2(mvpRates)
end
function  UIView:OnBtnMvp_tips_closeClickedProxy()
    self:SetActive(self.tf_mvp_tips,false)
end

--奖励界面
----@public 设置当前奖励信息
function  UIView:SetCurRewardItem(dataItem)
    if not dataItem then
        self:SetActive(self.tf_cur_item_reward_template2,false)
        return
    end
    self:SetActive(self.tf_cur_item_reward_template2,true)
    local range1 = string_util.ToScientificNotation(dataItem.range1)
    local range2 = string_util.ToScientificNotation(dataItem.range2)
    local title = string.format("%s-%s",range1,range2)
    if range2 == "nil" then
        title = range1.."-..."
    end
    if self.VData.cur_reward_item then
        self.VData.cur_reward_item:Dispose()
    end
    self.VData.cur_reward_item = item_reward_template2.NewItem(self.tf_cur_item_reward_template2,title,dataItem.rewardId,dataItem.isSelf,true)
end
--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
		window.isBlurBg = true

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil)
        else
			window:LoadUIResource(ui_path, nil, nil, nil)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close()
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
