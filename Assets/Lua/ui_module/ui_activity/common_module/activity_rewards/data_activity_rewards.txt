---
--- Created by: yuan<PERSON>.
--- DateTime: 2024/11/25.
--- Desc: item_activity_rewards 的数据模块,帮助传递正确的数据结构

local require = require

local reward_mgr = require "reward_mgr"

module("data_activity_rewards")

function NewActivityCfg(items, recharges, iconPath)
    local cfg = {
        items = items or nil,
        recharges = recharges or nil,
        iconPath = iconPath or nil,
    }
    return cfg
end

function NewActivityItem(id, exp, rewards1, rewards2, rewards3)
    local item = {
        id = id or 0,
        exp = exp or 0,
        rewards = { }
    }
    if rewards1 then
        item.rewards[1] = reward_mgr.GetRewardGoodsList2(rewards1)
    end

    if rewards2 then
        item.rewards[2] = reward_mgr.GetRewardGoodsList2(rewards2)
    end

    if rewards3 then
        item.rewards[3] = reward_mgr.GetRewardGoodsList2(rewards3)
    end
    return item
end

function NewActivityData(exp, index, indexTable, rechargeStates, taskType)
    local data = {
        exp = exp or 0,
        index = index or 0,
        indexTable = indexTable,
        rechargeStates = rechargeStates,
        taskType = taskType,
    }
    return data
end