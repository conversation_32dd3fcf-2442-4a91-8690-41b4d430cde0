--@region FileHead
-- ui_pioneer_reward_item.txt ---------------------------------
-- author:  无名氏
-- date:    2024/11/6 15:26:08
-- ver:     1.0
-- desc:    Description
-------------------------------------------------
--@endregion 

--@region Require
local require   = require
local typeof    = typeof


local item_data = require "item_data"
local goods_item = require "goods_item_new"
local idle = require "idle"
local Vector3 = require "Vector3"
local Canvas = CS.UnityEngine.Canvas
local class                 = require "class"
local module_scroll_list    = require "scroll_list"
local log                   = require "log"
--@endregion 

--@region ModuleDeclare
module("ui_pioneer_reward_item")
--local interface = require "iui_pioneer_reward_item"
local entityResPath = "ui/prefabs/gw/activity/pioneeracicity/uipioneerrewarditem.prefab"
local UIPioneerRewardItem = {}
local ShowEffect = "art/effects/effects/effect_ui_payreward/prefabs/effect_ui_payreward.prefab"
--@endregion 

--@region WidgetTable
UIPioneerRewardItem.widget_table = {
    Rtsf_IconRoot = { path = "Auto_IconRoot", type = "RectTransform", event_name = "" },
    Rtsf_CanGetShow = { path = "Auto_CanGetShow", type = "RectTransform", event_name = "" },
    Rtsf_GettedImg = {path = "Auto_GettedImg", type = "RectTransform", event_name = "" },
    Rtsf_LockImage = {path = "Auto_LockImage", type = "RectTransform", event_name = "" },
    Rtsf_RedObj = {path = "RedObj", type = "RectTransform", event_name = "" },
    
    
--@region User
--@endregion 
}
--@endregion 

function UIPioneerRewardItem:ctor()
	--self.__base:ctor(selfType)

end 
function UIPioneerRewardItem:Init(parentTrans, callback, scale, index)
    self.index = index
    if not self.isInit then
        self:LoadResource(entityResPath, "", function()
            if callback then
                callback()
            end
            self.isInit = true
            self.scale = scale or 1

            self.transform.localScale = Vector3.one * self.scale


        end, idle.ITEM_IO, parentTrans)
    else
        if callback then
            callback()
        end
    end

    return self
end 

function UIPioneerRewardItem:SetData(data, curOrder)
    if not data then
        log.Error("data is nil")
        return
    end
    self.Rtsf_LockImage.gameObject:SetActive(data.isLock)
    self.Rtsf_GettedImg.gameObject:SetActive(data.isGetted ~= 0)
    self.Rtsf_CanGetShow.gameObject:SetActive(data.isGetted == 0 and not data.isLock)
    if data.rewardId and data.rewardId ~= 0 then
        local item = self.goodIcon or goods_item.CGoodsItem()
        item:Init(self.Rtsf_IconRoot, nil, 0.7)
        item:SetGoods(nil,data.rewardId,data.rewardNum, function()
            if data.isGetted == 0 and not data.isLock then
                --领取奖励
                data.GetRewardEvent(data.TaskID)
            else
                local iui_item_detail =  require "iui_item_detail"
                iui_item_detail.Show(data.rewardId,nil,  item_data.Item_Show_Type_Enum.Reward_Interface, data.rewardNum,nil, nil, nil)
            end
        end)
        self.goodIcon = item
    end
    local effect_item = require "effect_item"
    self.effectItem = self.effectItem or effect_item.CEffectItem():Init(ShowEffect, self.Rtsf_CanGetShow.transform, curOrder, nil, 1, false, false, false)
    self.Rtsf_RedObj:GetComponent(typeof(Canvas)).overrideSorting = true
    self.Rtsf_RedObj:GetComponent(typeof(Canvas)).sortingOrder = curOrder + 2
end

function UIPioneerRewardItem:GetRedParent()
    return self.Rtsf_RedObj.transform
end

function UIPioneerRewardItem:SetEffectOrder(curOrder)
    if curOrder and curOrder ~= 0 then
        if self.effectItem  then
            self.effectItem:SetRenderOrder(curOrder)
        end
        self.Rtsf_RedObj:GetComponent(typeof(Canvas)).sortingOrder = curOrder + 2
    end
end

function UIPioneerRewardItem:RegisterEvents()
    
end

function UIPioneerRewardItem:UnsubscribeEvents()

end



function UIPioneerRewardItem:Dispose()
    if self.goodIcon then
        self.goodIcon:Dispose()
        self.goodIcon = nil
        
    end

    if self.effectItem then
        self.effectItem:Dispose()
        self.effectItem = nil
    end
    self.__base:Dispose()

end

local base_game_object = require "base_game_object"
CUIPioneerRewardItem = class(base_game_object, nil, UIPioneerRewardItem)

