local require = require
local pairs = pairs     
local table = table
local tonumber = tonumber
local type = type
local typeof = typeof
local math = math

local item_data = require "item_data"
local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local class = require "class"
local binding = require "ui_general_exchange_shop_binding"
local goodsItem_new = require "goods_item_new"
local Canvas = CS.UnityEngine.Canvas

local GWG = GWG

--region View Life
module("ui_general_exchange_shop")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base:SetVCTypeUI(enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base:Init(self)
    -- 提升层级以免被遮住
    self.UIRoot:GetComponent(typeof(Canvas)).sortingOrder = self.UIRoot:GetComponent(typeof(Canvas)).sortingOrder + 2

    self.VData = {}
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base:OnHide()
end

function UIView:Close()   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.VData = nil

    self.__base.Close(self)
end
--endregion

--region View Logic

--设置基础显示
function UIView:InitBaseShow(data)
    self.good = self.good or goodsItem_new.CGoodsItem()
    self.good:Init(self.rtf_IconRoot, nil, 0.8)
    self.good:SetGoods(nil, data.goodId, data.goodNum, function()
        if data.goodId == 41541 then
            local iui_item_detail = require "iui_item_detail"
            local skep_mgr = require "skep_mgr"
            local goodsSid = skep_mgr.GetConstGoodsSidByID(data.goodId)
            iui_item_detail.Show(data.goodId, goodsSid, item_data.Item_Show_Type_Enum.Reward_Interface, data.goodNum)
        end
    end)
    self.txt_PriceText.text = data.price

    GWG.GWAssetMgr:LoadGoodsIcon(data.priceIcon, function(sp)
        if self:IsValid() then
            self.img_PriceIcon.sprite = sp
            self.img_HasExchangeIcon.sprite = sp
            self.img_ExchangeBtnIcon.sprite = sp
        end
    end)
    self.txt_Title.text = lang.Get(tonumber(data.name))
    self:RefreshUI(data)
end

--设置价格和数量显示
function UIView:RefreshUI(data)
    self.inp_CountInputField.text = data.count
    self.tmp_HasGiftNum.text = data.canSpendNum
    local textColor = "#795B27"
    if math.floor(data.canSpendNum / data.price) < data.count then
        textColor = "#FF071B"
    end
    self.txt_ExchangeBtnText.text = "<color=" .. textColor .. ">" .. data.price * data.count .. "</color>"
end

--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil)
        else
			window:LoadUIResource(ui_path, nil, nil, nil)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close()
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
