--- 新加的活动相关的事件集中定义在这里
--- Generated by Emmy<PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by <PERSON><PERSON><PERSON><PERSON><PERSON>.
--- DateTime: 2024/11/6 20:31
----@module gw_event_activity_define
module("gw_event_activity_define")
--有活动更新的事件
GW_HAVE_ACTIVITIES_UPDATE = "GW_HAVE_ACTIVITIES_UPDATE" 
--一个活动更新事件 para1:活动ID  para2:是否 isOpen para3: 是否是服务器更新数据isServerUpdate
GW_ONE_ACTIVITY_UPDATE = "GW_ONE_ACTIVITY_UPDATE"

--入口更新事件 para1 :入口id
GW_ACTIVITY_ENTRANCE_UPDATE = "GW_ACTIVITY_ENTRANCE_UPDATE"

GW_ACTIVITY_CENTER_CLOSE = "GW_ACTIVITY_CENTER_CLOSE" --活动中心UI退出事件

GW_ACTIVITY_RED_NEED_UPDATE = "GW_ACTIVITY_RED_NEED_UPDATE" --新活动红点需要刷新事件 para1:活动ID
GW_ACTIVITY_SET_WEAK_RED = "GW_ACTIVITY_SET_WEAK_RED"       --设置活动的弱红点 para1: activityID 活动ID ;para2: 弱红点 number  0/1 ----表示消除弱红点/重置弱红点
GW_ACTIVITY_BIND_UI_TAB_RED = "GW_ACTIVITY_BIND_UI_TAB_RED"     --绑定UI的tab红点事件 
--先锋目标任务数据刷新
GW_PIONEER_TARGET_TASK_REFRESH = "GW_PIONEER_TARGET_TASK_REFRESH"

--先锋目标积分数据刷新
GW_PIONEER_TARGET_SCORE_REFRESH = "GW_PIONEER_TARGET_SCORE_REFRESH"

--------------------------世界boss模块-----------------------
---世界boss成就更新
GW_WORLD_BOSS_ACHIEVEMENT_UPDATE = "GW_WORLD_BOSS_ACHIEVEMENT_UPDATE"
--------------------------世界boss模块end--------------------

--------------------------联盟boss模块-----------------------
---联盟bossInfo更新
GW_ACTIVITY_ALLIANCE_BOSS_INFO_NTF = "GW_ACTIVITY_ALLIANCE_BOSS_INFO_NTF"
---联盟bossInfo请求
GW_ACTIVITY_ALLIANCE_BOSS_INFO_REQ = "GW_ACTIVITY_ALLIANCE_BOSS_INFO_REQ" 
--------------------------联盟boss模块end--------------------

--------------------------战令模块Start-------------------------------
-- 通用活动数据创建
GW_ACTIVITY_COMMON_CREATE = "GW_ACTIVITY_COMMON_CREATE"
-- 通用活动数据移除
GW_ACTIVITY_COMMON_REMOVE = "GW_ACTIVITY_COMMON_REMOVE"
-- 通用活动数据销毁
GW_ACTIVITY_COMMON_DISPOSE = "GW_ACTIVITY_COMMON_DISPOSE"

-- 通用活动配置更新
GW_ACTIVITY_COMMON_CFG_UPDATE = "GW_ACTIVITY_COMMON_CFG_UPDATE"
-- 通用活动数据更新
GW_ACTIVITY_COMMON_DATA_UPDATE = "GW_ACTIVITY_COMMON_DATA_UPDATE"
--------------------------战令模块End---------------------------------

-------------------------通用排行榜Start-----------------------------
--通用排行榜数据更新
GW_COMMON_RANK_DATA_UPDATE = "GW_COMMON_RANK_DATA_UPDATE"
-------------------------通用排行榜End-------------------------------

-------------------------全面备战模块Start----------------------------
--刷新全面备战礼包数据
GW_FULL_SIEGE_GIFT_DATA_UPDATE = "GW_FULL_SIEGE_GIFT_DATA_UPDATE"
--刷新活动礼券兑换商店数据
GW_FULL_SIEGE_EXCHANGE_SHOP_DATA_UPDATE = "GW_FULL_SIEGE_EXCHANGE_SHOP_DATA_UPDATE"
-------------------------全面备战模块End------------------------------
-------------------------幸运转盘模块Start----------------------------
--获得幸运转盘抽取结果
GW_LUCKY_SPIN_RESULT = "GW_LUCKY_SPIN_RESULT"
--刷新抽取次数
GW_LUCKY_SPIN_COUNT_UPDATE = "GW_LUCKY_SPIN_COUNT_UPDATE"
-------------------------幸运转盘模块End----------------------------
-------------------------寻宝大作战模块Start----------------------------
-- 活动信息
GW_FIND_TREASURE_ACTIVITY_DATA = "GW_FIND_TREASURE_ACTIVITY_DATA"
-- 奖励领取
GW_FIND_TREASURE_RECEIVE_AWARD = "GW_FIND_TREASURE_RECEIVE_AWARD"
-- 抽奖
GW_FIND_TREASURE_DRAW_AWARD = "GW_FIND_TREASURE_DRAW_AWARD"
-- 秘宝选中
GW_FIND_TREASURE_SELECT_BIG = "GW_FIND_TREASURE_SELECT_BIG"
---------------------------寻宝大作战模块End----------------------------
-------------------------登录好礼模块Start----------------------------
--登录好礼入口刷新事件
GW_LOGIN_GIFT_LOCK_STATE_UPDATE = "GW_LOGIN_GIFT_LOCK_STATE_UPDATE"
-------------------------登录好礼模块End----------------------------

-------------------------首充----------------------------
GW_FIRST_CHARGE_DATA_UPDATE = "GW_FIRST_CHARGE_DATA_UPDATE"


-------------------------限时礼包模块Start----------------------------
--限时礼包入口刷新事件
GW_LIMIT_GIFT_ICON_UPDATE = "GW_LIMIT_GIFT_ICON_UPDATE"
-------------------------限时礼包模块End----------------------------

------------------------活动底部红点提示刷新事件Start----------------------------
GW_ACTIVITY_REFRESH_RED_TIPS = "GW_ACTIVITY_REFRESH_RED_TIPS"
------------------------活动底部红点提示刷新事件End----------------------------