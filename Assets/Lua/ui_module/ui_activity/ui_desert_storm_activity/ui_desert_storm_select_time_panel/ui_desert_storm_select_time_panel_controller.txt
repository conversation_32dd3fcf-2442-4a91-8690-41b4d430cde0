local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type

local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local gw_storm_mgr = require "gw_storm_mgr"
local lang = require "lang"
local event_DesertStrom_define = require("event_DesertStrom_define")

--region Controller Life
module("ui_desert_storm_select_time_panel_controller")
local controller = nil
local UIController = newClass("ui_desert_storm_select_time_panel_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name)
    local activityData = gw_storm_mgr.GetStormDataActivity()
    self.CData.activityTimeData = activityData.OnGetBattleTimeList()
    self.isInit = false
    local signUpData = activityData.OnGetSignUpTeamData(event_DesertStrom_define.EnDesertStrom_Team.enDesertStrom_Team_A)
    self.selectTime = signUpData and signUpData.battleTimeIndex or 1;
    self:TriggerUIEvent("OnShowPanel",self.CData,self.selectTime)
    self.isInit = true
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close(data)   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents()
    
end

function UIController:AutoUnsubscribeEvents() 
    
end
--endregion

--region Controller Logic
function  UIController:OnBtnCloseBtnClickedProxy()
	ui_window_mgr:UnloadModule(self.view_name)
end
function  UIController:OnTogTimeSelectToggle_1ValueChange(state)
    if not self.isInit then
        return
    end
    if state then
        self.selectTime = 1;
    end
end
function  UIController:OnTogTimeSelectToggle_2ValueChange(state)
    if not self.isInit then
        return
    end
    if state then
        self.selectTime = 2;
    end
end
function  UIController:OnTogTimeSelectToggle_3ValueChange(state)
    if not self.isInit then
        return
    end
    if state then
        self.selectTime = 3;
    end
end
function  UIController:OnBtnConfirmClickedProxy()
    gw_storm_mgr.OnSelectBattleTime(self.selectTime)
    ui_window_mgr:UnloadModule(self.view_name)
end

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
