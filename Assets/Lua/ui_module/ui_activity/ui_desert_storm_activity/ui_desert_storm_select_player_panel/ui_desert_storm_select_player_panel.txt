local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local math = math
local tonumber = tonumber
local type = type
local Common_Util = CS.Common_Util.UIUtil

local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local game_scheme = require "game_scheme"
local binding = require "ui_desert_storm_select_player_panel_binding"
local event_DesertStrom_define = require("event_DesertStrom_define")
local os = os
local time_util = require "time_util"
local face_item = require "face_item_new"
local gw_storm_mgr = require "gw_storm_mgr"
local GameObject		= CS.UnityEngine.GameObject
local log = require "log"
local typeof = typeof
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem

--region View Life
module("ui_desert_storm_select_player_panel")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.VData = {}
    self.createObj = {}
    --[[
    self.sr_ScrollView.onValueChanged:AddListener(function(scrollPos)
        local viewportHeight =self.sr_ScrollView.viewport.rect.height
        local contentTopY = self.tf_Content.anchoredPosition.y
        for i = 5,1,-1 do
            local allianceListObj = self.createObj[i]
            if allianceListObj and allianceListObj.item then
                local item = allianceListObj.item
                local header = item:Get("Bg")
                local body = item:Get("PlayerList")
                
                local headerTopY = - header.anchoredPosition.y - contentTopY
                local headerBottomY = headerTopY - header.rect.height + 71
                
                if headerTopY < 71 and headerBottomY > 0 then--and headerViewportBottom <= viewportHeight  then
                     --local bodyBottomY = body.anchoredPosition.y - body.rect.height;
                     local visibleHeight = math.min(71,headerBottomY) --bodyBottomY;
                    if i == 4 then
                        --log.Error(i..":"..visibleHeight..","..headerViewportBottom..","..header.rect.height)
                    end
                    if visibleHeight > header.rect.height then
                        self:OnShowHeaderObj(allianceListObj.item)
                        return;
                    else
                        self:OnHideHeaderObj()
                    end
                end
                
            end
            
        end
    end)
    ]]
end

function UIView:OnShowHeaderObj(value)
    Common_Util.SetActive(self.obj_DesertStormHeaderObj,true)
    
end

function UIView:OnHideHeaderObj()
    Common_Util.SetActive(self.obj_DesertStormHeaderObj,false)
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.VData = nil    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic
function UIView:OnShowPanel(data)
    self.data = data
    Common_Util.SetActive(self.btn_darkMask,false)
    local signUpInfo = data.activityData.OnGetSignUpInfo()
    local activityState = data.activityData.GetActivityState()
    self.txt_BattleCount.text = string.format2("{%s1}/{%s2}",signUpInfo.battleCount,20)
    self.txt_ReplacementCount.text = string.format2("{%s1}/{%s2}",signUpInfo.replacementCount,20)
    local isSignUpState = activityState == event_DesertStrom_define.EnDesertStrom_ActivityType.enDesertStrom_ActivityType_SignUp
    Common_Util.SetActive(self.obj_ChangeTimeTog,isSignUpState)
    Common_Util.SetActive(self.obj_ChangeTimeTog,isSignUpState)
    self.tog_changeTime_1.isOn = data.isServerDate
    self.tog_changeTime_2.isOn = data.isServerDate
    if isSignUpState then
        self:OnSetTime(data.isServerDate)
    end
    if data.selectTime == 0 then
        self.txt_selectTime.text = lang.Get(1003497)
    else
        
    end

    self:SetOrUpdatePlayerList(false)
end

function UIView:SetOrUpdatePlayerList(isUpdate)
    local signUpInfo = self.data.activityData.OnGetSignUpInfo()
    local activityState = self.data.activityData.GetActivityState()
    local isSignUpState = activityState == event_DesertStrom_define.EnDesertStrom_ActivityType.enDesertStrom_ActivityType_SignUp
    local alliance_user_data = require "alliance_user_data"
    local alliance_data = require "alliance_data"
    for i = 5,1,-1 do
        local playerList = alliance_user_data.GetRankSortData(i, true)
        local playerData = {}
        local totalCount = 0
        local battleCount = 0
        local replaceCount = 0
        for j,k in pairs(playerList) do
            local temp = {}
            temp.timeHope = {}
            if signUpInfo.roleData[k.roleId] then
                temp.isSignUp = true;
                temp.isBattle = signUpInfo.roleData[k.roleId].main
                for l,m in ipairs(signUpInfo.roleData[k.roleId].battle_time_hope) do
                    table.insert(temp.timeHope,m)
                end
                temp.enTeam = signUpInfo.roleData[k.roleId].enTeam
                if signUpInfo.roleData[k.roleId].main then
                    battleCount = battleCount + 1
                else
                    replaceCount = replaceCount + 1
                end
            else
                if isSignUpState then
                    temp.isSignUp = false;
                    temp.isBattle = false;
                    temp.enTeam = nil
                end
            end
            temp.playerInfo = k
            table.insert(playerData,temp)
            totalCount = totalCount + 1
        end
        local allianceListObj = self.createObj[i]
        local item = {}
        if not allianceListObj then
            local go = GameObject.Instantiate(self.obj_CreateDesertStormHeaderObj,self.tf_Content)
            Common_Util.SetActive(go,true)
            item = Common_Util.GetComponent(go.transform,typeof(ScrollRectItem),"")
            allianceListObj = {}
            allianceListObj.playerList = {}
        else
            item = allianceListObj.item
        end
        local icon = item:Get("PosIcon")
        icon:Switch(i-1)
        local TotalPlayerCount = item:Get("TotalPlayerCount")
        TotalPlayerCount.text = totalCount
        local BattlePlayerCount = item:Get("BattlePlayerCount")
        BattlePlayerCount.text = battleCount
        local ReplacePlayerCount = item:Get("ReplacePlayerCount")
        ReplacePlayerCount.text = replaceCount
        local Toggle = item:Get("Toggle")

        local PlayerList = item:Get("PlayerList")
        local DesertStormPlayerObj = item:Get("DesertStormPlayerObj")
        for j,k in ipairs(playerData) do
            local tempObj = allianceListObj.playerList[j]
            local item2 = {}
            if not tempObj then
                local go1 = GameObject.Instantiate(DesertStormPlayerObj,PlayerList)
                Common_Util.SetActive(go1,true)
                item2 = Common_Util.GetComponent(go1.transform,typeof(ScrollRectItem),"")
                tempObj = {}
                tempObj.item = item2
            else
                item2 = tempObj.item
            end
            local applicationObj = item2:Get("applicationObj")
            Common_Util.SetActive(applicationObj,#k.timeHope > 0)
            local PlayerHead = item2:Get("PlayerHead")
            local HeadTran = face_item:CFaceItem():Init(PlayerHead, function(p)
                if not p then
                    return
                end
            end, 1)
            local faceStr = k.playerInfo.faceID
            if k.playerInfo.faceStr and not string.IsNullOrEmpty(k.playerInfo.faceStr) then
                faceStr = k.playerInfo.faceStr
            end
            HeadTran:SetFrameID(k.playerInfo.frameID, true)
            tempObj.headObj = HeadTran
            local PlayerName = item2:Get("PlayerName")
            PlayerName.text = k.playerInfo.strName
            local PowerText = item2:Get("PowerText")
            PowerText.text = k.playerInfo.personCE
            local battleToggle = item2:Get("battleToggle")
            local replacementToggle = item2:Get("replacementToggle")
            local NoBattle = item2:Get("NoBattle")
            local NoReplacement = item2:Get("NoReplacement")
            battleToggle.interactable = isSignUpState == true
            replacementToggle.interactable = isSignUpState == true
            if isSignUpState then
                Common_Util.SetActive(NoBattle,false)
                Common_Util.SetActive(NoReplacement,false)
                Common_Util.SetActive(battleToggle,true)
                Common_Util.SetActive(replacementToggle,true)
                if k.isSignUp then
                    battleToggle.isOn = k.isBattle
                    replacementToggle.isOn = not k.isBattle
                else
                    battleToggle.isOn = false
                    replacementToggle.isOn = false
                end
            else
                if k.isBattle then
                    Common_Util.SetActive(NoReplacement,true)
                    Common_Util.SetActive(NoBattle,false)
                    battleToggle.isOn = true
                    Common_Util.SetActive(battleToggle,true)
                    Common_Util.SetActive(replacementToggle,false)
                else
                    Common_Util.SetActive(NoReplacement,false)
                    Common_Util.SetActive(NoBattle,true)
                    replacementToggle.isOn = true
                    Common_Util.SetActive(battleToggle,false)
                    Common_Util.SetActive(replacementToggle,true)
                end
            end

            battleToggle.onValueChanged:RemoveAllListeners()
            battleToggle.onValueChanged:AddListener(function(value)
                if value then
                    gw_storm_mgr.OnSetMember(k.playerInfo.roleId,true,true)
                else
                    local replaceValue = replacementToggle.isOn
                    if not replaceValue then
                        gw_storm_mgr.OnSetMember(k.playerInfo.roleId,false,false)
                    end
                end
            end)
            replacementToggle.onValueChanged:RemoveAllListeners()
            replacementToggle.onValueChanged:AddListener(function(value)
                if value then
                    gw_storm_mgr.OnSetMember(k.playerInfo.roleId,false,true)
                else
                    local battleValue = battleToggle.isOn
                    if not battleValue then
                        gw_storm_mgr.OnSetMember(k.playerInfo.roleId,false,false)
                    end
                end
            end)
            allianceListObj.playerList[j] = tempObj
        end
        allianceListObj.item = item
        self.createObj[i] = allianceListObj
        if not isUpdate then
            Common_Util.SetActive(PlayerList,i == 5)
        end
    end
end

function UIView:OnSetTime(value)
    local signUpInfo = self.data.activityData.OnGetSignUpInfo()
    local selectTime = signUpInfo.teamData[event_DesertStrom_define.EnDesertStrom_Team.enDesertStrom_Team_A].battleTimeIndex--battle_time_index
    local timeCfg = game_scheme:DesertStormConfig_0(1).BattleStartTime
    local battleDay = self.data.activityData.GetBattleDay()
    local startStr = {}
    for part in string.gmatch(timeCfg,"([^#]+)")do
        table.insert(startStr,part);
    end
    if value then
        self.txt_TimeTips.text = lang.Get(1003491)
        self.txt_TimeTips_1.text = self.txt_TimeTips.text
        self.txt_TimeTips_2.text = self.txt_TimeTips.text
        local date = time_util.GetLocalDate(battleDay.beginTime)
        self.txt_battleTime.text = string.format2("{%s1} {%s2}",string.format('%d-%02d-%02d',date.year, date.month, date.day),startStr[selectTime])
    else
        self.txt_TimeTips.text = lang.Get(1003437)
        self.txt_TimeTips_1.text = self.txt_TimeTips.text
        self.txt_TimeTips_2.text = self.txt_TimeTips.text
        local date = os.date("*t", battleDay.beginTime)
        self.txt_battleTime.text = string.format('%d-%02d-%02d %02d:%02d:%02d',date.year, date.month, date.day,date.hour,date.min,date.sec)
    end
end

function UIView:OnHideSelectTimePanel()
    Common_Util.SetActive(self.btn_darkMask,false)
end

--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, nil, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil,nil, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
