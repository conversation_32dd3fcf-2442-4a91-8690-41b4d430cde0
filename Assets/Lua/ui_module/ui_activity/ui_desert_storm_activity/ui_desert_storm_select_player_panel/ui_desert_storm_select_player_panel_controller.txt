local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type

local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local gw_storm_mgr = require "gw_storm_mgr"
local player_prefs = require "player_prefs"
local event_DesertStrom_define = require("event_DesertStrom_define")
local lang = require "lang"

--region Controller Life
module("ui_desert_storm_select_player_panel_controller")
local controller = nil
local UIController = newClass("ui_desert_storm_select_player_panel_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name)
    local activityData = gw_storm_mgr.GetStormDataActivity()
    local tempData = {}

    tempData.activityData = activityData
    tempData.isServerDate = player_prefs.GetCacheData("desertStormCache",0) == 0
    tempData.selectTime = 0 --0表示全部，123则表示对应下标
    self.CData.selectTime = tempData.selectTime
    self:TriggerUIEvent( "OnShowPanel",tempData)
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close(data)   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents()
    self.updatePanel = function()
        self:OnActivityStateUpdate()
    end
    self:RegisterEvent(event_DesertStrom_define.DESERTSTROM_SIGNUP_INFO_UPDATE,self.updatePanel)
end

function UIController:AutoUnsubscribeEvents() 
end
--endregion

--region Controller Logic

function UIController:OnActivityStateUpdate()
    self:TriggerUIEvent("SetOrUpdatePlayerList")
end

function  UIController:OnBtnCloseBtnClickedProxy()
	ui_window_mgr:UnloadModule(self.view_name)
end
function  UIController:OnBtnTipsClickedProxy()
end
function  UIController:OnBtnDropdownClickedProxy()
end
function  UIController:OnBtnArrowClickedProxy()
end
function  UIController:OnBtnConfirmClickedProxy()
end
function  UIController:OnTogTimeSelectToggle_1ValueChange(state)
end
function  UIController:OnTogTimeSelectToggle_2ValueChange(state)
end
function  UIController:OnTogTimeSelectToggle_3ValueChange(state)
end
function  UIController:OnBtnConfirmSelectTimeClickedProxy()
end

function UIController:OnBtnMaskClickedProxy()
    
end

function UIController:OnBtnDarkMaskClickedProxy()
    self:TriggerUIEvent( "OnHideSelectTimePanel")
end

function UIController:OnTogChangeTimeValueChange(state)
    if state then
        player_prefs.SetCacheData("desertStormCache",0)
        self.CData.isServerDate = 0
    else
        player_prefs.SetCacheData("desertStormCache",1)
        self.CData.isServerDate = 1
    end
    self:TriggerUIEvent("OnSetTime",self.CData.isServerDate == 0) 
end

function UIController:OnTogChangeTime_1ValueChange(state)
    self:OnTogChangeTimeValueChange(state)
end

function UIController:OnTogSelectTimeTogValueChange(state)
    
end

function UIController:OnTogSelectTimeTog_1ValueChange(state)
    
end

function UIController:OnTogSelectTimeTog_2ValueChange(state)
    
end

function UIController:OnTogChangeTime_2ValueChange(state)
    self:OnTogChangeTimeValueChange(state)
end

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
