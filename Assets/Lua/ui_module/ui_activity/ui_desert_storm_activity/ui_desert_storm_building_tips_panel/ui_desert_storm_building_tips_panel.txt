local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local UIUtil = CS.Common_Util.UIUtil

local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_desert_storm_building_tips_panel_binding"
local card_sprite_asset = require "card_sprite_asset"
local game_scheme = require "game_scheme"
local Common_Util = CS.Common_Util.UIUtil

--region View Life
module("ui_desert_storm_building_tips_panel")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)
    self.buildingAsset = card_sprite_asset.CreateCardSpriteAsset("stormbuilding")
    self.buffSpriteAsset = card_sprite_asset.CreateBuffAsset()
    self.VData = {}
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    if self.buildingAsset then
        self.buildingAsset:Dispose()
        self.buildingAsset = nil
    end
    if self.buffSpriteAsset then
        self.buffSpriteAsset:Dispose()
        self.buffSpriteAsset = nil
    end
    self.VData = nil    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic
function UIView:OnSetPanel(data)
    local buildingCfg = data
    self.buildingAsset:GetSprite(buildingCfg.DetailsMinimap,function(sprite)
        self.img_buildingIcon.sprite = sprite
    end)
    self.txt_buildingName.text = lang.Get(buildingCfg.Remark2)
    self.txt_desc.text = lang.Get(buildingCfg.Desc)
    self.txt_aliiancePoint.text = buildingCfg.FirstOccupiedScore.data[0]
    self.txt_aliianceSpeed.text = string.format2("{%s1}/s",buildingCfg.PersonalPoints.data[0])
    self.txt_playerPoint.text = buildingCfg.FirstOccupiedScore.data[1]
    self.txt_playerSpeed.text = string.format2("{%s1}/s",buildingCfg.PersonalPoints.data[1])
    Common_Util.SetActive(self.obj_buffObj,buildingCfg.BuffID.count > 0)
    if buildingCfg.BuffID.count > 0 then
        local buffCfg = game_scheme:GWMapBuff_0(buildingCfg.BuffID.data[0])
        if buffCfg then
            self.buffSpriteAsset:GetSprite(buffCfg.icon,function(sprite)
                self.img_buffIcon.sprite = sprite
            end)
            self.txt_buffTitle.text = lang.Get(buffCfg.describe)
            if buffCfg.effect.count > 0 then
                local buffValue = game_scheme:GWMapEffect_0(buffCfg.effect.data[0])
                if buffValue then
                    local proToLang = game_scheme:ProToLang_0(buffValue.nGroupID);
                    if proToLang then
                        if proToLang.ProType == 1 then
                            self.txt_buffValue.text = "+"..(buffValue.strParam[0]);
                        elseif proToLang.ProType == 2 then
                            self.txt_buffValue.text = "+"..string.format("%g%%",buffValue.strParam[0] / 100);
                        elseif proToLang.ProType == 3 then
                            self.txt_buffValue.text = "+"..(buffValue.strParam[0] / 10000);
                        else
                            local time_util = require "time_util"
                            self.txt_buffValue.text = "+"..time_util.GetTimeStrBySecond(buffValue.strParam[0]);
                        end
                    else
                        self.txt_buffValue.text = "+"..(buffValue.strParam[0]);
                    end
                end
            elseif buffCfg.effect2.count > 0 then
                local buffValue = game_scheme:Buff_0(buffCfg.effect2.data[0])
                if buffValue then
                    local proToLang = game_scheme:ProToLang_0(buffValue.nGroupID);
                    if proToLang then
                        if proToLang.ProType == 1 then
                            self.txt_buffValue.text = "+"..(buffValue.strParam[0]);
                        elseif proToLang.ProType == 2 then
                            self.txt_buffValue.text = "+"..string.format("%g%%",buffValue.strParam[0] / 100);
                        elseif proToLang.ProType == 3 then
                            self.txt_buffValue.text = "+"..(buffValue.strParam[0] / 10000);
                        else
                            local time_util = require "time_util"
                            self.txt_buffValue.text = "+"..time_util.GetTimeStrBySecond(buffValue.strParam[0]);
                        end
                    else
                        self.txt_buffValue.text = "+"..(buffValue.strParam[0]);
                    end
                end
            end
        end
    end
end
--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, nil, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil,nil, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
