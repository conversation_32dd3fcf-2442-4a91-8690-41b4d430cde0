local require = require
local pairs = pairs     
local table = table
local type = type
local ipairs = ipairs

local enum_define = require "enum_define"
local ui_base = require "ui_base"
local class = require "class"
local binding = require "ui_desert_storm_activity_main_panel_binding"
local gw_storm_mgr = require "gw_storm_mgr"
local gw_storm_data_activity = require "gw_storm_data_activity"
local Common_Util = CS.Common_Util.UIUtil
local gw_const = require "gw_const"
local goods_item = require "goods_item_new"
local util = require "util"
local lang = require "lang"
local math = math
local string = string
local os = os
local iui_item_detail = require "iui_item_detail"
local item_data = require "item_data"
local time_util = require "time_util"
local game_scheme = require "game_scheme"
local event_DesertStrom_define = require("event_DesertStrom_define")
local card_sprite_asset = require "card_sprite_asset"
local alliance_user_data = require "alliance_user_data"

--region View Life
module("ui_desert_storm_activity_main_panel")
local ui_path = binding.UIPath
local window = nil
local alliance_sprite_asset = nil
local sprite_asset = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)
    alliance_sprite_asset = alliance_sprite_asset or card_sprite_asset.CreateLeagueAsset()
    self:InitScrollRectTable()
    self.activityData = gw_storm_mgr.GetStormDataActivity()
    self.VData = {}
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    if self.srt_content then
        for i,v in pairs(self.srt_content) do
            v:Dispose()
        end
    end
    if self.timeTicker then
        util.RemoveDelayCall(self.timeTicker)
        self.timeTicker = nil;
    end
    self.VData = nil    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic

function UIView:OnShowPanel(data)
    self.data = data;
    if self.timeTicker then
        util.RemoveDelayCall(self.timeTicker)
        self.timeTicker = nil;
    end
    local reward_mgr = require "reward_mgr"
    local rewardCfg = reward_mgr.GetRewardGoodsList(data.showAward)
    local dataList = {}
    local dataCount = 0
    for i,v in pairs(rewardCfg) do
        local item =
        {
            id = v.id,
            count = v.num,
        }
        table.insert(dataList,item)
        dataCount = dataCount + 1
    end
    for i,v in ipairs(dataList) do
        if not self.srt_content[i] then
            self.srt_content[i] = goods_item.CGoodsItem():Init(self.rtf_content, nil, 0.75)
        end
        self.srt_content[i]:SetGoods(nil, v.id, v.count, function()
            iui_item_detail.Show(v.id, nil, item_data.Item_Show_Type_Enum.Reward_Interface, nil)
        end)
    end
    if #self.srt_content > dataCount then
        for i,v in ipairs(self.srt_content) do
            if i >= dataCount then
                v:Dispose()
                v = nil
            end
        end
    end
    if self.data.activityState == event_DesertStrom_define.EnDesertStrom_ActivityType.enDesertStrom_ActivityType_SignUp then
        self:SetSignUpPanel(self.data.activityTimeData.endTime)
    elseif self.data.activityState == event_DesertStrom_define.EnDesertStrom_ActivityType.enDesertStrom_ActivityType_Match then
        if gw_storm_mgr.GetStormDataActivity().OnCheckSignUpState() then
            self:SetMatchPanel()
            gw_storm_mgr.OnGetMatchInfo()
        else
            local battleDay = self.activityData.GetBattleDay()
            self:SetSignUpPanel(battleDay.endTime)
        end

    elseif self.data.activityState == event_DesertStrom_define.EnDesertStrom_ActivityType.enDesertStrom_ActivityType_Battle then
        if gw_storm_mgr.GetStormDataActivity().OnCheckSignUpState() then
            self:SetBattlePanel()
            gw_storm_mgr.OnGetMatchInfo()
        else
            local battleDay = self.activityData.GetBattleDay()
            self:SetSignUpPanel(battleDay.endTime)
        end

    end
end

function UIView:SetBattlePanel()
    Common_Util.SetActive(self.obj_BeforeBattle,false)
    Common_Util.SetActive(self.obj_InBattle,true)
    Common_Util.SetActive(self.txt_TimeLeftTips,false)
    Common_Util.SetActive(self.obj_TargetAllianceObj,true)
    self:SetSelfAllianceObj(true)

    self.txt_TimeLeftTipsInBattle.text = lang.Get(1003431)
    local startTime = self.data.activityTimeData.endTime - os.server_time()
    if self.timeTicker then
        util.RemoveDelayCall(self.timeTicker)
        self.timeTicker = nil;
    end
    self.txt_TimesLeftInBattle.text = time_util.FormatTime5(startTime)
    self.timeTicker = util.IntervalCall(1,function()
        startTime = startTime - 1;
        self.txt_TimesLeftInBattle.text = time_util.FormatTime5(startTime)
        if startTime <= 0 then
            if self.timeTicker then
                util.RemoveDelayCall(self.timeTicker)
                self.timeTicker = nil;
            end
            self.activityData.OnCheckAndUpdateState() --立即检测一次状态
        end
    end)
end


function UIView:SetMatchPanel()
    Common_Util.SetActive(self.obj_BeforeBattle,false)
    Common_Util.SetActive(self.obj_InBattle,true)
    Common_Util.SetActive(self.txt_TimeLeftTips,false)
    Common_Util.SetActive(self.obj_TargetAllianceObj,false)
    self.txt_TimeLeftTipsInBattle.text = lang.Get(1003429)
    local startTime = self.data.activityTimeData.endTime - os.server_time()
    if self.timeTicker then
        util.RemoveDelayCall(self.timeTicker)
        self.timeTicker = nil;
    end
    self.txt_TimesLeftInBattle.text = time_util.FormatTime5(startTime)
    self.timeTicker = util.IntervalCall(1,function()
        startTime = startTime - 1;
        self.txt_TimesLeftInBattle.text = time_util.FormatTime5(startTime)
        if startTime <= 0 then
            if self.timeTicker then
                util.RemoveDelayCall(self.timeTicker)
                self.timeTicker = nil;
            end
            self.activityData.OnCheckAndUpdateState() --立即检测一次状态
        end
    end)
    self:SetSelfAllianceObj(false)
end

--展示未报名界面。其中未报名玩家也会进入该界面。
function UIView:SetSignUpPanel(endTime)
    Common_Util.SetActive(self.obj_BeforeBattle,true)
    Common_Util.SetActive(self.obj_InBattle,false)
    Common_Util.SetActive(self.txt_TimeLeftTips,true)
    
    local startTime = endTime - os.server_time()
    if self.timeTicker then
        util.RemoveDelayCall(self.timeTicker)
        self.timeTicker = nil;
    end
    self.timeTicker = util.IntervalCall(1,function()
        startTime = startTime - 1;
        self.txt_TimesLeft.text = time_util.FormatTime5(startTime)
        if startTime <= 0 then
            if self.timeTicker then
                util.RemoveDelayCall(self.timeTicker)
                self.timeTicker = nil;
            end
            self.activityData.OnCheckAndUpdateState() --立即检测一次状态
        end
    end)

    --现在开始判定自己是否有报名数据，有的话视为自己已经报名，否则视为未报名。
    --目前只考虑A小队
    local activityData = gw_storm_mgr.GetStormDataActivity()
    local cfgData = gw_storm_mgr.GetStormCfg().GetDesertStormConfig()
    if activityData.OnCheckSignUpState() then
        Common_Util.SetActive(self.obj_ChangeTime,true)
        Common_Util.SetActive(self.btn_Join,false)
        Common_Util.SetActive(self.btn_SelectTime,true)
        Common_Util.SetActive(self.btn_SelectPlayer,true)
        Common_Util.SetActive(self.btn_EnterBattleField,false)
        Common_Util.SetActive(self.btn_Spectate,false)
        self.tog_changeTime.isOn = self.data.isShowServerTime == 0
        self:OnSetTime(self.data.isShowServerTime == 0)
        self.txt_BattleCount.text = string.format2("{%s1}:{%s2}/{%s3}",lang.Get(1003495),activityData.OnGetSignUpInfo().battleCount,cfgData.UnitCap)
    else
        Common_Util.SetActive(self.obj_ChangeTime,false)
        Common_Util.SetActive(self.btn_Join,true)
        Common_Util.SetActive(self.btn_SelectTime,false)
        Common_Util.SetActive(self.btn_SelectPlayer,false)
        Common_Util.SetActive(self.btn_EnterBattleField,false)
        Common_Util.SetActive(self.btn_Spectate,false)
    end
end

function UIView:SetEnemyAllianceObj(matchInfo)
    if matchInfo.target then
        Common_Util.SetActive(self.img_RightAlliance,true)
        local allianceData = matchInfo.target.alliance
        local alliance_data = require("alliance_data")
        self.txt_RightAliianceName.text = string.format("[%s]%s", allianceData.shortName, allianceData.allianceName)
        local flagData = alliance_data.GetFlagIdData(allianceData.flag)
        if flagData then
            local allianceIconId = flagData.iconID;
            if alliance_sprite_asset then
                alliance_sprite_asset:GetSprite("qizhi" .. allianceIconId, function(sprite)
                    if sprite then
                        self.img_RightAlliance.sprite = sprite
                    end
                end)
            end
        end
        self.txt_RightAliiancePlayerCount.text = matchInfo.target.map_nums
        self.txt_RightAliianceScore.text = matchInfo.target.alliance_score
    else
        Common_Util.SetActive(self.img_RightAlliance,false)
    end
end

function UIView:SetSelfAllianceObj(isInBattle)
    local alliance_data = require("alliance_data")
    local userAllianceData = alliance_data.GetUserAllianceData()
    if not userAllianceData or userAllianceData.allianceId == nil then
        --TODO 没有联盟的人正常不会进入这里
    else
        self.txt_LeftAliianceName.text = string.format("[%s]%s", userAllianceData.shortName, userAllianceData.allianceName)
        local flagData = alliance_data.GetFlagIdData(userAllianceData.flag)
        if flagData then
            local allianceIconId = flagData.iconID;
            if alliance_sprite_asset then
                alliance_sprite_asset:GetSprite("qizhi" .. allianceIconId, function(sprite)
                    if sprite then
                        self.img_LeftAlliance.sprite = sprite
                    end
                end)
            end
        end
        local own = self.data.matchInfo.own
        if own then
            --TODO 没有联盟的人正常不会进入这里
            self.txt_LeftAliiancePlayerCount.text = own.map_nums
            self.txt_LeftAliianceScore.text = own.alliance_score
        else
            local signUpInfo = self.data.signUpInfo
            self.txt_LeftAliiancePlayerCount.text = signUpInfo and signUpInfo.playerCount or 0
            self.txt_LeftAliianceScore.text = 0
        end
        --if isInBattle then
        --    local own = self.data.matchInfo.own
        --    if own then
        --        --TODO 没有联盟的人正常不会进入这里
        --        self.txt_LeftAliiancePlayerCount.text = own.map_nums
        --        self.txt_LeftAliianceScore.text = own.alliance_score
        --    end
        --else
        --    local signUpInfo = self.data.signUpInfo
        --    self.txt_LeftAliiancePlayerCount.text = signUpInfo and signUpInfo.playerCount or 0
        --    self.txt_LeftAliianceScore.text = 0
        --end

    end
end

function UIView:InitScrollRectTable()
    self.srt_content = {}
    --self.srt_content.onItemRender = function(...)
    --    self:OnItemRender(...)
    --end;
    --self.srt_content.onItemDispose = function(scroll_rect_item,index)
    --    if scroll_rect_item then
    --        if scroll_rect_item.data and scroll_rect_item.data[3] then
    --            scroll_rect_item.data[3]:Dispose()
    --        end
    --    end
    --end
end

function UIView:OnItemRender(scroll_rect_item,index,dataItem)
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index;
    scroll_rect_item.data[2] = dataItem;
    Common_Util.SetActive(scroll_rect_item.gameObject,true)
    local objParent = scroll_rect_item:Get("parent");
    local item = scroll_rect_item.data[3]
    if not item then
        item = goods_item.CGoodsItem():Init(objParent, nil, 0.75)
        scroll_rect_item.data[3] = item
    end
    item:SetGoods(nil, dataItem.id, dataItem.count, function()
        iui_item_detail.Show(dataItem.id, nil, item_data.Item_Show_Type_Enum.Reward_Interface, nil)
    end)
end

function UIView:OnSetTime(value)
    local activityData = gw_storm_mgr.GetStormDataActivity()
    local timeList = activityData.OnGetBattleTimeList()
    local battleDay = self.activityData.GetBattleDay()
    if value then --服务器时间
        self.txt_TimeType.text = lang.Get(1003491)
        local date = time_util.GetLocalDate(battleDay.beginTime)
        self.txt_BattleDayValue.text = string.format('%d-%02d-%02d',date.year, date.month, date.day)
        self.txt_BattleTime.text = timeList[self.data.signUpInfo.teamData[event_DesertStrom_define.EnDesertStrom_Team.enDesertStrom_Team_A].battleTimeIndex].serverHour
    else
        self.txt_TimeType.text = lang.Get(1003437)
        local date = os.date("*t", battleDay.beginTime)
        self.txt_BattleDayValue.text = string.format('%d-%02d-%02d',date.year, date.month, date.day)
        self.txt_BattleTime.text = timeList[self.data.signUpInfo.teamData[event_DesertStrom_define.EnDesertStrom_Team.enDesertStrom_Team_A].battleTimeIndex].selfHour
    end

end

--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, nil, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil,nil, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
