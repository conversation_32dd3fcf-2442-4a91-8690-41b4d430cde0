local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type

local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"
local log = require "log"
local gw_storm_mgr = require "gw_storm_mgr"
local game_scheme = require "game_scheme"
local flow_text			= require "flow_text"
local player_prefs = require "player_prefs"
local event_DesertStrom_define = require("event_DesertStrom_define")

--region Controller Life
module("ui_desert_storm_activity_main_panel_controller")
local controller = nil
local UIController = newClass("ui_desert_storm_activity_main_panel_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name)
    gw_storm_mgr.GetSignUpData()
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close(data)   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:OnActivityStateUpdate()
    self.CData = {}
    local activityData = gw_storm_mgr.GetStormDataActivity()
    self.CData.activityState = activityData.GetActivityState()
    self.CData.activityTimeData = activityData.GetActivityTimeData()
    self.CData.matchInfo = activityData.OnGetMatchInfo()
    self.CData.signUpInfo = activityData.OnGetSignUpInfo()
    local rewardCfg = gw_storm_mgr.GetStormCfg().GetDesertStormConfig()
    self.CData.showAward = rewardCfg and rewardCfg.rewardid
    self.CData.isShowServerTime = player_prefs.GetCacheData("desertStormCache",0) --拿自己缓存的时间
    self:TriggerUIEvent("OnShowPanel",self.CData)
end

function UIController:OnMatchInfoUpdate()
    local activityData = gw_storm_mgr.GetStormDataActivity()
    local matchInfo = activityData.OnGetMatchInfo()
    self:TriggerUIEvent("SetEnemyAllianceObj",matchInfo)
end

function UIController:AutoSubscribeEvents()
    self.updatePanel = function()
        self:OnActivityStateUpdate()
    end
    self:RegisterEvent(event_DesertStrom_define.ON_DESERTSTROM_ACTIVITY_UPDATE,self.updatePanel)
    self:RegisterEvent(event_DesertStrom_define.ON_DESERTSTROM_ACTIVITY_STATE_UPDATE,self.updatePanel)
    self:RegisterEvent(event_DesertStrom_define.DESERTSTROM_SIGNUP_INFO_UPDATE,self.updatePanel)
    self.updateMatchInfo = function()
        self:OnMatchInfoUpdate()
    end
    self:RegisterEvent(event_DesertStrom_define.DESERTSTROM_MATCH_INFO_UPDATE,self.updateMatchInfo)
end

function UIController:AutoUnsubscribeEvents() 
    
end
--endregion

--region Controller Logic
function  UIController:OnBtnJoinClickedProxy()
    gw_storm_mgr.SignUpAction() --报名
end
function  UIController:OnBtnSelectTimeClickedProxy()
    local alliance_pb = require "alliance_pb"
    local alliance_mgr = require "alliance_mgr"
    local player_mgr = require "player_mgr"
    local temp, authority = alliance_mgr.GetRoleAuthority(player_mgr.GetPlayerRoleID())
    local isR4 = (authority >= alliance_pb.emAllianceAuthority_R4)
    if isR4 then
        ui_window_mgr:ShowModule("ui_desert_storm_select_time_panel")
    else
        flow_text.Add(lang.Get(1003439))
    end
    
end
function  UIController:OnBtnSelectPlayerClickedProxy()
    ui_window_mgr:ShowModule("ui_desert_storm_select_player_panel")
end
function  UIController:OnBtnEnterBattleFieldClickedProxy()
    gw_storm_mgr.EnterStorm()
    --gw_sand_mgr.TestEnterStorm(10001001)
end
function  UIController:OnBtnSpectateClickedProxy()
    log.Error("前往观战")
end
function  UIController:OnBtnRuleClickedProxy()
    ui_window_mgr:ShowModule("ui_desert_storm_help_tips_panel")
end
function  UIController:OnBtnShopClickedProxy()
    local gw_shop_util= require "gw_shop_util"
    gw_shop_util.ShowShopByType(gw_shop_util.ShopType.Honor)
end
function  UIController:OnBtnHistoryClickedProxy()
    log.Error("打开历史记录")
end
function  UIController:OnBtnTipsBtnClickedProxy()
    local ui_help = require "ui_help"
    ui_help.ShowWithDate(10061)
end
function UIController:OnBtnPlayerListClickedProxy()
    ui_window_mgr:ShowModule("ui_desert_storm_select_player_panel")
end
function  UIController:OnBtnCloseBtnClickedProxy()
	ui_window_mgr:UnloadModule(self.view_name)
end
function  UIController:OnBtnLogClickedProxy()
    log.Error("打开日志")
end
function  UIController:OnBtnRewardBtnClickedProxy()
    ui_window_mgr:ShowModule("ui_desert_storm_reward_panel")
end
function  UIController:OnTogGroupAValueChange(state)
    log.Error("切换A小队，本期不做")
end
function  UIController:OnTogGroupBValueChange(state)
    log.Error("切换B小队，本期不做")
end
function  UIController:OnBtnRewardBtnInBattleClickedProxy()
    ui_window_mgr:ShowModule("ui_desert_storm_reward_panel")
end
function  UIController:OnTogGroupAInBattleValueChange(state)
    log.Error("切换A小队，本期不做")
end
function  UIController:OnTogGroupBInBattleValueChange(state)
    log.Error("切换B小队，本期不做")
end

function UIController:OnTogChangeTimeValueChange(state)
    if state then
        player_prefs.SetCacheData("desertStormCache",0)
        self.CData.isShowServerTime = 0
    else
        player_prefs.SetCacheData("desertStormCache",1)
        self.CData.isShowServerTime = 1
    end
    self:TriggerUIEvent("OnSetTime",self.CData.isShowServerTime == 0)
end
--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
