local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type
local AllianceMgr = AllianceMgr

local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"
local game_scheme = require "game_scheme"
local e_handler_mgr = require "e_handler_mgr"
local player_mgr        = require "player_mgr"
local os = os
local city_siege_activity_data = require "city_siege_activity_data"
local flow_text = require "flow_text"
local log = require "log"
local cfg_util = require "cfg_util"
local festival_activity_mgr = require "festival_activity_mgr"
local activity_pb = require "activity_pb"
local event = require "event"
local event_activity_define = require("event_activity_define")
local sand_ui_event_define = require "sand_ui_event_define"
local gw_common_util = require "gw_common_util"

--region Controller Life
module("ui_city_siege_challenge_main_panel_controller")
local controller = nil
local UIController = newClass("ui_city_siege_challenge_main_panel_controller", controller_base)

local baseCity = --基础的城市，每个等级各一个，取该城市的信息来作为活动显示
{
    [1] = 11,
    [2] = 21,
    [3] = 31,
    [4] = 41,
    [5] = 51,
    [6] = 61,
    [7] = 71,
}

local baseActivityStage = --不同阶段类型对应的活动ID
{
    [1] = 2022,
    [2] = 2023,
    [3] = 2024,
}

function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self, view_name, controller_name)
    self.CData = {}
    self.CData.unlockPage = 0
    self:CheckActivityState()
end

function UIController:OnShow()
    self.__base.OnShow(self)
    --打开城市竞赛活动界面 上报
    city_siege_activity_data.EventReport("SandMapCompetition_ActivityCheck", {})
end

function UIController:Close()   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.CData = nil

    self.__base.Close(self)
end

function UIController:AutoSubscribeEvents()
    event.Register(event_activity_define.TMSG_COMM_ACTIVITY_RANK_RSP, self.OnSetRankingInfo)
    self:RegisterEvent(sand_ui_event_define.GET_CITY_SIEGE_ACTIVITY_DATA,function(_,msg)
        self:OnSetActivityData(msg)
    end)
end

function UIController:AutoUnsubscribeEvents()
    event.Unregister(event_activity_define.TMSG_COMM_ACTIVITY_RANK_RSP, self.OnSetRankingInfo)
end
--endregion

--region Controller Logic

function UIController:CheckActivityState()
    self.CData.pageData = {}
    
    local openServerTime = player_mgr.GetRoleOpenSvrTime()
    local currServerTime = os.server_time()
    local sandCity = city_siege_activity_data.GetSandCityData()
    local sandMapCompetition = city_siege_activity_data.GetSandMapCompetitionData()
    if sandCity == nil then
        self:TriggerUIEvent( "ShowEmptyPanel")
        return
    end
    for i,v in pairs(sandMapCompetition) do
        if v.cfg.stagetype <= 3 then --4以上是循环
            local isUnlock = festival_activity_mgr.GetIsOpenByActivityID(i)
            --local isUnlock = v.unlockDay + openServerTime <= currServerTime
            local temp = {}
            temp.actId = v.cfg.ActId
            local activityData = festival_activity_mgr.GetActivityDataByActivityID(temp.actId)
            if v.cfg.KongressAct and v.cfg.KongressAct == 1 and activityData then
                temp.isCapital = true;
                temp.cityID = baseCity[7]
                temp.isLast = true
                temp.data = game_scheme:SandMapCity_0(temp.cityID)--sandCity[v.cfg.citylevel.data[0]][1].data
                local openDay = (temp.data.condition.data[1] - 1) * 60 * 60 * 24 + temp.data.condition.data[2] * 60 * 60 + temp.data.condition.data[3] * 60 + temp.data.condition.data[4]
                local openTime = openDay + openServerTime
                temp.openTime = openTime;
                temp.isOpen = openTime <= currServerTime
                temp.endTime = activityData.endTimeStamp--v.endTime
                temp.attackCity = nil
                if sandCity[7] then
                    for j,k in ipairs(sandCity[7]) do
                        if k.isAggressor then
                            local lastCityRegionCfg = nil
                            for m = 0,game_scheme:SandMapRegion_nums() do
                                local cfg = game_scheme:SandMapRegion(m)
                                if cfg.cityID == temp.cityID then
                                    lastCityRegionCfg = cfg;
                                    break;
                                end
                            end
                            temp.attackCity = {
                                sandCfg = k,
                                cityRegionCfg = lastCityRegionCfg,
                            };
                            break;
                        end
                    end
                end
            else
                temp.isCapital = false;
                for j=0,v.cfg.citylevel.count - 1 do
                    temp.cityID = baseCity[v.cfg.citylevel.data[j]]
                    local tempCfg = game_scheme:SandMapCity_0(temp.cityID)
                    if tempCfg and tempCfg.condition.data[0] == 1 then
                        local openDay = (tempCfg.condition.data[1]-1) * 60 * 60 * 24 + tempCfg.condition.data[2] * 60 * 60 + tempCfg.condition.data[3] * 60 + tempCfg.condition.data[4]
                        local openTime = openDay + openServerTime
                        temp.openTime = openTime
                        temp.attackCity = nil

                        local ncDeclareList = AllianceMgr.GetNCDeclareList()
                        local len = #ncDeclareList
                        if len > 0 then
                            local lastCityRegionCfg = game_scheme:SandMapRegion_0(ncDeclareList[1].regionID)
                            if lastCityRegionCfg then
                                local sandCfg = game_scheme:SandMapCity_0(lastCityRegionCfg.cityID)
                                temp.attackCity = {
                                    sandCfg = sandCfg,
                                    cityRegionCfg = lastCityRegionCfg,
                                }
                            end
                        end
                        if openTime <= currServerTime then--这个城池开放争夺了，则看它的下一个城池是否开放争夺，若未开放则该城池为当前显示的城池
                            temp.isOpen = true
                            if j >= v.cfg.citylevel.count - 1 then --当前最高级城
                                temp.isLast = true
                                temp.data = tempCfg
                                temp.endTime = v.endTime
                            else
                                --若下一级存在，且下一级未开放，则取为当前显示的城池
                                local nextCity = baseCity[v.cfg.citylevel.data[j] + 1]
                                local nextCfg = game_scheme:SandMapCity_0(nextCity)
                                if nextCfg and nextCfg.condition.data[0] == 1 then
                                    local openDay_1 = (nextCfg.condition.data[1]-1) * 60 * 60 * 24 + nextCfg.condition.data[2] * 60 * 60 + nextCfg.condition.data[3] * 60 + nextCfg.condition.data[4]
                                    local openTime_1 = openDay_1 + openServerTime
                                    if openTime_1 > currServerTime then
                                        temp.isLast = false
                                        temp.data = tempCfg
                                        temp.endTime = openTime_1
                                        --temp.nextData = nextCfg
                                        break;
                                    end
                                end
                            end
                        else
                            temp.isOpen = false
                            temp.data = tempCfg
                            temp.endTime = -1 --未开放，所以结束时间无意义
                            break;
                        end
                    end
                end
            end
            temp.isUnlock = isUnlock
            self.CData.pageData[v.cfg.stagetype] = temp;
            self.CData.pageData[v.cfg.stagetype].stagetype = v.cfg.stagetype
            self.CData.pageData[v.cfg.stagetype].allianceList = {}
            --break; --当前最多只有一个活动开放，所以找到之后直接break
            if isUnlock then
                self.CData.unlockPage = v.cfg.stagetype
                self.CData.cutPage = v.cfg.stagetype
            end

        end
    end
    if self.CData.cutPage then
        city_siege_activity_data.OnGetCitySiegeRank(nil,self.CData.pageData[self.CData.cutPage].actId)
        if self.CData.pageData.actId == 2024 then
            city_siege_activity_data.OnGetActivityData()
        else
            self:TriggerUIEvent( "ShowPanel", self.CData.pageData[self.CData.cutPage],self.CData.unlockPage)
        end
    end
end

function UIController:OnSetRankingInfo(msg)
    if msg.actRankType == activity_pb.ACTTYPE_NCRANKING then
        e_handler_mgr.TriggerHandler("ui_city_siege_challenge_main_panel", "SetRankInfo",msg)
    end
end

function UIController:OnSetActivityData(msg)
    if msg.tInfoList then
        self:OnSetCapitalData(msg.tInfoList)
    else
        self:OnShowPanel()
    end
end

function UIController:OnShowPanel()
    self:TriggerUIEvent( "ShowPanel", self.CData.pageData[self.CData.cutPage],self.CData.unlockPage)
end

function UIController:OnSetCapitalData(infoList)
    for i,v in ipairs(infoList) do
        table.insert(self.CData.pageData.allianceList,v)
    end
    table.sort(self.CData.pageData.allianceList,function(a, b)
        return a.nScore >= b.nScore
    end)
    self:TriggerUIEvent( "ShowPanel", self.CData.pageData[self.CData.cutPage],self.CData.unlockPage)
end

function UIController:OnBtnWar_i_locked_objClickedProxy()
    --flow_text.Add(lang.Get(15649))
    self.CData.cutPage = 1
    self:TriggerUIEvent( "ShowPanel", self.CData.pageData[self.CData.cutPage],self.CData.unlockPage)
end

function UIController:OnBtnWar_ii_locked_objClickedProxy()
    --flow_text.Add(lang.Get(15649))
    self.CData.cutPage = 2
    self:TriggerUIEvent( "ShowPanel", self.CData.pageData[self.CData.cutPage],self.CData.unlockPage)
end

function UIController:OnBtnWar_iii_locked_objClickedProxy()
    --flow_text.Add(lang.Get(15649))
    self.CData.cutPage = 3
    self:TriggerUIEvent( "ShowPanel", self.CData.pageData[self.CData.cutPage],self.CData.unlockPage)
end

function  UIController:OnBtnWar_iClickedProxy()
    self.CData.cutPage = 1
    self:TriggerUIEvent( "ShowPanel", self.CData.pageData[self.CData.cutPage],self.CData.unlockPage)
end
function  UIController:OnBtnWar_iiClickedProxy()
    self.CData.cutPage = 2
    self:TriggerUIEvent( "ShowPanel", self.CData.pageData[self.CData.cutPage],self.CData.unlockPage)
end
function  UIController:OnBtnWar_iiiClickedProxy()
    self.CData.cutPage = 3
    self:TriggerUIEvent( "ShowPanel", self.CData.pageData[self.CData.cutPage],self.CData.unlockPage)
end

function UIController:OnBtnWarTipsClickedProxy()
    local data = self.CData.pageData[self.CData.cutPage]
    local lastPos = cfg_util.StringToNumberArray(data.attackCity.cityRegionCfg.CityPos)
    --self.txt_warTipsPos.text = string.format2("X:{%s1} Y:{%s2}",lastPos[1], lastPos[2])
    ui_window_mgr:UnloadModule("ui_festival_activity_center") --退出活动中心
    city_siege_activity_data.EventReport("SandMapCompetition_Jump",{})
    --log.Error(data.attackCity.cityRegionCfg.CityPos)
    gw_common_util.JumpToGrid({ x = lastPos[1], y = lastPos[2] })
end

function  UIController:OnBtnTipsBtnClickedProxy()
    if self.CData.cutPage then
        local StrategyID = game_scheme:Sandmapcompetition_0(self.CData.pageData[self.CData.cutPage].actId).helpID
        local ui_help = require "ui_help"
        ui_help.ShowWithDate(StrategyID)
        --查看城市竞赛活动说明 上报
        city_siege_activity_data.EventReport("SandMapCompetition_HelpCheck", {})
    end
end
function  UIController:OnBtnCityListBtnClickedProxy()
    ui_window_mgr:ShowModule("ui_alliance_city_new")
end
function  UIController:OnBtnTipsClickedProxy()
    if self.CData.cutPage then
        local StrategyID = game_scheme:Sandmapcompetition_0(self.CData.pageData[self.CData.cutPage].actId).StrategyID
        local ui_help = require "ui_help"
        ui_help.ShowWithDate(StrategyID)
        --查看城市竞赛活动说明 上报
        city_siege_activity_data.EventReport("SandMapCompetition_HelpCheck", {})
    end
end

function  UIController:OnBtnGetRewardClickedProxy()
    if self.CData.cutPage then
        ui_window_mgr:ShowModule("ui_city_siege_leaderboard",nil,nil,self.CData.pageData[self.CData.cutPage].actId)
    end
end



--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
