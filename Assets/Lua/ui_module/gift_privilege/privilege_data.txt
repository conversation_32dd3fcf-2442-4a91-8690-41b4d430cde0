-- Created by ta<PERSON><PERSON><PERSON><PERSON>.
--- DateTime: 2024/2/6 14:48
--- Des:特权数据
local require = require
local event = require "event"
local lang = require "lang"
local game_scheme = require "game_scheme"
local log = require "log"
local privilege_define = require "privilege_define"
local ReviewingUtil = require "ReviewingUtil"
local pairs = pairs
module("privilege_data")
---@field SliderType table 购买状态
local privilegeData = {}

--设置特权
--privilegeID:GiftPrivilege索引
--value:特权开启状态
function SetPrivilegeData(privilegeID,value)
    privilegeData[privilegeID] = value
    event.Trigger(privilege_define.PRIVILEGE_DATA_CHANGE)
end

--获取某一特权是否开启
function GetPrivilegeIsOpenByID(privilegeID)
    if ReviewingUtil.IsReviewing() then
        --审核服下开放第四编队
        if privilegeID and privilegeID == privilege_define.MONTH_FOUR_TEAM then
            return true
        end
    end
    return privilegeData[privilegeID]
end

--获取某一特权描述
function GetGetPrivilegeDescyID(privilegeID)
    local privilegeCfg = game_scheme:GiftPrivilege_0(privilegeID)
    local desc = ""
    if privilegeCfg then
        desc = lang.Get(privilegeCfg.PrivilegeLangID)
    else
        log.Error("GetGetPrivilegeDescyID", "cfg is nil",privilegeID)
    end
    return desc
end

function Clear()
    if privilegeData then
        for k,v in pairs(privilegeData) do
            privilegeData[k] = false
        end
    end
    privilegeData = {}
end

function Init()
    local num = game_scheme:GiftPrivilege_nums()
    for i = 0, num-1 do
        local privilegeCfg = game_scheme:GiftPrivilege(i)
        if privilegeCfg then
            privilegeData[privilegeCfg.ID] = false
        end
    end
end

Init()

event.Register(event.USER_DATA_RESET, Clear)




