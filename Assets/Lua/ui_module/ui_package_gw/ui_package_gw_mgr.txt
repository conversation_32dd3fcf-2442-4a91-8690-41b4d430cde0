---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON><PERSON><PERSON>)
--- Created by <PERSON><PERSON><PERSON>.
--- DateTime: 2024/8/27 10:53
---
local log = require "log"
local event = event
local ui_package_gw_data = require "ui_package_gw_data"

local M = {}

---运行即初始化
function M.Init()
    event.Register(event.FIRST_LOGIN_CREATE_ROGUE_ENTITY_FINISH, M.Init_LoginFinish)
   
    ui_package_gw_data.Init()

end

---player 数据初始化完成之后触发事件
function M.Init_LoginFinish()
    --log.Log("ui_package_gw_mgr Init_LoginFinish")
    ui_package_gw_data.Init_LoginFinish()
end



local function ClearUserData()
    event.Unregister(event.FIRST_LOGIN_CREATE_ROGUE_ENTITY_FINISH, M.Init_LoginFinish)
end

event.Register(event.USER_DATA_RESET, ClearUserData)

return M