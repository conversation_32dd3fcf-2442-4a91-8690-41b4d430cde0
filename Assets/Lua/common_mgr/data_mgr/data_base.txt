---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by fangy21120206.
--- DateTime: 2024/5/21 17:30
---
local require = require
local pairs = pairs
local data_enum = require "data_enum"
local e_data_tag = data_enum.e_data_tag
module("data_base")
local M = {}
function M:ctor(tag) 
   self.tag = tag
   self:Init()
end

function M:Init()
    for k,v in pairs(e_data_tag) do
        self[k] = {
            ---服务器下发数据，或服务器解析数据
            net = {},
            ---读表或者定义得固定数据存储
            const = {},
            clear = function(t)
                t.net = {}
                t.const = {}
            end
        }
    end
end

---@see 清理所有数据
function M:ClearAll()
   self:Init()
end

return M