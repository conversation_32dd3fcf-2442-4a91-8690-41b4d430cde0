local log = require "log"

local VirtualKeyboard = CS.VirtualKeyboardArea.VirtualKeyboard
local screen_util = require "screen_util"

local M = {}

function M:GetHeight(includeInput)
    local device_param_util = require "device_param_util"
    local factor = device_param_util.GetResolutionCoefficient()
    -- unity 2017.4, 2018 为 "b", 2020 为 "mSoftInputDialog"，具体视 unity 版本定
    local height,keyboardInfo = VirtualKeyboard.GetHeight(includeInput, "mSoftInputDialog", factor)
    -- log.Warning("screen height:", screen_util.height, ",pixel height:", keyboardInfo.screenHeight, ",visibleDisplayHeight:", keyboardInfo.visibleDisplayHeight,
    --     ",graphicHeight:", keyboardInfo.graphicHeight, ",decorHeight", keyboardInfo.decorHeight, ",error:", keyboardInfo.error)
    return height,keyboardInfo.graphicHeight,factor
end

function M:GetUnityKeyboardHeight(uiRootRectTrans, includeInput)
    local keyboardHeight,graphicHeight,factor = self:GetHeight(includeInput)
    local screenToRectRatio = screen_util.height  / (uiRootRectTrans.rect.height * factor)
    local unityKeyboardHeight = keyboardHeight / screenToRectRatio
    -- log.Warning("screenToRectRatio:", screenToRectRatio, ",unityKeyboardHeight:", unityKeyboardHeight, ",virtual height:", keyboardHeight)
    return unityKeyboardHeight,graphicHeight
end

local isHeightInterfaceValid = nil
function M:IsHeightInterfaceValid()
    if isHeightInterfaceValid ~= nil then
        return isHeightInterfaceValid
    end

    isHeightInterfaceValid = type(VirtualKeyboard.GetHeight) == "function"
    print("IsHeightInterfaceValid:", isHeightInterfaceValid)
    return isHeightInterfaceValid
end

return M