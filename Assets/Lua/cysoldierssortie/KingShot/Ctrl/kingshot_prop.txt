---@class kingshot_prop
local prop = bc_Class("kingshot_prop")
---@type kingshot_scene_mgr
prop.sceneMgr = nil
---@type kingshot_res_mgr
prop.resMgr = nil
---@type fusion_gopool
prop.propPool = nil
---@type kingshot_propunit[]
prop.PropUnitList = nil

function prop:__init(...)
    self.sceneMgr, self.resMgr = ...
    self.propPool = require("fusion_gopool").New(self.sceneMgr.PoolParent, self.resMgr.PrefabPair.PropUnit,
        "kingshot_propunit")
    self.propPool:Preload(5)
    --self.sceneMgr.TimeSecondBind:Register(function(timer)
    --    self:TimeSecondListener(timer)
    --end)
end

function prop:TimeSecondListener(timer)
    for i = 1, #self.sceneMgr.PropDatasByTime, 1 do
        local propData = self.sceneMgr.PropDatasByTime[i]
        if propData.Delay <= timer then
            self:SpawnProp(propData)
            table.remove(self.sceneMgr.PropDatasByTime, i)
            i = i - 1
        end
    end
end

function prop:SpawnTeamByPosCheck()
    for i = 1, #self.sceneMgr.PropDatasByPos, 1 do
        local propData = self.sceneMgr.PropDatasByPos[i]
        if self.sceneMgr.cameraCtrl:CheckInView(propData.Pos) then
            self:SpawnProp(propData)
            table.remove(self.sceneMgr.TeamDatasByPos, i)
            i = i - 1
        end
    end
end

---@param propData kingshot_propData
function prop:SpawnProp(propData)
    local config = self.resMgr:GetPropConfigById(propData.PropID)
    ---@type kingshot_propunit
    local propUnit = self.propPool:PopOne()
    propUnit.transform:SetParent(self.sceneMgr.LevelRoot)
    propUnit:Init(self.sceneMgr, self, propData, config)
    self.PropUnitList[#self.PropUnitList + 1] = propUnit
end


---小队死亡，掉落道具
---@param ids number[] 道具id
function prop:DropProps(oriPos, id)
    local config = self.resMgr:GetPropConfigById(id)
    if config and config.Num then
        self.sceneMgr:ChangeCoin(config.Num)
    end
    self.sceneMgr.uiMgr:SpawnCoinRewardAniTextXYZ(oriPos.x,oriPos.y,oriPos.z,config.Num)
    --Fusion.Error("DropProps")
end

function prop:Reset()
    self.PropUnitList = {}
end

function prop:Update(deltaTime)
    self:SpawnTeamByPosCheck()
end

function prop:__delete()
    
end

return prop
