---@class kingshot_buildingunit : fusion_gopoolitem
local unit = bc_Class("kingshot_buildingunit", require("fusion_gopoolitem"))

-- 导入必要的模块
local log = require "log"
local LookAtTargetSystemInstance = nil

---@type kingshot_scene_mgr
unit.sceneMgr = nil
---@type kingshot_building
unit.Ctrl = nil
unit.DataSrc = nil
---@type kingshot_BuildingConfig
unit.config = nil
---@type kingshot_buildingData
unit.buildingData = nil

---@type kingshot_buildingitem
unit.item = nil

---@type cysoldierssortie_comp_character
unit.character = nil
---@type boolean 是否可以攻击
unit.canAttack = nil

---@type boolean
unit.fireFlag = nil
---@type number
unit.fireTimer = nil
unit.lastTargetGo = nil
unit.lastTargetPos = nil
local focusCD = 0.1

---@type kingshot_BuildingSkillConfig
unit.skillConfig = nil
unit.skillPos = nil

unit.skillTimer = nil
unit.skillReady = nil
unit.skillLoopCount = nil
local emptyArray = {}
function unit:__init(...)
    self:Ctor(...)
    self.DataSrc = {}
    local neeRefer = self.gameObject:GetComponent(KingShot_Define.TypeOf.NeeReferCollection)
    neeRefer:Bind(self.DataSrc)

    -- 初始化LookAtTargetSystemInstance
    local res = xpcall(function()
        LookAtTargetSystemInstance = KingShot_Define.LookAtTargetSystem.Instance
    end, debug.traceback)
end

function unit:EnableCollider(flag)
    self.DataSrc.Collider.enabled = flag
end

function unit:EnableColliderTrigger(flag)
    self.DataSrc.Collider.isTrigger = flag
end

function unit:Init(sceneMgr, ctrl, data, config)
    self.sceneMgr = sceneMgr
    self.Ctrl = ctrl
    self.buildingData = data
    self.config = config
    KingShot_Define.SetTransformPositionXYZ(self.transform, self.buildingData.Pos.x, self.buildingData.Pos.y,
            self.buildingData.Pos.z)
    self.DataSrc.Collider.size = KingShot_Define.CS.Vector3(self.config.ColliderSize[1], self.config.ColliderSize[2], self.config.ColliderSize[3])
    self.DataSrc.Collider.center = KingShot_Define.CS.Vector3(self.config.ColliderCenter[1], self.config.ColliderCenter[2], self.config.ColliderCenter[3])
    self.gameObject:SetActive(true)

    self.item = self.Ctrl:PopOneBuildingItem()
    self.item.transform:SetParent(self.transform)
    KingShot_Define.SetTransformLocalPositionAndLocalRotation(self.item.transform, 0, 0, 0, 0, 0, 0)
    self.item:Init(self.sceneMgr,self.config)

    self:EnableCollider(true)

    self.DataSrc.ColListener:RegisterTriggerEnter(function(other)
        self:OnCollisionEnter(other)
    end)

    self.isDie = false

    if self.config.IsMain or self.config.CoinUse == 0 then
        self:CreateCharacter()
    end

    self:AddNavMeshObstacle(self.transform.gameObject,self.DataSrc.Collider.size,self.DataSrc.Collider.center)
end

function unit:AddNavMeshObstacle(go,size,center)
    if go~=nil then

        local obstacle = go.transform:GetComponent(typeof(CS.UnityEngine.AI.NavMeshObstacle))
        --go可能本身存在组件的 直接add会返回nil
        if obstacle == nil or bc_IsNull(obstacle) then
            obstacle = go:AddComponent(typeof(CS.UnityEngine.AI.NavMeshObstacle))
        end
        if obstacle then
            obstacle.carving = true
            obstacle.carvingMoveThreshold = 0.1
            obstacle.size = size ~= nil and size or KingShot_Define.CacheVector3.One
            obstacle.center = center ~= nil and center or KingShot_Define.CacheVector3.Zero
        end
    end
end

function unit:CreateCharacter()
    if self.character ~= nil then
        return
    end
    self:EnableColliderTrigger(false)
    ---@type kingshot_UnitConfig
    local unitConfig = nil
    if self.config.UnitID then
        unitConfig = self.sceneMgr.resMgr:GetUnitConfigById(self.config.UnitID)
    end

    self.canAttack = true -- 是否可以攻击

    -- 创建建筑的角色实体，用于索敌和攻击
    local actorMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.ActorInstanceMgr)
    if actorMgr then
        -- 创建一个Building类型的角色 (unitId=7)
        self.character = actorMgr:CreateCharacter(
                self.config.UnitID, -- unitId
                { x = 0, y = 0, z = 0 }, -- localPos
                self.transform, -- parent
                true, -- forceView
                nil, -- heroID
                unitConfig.HP, -- hp
                unitConfig.ATK, -- attack
                self, -- player
                false -- playEffect
        )
        self.Ctrl.BuildingUnitWithCharacter[self.character] = self
        --重设character的collider
        self.character._character_entity._collider.size =  self.DataSrc.Collider.size
        UIUtil.SetLocalPos( self.character.transform, -0.6, 0, 0)
    end
    
    --检查是否有技能关联 如果有 则启动技能
    self.skillConfig = self.sceneMgr:GetBuildingSkillConfig(self.buildingData.SkillUid)
    if self.skillConfig~=nil then
        self.skillPos = self.sceneMgr.buildingSkillArr[self.buildingData.SkillUid].Pos
        -- 初始化技能计时器
        self.skillTimer = 0
        self.skillReady = true
        self.skillLoopCount = 0
    end
end

---根据技能id，获取范围内的敌人
function unit:GetTargetObjsByWeaponID(weaponID)
    local skillConfig = self.sceneMgr.resMgr:GetSkillConfigById(weaponID)
    if skillConfig ~= nil and skillConfig.OverrideAtkRange and skillConfig.OverrideAtkRange > 0 then
        return KingShot_Define.Func_GetEnemiesByRange(self:GetCenterVec3(), skillConfig.OverrideAtkRange * 0.01)
    end
    return emptyArray
end

function unit:GetCenterVec3()
    return self.transform.position
end

function unit:ShowWarningEffect()
    if self.config.IsMain then
        self.sceneMgr.uiMgr:ShowWarningEffect(true)
    end
end

function unit:OnCollisionEnter(collider)
    if self.isDie then
        return
    end
    if not self.config.IsMain and self.item:CanUpgrade() then
        self.item:Upgrade()
        self:CreateCharacter()
    end
end

-- 获取建筑碰撞盒顶部位置
function unit:GetColliderTopPosition()
    local collider = self.DataSrc.Collider
    local position = self.transform.position
    local bounds = collider.bounds
    local topPosition = KingShot_Define.CS.Vector3(
        position.x,
        bounds.max.y,
        position.z
    )
    return topPosition
end

function unit:CharacterFire(character)
    
end
-- 发射子弹
function unit:Fire(targetGo)
    self.fireFlag = true
    self.fireTimer = 0
    
    --if not targetGo or not self.canAttack then
    --    return
    --end
end

function unit:UpdateSkill(deltaTime)
    if self.skillConfig == nil then
        return
    end
    -- 更新技能计时器
    if not self.skillReady then
        self.skillTimer = self.skillTimer + deltaTime
        if self.skillTimer >= self.skillConfig.CD then
            self.skillReady = true
            self.skillTimer = 0
        end
    end

    -- 技能准备好了，生成小兵
    if self.skillReady then
        self:SpawnSoldiers()
        self.skillReady = false
        self.skillTimer = 0

        -- 如果不是循环技能，则清除技能配置
        if not self.skillConfig.IsLoop then
            self.skillConfig = nil
        else
            self.skillLoopCount = self.skillLoopCount + 1
        end
       
        if self.skillConfig and self.skillConfig.LoopCount ~= 0 and self.skillLoopCount >= self.skillConfig.LoopCount then
            self.skillConfig = nil
        end
    end
end

-- 新增生成小兵的函数
function unit:SpawnSoldiers()
    if not self.skillConfig or not self.skillPos then
        return
    end

    -- 生成指定数量的小兵
    for i = 1, self.skillConfig.Num do
        -- 在技能位置附近随机一个点，避免小兵堆叠
        local randomOffset = {
            x = (math.random() - 0.5) * 2,
            y = 0,
            z = (math.random() - 0.5) * 2
        }

        local spawnPos = {
            x = self.skillPos.x + randomOffset.x,
            y = self.skillPos.y,
            z = self.skillPos.z + randomOffset.z
        }
        self.sceneMgr.soldierCtrl:SpawnSoldier(self.skillConfig, spawnPos )
    end
    
end

function unit:Update(deltaTime)
    if self.isDie or not self.canAttack or not self.character then
        return
    end
    self:UpdateSkill(deltaTime)

    self.character:UpdateAI()
    self.character:UpdateHp()

    local targetGo = self.character:GetTargetGo()
    local tmpTargetPos = nil
    if self.fireFlag then
        if targetGo ~= nil then
            self.lastTargetGo = targetGo
        end
        if self.lastTargetGo ~= nil then
            self.lastTargetPos = self.lastTargetGo.transform.position
        end
        tmpTargetPos = self.lastTargetPos
        self.fireTimer = self.fireTimer + deltaTime
        if self.fireTimer >= focusCD then
            self.fireFlag = false
            self.lastTargetGo = nil
            self.lastTargetPos = nil
        end
    else
        if targetGo ~= nil then
            tmpTargetPos = targetGo.transform.position
        end
    end
    if tmpTargetPos ~= nil then
        local dir = tmpTargetPos - self.character.transform.position
        dir.y = 0
        if dir.sqrMagnitude > 0.01 then
            self.targetRotate = KingShot_Define.CS.Quaternion.LookRotation(dir.normalized)
        end
    end
    if self.targetRotate ~= nil then
        local oldRot = self.character.transform.rotation
        self.character.transform.rotation = self.targetRotate
    end
end

---单位死亡
---cysoldierssortie_comp_character 调用
function unit:OnHeroDead(character)
    --self.item.gameObject:SetActive(false)
    self:EnableCollider(false)
    --self.Ctrl:PushOneBuildingItem(self.item)
    self.item:Dead()
    if self.config.IsMain then
        self.sceneMgr:GameOver(false)
        self.sceneMgr.uiMgr:ShowWarningEffect(false)
    end

end
---cysoldierssortie_comp_character 调用
function unit:RecycleSoldierPos(localPos, parent)

end
---cysoldierssortie_comp_character 调用
function unit:GetUnitIDByHeroID(heroID, level)
    
end
---cysoldierssortie_comp_character 调用
function unit:CreateAttackRange(unitID, attackRange)
    
end


return unit
