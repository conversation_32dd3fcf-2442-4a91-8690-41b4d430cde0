---@class kingshot_buildinghomeunit : fusion_gopoolitem
local unit = bc_Class("kingshot_buildinghomeunit", require("fusion_gopoolitem"))
KingShot_Helper = require("kingshot_helper")
-- 导入必要的模块
local log = require "log"

---@type kingshot_scene_mgr
unit.sceneMgr = nil
---@type kingshot_building_home
unit.Ctrl = nil
unit.DataSrc = nil
unit.serData = nil

---@type kingshot_buildingitem
unit.item = nil

---@type cysoldierssortie_comp_character
unit.character = nil
---@type boolean 是否可以攻击
unit.canAttack = nil

---@type boolean
unit.fireFlag = nil
---@type number
unit.fireTimer = nil
unit.lastTargetGo = nil
unit.lastTargetPos = nil
local focusCD = 0.1

---@type kingshot_BuildingSkillConfig
unit.skillConfig = nil
unit.skillPos = nil

unit.skillTimer = nil
unit.skillReady = nil
unit.skillLoopCount = nil

-----
local gw_home_grid_data = require "gw_home_grid_data"
local util = require "util"
local gw_home_card_sprite_asset_mgr = require "gw_home_card_sprite_asset_mgr"
local string    = string
local GameObject = CS.UnityEngine.GameObject

function unit:__init(...)
    self:Ctor(...)
    self.DataSrc = {}
    --local neeRefer = self.gameObject:GetComponent(KingShot_Define.TypeOf.NeeReferCollection)
    --neeRefer:Bind(self.DataSrc)
    
end

function unit:Init(sceneMgr, ctrl, data)
    self.sceneMgr = sceneMgr
    self.Ctrl = ctrl
    self.serData = data
end

function unit:OnLoaded(path,componentName,unitID)
    self.path = path
    self.componentName = componentName
    local x, y, z = gw_home_grid_data.GetPosByGridXY(self.serData.x, self.serData.y)
    self.defaultY = GWConst.HomeMapDefaultY
    self.buildingCfg =  KingShot_Helper.GetBuildingCfg(self.serData.nBuildingID, self.serData.nLevel)
    self.buildingTypeCfg = KingShot_Helper.GetBuildingTypeCfg(self.buildingCfg.TypeID)
    self.buildingTypeMiniGameCfg = KingShot_Helper.GetBuildingTypeMiniGameCfg(self.buildingCfg.TypeID)
    self.sortingGroup = UIUtil.GetComponent(self.transform, "SortingGroup")
    if self.buildingTypeMiniGameCfg and self.buildingTypeMiniGameCfg.boxLevel and self.serData.nLevel>=self.buildingTypeMiniGameCfg.boxLevel then
        self.isBoxWorking = true
    end
    self:SetSize()
    self:SetBuildingTextureInfo()
    self:SetPosition(x, y, z)
    self:SetGridPos(self.serData.x, self.serData.y, self.serData.uSid == -1, true)

    if componentName == "gw_home_comp_building_wall_rail" then
        self:SetPosition(0, 0, 0)
        self:CheckWallRail()
    end

    if unitID~=nil and unitID>0 and self.isBoxWorking ==  true then
        self:CreateCharacter(unitID)
    end

    -----如果城墙的box collider不为空
    --if self.boxCollider~=nil then
    --    self:AddNavMeshObstacle()
    --end
end

function unit:AddNavMeshObstacle(go,size,center)
    if go~=nil then

        local obstacle = go.transform:GetComponent(typeof(CS.UnityEngine.AI.NavMeshObstacle))
        --go可能本身存在组件的 直接add会返回nil
        if obstacle == nil or bc_IsNull(obstacle) then
            obstacle = go:AddComponent(typeof(CS.UnityEngine.AI.NavMeshObstacle))
        end
        if obstacle then
            obstacle.carving = true
            obstacle.carvingMoveThreshold = 0.1
            obstacle.size = size ~= nil and size or KingShot_Define.CacheVector3.One
            obstacle.center = center ~= nil and center or KingShot_Define.CacheVector3.Zero
        end
    end
end


function unit:CheckWallRail(targetAreaId)
    local newAreaId = targetAreaId
    if not newAreaId then
        newAreaId = GWG.GWHomeMgr.gridData.GetLastUnlockArea()
    end
    --刷新城墙数据  
    if  newAreaId ~= self.curLastActiveAreaId then
        self.gameObject.name = "homeBuildingWallRail_area_"..newAreaId
        --清理掉原来的，设置当前的
        if self.curLastActiveAreaId ~= nil then
            GWG.GWHomeMgr.gridData.SetGridStateByWallRail(self.curLastActiveAreaId,0)
        end
        GWG.GWHomeMgr.gridData.SetGridStateByWallRail(newAreaId,self.compId)
        --是否还需要重设一下城门的，因为重叠了????        
        self.curLastActiveAreaId = newAreaId

        --如果是直接配置的，将不走设置流程
        if GWConst.WallPrefabHandleConfig then
            return
        end
        --获取当前的城墙信息
        self.outlineSimpleData = GWG.GWHomeMgr.gridData.GetWallOutlineSimpleData(self.curLastActiveAreaId)
        self.outlineAllData = GWG.GWHomeMgr.gridData.GetWallOutlineAllData(self.curLastActiveAreaId)
        self:CreateWallRail()
    end
end
--当前规则： 角落资源： 2*3的大小
---------   城墙资源： 最大2*6   中间态  2*4   最小态 2*2 

--在角落处的偏移 判断当前朝向和上一次的朝向，自动计算一下偏移 0/1/2/3 --东/北/西/南
--角落柱子占地4个格子，以左下角为基准，刚好在角落里摆下整个2*2的格子
--城墙实体的占地偏移
--城墙实体的占地偏移
local sizeWidth = 2
---角落地块的偏移控制 ；注意这里因为角落的2*2 其实和城墙的2*2 有点不一样
local CornerOffset = {

    [22] =
    {
        --同向和逆向的当前不考虑
        [0] = {
            [1] = {x = -1, y = 0},
            [3] = {x = 0, y = 0 },
        },
        [1] = {
            [0] = {x = 0, y = -1},
            [2] = {x = -1, y = -1},
        },
        [2] = {
            [1] = {x = -1, y = -1},
            [3] = {x = 0, y = -1},
        },
        [3] = {
            [0] = {x = 0, y = 0},
            [2] = {x = 0, y = -1},
        },
    },

}

local forwardCornerNeedOffset =
{
    [22] =
    {
        --同向和逆向的当前不考虑
        [0] = {
            [3] =  3,
        },
        [1] = {
            [0] = 4,
        },
        [2] = {
            [1] = 1,
        },
        [3] = {
            [2] = 2,
        },
    },
}
---不同尺寸的墙块的偏移--不包括角落
local forwardOffset = {
    [11] =
    {
        --不偏移
    },
    [22] =
    {
        --同向和逆向的当前不考虑
        [0] = {
            [1] = {x = -1, y = 0},
            [3] = {x = 0, y = -1 },
        },
        [1] = {
            [0] = {x = -1, y = 0},
            [2] = {x = -1, y = -1},
        },
        [2] = {
            [1] = {x = -1, y = -1},
            [3] = {x = 0, y = -1},
        },
        [3] = {
            [0] = {x = 0, y = 0},
            [2] = {x = -1, y = -1},
        },
    },
    --只需要考虑逆时针即可
    [24] =
    {
        --同向和逆向的当前不考虑x = -4, y = 0
        [0] = {
            [1] = {x = -1, y = 0},
            [3] = {x =  0, y = -3 },
        },
        [1] = {
            [0] = {x = 0, y = -1},
            [2] = {x = -3, y = -1},
        },
        [2] = {
            [1] = {x = -1, y = -1},
            [3] = {x = 0, y = -3},
        },
        [3] = {
            [0] = {x = 0, y = 0},
            [2] = {x = -3, y = -1},
        },
    },
    [26] =
    {
        --同向和逆向的当前不考虑x = -6, y = 0
        [0] = {
            [1] = {x = -1, y = 0},
            [3] = {x = 0, y = -5 },
        },
        [1] = {
            [0] = {x = 0, y = -1},
            [2] = {x = -5, y = -1},
        },
        [2] = {
            [1] = {x = -1, y = -1},
            [3] = {x = 0, y = -5},
        },
        [3] = {
            [0] = {x = 0, y = 0},
            [2] = {x = -5, y = -1},
        },
    },

}
local cornerSize = 3 --表示普通的墙角柱占地宽度
local wallSize1 = 6 --表示普通的墙占地长度
local wallSize2 = 4 --表示普通的墙占地长度2    
local minFillSize = 2 --表示最小的补充墙占地长度 不够时用最小的墙来补
local nearGateSize = 2 --靠近城门得城墙占地尺寸

local cornerIconIndex =
{
    [1] = 13,  --表示上一次朝向0 当前次1  =0*10+1 = 1
    [12] = 14,
    [23] = 15,
    [30] = 12,
    [21] = 12,
    [10] = 15,
    [3] = 14,
    [32] = 13,
}
---@public 内外圈长度一样的时候；长度和起始位置偏移了
local InnerAndOuterSameLengthOffset =
{
    [0] = {
        [3] = {x = 0, y = -1 },
    },
    [3] = {
        [2] = {x = -1, y = 0},
    },
}


---@public 内外圈长度一样的时候；长度和起始位置偏移了
local InnerAndOuterSameLengthOffset =
{
    [0] = {
        [3] = {x = 0, y = -1 },
    },
    [3] = {
        [2] = {x = -1, y = 0},
    },
}


---@public 建造城墙
---利用外框数据构造城墙
function unit:CreateWallRail()
    self.icon_root = UIUtil.GetTrans(self.transform, "node/icon_root")

    if  not self.icon_root then
        GWG.GWAdmin.SwitchUtility.Error("建造城墙时 icon_root is nil")
        return
    end
    --获取一下当前城墙区域的endPos
    self.curOutAreaEndPos = nil
    self.curAreaEnterRepairing = false
    local game_scheme = require "game_scheme"
    local wallCfg = game_scheme:BuildingWall_0(self.curLastActiveAreaId)
    if wallCfg and wallCfg.outAreaEndPos then
        if not string.IsNullOrEmpty(wallCfg.outAreaEndPos) then
            local array = string.split(wallCfg.outAreaEndPos, "#")
            if #array == 2 then
                self.curOutAreaEndPos = {x = tonumber(array[1]), y = tonumber(array[2])}
            end
        end
    end
    --先判断一下城墙等级
    self.wallLevel = GWG.GWHomeMgr.buildingData.GetBuildingDataMaxLevel(GWConst.enBuildingType.enBuildingType_Wall*1000)
    --先隐藏所有的
    UIUtil.SetChildrenActive(self.icon_root,false)
    self.originWallIcon = self.icon_root:GetChild(0)
    local innerSimpleData = GWG.GWHomeMgr.gridData.GetWallOutlineSimpleData(self.curLastActiveAreaId,true)
    if self.outlineSimpleData then
        local len  = #self.outlineSimpleData
        self.tempIndex = 0
        for i = 1, len - 1 do
            --先取一条线起点
            local curPointInfo = self.outlineSimpleData[i]
            --判断改点是否已经是该区域的endPos
            if self.curOutAreaEndPos and i > 1  then
                if curPointInfo.x == self.curOutAreaEndPos.x and curPointInfo.y == self.curOutAreaEndPos.y then
                    self.curAreaEnterRepairing = true
                end
            end

            --判断剩余部分能摆下几个墙
            local curLen = curPointInfo.len
            --内外圈长度相同时，这里长度和start位置都要处理
            --需要判断一下是否内圈和外圈相等，相等需要+1
            --先获取内圈的长度
            --注意 这里只有墙的厚度为2的时候才会考虑内圈
            local InnerAndOuterSameLength = false
            if innerSimpleData[i] then
                if curPointInfo.len == innerSimpleData[i].len then
                    curLen = curLen + 1
                    InnerAndOuterSameLength = true
                end
            end
            --处理城门特殊点
            if  curPointInfo.type == 1 then
                --线段接城门
                self:DoWallArea(curPointInfo)
            elseif  curPointInfo.type == 2 then
                --不处理
            elseif curPointInfo.type == 3 then
                --城门下一段
                self:DoWallArea(curPointInfo)
            elseif  curLen < 2*cornerSize  then
                GWG.GWAdmin.SwitchUtility.Warn("围栏长度太短，还不够摆下俩个柱子")
            else

                local startPosX,startPosY = 0,0
                if InnerAndOuterSameLength and InnerAndOuterSameLengthOffset[curPointInfo.preDir] and InnerAndOuterSameLengthOffset[curPointInfo.preDir][curPointInfo.dir] then
                    startPosX = - InnerAndOuterSameLengthOffset[curPointInfo.preDir][curPointInfo.dir].x
                    startPosY = - InnerAndOuterSameLengthOffset[curPointInfo.preDir][curPointInfo.dir].y
                end
                --设置角落  角落的iconIndex 通过前后的方向开始计算  注意角落的偏移按2*2计算      !!!角落这里不计算偏移内外圈交叉的偏移    
                self:SetWallIcon(curPointInfo.x ,curPointInfo.y,-1,false,true,  CornerOffset[22],curPointInfo.preDir,curPointInfo.dir,cornerSize)
                --线起点柱子+1           
                local temp = curLen - 2*cornerSize
                local tempCount = math.floor( temp/wallSize1)
                --判断是否有余数 
                local remind1 = temp - tempCount* wallSize1
                local tempRemindCount1 = math.floor(remind1 / wallSize2)  --第1次结算剩余

                local remind2 = temp - tempCount* wallSize1 - tempRemindCount1*wallSize2 --最后补充时剩余多少
                local tempRemindCount2 = math.floor(remind2 / minFillSize)  --第1次结算剩余


                if curPointInfo.dir == 0 then
                    startPosX = startPosX + curPointInfo.x + cornerSize
                    startPosY = startPosY + curPointInfo.y
                    for j = 0, tempCount -1 do
                        self:SetWallIcon(startPosX + j* wallSize1,startPosY,0,0,false, forwardOffset[sizeWidth*10+wallSize1],curPointInfo.preDir,curPointInfo.dir,wallSize1)
                    end
                    --先铺第二小
                    local readyOffset = (tempCount)* wallSize1
                    for j = 0, tempRemindCount1 -1 do
                        self:SetWallIcon( startPosX + readyOffset + j* wallSize2, startPosY,4,1,false, forwardOffset[sizeWidth*10+wallSize2],curPointInfo.preDir,curPointInfo.dir,wallSize2)
                    end

                    readyOffset = (tempCount)* wallSize1 + tempRemindCount1*wallSize2
                    for j = 0, tempRemindCount2 -1 do
                        self:SetWallIcon( startPosX + readyOffset + j* minFillSize, startPosY,8,1,false, forwardOffset[sizeWidth*10+minFillSize],curPointInfo.preDir,curPointInfo.dir,minFillSize)
                    end
                elseif curPointInfo.dir == 1 then
                    startPosX =  startPosX + curPointInfo.x
                    startPosY = startPosY + curPointInfo.y + cornerSize
                    for j = 0, tempCount -1  do
                        self:SetWallIcon(startPosX,startPosY+ j* wallSize1,1,0, false, forwardOffset[sizeWidth*10+wallSize1],curPointInfo.preDir,curPointInfo.dir,wallSize1)
                    end
                    local readyOffset = (tempCount)* wallSize1
                    for j = 0, tempRemindCount1 -1 do
                        self:SetWallIcon( startPosX,readyOffset + startPosY + j* wallSize2,5,1,false,  forwardOffset[sizeWidth*10+wallSize2],curPointInfo.preDir,curPointInfo.dir,wallSize2)
                    end

                    readyOffset = (tempCount)* wallSize1 + tempRemindCount1*wallSize2
                    for j = 0, tempRemindCount2 -1 do
                        self:SetWallIcon( startPosX,readyOffset + startPosY + j* minFillSize,9,1,false, forwardOffset[sizeWidth*10+minFillSize],curPointInfo.preDir,curPointInfo.dir,minFillSize)
                    end
                elseif curPointInfo.dir == 2 then
                    startPosX = startPosX + curPointInfo.x - cornerSize
                    startPosY = startPosY + curPointInfo.y
                    for j = 0, tempCount -1 do
                        self:SetWallIcon(startPosX - j* wallSize1,startPosY,2,0, false, forwardOffset[sizeWidth*10+wallSize1],curPointInfo.preDir,curPointInfo.dir,wallSize1)
                    end
                    local readyOffset = (tempCount)* wallSize1
                    for j = 0, tempRemindCount1 -1 do
                        self:SetWallIcon( startPosX - readyOffset - j* wallSize2,startPosY,6,1,false,  forwardOffset[sizeWidth*10+wallSize2],curPointInfo.preDir,curPointInfo.dir,wallSize2)
                    end

                    readyOffset = (tempCount)* wallSize1 + tempRemindCount1*wallSize2
                    for j = 0, tempRemindCount2 -1 do
                        self:SetWallIcon( startPosX - readyOffset - j* minFillSize,startPosY,10,1,false, forwardOffset[sizeWidth*10+minFillSize],curPointInfo.preDir,curPointInfo.dir,minFillSize)
                    end
                elseif curPointInfo.dir == 3 then
                    startPosX = startPosX + curPointInfo.x
                    startPosY = startPosY + curPointInfo.y - cornerSize
                    for j = 0, tempCount -1 do
                        self:SetWallIcon(startPosX,startPosY- j* wallSize1,3,0, false, forwardOffset[sizeWidth*10+wallSize1],curPointInfo.preDir,curPointInfo.dir,wallSize1)
                    end

                    local readyOffset = (tempCount)* wallSize1
                    for j = 0, tempRemindCount1 -1 do
                        self:SetWallIcon(startPosX, startPosY -readyOffset - j* wallSize2,7,1,false,  forwardOffset[sizeWidth*10+wallSize2],curPointInfo.preDir,curPointInfo.dir,wallSize2)
                    end
                    readyOffset = (tempCount)* wallSize1 + tempRemindCount1*wallSize2
                    for j = 0, tempRemindCount2 -1 do
                        self:SetWallIcon(startPosX, startPosY -readyOffset - j* minFillSize,11,1,false,  forwardOffset[sizeWidth*10+minFillSize],curPointInfo.preDir,curPointInfo.dir,minFillSize)
                    end
                end
            end
        end
    end
end

--@function 处理城门区域的拼接
--@param 当前线段点
function unit:DoWallArea(curPointInfo)
    if curPointInfo.type ~= 1 and curPointInfo.type ~= 3 then
        return
    end
    local curLen = curPointInfo.len
    local temp = curLen - cornerSize --俩种情况都是只需要计算一个corner
    --城门特殊化，需要一定在城门俩侧加入俩个小块
    temp = temp -  nearGateSize --减去一个小块
    --城门这里需要城门俩侧对称
    local tempCount = math.floor( temp/wallSize1)
    --判断是否有余数 
    local remind1 = temp - tempCount* wallSize1
    local tempRemindCount1 = math.floor(remind1 / wallSize2)  --第1次结算剩余
    local remind2 = temp - tempCount* wallSize1 - tempRemindCount1*wallSize2 --最后补充时剩余多少
    local tempRemindCount2 = math.floor(remind2 / minFillSize)  --第1次结算剩余

    --这里我们明确知道是第一条线，明确是向东方向
    local startPosX,startPosY = curPointInfo.x,curPointInfo.y

    local readyOffset1 = 0
    local readyOffset2 = 0
    local readyOffset3 = 0
    if curPointInfo.type == 1 then
        --必须设置一下corner相关的
        self:SetWallIcon(curPointInfo.x ,curPointInfo.y,-1,false,true,  forwardOffset[22],curPointInfo.preDir,curPointInfo.dir,cornerSize)
        readyOffset1 = cornerSize
        readyOffset2 = cornerSize + tempCount *wallSize1
        readyOffset3 = cornerSize + tempCount *wallSize1 + tempRemindCount1* wallSize2
    elseif curPointInfo.type == 3 then
        readyOffset1 = nearGateSize + tempRemindCount2 *minFillSize + tempRemindCount1* wallSize2
        readyOffset2 = nearGateSize + tempRemindCount2 *minFillSize
        readyOffset3 = nearGateSize
    end
    --
    if curPointInfo.type == 3 then
        self:SetWallIcon( startPosX, startPosY,21,1,false, forwardOffset[sizeWidth*10+minFillSize],curPointInfo.preDir,curPointInfo.dir,minFillSize)
    end
    --替补资源
    for j = 0, tempCount -1 do
        self:SetWallIcon(startPosX + readyOffset1 +  j* wallSize1,startPosY,0,0,false, forwardOffset[sizeWidth*10+wallSize1],curPointInfo.preDir,curPointInfo.dir,wallSize1)
    end
    --小  
    for j = 0, tempRemindCount1 -1 do
        self:SetWallIcon( startPosX + readyOffset2 + j* wallSize2, startPosY,4,1,false, forwardOffset[sizeWidth*10+wallSize2],curPointInfo.preDir,curPointInfo.dir,wallSize2)
    end
    --大
    for j = 0, tempRemindCount2 -1 do
        self:SetWallIcon( startPosX + readyOffset3 + j* minFillSize, startPosY,8,1,false, forwardOffset[sizeWidth*10+minFillSize],curPointInfo.preDir,curPointInfo.dir,minFillSize)
    end
    if curPointInfo.type == 1 then
        self:SetWallIcon( startPosX + readyOffset3 + tempRemindCount2* minFillSize, startPosY,20,1,false, forwardOffset[sizeWidth*10+minFillSize],curPointInfo.preDir,curPointInfo.dir,minFillSize)
    end
end

---@public 设置城墙的图片
---@param index number 图片的索引
---@param iconIndex number 图片的类型  0-3 大图 ；4-7 小图；  8-11补充的最小城墙图  12-15 角落图
---@param iconSizeType number 图片尺寸type  0/1 2格图/1格图
---@param dir number 城墙的朝向
---@param isCorner number 是否是角落
---@param curLength number 当前长度尺寸
---@param curWidth number 当前厚度尺寸
function unit:SetWallIcon(x,z,iconIndex,iconSizeType,isCorner,forwardOffset,preDir,dir,curLength,curWidth)
    if not curWidth then
        curWidth = sizeWidth
    end
    local index =  self.tempIndex
    local count  = self.icon_root.childCount
    local needClone =   index >= count
    local iconTf
    local go
    if needClone then
        go = GameObject.Instantiate(self.originWallIcon, self.icon_root,false).gameObject
        iconTf = go.transform
    else
        go = self.icon_root:GetChild(index).gameObject
        iconTf = self.icon_root:GetChild(index)
    end
    iconTf.name = "Icon_dir" .. dir .."_index"..index
    self.tempIndex =  self.tempIndex + 1
    UIUtil.SetActive(iconTf,true)

    local SpriteRenderer = UIUtil.GetComponent(iconTf,"SpriteRenderer","icon");
    local tempDir = dir
    -- 添加碰撞盒
    local boxCollider = iconTf:GetComponent("BoxCollider")
    if not boxCollider then
        boxCollider = iconTf.gameObject:AddComponent(typeof(CS.UnityEngine.BoxCollider))
    end
    
    -- 设置碰撞盒的大小和中心点
    local centerOffsetX = 0
    local centerOffsetY = 0
    local colliderHeight = 3.0  -- 碰撞盒高度，可以根据需要调整
    
    -- 求图片内部偏移，想放中心  
    if isCorner then
        -- 墙的偏移
        local cornerIndex = preDir*10+dir
        -- 角落要要获取对应的index 
        local smallIndex = -1
        local curNeedOffsetPos = forwardCornerNeedOffset[22]
        if curNeedOffsetPos and curNeedOffsetPos[preDir][dir] then
            smallIndex = curNeedOffsetPos[preDir][dir] + 15; -- 前面已经偏移15了
        end
        if smallIndex ~= -1 then
            iconIndex = smallIndex
        else
            iconIndex = cornerIconIndex[cornerIndex]
        end
        -- 如果角落，则只取角落的中点
        curLength = curWidth
        curWidth = curWidth
        if tempDir == 0 then
            centerOffsetX = curLength/2
            centerOffsetY = curWidth/2
        elseif tempDir == 1 then
            centerOffsetX = curWidth/2
            centerOffsetY = curLength/2
        elseif tempDir == 2 then
            centerOffsetX = curLength/2
            centerOffsetY = curWidth/2
        elseif tempDir == 3 then
            centerOffsetX = curWidth/2
            centerOffsetY = curLength/2
        end
        
        -- 设置角落碰撞盒
        boxCollider.center = KingShot_Define.CS.Vector3(centerOffsetX, colliderHeight/2, centerOffsetY)
        boxCollider.size = KingShot_Define.CS.Vector3(curWidth+1, colliderHeight, curWidth+1) --角落碰撞盒稍微调大一点
    else
        --curWidth = curWidth/2
        if tempDir == 0 then
            centerOffsetX = curLength/2
            centerOffsetY = curWidth/2
            
            -- 设置水平方向碰撞盒
            boxCollider.center = KingShot_Define.CS.Vector3(centerOffsetX, colliderHeight/2, centerOffsetY-1) --每个方向都往外面挪一点
            boxCollider.size = KingShot_Define.CS.Vector3(curLength+1, colliderHeight, curWidth/2) --边碰撞盒尺寸长度调长1点，窄收一半，
        elseif tempDir == 1 then
            centerOffsetX = curWidth/2
            centerOffsetY = curLength/2
            
            -- 设置垂直方向碰撞盒
            boxCollider.center = KingShot_Define.CS.Vector3(centerOffsetX+1, colliderHeight/2, centerOffsetY) --每个方向都往外面挪一点
            boxCollider.size = KingShot_Define.CS.Vector3(curWidth/2, colliderHeight, curLength+1)--边碰撞盒尺寸长度调长1点，窄收一半，
        elseif tempDir == 2 then
            centerOffsetX = curLength/2
            centerOffsetY = curWidth/2
            
            -- 设置水平方向碰撞盒
            boxCollider.center = KingShot_Define.CS.Vector3(centerOffsetX, colliderHeight/2, centerOffsetY+1)--每个方向都往外面挪一点
            boxCollider.size = KingShot_Define.CS.Vector3(curLength+1, colliderHeight, curWidth/2)--边碰撞盒尺寸长度调长1点，窄收一半，
        elseif tempDir == 3 then
            centerOffsetX = curWidth/2
            centerOffsetY = curLength/2
            
            -- 设置垂直方向碰撞盒
            boxCollider.center = KingShot_Define.CS.Vector3(centerOffsetX-1, colliderHeight/2, centerOffsetY)--每个方向都往外面挪一点
            boxCollider.size = KingShot_Define.CS.Vector3(curWidth/2, colliderHeight, curLength+1)--边碰撞盒尺寸长度调长1点，窄收一半，
        end
    end

    ---如果城墙的box collider不为空
    if boxCollider~=nil then
        self:AddNavMeshObstacle(iconTf.gameObject,boxCollider.size,boxCollider.center)
    end
    
    -- 设置碰撞盒的标签和层，以便正确处理碰撞
    --boxCollider.gameObject.tag = "Wall"
    --boxCollider.gameObject.layer = CS.UnityEngine.LayerMask.NameToLayer("Wall")
    
    -- 设置碰撞盒的名称，方便调试
    --boxCollider.name = "WallCollider_" .. index
    
    -- 注意这里柱子角落得偏移不计算 直接取最角落的curWidth平方
    -- 求出全部地块的左下角位置的偏移
    local posX = x
    local posZ = z
    if preDir then
        if forwardOffset and forwardOffset[preDir] and forwardOffset[preDir][dir] then
            posX,posZ = x + forwardOffset[preDir][dir].x,z + forwardOffset[preDir][dir].y
        else
            --GWG.GWAdmin.SwitchUtility.Error("forwardOffset is nil preDir=", preDir," dir=",dir)
        end
    end
    --再算出在围墙砖块的中心
    UIUtil.SetLocalPos(iconTf,posX, 0, posZ)
    UIUtil.SetLocalPos(SpriteRenderer,centerOffsetX, 0, centerOffsetY)
    local switchSprite =  UIUtil.GetSpriteSwitcher(iconTf,"icon");
    --如果是未修复 怎iconIndex +22  因为目前一整套是22张图
    --注意22张图 
    --0-3 四个方向的大图 
    --4-7 4个方向的小图
    --8-11 4个方向的最小图
    --12-15 4个角落图
    --16-19 4个角落小图 --主要用于部分角落需要靠外
    --20-21 2张靠城门的图  修复中时不需要，因为一开始是都未修复的图，修复城门时就变成完整的了
    if self.wallLevel and self.wallLevel == 0 then
        iconIndex = iconIndex + 22
    elseif self.curAreaEnterRepairing then
        iconIndex = iconIndex + 22*2
    end
    UIUtil.SwitchSpriteRenderer(switchSprite, iconIndex)
    --UIUtil.SetActive(switchSprite,iconIndex < 44)
    --设置对应的层级 
    -- if sortingGroup then
    --     local sortingX = x
    --     sortingGroup.sortingOrder = GWG.GWAdmin.HomeCommonUtil.GetCurSortingOrder(sortingX, z)
    -- end  
end

---@public 设置建筑的Size
function unit:SetSize()
    --获取当前建筑的相关配置  
    if not self.buildingTypeCfg.tile then
        return
    end
    local sizeData = GWConst.HomeBuildingSizeCfg[self.buildingTypeCfg.tile]
    if not sizeData then
        return
    end
    self.sizeX = sizeData.x
    self.sizeY = sizeData.y
    self.size = self.buildingTypeCfg.tile
end

---@public 设置建筑贴图表现
---@see  override
function unit:SetBuildingTextureInfo()
    if not self.sizeX then
        return
    end
    --设置底座，当前主要用于展示交互热区大小   
    local tempOffsetX = self.sizeX / 2 - 0.5
    local tempOffsetZ = -(self.sizeY / 2 - 0.5)
    local ground = UIUtil.GetComponent(self.transform, "SpriteRenderer", "node/ground")
    if not util.IsObjNull(ground) then
        UIUtil.SetLocalScale(ground, self.sizeX * 100, self.sizeY * 100, 1)

        UIUtil.SetLocalPos(ground, tempOffsetX, 0, tempOffsetZ)
    end
    --设置boxCollider的id
    self.box_root = UIUtil.GetTrans(self.transform, "node/box")
    self.node = self.transform:Find("node/icon_root/soldierSlot")
    local boxTf = self.box_root:GetChild(0)
    local boxGo = boxTf.gameObject
    if not util.IsObjNull(boxTf) and self.isBoxWorking then
        local box = UIUtil.GetComponent(boxTf, "BoxCollider")
        if not util.IsObjNull(box) then
            box.name = "build_" .. self.serData.nBuildingID ----self.compId
            local checkBoxFun = function()
                local newBoxSize = nil 
                local newBoxCenter = nil 
                if self.buildingTypeMiniGameCfg and self.buildingTypeMiniGameCfg.boxSize then
                    
                    if util.get_len(self.buildingTypeMiniGameCfg.boxSize.data) >= 3 then
                        newBoxSize = { x = self.buildingTypeMiniGameCfg.boxSize.data[0]/10, 
                                y = self.buildingTypeMiniGameCfg.boxSize.data[1]/10,
                                z = self.buildingTypeMiniGameCfg.boxSize.data[2]/10 }
                    end
                end
                if self.buildingTypeMiniGameCfg and self.buildingTypeMiniGameCfg.boxCenter then

                    if util.get_len(self.buildingTypeMiniGameCfg.boxCenter.data) >= 3 then
                        newBoxCenter = { x = self.buildingTypeMiniGameCfg.boxCenter.data[0]/10,
                                       y = self.buildingTypeMiniGameCfg.boxCenter.data[1]/10,
                                       z = self.buildingTypeMiniGameCfg.boxCenter.data[2]/10 }
                    end
                end
                --获取默认的缩放；如果是vector3.one则使用规则；不是则表示重新设置了，按预设上的设置走
                if UIUtil.IsBoxColliderSize(box, 1, 1, 1) then
                    if newBoxSize then
                        UIUtil.SetBoxColliderSize(box, newBoxSize.x, newBoxSize.y, newBoxSize.z)
                    else
                        UIUtil.SetBoxColliderSize(box, self.sizeX, self.sizeY, 0.1)
                    end
                    if newBoxCenter then
                        box.center = newBoxCenter
                    end
                    if self.serData.nBuildingID ~= 16000 then --城门特殊处理
                        self:AddNavMeshObstacle(boxGo,newBoxSize,newBoxCenter)
                    end
                end
            end
            xpcall(checkBoxFun,
                    function()
                        local str = "UIUtil的字段数量=" .. (util.get_len(UIUtil))
                        local index = 1
                        for i, v in pairs(UIUtil) do
                            str = str .. index .. ":" .. tostring(i) .. "_" .. tostring(v ~= nil) .. "\t"
                            index = index + 1
                        end
                        log.Error("checkBoxFun BoxCollider Failed compName = ", self.compName, str)
                    end
            )
            --？？这个需要吗？？
            UIUtil.SetLocalPos(box, tempOffsetX, 0, tempOffsetZ)
        end
    end

    if not self.buildingSpriteAsset then
        self.buildingSpriteAsset = gw_home_card_sprite_asset_mgr.GetOrCreateCardSpriteAsset("gwhomescenebuilding")
    end
    --设置对应的图片
    if not self.childIcon then
        self.icon_root = UIUtil.GetTrans(self.transform, "node/icon_root")
        self.effect_root = UIUtil.GetTrans(self.transform, "node/icon_root/effect_root")
        if self.icon_root.childCount > 0 then
            self.childIcon = UIUtil.GetComponent(self.icon_root:GetChild(0), "SpriteRenderer")
        end
    end
    if not util.IsObjNull(self.childIcon) then
        UIUtil.SetLocalPos(self.childIcon.transform.parent, tempOffsetX, 0, tempOffsetZ)
        if not string.IsNullOrEmpty(self.buildingCfg.scale) and self.buildingCfg.scale ~= 0 then
            UIUtil.SetLocalScale(self.childIcon, self.buildingCfg.scale, self.buildingCfg.scale, self.buildingCfg.scale)
        end
        if self.buildingCfg.pos.count > 0 then
        end
        self:UpdateChildPos(tempOffsetX, 0, tempOffsetZ)
    end
end

---@public 设置一些预制体的偏移
---@see override
function unit:UpdateChildPos(x, y, z)

end

function unit:SetPosition(x, y, z, isWorld)
    if not y then
        y = self.defaultY
    end
    if isWorld then
        UIUtil.SetWorldPos(self.transform, x, y, z)
    else
        UIUtil.SetLocalPos(self.transform, x, y, z)
    end
end

---@public 设置GridPos
---@see  override
function unit:SetGridPos(x, y, notSyncGridState, isOrigin)
    self.curGridX = x
    self.curGridY = y
    self:SetLayer(self.curGridX, self.curGridY)
end

---@public 设置建筑层级
---@see override
function unit:SetLayer(gridX, gridY)
    if gridX == nil or gridY == nil then
        gridX = self.serData.x
        gridY = self.serData.y
    end
    --设置对应的层级
    if not util.IsObjNull(self.sortingGroup) then
        self.sortingGroup.sortingOrder = GWG.GWAdmin.HomeCommonUtil.GetCurSortingOrder(gridX, gridY)
    end
end

---------------------------------------------------------------------------------------
-------------------------------游戏逻辑-------------------------------------------------
---------------------------------------------------------------------------------------
function unit:CreateCharacter(unitId)
    if self.character ~= nil then
        return
    end
    ---@type kingshot_UnitConfig
    local unitConfig = nil
    if unitId then
        unitConfig = self.sceneMgr.resMgr:GetUnitConfigById(unitId)
    end

    self.canAttack = true -- 是否可以攻击

    -- 创建建筑的角色实体，用于索敌和攻击
    local actorMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.ActorInstanceMgr)
    if actorMgr then
        -- 创建一个Building类型的角色 (unitId=7)
        self.character = actorMgr:CreateCharacter(
                unitId, -- unitId
                { x = 0, y = 0, z = 0 }, -- localPos
                self.transform, -- parent
                true, -- forceView
                nil, -- heroID
                unitConfig.HP, -- hp
                unitConfig.ATK, -- attack
                self, -- player
                false -- playEffect
        )
        self.Ctrl.BuildingUnitWithCharacter[self.character] = self
        self.character:SetFreezeRotate(true)
        if self.box_root then
            local boxTf = self.box_root:GetChild(0)
            if not util.IsObjNull(boxTf) and self.isBoxWorking then
                local box = UIUtil.GetComponent(boxTf, "BoxCollider")
                --重新设置character的碰撞体大小
                self.character._character_entity._collider.size = box.size
                local tempOffsetX = self.sizeX / 2 - 0.5
                local tempOffsetZ = -(self.sizeY / 2 - 0.5)
                UIUtil.SetLocalPos( self.character.transform, tempOffsetX, 0, tempOffsetZ)
            end
        end

    end
    
    --检查是否有技能关联 如果有 则启动技能
    --遍历查询 self.sceneMgr.BuildingDatasBySkill 如果里面数据的BuildingID等于我这里的self.buildingCfg.Type，则表示有技能关联
    if self.sceneMgr.BuildingDatasBySkill and #self.sceneMgr.BuildingDatasBySkill>0 then
        for _, v in ipairs(self.sceneMgr.BuildingDatasBySkill) do
            if v.BuildingID == self.buildingCfg.TypeID then
                self.skillConfig = self.sceneMgr.resMgr:GetBuildingSkillConfigById(v.SkillUid)
                self.skillPos = {x = self.transform.position.x, y = self.transform.position.y, z = self.transform.position.z -2}
                -- 初始化技能计时器
                self.skillTimer = 0
                self.skillReady = true
                self.skillLoopCount = 0
            end
        end
    end
end

function unit:ShowWarningEffect()
    if self.buildingTypeCfg.TypeID == 1 then
        self.sceneMgr.uiMgr:ShowWarningEffect(true)
    end
end

function unit:OnCollisionEnter(collider)
    if self.isDie then
        return
    end
    if not self.config.IsMain and self.item:CanUpgrade() then
        self.item:Upgrade()
        self:CreateCharacter()
    end
end

function unit:CharacterFire(character)
    
end

-- 发射子弹
function unit:Fire(targetGo)
    self.fireFlag = true
    self.fireTimer = 0
    
    --if not targetGo or not self.canAttack then
    --    return
    --end
end

function unit:UpdateSkill(deltaTime)
    if self.skillConfig == nil then
        return
    end
    -- 更新技能计时器
    if not self.skillReady then
        self.skillTimer = self.skillTimer + deltaTime
        if self.skillTimer >= self.skillConfig.CD then
            self.skillReady = true
            self.skillTimer = 0
        end
    end

    -- 技能准备好了，生成小兵
    if self.skillReady then
        self:SpawnSoldiers()
        self.skillReady = false
        self.skillTimer = 0

        -- 如果不是循环技能，则清除技能配置
        if not self.skillConfig.IsLoop then
            self.skillConfig = nil
        else
            self.skillLoopCount = self.skillLoopCount + 1
        end
       
        if self.skillConfig and self.skillConfig.LoopCount ~= 0 and self.skillLoopCount >= self.skillConfig.LoopCount then
            self.skillConfig = nil
        end
    end
end

-- 新增生成小兵的函数
function unit:SpawnSoldiers()
    if not self.skillConfig or not self.skillPos then
        return
    end

    -- 生成指定数量的小兵
    for i = 1, self.skillConfig.Num do
        -- 在技能位置附近随机一个点，避免小兵堆叠
        local randomOffset = {
            x = (math.random() - 0.5) * 2,
            y = 0,
            z = (math.random() - 0.5) * 2
        }

        local spawnPos = {
            x = self.skillPos.x + randomOffset.x,
            y = self.skillPos.y,
            z = self.skillPos.z + randomOffset.z
        }
        self.sceneMgr.soldierCtrl:SpawnSoldier(self.skillConfig, spawnPos )
    end
    
end

function unit:Update(deltaTime)
    if self.isDie or not self.canAttack or not self.character then
        return
    end
    self:UpdateSkill(deltaTime)

    self.character:UpdateAI()
    self.character:UpdateHp()

    local targetGo = self.character:GetTargetGo()
    local tmpTargetPos = nil
    if self.fireFlag then
        if targetGo ~= nil then
            self.lastTargetGo = targetGo
        end
        if self.lastTargetGo ~= nil then
            self.lastTargetPos = self.lastTargetGo.transform.position
        end
        tmpTargetPos = self.lastTargetPos
        self.fireTimer = self.fireTimer + deltaTime
        if self.fireTimer >= focusCD then
            self.fireFlag = false
            self.lastTargetGo = nil
            self.lastTargetPos = nil
        end
    else
        if targetGo ~= nil then
            tmpTargetPos = targetGo.transform.position
        end
    end
    --建筑物先不处理旋转
    --if tmpTargetPos ~= nil then
    --    local dir = tmpTargetPos - self.character.transform.position
    --    dir.y = 0
    --    if dir.sqrMagnitude > 0.01 then
    --        self.targetRotate = KingShot_Define.CS.Quaternion.LookRotation(dir.normalized)
    --    end
    --end
    --if self.targetRotate ~= nil then
    --    local oldRot = self.character.transform.rotation
    --    self.character.transform.rotation = self.targetRotate
    --end
end

---单位死亡
---cysoldierssortie_comp_character 调用
function unit:OnHeroDead(character)
    --self.gameObject:SetActive(false)
    if self.box_root then
        self.box_root:SetActive(false)
    end
    if self.node then
        self.node:SetActive(false)
    end
    local icon = UIUtil.GetComponent(self.transform, "SpriteRenderer", "node/icon_root/icon")
    if not util.IsObjNull(icon) then
        --把icon的sprite换成其他图片
        self.buildingSpriteAsset:GetSprite("brokenBuilding", function(sp)
            icon.sprite = sp
        end)
    end
    
    --self.Ctrl:PushOneBuildingItem(self.item)
    if self.buildingTypeCfg.TypeID == 1 then
        self.sceneMgr:GameOver(false)
        self.sceneMgr.uiMgr:ShowWarningEffect(false)
    end

end
---cysoldierssortie_comp_character 调用
function unit:RecycleSoldierPos(localPos, parent)

end
---cysoldierssortie_comp_character 调用
function unit:GetUnitIDByHeroID(heroID, level)
    
end
---cysoldierssortie_comp_character 调用
function unit:CreateAttackRange(unitID, attackRange)
    
end


return unit
