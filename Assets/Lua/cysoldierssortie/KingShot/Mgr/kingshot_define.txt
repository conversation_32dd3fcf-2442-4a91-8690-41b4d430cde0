---@class kingshot_define
local define = {}
local cysoldierssortie_LayerName = cysoldierssortie_LayerName

define.ApiHelper = CS.XLuaUtil.LuaApiHelper
define.event = require "event"
define.game_scheme = require "game_scheme"
define.minigame_mgr = require "minigame_mgr"
define.minigame_buff_mgr = require "minigame_buff_mgr"
define.TweenEase = CS.DG.Tweening.Ease
define.DOTween = CS.DG.Tweening.DOTween
define.TweenLoopType = CS.DG.Tweening.LoopType
define.DOVirtual = CS.DG.Tweening.DOVirtual
define.Physics = CS.UnityEngine.Physics
define.NavMesh = CS.UnityEngine.AI.NavMesh
define.IgnoreCollision = define.Physics.IgnoreCollision
define.LookAtTargetSystem = CS.cysoldierssortie.LookAtTargetSystem

define.CS = {
    GameObject = CS.UnityEngine.GameObject,
    Vector3 = CS.UnityEngine.Vector3,
    Vector2 = CS.UnityEngine.Vector2,
    Quaternion = CS.UnityEngine.Quaternion,
    NeeGame = CS.CasualGame.lib_ChuagnYi.NeeG.NeeGame,
    Color = CS.UnityEngine.Color,
}

define.GetTransformPositionXYZ = define.ApiHelper.GetTransformPositionXYZ
define.SetTransformPositionXYZ = define.ApiHelper.SetTransformPositionXYZ
define.SetTransformPositionAndRotation = define.ApiHelper.SetTransformPositionAndRotation
define.SetTransformLocalPositionAndLocalRotation = define.ApiHelper.SetTransformLocalPositionAndLocalRotation
define.SetTransformLocalScale = define.ApiHelper.SetTransformLocalScale

define.TypeOf = {
    NeeReferCollection = typeof(CS.CasualGame.lib_ChuagnYi.NeeG.NeeReferCollection),
    LuaMono = typeof(CS.CasualGame.lib_ChuagnYi.LuaMono),
    LuaMonoEvent = typeof(CS.CasualGame.lib_ChuagnYi.MonoFunc.LuaMonoEvent),
    UISlider = typeof(CS.UnityEngine.UI.Slider),
    UIText = typeof(CS.UnityEngine.UI.Text),
    UIImage = typeof(CS.UnityEngine.UI.Image),
    BakedPath = typeof(CS.cysoldierssortie.PathTool.BakedPath),
    Canvas = typeof(CS.UnityEngine.Canvas),
    RectTransform = typeof(CS.UnityEngine.RectTransform),
    Animator = typeof(CS.UnityEngine.Animator),
    ParticleSystem = typeof(CS.UnityEngine.ParticleSystem),
    SpriteSwitcher = typeof(CS.War.UI.SpriteSwitcher),
}

define.CacheVector3 = {
    Zero = define.CS.Vector3.zero,
    One = define.CS.Vector3.one,
    Forward = define.CS.Vector3.forward,
    Up = define.CS.Vector3.up,
}

define.CacheVector2 = {
    Zero = define.CS.Vector2.zero,
}

define.CacheQuaternion = {
    Identity = define.CS.Quaternion.identity,
}

define.CacheColor = {
    Black = define.CS.Color.black,
    White = define.CS.Color.white,
    Red = define.CS.Color.red,
}

define.AbPath = {

    NeeGameEntry = "cysoldierssortie/kingshot/prefab/neegameentry_kingshot.prefab",
    MainScene = "cysoldierssortie/kingshot/prefab/mainscene.prefab",
    MainPanel = "cysoldierssortie/kingshot/prefab/mainpanel.prefab",
    HeroAnimator = "cysoldierssortie/kingshot/art/heroanimation/heroanimator.controller",
    LevelJsonFormat = "cysoldierssortie/kingshot/data/level/level%d.data",
    MapPrefabFormat = "cysoldierssortie/kingshot/prefab/maps/%s.prefab",
    NavMeshFormat = "cysoldierssortie/kingshot/prefab/maps/%s.asset",
    DataPair = "cysoldierssortie/kingshot/prefab/datapair.prefab",
    PrefabPair = "cysoldierssortie/kingshot/prefab/prefabpair.prefab",
    PropPrefab = "cysoldierssortie/kingshot/prefab/prop.prefab",
    PathPrefabFormat = "cysoldierssortie/kingshot/prefab/path/bakepath%s.prefab", --导航路径
    LangPath = "cysoldierssortie/kingshot/tablecsv/lang.csv",
    AttackRangeIndicator = "cysoldierssortie/kingshot/prefab/attackrangeindicator.prefab", --攻击范围指示器
    FloatingIcon = "cysoldierssortie/kingshot/prefab/floatingicon.prefab", --敌方出生点浮空图标
    HeroCircle = "cysoldierssortie/kingshot/prefab/herocircle.prefab", --英雄脚下圆圈
    HeroCircleMat1 = "cysoldierssortie/kingshot/material/herocircle1.mat", --英雄圆圈材质1
    HeroCircleMat2 = "cysoldierssortie/kingshot/material/herocircle2.mat", --英雄圆圈材质2
    HeroCircleMat3 = "cysoldierssortie/kingshot/material/herocircle3.mat", --英雄圆圈材质3


    EffectMonsterBase = "art/effects/effects/effect_chushengdian_kingshot/prefabs/effect_chushengdian_kingshot.prefab",
}

---@class kingshot_TeamSpawnType
define.TeamSpawnType = {
    Pos = "Pos",     --固定位置刷出，不受时间影响
    Timer = "Timer", --跟随关卡时间刷出
    Skill = "Skill", --跟随关卡时间刷出
}

define.HeroCircleType = {
    Soldier = 1, --士兵
    Monster = 2, --怪物
    Player = 3, --玩家
   
}

define.HeroSelectType = {
    Single  = 0, --单英雄
    MultiInside = 1, --多英雄 在游戏里
    MultiOutside = 2, --多英雄在游戏外

}

---@class kingshot_LevelPassType
define.LevelPassType = {
    AllEnemy = 1, --消灭所有敌人
    BossOnly = 2, --只消灭boss
}

define.Params = {
    PlayerRadius = 0.4,         --玩家单位半径
    PlayerMoveSpeed = 4,       --玩家小队移动速度
    PlayerSearchRange = 6,    --每个玩家单位索敌范围
    EnemySearchRange = 100000, -- 每个敌人单位索敌范围
    PlayerAtkRange = 3,       --玩家单位攻击范围
    MaxViewActor = math.huge,  --可显示的最大敌人数量
    
    HeroAnim_RunAttack = "Run_Skill01_Loop", --走A动画
    -- 攻击范围指示器参数
    AttackRangeIndicator = {
        DefaultColor = { r = 1, g = 1, b = 1, a = 0.3 }, --默认颜色（白色，60%透明度）
        LineThickness = 0.1,     --线条粗细
        DashLength = 0.5,        --虚线段长度
        DashSpacing = 0.3,       --虚线间隔
        SortingOrder = -3,     --渲染层级（较低优先级）
        HeightOffset = 0.1,      --距离地面高度
    },

    -- 浮空图标参数
    FloatingIcon = {
        DefaultColor = { r = 1, g = 1, b = 1, a = 1.0 }, --默认颜色（白色，不透明）
        BaseHeight = 3.0,        --基础高度（相对于出生点的Y轴偏移）
        FloatAmplitude = 0.5,    --浮动幅度
        FloatSpeed = 1.0,        --浮动速度（秒）
        SortingOrder = 1000,     --渲染层级（最高优先级）
        CanvasSize = 2.0,        --Canvas大小
    }
}

function define.Func_GetDistance(x1, z1, x2, z2)
    local num = x1 - x2
    local num2 = z1 - z2
    return math.sqrt(num * num + num2 * num2)
end


local typeof_ScriptConnector = nil
local typeof_GpuAnimate = nil
function define.GetGpuAnimate(gameObject)
    local fboard_mgr = require("fboard_mgr")
    if fboard_mgr.IsOpen() then
        if typeof_ScriptConnector == nil then
            typeof_ScriptConnector = typeof(CS.FBoard.ScriptConnector)
        end
        local connector = gameObject:GetComponent(typeof_ScriptConnector)
        if not util.IsObjNull(connector) then
            return connector.LogicObj
        end
        return nil
    else
        if typeof_GpuAnimate == nil then
            typeof_GpuAnimate = typeof(CS.GPUAnimationBaker.Engine.GpuAnimatorBehaviour)
        end
        local animator = gameObject:GetComponent(typeof_GpuAnimate)
        return animator
    end
end

--- 获取一个圆形范围内的所有敌人
---@param range number 范围m
function define.Func_GetEnemiesByRange(oriPos, range)
    local targetGos = {  }
    local hitInfos = define.Physics.SphereCastAll(oriPos, range, define.CacheVector3.Up, 0.1,
            2 ^ cysoldierssortie_LayerName.Enemy)
    if hitInfos ~= nil and hitInfos.Length > 0 then
        local tmpLen = hitInfos.Length - 1
        local tmpIndex = 0
        for i = 0, tmpLen do
            targetGos[i + 1] = hitInfos[i].collider.gameObject
        end
    end
    return targetGos
end

define.bullet_collider_size_set =
{
    ["art/effects/effects/effect_mini_atk_02/prefabs/effect_mini_atk_02.prefab"] =
    {
        pos = {x=0,y=0,z=3},
        center = {x=0,y=0,z=-2},
        size = {x=0.5,y=0.5,z=2.5}
    },
    ["art/effects/effects/effect_feierrui02_mini_attack/prefabs/effect_feierrui02_mini_attack.prefab"] =
    {
        pos = {x=0,y=1,z=0.7},
        center = {x=0,y=0,z=0.7},
        size = {x=1.4,y=0.7,z=1.4}
    },
    ["art/effects/effects/effect_mini_atk_01/prefabs/effect_mini_atk_01.prefab"] =
    {
        pos = {x=0,y=0,z=1.12},
        center = {x=0,y=0,z=1.5},
        size = {x=0.1,y=0.1,z=1}
    },
}

return define
