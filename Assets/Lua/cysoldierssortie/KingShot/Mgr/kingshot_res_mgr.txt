---@class kingshot_res_mgr : fusion_mgrbase
---@field lifeScope kingshot_lifescope
local mgr = bc_Class("kingshot_res_mgr", Fusion.MgrBase)
---@type fusion_load_mgr
mgr.loadMgr = nil
---@type fusion_taskscheduling_mgr
mgr.taskMgr = nil
mgr.NeeGameEntryPrefab = nil
mgr.MainScenePrefab = nil
mgr.ScenePrefab = nil
mgr.SceneNavMesh = nil
mgr.MainPanelPrefab = nil

mgr.HeroAnimator = nil
mgr.PrefabPair = nil

mgr.PathPrefabArr = nil

mgr.AttackRangeIndicator = nil
mgr.HelpBubbleHud = nil
mgr.FloatingIconPrefab = nil
mgr.HeroCirclePrefab = nil
mgr.HeroCircleMat1 = nil
mgr.HeroCircleMat2 = nil
mgr.HeroCircleMat3 = nil
mgr.monsterBaseEffect = nil

---@type kingshot_TeamConfig[]
mgr.TeamConfigs = nil
---@type kingshot_PropConfig[]
mgr.PropConfigs = nil
---@type kingshot_BuildingConfig[]
mgr.BuildingConfigs = nil
---@type kingshot_BuildingSkillConfig[]
mgr.BuildingSkillConfigs = nil
---@type kingshot_LevelConfig[]
mgr.LevelConfigs = nil
---@type kingshot_LevelConfig
mgr.CurLvConfig = nil
---@type table 关卡内容配置
mgr.LevelData = nil
---@type kingshot_UnitConfig[] 角色所有配置
mgr.UnitConfigs = nil
---@type kingshot_SkillConfig[] 技能配置
mgr.SkillConfigs = nil
---@type kingshot_PassiveSkillConfig[] 被动技能配置
mgr.PassiveSkillConfigs = nil
mgr.langTA = nil  --多语言配置

local dataSplitChar = "#"
function mgr:Init(lifeScope)
    self.lifeScope = lifeScope
    self.loadMgr = self.lifeScope:GetMgr("fusion_load_mgr")
    self.taskMgr = self.lifeScope:GetMgr("fusion_taskscheduling_mgr")
end

function mgr:LoadAssets(callBack)
    --装填加载任务
    --加3帧空帧
    local emptyFrame = function() end
    self.loadMgr:AddFrameTask(emptyFrame)
    self.loadMgr:AddFrameTask(emptyFrame)
    self.loadMgr:AddFrameTask(emptyFrame)
    self.loadMgr:AddLoadTask(KingShot_Define.AbPath.NeeGameEntry, function(obj)
        self.NeeGameEntryPrefab = obj
    end)
    self.loadMgr:AddLoadTask(KingShot_Define.AbPath.DataPair, function(obj)
        local dataSrc = {}
        local neeRefer = obj:GetComponent(KingShot_Define.TypeOf.NeeReferCollection)
        neeRefer:Bind(dataSrc)
        self:LoadLevelConfig(dataSrc.KingShotLevel)
        self:LoadTeamConfig(dataSrc.KingShotTeam)
        self:LoadPropConfig(dataSrc.KingShotProp)
        self:LoadBuildingConfig(dataSrc.KingShotBuilding)
        self:LoadBuildingSkillConfig(dataSrc.KingShotBuildingSkill)
        self:LoadUnitConfig(dataSrc.KingShotUnit)
        self:LoadSkillConfig(dataSrc.KingShotSkill)
        self:LoadPassiveSkillConfig(dataSrc.KingShotPassiveSkill)
    end)
    self.loadMgr:AddLoadTask(KingShot_Define.AbPath.MainScene, function(obj)
        self.MainScenePrefab = obj
    end)
    self.loadMgr:AddLoadTask(KingShot_Define.AbPath.MainPanel, function(obj)
        self.MainPanelPrefab = obj
    end)
    self.loadMgr:AddLoadTask(KingShot_Define.AbPath.HeroAnimator, function(obj)
        self.HeroAnimator = obj
    end)
    self.loadMgr:AddLoadTask(KingShot_Define.AbPath.PrefabPair, function(obj)
        self.PrefabPair = {}
        local neeRefer = obj:GetComponent(KingShot_Define.TypeOf.NeeReferCollection)
        neeRefer:Bind(self.PrefabPair)
    end)
    self.loadMgr:AddLoadTask(KingShot_Define.AbPath.LangPath, function(obj)
        self.langTA = obj
    end)
    self.loadMgr:AddLoadTask(KingShot_Define.AbPath.FloatingIcon, function(obj)
        self.FloatingIconPrefab = obj
    end)
    self.loadMgr:AddLoadTask(KingShot_Define.AbPath.AttackRangeIndicator, function(obj)
        self.AttackRangeIndicator = obj
    end)
    self.loadMgr:AddLoadTask(KingShot_Define.AbPath.HelpBubbleHud, function(obj)
        self.HelpBubbleHud = obj
    end)
    self.loadMgr:AddLoadTask(KingShot_Define.AbPath.HeroCircle, function(obj)
        self.HeroCirclePrefab = obj
    end)
    self.loadMgr:AddLoadTask(KingShot_Define.AbPath.HeroCircleMat1, function(obj)
        self.HeroCircleMat1 = obj
    end)
    self.loadMgr:AddLoadTask(KingShot_Define.AbPath.HeroCircleMat2, function(obj)
        self.HeroCircleMat2 = obj
    end)
    self.loadMgr:AddLoadTask(KingShot_Define.AbPath.HeroCircleMat3, function(obj)
        self.HeroCircleMat3 = obj
    end)
    self.loadMgr:AddLoadTask(KingShot_Define.AbPath.EffectMonsterBase, function(obj)
        self.monsterBaseEffect = obj
    end)
    --装载完成回调
    self.loadMgr:AddLoadedCall(function()
        self.CurLvConfig = self.LevelConfigs[self.lifeScope.miniLvConfig.MiniLevelID]
        --间隔一帧再继续加载
        self.taskMgr:AddTask(function()
            self.loadMgr:ClearTask()
            if #self.CurLvConfig.PreloadPath > 0  then
                self.PathPrefabArr = {}
                for _, path in pairs(self.CurLvConfig.PreloadPath) do
                    self.loadMgr:AddLoadTask(string.format(KingShot_Define.AbPath.PathPrefabFormat, path), function(obj)
                        self.PathPrefabArr[path] = obj
                        end)
                end
            end
            self.loadMgr:AddLoadTask(string.format(KingShot_Define.AbPath.MapPrefabFormat, self.CurLvConfig.Map),
                function(obj)
                    self.ScenePrefab = obj
                end)
            self.loadMgr:AddLoadTask(string.format(KingShot_Define.AbPath.NavMeshFormat, self.CurLvConfig.Map),
                function(obj)
                    self.SceneNavMesh = obj
                end)
            self.loadMgr:AddLoadTask(string.format(KingShot_Define.AbPath.LevelJsonFormat, self.CurLvConfig.LevelId),
                function(obj)
                    self.LevelData = require("dkjson").decode(obj.text)
                end)
            self.loadMgr:AddLoadedCall(callBack)
            self.loadMgr:LoadingInvoke()
        end)
    end)
    --开始执行加载任务
    self.loadMgr:LoadingInvoke()
end

---通过buildingid获得对应的prefab
function mgr:GetBuildingPrefabById(buildingID)
    return self.BuildingPrefabAll[buildingID]
end

---@return kingshot_TeamConfig
function mgr:GetTeamConfigById(teamId)
    return self.TeamConfigs[teamId]
end

function mgr:GetPropConfigById(propId)
    return self.PropConfigs[propId]
end

function mgr:GetBuildingConfigById(id)
    return self.BuildingConfigs[id]
end

--通过技能ID获得建筑技能config
function mgr:GetBuildingSkillConfigById(id)
    return self.BuildingSkillConfigs[id]
end

function mgr:GetUnitConfigById(id)
    return self.UnitConfigs[id]
end

function mgr:GetSkillConfigById(id)
    return self.SkillConfigs[id]
end

function mgr:GetPassiveSkillConfigById(id)
    return self.PassiveSkillConfigs[id]
end

function mgr.loadCSVByString(data, headLine, maxCol)
    local tmpMaxCol = maxCol or 9999
    -- 按行划分
    local lineStr = string.split(data, '\r\n')
    local allLine = {}
    local lineStrLength = #lineStr
    for i = headLine + 1, lineStrLength, 1 do
        if string.IsNullOrEmpty(string.trim(lineStr[i])) then
            break
        end
        -- 一行中，每一列的内容
        local content = string.split(lineStr[i], ",")
        local tmpRow = i - headLine
        if maxCol == nil then
            allLine[tmpRow] = content
        else
            local col = math.min(tmpMaxCol, #content)
            allLine[tmpRow] = {}
            for j = 1, col, 1 do
                allLine[tmpRow][j] = content[j]
            end
        end
    end
    return allLine
end

function mgr:LoadLevelConfig(ta)
    local allLines = self.loadCSVByString(ta.text, 1)
    self.LevelConfigs = {}
    local splitType = ";"
    for _, line in ipairs(allLines) do
        local miniLvId = tonumber(line[1])
        ---@class kingshot_LevelConfig
        local data = {
            MiniLevelId = miniLvId,
            LevelId = tonumber(line[2]),
            Map = line[3],
            ---@type troopclash_LevelPassType
            PassType = tonumber(line[4]),
            PassNum = tonumber(line[5]), --通关所需数量
            PreloadPath = {}, --预加载路径
            HidePath = {}, --隐藏路径
            FogArea = {}, --迷雾打开区域
        }
        if line[6]~=nil then
            local splitStrs = string.split(line[6], splitType)
            if #splitStrs > 0 then
                data.PreloadPath = splitStrs
            end
        end
        if line[7]~=nil then
            local splitStrs = string.split(line[7], splitType)
            if #splitStrs > 0 then
                data.HidePath = splitStrs
            end
        end
        if line[8]~=nil then
            local splitStrs = string.split(line[8], splitType)
            if #splitStrs>0 then
                for i, v in ipairs(splitStrs) do
                    splitStrs[i] = tonumber(v)
                end
                data.FogArea = splitStrs
            end
        end
        self.LevelConfigs[miniLvId] = data
    end
end

function mgr:CreateDataTable()
    -- 创建一个表并设置元表，重载__len元方法
    local newTable = setmetatable({}, {
        __len = function(t)
            -- 计算表中所有键值对的数量（包括数组和哈希部分）
            local count = 0
            for _ in pairs(t) do
                count = count + 1
            end
            return count
        end
    })
    return newTable
end

function mgr:LoadTeamConfig(ta)
    local splitType = ";"
    local splitNum = "_"
    local allLines = self.loadCSVByString(ta.text, 1)
    self.TeamConfigs = {}
    for _, line in ipairs(allLines) do
        local id = tonumber(line[1])
        ---@class kingshot_TeamConfig
        local data = {
            Id = id,                         --小队id
            Format = tonumber(line[2]),      -- 阵型
            Radius = tonumber(line[3]),      -- 队伍半径
            MoveSpeed = tonumber(line[4]),   -- 队伍移速
            SearchRange = tonumber(line[5]), -- 队伍索敌范围
            DropId = tonumber(line[7]),      -- 掉落配置
            IsBoss = tonumber(line[8]),      -- 是否BOSS
            ---@type kingshot_TeamUnitData[]
            UnitDatas = {},
            ---@type number 单位总数量
            UnitCount = nil,
        }
        local splitStrs = string.split(line[6], splitType)
        if #splitStrs > 0 then
            local index = 0
            data.UnitCount = 0
            for _, typeStr in ipairs(splitStrs) do
                local numStrs = string.split(typeStr, splitNum)
                if #numStrs > 1 then
                    index = index + 1
                    ---@class kingshot_TeamUnitData
                    local tmpUnitData = {
                        UnitId = tonumber(numStrs[1]),
                        Count = tonumber(numStrs[2]),
                    }
                    data.UnitCount = data.UnitCount + tmpUnitData.Count
                    data.UnitDatas[index] = tmpUnitData
                end
            end
        end
        self.TeamConfigs[id] = data
    end
end

function mgr:LoadPropConfig(ta)
    local allLines = self.loadCSVByString(ta.text, 1)
    self.PropConfigs = {}
    for _, line in ipairs(allLines) do
        local id = tonumber(line[1])
        ---@class kingshot_PropConfig
        local data = {
            Id = id, --道具id
            ---@type kingshot_PropType
            Type = tonumber(line[2]),                                           --道具类型
            Num = tonumber(line[3]),                                          --道具数量
        }
        self.PropConfigs[id] = data
    end
end
function mgr:LoadBuildingSkillConfig(ta)
    local allLines = self.loadCSVByString(ta.text, 1)
    self.BuildingSkillConfigs = {}
    for _, line in ipairs(allLines) do
        local id = tonumber(line[1])
        ---@class kingshot_BuildingSkillConfig
        local data = {
            ID = id, --技能id
            CD = tonumber(line[2])/1000, --cd 转成秒
            Num = tonumber(line[3]), --出兵数量
            SoldierID = tonumber(line[4]), --士兵id
            IsLoop = tonumber(line[5]) == 1, --是否循环
            LoopCount = tonumber(line[6]), --循环次数
        }
        self.BuildingSkillConfigs[id] = data
    end
end
function mgr:LoadBuildingConfig(ta)
    local splitType = ";"
    local allLines = self.loadCSVByString(ta.text, 1)
    self.BuildingConfigs = {}
    for _, line in ipairs(allLines) do
        local id = tonumber(line[1])
        ---@class kingshot_BuildingConfig
        local data = {
            BuildingID = id, --建筑id
            IsMain = tonumber(line[2]) == 1, --是否是基地
            CanAttack = tonumber(line[3]) == 1, --是否可以攻击
            CoinUse = tonumber(line[4]), --消耗金币数量
            ScaleSize = {  }, --缩放大小
            UIScaleSize = {  }, --缩放大小
            ColliderSize = { }, --碰撞体大小
            ColliderCenter = { }, --碰撞体中心
            UnitID = tonumber(line[9]), -- 关联unit单位
        }
        local splitStrs = string.split(line[5], splitType)
        if #splitStrs > 0 then
            --把splitStrs里每个元素转成数字，再除以100
            for i, v in ipairs(splitStrs) do
                splitStrs[i] = tonumber(v) / 100
            end
            data.ScaleSize = splitStrs
        end
        local splitStrs2 = string.split(line[6], splitType)
        if #splitStrs2 > 0 then
            for i, v in ipairs(splitStrs2) do
                splitStrs2[i] = tonumber(v) / 100
            end
            data.UIScaleSize = splitStrs2
        end
        local splitStrs3 = string.split(line[7], splitType)
        if #splitStrs3 > 0 then
            for i, v in ipairs(splitStrs3) do
                splitStrs3[i] = tonumber(v) / 100
            end
            data.ColliderSize = splitStrs3
        end
        local splitStrs4 = string.split(line[8], splitType)
        if #splitStrs4 > 0 then
            for i, v in ipairs(splitStrs4) do
                splitStrs4[i] = tonumber(v) / 100
            end
            data.ColliderCenter = splitStrs4
        end
        self.BuildingConfigs[id] = data
    end
end

function mgr:LoadUnitConfig(ta)
    local allLines = self.loadCSVByString(ta.text, 8)
    self.UnitConfigs = {}
    for _, line in ipairs(allLines) do
        local ID = tonumber(line[1])
        ---@class kingshot_UnitConfig
        local data = {
            ID = ID,
            UnitType = string.IsNullOrEmpty(line[3]) and 0 or tonumber(line[3]),
            UnitLevel = string.IsNullOrEmpty(line[4]) and 0 or tonumber(line[4]),
            RandomWeight = string.IsNullOrEmpty(line[5]) and 0 or tonumber(line[5]),
            ModelID = string.IsNullOrEmpty(line[6]) and 0 or tonumber(line[6]),
            HP = string.IsNullOrEmpty(line[7]) and 0 or tonumber(line[7]),
            ATK = string.IsNullOrEmpty(line[8]) and 0 or tonumber(line[8]),
            AtkRange = string.IsNullOrEmpty(line[9]) and 0 or tonumber(line[9]),
            MoveSpeed = string.IsNullOrEmpty(line[10]) and 0 or tonumber(line[10]),
            EnemyRange = string.IsNullOrEmpty(line[11]) and 0 or tonumber(line[11]),
            SkillID = nil,
            PathfindingRange = string.IsNullOrEmpty(line[13]) and 0 or tonumber(line[13]),
            Scale = string.IsNullOrEmpty(line[14]) and 0 or tonumber(line[14]),
            BloodOffset = string.IsNullOrEmpty(line[15]) and 0 or tonumber(line[15]),
            HitDeceleration = string.IsNullOrEmpty(line[16]) and 0 or tonumber(line[16]),
            coin = string.IsNullOrEmpty(line[17]) and 0 or tonumber(line[17]),
            PassiveSkills = nil,
            DeadSound = line[19],
            DroneOffsetX = string.IsNullOrEmpty(line[20]) and 0 or tonumber(line[20]),
            DroneOffsetY = string.IsNullOrEmpty(line[21]) and 0 or tonumber(line[21]),
            DroneScale = string.IsNullOrEmpty(line[22]) and 0 or tonumber(line[22]),
            TeamHpScale = string.IsNullOrEmpty(line[23]) and 100 or tonumber(line[23]),
        }
        if line[12] ~= nil then
            local splitStrs = string.split(line[12], dataSplitChar)
            data.SkillID = { data = self:CreateDataTable() }
            for i, str in ipairs(splitStrs) do
                data.SkillID.data[i - 1] = tonumber(str)
            end
        end
        if line[18] ~= nil then
            local splitStrs = string.split(line[18], dataSplitChar)
            data.PassiveSkills = { data = self:CreateDataTable() }
            for i, str in ipairs(splitStrs) do
                data.PassiveSkills.data[i - 1] = tonumber(str)
            end
        end
        self.UnitConfigs[ID] = data
    end
end

function mgr:LoadSkillConfig(ta)
    local allLines = self.loadCSVByString(ta.text, 7)
    self.SkillConfigs = {}
    for _, line in ipairs(allLines) do
        local ID = tonumber(line[1])
        ---@class kingshot_SkillConfig
        local data = {
            SkillID = ID,
            iIconID = line[3],
            IsUltra = string.IsNullOrEmpty(line[4]) and 0 or tonumber(line[4]),
            AttackType = string.IsNullOrEmpty(line[5]) and 0 or tonumber(line[5]),
            TypeParameter1 = string.IsNullOrEmpty(line[6]) and 0 or tonumber(line[6]),
            TypeParameter2 = string.IsNullOrEmpty(line[7]) and 0 or tonumber(line[7]),
            DamageCoefficient = string.IsNullOrEmpty(line[8]) and 0 or tonumber(line[8]),
            AttackSpeed = string.IsNullOrEmpty(line[9]) and 0 or tonumber(line[9]),
            SkillLoop = string.IsNullOrEmpty(line[10]) and 0 or tonumber(line[10]),
            SkillPriority = string.IsNullOrEmpty(line[11]) and 0 or tonumber(line[11]),
            strAttackAction = string.IsNullOrEmpty(line[12]) and 0 or tonumber(line[12]),
            OverrideAtkRange = string.IsNullOrEmpty(line[13]) and 0 or tonumber(line[13]),
            TargetType = string.IsNullOrEmpty(line[14]) and 0 or tonumber(line[14]),
            locktype = string.IsNullOrEmpty(line[15]) and 0 or tonumber(line[15]),
            nSkillRepeatCnt = string.IsNullOrEmpty(line[16]) and 0 or tonumber(line[16]),
            nTriggerProb = string.IsNullOrEmpty(line[17]) and 0 or tonumber(line[17]),
            RepeatMinInterval = string.IsNullOrEmpty(line[18]) and 0 or tonumber(line[18]),
            CriticalHit = string.IsNullOrEmpty(line[19]) and 0 or tonumber(line[19]),
            lockRange = string.IsNullOrEmpty(line[20]) and 0 or tonumber(line[20]),
            BallisticVelocity = string.IsNullOrEmpty(line[21]) and 0 or tonumber(line[21]),
            BallisticRange = string.IsNullOrEmpty(line[22]) and 0 or tonumber(line[22]),
            pierce = string.IsNullOrEmpty(line[23]) and 0 or tonumber(line[23]),
            MaxDamageNumber = string.IsNullOrEmpty(line[24]) and 0 or tonumber(line[24]),
            startCD = string.IsNullOrEmpty(line[25]) and 0 or tonumber(line[25]),
            nBuffIDs = nil,
            AddTargetType = string.IsNullOrEmpty(line[27]) and 0 or tonumber(line[27]),
            SpecialEffectScaling = string.IsNullOrEmpty(line[28]) and 0 or tonumber(line[28]),
            SpecialEffectsPath = line[29],
            DstEffectsPath = line[30],
            SpecialSound = line[31],
            DstSound = line[32],
            FullscreenEffectsTime = line[33],
        }
        if line[26] ~= nil then
            local splitStrs = string.split(line[26], dataSplitChar)
            data.nBuffIDs = { data = self:CreateDataTable() }
            for i, str in ipairs(splitStrs) do
                data.nBuffIDs.data[i - 1] = tonumber(str)
            end
        end
        if line[34] ~= nil then
            local splitStrs = string.split(line[34], dataSplitChar)
            data.OverridePos = {  pos = {x=0,y=0,z=0}}
            for i, str in ipairs(splitStrs) do
                if i == 1 then
                    data.OverridePos.pos.x = tonumber(str)/10
                elseif i == 2 then
                    data.OverridePos.pos.y = tonumber(str)/10
                elseif i == 3 then
                    data.OverridePos.pos.z = tonumber(str)/10
                end
            end
        end
        self.SkillConfigs[ID] = data
    end
end

function mgr:LoadPassiveSkillConfig(ta)
    local allLines = self.loadCSVByString(ta.text, 7)
    self.PassiveSkillConfigs = {}
    for _, line in ipairs(allLines) do
        local ID = tonumber(line[1])
        ---@class kingshot_PassiveSkillConfig
        local data = {
            SkillID = ID,
            condition = string.IsNullOrEmpty(line[4]) and 0 or tonumber(line[4]),
            ConditionParam = string.IsNullOrEmpty(line[5]) and 0 or tonumber(line[5]),
            FailureConditions = string.IsNullOrEmpty(line[6]) and 0 or tonumber(line[6]),
            FailureParam = string.IsNullOrEmpty(line[7]) and 0 or tonumber(line[7]),
            nGroupID = string.IsNullOrEmpty(line[8]) and 0 or tonumber(line[8]),
            strParam1 = string.IsNullOrEmpty(line[9]) and 0 or tonumber(line[9]),
            strParam2 = string.IsNullOrEmpty(line[10]) and 0 or tonumber(line[10]),
            strParam3 = string.IsNullOrEmpty(line[11]) and 0 or tonumber(line[11]),
            strParam4 = string.IsNullOrEmpty(line[12]) and 0 or tonumber(line[12]),
            strParam5 = string.IsNullOrEmpty(line[13]) and 0 or tonumber(line[13]),
            strParam6 = string.IsNullOrEmpty(line[14]) and 0 or tonumber(line[14]),
            unReplaceRule = string.IsNullOrEmpty(line[15]) and 0 or tonumber(line[15]),
            unReplaceNum = string.IsNullOrEmpty(line[16]) and 0 or tonumber(line[16]),
            ReplaceTimeRule = string.IsNullOrEmpty(line[17]) and 0 or tonumber(line[17]),
            StrLevel = string.IsNullOrEmpty(line[18]) and 0 or tonumber(line[18]),
            SpecialEffectsPath = line[19],
        }
        self.PassiveSkillConfigs[ID] = data
    end
end

return mgr
