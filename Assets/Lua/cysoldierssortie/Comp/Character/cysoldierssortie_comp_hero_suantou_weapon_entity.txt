local cysoldierssortie_comp_base_weapon_entity = require("cysoldierssortie_comp_base_weapon_entity")
local cysoldierssortie_comp_hero_suantou_weapon_entity = bc_Class("cysoldierssortie_comp_hero_suantou_weapon_entity",cysoldierssortie_comp_base_weapon_entity) --类名用小游戏名加后缀保证全局唯一
local NeeGame = CS.CasualGame.lib_ChuagnYi.NeeG.NeeGame
local UnityEngine = CS.UnityEngine
local math = math
local Mathf = CS.UnityEngine.Mathf
local PoolObject = NeeGame.PoolObject
local cysoldierssortie_GetMgr = cysoldierssortie_GetMgr
local cysoldierssortie_GetLuaComp = cysoldierssortie_GetLuaComp
local cysoldierssortie_DelayCallOnce = cysoldierssortie_DelayCallOnce
local bc_CS_Vector3 = bc_CS_Vector3
local cysoldierssortie_MgrName = cysoldierssortie_MgrName
local cysoldierssortie_PoolObjectName = cysoldierssortie_PoolObjectName
local ApiHelper = CS.XLuaUtil.LuaApiHelper
local SetTransformPositionByTransform = ApiHelper.SetTransformPositionByTransform

function cysoldierssortie_comp_hero_suantou_weapon_entity:CreateData(data)
    cysoldierssortie_comp_base_weapon_entity.CreateData(self,data)
end

local skillParam = {}
function cysoldierssortie_comp_hero_suantou_weapon_entity:Fire()
        self._fire_timer = cysoldierssortie_DelayCallOnce(self._release_skill_time, function()
        
        --self._class_type.super:Fire()
        cysoldierssortie_comp_base_weapon_entity.Fire(self)

        local attackCount = 5         -- Number of attacks
        local angleSpread = 45        -- Total spread angle in degrees
        
        -- Get the character's forward direction
        local forwardX,forwardY,forwardZ = ApiHelper.GetTransformForwardXYZ(self._character._weaponRoot)
        
        -- Calculate the center angle
        local centerAngle = math.atan2(forwardZ, forwardX) * Mathf.Rad2Deg

        -- Distribute attacks evenly across the fan
        local angleStep = angleSpread / (attackCount - 1)
        local startAngle = centerAngle - (angleSpread / 2)

        for i = 0, attackCount - 1 do
            -- Calculate the angle for this attack
            local attackAngle = startAngle + (i * angleStep)

            -- Convert angle to direction vector
            local radians = Mathf.Deg2Rad * attackAngle
            local direction = bc_CS_Vector3(math.cos(radians), 0, math.sin(radians))

            -- Spawn the attack at the correct position and direction
            local poolMgr =  cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
            local skillGo = poolMgr:AcquireObj("cysoldierssortie_comp_base_bullet_entity",poolMgr.transform)
            SetTransformPositionByTransform(skillGo.transform,self._character._weaponRoot)
            skillGo.transform.forward = direction
            
            local skillEntity = cysoldierssortie_GetLuaComp(skillGo.gameObject)
            skillParam.character=self._character
            skillParam.ballisiticVelocity = self._weaponData._ballisiticVelocity
            skillParam.ballisiticRange = self._weaponData._ballisiticRange
            skillParam.attack = self._character._attack * (self._weaponData._damageCoefficient/10000)
            local coe =  self._character:GetStarLvAddCoefficient(self._weaponData)
            if coe then
                skillParam.attack = skillParam.attack + skillParam.attack * coe
            end
            skillParam.damageRange = self._weaponData._damageRange
            skillParam.attackPentration = self._weaponData._pierce
            skillParam.critical = self._weaponData._criticalHit
            skillParam.bulletScale = self._weaponData._bulletScale
            skillParam.skillPath = self._weaponData._skillEffectPath
            skillParam.dstEffectsPath = self._weaponData._dstEffectsPath
            skillParam.skillID = self._weaponData._skillID
            skillEntity:CreateData(skillParam)
        end
    end)
end
return cysoldierssortie_comp_hero_suantou_weapon_entity