local require = require
local table = table
local cysoldierssortie_mgr_effect = bc_Class("cysoldierssortie_mgr_effect")
local cysoldierssortie_effect_obj = require("cysoldierssortie_effect_obj")
local cysoldierssortie_DelayCallOnce = cysoldierssortie_DelayCallOnce

local EffectConfig = 
{
    
}

function cysoldierssortie_mgr_effect.__init(self, luaMono, referCol, luaData, ...)
    if luaMono then
        self.luaMono = luaMono
    end
    if referCol then
        referCol:Bind(self)
    end
    if luaData then
        cysoldierssortie_InitLuaData(self, luaData)
    end
end
-- lua脚本正式开始

--生命周期函数
function cysoldierssortie_mgr_effect:OnEnable(data)
    if self.enabledOnce then
        return
    end
    self.enabledOnce = true;

    self.dataSrc = cysoldierssortie_CshapToLuaValue(data)
    self.gameObject = self.dataSrc.selfCshap.gameObject
    self.transform = self.dataSrc.selfCshap.transform
end

function cysoldierssortie_mgr_effect:Start()
    self._effects_weight_table = {}
    self._effects_obj_pool = {}
    self._accumulative_sid = 0 
    self._active_effects_obj = {}
end

function cysoldierssortie_mgr_effect:OnDisable()
    self._active_effects_obj = nil
    self._effects_obj_pool = nil
    self._effects_weight_table = nil
end

function cysoldierssortie_mgr_effect:CreateEffect(data)
    if not self._effects_obj_pool then
        self._effects_obj_pool = {}
    end
    
    local maxWeightLimit = data.maxWeightLimit
    local effect_path = data.effect_path
    if not effect_path then
        return
    end
    
    local effect_weight
    if maxWeightLimit then
        effect_weight = self._effects_weight_table[effect_path] or 0
        if effect_weight > 10 then
            return
        end
    end

    self._accumulative_sid = self._accumulative_sid + 1
    data.sid = self._accumulative_sid
    
    local effect_obj
    if #self._effects_obj_pool > 0 then
        effect_obj =  self._effects_obj_pool[1]
        table.remove(self._effects_obj_pool,1)
    else
        effect_obj = cysoldierssortie_effect_obj.New()
    end
    
    effect_obj:CreateData(data)

    if maxWeightLimit then
        self._effects_weight_table[effect_path] = effect_weight + 1
    end
    self._active_effects_obj[data.sid ] =  effect_obj
    return self._accumulative_sid
end

function cysoldierssortie_mgr_effect:DelayRecycleEffect(delayTime,recycleFunc)
    cysoldierssortie_DelayCallOnce(delayTime,function()
        recycleFunc()
    end)
end

function cysoldierssortie_mgr_effect:ReleaseEffect(effect_sid)
    if not self._active_effects_obj then
        return
    end
    local effect_obj =  self._active_effects_obj[effect_sid]
    if effect_obj._maxWeightLimit then
        self._effects_weight_table[effect_obj._effect_path] = self._effects_weight_table[effect_obj._effect_path] and (self._effects_weight_table[effect_obj._effect_path] - 1) or 0
    end
    effect_obj:RecycleEffect()
    self._effects_obj_pool[#self._effects_obj_pool+1] = effect_obj
    self._active_effects_obj[effect_sid] = nil
end



return cysoldierssortie_mgr_effect