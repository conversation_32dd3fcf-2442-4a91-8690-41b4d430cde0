local cysoldierssortie_event = bc_Class("cysoldierssortie_event")
--cysoldierssortie_event.events={}

function cysoldierssortie_event:__init()
    self.events = {  }
end

-- accepts any amount and type of arguments after the event name
-- NOTE: triggered events have no guaranteed order in which callback objects are called
function cysoldierssortie_event:TriggerEvt(eventname, ...)
    local eventlist = self.events[eventname] or {}

    for obj, callback in pairs(eventlist) do
        if type(obj) == "function" then
            obj(...)
        elseif obj[eventname] then
            obj[eventname](obj, ...)
        elseif obj.OnEvent then
            obj:OnEvent(eventname, ...)
        end
    end
end


-- can register multiple events at the same time
-- any arguments after the object are treated as event names to be registered
function cysoldierssortie_event:RegisterEvt(obj, ...)
    if not obj then
        return error("cysoldierssortie_event.Register error: nil callback object", 2)
    end

    local eventnames = type(...) == "table" and ... or { ... }

    if #eventnames == 0 then
        return error("cysoldierssortie_event.Register error: nil event name", 2)
    end

    for i, eventname in ipairs(eventnames) do
        if type(eventname) == "number" or type(eventname) == "string" then
            local eventlist = self.events[eventname]

            if not eventlist then
                eventlist = {}
                setmetatable(eventlist, { __mode = "k" }) -- weak keys so garbage collector can clean up properly
            end

            if type(obj) ~= "function" and type(obj) ~= "table" then
                return error("cysoldierssortie_event.Register error: callback object is not a table or function", 2)
            end

            eventlist[obj] = true
            self.events[eventname] = eventlist
        end
    end

    return obj
end


-- can unregister multiple events at the same time
-- any arguments after the object are treated as event names to be unregistered
function cysoldierssortie_event:UnRegisterEvt(obj, ...)
    if not obj then
        return error("cysoldierssortie_event.Unregister error: nil callback object", 2)
    end

    local eventnames = type(...) == "table" and ... or { ... }

    if #eventnames == 0 then
        return error("cysoldierssortie_event.Unregister error: nil event name", 2)
    end

    for i, eventname in ipairs(eventnames) do
        local eventlist = self.events[eventname]
        if eventlist and eventlist[obj] then
            eventlist[obj] = nil
        end
    end
end


-- returns array of event names registered to an object
function cysoldierssortie_event:LookUp(obj)
    if type(obj) ~= "table" and type(obj) ~= "function" then
        return error("cysoldierssortie_event.LookUp error: callback object is not a table or function", 2)
    end

    local registeredevents = {}

    for eventname, eventlist in pairs(events) do
        for _obj, callback in pairs(eventlist) do
            if obj == _obj then
                table.insert(registeredevents, eventname)
                break
            end
        end
    end

    return registeredevents
end

return cysoldierssortie_event