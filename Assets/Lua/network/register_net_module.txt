local require = require
local table_util = require "table_util"
local log = require "log"
local val = require "val"

module("register_net_module")
local Warning = log.Warning

--[[require "net_xxx_module"
]]
RegisterNet = {}
---未登录前就必须require
---机制：该模块登录前一定会被requier 启动对应功能 --区别 net模块没有Init，统一不处理
---警告：仅只有逻辑执行时才会执行的模块且不需要依赖网络/监听消息的请逻辑自己维护，不要加入初始化管理
RegisterNet.moduleNameBeforeLogin = {    
    "net_prop_module",
    "net_recharge_module",
    "unforced_guide_mgr",
    "net_module_open",
    "net_common_xManMsg_module",
    "net_role_module",
    --"activity_mgr",
    "net_personalInfo",
    "net_skin_module",
    "net_hero_module"
}
---角色+城建数据等基础数据后<时机Time<登录创建数据完成
---常见模块：非（支撑玩家基础数据+城建数据的模块）；  比如在处理其他各种登录数据部件会引用到的模块
---实现目标：1，准备好必要数据后，就尽快进入游戏 关闭登录loading  ----以前是等所有的登录数据，玩家部件等都初始化好后再进入游戏
---当前该模块初始化机制： 
---A,优先处理角色数据和城建数据等基础数据后，执行B,(同时会立马触发加载城建资源逻辑,等待城建场景加载完成时关闭loading界面进入游戏）
---B,该模块列表会自动require且被Init,因为登录的其他数据也在同步处理，可能会早于分帧require（但会支持模块首次访问自动require并Init，并不会影响逻辑） --区别 net模块没有Init，统一不处理
---C,在等待资源加载过程中可以合理利用其初始化模块，且这时候就关闭登录界面，尽早进入游戏 --注意，这里会保证该列表中的所有模块被require且Init后才会发FIRST_LOGIN_CREATE_DATA_FINISH事件
---警告：仅只有逻辑执行时才会执行的模块且不需要依赖网络/监听消息的请逻辑自己维护，不要加入初始化管理
RegisterNet.modulesBTWHomeAndCreateDataFinish =
{

}
---@description 登录创建数据完成,正式进入游戏   FIRST_LOGIN_CREATE_DATA_FINISH事件 <时机
---正式进入游戏后，登录数据都已完成才需要初始化的模块
---常见模块：eg,活动模块  ---等等不需要监听登录事件和被登录调用的
---当前该模块初始化机制： 
---A,正式登录成功后，会分帧依次将下列的模块都require一次并且Init一次         --区别 net模块没有Init，统一不处理
---B,这里如果分帧的过程中有使用场合中的场合1/2：目前该模块会支持使用到时自动Init，会被抢先require并被初始化
---D,这里如果分帧的过程中有使用场合中的场合3时：目前网络模块有未注册的消息等注册时会被补发，能解决
---使用场合：1，登录无需启动，但登录后该模块会--前往主动打开触发 
---        2，登录无需启动，但登录后该模块会--被其他模块使用到被触发，
---        3，登录无需启动，但登录后该模块会--被收到网络消息触发
---警告：仅只有逻辑执行时才会执行的模块且不需要依赖网络/监听消息的请逻辑维护，不要加入初始化管理
RegisterNet.moduleNameInLobby = {

    "popups_cache",
   
    "net_story_module",
    "net_shop_module",
    --"net_lottery_module",
    "net_arena_module",
    "net_mail_module",
    "net_setting_module",
    "net_leaguepro_module",
    "net_chat_module_new",
    "net_mill_module",
    "net_vip_module",
    "archive_data",
    "net_Anti_addiction",
    "net_halo_module", 
    "net_farm_module",
    "net_weapon_module",
    "friendship_house_data",
    "net_comment_module",
    "net_broadcast_module",
    "net_onlineReward_module",
    "net_hero_rank_module",
    "net_rank_module",
    "net_passport_module",
    "net_friend_module",
    "net_soul_link_module",
    "net_frame_module",
    "net_title_module",
    "hero_reward_mgr",
    "net_package_module",
    "net_domination_module",
    "net_championships_module",
    "net_open_test_module",
    "net_return_player_module",
    "net_void_arena_module",
    "net_legend_championships_module",
    --"story_mgr",
    "operate_activity_data",
    "operate_activity_mgr",
    "weekend_arena_mgr",
    "net_treasuer_rare_module",
    "net_new_return_player_module",
    "net_peakGame_module",
    "net_alliance_module",
    "net_sandbox_module",
    "net_scientific_research_module",
    "net_drone_center_module",
    "net_armsRace_module",
    "net_allianceTrain_module",
    "net_carriage_module",
    "net_honour_wall_module",
    "net_rechargeGift_module",
    "net_allianceDuel_module",   
    "net_acornpub_module",
    "net_DesertStrom_module",
    "net_MonsterComing_module",
}

-- if recharge_data.IsUseRechargeChannel() then
--     local index = nil
--     for i, v in ipairs(RegisterNet.moduleName) do
--         if "Recharge" == v then
--             index = i
--             break
--         end
--     end

--     if index then
--         log.Warning("[RECHARGE]register_net_module : remove before", index, RegisterNet.moduleName[index])
--         table.remove(RegisterNet.moduleName, index)
--         log.Warning("[RECHARGE]register_net_module : remove after", index, RegisterNet.moduleName[index])
--     end
-- end

-- function RegisterNet:RequireModule()
--     local len = #self.moduleName
--     local ind = 0
--     local iterval = 50
--     util.DelayCallOnce(
--         0,
--         function()
--             for i = 1, iterval do
--                 ind = ind + 1
--                 if ind > len then
--                     if self.onExit then
--                         self.onExit(moduleName, mark)
--                     end
--                     return
--                 end
--                 local module = require(moduleName[ind])
--             end
--             return 0
--         end
--     )
-- end

function RegisterNet:InitRequire()
    if self.is_init_require then
        return
    end
    self.is_init_require = true
    self.is_login_require_optimize = val.IsTrue("sw_login_require_optimize",0)  
    local require_optimize_mgr = require("require_optimize_mgr")
    local event = require("event")
    self.onExit = function(mark)
        if mark == "moduleNameBeforeLogin" then
            if self.is_login_require_optimize then
                require_optimize_mgr.DelayerRequireModule({},30,"moduleNameInLobby",false,self.onExit)
            else
                --合并一下
                local array =  table_util.ConcatArray(self.moduleNameInLobby,self.modulesBTWHomeAndCreateDataFinish)
                require_optimize_mgr.DelayerRequireModule(array,30,"moduleNameInLobby",false,self.onExit)
            end
        end
        event.Trigger(event.SET_FLOW_STATE_CACHE, "register_net_module", "1", mark)
    end
    if self.is_login_require_optimize then
        require_optimize_mgr.DelayerRequireModule(self.moduleNameBeforeLogin,50,"moduleNameBeforeLogin",false,self.onExit)
    else
        require_optimize_mgr.DelayerRequireModule(self.moduleNameBeforeLogin,50,"moduleNameBeforeLogin",false,self.onExit)
    end
end
return RegisterNet
