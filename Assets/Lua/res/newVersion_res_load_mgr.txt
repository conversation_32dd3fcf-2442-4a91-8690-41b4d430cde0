-- newVersion_res_load_mgr.txt ------------------------------------------
-- author:  刘军
-- date:    2023.11.22
-- desc:    运行时新版本资源预下载(后台更新)
--------------------------------------------------------------
local require = require
local string = string
local tostring = tostring
local log = require 'log'
local util = require "util"
local ReviewingUtil 	= require "ReviewingUtil"
local IsReviewing = ReviewingUtil.IsReviewing()
local Application = CS.UnityEngine.Application
local Directory = CS.System.IO.Directory
local BaseLoader = CS.War.Base.BaseLoader
local Time = CS.UnityEngine.Time
local tonumber = tonumber
local Path = CS.System.IO.Path
local File = CS.System.IO.File
local files_version_mgr = require "files_version_mgr"
local event = require "event"
local newVersion_res_load_helper = require "newVersion_res_load_helper"
local os = os
local math = math
local OldResVersionManager = CS.War.Base.OldResVersionManager
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
local FileEd = CS.War.Common.FileEd
local ZipInputStream = CS.ICSharpCode.SharpZipLib.Zip.ZipInputStream
local DecompressFile = CS.War.Script.Utility.DecompressFile
local xpcall = xpcall

module("newVersion_res_load_mgr")
local bStartDownLoad = false  --正在下载标识
local funcOpenFlag = false --功能开关
local resPreloadVerNum = 0 --资源预下载版本号
local checkNVerResTicker = nil --检测新版资源定时器
local checkNVerResInterval = 90  --检测新版本资源定时器时间间隔
local checkNVerResBetween = 1  --前后两次检测新版本资源时间间隔
local lastCheckTime = 0  --上一次检测时间
local filesJsonData = nil --filesJson数据
local lastCheckNVerResNo = 0 --上一次检测到的版本号
local checkNVerResNoChanged = false  --当前最新检测的版本号和上一次比较是否有发生改变
local diffsListLength = 0  --差异列表个数
local lastDownloadCount = 0  --上一次下载已完成个数
local startDownloadTime = 0  --开始下载新版本资源时间
local downloadAllSize = 0  --下载总大小
local downloadAllSizeInit = 0  --初始下载总大小
local downloadAllSizeStr = ""  --下载总大小字符串
local reportDataPercent =  --资源下载进度
{
    ["0%"] = 0,
    ["10%"] = 0,
    ["20%"] = 0,
    ["30%"] = 0,
    ["40%"] = 0,
    ["50%"] = 0,
    ["60%"] = 0,
    ["70%"] = 0,
    ["80%"] = 0,
    ["90%"] = 0,
    ["100%"] = 0,
}

--新版本资源后台下载常量字符串
local newVerResBgDownStr = "newVerResBgDown"

--上报数据事件类型枚举
local reportDataEventType =
{
    beforeChooseMark = "beforeChooseMark", --ChooseMark前
    afterChooseMark = "afterChooseMark", --ChooseMark后
    downloadStart = "downloadStart", --开始下载
    downloadProgress = "downloadProgress", --下载进度
    downloadFinish = "downloadFinish", --下载结束
}

--新版本资源下载类型枚举
local newVerResDownloadType =
{
    none = "none",   --无
    normal = "normal", --正常 通过update.json
    preload = "preload", --预下载 通过预下载filesUrl
}
--当前资源下载类型
local curNewVerResDownloadType = newVerResDownloadType.none
--当前filesUrl
local nowFilesUrl

--当前远程hash检测信息   使用老版本,预下载模式下,首先判断服务器当前最新版本资源是否在本地是否下载完整
local curRemoteHashCheck =
{
    filesJsonData = nil,  --json数据
    resVersion = 0,  --资源版本号
    filesUrl = "",  --filesUrl
    downOver = false,  --是否下载完成标志
}

--是否开始
local started = false
--检测差异列表,实现类似C#端AssetBundleManager.GetDiffList函数,再加上新版本资源资源记录已下载文件判断
--当远程资源最新版本号和本地应用的老版本号相等时检测
local checkGetDiffList = false
--是否已经检测完成,每次登录后只检测一次
local checkGetDiffListOver = false

--初始化
function Init()
    log.Warning("newVersion_res_load_mgr.Init__()")
    funcOpenFlag = newVersion_res_load_helper.GetFuncOpenFlag()
    newVersion_res_load_helper.InitNewVerResPreloadInfo()
    resPreloadVerNum = newVersion_res_load_helper.GetResPreloadVerNum()
    ProcessNewVerResDownloadType()
    RemoveCheckNVerResTicker()
    if IsFuncOpen() and not IsReviewing then
        log.Warning("newVersion_res_load_mgr.Init().funcOpenFlag:", curNewVerResDownloadType)
        checkNVerResTicker = util.IntervalCall(checkNVerResInterval, function()
            LoadNewVersionRes()
        end)
    end
    newVersion_res_load_helper.InitThreadCheckSumRes()
    CheckOverlayInstallation()
    newVersion_res_load_helper.CheckMaxHashRemoteVirtualVersion()
end

--检测覆盖安装
function CheckOverlayInstallation()
    local apkVersion = files_version_mgr.GetApkResourceVersion()
    if not apkVersion or apkVersion <= 0 then
        return
    end
    
    local maxHashRemoteVirtualVersion = PlayerPrefs.GetInt("MaxHashRemoteVirtualVersion", 0)
    if maxHashRemoteVirtualVersion <= apkVersion then
        log.Warning("newVersion_res_load_mgr.CheckOverlayInstallation(),apkVersion:", apkVersion, "__MaxHashRemoteVirtualVersion:", maxHashRemoteVirtualVersion)
        PlayerPrefs.SetInt("MaxHashRemoteVirtualVersion", 0)
        PlayerPrefs.Save()
    end
end

--功能是否开启
function IsFuncOpen()
    return curNewVerResDownloadType ~= newVerResDownloadType.none
end

--是否是预下载资源类型
function IsPreloadDownloadType()
    return curNewVerResDownloadType == newVerResDownloadType.preload
end

--处理新版本资源下载类型枚举
function ProcessNewVerResDownloadType()
    local optimalResPreload = newVersion_res_load_helper.GetOptimalResPreloadFlag()
    if funcOpenFlag then
        curNewVerResDownloadType = newVerResDownloadType.normal
    end
    if optimalResPreload and resPreloadVerNum > 0 then  --资源预下载优化标志为真并且预下载的资源版本号大于0
        local localResVersion = GetLocalResourceVersion()
        local files_version_mgr = require "files_version_mgr"
        local remoteResVersion = files_version_mgr.GetRemoteResourceVersion()
        if resPreloadVerNum > localResVersion and resPreloadVerNum > remoteResVersion then
            curNewVerResDownloadType = newVerResDownloadType.preload
        end
    end
end

--删除检测新版本资源定时器
function RemoveCheckNVerResTicker()
    if checkNVerResTicker then
        util.RemoveDelayCall(checkNVerResTicker)
        checkNVerResTicker = nil
    end
end

--检测登录
function CheckOnceLogined()
    local ui_login_main_mgr = require "ui_login_main_mgr"
    local enter = ui_login_main_mgr.GetOnceLogined()
    if not enter then
        return false
    end
    
    return true
end

--两次触发间隔
function CheckTimeIntervalSatisfy()
    local timePassed = os.clock() - lastCheckTime
    if timePassed < checkNVerResBetween then
        return false
    end
    
    lastCheckTime = os.clock()
    return true
end

--检测当前是否能下载
function CheckCanDownloadNow()
    if not IsFuncOpen() then  --功能开关未打开
        RemoveCheckNVerResTicker()
        return false
    end

    if not started then
        return false
    end
    local files_version_mgr = require "files_version_mgr"
    local newVerResPreloadBeforeLogin = files_version_mgr.GetNewVerResPreloadBeforeLogin()
    
    if not CheckOnceLogined() and not newVerResPreloadBeforeLogin then  --未登录
        return false
    end

    if IsReviewing then  --如果是审核版本
        return false
    end
    
    local gray_load_mgr = require "gray_load_mgr"
    if not gray_load_mgr.CheckLoadSizeOver() then  --后台下载过程中
        return false
    end
    
    --[[if not IsHashRemoteDiffListIsEmpty() then  --本地和远程差异列表不为空
        return false
    end--]]
    
    return true
end

--下载新版本资源
function LoadNewVersionRes(ignoreCheckTime)
    log.Warning("newVersion_res_load_mgr.LoadNewVersionRes()")
    if not CheckCanDownloadNow() then  --当前不能下载
        return
    end

    if not ignoreCheckTime and not CheckTimeIntervalSatisfy() then
        return
    end
    
    --如果是预下载类型 版本号解析失败或者预下载的版本号不大于本地版本号就返回
    if curNewVerResDownloadType == newVerResDownloadType.preload then
        if not newVersion_res_load_helper.GetResPreloadFlag(resPreloadVerNum) then
            log.Warning("newVersion_res_load_mgr.LoadNewVersionRes.GetResPreloadFlag.false")
            return
        end
    end
    
    --下载资源文件url
    local RequestFilesUrl = function(jsonData, resVersionNo)
        local filesUrlStr = "files_url"
        local filesUrl = ""
        if curNewVerResDownloadType == newVerResDownloadType.normal then  --正常类型
            if jsonData[filesUrlStr] == nil then  --filesUrl不存在
                return
            end
            filesUrl = tostring(jsonData[filesUrlStr])
        elseif curNewVerResDownloadType == newVerResDownloadType.preload then  --资源预下载
            filesUrl = newVersion_res_load_helper.GetResPreloadFilesUrl()
        end
        
        if not filesUrl or string.IsNullOrEmpty(filesUrl) then --filesUrl解析异常
            return
        end

        nowFilesUrl = filesUrl
        
        RequestFilesUrlHttp(filesUrl, function(result, strJson, jsonData)
            if result == 1 then  --请求返回成功
                filesJsonData = jsonData
                RequestFilesUrlInternal(jsonData, resVersionNo)
            end
        end)
    end
    
    --比较版本逻辑决定是否下载
    local CompareVersionToDownload = function(remoteResVersion, localResVersion, jsonData)
        log.Warning("newVersion_res_load_mgr.localResVersion:", localResVersion, "  remoteResVersion:", remoteResVersion)
        if not remoteResVersion or not localResVersion or remoteResVersion <= 0 or localResVersion <= 0 then --resVersion解析异常
            return
        end

        local localFileValid = true
        if remoteResVersion == localResVersion then
            localFileValid = newVersion_res_load_helper.CheckHashRemoteFileValid(localResVersion)
            if IsUseOldResVersion() then
                checkGetDiffList = localFileValid  --版本号相等时 
            end
        else
            checkGetDiffList = false
            checkGetDiffListOver = true
        end
        checkNVerResNoChanged = false
        if remoteResVersion > localResVersion or not localFileValid or (checkGetDiffList and not checkGetDiffListOver) then  --远程版本大于本地版本或者本地文件不存在
            if remoteResVersion ~= lastCheckNVerResNo then
                ResetOnNewVersion()
                lastCheckNVerResNo = remoteResVersion
                checkNVerResNoChanged = true
                RequestFilesUrl(jsonData, remoteResVersion)
            else
                lastCheckNVerResNo = remoteResVersion
                RequestFilesUrlInternal(filesJsonData, remoteResVersion)
            end
        end
    end

    local remoteResVersion = 0
    local localResVersion = GetLocalResourceVersion()
    if curNewVerResDownloadType == newVerResDownloadType.normal then  --正常流程
        --请求updateJson
        RequestUpdateJson(function(result, jsonData)
            if result == 1 then  --updateJson请求返回成功
                if jsonData == nil then
                    return
                end

                local files_urlStr = "files_url"
                if jsonData[files_urlStr] == nil then  --files_url不存在
                    return
                end

                remoteResVersion = GetRemoteResourceVersion(tostring(jsonData[files_urlStr]))
                CompareVersionToDownload(remoteResVersion, localResVersion, jsonData)
            end
        end)
    elseif curNewVerResDownloadType == newVerResDownloadType.preload then  --新版本资源预下载
        if IsUseOldResVersion() and not CheckCurRemoteHashCheckInfo() then
            log.Warning("newVersion_res_load_mgr.CheckCurRemoteHashCheckInfo:True:", curRemoteHashCheck.resVersion)
            return
        end
        log.Warning("newVersion_res_load_mgr.CheckCurRemoteHashCheckInfo:False")
        remoteResVersion = resPreloadVerNum
        CompareVersionToDownload(remoteResVersion, localResVersion)
    end
end

--是否处于使用老版本资源状态且在预下载模式下且当前版本资源未下载完成
function IsUseOldResOnPreload()
    return IsUseOldResVersion() and curNewVerResDownloadType == newVerResDownloadType.preload and not curRemoteHashCheck.downOver
end

--检测当前远程信息hashCheck信息,判断当前资源信息在本地是否已经存在
function CheckCurRemoteHashCheckInfo()
    --下载当前远程HashCheck资源
    local DownCurRemoteHashRes = function()
        if curRemoteHashCheck.filesJsonData and  curRemoteHashCheck.resVersion > 0 then
            nowFilesUrl = curRemoteHashCheck.filesUrl
            RequestFilesUrlInternal(curRemoteHashCheck.filesJsonData, curRemoteHashCheck.resVersion)
        end
    end

    if not curRemoteHashCheck.downOver then
        if not curRemoteHashCheck.filesJsonData then
            RequestUpdateJson(function(result, jsonData)
                if result == 1 then  --updateJson请求返回成功
                    if jsonData == nil then
                        return
                    end

                    local files_urlStr = "files_url"
                    if jsonData[files_urlStr] == nil then  --files_url不存在
                        return
                    end

                    curRemoteHashCheck.filesUrl = tostring(jsonData[files_urlStr])
                    curRemoteHashCheck.resVersion = GetRemoteResourceVersion(curRemoteHashCheck.filesUrl)
                    if curRemoteHashCheck.resVersion > 0 then
                        RequestFilesUrlHttp(curRemoteHashCheck.filesUrl, function(result, strJson, jsonData)
                            if result == 1 then  --请求返回成功
                                newVersion_res_load_helper.InitHashCurVerResLocalCheck(curRemoteHashCheck)
                                curRemoteHashCheck.filesJsonData = jsonData
                                checkNVerResNoChanged = true
                                DownCurRemoteHashRes()
                            end
                        end)
                    end
                end
            end)
        else
            DownCurRemoteHashRes()
        end
    else
        return true
    end
end

--检测使用老版本资源并且在预下载模式下
function CheckUseOldResOnPreload()
    if IsUseOldResVersion() and curNewVerResDownloadType == newVerResDownloadType.preload then
        curRemoteHashCheck.downOver = true
        curRemoteHashCheck.filesJsonData = nil
        curRemoteHashCheck.resVersion = nil
        curRemoteHashCheck.filesUrl = nil
    end
end

--获取到差异列表
function OnGetDiffList(diffs)
    if not diffs then  --要下载的列表为空
        return
    end

    if #diffs <= 0 and diffsListLength <= 0 then
        CheckUseOldResOnPreload()
    end
end

--下载完成事件
function OnDownFinishEvent()
    CheckUseOldResOnPreload()
end

--获取创建完整虚拟文件tag
function GetCreateHashRemoteVirtualTag()
    local tag = "newVersion_res_load__" .. curNewVerResDownloadType
    if IsUseOldResVersion() and curNewVerResDownloadType == newVerResDownloadType.preload and not curRemoteHashCheck.downOver then --处于使用老版本资源状态且在预下载模式下且当前版本资源未下载完成
        tag = "newVersion_res_load__" .. newVerResDownloadType.normal
    end

    return tag
end

--下载资源实现
function LoadResInternal(diffs)
    if not CheckCanDownloadNow() then  --当前不能下载
        return
    end
    
    if not diffs then  --要下载的列表为空l
        return
    end

    local startDownLoad = function()
        if #diffs > 0 and checkNVerResNoChanged then
            diffsListLength = #diffs
            lastDownloadCount = 0
            startDownloadTime = os.clock()
            downloadAllSizeInit = downloadAllSize
            downloadAllSizeStr = ToMNumberStr(downloadAllSize)
            ReportNewVerResDownloadStart()
        end
    end
    
    if diffs then
        startDownLoad()
        if #diffs > 0 then
            newVersion_res_load_helper.CancelResListDownLoad()
            newVersion_res_load_helper.AddResListDownLoad(diffs, onResListDownLoadFinish)
            bStartDownLoad = true
        else
            bStartDownLoad = false
        end
    end
end

--下载资源完成回调
function onResListDownLoadFinish()
    LoadNewVersionRes(true)
end

--检测本地和远程差异是否为空
--[[function IsHashRemoteDiffListIsEmpty()
    local diffs = AssetBundleManager.GetDiffList()
    if not diffs then
        return true
    end

    if diffs.Length > 0 then
        return false
    end
    
    return true
end--]]

--后台下载完成
function OnGrayLoadOver()
    local gray_load_mgr = require "gray_load_mgr"
    if --[[IsHashRemoteDiffListIsEmpty() and --]]gray_load_mgr.CheckLoadSizeOver() and IsFuncOpen() then
        LoadNewVersionRes()
    end
end

--后台下载开始
function OnGrayLoadStart()
    
end

--请求资源列表文件实现
function RequestFilesUrlInternal(jsonData, resVersionNo)
    if not jsonData then
        return
    end
    newVersion_res_load_helper.UpdateNewVersionResRemoteHash(jsonData, resVersionNo)
    local getDiffList = function(diffs, diffFilesSize)
        OnGetDiffList(diffs)
        downloadAllSize = diffFilesSize
        LoadResInternal(diffs)
        newVersion_res_load_helper.CheckNewVerResLoadComplete(GetLocalResourceVersion(), diffs)
        log.Warning("newVersion_res_load_mgr.RequestFilesUrlInternal.diffs:", #diffs,".filesSize:", downloadAllSizeStr)
    end
    newVersion_res_load_helper.StartNewVerResDiffList(checkGetDiffList, getDiffList)
end

--文件下载完成
function FileDownCompleteOnGetDiffList()
    if checkGetDiffList then
        checkGetDiffListOver = true
    end
end

--是否处于检测CheckGetDiffList状态
function IsOnCheckGetDiffList()
   return checkGetDiffList and not checkGetDiffListOver
end

--请求资源列表文件使用http
function RequestFilesUrlHttp(filesurl, func)
    if string.IsNullOrEmpty(filesurl) then
        func(0, "", nil)
        return
    end
    
    if CheckIsEditor() then
        func(0, "", nil)
        return
    end
    
    local newVersion_res_load_helper = require "newVersion_res_load_helper"
    
    --存在解压方法类
    local hasDecompressFileCond = function()
        --替换files.txt为files2.bytes
        local urlNow = filesurl:gsub("files.txt", "files2.bytes")
        local http_inst = require "http_inst"
        local files2BytesSavePath = newVersion_res_load_helper.GetFileNewCachingSavePath("files2.bytes")
        local files2TxtSavePath = newVersion_res_load_helper.GetFileNewCachingSavePath("files2.txt")
        local decompressDir = newVersion_res_load_helper.GetNewCachingSaveDir()
        log.Warning("newVersion_res_load_mgr.hasDecompressFileCond:", urlNow)
        http_inst.RequestDownloadFile(urlNow, files2BytesSavePath, 10, function(result, url, savePath, param)
            if result == 0 then
                func(0, "", nil)
            else
                local doCB = false
                --读取文件内容
                local readFileContent = function()
                    if File.Exists(files2BytesSavePath) then
                        local zipInputStream = ZipInputStream(File.OpenRead(files2BytesSavePath))
                        local result = DecompressFile(zipInputStream, files2BytesSavePath, decompressDir)
                        if zipInputStream then
                            zipInputStream:Close()
                            zipInputStream:Dispose()
                        end

                        if result then
                            log.Warning("newVersion_res_load_mgr.DecompressFile.Success")
                            local data = FileEd.ReadAllText(files2TxtSavePath)
                            if not string.IsNullOrEmpty(data) then
                                local dkjson = require "dkjson"
                                local jsonData = dkjson.decode(data)
                                func(1, data, jsonData)
                                doCB = true
                            end
                        end
                    end
                end

                local f, res = xpcall(readFileContent, function(err)
                    log.Warning("newVersion_res_load_mgr.readFileContent()发生异常!!!!!!", err)
                end)

                if not doCB then
                    func(0, "", nil)
                end
            end
        end) 
    end

    --不存在解压方法类
    local hasNotDecompressFileCond = function()
        --替换files.txt为files2.txt
        local urlNow = filesurl:gsub("files.txt", "files2.txt")
        log.Warning("newVersion_res_load_mgr.hasNotDecompressFileCond:", urlNow)
        local http_inst = require "http_inst"
        http_inst.Req_Timeout(urlNow, 10, function(data)
            if nil == data then
                func(0, "", nil)
                return
            end

            local dkjson = require "dkjson"
            local jsonData = dkjson.decode(data)
            func(1, data, jsonData)
        end)
    end

    local hasDecompressFileFlag = newVersion_res_load_helper.GetDecompressFileFlag()
    if hasDecompressFileFlag then
        hasDecompressFileCond()
    else
        hasNotDecompressFileCond()
    end
end

--请求updateJson
--func为函数回调带两个参数 参数1(int) 0失败 1成功 参数2为(table)jsonData
function RequestUpdateJson(func)
    --回调
    local callBackFunc = function(result, jsonStr)
        if func then
            func(result, jsonStr)
        end
    end
    
    if CheckIsEditor() then
        callBackFunc(0,"")
        return
    end
    
    local update_json_url = GetUpdateJsonUrl()
    if not update_json_url then
        callBackFunc(0,"")
        return
    end

    local real_url = update_json_url
    local http_inst = require "http_inst"
    http_inst.Req_Timeout(real_url, 10, function(data, hasError, bytes)
        if nil == data then
            callBackFunc(0,"")
            return
        end
        
        local dkjson = require "dkjson"
        local jsonData = dkjson.decode(data)
        callBackFunc(1, jsonData)
    end)
end

--检测是否是编辑器
function CheckIsEditor()
    return Application.isEditor
    --return false
end

--获取updateJson url
function GetUpdateJsonUrl()
    local files_version_mgr = require "files_version_mgr"
    return files_version_mgr.GetUpdateJsonUrl()
    --return "http://down-wmzz-cn.q1.com/xhero_update/wm2_2020/Android/update.json"
    --return "http://172.16.126.185:3121/xGame_android_trunk_overseas/Android/update.json"
end

--是否当前在使用老版本资源
function IsUseOldResVersion()
    return util.IsCSharpClass(OldResVersionManager) and OldResVersionManager.IS_OLD_RES_VERSION
end

--获取本地资源版本号
function GetLocalResourceVersion()
    local local_resource_version = -1
    -- 获取本地资源版本号
    local patchSaveRootPath = GetPatchSaveRootPath()
    local patchRecordFilePath = Path.Combine(patchSaveRootPath, "resource_record.bytes")
    if File.Exists(patchRecordFilePath) then
        recordJson = util.ReadJson(patchRecordFilePath)
    else
        -- 获得 APK 内置资源版本号, Application.version = "x.x.x",不是资源版本号
        local apkVersion = files_version_mgr.GetApkResourceVersion()
        recordJson =
        {
            cur_version = apkVersion,
            downloading_version = -1,
            subpatch_set = {},
        }
    end

    if recordJson.cur_version then
        local_resource_version = recordJson.cur_version
    end

    if IsUseOldResVersion() then  --如果是使用老版本资源
        if OldResVersionManager.GetOldResVersion then
            local_resource_version = OldResVersionManager.GetOldResVersion()
        else
            local_resource_version = files_version_mgr.GetApkResourceVersion()
        end
    end
    
    return local_resource_version
    --return 314
end

local subPatchRootPath = nil
--获取patch保存路径
function GetPatchSaveRootPath()
    if subPatchRootPath then
        return subPatchRootPath
    end

    -- persistentDataPath
    local persistentRelativeUrl = BaseLoader.GetPersistentRelativeUrl()
    persistentRelativeUrl = string.gsub(persistentRelativeUrl, "file://", "")
    local newCachingPath = persistentRelativeUrl .. "/NewCaching/"
    subPatchRootPath = newCachingPath .. "SubPatchPackage/"
    if not Directory.Exists(subPatchRootPath) then
        Directory.CreateDirectory(subPatchRootPath)
    end
    return subPatchRootPath
end

--获取远程资源版本号
function GetRemoteResourceVersion(files_url)
    local curResourceVersion = -1
    if files_url == nil then
        return curResourceVersion
    end

    -- e.g. "files_url": "https://down-wmzz.q1.com/marvelous/marvelous_update/huawei/Android/resource/38/files.txt"
    -- http://172.16.126.185:3011/rogue_t5_trunk/Android/resource/765/files.txt
    local resource_version = ParseResourceVersion(files_url)
    if string.empty(resource_version) then
        return curResourceVersion
    end
    curResourceVersion = tonumber(resource_version)
    return curResourceVersion
end

--解析资源版本
function ParseResourceVersion(filesUrl)
    local resource_version = string.match(filesUrl, "/resource/(%d+)/")
    if not resource_version then
        return -1
    end
    return resource_version
end

--转换到Mb字符串
function ToMNumberStr(v)
    return (math.round(v/1000000)).."M"
end

--重置当检测到新版本时
function ResetOnNewVersion()
    diffsListLength = 0
    lastDownloadCount = 0
end

--获取差异列表文件个数
function GetDiffsListLength()
    return diffsListLength
end

------------------------------------打点上报数据Start---------------------------------------------------
--远程资源在chooseMark之前list的长度
function ReportRemoteListBeforeChooseMark(listLength)
    local properties = {}
    properties.reportEventType = reportDataEventType.beforeChooseMark
    properties.listLength = listLength
    Report(newVerResBgDownStr, properties)
end

--远程资源在chooseMark之后list的长度
function ReportRemoteListAfterChooseMark(listLength, res_key, ch_res_key)
    local properties = {}
    properties.reportEventType = reportDataEventType.afterChooseMark
    properties.listLength = listLength
    properties.res_key = res_key
    properties.ch_res_key = ch_res_key
    Report(newVerResBgDownStr, properties)
end

--新版本资源开始下载
function ReportNewVerResDownloadStart()
    local properties = {}
    properties.reportEventType = reportDataEventType.downloadStart
    properties.diffsListLength = diffsListLength
    Report(newVerResBgDownStr, properties)
    CheckRefreshGrayLoadUI(downloadAllSizeInit, 0, true)
end

--新版本资源文件下载响应
function ReportNewVerResDownloaded(downLoadCount)
    if not downLoadCount then
        return
    end

    if downLoadCount == lastDownloadCount then
        return
    end

    lastDownloadCount = downLoadCount
    local nowDownLoadSize = newVersion_res_load_helper.GetDownloadedFileSize()
    local ratio = nowDownLoadSize / downloadAllSizeInit
    local percent = string.format("%d%%", ratio * 100)
    if reportDataPercent[percent] and reportDataPercent[percent] == 0 then
        reportDataPercent[percent] = 1
        local nowDownLoadSizeStr = ToMNumberStr(nowDownLoadSize)
        local nowTime = os.clock()
        local properties = {}
        properties.reportEventType = reportDataEventType.downloadProgress
        properties.diffsListLength = diffsListLength
        properties.finishCount = downLoadCount
        properties.progress = percent
        properties.totalTime = nowTime - startDownloadTime
        properties.downloadNowSize = nowDownLoadSizeStr
        Report(newVerResBgDownStr, properties) 
    end
    CheckRefreshGrayLoadUI(downloadAllSizeInit, nowDownLoadSize)
end

--新版本资源下载完成
function ReportNewVerResDownloadFinish(enableResPreload, callBackFunc)
    local writeFileFinish = function()  --写文件完成
        if callBackFunc then
            callBackFunc()
        end
    end
    --登录前完整资源下载完成后，创建对应版本的本地模拟HashRemote
    if util.IsCSharpClass(OldResVersionManager) then
        if OldResVersionManager then
            local filesUrl = newVersion_res_load_helper.ConstructRemoteVerResNoFilesUrl()
            local hashNewVerResRemote = newVersion_res_load_helper.GetHashNewVerRemote()
            if hashNewVerResRemote and hashNewVerResRemote.list then
                local tag = GetCreateHashRemoteVirtualTag()
                local tList = {}
                tList = util.shallow_copy(hashNewVerResRemote.list)
                if not OldResVersionManager.FixAddLuaScriptABName then
                    log.Warning("newVersion_res_load_mgr.ReportNewVerResDownloadFinish.FixAddLuaScriptABName.False")
                    local val = hashNewVerResRemote.list["luascript.zip"]
                    if val then
                        tList["luascript"] = val
                    end
                end

                local optimalSaveHashFile = newVersion_res_load_helper.GetOptimalSaveHashFileFlag()
                local isSaveHashRemoteVirtual = OldResVersionManager.isSaveHashRemoteVirtual
                if not isSaveHashRemoteVirtual then  --如果不保存文件
                    writeFileFinish()
                    return
                end
                
                local saveFileComplete = function()  --保存文件完成
                    writeFileFinish()
                    PlayerPrefs.Save()
                    log.Warning("UseOldResVersion CreateHashRemoteVirtualOfCurrentDownLoad by :", tag)
                end
                
                if not optimalSaveHashFile then
                    OldResVersionManager.CreateHashRemoteVirtualOfCurrentDownLoad(tag, true, filesUrl, tList)
                    saveFileComplete()
                else
                    OldResVersionManager.BeginUseNewResList()
                    newVersion_res_load_helper.StartSaveWholeResList(tList, function(finishAll, list)
                        if finishAll then
                            OldResVersionManager.CreateHashRemoteVirtualOfCurrentDownLoad(tag, true, filesUrl, nil, true, function()
                                saveFileComplete()
                                OldResVersionManager.EndUseNewResList()
                            end)
                        else
                            if list then
                                OldResVersionManager.AddNewResList(list)
                            end
                        end
                    end)
                end
            end
        end
    else  --不存在使用老版本功能类
        writeFileFinish()
    end

    OnDownFinishEvent()
    FileDownCompleteOnGetDiffList()
    log.Warning("newVersion_res_load_mgr.ReportNewVerResDownloadFinish:", curNewVerResDownloadType)

    if GetDiffsListLength() <= 0 then  --差异列表个数小于等于0
        return
    end
    
    local percent = string.format("%d%%", 100)
    local nowTime = os.clock()
    local properties = {}
    properties.reportEventType = reportDataEventType.downloadFinish
    properties.diffsListLength = diffsListLength
    properties.finishCount = diffsListLength
    properties.progress = percent
    properties.totalTime = nowTime - startDownloadTime
    Report(newVerResBgDownStr, properties)
    CheckRefreshGrayLoadUI(downloadAllSizeInit, downloadAllSizeInit, false, true)
end

--打点上报数据
function Report(eName,obj)
    if (not eName) or (not obj) then
        return
    end
    local player_mgr = require "player_mgr"
    local playerProp = player_mgr.GetPlayerProp()

    local game_config 	= require "game_config"
    local q1sdk 	= require "q1sdk"
    local ui_login_main_mgr = require "ui_login_main_mgr"
    local remoteResVersion = lastCheckNVerResNo  --当前最新版本资源版本号
    local localResVersion = GetLocalResourceVersion()  --当前本地版本资源版本号
    obj.pid = game_config.CHANNEL_ID
    obj.uuid = q1sdk.GetUUID()
    obj.level = playerProp and playerProp.lv
    obj.server_id = ui_login_main_mgr.GetLoginServerID()
    obj.role_id = player_mgr.GetPlayerRoleID()
    obj.since_start_time = Time.realtimeSinceStartup
    obj.localResVersion = localResVersion
    obj.remoteResVersion = remoteResVersion
    obj.newVerResDownloadType = curNewVerResDownloadType
    obj.downTotalSize = downloadAllSizeStr
    event.Trigger(event.GAME_EVENT_REPORT,eName, obj)
end
------------------------------------打点上报数据End---------------------------------------------------

--检测刷新后台下载UI
function CheckRefreshGrayLoadUI(totalSize, loadSize, start, finish)
    local newVersion_res_load_view = require "newVersion_res_load_view"
    newVersion_res_load_view.CheckRefreshGrayLoadUI(totalSize, loadSize, start, finish)
end

--开始
--downloadType对应preload_resources中DownloadPatchType枚举
function OnStart(downloadType)
    local files_version_mgr = require "files_version_mgr"
    local newVerResPreloadBeforeLogin = files_version_mgr.GetNewVerResPreloadBeforeLogin()
    started = true
    log.Warning("newVersion_res_load_mgr.OnStart.downloadType:", downloadType, "__newVerResPreloadBeforeLogin:", newVerResPreloadBeforeLogin)

    if newVerResPreloadBeforeLogin and not IsReviewing then
        require "gray_load_mgr" 
    end
end

Init()

