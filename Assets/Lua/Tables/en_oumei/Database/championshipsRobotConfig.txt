return {
['fieldNames']={['ID']=1,['nHookStar']=2,['nHookLevelID']=3,['nRobotRoleLvMin']=4,['nRobotRoleLvMax']=5,['nRobotHeroLvMin']=6,['nRobotHeroLvMax']=7,['nRobotHeroStarMin']=8,['nRobotHeroStarMax']=9,['nRobotHeroScore']=10},
['addInfos']={{1,{1,22},{1,10},{5},{7},{1,1},{9,9},{4,4},{5,5},{262,262}},
{2,{23,45},{51,60},{8},{9},{10,10},{19,19},{5,5},{5,5},{143,413}},
{3,{46,69},{101,109},{10},{10},{20,20},{34,34},{5,5},{5,5},{102,553}},
{4,{70,92},{110,154},{11},{11},{35,35},{47,47},{5,5},{5,5},{104,855}},
{5,{93,116},{155,165},{12},{12},{48,48},{55,55},{5,5},{5,5},{150,1540}},
{6,{117,140},{201,209},{13},{14},{56,56},{63,63},{5,5},{5,5},{135,1611}},
{7,{141,164},{210,220},{15},{15},{64,64},{71,71},{5,5},{5,5},{148,2000}},
{8,{165,188},{251,259},{16},{16},{72,72},{79,79},{5,5},{5,5},{137,2062}},
{9,{189,212},{260,269},{17},{17},{80,80},{87,87},{5,5},{5,5},{130,2178}},
{10,{213,237},{270,310},{18},{18},{88,88},{95,95},{5,5},{5,5},{131,2388}},
{11,{238,261},{311,319},{19},{20},{96,96},{103,103},{6,6},{6,6},{127,2102}},
{12,{262,286},{320,359},{21},{21},{104,104},{111,111},{6,6},{6,6},{155,2777}},
{13,{287,311},{360,370},{22},{22},{112,112},{119,119},{6,6},{6,6},{177,3409}},
{14,{312,336},{1001,1009},{23},{23},{120,120},{124,124},{6,6},{6,6},{176,3583}},
{15,{337,361},{1010,1059},{24},{25},{125,125},{129,129},{6,6},{6,6},{178,3761}},
{16,{362,386},{1060,1109},{26},{26},{130,130},{134,134},{6,6},{6,6},{173,3808}},
{17,{387,412},{1110,1155},{27},{27},{135,135},{139,139},{7,7},{7,7},{178,3488}},
{18,{413,438},{1156,1164},{28},{28},{140,140},{144,144},{7,7},{7,7},{174,3527}},
{19,{439,463},{1165,1209},{29},{29},{145,145},{149,149},{7,7},{7,7},{165,3475}},
{20,{464,489},{1210,1219},{30},{31},{150,150},{154,154},{7,7},{7,7},{171,3708}},
{21,{490,516},{1220,1260},{32},{32},{155,155},{159,159},{7,7},{7,7},{190,4264}},
{22,{517,542},{1261,1269},{33},{33},{160,160},{164,164},{8,8},{8,8},{209,4237}},
{23,{543,568},{1270,1309},{34},{34},{165,165},{167,167},{8,8},{8,8},{226,4698}},
{24,{569,595},{1310,1319},{35},{36},{168,168},{170,170},{8,8},{8,8},{249,5264}},
{25,{596,622},{1320,1360},{37},{37},{171,171},{173,173},{8,8},{8,8},{289,6222}},
{26,{623,648},{1361,1369},{38},{38},{174,174},{176,176},{8,8},{8,8},{284,6217}},
{27,{649,675},{1370,1409},{39},{39},{177,177},{179,179},{8,8},{8,8},{287,6389}},
{28,{676,703},{1410,1419},{40},{40},{180,180},{182,182},{9,9},{9,9},{311,6252}},
{29,{704,730},{1420,1460},{41},{42},{183,183},{185,185},{9,9},{9,9},{327,6681}},
{30,{731,757},{1461,1469},{43},{43},{186,186},{188,188},{9,9},{9,9},{329,6833}},
{31,{758,785},{1470,2009},{44},{44},{189,189},{191,191},{9,9},{9,9},{337,7124}},
{32,{786,813},{2010,2059},{45},{45},{192,192},{194,194},{9,9},{9,9},{341,7303}},
{33,{814,841},{2060,2110},{46},{46},{195,195},{197,197},{9,9},{9,9},{374,8149}},
{34,{842,869},{2111,2154},{47},{48},{198,198},{200,200},{9,9},{9,9},{379,8375}},
{35,{870,897},{2155,2164},{49},{49},{201,201},{203,203},{10,10},{10,10},{388,7835}},
{36,{898,926},{2165,2209},{50},{50},{204,204},{206,206},{10,10},{10,10},{385,7896}},
{37,{927,954},{2210,2220},{51},{51},{207,207},{209,209},{10,10},{10,10},{403,8391}},
{38,{955,983},{2251,2259},{52},{53},{210,210},{212,212},{10,10},{10,10},{426,8996}},
{39,{984,1012},{2260,2269},{54},{54},{213,213},{215,215},{10,10},{10,10},{457,9770}},
{40,{1013,1041},{2270,2309},{55},{55},{216,216},{218,218},{10,10},{10,10},{476,10320}},
{41,{1042,1070},{2310,2320},{56},{56},{219,219},{221,221},{10,10},{10,10},{494,10861}},
{42,{1071,1099},{2351,2359},{57},{57},{222,222},{224,224},{10,10},{10,10},{516,11517}},
{43,{1100,1129},{2360,2369},{58},{59},{225,225},{227,227},{10,10},{10,10},{548,12378}},
{44,{1130,1158},{2370,2409},{60},{60},{228,228},{230,230},{10,10},{10,10},{568,13003}},
{45,{1159,1188},{2410,2420},{61},{61},{231,231},{233,233},{10,10},{10,10},{594,13777}},
{46,{1189,1218},{2451,2459},{62},{62},{234,234},{236,236},{10,10},{10,10},{611,14360}},
{47,{1219,1248},{2460,2469},{63},{64},{237,237},{239,239},{10,10},{10,10},{635,15105}},
{48,{1249,1278},{2470,2509},{65},{65},{240,240},{242,242},{11,11},{11,11},{687,15058}},
{49,{1279,1309},{2510,2520},{66},{66},{243,243},{245,245},{11,11},{11,11},{746,16544}},
{50,{1310,1339},{2551,2559},{67},{67},{246,246},{248,248},{11,11},{11,11},{740,16608}},
{51,{1340,1370},{2560,2569},{68},{68},{249,249},{251,251},{11,11},{11,11},{827,18803}},
{52,{1371,1401},{2570,3009},{69},{70},{252,252},{254,254},{11,11},{11,11},{923,21236}},
{53,{1402,1432},{3010,3060},{71},{71},{255,255},{257,257},{11,11},{11,11},{997,23214}},
{54,{1433,1463},{3101,3109},{72},{72},{258,258},{260,260},{11,11},{11,11},{1022,24063}},
{55,{1464,1494},{3110,3154},{73},{73},{261,261},{263,263},{12,12},{12,12},{1040,22702}},
{56,{1495,1526},{3155,3164},{74},{75},{264,264},{266,266},{12,12},{12,12},{1158,25577}},
{57,{1527,1557},{3165,3210},{76},{76},{267,267},{269,269},{12,12},{12,12},{1216,27161}},
{58,{1558,1589},{3211,3219},{77},{77},{270,270},{272,272},{12,12},{12,12},{1253,28289}},
{59,{1590,1621},{3220,3259},{78},{78},{273,273},{275,275},{12,12},{12,12},{1290,29452}},
{60,{1622,1653},{3260,3269},{79},{79},{276,276},{278,278},{12,12},{12,12},{1325,30588}},
{61,{1654,1685},{3270,3310},{80},{81},{279,279},{281,281},{13,13},{13,13},{1360,29295}},
{62,{1686,1717},{3311,3319},{82},{82},{282,282},{284,284},{13,13},{13,13},{1364,29697}},
{63,{1718,1750},{3320,3359},{83},{83},{285,285},{287,287},{13,13},{13,13},{1401,30824}},
{64,{1751,1783},{3360,3369},{84},{84},{288,288},{290,290},{13,13},{13,13},{1460,32462}},
{65,{1784,1815},{3370,3410},{85},{86},{291,291},{293,293},{13,13},{13,13},{1508,33876}},
{66,{1816,1848},{3411,3419},{87},{87},{294,294},{296,296},{13,13},{13,13},{1493,33888}},
{67,{1849,1882},{3420,3459},{88},{88},{297,297},{299,299},{13,13},{13,13},{1576,36130}},
{68,{1883,1915},{3460,3469},{89},{89},{300,300},{302,302},{14,14},{14,14},{1629,35030}},
{69,{1916,1948},{3470,3510},{90},{90},{303,303},{305,305},{14,14},{14,14},{1700,36915}},
{70,{1949,1982},{3511,3519},{91},{92},{306,306},{308,308},{14,14},{14,14},{1739,38139}},
{71,{1983,2016},{3520,3559},{93},{93},{309,309},{311,311},{14,14},{14,14},{1767,39119}},
{72,{2017,2049},{3560,3569},{94},{94},{312,312},{314,314},{14,14},{14,14},{1746,39046}},
{73,{2050,2083},{3570,4010},{95},{95},{315,315},{317,317},{14,14},{14,14},{1784,40259}},
{74,{2084,2118},{4051,4059},{96},{97},{318,318},{320,320},{14,14},{14,14},{1772,40371}},
{75,{2119,2152},{4060,4109},{98},{98},{321,321},{323,323},{15,15},{15,15},{1795,38542}},
{76,{2153,2186},{4110,4154},{99},{99},{324,324},{326,326},{15,15},{15,15},{1833,39708}},
{77,{2187,2221},{4155,4165},{100},{100},{327,327},{329,329},{15,15},{15,15},{1873,40962}},
{78,{2222,2256},{4201,4209},{101},{101},{330,330},{332,332},{15,15},{15,15},{1867,41190}},
{79,{2257,2291},{4210,4219},{102},{103},{333,333},{335,335},{15,15},{15,15},{1926,42882}},
{80,{2292,2326},{4220,4259},{104},{104},{336,336},{338,338},{15,15},{15,15},{1921,43157}},
{81,{2327,2361},{4260,4270},{105},{105},{339,339},{339,339},{15,15},{15,15},{1973,44579}},
{82,{2362,2397},{4301,4309},{106},{106},{340,340},{340,340},{15,15},{15,15},{1979,44857}},
{83,{2398,2432},{4310,4319},{107},{107},{340,340},{340,340},{15,15},{15,15},{2036,46152}},
{84,{2433,2468},{4320,4359},{108},{109},{340,340},{340,340},{15,15},{15,15},{2047,46406}},
{85,{2469,2504},{4360,4370},{110},{110},{340,340},{340,340},{15,15},{15,15},{2110,47837}},
{86,{2505,2540},{4401,4409},{111},{111},{340,340},{340,340},{15,15},{15,15},{2116,47957}},
{87,{2541,2576},{4410,4419},{112},{112},{340,340},{340,340},{15,15},{15,15},{2181,49428}},
{88,{2577,2612},{4420,4459},{113},{114},{340,340},{340,340},{15,15},{15,15},{2192,49683}},
{89,{2613,2649},{4460,4470},{115},{115},{340,340},{340,340},{15,15},{15,15},{2255,51119}},
{90,{2650,2685},{4501,4509},{116},{116},{340,340},{340,340},{15,15},{15,15},{2312,52408}},
{91,{2686,2722},{4510,4519},{117},{117},{340,340},{340,340},{15,15},{15,15},{2336,52940}},
{92,{2723,2759},{4520,4559},{118},{118},{340,340},{340,340},{15,15},{15,15},{2413,54705}},
{93,{2760,2790},{4560,4570},{119},{120},{340,340},{340,340},{15,15},{15,15},{2427,55014}}},
['num']=93,
['keyFuncs']={{1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93}},
['md5']='f3cce137357c9fce5880158b13275f67',
['keys']={{1}}}