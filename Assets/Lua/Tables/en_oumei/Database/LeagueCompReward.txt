return {
['fieldNames']={['ID']=1,['nUnionRank']=2,['nMajorCityLevel']=3,['arrEndPrizeScope']=4,['strEndPrize']=5},
['addInfos']={{1,1,1,0,10},{2,1,2,0,11},{3,1,3,0,12},{4,1,4,0,13},{5,1,5,0,14},{6,1,6,0,15},{7,1,7,0,16},{8,1,8,0,17},{9,1,9,0,18},{19,2,1,0,20},{21,2,2,0,22},{23,2,3,0,24},{25,2,4,0,26},{27,2,5,0,28},{29,2,6,0,30},{31,2,7,0,32},{33,2,8,0,34},{35,2,9,0,36},{37,3,1,0,38},{39,3,2,0,40},{41,3,3,0,42},{43,3,4,0,44},{45,3,5,0,46},{47,3,6,0,48},{49,3,7,0,50},{51,3,8,0,52},{53,3,9,0,54},{55,4,1,0,56},{57,4,2,0,58},{59,4,3,0,60},{61,4,4,0,62},{63,4,5,0,64},{65,4,6,0,66},{67,4,7,0,68},{69,4,8,0,70},{71,4,9,0,72}},
['num']=36,['vEnums']={{0,1100,3400,4500,6000},1,2,3,4,5,6,7,8,9,'201001#201181#201361;201037#201217#201362;201073#201253#201363;201109#201289#201364;201145#201325#201365','201002#201182#201361;201038#201218#201362;201074#201254#201363;201110#201290#201364;201146#201326#201365','201003#201183#201361;201039#201219#201362;201075#201255#201363;201111#201291#201364;201147#201327#201365','201004#201184#201361;201040#201220#201362;201076#201256#201363;201112#201292#201364;201148#201328#201365','201005#201185#201361;201041#201221#201362;201077#201257#201363;201113#201293#201364;201149#201329#201365','201006#201186#201361;201042#201222#201362;201078#201258#201363;201114#201294#201364;201150#201330#201365','201007#201187#201361;201043#201223#201362;201079#201259#201363;201115#201295#201364;201151#201331#201365','201008#201188#201361;201044#201224#201362;201080#201260#201363;201116#201296#201364;201152#201332#201365','201009#201189#201361;201045#201225#201362;201081#201261#201363;201117#201297#201364;201153#201333#201365',10,'201010#201190#201361;201046#201226#201362;201082#201262#201363;201118#201298#201364;201154#201334#201365',11,'201011#201191#201361;201047#201227#201362;201083#201263#201363;201119#201299#201364;201155#201335#201365',12,'201012#201192#201361;201048#201228#201362;201084#201264#201363;201120#201300#201364;201156#201336#201365',13,'201013#201193#201361;201049#201229#201362;201085#201265#201363;201121#201301#201364;201157#201337#201365',14,'201014#201194#201361;201050#201230#201362;201086#201266#201363;201122#201302#201364;201158#201338#201365',15,'201015#201195#201361;201051#201231#201362;201087#201267#201363;201123#201303#201364;201159#201339#201365',16,'201016#201196#201361;201052#201232#201362;201088#201268#201363;201124#201304#201364;201160#201340#201365',17,'201017#201197#201361;201053#201233#201362;201089#201269#201363;201125#201305#201364;201161#201341#201365',18,'201018#201198#201361;201054#201234#201362;201090#201270#201363;201126#201306#201364;201162#201342#201365',19,'201019#201199#201361;201055#201235#201362;201091#201271#201363;201127#201307#201364;201163#201343#201365',20,'201020#201200#201361;201056#201236#201362;201092#201272#201363;201128#201308#201364;201164#201344#201365',21,'201021#201201#201361;201057#201237#201362;201093#201273#201363;201129#201309#201364;201165#201345#201365',22,'201022#201202#201361;201058#201238#201362;201094#201274#201363;201130#201310#201364;201166#201346#201365',23,'201023#201203#201361;201059#201239#201362;201095#201275#201363;201131#201311#201364;201167#201347#201365',24,'201024#201204#201361;201060#201240#201362;201096#201276#201363;201132#201312#201364;201168#201348#201365',25,'201025#201205#201361;201061#201241#201362;201097#201277#201363;201133#201313#201364;201169#201349#201365',26,'201026#201206#201361;201062#201242#201362;201098#201278#201363;201134#201314#201364;201170#201350#201365',27,'201027#201207#201361;201063#201243#201362;201099#201279#201363;201135#201315#201364;201171#201351#201365',28,'201028#201208#201361;201064#201244#201362;201100#201280#201363;201136#201316#201364;201172#201352#201365',29,'201029#201209#201361;201065#201245#201362;201101#201281#201363;201137#201317#201364;201173#201353#201365',30,'201030#201210#201361;201066#201246#201362;201102#201282#201363;201138#201318#201364;201174#201354#201365',31,'201031#201211#201361;201067#201247#201362;201103#201283#201363;201139#201319#201364;201175#201355#201365',32,'201032#201212#201361;201068#201248#201362;201104#201284#201363;201140#201320#201364;201176#201356#201365',33,'201033#201213#201361;201069#201249#201362;201105#201285#201363;201141#201321#201364;201177#201357#201365',34,'201034#201214#201361;201070#201250#201362;201106#201286#201363;201142#201322#201364;201178#201358#201365',35,'201035#201215#201361;201071#201251#201362;201107#201287#201363;201143#201323#201364;201179#201359#201365',36,'201036#201216#201361;201072#201252#201362;201108#201288#201363;201144#201324#201364;201180#201360#201365'},
['keyFuncs']={{1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36},{['1_1']=1,['1_2']=2,['1_3']=3,['1_4']=4,['1_5']=5,['1_6']=6,['1_7']=7,['1_8']=8,['1_9']=9,['2_1']=10,['2_2']=11,['2_3']=12,['2_4']=13,['2_5']=14,['2_6']=15,['2_7']=16,['2_8']=17,['2_9']=18,['3_1']=19,['3_2']=20,['3_3']=21,['3_4']=22,['3_5']=23,['3_6']=24,['3_7']=25,['3_8']=26,['3_9']=27,['4_1']=28,['4_2']=29,['4_3']=30,['4_4']=31,['4_5']=32,['4_6']=33,['4_7']=34,['4_8']=35,['4_9']=36}},
['md5']='25c966425f05f2d0d7b5f309f3596014',
['keys']={{1},{2,3}}}