local require = require
local OptimizerTable = require "OptimizerTable"
module("GameScheme")
GameScheme = {}
function GameScheme:Init(pDataCenter)
    self.m_pDC = pDataCenter
end
OptimizerTable[0]="Effect"
OptimizerTable[1]="BattleScore"
OptimizerTable[2]="Buff"
OptimizerTable[3]="MapInfo"
OptimizerTable[4]="Sound"
OptimizerTable[5]="RandName"
OptimizerTable[6]="HookLevel"
OptimizerTable[7]="HangUp"
OptimizerTable[8]="Hero"
OptimizerTable[9]="Equipment"
OptimizerTable[10]="EquipmentPro"
OptimizerTable[11]="EquipmentSet"
OptimizerTable[12]="HeroPro"
OptimizerTable[13]="HeroSkill"
OptimizerTable[14]="HeroUpgrade"
OptimizerTable[15]="Item"
OptimizerTable[16]="HeroAdvance"
OptimizerTable[17]="Skill"
OptimizerTable[18]="Composite"
OptimizerTable[19]="HelpTips"
OptimizerTable[20]="LotteryBox"
OptimizerTable[21]="Reward"
OptimizerTable[22]="LotteryWish"
OptimizerTable[23]="Lottery"
OptimizerTable[24]="EquipDevour"
OptimizerTable[25]="Shopping"
OptimizerTable[26]="Modul"
OptimizerTable[27]="HeroComposite"
OptimizerTable[28]="InitBattleProp"
OptimizerTable[29]="EquipmentProRand"
OptimizerTable[30]="Task"
OptimizerTable[31]="TavernTask"
OptimizerTable[32]="Arena"
OptimizerTable[33]="IllusionTower"
OptimizerTable[34]="monsterTeam"
OptimizerTable[35]="Achievement"
OptimizerTable[36]="ActivityContent"
OptimizerTable[37]="monthlyActivity"
OptimizerTable[38]="rmbActivity"
OptimizerTable[39]="HeroTransformTB"
OptimizerTable[40]="TrialEvent"
OptimizerTable[41]="ArenaReward"
OptimizerTable[42]="TrialLevel"
OptimizerTable[43]="TrialRefresh"
OptimizerTable[44]="RoleFace"
OptimizerTable[45]="AshDungeon"
OptimizerTable[46]="AshMonsterTeam"
OptimizerTable[47]="SpecialChar"
OptimizerTable[48]="dailyAttendance"
OptimizerTable[49]="TrialMonsterTeam"
OptimizerTable[50]="RoleUpgrade"
OptimizerTable[51]="Mail"
OptimizerTable[52]="menu"
OptimizerTable[53]="PowerCoefficient"
OptimizerTable[54]="heroDecompose"
OptimizerTable[55]="AllianceTechnology"
OptimizerTable[56]="LeagueIcon"
OptimizerTable[57]="ActivityParam"
OptimizerTable[58]="LeagueCompCell"
OptimizerTable[59]="LeagueCompCellRefresh"
OptimizerTable[60]="LeagueCompDistance"
OptimizerTable[61]="LeagueCompGrid"
OptimizerTable[62]="LeagueCompIntegral"
OptimizerTable[63]="LeagueCompMajorCity"
OptimizerTable[64]="LeagueCompAchievement"
OptimizerTable[65]="LeagueCompReward"
OptimizerTable[66]="leagueMillOrder"
OptimizerTable[67]="leagueMill"
OptimizerTable[68]="IllusionTowerMopUp"
OptimizerTable[69]="VipPrivilege"
OptimizerTable[70]="Recharge"
OptimizerTable[71]="Boss"
OptimizerTable[72]="StrFun"
OptimizerTable[73]="HeroReplace"
OptimizerTable[74]="LeagueTreasureFlower"
OptimizerTable[75]="GoldenHand"
OptimizerTable[76]="keyword"
OptimizerTable[77]="HaloUpgrade"
OptimizerTable[78]="HaloUnLock"
OptimizerTable[79]="LeagueTreasureReward"
OptimizerTable[80]="ForceGuide"
OptimizerTable[81]="GuideStep"
OptimizerTable[82]="BtnTips"
OptimizerTable[83]="BubbleSkill"
OptimizerTable[84]="BubbleHero"
OptimizerTable[85]="FloatText"
OptimizerTable[86]="Particle"
OptimizerTable[87]="PlotStep"
OptimizerTable[88]="Tips"
OptimizerTable[89]="MagicBotany"
OptimizerTable[90]="MagicMain"
OptimizerTable[91]="MagicWeapon"
OptimizerTable[92]="MagicMate"
OptimizerTable[93]="Community"
OptimizerTable[94]="UnforcedGuide"
OptimizerTable[95]="UnforcedGuideStep"
OptimizerTable[96]="scoreArray"
OptimizerTable[97]="scoreGem"
OptimizerTable[98]="scoreHero"
OptimizerTable[99]="scoreSkill"
OptimizerTable[100]="scoreStandard"
OptimizerTable[101]="scoreTech"
OptimizerTable[102]="scoreWeapon"
OptimizerTable[103]="GuideRadar"
OptimizerTable[104]="BubbleMap"
OptimizerTable[105]="weeklyTask"
OptimizerTable[106]="welfareActivity"
OptimizerTable[107]="BubbleHeroTips"
OptimizerTable[108]="TargetTask"
OptimizerTable[109]="TargetTaskSub"
OptimizerTable[110]="MoneyExchange"
OptimizerTable[111]="SevenDayAttendance"
OptimizerTable[112]="Broadcast"
OptimizerTable[113]="ChallengeList"
OptimizerTable[114]="ChallengeReward"
OptimizerTable[115]="SubsequentRewardRule"
OptimizerTable[116]="Notice"
OptimizerTable[117]="NodeBubbleTips"
OptimizerTable[118]="LoadingHeroTips"
OptimizerTable[119]="EventTips"
OptimizerTable[120]="AvatarFrame"
OptimizerTable[121]="NameIcon"
OptimizerTable[122]="TimeRewardItem"
OptimizerTable[123]="SpecialGift"
OptimizerTable[124]="HeroRankingList"
OptimizerTable[125]="TopSquadList"
OptimizerTable[126]="LeagueCompCellWall"
OptimizerTable[127]="ChatTextColor"
OptimizerTable[128]="MazeLevel"
OptimizerTable[129]="MazeRefresh"
OptimizerTable[130]="MazeMercenary"
OptimizerTable[131]="RankAchv"
OptimizerTable[132]="MazePos"
OptimizerTable[133]="TrialTreasure"
OptimizerTable[134]="Passport"
OptimizerTable[135]="SubsequentRewardTask"
OptimizerTable[136]="AdvArenaReward"
OptimizerTable[137]="ShopRefresh"
OptimizerTable[138]="MazeMonsterTeam"
OptimizerTable[139]="BoxChoose"
OptimizerTable[140]="SummonReward"
OptimizerTable[141]="Slot"
OptimizerTable[142]="RoleFrame"
OptimizerTable[143]="festivalActivity"
OptimizerTable[144]="festivalActivityUI"
OptimizerTable[145]="festivalActivityExtraTask"
OptimizerTable[146]="DivSkillHelper"
OptimizerTable[147]="RechargeExtraReward"
OptimizerTable[148]="ChristmasTree"
OptimizerTable[149]="festivalChristmasUI"
OptimizerTable[150]="festivalCenturionCardUI"
OptimizerTable[151]="monthlyTask"
OptimizerTable[152]="TavernLevel"
OptimizerTable[153]="FactionHalo"
OptimizerTable[154]="FactionTower"
OptimizerTable[155]="UnlockRmbActivity"
OptimizerTable[156]="HeroReturn"
OptimizerTable[157]="FestivalUIDetail"
OptimizerTable[158]="leagueLevel"
OptimizerTable[159]="SpaceDominator"
OptimizerTable[160]="SpaceDominatorReward"
OptimizerTable[161]="LoginPopup"
OptimizerTable[162]="PeakOfTimeEvent"
OptimizerTable[163]="PeakOfTimeChapter"
OptimizerTable[164]="PeakOfTimeLevel"
OptimizerTable[165]="PeakOfTimeMonsterTeam"
OptimizerTable[166]="PeakOfTimeStory"
OptimizerTable[167]="KillEffect"
OptimizerTable[168]="SpaceDominatorRobot"
OptimizerTable[169]="SpecialGiftUIDetail"
OptimizerTable[170]="HookLevelChest"
OptimizerTable[171]="LeagueHelp"
OptimizerTable[172]="WorldMap"
OptimizerTable[173]="EquipEnhance"
OptimizerTable[174]="EquipEnhanceMaster"
OptimizerTable[175]="PopupControl"
OptimizerTable[176]="MagicRobot"
OptimizerTable[177]="MagicGamble"
OptimizerTable[178]="championships"
OptimizerTable[179]="championshipsSubsection"
OptimizerTable[180]="ArenaLevel"
OptimizerTable[181]="SinglefieldAward"
OptimizerTable[182]="ItemSource"
OptimizerTable[183]="SystemSource"
OptimizerTable[184]="Lnstance"
OptimizerTable[185]="LnstanceType"
OptimizerTable[186]="EntertainmentCityLv"
OptimizerTable[187]="EntertainmentCityReward"
OptimizerTable[188]="MagicReward"
OptimizerTable[189]="UnionBattle"
OptimizerTable[190]="ChallengeListNew"
OptimizerTable[191]="ChallengeContent"
OptimizerTable[192]="ActivityTask"
OptimizerTable[193]="LangParameters"
OptimizerTable[194]="ItemAwaken"
OptimizerTable[195]="LotteryShare"
OptimizerTable[196]="GiftPopUpControl"
OptimizerTable[197]="GiftPopUp"
OptimizerTable[198]="ShareGameActivity"
OptimizerTable[199]="NewHeroSummonReward"
OptimizerTable[200]="NewHeroTreasureReward"
OptimizerTable[201]="NewHeroTreasureTask"
OptimizerTable[202]="GlobalEvents"
OptimizerTable[203]="TalentSkill"
OptimizerTable[204]="MergeServerContent"
OptimizerTable[205]="draftRecommand"
OptimizerTable[206]="draftTotal"
OptimizerTable[207]="ArtifactSuit"
OptimizerTable[208]="GVEUnionBoss"
OptimizerTable[209]="MergeServerList"
OptimizerTable[210]="ReturnActivityContent"
OptimizerTable[211]="ReturnActivityList"
OptimizerTable[212]="RookieActivity"
OptimizerTable[213]="RookieContent"
OptimizerTable[214]="Summoned"
OptimizerTable[215]="DailyGift"
OptimizerTable[216]="RoleTitle"
OptimizerTable[217]="VoidArena"
OptimizerTable[218]="HeroPortraits"
OptimizerTable[219]="HeroPortraitsPic"
OptimizerTable[220]="VoidArenaMonsterStar"
OptimizerTable[221]="AreaChampionship"
OptimizerTable[222]="slgMoraleLimit"
OptimizerTable[223]="slgMoveCost"
OptimizerTable[224]="slgOfficial"
OptimizerTable[225]="slgOverReward"
OptimizerTable[226]="slgPointReward"
OptimizerTable[227]="slgStarWarbarbarian"
OptimizerTable[228]="slgStarWarCityPosition"
OptimizerTable[229]="slgStarWarLandResource"
OptimizerTable[230]="slgStarWarMap"
OptimizerTable[231]="slgStarWarMaterial"
OptimizerTable[232]="slgStarWarMaterialArea"
OptimizerTable[233]="slgStarWarObstacle"
OptimizerTable[234]="slgStarWarPlayerHome"
OptimizerTable[235]="slgStarWarstronghold"
OptimizerTable[236]="slgStarWarstrongholdSum"
OptimizerTable[237]="slgTask"
OptimizerTable[238]="SLGTechnology"
OptimizerTable[239]="AreaChampionshipSvrCfg"
OptimizerTable[240]="MonopolyCheckerboard"
OptimizerTable[241]="MonopolyStar"
OptimizerTable[242]="championshipsRobotConfig"
OptimizerTable[243]="championshipsRobotHeroConfig"
OptimizerTable[244]="GameEventReport"
OptimizerTable[245]="ActivityFlop"
OptimizerTable[246]="ActivityFlopReward"
OptimizerTable[247]="SpaceGap"
OptimizerTable[248]="SpaceGapStory"
OptimizerTable[249]="RechargeGoogleShop"
OptimizerTable[250]="UnionMedal"
OptimizerTable[251]="RecomCard"
OptimizerTable[252]="RecomHero"
OptimizerTable[253]="Skin"
OptimizerTable[254]="SkinProp"
OptimizerTable[255]="SkinSkill"
OptimizerTable[256]="ExclusiveFight"
OptimizerTable[257]="ExclusiveStage"
OptimizerTable[258]="ModuleCfg"
OptimizerTable[259]="ModuleActivity"
OptimizerTable[260]="EquipmentResonance"
OptimizerTable[261]="EquipAdvance"
OptimizerTable[262]="ActivityIntimacy"
OptimizerTable[263]="ItemExchange"
OptimizerTable[264]="RecommendGift"
OptimizerTable[265]="PuzzleLevel"
OptimizerTable[266]="Creature"
OptimizerTable[267]="BingoReward"
OptimizerTable[268]="GuideStepPuzzleGame"
OptimizerTable[269]="ForceGuidePuzzleGame"
OptimizerTable[270]="PuzzleGameChapters"
OptimizerTable[271]="PuzzleGameLevel"
OptimizerTable[272]="NewActivity"
OptimizerTable[273]="HeroRecList"
OptimizerTable[274]="StarExploreEvent"
OptimizerTable[275]="StarExploreGalaxyMap"
OptimizerTable[276]="StarExploreMap"
OptimizerTable[277]="StarExploreStars"
OptimizerTable[278]="StarExploreTech"
OptimizerTable[279]="StarScience"
OptimizerTable[280]="Sigil"
OptimizerTable[281]="ProToLang"
OptimizerTable[282]="NewExclusiveFight"
OptimizerTable[283]="RecommendPack"
OptimizerTable[284]="NewSvrAreaChampionshipSvrCfg"
OptimizerTable[285]="RechargeCumulative"
OptimizerTable[286]="NewSvrAreaChampionship"
OptimizerTable[287]="SpiritLinkUpgrade"
OptimizerTable[288]="LegendsTournament"
OptimizerTable[289]="SubmitGift"
OptimizerTable[290]="HeroChoice"
OptimizerTable[291]="welfare"
OptimizerTable[292]="FunctionBar"
OptimizerTable[293]="zhanxingwu"
OptimizerTable[294]="passSystem"
OptimizerTable[295]="NewbeeAreaChampionshipSchedule"
OptimizerTable[296]="StoryCfg"
OptimizerTable[297]="StoryStep"
OptimizerTable[298]="ActivityBossTrial"
OptimizerTable[299]="ChallengeDeck"
OptimizerTable[300]="NewAwakenRoad"
OptimizerTable[301]="HeroAwakeSkill"
OptimizerTable[302]="ChallengeDeckBoard"
OptimizerTable[303]="LotteryChoose"
OptimizerTable[304]="towerMiniGame"
OptimizerTable[305]="towerMiniGameMonsterPath"
OptimizerTable[306]="MiniGameControl"
OptimizerTable[307]="MiniGameLevelControl"
OptimizerTable[308]="SummonBlessHero"
OptimizerTable[309]="Odyssey"
OptimizerTable[310]="Odysseychapters"
OptimizerTable[311]="WeekendArena"
OptimizerTable[312]="WeekendArenaSvrCfg"
OptimizerTable[313]="OdysseyMopUp"
OptimizerTable[314]="ChallengeDeckPro"
OptimizerTable[315]="CasualGame"
OptimizerTable[316]="MiniGameSkill"
OptimizerTable[317]="CasualGameWithBg"
OptimizerTable[318]="KeywordSwindle"
OptimizerTable[319]="ActorBackground"
OptimizerTable[320]="BattleBackground"
OptimizerTable[321]="BattleSkillName"
OptimizerTable[322]="Decorations"
OptimizerTable[323]="Bless"
OptimizerTable[324]="Biography"
OptimizerTable[325]="HikingActivity"
OptimizerTable[326]="BubbleTips"
OptimizerTable[327]="RechargeGoods"
OptimizerTable[328]="RechargeExchange"
OptimizerTable[329]="RechargeChannelGoods"
OptimizerTable[330]="RechargeChannelMap"
OptimizerTable[331]="MoneyType"
OptimizerTable[332]="IosExchange"
OptimizerTable[333]="NewHeroActivityUI"
OptimizerTable[334]="LotteryChanceDetail"
OptimizerTable[335]="Currency"
OptimizerTable[336]="SkinBuff"
OptimizerTable[337]="ChannelTag"
OptimizerTable[338]="festivalActivityTime"
OptimizerTable[339]="MiniGameQuickChallenge"
OptimizerTable[340]="ABTest"
OptimizerTable[341]="HeroLimit"
OptimizerTable[342]="ActivityCollect"
OptimizerTable[343]="ActivityExchange"
OptimizerTable[344]="SkinPortraitsPic"
OptimizerTable[345]="Country"
OptimizerTable[346]="GalaxyTask"
OptimizerTable[347]="GalaxyHero"
OptimizerTable[348]="GalaxyMonster"
OptimizerTable[349]="GalaxyMonsterDefineLogic"
OptimizerTable[350]="GalaxyRound"
OptimizerTable[351]="GalaxyShop"
OptimizerTable[352]="GalaxyItem"
OptimizerTable[353]="GalaxyBanner"
OptimizerTable[354]="GalaxySvrCfg"
OptimizerTable[355]="GalaxyRank"
OptimizerTable[356]="GalaxyRankReward"
OptimizerTable[357]="ActivityKoiFish"
OptimizerTable[358]="festivalActivityUrl"
OptimizerTable[359]="DimensionWarSvrCfg"
OptimizerTable[360]="DimensionWarTeamlimt"
OptimizerTable[361]="DimensionWarTask"
OptimizerTable[362]="DimensionWarMatchScore"
OptimizerTable[363]="DimensionWarMap"
OptimizerTable[364]="DimensionWarFort"
OptimizerTable[365]="DimensionWar"
OptimizerTable[366]="DimensionWarBoss"
OptimizerTable[367]="DimensionWarDescribe"
OptimizerTable[368]="NewMiniGameControl"
OptimizerTable[369]="NewMiniGameLevelControl"
OptimizerTable[370]="MinigameSetting"
OptimizerTable[371]="ModuleOpen"
OptimizerTable[372]="StarWeapon"
OptimizerTable[373]="Gem"
OptimizerTable[374]="GemComposite"
OptimizerTable[375]="PickTheRoute"
OptimizerTable[376]="PickTheRouteEvent"
OptimizerTable[377]="FestivalAssetsConfig"
OptimizerTable[378]="SpringFestivalBlessing"
OptimizerTable[379]="HalloweenCostume"
OptimizerTable[380]="HalloweenRiddle"
OptimizerTable[381]="HalloweenScore"
OptimizerTable[382]="TangrenDay"
OptimizerTable[383]="TangrenRes"
OptimizerTable[384]="SkinBubbleSkill"
OptimizerTable[385]="SkinHeroSkill"
OptimizerTable[386]="HeroTrial"
OptimizerTable[387]="returnActivityHandup"
OptimizerTable[388]="ActivityPrivilege"
OptimizerTable[389]="returnGuidelines"
OptimizerTable[390]="GraphicsDevice"
OptimizerTable[391]="DeviceLevel"
OptimizerTable[392]="supervalue"
OptimizerTable[393]="Calendar"
OptimizerTable[394]="ForgeLottery"
OptimizerTable[395]="TreasureRare"
OptimizerTable[396]="TreasureRareGift"
OptimizerTable[397]="ForgeSlot"
OptimizerTable[398]="ReincarnationSvrCfg"
OptimizerTable[399]="ReincarnationReward"
OptimizerTable[400]="ReincarnationRobot"
OptimizerTable[401]="Reincarnation"
OptimizerTable[402]="UnionTreasure"
OptimizerTable[403]="AnniversaryList"
OptimizerTable[404]="WelfarePlay"
OptimizerTable[405]="ExChampionshipSvrCfg"
OptimizerTable[406]="ExChampionship"
OptimizerTable[407]="ExChampionshipBet"
OptimizerTable[408]="ExChampionshipTask"
OptimizerTable[409]="ExChampionshipTime"
OptimizerTable[410]="Grandarea"
OptimizerTable[411]="BuildAreaMap"
OptimizerTable[412]="BuildMaincityMap"
OptimizerTable[413]="Building"
OptimizerTable[414]="BuildingType"
OptimizerTable[415]="FunctionOpen"
OptimizerTable[416]="ScientificClassification"
OptimizerTable[417]="ScientificResearch"
OptimizerTable[418]="GWMapEffect"
OptimizerTable[419]="SpoilsChest"
OptimizerTable[420]="LeagueTechnology"
OptimizerTable[421]="LeagueAuthority"
OptimizerTable[422]="leagueTables"
OptimizerTable[423]="LeagueDonations"
OptimizerTable[424]="LeaguePermissionEffect"
OptimizerTable[425]="AccelerateRewardItem"
OptimizerTable[426]="LeagueMutualAid"
OptimizerTable[427]="SandMapCity"
OptimizerTable[428]="SandMapRegion"
OptimizerTable[429]="SandMapCompare"
OptimizerTable[430]="SandMapResources"
OptimizerTable[431]="SandMapMonster"
OptimizerTable[432]="SandMapMarch"
OptimizerTable[433]="GWMapConstant"
OptimizerTable[434]="SandMapStratum"
OptimizerTable[435]="SandMapModelResource"
OptimizerTable[436]="SandMapCityAdorn"
OptimizerTable[437]="Soldier"
OptimizerTable[438]="BuildProduce"
OptimizerTable[439]="SandMapWander"
OptimizerTable[440]="BuildSurvivor"
OptimizerTable[441]="BuildSurvivorSkill"
OptimizerTable[442]="SandMapViewLevel"
OptimizerTable[443]="SandMapModelResourceNew"
OptimizerTable[444]="BuildEvent"
OptimizerTable[445]="BuildingMapData"
OptimizerTable[446]="BubbleManager"
OptimizerTable[447]="GWMapBuff"
OptimizerTable[448]="HeroSkillLevelup"
OptimizerTable[449]="HeroSkillUnlock"
OptimizerTable[450]="HeroLevelUp"
OptimizerTable[451]="HeroProList"
OptimizerTable[452]="ProSourceToLang"
OptimizerTable[453]="RoleSchloss"
OptimizerTable[454]="RadarMission"
OptimizerTable[455]="Radarlevel"
OptimizerTable[456]="RadarRefresh"
OptimizerTable[457]="DiggingTreasures"
OptimizerTable[458]="RadarTaskLogic"
OptimizerTable[459]="SandkastenBox"
OptimizerTable[460]="ChatSharing"
OptimizerTable[461]="DailyGameplay"
OptimizerTable[462]="SoldierMonster"
OptimizerTable[463]="BuildingWall"
OptimizerTable[464]="Prescription"
OptimizerTable[465]="WorldBOSSManagement"
OptimizerTable[466]="RankingRewards"
OptimizerTable[467]="FightPowerDetails"
OptimizerTable[468]="ActivityMain"
OptimizerTable[469]="PioneerCommander"
OptimizerTable[470]="Allianzboss"
OptimizerTable[471]="AllianzbossDonation"
OptimizerTable[472]="CampTrial"
OptimizerTable[473]="CampTrialLevel"
OptimizerTable[474]="PersonalTrial"
OptimizerTable[475]="TrialleagueChallenge"
OptimizerTable[476]="NewHeroTreasureBox"
OptimizerTable[477]="GatheringActivities"
OptimizerTable[478]="SandMapSearch"
OptimizerTable[479]="ActivityCommonUI"
OptimizerTable[480]="ArmsRaceRounds"
OptimizerTable[481]="ArmsRaceTarget"
OptimizerTable[482]="ArmsRaceTheme"
OptimizerTable[483]="ArmsRaceThemeTime"
OptimizerTable[484]="TaskMain"
OptimizerTable[485]="Ranking"
OptimizerTable[486]="LeagueAchievements"
OptimizerTable[487]="Vip"
OptimizerTable[488]="ActivityServerPlan"
OptimizerTable[489]="ActivityServerMatch"
OptimizerTable[490]="NpcConfig"
OptimizerTable[491]="TruckBotSift"
OptimizerTable[492]="TruckDepot"
OptimizerTable[493]="TruckRefresh"
OptimizerTable[494]="TruckOtherPeople"
OptimizerTable[495]="TruckSummary"
OptimizerTable[496]="TruckTrade"
OptimizerTable[497]="BuildPreProcess"
OptimizerTable[498]="AutomaticMail"
OptimizerTable[499]="Sandmapcompetition"
OptimizerTable[500]="SecretTask"
OptimizerTable[501]="SecretStar"
OptimizerTable[502]="AllianceDuelChest"
OptimizerTable[503]="AllianceDuelRewards"
OptimizerTable[504]="AllianceDuelTheme"
OptimizerTable[505]="AllianceDuelScience"
OptimizerTable[506]="HonorWall"
OptimizerTable[507]="CongressOfficialPosition"
OptimizerTable[508]="CongressAwards"
OptimizerTable[509]="Congressbadge"
OptimizerTable[510]="GuideContent"
OptimizerTable[511]="CommanderWeek"
OptimizerTable[512]="DailySpecialGift"
OptimizerTable[513]="MiniSkill"
OptimizerTable[514]="MiniObstacle"
OptimizerTable[515]="MiniUnit"
OptimizerTable[516]="MiniLevel"
OptimizerTable[517]="GWMapBtn"
OptimizerTable[518]="GiftPrivilege"
OptimizerTable[519]="CumulList"
OptimizerTable[520]="MiniPassiveSkill"
OptimizerTable[521]="TradeShip"
OptimizerTable[522]="AccessoryAdorn"
OptimizerTable[523]="TradeShipCargo"
OptimizerTable[524]="TradeShipRefresh"
OptimizerTable[525]="MiniGameMapping"
OptimizerTable[526]="HeroResourceIncrease"
OptimizerTable[527]="MiracleBox"
OptimizerTable[528]="GoldZombie"
OptimizerTable[529]="CampBuff"
OptimizerTable[530]="SandMapSpecialAreas"
OptimizerTable[531]="leaguelanguage"
OptimizerTable[532]="DesertStormBuilding"
OptimizerTable[533]="DesertStormReward"
OptimizerTable[534]="SecretTreasure"
OptimizerTable[535]="DesertStormConfig"
OptimizerTable[536]="MonstersApproaching"
OptimizerTable[537]="SrverShowdownCompetition"
OptimizerTable[538]="TodoSchedule"
OptimizerTable[539]="GearSupply"
OptimizerTable[540]="BuildingTypeMiniGame"
OptimizerTable[541]="BuildMaincityMapMiniGame"
function GameScheme:Effect(iIndex)
return self.m_pDC:GetRecordByIndex(0, iIndex)
end
function GameScheme:Effect_nums()
return self.m_pDC:GetFileRecordNums(0)
end
function GameScheme:Effect_0(nEffectID)
local pKey={nEffectID=nEffectID}
return self.m_pDC:GetRecord(0, 0, pKey);
end
function GameScheme:BattleScore(iIndex)
return self.m_pDC:GetRecordByIndex(1, iIndex)
end
function GameScheme:BattleScore_nums()
return self.m_pDC:GetFileRecordNums(1)
end
function GameScheme:BattleScore_0(ScoreType,ConditionValue)
local pKey={ScoreType=ScoreType,ConditionValue=ConditionValue}
return self.m_pDC:GetRecord(1, 0, pKey);
end
function GameScheme:Buff(iIndex)
return self.m_pDC:GetRecordByIndex(2, iIndex)
end
function GameScheme:Buff_nums()
return self.m_pDC:GetFileRecordNums(2)
end
function GameScheme:Buff_0(unBuffID,unBuffLevel)
local pKey={unBuffID=unBuffID,unBuffLevel=unBuffLevel}
return self.m_pDC:GetRecord(2, 0, pKey);
end
function GameScheme:MapInfo(iIndex)
return self.m_pDC:GetRecordByIndex(3, iIndex)
end
function GameScheme:MapInfo_nums()
return self.m_pDC:GetFileRecordNums(3)
end
function GameScheme:MapInfo_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(3, 0, pKey);
end
function GameScheme:Sound(iIndex)
return self.m_pDC:GetRecordByIndex(4, iIndex)
end
function GameScheme:Sound_nums()
return self.m_pDC:GetFileRecordNums(4)
end
function GameScheme:Sound_0(nSoundID)
local pKey={nSoundID=nSoundID}
return self.m_pDC:GetRecord(4, 0, pKey);
end
function GameScheme:RandName(iIndex)
return self.m_pDC:GetRecordByIndex(5, iIndex)
end
function GameScheme:RandName_nums()
return self.m_pDC:GetFileRecordNums(5)
end
function GameScheme:RandName_0(iID)
local pKey={iID=iID}
return self.m_pDC:GetRecord(5, 0, pKey);
end
function GameScheme:HookLevel(iIndex)
return self.m_pDC:GetRecordByIndex(6, iIndex)
end
function GameScheme:HookLevel_nums()
return self.m_pDC:GetFileRecordNums(6)
end
function GameScheme:HookLevel_0(checkPointID)
local pKey={checkPointID=checkPointID}
return self.m_pDC:GetRecord(6, 0, pKey);
end
function GameScheme:HookLevel_1(mapID,checkpoint)
local pKey={mapID=mapID,checkpoint=checkpoint}
return self.m_pDC:GetRecord(6, 1, pKey);
end
function GameScheme:HookLevel_2(ChapterID,LevelID)
local pKey={ChapterID=ChapterID,LevelID=LevelID}
return self.m_pDC:GetRecord(6, 2, pKey);
end
function GameScheme:HangUp(iIndex)
return self.m_pDC:GetRecordByIndex(7, iIndex)
end
function GameScheme:HangUp_nums()
return self.m_pDC:GetFileRecordNums(7)
end
function GameScheme:HangUp_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(7, 0, pKey);
end
function GameScheme:Hero(iIndex)
return self.m_pDC:GetRecordByIndex(8, iIndex)
end
function GameScheme:Hero_nums()
return self.m_pDC:GetFileRecordNums(8)
end
function GameScheme:Hero_0(heroID)
local pKey={heroID=heroID}
return self.m_pDC:GetRecord(8, 0, pKey);
end
function GameScheme:Equipment(iIndex)
return self.m_pDC:GetRecordByIndex(9, iIndex)
end
function GameScheme:Equipment_nums()
return self.m_pDC:GetFileRecordNums(9)
end
function GameScheme:Equipment_0(equipmentID)
local pKey={equipmentID=equipmentID}
return self.m_pDC:GetRecord(9, 0, pKey);
end
function GameScheme:EquipmentPro(iIndex)
return self.m_pDC:GetRecordByIndex(10, iIndex)
end
function GameScheme:EquipmentPro_nums()
return self.m_pDC:GetFileRecordNums(10)
end
function GameScheme:EquipmentPro_0(proID)
local pKey={proID=proID}
return self.m_pDC:GetRecord(10, 0, pKey);
end
function GameScheme:EquipmentSet(iIndex)
return self.m_pDC:GetRecordByIndex(11, iIndex)
end
function GameScheme:EquipmentSet_nums()
return self.m_pDC:GetFileRecordNums(11)
end
function GameScheme:EquipmentSet_0(suitID)
local pKey={suitID=suitID}
return self.m_pDC:GetRecord(11, 0, pKey);
end
function GameScheme:HeroPro(iIndex)
return self.m_pDC:GetRecordByIndex(12, iIndex)
end
function GameScheme:HeroPro_nums()
return self.m_pDC:GetFileRecordNums(12)
end
function GameScheme:HeroPro_0(propID,starLv)
local pKey={propID=propID,starLv=starLv}
return self.m_pDC:GetRecord(12, 0, pKey);
end
function GameScheme:HeroSkill(iIndex)
return self.m_pDC:GetRecordByIndex(13, iIndex)
end
function GameScheme:HeroSkill_nums()
return self.m_pDC:GetFileRecordNums(13)
end
function GameScheme:HeroSkill_0(heroSkillID,star)
local pKey={heroSkillID=heroSkillID,star=star}
return self.m_pDC:GetRecord(13, 0, pKey);
end
function GameScheme:HeroUpgrade(iIndex)
return self.m_pDC:GetRecordByIndex(14, iIndex)
end
function GameScheme:HeroUpgrade_nums()
return self.m_pDC:GetFileRecordNums(14)
end
function GameScheme:HeroUpgrade_0(heroLv)
local pKey={heroLv=heroLv}
return self.m_pDC:GetRecord(14, 0, pKey);
end
function GameScheme:Item(iIndex)
return self.m_pDC:GetRecordByIndex(15, iIndex)
end
function GameScheme:Item_nums()
return self.m_pDC:GetFileRecordNums(15)
end
function GameScheme:Item_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(15, 0, pKey);
end
function GameScheme:HeroAdvance(iIndex)
return self.m_pDC:GetRecordByIndex(16, iIndex)
end
function GameScheme:HeroAdvance_nums()
return self.m_pDC:GetFileRecordNums(16)
end
function GameScheme:HeroAdvance_0(heroID,advanceTyp,advanceLv)
local pKey={heroID=heroID,advanceTyp=advanceTyp,advanceLv=advanceLv}
return self.m_pDC:GetRecord(16, 0, pKey);
end
function GameScheme:Skill(iIndex)
return self.m_pDC:GetRecordByIndex(17, iIndex)
end
function GameScheme:Skill_nums()
return self.m_pDC:GetFileRecordNums(17)
end
function GameScheme:Skill_0(nSkillID)
local pKey={nSkillID=nSkillID}
return self.m_pDC:GetRecord(17, 0, pKey);
end
function GameScheme:Composite(iIndex)
return self.m_pDC:GetRecordByIndex(18, iIndex)
end
function GameScheme:Composite_nums()
return self.m_pDC:GetFileRecordNums(18)
end
function GameScheme:Composite_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(18, 0, pKey);
end
function GameScheme:HelpTips(iIndex)
return self.m_pDC:GetRecordByIndex(19, iIndex)
end
function GameScheme:HelpTips_nums()
return self.m_pDC:GetFileRecordNums(19)
end
function GameScheme:HelpTips_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(19, 0, pKey);
end
function GameScheme:LotteryBox(iIndex)
return self.m_pDC:GetRecordByIndex(20, iIndex)
end
function GameScheme:LotteryBox_nums()
return self.m_pDC:GetFileRecordNums(20)
end
function GameScheme:LotteryBox_0(iBoxID)
local pKey={iBoxID=iBoxID}
return self.m_pDC:GetRecord(20, 0, pKey);
end
function GameScheme:Reward(iIndex)
return self.m_pDC:GetRecordByIndex(21, iIndex)
end
function GameScheme:Reward_nums()
return self.m_pDC:GetFileRecordNums(21)
end
function GameScheme:Reward_0(iRewardID)
local pKey={iRewardID=iRewardID}
return self.m_pDC:GetRecord(21, 0, pKey);
end
function GameScheme:LotteryWish(iIndex)
return self.m_pDC:GetRecordByIndex(22, iIndex)
end
function GameScheme:LotteryWish_nums()
return self.m_pDC:GetFileRecordNums(22)
end
function GameScheme:LotteryWish_0(iID)
local pKey={iID=iID}
return self.m_pDC:GetRecord(22, 0, pKey);
end
function GameScheme:LotteryWish_1(iType,iLevel)
local pKey={iType=iType,iLevel=iLevel}
return self.m_pDC:GetRecord(22, 1, pKey);
end
function GameScheme:Lottery(iIndex)
return self.m_pDC:GetRecordByIndex(23, iIndex)
end
function GameScheme:Lottery_nums()
return self.m_pDC:GetFileRecordNums(23)
end
function GameScheme:Lottery_0(iLotteryID)
local pKey={iLotteryID=iLotteryID}
return self.m_pDC:GetRecord(23, 0, pKey);
end
function GameScheme:EquipDevour(iIndex)
return self.m_pDC:GetRecordByIndex(24, iIndex)
end
function GameScheme:EquipDevour_nums()
return self.m_pDC:GetFileRecordNums(24)
end
function GameScheme:EquipDevour_0(grade,level)
local pKey={grade=grade,level=level}
return self.m_pDC:GetRecord(24, 0, pKey);
end
function GameScheme:Shopping(iIndex)
return self.m_pDC:GetRecordByIndex(25, iIndex)
end
function GameScheme:Shopping_nums()
return self.m_pDC:GetFileRecordNums(25)
end
function GameScheme:Shopping_0(TypeID)
local pKey={TypeID=TypeID}
return self.m_pDC:GetRecord(25, 0, pKey);
end
function GameScheme:Modul(iIndex)
return self.m_pDC:GetRecordByIndex(26, iIndex)
end
function GameScheme:Modul_nums()
return self.m_pDC:GetFileRecordNums(26)
end
function GameScheme:Modul_0(modelID)
local pKey={modelID=modelID}
return self.m_pDC:GetRecord(26, 0, pKey);
end
function GameScheme:HeroComposite(iIndex)
return self.m_pDC:GetRecordByIndex(27, iIndex)
end
function GameScheme:HeroComposite_nums()
return self.m_pDC:GetFileRecordNums(27)
end
function GameScheme:HeroComposite_0(heroId,starLv)
local pKey={heroId=heroId,starLv=starLv}
return self.m_pDC:GetRecord(27, 0, pKey);
end
function GameScheme:InitBattleProp(iIndex)
return self.m_pDC:GetRecordByIndex(28, iIndex)
end
function GameScheme:InitBattleProp_nums()
return self.m_pDC:GetFileRecordNums(28)
end
function GameScheme:InitBattleProp_0(iIndex)
local pKey={iIndex=iIndex}
return self.m_pDC:GetRecord(28, 0, pKey);
end
function GameScheme:EquipmentProRand(iIndex)
return self.m_pDC:GetRecordByIndex(29, iIndex)
end
function GameScheme:EquipmentProRand_nums()
return self.m_pDC:GetFileRecordNums(29)
end
function GameScheme:EquipmentProRand_0(randID)
local pKey={randID=randID}
return self.m_pDC:GetRecord(29, 0, pKey);
end
function GameScheme:Task(iIndex)
return self.m_pDC:GetRecordByIndex(30, iIndex)
end
function GameScheme:Task_nums()
return self.m_pDC:GetFileRecordNums(30)
end
function GameScheme:Task_0(nTaskID)
local pKey={nTaskID=nTaskID}
return self.m_pDC:GetRecord(30, 0, pKey);
end
function GameScheme:TavernTask(iIndex)
return self.m_pDC:GetRecordByIndex(31, iIndex)
end
function GameScheme:TavernTask_nums()
return self.m_pDC:GetFileRecordNums(31)
end
function GameScheme:TavernTask_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(31, 0, pKey);
end
function GameScheme:Arena(iIndex)
return self.m_pDC:GetRecordByIndex(32, iIndex)
end
function GameScheme:Arena_nums()
return self.m_pDC:GetFileRecordNums(32)
end
function GameScheme:Arena_0(arenaType)
local pKey={arenaType=arenaType}
return self.m_pDC:GetRecord(32, 0, pKey);
end
function GameScheme:IllusionTower(iIndex)
return self.m_pDC:GetRecordByIndex(33, iIndex)
end
function GameScheme:IllusionTower_nums()
return self.m_pDC:GetFileRecordNums(33)
end
function GameScheme:IllusionTower_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(33, 0, pKey);
end
function GameScheme:monsterTeam(iIndex)
return self.m_pDC:GetRecordByIndex(34, iIndex)
end
function GameScheme:monsterTeam_nums()
return self.m_pDC:GetFileRecordNums(34)
end
function GameScheme:monsterTeam_0(Teampid)
local pKey={Teampid=Teampid}
return self.m_pDC:GetRecord(34, 0, pKey);
end
function GameScheme:Achievement(iIndex)
return self.m_pDC:GetRecordByIndex(35, iIndex)
end
function GameScheme:Achievement_nums()
return self.m_pDC:GetFileRecordNums(35)
end
function GameScheme:Achievement_0(nTaskID)
local pKey={nTaskID=nTaskID}
return self.m_pDC:GetRecord(35, 0, pKey);
end
function GameScheme:ActivityContent(iIndex)
return self.m_pDC:GetRecordByIndex(36, iIndex)
end
function GameScheme:ActivityContent_nums()
return self.m_pDC:GetFileRecordNums(36)
end
function GameScheme:ActivityContent_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(36, 0, pKey);
end
function GameScheme:monthlyActivity(iIndex)
return self.m_pDC:GetRecordByIndex(37, iIndex)
end
function GameScheme:monthlyActivity_nums()
return self.m_pDC:GetFileRecordNums(37)
end
function GameScheme:monthlyActivity_0(monthActivityID)
local pKey={monthActivityID=monthActivityID}
return self.m_pDC:GetRecord(37, 0, pKey);
end
function GameScheme:rmbActivity(iIndex)
return self.m_pDC:GetRecordByIndex(38, iIndex)
end
function GameScheme:rmbActivity_nums()
return self.m_pDC:GetFileRecordNums(38)
end
function GameScheme:rmbActivity_0(rmbActivityID)
local pKey={rmbActivityID=rmbActivityID}
return self.m_pDC:GetRecord(38, 0, pKey);
end
function GameScheme:HeroTransformTB(iIndex)
return self.m_pDC:GetRecordByIndex(39, iIndex)
end
function GameScheme:HeroTransformTB_nums()
return self.m_pDC:GetFileRecordNums(39)
end
function GameScheme:HeroTransformTB_0(iType,iStar)
local pKey={iType=iType,iStar=iStar}
return self.m_pDC:GetRecord(39, 0, pKey);
end
function GameScheme:TrialEvent(iIndex)
return self.m_pDC:GetRecordByIndex(40, iIndex)
end
function GameScheme:TrialEvent_nums()
return self.m_pDC:GetFileRecordNums(40)
end
function GameScheme:TrialEvent_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(40, 0, pKey);
end
function GameScheme:ArenaReward(iIndex)
return self.m_pDC:GetRecordByIndex(41, iIndex)
end
function GameScheme:ArenaReward_nums()
return self.m_pDC:GetFileRecordNums(41)
end
function GameScheme:ArenaReward_0(arenaType,rewardType,stageLevel)
local pKey={arenaType=arenaType,rewardType=rewardType,stageLevel=stageLevel}
return self.m_pDC:GetRecord(41, 0, pKey);
end
function GameScheme:TrialLevel(iIndex)
return self.m_pDC:GetRecordByIndex(42, iIndex)
end
function GameScheme:TrialLevel_nums()
return self.m_pDC:GetFileRecordNums(42)
end
function GameScheme:TrialLevel_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(42, 0, pKey);
end
function GameScheme:TrialRefresh(iIndex)
return self.m_pDC:GetRecordByIndex(43, iIndex)
end
function GameScheme:TrialRefresh_nums()
return self.m_pDC:GetFileRecordNums(43)
end
function GameScheme:TrialRefresh_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(43, 0, pKey);
end
function GameScheme:RoleFace(iIndex)
return self.m_pDC:GetRecordByIndex(44, iIndex)
end
function GameScheme:RoleFace_nums()
return self.m_pDC:GetFileRecordNums(44)
end
function GameScheme:RoleFace_0(headID)
local pKey={headID=headID}
return self.m_pDC:GetRecord(44, 0, pKey);
end
function GameScheme:AshDungeon(iIndex)
return self.m_pDC:GetRecordByIndex(45, iIndex)
end
function GameScheme:AshDungeon_nums()
return self.m_pDC:GetFileRecordNums(45)
end
function GameScheme:AshDungeon_0(AshDungeonId)
local pKey={AshDungeonId=AshDungeonId}
return self.m_pDC:GetRecord(45, 0, pKey);
end
function GameScheme:AshDungeon_1(Mapid,Level)
local pKey={Mapid=Mapid,Level=Level}
return self.m_pDC:GetRecord(45, 1, pKey);
end
function GameScheme:AshMonsterTeam(iIndex)
return self.m_pDC:GetRecordByIndex(46, iIndex)
end
function GameScheme:AshMonsterTeam_nums()
return self.m_pDC:GetFileRecordNums(46)
end
function GameScheme:AshMonsterTeam_0(TeamId)
local pKey={TeamId=TeamId}
return self.m_pDC:GetRecord(46, 0, pKey);
end
function GameScheme:SpecialChar(iIndex)
return self.m_pDC:GetRecordByIndex(47, iIndex)
end
function GameScheme:SpecialChar_nums()
return self.m_pDC:GetFileRecordNums(47)
end
function GameScheme:SpecialChar_0(typeId)
local pKey={typeId=typeId}
return self.m_pDC:GetRecord(47, 0, pKey);
end
function GameScheme:dailyAttendance(iIndex)
return self.m_pDC:GetRecordByIndex(48, iIndex)
end
function GameScheme:dailyAttendance_nums()
return self.m_pDC:GetFileRecordNums(48)
end
function GameScheme:dailyAttendance_0(dailyAttendanceID)
local pKey={dailyAttendanceID=dailyAttendanceID}
return self.m_pDC:GetRecord(48, 0, pKey);
end
function GameScheme:TrialMonsterTeam(iIndex)
return self.m_pDC:GetRecordByIndex(49, iIndex)
end
function GameScheme:TrialMonsterTeam_nums()
return self.m_pDC:GetFileRecordNums(49)
end
function GameScheme:TrialMonsterTeam_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(49, 0, pKey);
end
function GameScheme:RoleUpgrade(iIndex)
return self.m_pDC:GetRecordByIndex(50, iIndex)
end
function GameScheme:RoleUpgrade_nums()
return self.m_pDC:GetFileRecordNums(50)
end
function GameScheme:RoleUpgrade_0(roleLv)
local pKey={roleLv=roleLv}
return self.m_pDC:GetRecord(50, 0, pKey);
end
function GameScheme:Mail(iIndex)
return self.m_pDC:GetRecordByIndex(51, iIndex)
end
function GameScheme:Mail_nums()
return self.m_pDC:GetFileRecordNums(51)
end
function GameScheme:Mail_0(mailTempletID)
local pKey={mailTempletID=mailTempletID}
return self.m_pDC:GetRecord(51, 0, pKey);
end
function GameScheme:menu(iIndex)
return self.m_pDC:GetRecordByIndex(52, iIndex)
end
function GameScheme:menu_nums()
return self.m_pDC:GetFileRecordNums(52)
end
function GameScheme:menu_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(52, 0, pKey);
end
function GameScheme:PowerCoefficient(iIndex)
return self.m_pDC:GetRecordByIndex(53, iIndex)
end
function GameScheme:PowerCoefficient_nums()
return self.m_pDC:GetFileRecordNums(53)
end
function GameScheme:PowerCoefficient_0(prof,starLv)
local pKey={prof=prof,starLv=starLv}
return self.m_pDC:GetRecord(53, 0, pKey);
end
function GameScheme:heroDecompose(iIndex)
return self.m_pDC:GetRecordByIndex(54, iIndex)
end
function GameScheme:heroDecompose_nums()
return self.m_pDC:GetFileRecordNums(54)
end
function GameScheme:heroDecompose_0(heroStarLv,heroAdvanceLv)
local pKey={heroStarLv=heroStarLv,heroAdvanceLv=heroAdvanceLv}
return self.m_pDC:GetRecord(54, 0, pKey);
end
function GameScheme:AllianceTechnology(iIndex)
return self.m_pDC:GetRecordByIndex(55, iIndex)
end
function GameScheme:AllianceTechnology_nums()
return self.m_pDC:GetFileRecordNums(55)
end
function GameScheme:AllianceTechnology_0(GuildSkillId,profession,level)
local pKey={GuildSkillId=GuildSkillId,profession=profession,level=level}
return self.m_pDC:GetRecord(55, 0, pKey);
end
function GameScheme:LeagueIcon(iIndex)
return self.m_pDC:GetRecordByIndex(56, iIndex)
end
function GameScheme:LeagueIcon_nums()
return self.m_pDC:GetFileRecordNums(56)
end
function GameScheme:LeagueIcon_0(headID)
local pKey={headID=headID}
return self.m_pDC:GetRecord(56, 0, pKey);
end
function GameScheme:ActivityParam(iIndex)
return self.m_pDC:GetRecordByIndex(57, iIndex)
end
function GameScheme:ActivityParam_nums()
return self.m_pDC:GetFileRecordNums(57)
end
function GameScheme:ActivityParam_0(iIndex)
local pKey={iIndex=iIndex}
return self.m_pDC:GetRecord(57, 0, pKey);
end
function GameScheme:LeagueCompCell(iIndex)
return self.m_pDC:GetRecordByIndex(58, iIndex)
end
function GameScheme:LeagueCompCell_nums()
return self.m_pDC:GetFileRecordNums(58)
end
function GameScheme:LeagueCompCell_0(nCellID,nMapLevel)
local pKey={nCellID=nCellID,nMapLevel=nMapLevel}
return self.m_pDC:GetRecord(58, 0, pKey);
end
function GameScheme:LeagueCompCellRefresh(iIndex)
return self.m_pDC:GetRecordByIndex(59, iIndex)
end
function GameScheme:LeagueCompCellRefresh_nums()
return self.m_pDC:GetFileRecordNums(59)
end
function GameScheme:LeagueCompCellRefresh_0(nCellRefreshID)
local pKey={nCellRefreshID=nCellRefreshID}
return self.m_pDC:GetRecord(59, 0, pKey);
end
function GameScheme:LeagueCompDistance(iIndex)
return self.m_pDC:GetRecordByIndex(60, iIndex)
end
function GameScheme:LeagueCompDistance_nums()
return self.m_pDC:GetFileRecordNums(60)
end
function GameScheme:LeagueCompDistance_0(nDistanceIndex)
local pKey={nDistanceIndex=nDistanceIndex}
return self.m_pDC:GetRecord(60, 0, pKey);
end
function GameScheme:LeagueCompGrid(iIndex)
return self.m_pDC:GetRecordByIndex(61, iIndex)
end
function GameScheme:LeagueCompGrid_nums()
return self.m_pDC:GetFileRecordNums(61)
end
function GameScheme:LeagueCompGrid_0(nGridID)
local pKey={nGridID=nGridID}
return self.m_pDC:GetRecord(61, 0, pKey);
end
function GameScheme:LeagueCompIntegral(iIndex)
return self.m_pDC:GetRecordByIndex(62, iIndex)
end
function GameScheme:LeagueCompIntegral_nums()
return self.m_pDC:GetFileRecordNums(62)
end
function GameScheme:LeagueCompIntegral_0(nIntegralIndex)
local pKey={nIntegralIndex=nIntegralIndex}
return self.m_pDC:GetRecord(62, 0, pKey);
end
function GameScheme:LeagueCompMajorCity(iIndex)
return self.m_pDC:GetRecordByIndex(63, iIndex)
end
function GameScheme:LeagueCompMajorCity_nums()
return self.m_pDC:GetFileRecordNums(63)
end
function GameScheme:LeagueCompMajorCity_0(nMajorCityLevel,nMapLevel)
local pKey={nMajorCityLevel=nMajorCityLevel,nMapLevel=nMapLevel}
return self.m_pDC:GetRecord(63, 0, pKey);
end
function GameScheme:LeagueCompAchievement(iIndex)
return self.m_pDC:GetRecordByIndex(64, iIndex)
end
function GameScheme:LeagueCompAchievement_nums()
return self.m_pDC:GetFileRecordNums(64)
end
function GameScheme:LeagueCompAchievement_0(nTaskID)
local pKey={nTaskID=nTaskID}
return self.m_pDC:GetRecord(64, 0, pKey);
end
function GameScheme:LeagueCompReward(iIndex)
return self.m_pDC:GetRecordByIndex(65, iIndex)
end
function GameScheme:LeagueCompReward_nums()
return self.m_pDC:GetFileRecordNums(65)
end
function GameScheme:LeagueCompReward_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(65, 0, pKey);
end
function GameScheme:LeagueCompReward_1(nUnionRank,nMajorCityLevel)
local pKey={nUnionRank=nUnionRank,nMajorCityLevel=nMajorCityLevel}
return self.m_pDC:GetRecord(65, 1, pKey);
end
function GameScheme:leagueMillOrder(iIndex)
return self.m_pDC:GetRecordByIndex(66, iIndex)
end
function GameScheme:leagueMillOrder_nums()
return self.m_pDC:GetFileRecordNums(66)
end
function GameScheme:leagueMillOrder_0(Level)
local pKey={Level=Level}
return self.m_pDC:GetRecord(66, 0, pKey);
end
function GameScheme:leagueMill(iIndex)
return self.m_pDC:GetRecordByIndex(67, iIndex)
end
function GameScheme:leagueMill_nums()
return self.m_pDC:GetFileRecordNums(67)
end
function GameScheme:leagueMill_0(Level)
local pKey={Level=Level}
return self.m_pDC:GetRecord(67, 0, pKey);
end
function GameScheme:IllusionTowerMopUp(iIndex)
return self.m_pDC:GetRecordByIndex(68, iIndex)
end
function GameScheme:IllusionTowerMopUp_nums()
return self.m_pDC:GetFileRecordNums(68)
end
function GameScheme:IllusionTowerMopUp_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(68, 0, pKey);
end
function GameScheme:VipPrivilege(iIndex)
return self.m_pDC:GetRecordByIndex(69, iIndex)
end
function GameScheme:VipPrivilege_nums()
return self.m_pDC:GetFileRecordNums(69)
end
function GameScheme:VipPrivilege_0(iVipLevel)
local pKey={iVipLevel=iVipLevel}
return self.m_pDC:GetRecord(69, 0, pKey);
end
function GameScheme:Recharge(iIndex)
return self.m_pDC:GetRecordByIndex(70, iIndex)
end
function GameScheme:Recharge_nums()
return self.m_pDC:GetFileRecordNums(70)
end
function GameScheme:Recharge_0(iGoodsID)
local pKey={iGoodsID=iGoodsID}
return self.m_pDC:GetRecord(70, 0, pKey);
end
function GameScheme:Boss(iIndex)
return self.m_pDC:GetRecordByIndex(71, iIndex)
end
function GameScheme:Boss_nums()
return self.m_pDC:GetFileRecordNums(71)
end
function GameScheme:Boss_0(BossId)
local pKey={BossId=BossId}
return self.m_pDC:GetRecord(71, 0, pKey);
end
function GameScheme:StrFun(iIndex)
return self.m_pDC:GetRecordByIndex(72, iIndex)
end
function GameScheme:StrFun_nums()
return self.m_pDC:GetFileRecordNums(72)
end
function GameScheme:StrFun_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(72, 0, pKey);
end
function GameScheme:HeroReplace(iIndex)
return self.m_pDC:GetRecordByIndex(73, iIndex)
end
function GameScheme:HeroReplace_nums()
return self.m_pDC:GetFileRecordNums(73)
end
function GameScheme:HeroReplace_0(iIndex,heroType,heroStar)
local pKey={iIndex=iIndex,heroType=heroType,heroStar=heroStar}
return self.m_pDC:GetRecord(73, 0, pKey);
end
function GameScheme:LeagueTreasureFlower(iIndex)
return self.m_pDC:GetRecordByIndex(74, iIndex)
end
function GameScheme:LeagueTreasureFlower_nums()
return self.m_pDC:GetFileRecordNums(74)
end
function GameScheme:LeagueTreasureFlower_0(GroupId)
local pKey={GroupId=GroupId}
return self.m_pDC:GetRecord(74, 0, pKey);
end
function GameScheme:GoldenHand(iIndex)
return self.m_pDC:GetRecordByIndex(75, iIndex)
end
function GameScheme:GoldenHand_nums()
return self.m_pDC:GetFileRecordNums(75)
end
function GameScheme:GoldenHand_0(iGoodsID)
local pKey={iGoodsID=iGoodsID}
return self.m_pDC:GetRecord(75, 0, pKey);
end
function GameScheme:keyword(iIndex)
return self.m_pDC:GetRecordByIndex(76, iIndex)
end
function GameScheme:keyword_nums()
return self.m_pDC:GetFileRecordNums(76)
end
function GameScheme:keyword_0(iID)
local pKey={iID=iID}
return self.m_pDC:GetRecord(76, 0, pKey);
end
function GameScheme:HaloUpgrade(iIndex)
return self.m_pDC:GetRecordByIndex(77, iIndex)
end
function GameScheme:HaloUpgrade_nums()
return self.m_pDC:GetFileRecordNums(77)
end
function GameScheme:HaloUpgrade_0(campID,Lv)
local pKey={campID=campID,Lv=Lv}
return self.m_pDC:GetRecord(77, 0, pKey);
end
function GameScheme:HaloUnLock(iIndex)
return self.m_pDC:GetRecordByIndex(78, iIndex)
end
function GameScheme:HaloUnLock_nums()
return self.m_pDC:GetFileRecordNums(78)
end
function GameScheme:HaloUnLock_0(haloID)
local pKey={haloID=haloID}
return self.m_pDC:GetRecord(78, 0, pKey);
end
function GameScheme:LeagueTreasureReward(iIndex)
return self.m_pDC:GetRecordByIndex(79, iIndex)
end
function GameScheme:LeagueTreasureReward_nums()
return self.m_pDC:GetFileRecordNums(79)
end
function GameScheme:LeagueTreasureReward_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(79, 0, pKey);
end
function GameScheme:ForceGuide(iIndex)
return self.m_pDC:GetRecordByIndex(80, iIndex)
end
function GameScheme:ForceGuide_nums()
return self.m_pDC:GetFileRecordNums(80)
end
function GameScheme:ForceGuide_0(guideId)
local pKey={guideId=guideId}
return self.m_pDC:GetRecord(80, 0, pKey);
end
function GameScheme:GuideStep(iIndex)
return self.m_pDC:GetRecordByIndex(81, iIndex)
end
function GameScheme:GuideStep_nums()
return self.m_pDC:GetFileRecordNums(81)
end
function GameScheme:GuideStep_0(stepId)
local pKey={stepId=stepId}
return self.m_pDC:GetRecord(81, 0, pKey);
end
function GameScheme:BtnTips(iIndex)
return self.m_pDC:GetRecordByIndex(82, iIndex)
end
function GameScheme:BtnTips_nums()
return self.m_pDC:GetFileRecordNums(82)
end
function GameScheme:BtnTips_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(82, 0, pKey);
end
function GameScheme:BubbleSkill(iIndex)
return self.m_pDC:GetRecordByIndex(83, iIndex)
end
function GameScheme:BubbleSkill_nums()
return self.m_pDC:GetFileRecordNums(83)
end
function GameScheme:BubbleSkill_0(nSkillID)
local pKey={nSkillID=nSkillID}
return self.m_pDC:GetRecord(83, 0, pKey);
end
function GameScheme:BubbleHero(iIndex)
return self.m_pDC:GetRecordByIndex(84, iIndex)
end
function GameScheme:BubbleHero_nums()
return self.m_pDC:GetFileRecordNums(84)
end
function GameScheme:BubbleHero_0(nHeroID)
local pKey={nHeroID=nHeroID}
return self.m_pDC:GetRecord(84, 0, pKey);
end
function GameScheme:FloatText(iIndex)
return self.m_pDC:GetRecordByIndex(85, iIndex)
end
function GameScheme:FloatText_nums()
return self.m_pDC:GetFileRecordNums(85)
end
function GameScheme:FloatText_0(nFloatTextID)
local pKey={nFloatTextID=nFloatTextID}
return self.m_pDC:GetRecord(85, 0, pKey);
end
function GameScheme:Particle(iIndex)
return self.m_pDC:GetRecordByIndex(86, iIndex)
end
function GameScheme:Particle_nums()
return self.m_pDC:GetFileRecordNums(86)
end
function GameScheme:Particle_0(nParticleID)
local pKey={nParticleID=nParticleID}
return self.m_pDC:GetRecord(86, 0, pKey);
end
function GameScheme:PlotStep(iIndex)
return self.m_pDC:GetRecordByIndex(87, iIndex)
end
function GameScheme:PlotStep_nums()
return self.m_pDC:GetFileRecordNums(87)
end
function GameScheme:PlotStep_0(iStepID)
local pKey={iStepID=iStepID}
return self.m_pDC:GetRecord(87, 0, pKey);
end
function GameScheme:Tips(iIndex)
return self.m_pDC:GetRecordByIndex(88, iIndex)
end
function GameScheme:Tips_nums()
return self.m_pDC:GetFileRecordNums(88)
end
function GameScheme:Tips_0(nTipID)
local pKey={nTipID=nTipID}
return self.m_pDC:GetRecord(88, 0, pKey);
end
function GameScheme:MagicBotany(iIndex)
return self.m_pDC:GetRecordByIndex(89, iIndex)
end
function GameScheme:MagicBotany_nums()
return self.m_pDC:GetFileRecordNums(89)
end
function GameScheme:MagicBotany_0(botanyId,Lv)
local pKey={botanyId=botanyId,Lv=Lv}
return self.m_pDC:GetRecord(89, 0, pKey);
end
function GameScheme:MagicMain(iIndex)
return self.m_pDC:GetRecordByIndex(90, iIndex)
end
function GameScheme:MagicMain_nums()
return self.m_pDC:GetFileRecordNums(90)
end
function GameScheme:MagicMain_0(mainCityLv)
local pKey={mainCityLv=mainCityLv}
return self.m_pDC:GetRecord(90, 0, pKey);
end
function GameScheme:MagicWeapon(iIndex)
return self.m_pDC:GetRecordByIndex(91, iIndex)
end
function GameScheme:MagicWeapon_nums()
return self.m_pDC:GetFileRecordNums(91)
end
function GameScheme:MagicWeapon_0(weaponID,weaponType,weaponLv,UpgradeStage)
local pKey={weaponID=weaponID,weaponType=weaponType,weaponLv=weaponLv,UpgradeStage=UpgradeStage}
return self.m_pDC:GetRecord(91, 0, pKey);
end
function GameScheme:MagicMate(iIndex)
return self.m_pDC:GetRecordByIndex(92, iIndex)
end
function GameScheme:MagicMate_nums()
return self.m_pDC:GetFileRecordNums(92)
end
function GameScheme:MagicMate_0(mateLv)
local pKey={mateLv=mateLv}
return self.m_pDC:GetRecord(92, 0, pKey);
end
function GameScheme:Community(iIndex)
return self.m_pDC:GetRecordByIndex(93, iIndex)
end
function GameScheme:Community_nums()
return self.m_pDC:GetFileRecordNums(93)
end
function GameScheme:Community_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(93, 0, pKey);
end
function GameScheme:UnforcedGuide(iIndex)
return self.m_pDC:GetRecordByIndex(94, iIndex)
end
function GameScheme:UnforcedGuide_nums()
return self.m_pDC:GetFileRecordNums(94)
end
function GameScheme:UnforcedGuide_0(guideID,GuideGroupID)
local pKey={guideID=guideID,GuideGroupID=GuideGroupID}
return self.m_pDC:GetRecord(94, 0, pKey);
end
function GameScheme:UnforcedGuide_1(GuideGroupID,flag)
local pKey={GuideGroupID=GuideGroupID,flag=flag}
return self.m_pDC:GetRecord(94, 1, pKey);
end
function GameScheme:UnforcedGuideStep(iIndex)
return self.m_pDC:GetRecordByIndex(95, iIndex)
end
function GameScheme:UnforcedGuideStep_nums()
return self.m_pDC:GetFileRecordNums(95)
end
function GameScheme:UnforcedGuideStep_0(stepId)
local pKey={stepId=stepId}
return self.m_pDC:GetRecord(95, 0, pKey);
end
function GameScheme:scoreArray(iIndex)
return self.m_pDC:GetRecordByIndex(96, iIndex)
end
function GameScheme:scoreArray_nums()
return self.m_pDC:GetFileRecordNums(96)
end
function GameScheme:scoreArray_0(arrayLevel)
local pKey={arrayLevel=arrayLevel}
return self.m_pDC:GetRecord(96, 0, pKey);
end
function GameScheme:scoreGem(iIndex)
return self.m_pDC:GetRecordByIndex(97, iIndex)
end
function GameScheme:scoreGem_nums()
return self.m_pDC:GetFileRecordNums(97)
end
function GameScheme:scoreGem_0(gemGrande,hgemLevel)
local pKey={gemGrande=gemGrande,hgemLevel=hgemLevel}
return self.m_pDC:GetRecord(97, 0, pKey);
end
function GameScheme:scoreHero(iIndex)
return self.m_pDC:GetRecordByIndex(98, iIndex)
end
function GameScheme:scoreHero_nums()
return self.m_pDC:GetFileRecordNums(98)
end
function GameScheme:scoreHero_0(heroStar,heroLevel)
local pKey={heroStar=heroStar,heroLevel=heroLevel}
return self.m_pDC:GetRecord(98, 0, pKey);
end
function GameScheme:scoreSkill(iIndex)
return self.m_pDC:GetRecordByIndex(99, iIndex)
end
function GameScheme:scoreSkill_nums()
return self.m_pDC:GetFileRecordNums(99)
end
function GameScheme:scoreSkill_0(skillLevel)
local pKey={skillLevel=skillLevel}
return self.m_pDC:GetRecord(99, 0, pKey);
end
function GameScheme:scoreStandard(iIndex)
return self.m_pDC:GetRecordByIndex(100, iIndex)
end
function GameScheme:scoreStandard_nums()
return self.m_pDC:GetFileRecordNums(100)
end
function GameScheme:scoreStandard_0(roleLevel)
local pKey={roleLevel=roleLevel}
return self.m_pDC:GetRecord(100, 0, pKey);
end
function GameScheme:scoreTech(iIndex)
return self.m_pDC:GetRecordByIndex(101, iIndex)
end
function GameScheme:scoreTech_nums()
return self.m_pDC:GetFileRecordNums(101)
end
function GameScheme:scoreTech_0(techLevel)
local pKey={techLevel=techLevel}
return self.m_pDC:GetRecord(101, 0, pKey);
end
function GameScheme:scoreWeapon(iIndex)
return self.m_pDC:GetRecordByIndex(102, iIndex)
end
function GameScheme:scoreWeapon_nums()
return self.m_pDC:GetFileRecordNums(102)
end
function GameScheme:scoreWeapon_0(techLevel)
local pKey={techLevel=techLevel}
return self.m_pDC:GetRecord(102, 0, pKey);
end
function GameScheme:GuideRadar(iIndex)
return self.m_pDC:GetRecordByIndex(103, iIndex)
end
function GameScheme:GuideRadar_nums()
return self.m_pDC:GetFileRecordNums(103)
end
function GameScheme:GuideRadar_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(103, 0, pKey);
end
function GameScheme:BubbleMap(iIndex)
return self.m_pDC:GetRecordByIndex(104, iIndex)
end
function GameScheme:BubbleMap_nums()
return self.m_pDC:GetFileRecordNums(104)
end
function GameScheme:BubbleMap_0(nBubbleID)
local pKey={nBubbleID=nBubbleID}
return self.m_pDC:GetRecord(104, 0, pKey);
end
function GameScheme:weeklyTask(iIndex)
return self.m_pDC:GetRecordByIndex(105, iIndex)
end
function GameScheme:weeklyTask_nums()
return self.m_pDC:GetFileRecordNums(105)
end
function GameScheme:weeklyTask_0(nTaskID)
local pKey={nTaskID=nTaskID}
return self.m_pDC:GetRecord(105, 0, pKey);
end
function GameScheme:welfareActivity(iIndex)
return self.m_pDC:GetRecordByIndex(106, iIndex)
end
function GameScheme:welfareActivity_nums()
return self.m_pDC:GetFileRecordNums(106)
end
function GameScheme:welfareActivity_0(welfareActivityID)
local pKey={welfareActivityID=welfareActivityID}
return self.m_pDC:GetRecord(106, 0, pKey);
end
function GameScheme:welfareActivity_1(headingCode)
local pKey={headingCode=headingCode}
return self.m_pDC:GetRecord(106, 1, pKey);
end
function GameScheme:BubbleHeroTips(iIndex)
return self.m_pDC:GetRecordByIndex(107, iIndex)
end
function GameScheme:BubbleHeroTips_nums()
return self.m_pDC:GetFileRecordNums(107)
end
function GameScheme:BubbleHeroTips_0(nameID)
local pKey={nameID=nameID}
return self.m_pDC:GetRecord(107, 0, pKey);
end
function GameScheme:TargetTask(iIndex)
return self.m_pDC:GetRecordByIndex(108, iIndex)
end
function GameScheme:TargetTask_nums()
return self.m_pDC:GetFileRecordNums(108)
end
function GameScheme:TargetTask_0(nTaskID)
local pKey={nTaskID=nTaskID}
return self.m_pDC:GetRecord(108, 0, pKey);
end
function GameScheme:TargetTask_1(nTaskIndex)
local pKey={nTaskIndex=nTaskIndex}
return self.m_pDC:GetRecord(108, 1, pKey);
end
function GameScheme:TargetTaskSub(iIndex)
return self.m_pDC:GetRecordByIndex(109, iIndex)
end
function GameScheme:TargetTaskSub_nums()
return self.m_pDC:GetFileRecordNums(109)
end
function GameScheme:TargetTaskSub_0(nTaskID)
local pKey={nTaskID=nTaskID}
return self.m_pDC:GetRecord(109, 0, pKey);
end
function GameScheme:MoneyExchange(iIndex)
return self.m_pDC:GetRecordByIndex(110, iIndex)
end
function GameScheme:MoneyExchange_nums()
return self.m_pDC:GetFileRecordNums(110)
end
function GameScheme:MoneyExchange_0(zh)
local pKey={zh=zh}
return self.m_pDC:GetRecord(110, 0, pKey);
end
function GameScheme:SevenDayAttendance(iIndex)
return self.m_pDC:GetRecordByIndex(111, iIndex)
end
function GameScheme:SevenDayAttendance_nums()
return self.m_pDC:GetFileRecordNums(111)
end
function GameScheme:SevenDayAttendance_0(dailyAttendanceID)
local pKey={dailyAttendanceID=dailyAttendanceID}
return self.m_pDC:GetRecord(111, 0, pKey);
end
function GameScheme:Broadcast(iIndex)
return self.m_pDC:GetRecordByIndex(112, iIndex)
end
function GameScheme:Broadcast_nums()
return self.m_pDC:GetFileRecordNums(112)
end
function GameScheme:Broadcast_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(112, 0, pKey);
end
function GameScheme:ChallengeList(iIndex)
return self.m_pDC:GetRecordByIndex(113, iIndex)
end
function GameScheme:ChallengeList_nums()
return self.m_pDC:GetFileRecordNums(113)
end
function GameScheme:ChallengeList_0(nDayNum)
local pKey={nDayNum=nDayNum}
return self.m_pDC:GetRecord(113, 0, pKey);
end
function GameScheme:ChallengeReward(iIndex)
return self.m_pDC:GetRecordByIndex(114, iIndex)
end
function GameScheme:ChallengeReward_nums()
return self.m_pDC:GetFileRecordNums(114)
end
function GameScheme:ChallengeReward_0(nID)
local pKey={nID=nID}
return self.m_pDC:GetRecord(114, 0, pKey);
end
function GameScheme:ChallengeReward_1(nNeedScore,nWeek)
local pKey={nNeedScore=nNeedScore,nWeek=nWeek}
return self.m_pDC:GetRecord(114, 1, pKey);
end
function GameScheme:ChallengeReward_2(AtyID,nRewardIndex)
local pKey={AtyID=AtyID,nRewardIndex=nRewardIndex}
return self.m_pDC:GetRecord(114, 2, pKey);
end
function GameScheme:SubsequentRewardRule(iIndex)
return self.m_pDC:GetRecordByIndex(115, iIndex)
end
function GameScheme:SubsequentRewardRule_nums()
return self.m_pDC:GetFileRecordNums(115)
end
function GameScheme:SubsequentRewardRule_0(RuleID)
local pKey={RuleID=RuleID}
return self.m_pDC:GetRecord(115, 0, pKey);
end
function GameScheme:Notice(iIndex)
return self.m_pDC:GetRecordByIndex(116, iIndex)
end
function GameScheme:Notice_nums()
return self.m_pDC:GetFileRecordNums(116)
end
function GameScheme:Notice_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(116, 0, pKey);
end
function GameScheme:NodeBubbleTips(iIndex)
return self.m_pDC:GetRecordByIndex(117, iIndex)
end
function GameScheme:NodeBubbleTips_nums()
return self.m_pDC:GetFileRecordNums(117)
end
function GameScheme:NodeBubbleTips_0(checkPoint)
local pKey={checkPoint=checkPoint}
return self.m_pDC:GetRecord(117, 0, pKey);
end
function GameScheme:LoadingHeroTips(iIndex)
return self.m_pDC:GetRecordByIndex(118, iIndex)
end
function GameScheme:LoadingHeroTips_nums()
return self.m_pDC:GetFileRecordNums(118)
end
function GameScheme:LoadingHeroTips_0(NumID)
local pKey={NumID=NumID}
return self.m_pDC:GetRecord(118, 0, pKey);
end
function GameScheme:EventTips(iIndex)
return self.m_pDC:GetRecordByIndex(119, iIndex)
end
function GameScheme:EventTips_nums()
return self.m_pDC:GetFileRecordNums(119)
end
function GameScheme:EventTips_0(tipsID)
local pKey={tipsID=tipsID}
return self.m_pDC:GetRecord(119, 0, pKey);
end
function GameScheme:AvatarFrame(iIndex)
return self.m_pDC:GetRecordByIndex(120, iIndex)
end
function GameScheme:AvatarFrame_nums()
return self.m_pDC:GetFileRecordNums(120)
end
function GameScheme:AvatarFrame_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(120, 0, pKey);
end
function GameScheme:NameIcon(iIndex)
return self.m_pDC:GetRecordByIndex(121, iIndex)
end
function GameScheme:NameIcon_nums()
return self.m_pDC:GetFileRecordNums(121)
end
function GameScheme:NameIcon_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(121, 0, pKey);
end
function GameScheme:TimeRewardItem(iIndex)
return self.m_pDC:GetRecordByIndex(122, iIndex)
end
function GameScheme:TimeRewardItem_nums()
return self.m_pDC:GetFileRecordNums(122)
end
function GameScheme:TimeRewardItem_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(122, 0, pKey);
end
function GameScheme:SpecialGift(iIndex)
return self.m_pDC:GetRecordByIndex(123, iIndex)
end
function GameScheme:SpecialGift_nums()
return self.m_pDC:GetFileRecordNums(123)
end
function GameScheme:SpecialGift_0(GiftID)
local pKey={GiftID=GiftID}
return self.m_pDC:GetRecord(123, 0, pKey);
end
function GameScheme:HeroRankingList(iIndex)
return self.m_pDC:GetRecordByIndex(124, iIndex)
end
function GameScheme:HeroRankingList_nums()
return self.m_pDC:GetFileRecordNums(124)
end
function GameScheme:HeroRankingList_0(heroID)
local pKey={heroID=heroID}
return self.m_pDC:GetRecord(124, 0, pKey);
end
function GameScheme:TopSquadList(iIndex)
return self.m_pDC:GetRecordByIndex(125, iIndex)
end
function GameScheme:TopSquadList_nums()
return self.m_pDC:GetFileRecordNums(125)
end
function GameScheme:TopSquadList_0(teamID)
local pKey={teamID=teamID}
return self.m_pDC:GetRecord(125, 0, pKey);
end
function GameScheme:LeagueCompCellWall(iIndex)
return self.m_pDC:GetRecordByIndex(126, iIndex)
end
function GameScheme:LeagueCompCellWall_nums()
return self.m_pDC:GetFileRecordNums(126)
end
function GameScheme:LeagueCompCellWall_0(nWallId)
local pKey={nWallId=nWallId}
return self.m_pDC:GetRecord(126, 0, pKey);
end
function GameScheme:ChatTextColor(iIndex)
return self.m_pDC:GetRecordByIndex(127, iIndex)
end
function GameScheme:ChatTextColor_nums()
return self.m_pDC:GetFileRecordNums(127)
end
function GameScheme:ChatTextColor_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(127, 0, pKey);
end
function GameScheme:MazeLevel(iIndex)
return self.m_pDC:GetRecordByIndex(128, iIndex)
end
function GameScheme:MazeLevel_nums()
return self.m_pDC:GetFileRecordNums(128)
end
function GameScheme:MazeLevel_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(128, 0, pKey);
end
function GameScheme:MazeRefresh(iIndex)
return self.m_pDC:GetRecordByIndex(129, iIndex)
end
function GameScheme:MazeRefresh_nums()
return self.m_pDC:GetFileRecordNums(129)
end
function GameScheme:MazeRefresh_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(129, 0, pKey);
end
function GameScheme:MazeMercenary(iIndex)
return self.m_pDC:GetRecordByIndex(130, iIndex)
end
function GameScheme:MazeMercenary_nums()
return self.m_pDC:GetFileRecordNums(130)
end
function GameScheme:MazeMercenary_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(130, 0, pKey);
end
function GameScheme:RankAchv(iIndex)
return self.m_pDC:GetRecordByIndex(131, iIndex)
end
function GameScheme:RankAchv_nums()
return self.m_pDC:GetFileRecordNums(131)
end
function GameScheme:RankAchv_0(nRankType,nRankSubType)
local pKey={nRankType=nRankType,nRankSubType=nRankSubType}
return self.m_pDC:GetRecord(131, 0, pKey);
end
function GameScheme:MazePos(iIndex)
return self.m_pDC:GetRecordByIndex(132, iIndex)
end
function GameScheme:MazePos_nums()
return self.m_pDC:GetFileRecordNums(132)
end
function GameScheme:MazePos_0(Index)
local pKey={Index=Index}
return self.m_pDC:GetRecord(132, 0, pKey);
end
function GameScheme:MazePos_1(x,y)
local pKey={x=x,y=y}
return self.m_pDC:GetRecord(132, 1, pKey);
end
function GameScheme:TrialTreasure(iIndex)
return self.m_pDC:GetRecordByIndex(133, iIndex)
end
function GameScheme:TrialTreasure_nums()
return self.m_pDC:GetFileRecordNums(133)
end
function GameScheme:TrialTreasure_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(133, 0, pKey);
end
function GameScheme:TrialTreasure_1(itemID)
local pKey={itemID=itemID}
return self.m_pDC:GetRecord(133, 1, pKey);
end
function GameScheme:Passport(iIndex)
return self.m_pDC:GetRecordByIndex(134, iIndex)
end
function GameScheme:Passport_nums()
return self.m_pDC:GetFileRecordNums(134)
end
function GameScheme:Passport_0(nPassportID)
local pKey={nPassportID=nPassportID}
return self.m_pDC:GetRecord(134, 0, pKey);
end
function GameScheme:SubsequentRewardTask(iIndex)
return self.m_pDC:GetRecordByIndex(135, iIndex)
end
function GameScheme:SubsequentRewardTask_nums()
return self.m_pDC:GetFileRecordNums(135)
end
function GameScheme:SubsequentRewardTask_0(TaskID)
local pKey={TaskID=TaskID}
return self.m_pDC:GetRecord(135, 0, pKey);
end
function GameScheme:AdvArenaReward(iIndex)
return self.m_pDC:GetRecordByIndex(136, iIndex)
end
function GameScheme:AdvArenaReward_nums()
return self.m_pDC:GetFileRecordNums(136)
end
function GameScheme:AdvArenaReward_0(StageID)
local pKey={StageID=StageID}
return self.m_pDC:GetRecord(136, 0, pKey);
end
function GameScheme:ShopRefresh(iIndex)
return self.m_pDC:GetRecordByIndex(137, iIndex)
end
function GameScheme:ShopRefresh_nums()
return self.m_pDC:GetFileRecordNums(137)
end
function GameScheme:ShopRefresh_0(shopType)
local pKey={shopType=shopType}
return self.m_pDC:GetRecord(137, 0, pKey);
end
function GameScheme:MazeMonsterTeam(iIndex)
return self.m_pDC:GetRecordByIndex(138, iIndex)
end
function GameScheme:MazeMonsterTeam_nums()
return self.m_pDC:GetFileRecordNums(138)
end
function GameScheme:MazeMonsterTeam_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(138, 0, pKey);
end
function GameScheme:BoxChoose(iIndex)
return self.m_pDC:GetRecordByIndex(139, iIndex)
end
function GameScheme:BoxChoose_nums()
return self.m_pDC:GetFileRecordNums(139)
end
function GameScheme:BoxChoose_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(139, 0, pKey);
end
function GameScheme:BoxChoose_1(nIndexID)
local pKey={nIndexID=nIndexID}
return self.m_pDC:GetRecord(139, 1, pKey);
end
function GameScheme:SummonReward(iIndex)
return self.m_pDC:GetRecordByIndex(140, iIndex)
end
function GameScheme:SummonReward_nums()
return self.m_pDC:GetFileRecordNums(140)
end
function GameScheme:SummonReward_0(stageID)
local pKey={stageID=stageID}
return self.m_pDC:GetRecord(140, 0, pKey);
end
function GameScheme:Slot(iIndex)
return self.m_pDC:GetRecordByIndex(141, iIndex)
end
function GameScheme:Slot_nums()
return self.m_pDC:GetFileRecordNums(141)
end
function GameScheme:Slot_0(slotnumber)
local pKey={slotnumber=slotnumber}
return self.m_pDC:GetRecord(141, 0, pKey);
end
function GameScheme:RoleFrame(iIndex)
return self.m_pDC:GetRecordByIndex(142, iIndex)
end
function GameScheme:RoleFrame_nums()
return self.m_pDC:GetFileRecordNums(142)
end
function GameScheme:RoleFrame_0(frameID)
local pKey={frameID=frameID}
return self.m_pDC:GetRecord(142, 0, pKey);
end
function GameScheme:festivalActivity(iIndex)
return self.m_pDC:GetRecordByIndex(143, iIndex)
end
function GameScheme:festivalActivity_nums()
return self.m_pDC:GetFileRecordNums(143)
end
function GameScheme:festivalActivity_0(cfgID)
local pKey={cfgID=cfgID}
return self.m_pDC:GetRecord(143, 0, pKey);
end
function GameScheme:festivalActivityUI(iIndex)
return self.m_pDC:GetRecordByIndex(144, iIndex)
end
function GameScheme:festivalActivityUI_nums()
return self.m_pDC:GetFileRecordNums(144)
end
function GameScheme:festivalActivityUI_0(AtyID)
local pKey={AtyID=AtyID}
return self.m_pDC:GetRecord(144, 0, pKey);
end
function GameScheme:festivalActivityExtraTask(iIndex)
return self.m_pDC:GetRecordByIndex(145, iIndex)
end
function GameScheme:festivalActivityExtraTask_nums()
return self.m_pDC:GetFileRecordNums(145)
end
function GameScheme:festivalActivityExtraTask_0(TaskID)
local pKey={TaskID=TaskID}
return self.m_pDC:GetRecord(145, 0, pKey);
end
function GameScheme:DivSkillHelper(iIndex)
return self.m_pDC:GetRecordByIndex(146, iIndex)
end
function GameScheme:DivSkillHelper_nums()
return self.m_pDC:GetFileRecordNums(146)
end
function GameScheme:DivSkillHelper_0(occupySkillPoint)
local pKey={occupySkillPoint=occupySkillPoint}
return self.m_pDC:GetRecord(146, 0, pKey);
end
function GameScheme:RechargeExtraReward(iIndex)
return self.m_pDC:GetRecordByIndex(147, iIndex)
end
function GameScheme:RechargeExtraReward_nums()
return self.m_pDC:GetFileRecordNums(147)
end
function GameScheme:RechargeExtraReward_0(nIndex)
local pKey={nIndex=nIndex}
return self.m_pDC:GetRecord(147, 0, pKey);
end
function GameScheme:RechargeExtraReward_1(nAtyID,nActivityContentID)
local pKey={nAtyID=nAtyID,nActivityContentID=nActivityContentID}
return self.m_pDC:GetRecord(147, 1, pKey);
end
function GameScheme:ChristmasTree(iIndex)
return self.m_pDC:GetRecordByIndex(148, iIndex)
end
function GameScheme:ChristmasTree_nums()
return self.m_pDC:GetFileRecordNums(148)
end
function GameScheme:ChristmasTree_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(148, 0, pKey);
end
function GameScheme:festivalChristmasUI(iIndex)
return self.m_pDC:GetRecordByIndex(149, iIndex)
end
function GameScheme:festivalChristmasUI_nums()
return self.m_pDC:GetFileRecordNums(149)
end
function GameScheme:festivalChristmasUI_0(AtyID)
local pKey={AtyID=AtyID}
return self.m_pDC:GetRecord(149, 0, pKey);
end
function GameScheme:festivalCenturionCardUI(iIndex)
return self.m_pDC:GetRecordByIndex(150, iIndex)
end
function GameScheme:festivalCenturionCardUI_nums()
return self.m_pDC:GetFileRecordNums(150)
end
function GameScheme:festivalCenturionCardUI_0(AtyID)
local pKey={AtyID=AtyID}
return self.m_pDC:GetRecord(150, 0, pKey);
end
function GameScheme:monthlyTask(iIndex)
return self.m_pDC:GetRecordByIndex(151, iIndex)
end
function GameScheme:monthlyTask_nums()
return self.m_pDC:GetFileRecordNums(151)
end
function GameScheme:monthlyTask_0(nTaskID)
local pKey={nTaskID=nTaskID}
return self.m_pDC:GetRecord(151, 0, pKey);
end
function GameScheme:TavernLevel(iIndex)
return self.m_pDC:GetRecordByIndex(152, iIndex)
end
function GameScheme:TavernLevel_nums()
return self.m_pDC:GetFileRecordNums(152)
end
function GameScheme:TavernLevel_0(Level)
local pKey={Level=Level}
return self.m_pDC:GetRecord(152, 0, pKey);
end
function GameScheme:FactionHalo(iIndex)
return self.m_pDC:GetRecordByIndex(153, iIndex)
end
function GameScheme:FactionHalo_nums()
return self.m_pDC:GetFileRecordNums(153)
end
function GameScheme:FactionHalo_0(type,haloLv)
local pKey={type=type,haloLv=haloLv}
return self.m_pDC:GetRecord(153, 0, pKey);
end
function GameScheme:FactionTower(iIndex)
return self.m_pDC:GetRecordByIndex(154, iIndex)
end
function GameScheme:FactionTower_nums()
return self.m_pDC:GetFileRecordNums(154)
end
function GameScheme:FactionTower_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(154, 0, pKey);
end
function GameScheme:UnlockRmbActivity(iIndex)
return self.m_pDC:GetRecordByIndex(155, iIndex)
end
function GameScheme:UnlockRmbActivity_nums()
return self.m_pDC:GetFileRecordNums(155)
end
function GameScheme:UnlockRmbActivity_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(155, 0, pKey);
end
function GameScheme:HeroReturn(iIndex)
return self.m_pDC:GetRecordByIndex(156, iIndex)
end
function GameScheme:HeroReturn_nums()
return self.m_pDC:GetFileRecordNums(156)
end
function GameScheme:HeroReturn_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(156, 0, pKey);
end
function GameScheme:HeroReturn_1(heroType,heroStar)
local pKey={heroType=heroType,heroStar=heroStar}
return self.m_pDC:GetRecord(156, 1, pKey);
end
function GameScheme:FestivalUIDetail(iIndex)
return self.m_pDC:GetRecordByIndex(157, iIndex)
end
function GameScheme:FestivalUIDetail_nums()
return self.m_pDC:GetFileRecordNums(157)
end
function GameScheme:FestivalUIDetail_0(AtyID)
local pKey={AtyID=AtyID}
return self.m_pDC:GetRecord(157, 0, pKey);
end
function GameScheme:leagueLevel(iIndex)
return self.m_pDC:GetRecordByIndex(158, iIndex)
end
function GameScheme:leagueLevel_nums()
return self.m_pDC:GetFileRecordNums(158)
end
function GameScheme:leagueLevel_0(Level)
local pKey={Level=Level}
return self.m_pDC:GetRecord(158, 0, pKey);
end
function GameScheme:SpaceDominator(iIndex)
return self.m_pDC:GetRecordByIndex(159, iIndex)
end
function GameScheme:SpaceDominator_nums()
return self.m_pDC:GetFileRecordNums(159)
end
function GameScheme:SpaceDominator_0(PowerID)
local pKey={PowerID=PowerID}
return self.m_pDC:GetRecord(159, 0, pKey);
end
function GameScheme:SpaceDominator_1(Power,type)
local pKey={Power=Power,type=type}
return self.m_pDC:GetRecord(159, 1, pKey);
end
function GameScheme:SpaceDominatorReward(iIndex)
return self.m_pDC:GetRecordByIndex(160, iIndex)
end
function GameScheme:SpaceDominatorReward_nums()
return self.m_pDC:GetFileRecordNums(160)
end
function GameScheme:SpaceDominatorReward_0(iRankprize)
local pKey={iRankprize=iRankprize}
return self.m_pDC:GetRecord(160, 0, pKey);
end
function GameScheme:LoginPopup(iIndex)
return self.m_pDC:GetRecordByIndex(161, iIndex)
end
function GameScheme:LoginPopup_nums()
return self.m_pDC:GetFileRecordNums(161)
end
function GameScheme:LoginPopup_0(PlanID)
local pKey={PlanID=PlanID}
return self.m_pDC:GetRecord(161, 0, pKey);
end
function GameScheme:PeakOfTimeEvent(iIndex)
return self.m_pDC:GetRecordByIndex(162, iIndex)
end
function GameScheme:PeakOfTimeEvent_nums()
return self.m_pDC:GetFileRecordNums(162)
end
function GameScheme:PeakOfTimeEvent_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(162, 0, pKey);
end
function GameScheme:PeakOfTimeChapter(iIndex)
return self.m_pDC:GetRecordByIndex(163, iIndex)
end
function GameScheme:PeakOfTimeChapter_nums()
return self.m_pDC:GetFileRecordNums(163)
end
function GameScheme:PeakOfTimeChapter_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(163, 0, pKey);
end
function GameScheme:PeakOfTimeLevel(iIndex)
return self.m_pDC:GetRecordByIndex(164, iIndex)
end
function GameScheme:PeakOfTimeLevel_nums()
return self.m_pDC:GetFileRecordNums(164)
end
function GameScheme:PeakOfTimeLevel_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(164, 0, pKey);
end
function GameScheme:PeakOfTimeMonsterTeam(iIndex)
return self.m_pDC:GetRecordByIndex(165, iIndex)
end
function GameScheme:PeakOfTimeMonsterTeam_nums()
return self.m_pDC:GetFileRecordNums(165)
end
function GameScheme:PeakOfTimeMonsterTeam_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(165, 0, pKey);
end
function GameScheme:PeakOfTimeStory(iIndex)
return self.m_pDC:GetRecordByIndex(166, iIndex)
end
function GameScheme:PeakOfTimeStory_nums()
return self.m_pDC:GetFileRecordNums(166)
end
function GameScheme:PeakOfTimeStory_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(166, 0, pKey);
end
function GameScheme:KillEffect(iIndex)
return self.m_pDC:GetRecordByIndex(167, iIndex)
end
function GameScheme:KillEffect_nums()
return self.m_pDC:GetFileRecordNums(167)
end
function GameScheme:KillEffect_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(167, 0, pKey);
end
function GameScheme:SpaceDominatorRobot(iIndex)
return self.m_pDC:GetRecordByIndex(168, iIndex)
end
function GameScheme:SpaceDominatorRobot_nums()
return self.m_pDC:GetFileRecordNums(168)
end
function GameScheme:SpaceDominatorRobot_0(RobotID)
local pKey={RobotID=RobotID}
return self.m_pDC:GetRecord(168, 0, pKey);
end
function GameScheme:SpecialGiftUIDetail(iIndex)
return self.m_pDC:GetRecordByIndex(169, iIndex)
end
function GameScheme:SpecialGiftUIDetail_nums()
return self.m_pDC:GetFileRecordNums(169)
end
function GameScheme:SpecialGiftUIDetail_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(169, 0, pKey);
end
function GameScheme:HookLevelChest(iIndex)
return self.m_pDC:GetRecordByIndex(170, iIndex)
end
function GameScheme:HookLevelChest_nums()
return self.m_pDC:GetFileRecordNums(170)
end
function GameScheme:HookLevelChest_0(chestID)
local pKey={chestID=chestID}
return self.m_pDC:GetRecord(170, 0, pKey);
end
function GameScheme:HookLevelChest_1(chestType,ConditionValue)
local pKey={chestType=chestType,ConditionValue=ConditionValue}
return self.m_pDC:GetRecord(170, 1, pKey);
end
function GameScheme:HookLevelChest_2(chestType,chestSequence)
local pKey={chestType=chestType,chestSequence=chestSequence}
return self.m_pDC:GetRecord(170, 2, pKey);
end
function GameScheme:LeagueHelp(iIndex)
return self.m_pDC:GetRecordByIndex(171, iIndex)
end
function GameScheme:LeagueHelp_nums()
return self.m_pDC:GetFileRecordNums(171)
end
function GameScheme:LeagueHelp_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(171, 0, pKey);
end
function GameScheme:WorldMap(iIndex)
return self.m_pDC:GetRecordByIndex(172, iIndex)
end
function GameScheme:WorldMap_nums()
return self.m_pDC:GetFileRecordNums(172)
end
function GameScheme:WorldMap_0(worldD)
local pKey={worldD=worldD}
return self.m_pDC:GetRecord(172, 0, pKey);
end
function GameScheme:WorldMap_1(chapterIDchapterName)
local pKey={chapterIDchapterName=chapterIDchapterName}
return self.m_pDC:GetRecord(172, 1, pKey);
end
function GameScheme:EquipEnhance(iIndex)
return self.m_pDC:GetRecordByIndex(173, iIndex)
end
function GameScheme:EquipEnhance_nums()
return self.m_pDC:GetFileRecordNums(173)
end
function GameScheme:EquipEnhance_0(grade,level,type)
local pKey={grade=grade,level=level,type=type}
return self.m_pDC:GetRecord(173, 0, pKey);
end
function GameScheme:EquipEnhanceMaster(iIndex)
return self.m_pDC:GetRecordByIndex(174, iIndex)
end
function GameScheme:EquipEnhanceMaster_nums()
return self.m_pDC:GetFileRecordNums(174)
end
function GameScheme:EquipEnhanceMaster_0(EnhanceLv)
local pKey={EnhanceLv=EnhanceLv}
return self.m_pDC:GetRecord(174, 0, pKey);
end
function GameScheme:EquipEnhanceMaster_1(Level)
local pKey={Level=Level}
return self.m_pDC:GetRecord(174, 1, pKey);
end
function GameScheme:PopupControl(iIndex)
return self.m_pDC:GetRecordByIndex(175, iIndex)
end
function GameScheme:PopupControl_nums()
return self.m_pDC:GetFileRecordNums(175)
end
function GameScheme:PopupControl_0(PopupType)
local pKey={PopupType=PopupType}
return self.m_pDC:GetRecord(175, 0, pKey);
end
function GameScheme:MagicRobot(iIndex)
return self.m_pDC:GetRecordByIndex(176, iIndex)
end
function GameScheme:MagicRobot_nums()
return self.m_pDC:GetFileRecordNums(176)
end
function GameScheme:MagicRobot_0(robotID)
local pKey={robotID=robotID}
return self.m_pDC:GetRecord(176, 0, pKey);
end
function GameScheme:MagicGamble(iIndex)
return self.m_pDC:GetRecordByIndex(177, iIndex)
end
function GameScheme:MagicGamble_nums()
return self.m_pDC:GetFileRecordNums(177)
end
function GameScheme:MagicGamble_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(177, 0, pKey);
end
function GameScheme:championships(iIndex)
return self.m_pDC:GetRecordByIndex(178, iIndex)
end
function GameScheme:championships_nums()
return self.m_pDC:GetFileRecordNums(178)
end
function GameScheme:championships_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(178, 0, pKey);
end
function GameScheme:championshipsSubsection(iIndex)
return self.m_pDC:GetRecordByIndex(179, iIndex)
end
function GameScheme:championshipsSubsection_nums()
return self.m_pDC:GetFileRecordNums(179)
end
function GameScheme:championshipsSubsection_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(179, 0, pKey);
end
function GameScheme:ArenaLevel(iIndex)
return self.m_pDC:GetRecordByIndex(180, iIndex)
end
function GameScheme:ArenaLevel_nums()
return self.m_pDC:GetFileRecordNums(180)
end
function GameScheme:ArenaLevel_0(StageID)
local pKey={StageID=StageID}
return self.m_pDC:GetRecord(180, 0, pKey);
end
function GameScheme:SinglefieldAward(iIndex)
return self.m_pDC:GetRecordByIndex(181, iIndex)
end
function GameScheme:SinglefieldAward_nums()
return self.m_pDC:GetFileRecordNums(181)
end
function GameScheme:SinglefieldAward_0(iArenaType,iLevel)
local pKey={iArenaType=iArenaType,iLevel=iLevel}
return self.m_pDC:GetRecord(181, 0, pKey);
end
function GameScheme:ItemSource(iIndex)
return self.m_pDC:GetRecordByIndex(182, iIndex)
end
function GameScheme:ItemSource_nums()
return self.m_pDC:GetFileRecordNums(182)
end
function GameScheme:ItemSource_0(Index)
local pKey={Index=Index}
return self.m_pDC:GetRecord(182, 0, pKey);
end
function GameScheme:SystemSource(iIndex)
return self.m_pDC:GetRecordByIndex(183, iIndex)
end
function GameScheme:SystemSource_nums()
return self.m_pDC:GetFileRecordNums(183)
end
function GameScheme:SystemSource_0(guideID)
local pKey={guideID=guideID}
return self.m_pDC:GetRecord(183, 0, pKey);
end
function GameScheme:SystemSource_1(flag)
local pKey={flag=flag}
return self.m_pDC:GetRecord(183, 1, pKey);
end
function GameScheme:Lnstance(iIndex)
return self.m_pDC:GetRecordByIndex(184, iIndex)
end
function GameScheme:Lnstance_nums()
return self.m_pDC:GetFileRecordNums(184)
end
function GameScheme:Lnstance_0(Id)
local pKey={Id=Id}
return self.m_pDC:GetRecord(184, 0, pKey);
end
function GameScheme:LnstanceType(iIndex)
return self.m_pDC:GetRecordByIndex(185, iIndex)
end
function GameScheme:LnstanceType_nums()
return self.m_pDC:GetFileRecordNums(185)
end
function GameScheme:LnstanceType_0(Type)
local pKey={Type=Type}
return self.m_pDC:GetRecord(185, 0, pKey);
end
function GameScheme:EntertainmentCityLv(iIndex)
return self.m_pDC:GetRecordByIndex(186, iIndex)
end
function GameScheme:EntertainmentCityLv_nums()
return self.m_pDC:GetFileRecordNums(186)
end
function GameScheme:EntertainmentCityLv_0(Lv)
local pKey={Lv=Lv}
return self.m_pDC:GetRecord(186, 0, pKey);
end
function GameScheme:EntertainmentCityReward(iIndex)
return self.m_pDC:GetRecordByIndex(187, iIndex)
end
function GameScheme:EntertainmentCityReward_nums()
return self.m_pDC:GetFileRecordNums(187)
end
function GameScheme:EntertainmentCityReward_0(RoundID)
local pKey={RoundID=RoundID}
return self.m_pDC:GetRecord(187, 0, pKey);
end
function GameScheme:MagicReward(iIndex)
return self.m_pDC:GetRecordByIndex(188, iIndex)
end
function GameScheme:MagicReward_nums()
return self.m_pDC:GetFileRecordNums(188)
end
function GameScheme:MagicReward_0(Id)
local pKey={Id=Id}
return self.m_pDC:GetRecord(188, 0, pKey);
end
function GameScheme:UnionBattle(iIndex)
return self.m_pDC:GetRecordByIndex(189, iIndex)
end
function GameScheme:UnionBattle_nums()
return self.m_pDC:GetFileRecordNums(189)
end
function GameScheme:UnionBattle_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(189, 0, pKey);
end
function GameScheme:ChallengeListNew(iIndex)
return self.m_pDC:GetRecordByIndex(190, iIndex)
end
function GameScheme:ChallengeListNew_nums()
return self.m_pDC:GetFileRecordNums(190)
end
function GameScheme:ChallengeListNew_0(nID)
local pKey={nID=nID}
return self.m_pDC:GetRecord(190, 0, pKey);
end
function GameScheme:ChallengeListNew_1(nDayNum,nWeek)
local pKey={nDayNum=nDayNum,nWeek=nWeek}
return self.m_pDC:GetRecord(190, 1, pKey);
end
function GameScheme:ChallengeContent(iIndex)
return self.m_pDC:GetRecordByIndex(191, iIndex)
end
function GameScheme:ChallengeContent_nums()
return self.m_pDC:GetFileRecordNums(191)
end
function GameScheme:ChallengeContent_0(nContentID)
local pKey={nContentID=nContentID}
return self.m_pDC:GetRecord(191, 0, pKey);
end
function GameScheme:ActivityTask(iIndex)
return self.m_pDC:GetRecordByIndex(192, iIndex)
end
function GameScheme:ActivityTask_nums()
return self.m_pDC:GetFileRecordNums(192)
end
function GameScheme:ActivityTask_0(nTaskID)
local pKey={nTaskID=nTaskID}
return self.m_pDC:GetRecord(192, 0, pKey);
end
function GameScheme:LangParameters(iIndex)
return self.m_pDC:GetRecordByIndex(193, iIndex)
end
function GameScheme:LangParameters_nums()
return self.m_pDC:GetFileRecordNums(193)
end
function GameScheme:LangParameters_0(nKey,packageName)
local pKey={nKey=nKey,packageName=packageName}
return self.m_pDC:GetRecord(193, 0, pKey);
end
function GameScheme:ItemAwaken(iIndex)
return self.m_pDC:GetRecordByIndex(194, iIndex)
end
function GameScheme:ItemAwaken_nums()
return self.m_pDC:GetFileRecordNums(194)
end
function GameScheme:ItemAwaken_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(194, 0, pKey);
end
function GameScheme:LotteryShare(iIndex)
return self.m_pDC:GetRecordByIndex(195, iIndex)
end
function GameScheme:LotteryShare_nums()
return self.m_pDC:GetFileRecordNums(195)
end
function GameScheme:LotteryShare_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(195, 0, pKey);
end
function GameScheme:GiftPopUpControl(iIndex)
return self.m_pDC:GetRecordByIndex(196, iIndex)
end
function GameScheme:GiftPopUpControl_nums()
return self.m_pDC:GetFileRecordNums(196)
end
function GameScheme:GiftPopUpControl_0(Id)
local pKey={Id=Id}
return self.m_pDC:GetRecord(196, 0, pKey);
end
function GameScheme:GiftPopUp(iIndex)
return self.m_pDC:GetRecordByIndex(197, iIndex)
end
function GameScheme:GiftPopUp_nums()
return self.m_pDC:GetFileRecordNums(197)
end
function GameScheme:GiftPopUp_0(Id)
local pKey={Id=Id}
return self.m_pDC:GetRecord(197, 0, pKey);
end
function GameScheme:ShareGameActivity(iIndex)
return self.m_pDC:GetRecordByIndex(198, iIndex)
end
function GameScheme:ShareGameActivity_nums()
return self.m_pDC:GetFileRecordNums(198)
end
function GameScheme:ShareGameActivity_0(nShareID)
local pKey={nShareID=nShareID}
return self.m_pDC:GetRecord(198, 0, pKey);
end
function GameScheme:NewHeroSummonReward(iIndex)
return self.m_pDC:GetRecordByIndex(199, iIndex)
end
function GameScheme:NewHeroSummonReward_nums()
return self.m_pDC:GetFileRecordNums(199)
end
function GameScheme:NewHeroSummonReward_0(stageID)
local pKey={stageID=stageID}
return self.m_pDC:GetRecord(199, 0, pKey);
end
function GameScheme:NewHeroTreasureReward(iIndex)
return self.m_pDC:GetRecordByIndex(200, iIndex)
end
function GameScheme:NewHeroTreasureReward_nums()
return self.m_pDC:GetFileRecordNums(200)
end
function GameScheme:NewHeroTreasureReward_0(nID)
local pKey={nID=nID}
return self.m_pDC:GetRecord(200, 0, pKey);
end
function GameScheme:NewHeroTreasureTask(iIndex)
return self.m_pDC:GetRecordByIndex(201, iIndex)
end
function GameScheme:NewHeroTreasureTask_nums()
return self.m_pDC:GetFileRecordNums(201)
end
function GameScheme:NewHeroTreasureTask_0(nTaskID)
local pKey={nTaskID=nTaskID}
return self.m_pDC:GetRecord(201, 0, pKey);
end
function GameScheme:NewHeroTreasureTask_1(AtyID,nTaskIndex,headingCode,versionNumber)
local pKey={AtyID=AtyID,nTaskIndex=nTaskIndex,headingCode=headingCode,versionNumber=versionNumber}
return self.m_pDC:GetRecord(201, 1, pKey);
end
function GameScheme:GlobalEvents(iIndex)
return self.m_pDC:GetRecordByIndex(202, iIndex)
end
function GameScheme:GlobalEvents_nums()
return self.m_pDC:GetFileRecordNums(202)
end
function GameScheme:GlobalEvents_0(EventId)
local pKey={EventId=EventId}
return self.m_pDC:GetRecord(202, 0, pKey);
end
function GameScheme:TalentSkill(iIndex)
return self.m_pDC:GetRecordByIndex(203, iIndex)
end
function GameScheme:TalentSkill_nums()
return self.m_pDC:GetFileRecordNums(203)
end
function GameScheme:TalentSkill_0(TalentID)
local pKey={TalentID=TalentID}
return self.m_pDC:GetRecord(203, 0, pKey);
end
function GameScheme:MergeServerContent(iIndex)
return self.m_pDC:GetRecordByIndex(204, iIndex)
end
function GameScheme:MergeServerContent_nums()
return self.m_pDC:GetFileRecordNums(204)
end
function GameScheme:MergeServerContent_0(nContentID)
local pKey={nContentID=nContentID}
return self.m_pDC:GetRecord(204, 0, pKey);
end
function GameScheme:draftRecommand(iIndex)
return self.m_pDC:GetRecordByIndex(205, iIndex)
end
function GameScheme:draftRecommand_nums()
return self.m_pDC:GetFileRecordNums(205)
end
function GameScheme:draftRecommand_0(typeID,heroID)
local pKey={typeID=typeID,heroID=heroID}
return self.m_pDC:GetRecord(205, 0, pKey);
end
function GameScheme:draftRecommand_1(typeID,levelID)
local pKey={typeID=typeID,levelID=levelID}
return self.m_pDC:GetRecord(205, 1, pKey);
end
function GameScheme:draftRecommand_2(typeID)
local pKey={typeID=typeID}
return self.m_pDC:GetRecord(205, 2, pKey);
end
function GameScheme:draftTotal(iIndex)
return self.m_pDC:GetRecordByIndex(206, iIndex)
end
function GameScheme:draftTotal_nums()
return self.m_pDC:GetFileRecordNums(206)
end
function GameScheme:draftTotal_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(206, 0, pKey);
end
function GameScheme:ArtifactSuit(iIndex)
return self.m_pDC:GetRecordByIndex(207, iIndex)
end
function GameScheme:ArtifactSuit_nums()
return self.m_pDC:GetFileRecordNums(207)
end
function GameScheme:ArtifactSuit_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(207, 0, pKey);
end
function GameScheme:GVEUnionBoss(iIndex)
return self.m_pDC:GetRecordByIndex(208, iIndex)
end
function GameScheme:GVEUnionBoss_nums()
return self.m_pDC:GetFileRecordNums(208)
end
function GameScheme:GVEUnionBoss_0(BOSSID)
local pKey={BOSSID=BOSSID}
return self.m_pDC:GetRecord(208, 0, pKey);
end
function GameScheme:MergeServerList(iIndex)
return self.m_pDC:GetRecordByIndex(209, iIndex)
end
function GameScheme:MergeServerList_nums()
return self.m_pDC:GetFileRecordNums(209)
end
function GameScheme:MergeServerList_0(nID)
local pKey={nID=nID}
return self.m_pDC:GetRecord(209, 0, pKey);
end
function GameScheme:MergeServerList_1(nContentType,nDayNum)
local pKey={nContentType=nContentType,nDayNum=nDayNum}
return self.m_pDC:GetRecord(209, 1, pKey);
end
function GameScheme:ReturnActivityContent(iIndex)
return self.m_pDC:GetRecordByIndex(210, iIndex)
end
function GameScheme:ReturnActivityContent_nums()
return self.m_pDC:GetFileRecordNums(210)
end
function GameScheme:ReturnActivityContent_0(nContentID)
local pKey={nContentID=nContentID}
return self.m_pDC:GetRecord(210, 0, pKey);
end
function GameScheme:ReturnActivityList(iIndex)
return self.m_pDC:GetRecordByIndex(211, iIndex)
end
function GameScheme:ReturnActivityList_nums()
return self.m_pDC:GetFileRecordNums(211)
end
function GameScheme:ReturnActivityList_0(nID)
local pKey={nID=nID}
return self.m_pDC:GetRecord(211, 0, pKey);
end
function GameScheme:ReturnActivityList_1(nContentType,nDayNum)
local pKey={nContentType=nContentType,nDayNum=nDayNum}
return self.m_pDC:GetRecord(211, 1, pKey);
end
function GameScheme:RookieActivity(iIndex)
return self.m_pDC:GetRecordByIndex(212, iIndex)
end
function GameScheme:RookieActivity_nums()
return self.m_pDC:GetFileRecordNums(212)
end
function GameScheme:RookieActivity_0(AtyID)
local pKey={AtyID=AtyID}
return self.m_pDC:GetRecord(212, 0, pKey);
end
function GameScheme:RookieContent(iIndex)
return self.m_pDC:GetRecordByIndex(213, iIndex)
end
function GameScheme:RookieContent_nums()
return self.m_pDC:GetFileRecordNums(213)
end
function GameScheme:RookieContent_0(nContentID)
local pKey={nContentID=nContentID}
return self.m_pDC:GetRecord(213, 0, pKey);
end
function GameScheme:Summoned(iIndex)
return self.m_pDC:GetRecordByIndex(214, iIndex)
end
function GameScheme:Summoned_nums()
return self.m_pDC:GetFileRecordNums(214)
end
function GameScheme:Summoned_0(SummonedId)
local pKey={SummonedId=SummonedId}
return self.m_pDC:GetRecord(214, 0, pKey);
end
function GameScheme:DailyGift(iIndex)
return self.m_pDC:GetRecordByIndex(215, iIndex)
end
function GameScheme:DailyGift_nums()
return self.m_pDC:GetFileRecordNums(215)
end
function GameScheme:DailyGift_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(215, 0, pKey);
end
function GameScheme:RoleTitle(iIndex)
return self.m_pDC:GetRecordByIndex(216, iIndex)
end
function GameScheme:RoleTitle_nums()
return self.m_pDC:GetFileRecordNums(216)
end
function GameScheme:RoleTitle_0(TitleID)
local pKey={TitleID=TitleID}
return self.m_pDC:GetRecord(216, 0, pKey);
end
function GameScheme:VoidArena(iIndex)
return self.m_pDC:GetRecordByIndex(217, iIndex)
end
function GameScheme:VoidArena_nums()
return self.m_pDC:GetFileRecordNums(217)
end
function GameScheme:VoidArena_0(VoidID)
local pKey={VoidID=VoidID}
return self.m_pDC:GetRecord(217, 0, pKey);
end
function GameScheme:HeroPortraits(iIndex)
return self.m_pDC:GetRecordByIndex(218, iIndex)
end
function GameScheme:HeroPortraits_nums()
return self.m_pDC:GetFileRecordNums(218)
end
function GameScheme:HeroPortraits_0(rarityType,starLv)
local pKey={rarityType=rarityType,starLv=starLv}
return self.m_pDC:GetRecord(218, 0, pKey);
end
function GameScheme:HeroPortraitsPic(iIndex)
return self.m_pDC:GetRecordByIndex(219, iIndex)
end
function GameScheme:HeroPortraitsPic_nums()
return self.m_pDC:GetFileRecordNums(219)
end
function GameScheme:HeroPortraitsPic_0(heroID)
local pKey={heroID=heroID}
return self.m_pDC:GetRecord(219, 0, pKey);
end
function GameScheme:VoidArenaMonsterStar(iIndex)
return self.m_pDC:GetRecordByIndex(220, iIndex)
end
function GameScheme:VoidArenaMonsterStar_nums()
return self.m_pDC:GetFileRecordNums(220)
end
function GameScheme:VoidArenaMonsterStar_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(220, 0, pKey);
end
function GameScheme:AreaChampionship(iIndex)
return self.m_pDC:GetRecordByIndex(221, iIndex)
end
function GameScheme:AreaChampionship_nums()
return self.m_pDC:GetFileRecordNums(221)
end
function GameScheme:AreaChampionship_0(arenaId)
local pKey={arenaId=arenaId}
return self.m_pDC:GetRecord(221, 0, pKey);
end
function GameScheme:slgMoraleLimit(iIndex)
return self.m_pDC:GetRecordByIndex(222, iIndex)
end
function GameScheme:slgMoraleLimit_nums()
return self.m_pDC:GetFileRecordNums(222)
end
function GameScheme:slgMoraleLimit_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(222, 0, pKey);
end
function GameScheme:slgMoveCost(iIndex)
return self.m_pDC:GetRecordByIndex(223, iIndex)
end
function GameScheme:slgMoveCost_nums()
return self.m_pDC:GetFileRecordNums(223)
end
function GameScheme:slgMoveCost_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(223, 0, pKey);
end
function GameScheme:slgOfficial(iIndex)
return self.m_pDC:GetRecordByIndex(224, iIndex)
end
function GameScheme:slgOfficial_nums()
return self.m_pDC:GetFileRecordNums(224)
end
function GameScheme:slgOfficial_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(224, 0, pKey);
end
function GameScheme:slgOverReward(iIndex)
return self.m_pDC:GetRecordByIndex(225, iIndex)
end
function GameScheme:slgOverReward_nums()
return self.m_pDC:GetFileRecordNums(225)
end
function GameScheme:slgOverReward_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(225, 0, pKey);
end
function GameScheme:slgPointReward(iIndex)
return self.m_pDC:GetRecordByIndex(226, iIndex)
end
function GameScheme:slgPointReward_nums()
return self.m_pDC:GetFileRecordNums(226)
end
function GameScheme:slgPointReward_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(226, 0, pKey);
end
function GameScheme:slgStarWarbarbarian(iIndex)
return self.m_pDC:GetRecordByIndex(227, iIndex)
end
function GameScheme:slgStarWarbarbarian_nums()
return self.m_pDC:GetFileRecordNums(227)
end
function GameScheme:slgStarWarbarbarian_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(227, 0, pKey);
end
function GameScheme:slgStarWarCityPosition(iIndex)
return self.m_pDC:GetRecordByIndex(228, iIndex)
end
function GameScheme:slgStarWarCityPosition_nums()
return self.m_pDC:GetFileRecordNums(228)
end
function GameScheme:slgStarWarCityPosition_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(228, 0, pKey);
end
function GameScheme:slgStarWarLandResource(iIndex)
return self.m_pDC:GetRecordByIndex(229, iIndex)
end
function GameScheme:slgStarWarLandResource_nums()
return self.m_pDC:GetFileRecordNums(229)
end
function GameScheme:slgStarWarLandResource_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(229, 0, pKey);
end
function GameScheme:slgStarWarMap(iIndex)
return self.m_pDC:GetRecordByIndex(230, iIndex)
end
function GameScheme:slgStarWarMap_nums()
return self.m_pDC:GetFileRecordNums(230)
end
function GameScheme:slgStarWarMap_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(230, 0, pKey);
end
function GameScheme:slgStarWarMaterial(iIndex)
return self.m_pDC:GetRecordByIndex(231, iIndex)
end
function GameScheme:slgStarWarMaterial_nums()
return self.m_pDC:GetFileRecordNums(231)
end
function GameScheme:slgStarWarMaterial_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(231, 0, pKey);
end
function GameScheme:slgStarWarMaterialArea(iIndex)
return self.m_pDC:GetRecordByIndex(232, iIndex)
end
function GameScheme:slgStarWarMaterialArea_nums()
return self.m_pDC:GetFileRecordNums(232)
end
function GameScheme:slgStarWarMaterialArea_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(232, 0, pKey);
end
function GameScheme:slgStarWarObstacle(iIndex)
return self.m_pDC:GetRecordByIndex(233, iIndex)
end
function GameScheme:slgStarWarObstacle_nums()
return self.m_pDC:GetFileRecordNums(233)
end
function GameScheme:slgStarWarObstacle_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(233, 0, pKey);
end
function GameScheme:slgStarWarPlayerHome(iIndex)
return self.m_pDC:GetRecordByIndex(234, iIndex)
end
function GameScheme:slgStarWarPlayerHome_nums()
return self.m_pDC:GetFileRecordNums(234)
end
function GameScheme:slgStarWarPlayerHome_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(234, 0, pKey);
end
function GameScheme:slgStarWarstronghold(iIndex)
return self.m_pDC:GetRecordByIndex(235, iIndex)
end
function GameScheme:slgStarWarstronghold_nums()
return self.m_pDC:GetFileRecordNums(235)
end
function GameScheme:slgStarWarstronghold_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(235, 0, pKey);
end
function GameScheme:slgStarWarstrongholdSum(iIndex)
return self.m_pDC:GetRecordByIndex(236, iIndex)
end
function GameScheme:slgStarWarstrongholdSum_nums()
return self.m_pDC:GetFileRecordNums(236)
end
function GameScheme:slgStarWarstrongholdSum_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(236, 0, pKey);
end
function GameScheme:slgTask(iIndex)
return self.m_pDC:GetRecordByIndex(237, iIndex)
end
function GameScheme:slgTask_nums()
return self.m_pDC:GetFileRecordNums(237)
end
function GameScheme:slgTask_0(nTaskID)
local pKey={nTaskID=nTaskID}
return self.m_pDC:GetRecord(237, 0, pKey);
end
function GameScheme:SLGTechnology(iIndex)
return self.m_pDC:GetRecordByIndex(238, iIndex)
end
function GameScheme:SLGTechnology_nums()
return self.m_pDC:GetFileRecordNums(238)
end
function GameScheme:SLGTechnology_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(238, 0, pKey);
end
function GameScheme:AreaChampionshipSvrCfg(iIndex)
return self.m_pDC:GetRecordByIndex(239, iIndex)
end
function GameScheme:AreaChampionshipSvrCfg_nums()
return self.m_pDC:GetFileRecordNums(239)
end
function GameScheme:AreaChampionshipSvrCfg_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(239, 0, pKey);
end
function GameScheme:AreaChampionshipSvrCfg_1(ishowID)
local pKey={ishowID=ishowID}
return self.m_pDC:GetRecord(239, 1, pKey);
end
function GameScheme:MonopolyCheckerboard(iIndex)
return self.m_pDC:GetRecordByIndex(240, iIndex)
end
function GameScheme:MonopolyCheckerboard_nums()
return self.m_pDC:GetFileRecordNums(240)
end
function GameScheme:MonopolyCheckerboard_0(Id)
local pKey={Id=Id}
return self.m_pDC:GetRecord(240, 0, pKey);
end
function GameScheme:MonopolyCheckerboard_1(nTaskIndex,versionNumher)
local pKey={nTaskIndex=nTaskIndex,versionNumher=versionNumher}
return self.m_pDC:GetRecord(240, 1, pKey);
end
function GameScheme:MonopolyStar(iIndex)
return self.m_pDC:GetRecordByIndex(241, iIndex)
end
function GameScheme:MonopolyStar_nums()
return self.m_pDC:GetFileRecordNums(241)
end
function GameScheme:MonopolyStar_0(Id)
local pKey={Id=Id}
return self.m_pDC:GetRecord(241, 0, pKey);
end
function GameScheme:MonopolyStar_1(nTaskIndex,versionNumher)
local pKey={nTaskIndex=nTaskIndex,versionNumher=versionNumher}
return self.m_pDC:GetRecord(241, 1, pKey);
end
function GameScheme:championshipsRobotConfig(iIndex)
return self.m_pDC:GetRecordByIndex(242, iIndex)
end
function GameScheme:championshipsRobotConfig_nums()
return self.m_pDC:GetFileRecordNums(242)
end
function GameScheme:championshipsRobotConfig_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(242, 0, pKey);
end
function GameScheme:championshipsRobotHeroConfig(iIndex)
return self.m_pDC:GetRecordByIndex(243, iIndex)
end
function GameScheme:championshipsRobotHeroConfig_nums()
return self.m_pDC:GetFileRecordNums(243)
end
function GameScheme:championshipsRobotHeroConfig_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(243, 0, pKey);
end
function GameScheme:GameEventReport(iIndex)
return self.m_pDC:GetRecordByIndex(244, iIndex)
end
function GameScheme:GameEventReport_nums()
return self.m_pDC:GetFileRecordNums(244)
end
function GameScheme:GameEventReport_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(244, 0, pKey);
end
function GameScheme:GameEventReport_1(eventName)
local pKey={eventName=eventName}
return self.m_pDC:GetRecord(244, 1, pKey);
end
function GameScheme:ActivityFlop(iIndex)
return self.m_pDC:GetRecordByIndex(245, iIndex)
end
function GameScheme:ActivityFlop_nums()
return self.m_pDC:GetFileRecordNums(245)
end
function GameScheme:ActivityFlop_0(Id)
local pKey={Id=Id}
return self.m_pDC:GetRecord(245, 0, pKey);
end
function GameScheme:ActivityFlop_1(nTaskIndex,versionNumber)
local pKey={nTaskIndex=nTaskIndex,versionNumber=versionNumber}
return self.m_pDC:GetRecord(245, 1, pKey);
end
function GameScheme:ActivityFlopReward(iIndex)
return self.m_pDC:GetRecordByIndex(246, iIndex)
end
function GameScheme:ActivityFlopReward_nums()
return self.m_pDC:GetFileRecordNums(246)
end
function GameScheme:ActivityFlopReward_0(Id)
local pKey={Id=Id}
return self.m_pDC:GetRecord(246, 0, pKey);
end
function GameScheme:ActivityFlopReward_1(nLevel)
local pKey={nLevel=nLevel}
return self.m_pDC:GetRecord(246, 1, pKey);
end
function GameScheme:SpaceGap(iIndex)
return self.m_pDC:GetRecordByIndex(247, iIndex)
end
function GameScheme:SpaceGap_nums()
return self.m_pDC:GetFileRecordNums(247)
end
function GameScheme:SpaceGap_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(247, 0, pKey);
end
function GameScheme:SpaceGap_1(UnlockStage)
local pKey={UnlockStage=UnlockStage}
return self.m_pDC:GetRecord(247, 1, pKey);
end
function GameScheme:SpaceGapStory(iIndex)
return self.m_pDC:GetRecordByIndex(248, iIndex)
end
function GameScheme:SpaceGapStory_nums()
return self.m_pDC:GetFileRecordNums(248)
end
function GameScheme:SpaceGapStory_0(Index)
local pKey={Index=Index}
return self.m_pDC:GetRecord(248, 0, pKey);
end
function GameScheme:SpaceGapStory_1(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(248, 1, pKey);
end
function GameScheme:RechargeGoogleShop(iIndex)
return self.m_pDC:GetRecordByIndex(249, iIndex)
end
function GameScheme:RechargeGoogleShop_nums()
return self.m_pDC:GetFileRecordNums(249)
end
function GameScheme:RechargeGoogleShop_0(iGoodsID)
local pKey={iGoodsID=iGoodsID}
return self.m_pDC:GetRecord(249, 0, pKey);
end
function GameScheme:RechargeGoogleShop_1(index)
local pKey={index=index}
return self.m_pDC:GetRecord(249, 1, pKey);
end
function GameScheme:UnionMedal(iIndex)
return self.m_pDC:GetRecordByIndex(250, iIndex)
end
function GameScheme:UnionMedal_nums()
return self.m_pDC:GetFileRecordNums(250)
end
function GameScheme:UnionMedal_0(ID,MedalLevel)
local pKey={ID=ID,MedalLevel=MedalLevel}
return self.m_pDC:GetRecord(250, 0, pKey);
end
function GameScheme:RecomCard(iIndex)
return self.m_pDC:GetRecordByIndex(251, iIndex)
end
function GameScheme:RecomCard_nums()
return self.m_pDC:GetFileRecordNums(251)
end
function GameScheme:RecomCard_0(Id)
local pKey={Id=Id}
return self.m_pDC:GetRecord(251, 0, pKey);
end
function GameScheme:RecomHero(iIndex)
return self.m_pDC:GetRecordByIndex(252, iIndex)
end
function GameScheme:RecomHero_nums()
return self.m_pDC:GetFileRecordNums(252)
end
function GameScheme:RecomHero_0(heroType)
local pKey={heroType=heroType}
return self.m_pDC:GetRecord(252, 0, pKey);
end
function GameScheme:Skin(iIndex)
return self.m_pDC:GetRecordByIndex(253, iIndex)
end
function GameScheme:Skin_nums()
return self.m_pDC:GetFileRecordNums(253)
end
function GameScheme:Skin_0(Index)
local pKey={Index=Index}
return self.m_pDC:GetRecord(253, 0, pKey);
end
function GameScheme:Skin_1(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(253, 1, pKey);
end
function GameScheme:SkinProp(iIndex)
return self.m_pDC:GetRecordByIndex(254, iIndex)
end
function GameScheme:SkinProp_nums()
return self.m_pDC:GetFileRecordNums(254)
end
function GameScheme:SkinProp_0(ProID)
local pKey={ProID=ProID}
return self.m_pDC:GetRecord(254, 0, pKey);
end
function GameScheme:SkinSkill(iIndex)
return self.m_pDC:GetRecordByIndex(255, iIndex)
end
function GameScheme:SkinSkill_nums()
return self.m_pDC:GetFileRecordNums(255)
end
function GameScheme:SkinSkill_0(nSkinSkillID)
local pKey={nSkinSkillID=nSkinSkillID}
return self.m_pDC:GetRecord(255, 0, pKey);
end
function GameScheme:ExclusiveFight(iIndex)
return self.m_pDC:GetRecordByIndex(256, iIndex)
end
function GameScheme:ExclusiveFight_nums()
return self.m_pDC:GetFileRecordNums(256)
end
function GameScheme:ExclusiveFight_0(index)
local pKey={index=index}
return self.m_pDC:GetRecord(256, 0, pKey);
end
function GameScheme:ExclusiveFight_1(newherotype,stage)
local pKey={newherotype=newherotype,stage=stage}
return self.m_pDC:GetRecord(256, 1, pKey);
end
function GameScheme:ExclusiveStage(iIndex)
return self.m_pDC:GetRecordByIndex(257, iIndex)
end
function GameScheme:ExclusiveStage_nums()
return self.m_pDC:GetFileRecordNums(257)
end
function GameScheme:ExclusiveStage_0(Index)
local pKey={Index=Index}
return self.m_pDC:GetRecord(257, 0, pKey);
end
function GameScheme:ExclusiveStage_1(storyID)
local pKey={storyID=storyID}
return self.m_pDC:GetRecord(257, 1, pKey);
end
function GameScheme:ModuleCfg(iIndex)
return self.m_pDC:GetRecordByIndex(258, iIndex)
end
function GameScheme:ModuleCfg_nums()
return self.m_pDC:GetFileRecordNums(258)
end
function GameScheme:ModuleCfg_0(iModuleID)
local pKey={iModuleID=iModuleID}
return self.m_pDC:GetRecord(258, 0, pKey);
end
function GameScheme:ModuleActivity(iIndex)
return self.m_pDC:GetRecordByIndex(259, iIndex)
end
function GameScheme:ModuleActivity_nums()
return self.m_pDC:GetFileRecordNums(259)
end
function GameScheme:ModuleActivity_0(nTaskID)
local pKey={nTaskID=nTaskID}
return self.m_pDC:GetRecord(259, 0, pKey);
end
function GameScheme:EquipmentResonance(iIndex)
return self.m_pDC:GetRecordByIndex(260, iIndex)
end
function GameScheme:EquipmentResonance_nums()
return self.m_pDC:GetFileRecordNums(260)
end
function GameScheme:EquipmentResonance_0(proID)
local pKey={proID=proID}
return self.m_pDC:GetRecord(260, 0, pKey);
end
function GameScheme:EquipAdvance(iIndex)
return self.m_pDC:GetRecordByIndex(261, iIndex)
end
function GameScheme:EquipAdvance_nums()
return self.m_pDC:GetFileRecordNums(261)
end
function GameScheme:EquipAdvance_0(equipmentID)
local pKey={equipmentID=equipmentID}
return self.m_pDC:GetRecord(261, 0, pKey);
end
function GameScheme:ActivityIntimacy(iIndex)
return self.m_pDC:GetRecordByIndex(262, iIndex)
end
function GameScheme:ActivityIntimacy_nums()
return self.m_pDC:GetFileRecordNums(262)
end
function GameScheme:ActivityIntimacy_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(262, 0, pKey);
end
function GameScheme:ItemExchange(iIndex)
return self.m_pDC:GetRecordByIndex(263, iIndex)
end
function GameScheme:ItemExchange_nums()
return self.m_pDC:GetFileRecordNums(263)
end
function GameScheme:ItemExchange_0(ExchangeID)
local pKey={ExchangeID=ExchangeID}
return self.m_pDC:GetRecord(263, 0, pKey);
end
function GameScheme:RecommendGift(iIndex)
return self.m_pDC:GetRecordByIndex(264, iIndex)
end
function GameScheme:RecommendGift_nums()
return self.m_pDC:GetFileRecordNums(264)
end
function GameScheme:RecommendGift_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(264, 0, pKey);
end
function GameScheme:PuzzleLevel(iIndex)
return self.m_pDC:GetRecordByIndex(265, iIndex)
end
function GameScheme:PuzzleLevel_nums()
return self.m_pDC:GetFileRecordNums(265)
end
function GameScheme:PuzzleLevel_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(265, 0, pKey);
end
function GameScheme:Creature(iIndex)
return self.m_pDC:GetRecordByIndex(266, iIndex)
end
function GameScheme:Creature_nums()
return self.m_pDC:GetFileRecordNums(266)
end
function GameScheme:Creature_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(266, 0, pKey);
end
function GameScheme:BingoReward(iIndex)
return self.m_pDC:GetRecordByIndex(267, iIndex)
end
function GameScheme:BingoReward_nums()
return self.m_pDC:GetFileRecordNums(267)
end
function GameScheme:BingoReward_0(nID,versionNumber)
local pKey={nID=nID,versionNumber=versionNumber}
return self.m_pDC:GetRecord(267, 0, pKey);
end
function GameScheme:GuideStepPuzzleGame(iIndex)
return self.m_pDC:GetRecordByIndex(268, iIndex)
end
function GameScheme:GuideStepPuzzleGame_nums()
return self.m_pDC:GetFileRecordNums(268)
end
function GameScheme:GuideStepPuzzleGame_0(stepId)
local pKey={stepId=stepId}
return self.m_pDC:GetRecord(268, 0, pKey);
end
function GameScheme:ForceGuidePuzzleGame(iIndex)
return self.m_pDC:GetRecordByIndex(269, iIndex)
end
function GameScheme:ForceGuidePuzzleGame_nums()
return self.m_pDC:GetFileRecordNums(269)
end
function GameScheme:ForceGuidePuzzleGame_0(guideId)
local pKey={guideId=guideId}
return self.m_pDC:GetRecord(269, 0, pKey);
end
function GameScheme:PuzzleGameChapters(iIndex)
return self.m_pDC:GetRecordByIndex(270, iIndex)
end
function GameScheme:PuzzleGameChapters_nums()
return self.m_pDC:GetFileRecordNums(270)
end
function GameScheme:PuzzleGameChapters_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(270, 0, pKey);
end
function GameScheme:PuzzleGameLevel(iIndex)
return self.m_pDC:GetRecordByIndex(271, iIndex)
end
function GameScheme:PuzzleGameLevel_nums()
return self.m_pDC:GetFileRecordNums(271)
end
function GameScheme:PuzzleGameLevel_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(271, 0, pKey);
end
function GameScheme:PuzzleGameLevel_1(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(271, 1, pKey);
end
function GameScheme:NewActivity(iIndex)
return self.m_pDC:GetRecordByIndex(272, iIndex)
end
function GameScheme:NewActivity_nums()
return self.m_pDC:GetFileRecordNums(272)
end
function GameScheme:NewActivity_0(AtyID)
local pKey={AtyID=AtyID}
return self.m_pDC:GetRecord(272, 0, pKey);
end
function GameScheme:NewActivity_1(headingCode,versionNumber)
local pKey={headingCode=headingCode,versionNumber=versionNumber}
return self.m_pDC:GetRecord(272, 1, pKey);
end
function GameScheme:HeroRecList(iIndex)
return self.m_pDC:GetRecordByIndex(273, iIndex)
end
function GameScheme:HeroRecList_nums()
return self.m_pDC:GetFileRecordNums(273)
end
function GameScheme:HeroRecList_0(type)
local pKey={type=type}
return self.m_pDC:GetRecord(273, 0, pKey);
end
function GameScheme:HeroRecList_1(RecID)
local pKey={RecID=RecID}
return self.m_pDC:GetRecord(273, 1, pKey);
end
function GameScheme:HeroRecList_2(Decktype)
local pKey={Decktype=Decktype}
return self.m_pDC:GetRecord(273, 2, pKey);
end
function GameScheme:StarExploreEvent(iIndex)
return self.m_pDC:GetRecordByIndex(274, iIndex)
end
function GameScheme:StarExploreEvent_nums()
return self.m_pDC:GetFileRecordNums(274)
end
function GameScheme:StarExploreEvent_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(274, 0, pKey);
end
function GameScheme:StarExploreGalaxyMap(iIndex)
return self.m_pDC:GetRecordByIndex(275, iIndex)
end
function GameScheme:StarExploreGalaxyMap_nums()
return self.m_pDC:GetFileRecordNums(275)
end
function GameScheme:StarExploreGalaxyMap_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(275, 0, pKey);
end
function GameScheme:StarExploreMap(iIndex)
return self.m_pDC:GetRecordByIndex(276, iIndex)
end
function GameScheme:StarExploreMap_nums()
return self.m_pDC:GetFileRecordNums(276)
end
function GameScheme:StarExploreMap_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(276, 0, pKey);
end
function GameScheme:StarExploreStars(iIndex)
return self.m_pDC:GetRecordByIndex(277, iIndex)
end
function GameScheme:StarExploreStars_nums()
return self.m_pDC:GetFileRecordNums(277)
end
function GameScheme:StarExploreStars_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(277, 0, pKey);
end
function GameScheme:StarExploreTech(iIndex)
return self.m_pDC:GetRecordByIndex(278, iIndex)
end
function GameScheme:StarExploreTech_nums()
return self.m_pDC:GetFileRecordNums(278)
end
function GameScheme:StarExploreTech_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(278, 0, pKey);
end
function GameScheme:StarExploreTech_1(Type,level)
local pKey={Type=Type,level=level}
return self.m_pDC:GetRecord(278, 1, pKey);
end
function GameScheme:StarScience(iIndex)
return self.m_pDC:GetRecordByIndex(279, iIndex)
end
function GameScheme:StarScience_nums()
return self.m_pDC:GetFileRecordNums(279)
end
function GameScheme:StarScience_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(279, 0, pKey);
end
function GameScheme:Sigil(iIndex)
return self.m_pDC:GetRecordByIndex(280, iIndex)
end
function GameScheme:Sigil_nums()
return self.m_pDC:GetFileRecordNums(280)
end
function GameScheme:Sigil_0(SigilID)
local pKey={SigilID=SigilID}
return self.m_pDC:GetRecord(280, 0, pKey);
end
function GameScheme:ProToLang(iIndex)
return self.m_pDC:GetRecordByIndex(281, iIndex)
end
function GameScheme:ProToLang_nums()
return self.m_pDC:GetFileRecordNums(281)
end
function GameScheme:ProToLang_0(iProID)
local pKey={iProID=iProID}
return self.m_pDC:GetRecord(281, 0, pKey);
end
function GameScheme:NewExclusiveFight(iIndex)
return self.m_pDC:GetRecordByIndex(282, iIndex)
end
function GameScheme:NewExclusiveFight_nums()
return self.m_pDC:GetFileRecordNums(282)
end
function GameScheme:NewExclusiveFight_0(index)
local pKey={index=index}
return self.m_pDC:GetRecord(282, 0, pKey);
end
function GameScheme:NewExclusiveFight_1(newherotype,stage)
local pKey={newherotype=newherotype,stage=stage}
return self.m_pDC:GetRecord(282, 1, pKey);
end
function GameScheme:RecommendPack(iIndex)
return self.m_pDC:GetRecordByIndex(283, iIndex)
end
function GameScheme:RecommendPack_nums()
return self.m_pDC:GetFileRecordNums(283)
end
function GameScheme:RecommendPack_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(283, 0, pKey);
end
function GameScheme:RecommendPack_1(type,index)
local pKey={type=type,index=index}
return self.m_pDC:GetRecord(283, 1, pKey);
end
function GameScheme:NewSvrAreaChampionshipSvrCfg(iIndex)
return self.m_pDC:GetRecordByIndex(284, iIndex)
end
function GameScheme:NewSvrAreaChampionshipSvrCfg_nums()
return self.m_pDC:GetFileRecordNums(284)
end
function GameScheme:NewSvrAreaChampionshipSvrCfg_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(284, 0, pKey);
end
function GameScheme:RechargeCumulative(iIndex)
return self.m_pDC:GetRecordByIndex(285, iIndex)
end
function GameScheme:RechargeCumulative_nums()
return self.m_pDC:GetFileRecordNums(285)
end
function GameScheme:RechargeCumulative_0(Id)
local pKey={Id=Id}
return self.m_pDC:GetRecord(285, 0, pKey);
end
function GameScheme:NewSvrAreaChampionship(iIndex)
return self.m_pDC:GetRecordByIndex(286, iIndex)
end
function GameScheme:NewSvrAreaChampionship_nums()
return self.m_pDC:GetFileRecordNums(286)
end
function GameScheme:NewSvrAreaChampionship_0(arenaId)
local pKey={arenaId=arenaId}
return self.m_pDC:GetRecord(286, 0, pKey);
end
function GameScheme:SpiritLinkUpgrade(iIndex)
return self.m_pDC:GetRecordByIndex(287, iIndex)
end
function GameScheme:SpiritLinkUpgrade_nums()
return self.m_pDC:GetFileRecordNums(287)
end
function GameScheme:SpiritLinkUpgrade_0(LevelID,iProgress)
local pKey={LevelID=LevelID,iProgress=iProgress}
return self.m_pDC:GetRecord(287, 0, pKey);
end
function GameScheme:LegendsTournament(iIndex)
return self.m_pDC:GetRecordByIndex(288, iIndex)
end
function GameScheme:LegendsTournament_nums()
return self.m_pDC:GetFileRecordNums(288)
end
function GameScheme:LegendsTournament_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(288, 0, pKey);
end
function GameScheme:SubmitGift(iIndex)
return self.m_pDC:GetRecordByIndex(289, iIndex)
end
function GameScheme:SubmitGift_nums()
return self.m_pDC:GetFileRecordNums(289)
end
function GameScheme:SubmitGift_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(289, 0, pKey);
end
function GameScheme:HeroChoice(iIndex)
return self.m_pDC:GetRecordByIndex(290, iIndex)
end
function GameScheme:HeroChoice_nums()
return self.m_pDC:GetFileRecordNums(290)
end
function GameScheme:HeroChoice_0(Id)
local pKey={Id=Id}
return self.m_pDC:GetRecord(290, 0, pKey);
end
function GameScheme:welfare(iIndex)
return self.m_pDC:GetRecordByIndex(291, iIndex)
end
function GameScheme:welfare_nums()
return self.m_pDC:GetFileRecordNums(291)
end
function GameScheme:welfare_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(291, 0, pKey);
end
function GameScheme:FunctionBar(iIndex)
return self.m_pDC:GetRecordByIndex(292, iIndex)
end
function GameScheme:FunctionBar_nums()
return self.m_pDC:GetFileRecordNums(292)
end
function GameScheme:FunctionBar_0(AtyEntrance)
local pKey={AtyEntrance=AtyEntrance}
return self.m_pDC:GetRecord(292, 0, pKey);
end
function GameScheme:zhanxingwu(iIndex)
return self.m_pDC:GetRecordByIndex(293, iIndex)
end
function GameScheme:zhanxingwu_nums()
return self.m_pDC:GetFileRecordNums(293)
end
function GameScheme:zhanxingwu_0(ID,versionNumber)
local pKey={ID=ID,versionNumber=versionNumber}
return self.m_pDC:GetRecord(293, 0, pKey);
end
function GameScheme:passSystem(iIndex)
return self.m_pDC:GetRecordByIndex(294, iIndex)
end
function GameScheme:passSystem_nums()
return self.m_pDC:GetFileRecordNums(294)
end
function GameScheme:passSystem_0(Index)
local pKey={Index=Index}
return self.m_pDC:GetRecord(294, 0, pKey);
end
function GameScheme:NewbeeAreaChampionshipSchedule(iIndex)
return self.m_pDC:GetRecordByIndex(295, iIndex)
end
function GameScheme:NewbeeAreaChampionshipSchedule_nums()
return self.m_pDC:GetFileRecordNums(295)
end
function GameScheme:NewbeeAreaChampionshipSchedule_0(Round)
local pKey={Round=Round}
return self.m_pDC:GetRecord(295, 0, pKey);
end
function GameScheme:StoryCfg(iIndex)
return self.m_pDC:GetRecordByIndex(296, iIndex)
end
function GameScheme:StoryCfg_nums()
return self.m_pDC:GetFileRecordNums(296)
end
function GameScheme:StoryCfg_0(StoryType,LevelID)
local pKey={StoryType=StoryType,LevelID=LevelID}
return self.m_pDC:GetRecord(296, 0, pKey);
end
function GameScheme:StoryCfg_1(StoryID)
local pKey={StoryID=StoryID}
return self.m_pDC:GetRecord(296, 1, pKey);
end
function GameScheme:StoryStep(iIndex)
return self.m_pDC:GetRecordByIndex(297, iIndex)
end
function GameScheme:StoryStep_nums()
return self.m_pDC:GetFileRecordNums(297)
end
function GameScheme:StoryStep_0(StoryStepID)
local pKey={StoryStepID=StoryStepID}
return self.m_pDC:GetRecord(297, 0, pKey);
end
function GameScheme:ActivityBossTrial(iIndex)
return self.m_pDC:GetRecordByIndex(298, iIndex)
end
function GameScheme:ActivityBossTrial_nums()
return self.m_pDC:GetFileRecordNums(298)
end
function GameScheme:ActivityBossTrial_0(BossID)
local pKey={BossID=BossID}
return self.m_pDC:GetRecord(298, 0, pKey);
end
function GameScheme:ActivityBossTrial_1(headingCode,versionNumber)
local pKey={headingCode=headingCode,versionNumber=versionNumber}
return self.m_pDC:GetRecord(298, 1, pKey);
end
function GameScheme:ChallengeDeck(iIndex)
return self.m_pDC:GetRecordByIndex(299, iIndex)
end
function GameScheme:ChallengeDeck_nums()
return self.m_pDC:GetFileRecordNums(299)
end
function GameScheme:ChallengeDeck_0(ChallengeDeckId)
local pKey={ChallengeDeckId=ChallengeDeckId}
return self.m_pDC:GetRecord(299, 0, pKey);
end
function GameScheme:ChallengeDeck_1(DeckType,DeckLevel,Waveid)
local pKey={DeckType=DeckType,DeckLevel=DeckLevel,Waveid=Waveid}
return self.m_pDC:GetRecord(299, 1, pKey);
end
function GameScheme:NewAwakenRoad(iIndex)
return self.m_pDC:GetRecordByIndex(300, iIndex)
end
function GameScheme:NewAwakenRoad_nums()
return self.m_pDC:GetFileRecordNums(300)
end
function GameScheme:NewAwakenRoad_0(HeroID)
local pKey={HeroID=HeroID}
return self.m_pDC:GetRecord(300, 0, pKey);
end
function GameScheme:HeroAwakeSkill(iIndex)
return self.m_pDC:GetRecordByIndex(301, iIndex)
end
function GameScheme:HeroAwakeSkill_nums()
return self.m_pDC:GetFileRecordNums(301)
end
function GameScheme:HeroAwakeSkill_0(heroskillID,Lv)
local pKey={heroskillID=heroskillID,Lv=Lv}
return self.m_pDC:GetRecord(301, 0, pKey);
end
function GameScheme:ChallengeDeckBoard(iIndex)
return self.m_pDC:GetRecordByIndex(302, iIndex)
end
function GameScheme:ChallengeDeckBoard_nums()
return self.m_pDC:GetFileRecordNums(302)
end
function GameScheme:ChallengeDeckBoard_0(LeaderboardId)
local pKey={LeaderboardId=LeaderboardId}
return self.m_pDC:GetRecord(302, 0, pKey);
end
function GameScheme:LotteryChoose(iIndex)
return self.m_pDC:GetRecordByIndex(303, iIndex)
end
function GameScheme:LotteryChoose_nums()
return self.m_pDC:GetFileRecordNums(303)
end
function GameScheme:LotteryChoose_0(Id)
local pKey={Id=Id}
return self.m_pDC:GetRecord(303, 0, pKey);
end
function GameScheme:towerMiniGame(iIndex)
return self.m_pDC:GetRecordByIndex(304, iIndex)
end
function GameScheme:towerMiniGame_nums()
return self.m_pDC:GetFileRecordNums(304)
end
function GameScheme:towerMiniGame_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(304, 0, pKey);
end
function GameScheme:towerMiniGameMonsterPath(iIndex)
return self.m_pDC:GetRecordByIndex(305, iIndex)
end
function GameScheme:towerMiniGameMonsterPath_nums()
return self.m_pDC:GetFileRecordNums(305)
end
function GameScheme:towerMiniGameMonsterPath_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(305, 0, pKey);
end
function GameScheme:MiniGameControl(iIndex)
return self.m_pDC:GetRecordByIndex(306, iIndex)
end
function GameScheme:MiniGameControl_nums()
return self.m_pDC:GetFileRecordNums(306)
end
function GameScheme:MiniGameControl_0(ChaptersID)
local pKey={ChaptersID=ChaptersID}
return self.m_pDC:GetRecord(306, 0, pKey);
end
function GameScheme:MiniGameControl_1(AdvertisingID,ChaptersNum)
local pKey={AdvertisingID=AdvertisingID,ChaptersNum=ChaptersNum}
return self.m_pDC:GetRecord(306, 1, pKey);
end
function GameScheme:MiniGameLevelControl(iIndex)
return self.m_pDC:GetRecordByIndex(307, iIndex)
end
function GameScheme:MiniGameLevelControl_nums()
return self.m_pDC:GetFileRecordNums(307)
end
function GameScheme:MiniGameLevelControl_0(ID,UnlockType)
local pKey={ID=ID,UnlockType=UnlockType}
return self.m_pDC:GetRecord(307, 0, pKey);
end
function GameScheme:MiniGameLevelControl_1(UnlockType,LevelType,LevelID)
local pKey={UnlockType=UnlockType,LevelType=LevelType,LevelID=LevelID}
return self.m_pDC:GetRecord(307, 1, pKey);
end
function GameScheme:SummonBlessHero(iIndex)
return self.m_pDC:GetRecordByIndex(308, iIndex)
end
function GameScheme:SummonBlessHero_nums()
return self.m_pDC:GetFileRecordNums(308)
end
function GameScheme:SummonBlessHero_0(heroType)
local pKey={heroType=heroType}
return self.m_pDC:GetRecord(308, 0, pKey);
end
function GameScheme:Odyssey(iIndex)
return self.m_pDC:GetRecordByIndex(309, iIndex)
end
function GameScheme:Odyssey_nums()
return self.m_pDC:GetFileRecordNums(309)
end
function GameScheme:Odyssey_0(checkPointID)
local pKey={checkPointID=checkPointID}
return self.m_pDC:GetRecord(309, 0, pKey);
end
function GameScheme:Odysseychapters(iIndex)
return self.m_pDC:GetRecordByIndex(310, iIndex)
end
function GameScheme:Odysseychapters_nums()
return self.m_pDC:GetFileRecordNums(310)
end
function GameScheme:Odysseychapters_0(chaptersID)
local pKey={chaptersID=chaptersID}
return self.m_pDC:GetRecord(310, 0, pKey);
end
function GameScheme:WeekendArena(iIndex)
return self.m_pDC:GetRecordByIndex(311, iIndex)
end
function GameScheme:WeekendArena_nums()
return self.m_pDC:GetFileRecordNums(311)
end
function GameScheme:WeekendArena_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(311, 0, pKey);
end
function GameScheme:WeekendArenaSvrCfg(iIndex)
return self.m_pDC:GetRecordByIndex(312, iIndex)
end
function GameScheme:WeekendArenaSvrCfg_nums()
return self.m_pDC:GetFileRecordNums(312)
end
function GameScheme:WeekendArenaSvrCfg_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(312, 0, pKey);
end
function GameScheme:OdysseyMopUp(iIndex)
return self.m_pDC:GetRecordByIndex(313, iIndex)
end
function GameScheme:OdysseyMopUp_nums()
return self.m_pDC:GetFileRecordNums(313)
end
function GameScheme:OdysseyMopUp_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(313, 0, pKey);
end
function GameScheme:ChallengeDeckPro(iIndex)
return self.m_pDC:GetRecordByIndex(314, iIndex)
end
function GameScheme:ChallengeDeckPro_nums()
return self.m_pDC:GetFileRecordNums(314)
end
function GameScheme:ChallengeDeckPro_0(ChallengeDeckPropID)
local pKey={ChallengeDeckPropID=ChallengeDeckPropID}
return self.m_pDC:GetRecord(314, 0, pKey);
end
function GameScheme:ChallengeDeckPro_1(tFlag,starLv)
local pKey={tFlag=tFlag,starLv=starLv}
return self.m_pDC:GetRecord(314, 1, pKey);
end
function GameScheme:CasualGame(iIndex)
return self.m_pDC:GetRecordByIndex(315, iIndex)
end
function GameScheme:CasualGame_nums()
return self.m_pDC:GetFileRecordNums(315)
end
function GameScheme:CasualGame_0(Id)
local pKey={Id=Id}
return self.m_pDC:GetRecord(315, 0, pKey);
end
function GameScheme:MiniGameSkill(iIndex)
return self.m_pDC:GetRecordByIndex(316, iIndex)
end
function GameScheme:MiniGameSkill_nums()
return self.m_pDC:GetFileRecordNums(316)
end
function GameScheme:MiniGameSkill_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(316, 0, pKey);
end
function GameScheme:MiniGameSkill_1(level)
local pKey={level=level}
return self.m_pDC:GetRecord(316, 1, pKey);
end
function GameScheme:CasualGameWithBg(iIndex)
return self.m_pDC:GetRecordByIndex(317, iIndex)
end
function GameScheme:CasualGameWithBg_nums()
return self.m_pDC:GetFileRecordNums(317)
end
function GameScheme:CasualGameWithBg_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(317, 0, pKey);
end
function GameScheme:CasualGameWithBg_1(LevelType)
local pKey={LevelType=LevelType}
return self.m_pDC:GetRecord(317, 1, pKey);
end
function GameScheme:KeywordSwindle(iIndex)
return self.m_pDC:GetRecordByIndex(318, iIndex)
end
function GameScheme:KeywordSwindle_nums()
return self.m_pDC:GetFileRecordNums(318)
end
function GameScheme:KeywordSwindle_0(iID)
local pKey={iID=iID}
return self.m_pDC:GetRecord(318, 0, pKey);
end
function GameScheme:ActorBackground(iIndex)
return self.m_pDC:GetRecordByIndex(319, iIndex)
end
function GameScheme:ActorBackground_nums()
return self.m_pDC:GetFileRecordNums(319)
end
function GameScheme:ActorBackground_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(319, 0, pKey);
end
function GameScheme:ActorBackground_1(bType,bIndex)
local pKey={bType=bType,bIndex=bIndex}
return self.m_pDC:GetRecord(319, 1, pKey);
end
function GameScheme:BattleBackground(iIndex)
return self.m_pDC:GetRecordByIndex(320, iIndex)
end
function GameScheme:BattleBackground_nums()
return self.m_pDC:GetFileRecordNums(320)
end
function GameScheme:BattleBackground_0(Type)
local pKey={Type=Type}
return self.m_pDC:GetRecord(320, 0, pKey);
end
function GameScheme:BattleBackground_1(Type,ID)
local pKey={Type=Type,ID=ID}
return self.m_pDC:GetRecord(320, 1, pKey);
end
function GameScheme:BattleSkillName(iIndex)
return self.m_pDC:GetRecordByIndex(321, iIndex)
end
function GameScheme:BattleSkillName_nums()
return self.m_pDC:GetFileRecordNums(321)
end
function GameScheme:BattleSkillName_0(rarityType,heroType)
local pKey={rarityType=rarityType,heroType=heroType}
return self.m_pDC:GetRecord(321, 0, pKey);
end
function GameScheme:Decorations(iIndex)
return self.m_pDC:GetRecordByIndex(322, iIndex)
end
function GameScheme:Decorations_nums()
return self.m_pDC:GetFileRecordNums(322)
end
function GameScheme:Decorations_0(DecorationsID)
local pKey={DecorationsID=DecorationsID}
return self.m_pDC:GetRecord(322, 0, pKey);
end
function GameScheme:Bless(iIndex)
return self.m_pDC:GetRecordByIndex(323, iIndex)
end
function GameScheme:Bless_nums()
return self.m_pDC:GetFileRecordNums(323)
end
function GameScheme:Bless_0(BlessID)
local pKey={BlessID=BlessID}
return self.m_pDC:GetRecord(323, 0, pKey);
end
function GameScheme:Biography(iIndex)
return self.m_pDC:GetRecordByIndex(324, iIndex)
end
function GameScheme:Biography_nums()
return self.m_pDC:GetFileRecordNums(324)
end
function GameScheme:Biography_0(nID)
local pKey={nID=nID}
return self.m_pDC:GetRecord(324, 0, pKey);
end
function GameScheme:Biography_1(heroMapID)
local pKey={heroMapID=heroMapID}
return self.m_pDC:GetRecord(324, 1, pKey);
end
function GameScheme:HikingActivity(iIndex)
return self.m_pDC:GetRecordByIndex(325, iIndex)
end
function GameScheme:HikingActivity_nums()
return self.m_pDC:GetFileRecordNums(325)
end
function GameScheme:HikingActivity_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(325, 0, pKey);
end
function GameScheme:HikingActivity_1(Version,Step)
local pKey={Version=Version,Step=Step}
return self.m_pDC:GetRecord(325, 1, pKey);
end
function GameScheme:BubbleTips(iIndex)
return self.m_pDC:GetRecordByIndex(326, iIndex)
end
function GameScheme:BubbleTips_nums()
return self.m_pDC:GetFileRecordNums(326)
end
function GameScheme:BubbleTips_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(326, 0, pKey);
end
function GameScheme:RechargeGoods(iIndex)
return self.m_pDC:GetRecordByIndex(327, iIndex)
end
function GameScheme:RechargeGoods_nums()
return self.m_pDC:GetFileRecordNums(327)
end
function GameScheme:RechargeGoods_0(iGoodsID)
local pKey={iGoodsID=iGoodsID}
return self.m_pDC:GetRecord(327, 0, pKey);
end
function GameScheme:RechargeExchange(iIndex)
return self.m_pDC:GetRecordByIndex(328, iIndex)
end
function GameScheme:RechargeExchange_nums()
return self.m_pDC:GetFileRecordNums(328)
end
function GameScheme:RechargeExchange_0(CNY)
local pKey={CNY=CNY}
return self.m_pDC:GetRecord(328, 0, pKey);
end
function GameScheme:RechargeChannelGoods(iIndex)
return self.m_pDC:GetRecordByIndex(329, iIndex)
end
function GameScheme:RechargeChannelGoods_nums()
return self.m_pDC:GetFileRecordNums(329)
end
function GameScheme:RechargeChannelGoods_0(iPrice,packageName)
local pKey={iPrice=iPrice,packageName=packageName}
return self.m_pDC:GetRecord(329, 0, pKey);
end
function GameScheme:RechargeChannelMap(iIndex)
return self.m_pDC:GetRecordByIndex(330, iIndex)
end
function GameScheme:RechargeChannelMap_nums()
return self.m_pDC:GetFileRecordNums(330)
end
function GameScheme:RechargeChannelMap_0(payTag)
local pKey={payTag=payTag}
return self.m_pDC:GetRecord(330, 0, pKey);
end
function GameScheme:MoneyType(iIndex)
return self.m_pDC:GetRecordByIndex(331, iIndex)
end
function GameScheme:MoneyType_nums()
return self.m_pDC:GetFileRecordNums(331)
end
function GameScheme:MoneyType_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(331, 0, pKey);
end
function GameScheme:MoneyType_1(strCurrencyType)
local pKey={strCurrencyType=strCurrencyType}
return self.m_pDC:GetRecord(331, 1, pKey);
end
function GameScheme:IosExchange(iIndex)
return self.m_pDC:GetRecordByIndex(332, iIndex)
end
function GameScheme:IosExchange_nums()
return self.m_pDC:GetFileRecordNums(332)
end
function GameScheme:IosExchange_0(CNY)
local pKey={CNY=CNY}
return self.m_pDC:GetRecord(332, 0, pKey);
end
function GameScheme:NewHeroActivityUI(iIndex)
return self.m_pDC:GetRecordByIndex(333, iIndex)
end
function GameScheme:NewHeroActivityUI_nums()
return self.m_pDC:GetFileRecordNums(333)
end
function GameScheme:NewHeroActivityUI_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(333, 0, pKey);
end
function GameScheme:LotteryChanceDetail(iIndex)
return self.m_pDC:GetRecordByIndex(334, iIndex)
end
function GameScheme:LotteryChanceDetail_nums()
return self.m_pDC:GetFileRecordNums(334)
end
function GameScheme:LotteryChanceDetail_0(chanceID)
local pKey={chanceID=chanceID}
return self.m_pDC:GetRecord(334, 0, pKey);
end
function GameScheme:Currency(iIndex)
return self.m_pDC:GetRecordByIndex(335, iIndex)
end
function GameScheme:Currency_nums()
return self.m_pDC:GetFileRecordNums(335)
end
function GameScheme:Currency_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(335, 0, pKey);
end
function GameScheme:Currency_1(currencyCode)
local pKey={currencyCode=currencyCode}
return self.m_pDC:GetRecord(335, 1, pKey);
end
function GameScheme:SkinBuff(iIndex)
return self.m_pDC:GetRecordByIndex(336, iIndex)
end
function GameScheme:SkinBuff_nums()
return self.m_pDC:GetFileRecordNums(336)
end
function GameScheme:SkinBuff_0(nSkinSkillID,unBuffLevel)
local pKey={nSkinSkillID=nSkinSkillID,unBuffLevel=unBuffLevel}
return self.m_pDC:GetRecord(336, 0, pKey);
end
function GameScheme:ChannelTag(iIndex)
return self.m_pDC:GetRecordByIndex(337, iIndex)
end
function GameScheme:ChannelTag_nums()
return self.m_pDC:GetFileRecordNums(337)
end
function GameScheme:ChannelTag_0(payTag)
local pKey={payTag=payTag}
return self.m_pDC:GetRecord(337, 0, pKey);
end
function GameScheme:festivalActivityTime(iIndex)
return self.m_pDC:GetRecordByIndex(338, iIndex)
end
function GameScheme:festivalActivityTime_nums()
return self.m_pDC:GetFileRecordNums(338)
end
function GameScheme:festivalActivityTime_0(AtyID)
local pKey={AtyID=AtyID}
return self.m_pDC:GetRecord(338, 0, pKey);
end
function GameScheme:festivalActivityTime_1(headingCode,versionNumber)
local pKey={headingCode=headingCode,versionNumber=versionNumber}
return self.m_pDC:GetRecord(338, 1, pKey);
end
function GameScheme:MiniGameQuickChallenge(iIndex)
return self.m_pDC:GetRecordByIndex(339, iIndex)
end
function GameScheme:MiniGameQuickChallenge_nums()
return self.m_pDC:GetFileRecordNums(339)
end
function GameScheme:MiniGameQuickChallenge_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(339, 0, pKey);
end
function GameScheme:MiniGameQuickChallenge_1(ID,GuideGroupID)
local pKey={ID=ID,GuideGroupID=GuideGroupID}
return self.m_pDC:GetRecord(339, 1, pKey);
end
function GameScheme:ABTest(iIndex)
return self.m_pDC:GetRecordByIndex(340, iIndex)
end
function GameScheme:ABTest_nums()
return self.m_pDC:GetFileRecordNums(340)
end
function GameScheme:ABTest_0(Id)
local pKey={Id=Id}
return self.m_pDC:GetRecord(340, 0, pKey);
end
function GameScheme:HeroLimit(iIndex)
return self.m_pDC:GetRecordByIndex(341, iIndex)
end
function GameScheme:HeroLimit_nums()
return self.m_pDC:GetFileRecordNums(341)
end
function GameScheme:HeroLimit_0(iHeroID)
local pKey={iHeroID=iHeroID}
return self.m_pDC:GetRecord(341, 0, pKey);
end
function GameScheme:ActivityCollect(iIndex)
return self.m_pDC:GetRecordByIndex(342, iIndex)
end
function GameScheme:ActivityCollect_nums()
return self.m_pDC:GetFileRecordNums(342)
end
function GameScheme:ActivityCollect_0(Id)
local pKey={Id=Id}
return self.m_pDC:GetRecord(342, 0, pKey);
end
function GameScheme:ActivityExchange(iIndex)
return self.m_pDC:GetRecordByIndex(343, iIndex)
end
function GameScheme:ActivityExchange_nums()
return self.m_pDC:GetFileRecordNums(343)
end
function GameScheme:ActivityExchange_0(Id)
local pKey={Id=Id}
return self.m_pDC:GetRecord(343, 0, pKey);
end
function GameScheme:SkinPortraitsPic(iIndex)
return self.m_pDC:GetRecordByIndex(344, iIndex)
end
function GameScheme:SkinPortraitsPic_nums()
return self.m_pDC:GetFileRecordNums(344)
end
function GameScheme:SkinPortraitsPic_0(skinID)
local pKey={skinID=skinID}
return self.m_pDC:GetRecord(344, 0, pKey);
end
function GameScheme:Country(iIndex)
return self.m_pDC:GetRecordByIndex(345, iIndex)
end
function GameScheme:Country_nums()
return self.m_pDC:GetFileRecordNums(345)
end
function GameScheme:Country_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(345, 0, pKey);
end
function GameScheme:GalaxyTask(iIndex)
return self.m_pDC:GetRecordByIndex(346, iIndex)
end
function GameScheme:GalaxyTask_nums()
return self.m_pDC:GetFileRecordNums(346)
end
function GameScheme:GalaxyTask_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(346, 0, pKey);
end
function GameScheme:GalaxyHero(iIndex)
return self.m_pDC:GetRecordByIndex(347, iIndex)
end
function GameScheme:GalaxyHero_nums()
return self.m_pDC:GetFileRecordNums(347)
end
function GameScheme:GalaxyHero_0(heroSerialNo)
local pKey={heroSerialNo=heroSerialNo}
return self.m_pDC:GetRecord(347, 0, pKey);
end
function GameScheme:GalaxyMonster(iIndex)
return self.m_pDC:GetRecordByIndex(348, iIndex)
end
function GameScheme:GalaxyMonster_nums()
return self.m_pDC:GetFileRecordNums(348)
end
function GameScheme:GalaxyMonster_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(348, 0, pKey);
end
function GameScheme:GalaxyMonsterDefineLogic(iIndex)
return self.m_pDC:GetRecordByIndex(349, iIndex)
end
function GameScheme:GalaxyMonsterDefineLogic_nums()
return self.m_pDC:GetFileRecordNums(349)
end
function GameScheme:GalaxyMonsterDefineLogic_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(349, 0, pKey);
end
function GameScheme:GalaxyRound(iIndex)
return self.m_pDC:GetRecordByIndex(350, iIndex)
end
function GameScheme:GalaxyRound_nums()
return self.m_pDC:GetFileRecordNums(350)
end
function GameScheme:GalaxyRound_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(350, 0, pKey);
end
function GameScheme:GalaxyShop(iIndex)
return self.m_pDC:GetRecordByIndex(351, iIndex)
end
function GameScheme:GalaxyShop_nums()
return self.m_pDC:GetFileRecordNums(351)
end
function GameScheme:GalaxyShop_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(351, 0, pKey);
end
function GameScheme:GalaxyItem(iIndex)
return self.m_pDC:GetRecordByIndex(352, iIndex)
end
function GameScheme:GalaxyItem_nums()
return self.m_pDC:GetFileRecordNums(352)
end
function GameScheme:GalaxyItem_0(itemSerialNo)
local pKey={itemSerialNo=itemSerialNo}
return self.m_pDC:GetRecord(352, 0, pKey);
end
function GameScheme:GalaxyBanner(iIndex)
return self.m_pDC:GetRecordByIndex(353, iIndex)
end
function GameScheme:GalaxyBanner_nums()
return self.m_pDC:GetFileRecordNums(353)
end
function GameScheme:GalaxyBanner_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(353, 0, pKey);
end
function GameScheme:GalaxySvrCfg(iIndex)
return self.m_pDC:GetRecordByIndex(354, iIndex)
end
function GameScheme:GalaxySvrCfg_nums()
return self.m_pDC:GetFileRecordNums(354)
end
function GameScheme:GalaxySvrCfg_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(354, 0, pKey);
end
function GameScheme:GalaxyRank(iIndex)
return self.m_pDC:GetRecordByIndex(355, iIndex)
end
function GameScheme:GalaxyRank_nums()
return self.m_pDC:GetFileRecordNums(355)
end
function GameScheme:GalaxyRank_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(355, 0, pKey);
end
function GameScheme:GalaxyRankReward(iIndex)
return self.m_pDC:GetRecordByIndex(356, iIndex)
end
function GameScheme:GalaxyRankReward_nums()
return self.m_pDC:GetFileRecordNums(356)
end
function GameScheme:GalaxyRankReward_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(356, 0, pKey);
end
function GameScheme:ActivityKoiFish(iIndex)
return self.m_pDC:GetRecordByIndex(357, iIndex)
end
function GameScheme:ActivityKoiFish_nums()
return self.m_pDC:GetFileRecordNums(357)
end
function GameScheme:ActivityKoiFish_0(Id)
local pKey={Id=Id}
return self.m_pDC:GetRecord(357, 0, pKey);
end
function GameScheme:festivalActivityUrl(iIndex)
return self.m_pDC:GetRecordByIndex(358, iIndex)
end
function GameScheme:festivalActivityUrl_nums()
return self.m_pDC:GetFileRecordNums(358)
end
function GameScheme:festivalActivityUrl_0(cfgID)
local pKey={cfgID=cfgID}
return self.m_pDC:GetRecord(358, 0, pKey);
end
function GameScheme:DimensionWarSvrCfg(iIndex)
return self.m_pDC:GetRecordByIndex(359, iIndex)
end
function GameScheme:DimensionWarSvrCfg_nums()
return self.m_pDC:GetFileRecordNums(359)
end
function GameScheme:DimensionWarSvrCfg_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(359, 0, pKey);
end
function GameScheme:DimensionWarTeamlimt(iIndex)
return self.m_pDC:GetRecordByIndex(360, iIndex)
end
function GameScheme:DimensionWarTeamlimt_nums()
return self.m_pDC:GetFileRecordNums(360)
end
function GameScheme:DimensionWarTeamlimt_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(360, 0, pKey);
end
function GameScheme:DimensionWarTask(iIndex)
return self.m_pDC:GetRecordByIndex(361, iIndex)
end
function GameScheme:DimensionWarTask_nums()
return self.m_pDC:GetFileRecordNums(361)
end
function GameScheme:DimensionWarTask_0(nTaskID)
local pKey={nTaskID=nTaskID}
return self.m_pDC:GetRecord(361, 0, pKey);
end
function GameScheme:DimensionWarMatchScore(iIndex)
return self.m_pDC:GetRecordByIndex(362, iIndex)
end
function GameScheme:DimensionWarMatchScore_nums()
return self.m_pDC:GetFileRecordNums(362)
end
function GameScheme:DimensionWarMatchScore_0(score)
local pKey={score=score}
return self.m_pDC:GetRecord(362, 0, pKey);
end
function GameScheme:DimensionWarMap(iIndex)
return self.m_pDC:GetRecordByIndex(363, iIndex)
end
function GameScheme:DimensionWarMap_nums()
return self.m_pDC:GetFileRecordNums(363)
end
function GameScheme:DimensionWarMap_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(363, 0, pKey);
end
function GameScheme:DimensionWarMap_1(iIndex,iMapID)
local pKey={iIndex=iIndex,iMapID=iMapID}
return self.m_pDC:GetRecord(363, 1, pKey);
end
function GameScheme:DimensionWarFort(iIndex)
return self.m_pDC:GetRecordByIndex(364, iIndex)
end
function GameScheme:DimensionWarFort_nums()
return self.m_pDC:GetFileRecordNums(364)
end
function GameScheme:DimensionWarFort_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(364, 0, pKey);
end
function GameScheme:DimensionWarFort_1(iGroupID,iPointType)
local pKey={iGroupID=iGroupID,iPointType=iPointType}
return self.m_pDC:GetRecord(364, 1, pKey);
end
function GameScheme:DimensionWar(iIndex)
return self.m_pDC:GetRecordByIndex(365, iIndex)
end
function GameScheme:DimensionWar_nums()
return self.m_pDC:GetFileRecordNums(365)
end
function GameScheme:DimensionWar_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(365, 0, pKey);
end
function GameScheme:DimensionWarBoss(iIndex)
return self.m_pDC:GetRecordByIndex(366, iIndex)
end
function GameScheme:DimensionWarBoss_nums()
return self.m_pDC:GetFileRecordNums(366)
end
function GameScheme:DimensionWarBoss_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(366, 0, pKey);
end
function GameScheme:DimensionWarDescribe(iIndex)
return self.m_pDC:GetRecordByIndex(367, iIndex)
end
function GameScheme:DimensionWarDescribe_nums()
return self.m_pDC:GetFileRecordNums(367)
end
function GameScheme:DimensionWarDescribe_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(367, 0, pKey);
end
function GameScheme:NewMiniGameControl(iIndex)
return self.m_pDC:GetRecordByIndex(368, iIndex)
end
function GameScheme:NewMiniGameControl_nums()
return self.m_pDC:GetFileRecordNums(368)
end
function GameScheme:NewMiniGameControl_0(CollectionID)
local pKey={CollectionID=CollectionID}
return self.m_pDC:GetRecord(368, 0, pKey);
end
function GameScheme:NewMiniGameLevelControl(iIndex)
return self.m_pDC:GetRecordByIndex(369, iIndex)
end
function GameScheme:NewMiniGameLevelControl_nums()
return self.m_pDC:GetFileRecordNums(369)
end
function GameScheme:NewMiniGameLevelControl_0(CollectionID,LevelID)
local pKey={CollectionID=CollectionID,LevelID=LevelID}
return self.m_pDC:GetRecord(369, 0, pKey);
end
function GameScheme:MinigameSetting(iIndex)
return self.m_pDC:GetRecordByIndex(370, iIndex)
end
function GameScheme:MinigameSetting_nums()
return self.m_pDC:GetFileRecordNums(370)
end
function GameScheme:MinigameSetting_0(Id)
local pKey={Id=Id}
return self.m_pDC:GetRecord(370, 0, pKey);
end
function GameScheme:ModuleOpen(iIndex)
return self.m_pDC:GetRecordByIndex(371, iIndex)
end
function GameScheme:ModuleOpen_nums()
return self.m_pDC:GetFileRecordNums(371)
end
function GameScheme:ModuleOpen_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(371, 0, pKey);
end
function GameScheme:StarWeapon(iIndex)
return self.m_pDC:GetRecordByIndex(372, iIndex)
end
function GameScheme:StarWeapon_nums()
return self.m_pDC:GetFileRecordNums(372)
end
function GameScheme:StarWeapon_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(372, 0, pKey);
end
function GameScheme:Gem(iIndex)
return self.m_pDC:GetRecordByIndex(373, iIndex)
end
function GameScheme:Gem_nums()
return self.m_pDC:GetFileRecordNums(373)
end
function GameScheme:Gem_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(373, 0, pKey);
end
function GameScheme:Gem_1(starLv,type)
local pKey={starLv=starLv,type=type}
return self.m_pDC:GetRecord(373, 1, pKey);
end
function GameScheme:GemComposite(iIndex)
return self.m_pDC:GetRecordByIndex(374, iIndex)
end
function GameScheme:GemComposite_nums()
return self.m_pDC:GetFileRecordNums(374)
end
function GameScheme:GemComposite_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(374, 0, pKey);
end
function GameScheme:PickTheRoute(iIndex)
return self.m_pDC:GetRecordByIndex(375, iIndex)
end
function GameScheme:PickTheRoute_nums()
return self.m_pDC:GetFileRecordNums(375)
end
function GameScheme:PickTheRoute_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(375, 0, pKey);
end
function GameScheme:PickTheRouteEvent(iIndex)
return self.m_pDC:GetRecordByIndex(376, iIndex)
end
function GameScheme:PickTheRouteEvent_nums()
return self.m_pDC:GetFileRecordNums(376)
end
function GameScheme:PickTheRouteEvent_0(nIndex)
local pKey={nIndex=nIndex}
return self.m_pDC:GetRecord(376, 0, pKey);
end
function GameScheme:PickTheRouteEvent_1(eventId,group)
local pKey={eventId=eventId,group=group}
return self.m_pDC:GetRecord(376, 1, pKey);
end
function GameScheme:FestivalAssetsConfig(iIndex)
return self.m_pDC:GetRecordByIndex(377, iIndex)
end
function GameScheme:FestivalAssetsConfig_nums()
return self.m_pDC:GetFileRecordNums(377)
end
function GameScheme:FestivalAssetsConfig_0(assetName)
local pKey={assetName=assetName}
return self.m_pDC:GetRecord(377, 0, pKey);
end
function GameScheme:SpringFestivalBlessing(iIndex)
return self.m_pDC:GetRecordByIndex(378, iIndex)
end
function GameScheme:SpringFestivalBlessing_nums()
return self.m_pDC:GetFileRecordNums(378)
end
function GameScheme:SpringFestivalBlessing_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(378, 0, pKey);
end
function GameScheme:SpringFestivalBlessing_1(dayID,headingCode,versionNumber)
local pKey={dayID=dayID,headingCode=headingCode,versionNumber=versionNumber}
return self.m_pDC:GetRecord(378, 1, pKey);
end
function GameScheme:HalloweenCostume(iIndex)
return self.m_pDC:GetRecordByIndex(379, iIndex)
end
function GameScheme:HalloweenCostume_nums()
return self.m_pDC:GetFileRecordNums(379)
end
function GameScheme:HalloweenCostume_0(DressID)
local pKey={DressID=DressID}
return self.m_pDC:GetRecord(379, 0, pKey);
end
function GameScheme:HalloweenRiddle(iIndex)
return self.m_pDC:GetRecordByIndex(380, iIndex)
end
function GameScheme:HalloweenRiddle_nums()
return self.m_pDC:GetFileRecordNums(380)
end
function GameScheme:HalloweenRiddle_0(riddleID)
local pKey={riddleID=riddleID}
return self.m_pDC:GetRecord(380, 0, pKey);
end
function GameScheme:HalloweenScore(iIndex)
return self.m_pDC:GetRecordByIndex(381, iIndex)
end
function GameScheme:HalloweenScore_nums()
return self.m_pDC:GetFileRecordNums(381)
end
function GameScheme:HalloweenScore_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(381, 0, pKey);
end
function GameScheme:TangrenDay(iIndex)
return self.m_pDC:GetRecordByIndex(382, iIndex)
end
function GameScheme:TangrenDay_nums()
return self.m_pDC:GetFileRecordNums(382)
end
function GameScheme:TangrenDay_0(activitydays)
local pKey={activitydays=activitydays}
return self.m_pDC:GetRecord(382, 0, pKey);
end
function GameScheme:TangrenRes(iIndex)
return self.m_pDC:GetRecordByIndex(383, iIndex)
end
function GameScheme:TangrenRes_nums()
return self.m_pDC:GetFileRecordNums(383)
end
function GameScheme:TangrenRes_0(tangrenID)
local pKey={tangrenID=tangrenID}
return self.m_pDC:GetRecord(383, 0, pKey);
end
function GameScheme:SkinBubbleSkill(iIndex)
return self.m_pDC:GetRecordByIndex(384, iIndex)
end
function GameScheme:SkinBubbleSkill_nums()
return self.m_pDC:GetFileRecordNums(384)
end
function GameScheme:SkinBubbleSkill_0(skinID,nSkillID)
local pKey={skinID=skinID,nSkillID=nSkillID}
return self.m_pDC:GetRecord(384, 0, pKey);
end
function GameScheme:SkinHeroSkill(iIndex)
return self.m_pDC:GetRecordByIndex(385, iIndex)
end
function GameScheme:SkinHeroSkill_nums()
return self.m_pDC:GetFileRecordNums(385)
end
function GameScheme:SkinHeroSkill_0(skinID,heroSkillID,Lv)
local pKey={skinID=skinID,heroSkillID=heroSkillID,Lv=Lv}
return self.m_pDC:GetRecord(385, 0, pKey);
end
function GameScheme:HeroTrial(iIndex)
return self.m_pDC:GetRecordByIndex(386, iIndex)
end
function GameScheme:HeroTrial_nums()
return self.m_pDC:GetFileRecordNums(386)
end
function GameScheme:HeroTrial_0(AtyID)
local pKey={AtyID=AtyID}
return self.m_pDC:GetRecord(386, 0, pKey);
end
function GameScheme:returnActivityHandup(iIndex)
return self.m_pDC:GetRecordByIndex(387, iIndex)
end
function GameScheme:returnActivityHandup_nums()
return self.m_pDC:GetFileRecordNums(387)
end
function GameScheme:returnActivityHandup_0(TypeID)
local pKey={TypeID=TypeID}
return self.m_pDC:GetRecord(387, 0, pKey);
end
function GameScheme:ActivityPrivilege(iIndex)
return self.m_pDC:GetRecordByIndex(388, iIndex)
end
function GameScheme:ActivityPrivilege_nums()
return self.m_pDC:GetFileRecordNums(388)
end
function GameScheme:ActivityPrivilege_0(versionNumber,Type)
local pKey={versionNumber=versionNumber,Type=Type}
return self.m_pDC:GetRecord(388, 0, pKey);
end
function GameScheme:returnGuidelines(iIndex)
return self.m_pDC:GetRecordByIndex(389, iIndex)
end
function GameScheme:returnGuidelines_nums()
return self.m_pDC:GetFileRecordNums(389)
end
function GameScheme:returnGuidelines_0(TypeID)
local pKey={TypeID=TypeID}
return self.m_pDC:GetRecord(389, 0, pKey);
end
function GameScheme:GraphicsDevice(iIndex)
return self.m_pDC:GetRecordByIndex(390, iIndex)
end
function GameScheme:GraphicsDevice_nums()
return self.m_pDC:GetFileRecordNums(390)
end
function GameScheme:GraphicsDevice_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(390, 0, pKey);
end
function GameScheme:DeviceLevel(iIndex)
return self.m_pDC:GetRecordByIndex(391, iIndex)
end
function GameScheme:DeviceLevel_nums()
return self.m_pDC:GetFileRecordNums(391)
end
function GameScheme:DeviceLevel_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(391, 0, pKey);
end
function GameScheme:supervalue(iIndex)
return self.m_pDC:GetRecordByIndex(392, iIndex)
end
function GameScheme:supervalue_nums()
return self.m_pDC:GetFileRecordNums(392)
end
function GameScheme:supervalue_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(392, 0, pKey);
end
function GameScheme:Calendar(iIndex)
return self.m_pDC:GetRecordByIndex(393, iIndex)
end
function GameScheme:Calendar_nums()
return self.m_pDC:GetFileRecordNums(393)
end
function GameScheme:Calendar_0(CalID)
local pKey={CalID=CalID}
return self.m_pDC:GetRecord(393, 0, pKey);
end
function GameScheme:ForgeLottery(iIndex)
return self.m_pDC:GetRecordByIndex(394, iIndex)
end
function GameScheme:ForgeLottery_nums()
return self.m_pDC:GetFileRecordNums(394)
end
function GameScheme:ForgeLottery_0(Id)
local pKey={Id=Id}
return self.m_pDC:GetRecord(394, 0, pKey);
end
function GameScheme:TreasureRare(iIndex)
return self.m_pDC:GetRecordByIndex(395, iIndex)
end
function GameScheme:TreasureRare_nums()
return self.m_pDC:GetFileRecordNums(395)
end
function GameScheme:TreasureRare_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(395, 0, pKey);
end
function GameScheme:TreasureRare_1(icongroup,refineCount)
local pKey={icongroup=icongroup,refineCount=refineCount}
return self.m_pDC:GetRecord(395, 1, pKey);
end
function GameScheme:TreasureRare_2(ID,clipsId)
local pKey={ID=ID,clipsId=clipsId}
return self.m_pDC:GetRecord(395, 2, pKey);
end
function GameScheme:TreasureRareGift(iIndex)
return self.m_pDC:GetRecordByIndex(396, iIndex)
end
function GameScheme:TreasureRareGift_nums()
return self.m_pDC:GetFileRecordNums(396)
end
function GameScheme:TreasureRareGift_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(396, 0, pKey);
end
function GameScheme:ForgeSlot(iIndex)
return self.m_pDC:GetRecordByIndex(397, iIndex)
end
function GameScheme:ForgeSlot_nums()
return self.m_pDC:GetFileRecordNums(397)
end
function GameScheme:ForgeSlot_0(Id)
local pKey={Id=Id}
return self.m_pDC:GetRecord(397, 0, pKey);
end
function GameScheme:ReincarnationSvrCfg(iIndex)
return self.m_pDC:GetRecordByIndex(398, iIndex)
end
function GameScheme:ReincarnationSvrCfg_nums()
return self.m_pDC:GetFileRecordNums(398)
end
function GameScheme:ReincarnationSvrCfg_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(398, 0, pKey);
end
function GameScheme:ReincarnationReward(iIndex)
return self.m_pDC:GetRecordByIndex(399, iIndex)
end
function GameScheme:ReincarnationReward_nums()
return self.m_pDC:GetFileRecordNums(399)
end
function GameScheme:ReincarnationReward_0(index)
local pKey={index=index}
return self.m_pDC:GetRecord(399, 0, pKey);
end
function GameScheme:ReincarnationRobot(iIndex)
return self.m_pDC:GetRecordByIndex(400, iIndex)
end
function GameScheme:ReincarnationRobot_nums()
return self.m_pDC:GetFileRecordNums(400)
end
function GameScheme:ReincarnationRobot_0()
local pKey={}
return self.m_pDC:GetRecord(400, 0, pKey);
end
function GameScheme:Reincarnation(iIndex)
return self.m_pDC:GetRecordByIndex(401, iIndex)
end
function GameScheme:Reincarnation_nums()
return self.m_pDC:GetFileRecordNums(401)
end
function GameScheme:Reincarnation_0(index)
local pKey={index=index}
return self.m_pDC:GetRecord(401, 0, pKey);
end
function GameScheme:UnionTreasure(iIndex)
return self.m_pDC:GetRecordByIndex(402, iIndex)
end
function GameScheme:UnionTreasure_nums()
return self.m_pDC:GetFileRecordNums(402)
end
function GameScheme:UnionTreasure_0(index)
local pKey={index=index}
return self.m_pDC:GetRecord(402, 0, pKey);
end
function GameScheme:AnniversaryList(iIndex)
return self.m_pDC:GetRecordByIndex(403, iIndex)
end
function GameScheme:AnniversaryList_nums()
return self.m_pDC:GetFileRecordNums(403)
end
function GameScheme:AnniversaryList_0(Index)
local pKey={Index=Index}
return self.m_pDC:GetRecord(403, 0, pKey);
end
function GameScheme:WelfarePlay(iIndex)
return self.m_pDC:GetRecordByIndex(404, iIndex)
end
function GameScheme:WelfarePlay_nums()
return self.m_pDC:GetFileRecordNums(404)
end
function GameScheme:WelfarePlay_0(ProtossDeckId)
local pKey={ProtossDeckId=ProtossDeckId}
return self.m_pDC:GetRecord(404, 0, pKey);
end
function GameScheme:ExChampionshipSvrCfg(iIndex)
return self.m_pDC:GetRecordByIndex(405, iIndex)
end
function GameScheme:ExChampionshipSvrCfg_nums()
return self.m_pDC:GetFileRecordNums(405)
end
function GameScheme:ExChampionshipSvrCfg_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(405, 0, pKey);
end
function GameScheme:ExChampionship(iIndex)
return self.m_pDC:GetRecordByIndex(406, iIndex)
end
function GameScheme:ExChampionship_nums()
return self.m_pDC:GetFileRecordNums(406)
end
function GameScheme:ExChampionship_0(arenaId)
local pKey={arenaId=arenaId}
return self.m_pDC:GetRecord(406, 0, pKey);
end
function GameScheme:ExChampionshipBet(iIndex)
return self.m_pDC:GetRecordByIndex(407, iIndex)
end
function GameScheme:ExChampionshipBet_nums()
return self.m_pDC:GetFileRecordNums(407)
end
function GameScheme:ExChampionshipBet_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(407, 0, pKey);
end
function GameScheme:ExChampionshipTask(iIndex)
return self.m_pDC:GetRecordByIndex(408, iIndex)
end
function GameScheme:ExChampionshipTask_nums()
return self.m_pDC:GetFileRecordNums(408)
end
function GameScheme:ExChampionshipTask_0(nTaskID)
local pKey={nTaskID=nTaskID}
return self.m_pDC:GetRecord(408, 0, pKey);
end
function GameScheme:ExChampionshipTime(iIndex)
return self.m_pDC:GetRecordByIndex(409, iIndex)
end
function GameScheme:ExChampionshipTime_nums()
return self.m_pDC:GetFileRecordNums(409)
end
function GameScheme:ExChampionshipTime_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(409, 0, pKey);
end
function GameScheme:ExChampionshipTime_1(phaseid,teamid)
local pKey={phaseid=phaseid,teamid=teamid}
return self.m_pDC:GetRecord(409, 1, pKey);
end
function GameScheme:Grandarea(iIndex)
return self.m_pDC:GetRecordByIndex(410, iIndex)
end
function GameScheme:Grandarea_nums()
return self.m_pDC:GetFileRecordNums(410)
end
function GameScheme:Grandarea_0(GrandareaID)
local pKey={GrandareaID=GrandareaID}
return self.m_pDC:GetRecord(410, 0, pKey);
end
function GameScheme:BuildAreaMap(iIndex)
return self.m_pDC:GetRecordByIndex(411, iIndex)
end
function GameScheme:BuildAreaMap_nums()
return self.m_pDC:GetFileRecordNums(411)
end
function GameScheme:BuildAreaMap_0(AreaID)
local pKey={AreaID=AreaID}
return self.m_pDC:GetRecord(411, 0, pKey);
end
function GameScheme:BuildMaincityMap(iIndex)
return self.m_pDC:GetRecordByIndex(412, iIndex)
end
function GameScheme:BuildMaincityMap_nums()
return self.m_pDC:GetFileRecordNums(412)
end
function GameScheme:BuildMaincityMap_0(MapID)
local pKey={MapID=MapID}
return self.m_pDC:GetRecord(412, 0, pKey);
end
function GameScheme:Building(iIndex)
return self.m_pDC:GetRecordByIndex(413, iIndex)
end
function GameScheme:Building_nums()
return self.m_pDC:GetFileRecordNums(413)
end
function GameScheme:Building_0(BuildingID,level)
local pKey={BuildingID=BuildingID,level=level}
return self.m_pDC:GetRecord(413, 0, pKey);
end
function GameScheme:BuildingType(iIndex)
return self.m_pDC:GetRecordByIndex(414, iIndex)
end
function GameScheme:BuildingType_nums()
return self.m_pDC:GetFileRecordNums(414)
end
function GameScheme:BuildingType_0(TypeID)
local pKey={TypeID=TypeID}
return self.m_pDC:GetRecord(414, 0, pKey);
end
function GameScheme:FunctionOpen(iIndex)
return self.m_pDC:GetRecordByIndex(415, iIndex)
end
function GameScheme:FunctionOpen_nums()
return self.m_pDC:GetFileRecordNums(415)
end
function GameScheme:FunctionOpen_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(415, 0, pKey);
end
function GameScheme:ScientificClassification(iIndex)
return self.m_pDC:GetRecordByIndex(416, iIndex)
end
function GameScheme:ScientificClassification_nums()
return self.m_pDC:GetFileRecordNums(416)
end
function GameScheme:ScientificClassification_0(ScientificTypeID)
local pKey={ScientificTypeID=ScientificTypeID}
return self.m_pDC:GetRecord(416, 0, pKey);
end
function GameScheme:ScientificResearch(iIndex)
return self.m_pDC:GetRecordByIndex(417, iIndex)
end
function GameScheme:ScientificResearch_nums()
return self.m_pDC:GetFileRecordNums(417)
end
function GameScheme:ScientificResearch_0(ScientificID,Level)
local pKey={ScientificID=ScientificID,Level=Level}
return self.m_pDC:GetRecord(417, 0, pKey);
end
function GameScheme:GWMapEffect(iIndex)
return self.m_pDC:GetRecordByIndex(418, iIndex)
end
function GameScheme:GWMapEffect_nums()
return self.m_pDC:GetFileRecordNums(418)
end
function GameScheme:GWMapEffect_0(effectID)
local pKey={effectID=effectID}
return self.m_pDC:GetRecord(418, 0, pKey);
end
function GameScheme:SpoilsChest(iIndex)
return self.m_pDC:GetRecordByIndex(419, iIndex)
end
function GameScheme:SpoilsChest_nums()
return self.m_pDC:GetFileRecordNums(419)
end
function GameScheme:SpoilsChest_0(SpoilsID,AllianceGiftLevel)
local pKey={SpoilsID=SpoilsID,AllianceGiftLevel=AllianceGiftLevel}
return self.m_pDC:GetRecord(419, 0, pKey);
end
function GameScheme:LeagueTechnology(iIndex)
return self.m_pDC:GetRecordByIndex(420, iIndex)
end
function GameScheme:LeagueTechnology_nums()
return self.m_pDC:GetFileRecordNums(420)
end
function GameScheme:LeagueTechnology_0(TechnologyID)
local pKey={TechnologyID=TechnologyID}
return self.m_pDC:GetRecord(420, 0, pKey);
end
function GameScheme:LeagueAuthority(iIndex)
return self.m_pDC:GetRecordByIndex(421, iIndex)
end
function GameScheme:LeagueAuthority_nums()
return self.m_pDC:GetFileRecordNums(421)
end
function GameScheme:LeagueAuthority_0(AuthorityID)
local pKey={AuthorityID=AuthorityID}
return self.m_pDC:GetRecord(421, 0, pKey);
end
function GameScheme:LeagueAuthority_1(PermissionNameId)
local pKey={PermissionNameId=PermissionNameId}
return self.m_pDC:GetRecord(421, 1, pKey);
end
function GameScheme:leagueTables(iIndex)
return self.m_pDC:GetRecordByIndex(422, iIndex)
end
function GameScheme:leagueTables_nums()
return self.m_pDC:GetFileRecordNums(422)
end
function GameScheme:leagueTables_0(TableID)
local pKey={TableID=TableID}
return self.m_pDC:GetRecord(422, 0, pKey);
end
function GameScheme:LeagueDonations(iIndex)
return self.m_pDC:GetRecordByIndex(423, iIndex)
end
function GameScheme:LeagueDonations_nums()
return self.m_pDC:GetFileRecordNums(423)
end
function GameScheme:LeagueDonations_0(DonationsID)
local pKey={DonationsID=DonationsID}
return self.m_pDC:GetRecord(423, 0, pKey);
end
function GameScheme:LeaguePermissionEffect(iIndex)
return self.m_pDC:GetRecordByIndex(424, iIndex)
end
function GameScheme:LeaguePermissionEffect_nums()
return self.m_pDC:GetFileRecordNums(424)
end
function GameScheme:LeaguePermissionEffect_0(AuthorityID)
local pKey={AuthorityID=AuthorityID}
return self.m_pDC:GetRecord(424, 0, pKey);
end
function GameScheme:AccelerateRewardItem(iIndex)
return self.m_pDC:GetRecordByIndex(425, iIndex)
end
function GameScheme:AccelerateRewardItem_nums()
return self.m_pDC:GetFileRecordNums(425)
end
function GameScheme:AccelerateRewardItem_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(425, 0, pKey);
end
function GameScheme:LeagueMutualAid(iIndex)
return self.m_pDC:GetRecordByIndex(426, iIndex)
end
function GameScheme:LeagueMutualAid_nums()
return self.m_pDC:GetFileRecordNums(426)
end
function GameScheme:LeagueMutualAid_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(426, 0, pKey);
end
function GameScheme:SandMapCity(iIndex)
return self.m_pDC:GetRecordByIndex(427, iIndex)
end
function GameScheme:SandMapCity_nums()
return self.m_pDC:GetFileRecordNums(427)
end
function GameScheme:SandMapCity_0(cityID)
local pKey={cityID=cityID}
return self.m_pDC:GetRecord(427, 0, pKey);
end
function GameScheme:SandMapRegion(iIndex)
return self.m_pDC:GetRecordByIndex(428, iIndex)
end
function GameScheme:SandMapRegion_nums()
return self.m_pDC:GetFileRecordNums(428)
end
function GameScheme:SandMapRegion_0(regionID)
local pKey={regionID=regionID}
return self.m_pDC:GetRecord(428, 0, pKey);
end
function GameScheme:SandMapRegion_1(CityPos)
local pKey={CityPos=CityPos}
return self.m_pDC:GetRecord(428, 1, pKey);
end
function GameScheme:SandMapCompare(iIndex)
return self.m_pDC:GetRecordByIndex(429, iIndex)
end
function GameScheme:SandMapCompare_nums()
return self.m_pDC:GetFileRecordNums(429)
end
function GameScheme:SandMapCompare_0(endID)
local pKey={endID=endID}
return self.m_pDC:GetRecord(429, 0, pKey);
end
function GameScheme:SandMapResources(iIndex)
return self.m_pDC:GetRecordByIndex(430, iIndex)
end
function GameScheme:SandMapResources_nums()
return self.m_pDC:GetFileRecordNums(430)
end
function GameScheme:SandMapResources_0(resourcesID)
local pKey={resourcesID=resourcesID}
return self.m_pDC:GetRecord(430, 0, pKey);
end
function GameScheme:SandMapMonster(iIndex)
return self.m_pDC:GetRecordByIndex(431, iIndex)
end
function GameScheme:SandMapMonster_nums()
return self.m_pDC:GetFileRecordNums(431)
end
function GameScheme:SandMapMonster_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(431, 0, pKey);
end
function GameScheme:SandMapMarch(iIndex)
return self.m_pDC:GetRecordByIndex(432, iIndex)
end
function GameScheme:SandMapMarch_nums()
return self.m_pDC:GetFileRecordNums(432)
end
function GameScheme:SandMapMarch_0(marchID)
local pKey={marchID=marchID}
return self.m_pDC:GetRecord(432, 0, pKey);
end
function GameScheme:GWMapConstant(iIndex)
return self.m_pDC:GetRecordByIndex(433, iIndex)
end
function GameScheme:GWMapConstant_nums()
return self.m_pDC:GetFileRecordNums(433)
end
function GameScheme:GWMapConstant_0(iIndex)
local pKey={iIndex=iIndex}
return self.m_pDC:GetRecord(433, 0, pKey);
end
function GameScheme:SandMapStratum(iIndex)
return self.m_pDC:GetRecordByIndex(434, iIndex)
end
function GameScheme:SandMapStratum_nums()
return self.m_pDC:GetFileRecordNums(434)
end
function GameScheme:SandMapStratum_0(resourceID)
local pKey={resourceID=resourceID}
return self.m_pDC:GetRecord(434, 0, pKey);
end
function GameScheme:SandMapModelResource(iIndex)
return self.m_pDC:GetRecordByIndex(435, iIndex)
end
function GameScheme:SandMapModelResource_nums()
return self.m_pDC:GetFileRecordNums(435)
end
function GameScheme:SandMapModelResource_0(resourceID)
local pKey={resourceID=resourceID}
return self.m_pDC:GetRecord(435, 0, pKey);
end
function GameScheme:SandMapCityAdorn(iIndex)
return self.m_pDC:GetRecordByIndex(436, iIndex)
end
function GameScheme:SandMapCityAdorn_nums()
return self.m_pDC:GetFileRecordNums(436)
end
function GameScheme:SandMapCityAdorn_0(adornID)
local pKey={adornID=adornID}
return self.m_pDC:GetRecord(436, 0, pKey);
end
function GameScheme:Soldier(iIndex)
return self.m_pDC:GetRecordByIndex(437, iIndex)
end
function GameScheme:Soldier_nums()
return self.m_pDC:GetFileRecordNums(437)
end
function GameScheme:Soldier_0(soldierID)
local pKey={soldierID=soldierID}
return self.m_pDC:GetRecord(437, 0, pKey);
end
function GameScheme:Soldier_1(level)
local pKey={level=level}
return self.m_pDC:GetRecord(437, 1, pKey);
end
function GameScheme:BuildProduce(iIndex)
return self.m_pDC:GetRecordByIndex(438, iIndex)
end
function GameScheme:BuildProduce_nums()
return self.m_pDC:GetFileRecordNums(438)
end
function GameScheme:BuildProduce_0(produceID)
local pKey={produceID=produceID}
return self.m_pDC:GetRecord(438, 0, pKey);
end
function GameScheme:SandMapWander(iIndex)
return self.m_pDC:GetRecordByIndex(439, iIndex)
end
function GameScheme:SandMapWander_nums()
return self.m_pDC:GetFileRecordNums(439)
end
function GameScheme:SandMapWander_0(refreshID)
local pKey={refreshID=refreshID}
return self.m_pDC:GetRecord(439, 0, pKey);
end
function GameScheme:SandMapWander_1(day)
local pKey={day=day}
return self.m_pDC:GetRecord(439, 1, pKey);
end
function GameScheme:BuildSurvivor(iIndex)
return self.m_pDC:GetRecordByIndex(440, iIndex)
end
function GameScheme:BuildSurvivor_nums()
return self.m_pDC:GetFileRecordNums(440)
end
function GameScheme:BuildSurvivor_0(SurvivorID)
local pKey={SurvivorID=SurvivorID}
return self.m_pDC:GetRecord(440, 0, pKey);
end
function GameScheme:BuildSurvivorSkill(iIndex)
return self.m_pDC:GetRecordByIndex(441, iIndex)
end
function GameScheme:BuildSurvivorSkill_nums()
return self.m_pDC:GetFileRecordNums(441)
end
function GameScheme:BuildSurvivorSkill_0(SurvivorSkillID)
local pKey={SurvivorSkillID=SurvivorSkillID}
return self.m_pDC:GetRecord(441, 0, pKey);
end
function GameScheme:SandMapViewLevel(iIndex)
return self.m_pDC:GetRecordByIndex(442, iIndex)
end
function GameScheme:SandMapViewLevel_nums()
return self.m_pDC:GetFileRecordNums(442)
end
function GameScheme:SandMapViewLevel_0(resourceID)
local pKey={resourceID=resourceID}
return self.m_pDC:GetRecord(442, 0, pKey);
end
function GameScheme:SandMapModelResourceNew(iIndex)
return self.m_pDC:GetRecordByIndex(443, iIndex)
end
function GameScheme:SandMapModelResourceNew_nums()
return self.m_pDC:GetFileRecordNums(443)
end
function GameScheme:SandMapModelResourceNew_0(resourceID)
local pKey={resourceID=resourceID}
return self.m_pDC:GetRecord(443, 0, pKey);
end
function GameScheme:BuildEvent(iIndex)
return self.m_pDC:GetRecordByIndex(444, iIndex)
end
function GameScheme:BuildEvent_nums()
return self.m_pDC:GetFileRecordNums(444)
end
function GameScheme:BuildEvent_0(EventID)
local pKey={EventID=EventID}
return self.m_pDC:GetRecord(444, 0, pKey);
end
function GameScheme:BuildEvent_1(PreEvent)
local pKey={PreEvent=PreEvent}
return self.m_pDC:GetRecord(444, 1, pKey);
end
function GameScheme:BuildingMapData(iIndex)
return self.m_pDC:GetRecordByIndex(445, iIndex)
end
function GameScheme:BuildingMapData_nums()
return self.m_pDC:GetFileRecordNums(445)
end
function GameScheme:BuildingMapData_0(mapID)
local pKey={mapID=mapID}
return self.m_pDC:GetRecord(445, 0, pKey);
end
function GameScheme:BubbleManager(iIndex)
return self.m_pDC:GetRecordByIndex(446, iIndex)
end
function GameScheme:BubbleManager_nums()
return self.m_pDC:GetFileRecordNums(446)
end
function GameScheme:BubbleManager_0(BubbleID)
local pKey={BubbleID=BubbleID}
return self.m_pDC:GetRecord(446, 0, pKey);
end
function GameScheme:GWMapBuff(iIndex)
return self.m_pDC:GetRecordByIndex(447, iIndex)
end
function GameScheme:GWMapBuff_nums()
return self.m_pDC:GetFileRecordNums(447)
end
function GameScheme:GWMapBuff_0(BuffID)
local pKey={BuffID=BuffID}
return self.m_pDC:GetRecord(447, 0, pKey);
end
function GameScheme:HeroSkillLevelup(iIndex)
return self.m_pDC:GetRecordByIndex(448, iIndex)
end
function GameScheme:HeroSkillLevelup_nums()
return self.m_pDC:GetFileRecordNums(448)
end
function GameScheme:HeroSkillLevelup_0(skillLevel,heroQuality,skillList)
local pKey={skillLevel=skillLevel,heroQuality=heroQuality,skillList=skillList}
return self.m_pDC:GetRecord(448, 0, pKey);
end
function GameScheme:HeroSkillUnlock(iIndex)
return self.m_pDC:GetRecordByIndex(449, iIndex)
end
function GameScheme:HeroSkillUnlock_nums()
return self.m_pDC:GetFileRecordNums(449)
end
function GameScheme:HeroSkillUnlock_0(heroQuality,skillList)
local pKey={heroQuality=heroQuality,skillList=skillList}
return self.m_pDC:GetRecord(449, 0, pKey);
end
function GameScheme:HeroLevelUp(iIndex)
return self.m_pDC:GetRecordByIndex(450, iIndex)
end
function GameScheme:HeroLevelUp_nums()
return self.m_pDC:GetFileRecordNums(450)
end
function GameScheme:HeroLevelUp_0(heroID,level)
local pKey={heroID=heroID,level=level}
return self.m_pDC:GetRecord(450, 0, pKey);
end
function GameScheme:HeroProList(iIndex)
return self.m_pDC:GetRecordByIndex(451, iIndex)
end
function GameScheme:HeroProList_nums()
return self.m_pDC:GetFileRecordNums(451)
end
function GameScheme:HeroProList_0(ListID)
local pKey={ListID=ListID}
return self.m_pDC:GetRecord(451, 0, pKey);
end
function GameScheme:ProSourceToLang(iIndex)
return self.m_pDC:GetRecordByIndex(452, iIndex)
end
function GameScheme:ProSourceToLang_nums()
return self.m_pDC:GetFileRecordNums(452)
end
function GameScheme:ProSourceToLang_0(iProSourceID)
local pKey={iProSourceID=iProSourceID}
return self.m_pDC:GetRecord(452, 0, pKey);
end
function GameScheme:RoleSchloss(iIndex)
return self.m_pDC:GetRecordByIndex(453, iIndex)
end
function GameScheme:RoleSchloss_nums()
return self.m_pDC:GetFileRecordNums(453)
end
function GameScheme:RoleSchloss_0(CastleID)
local pKey={CastleID=CastleID}
return self.m_pDC:GetRecord(453, 0, pKey);
end
function GameScheme:RadarMission(iIndex)
return self.m_pDC:GetRecordByIndex(454, iIndex)
end
function GameScheme:RadarMission_nums()
return self.m_pDC:GetFileRecordNums(454)
end
function GameScheme:RadarMission_0(taskID)
local pKey={taskID=taskID}
return self.m_pDC:GetRecord(454, 0, pKey);
end
function GameScheme:Radarlevel(iIndex)
return self.m_pDC:GetRecordByIndex(455, iIndex)
end
function GameScheme:Radarlevel_nums()
return self.m_pDC:GetFileRecordNums(455)
end
function GameScheme:Radarlevel_0(Radarlevel)
local pKey={Radarlevel=Radarlevel}
return self.m_pDC:GetRecord(455, 0, pKey);
end
function GameScheme:RadarRefresh(iIndex)
return self.m_pDC:GetRecordByIndex(456, iIndex)
end
function GameScheme:RadarRefresh_nums()
return self.m_pDC:GetFileRecordNums(456)
end
function GameScheme:RadarRefresh_0(refreshID)
local pKey={refreshID=refreshID}
return self.m_pDC:GetRecord(456, 0, pKey);
end
function GameScheme:DiggingTreasures(iIndex)
return self.m_pDC:GetRecordByIndex(457, iIndex)
end
function GameScheme:DiggingTreasures_nums()
return self.m_pDC:GetFileRecordNums(457)
end
function GameScheme:DiggingTreasures_0(treasuresID)
local pKey={treasuresID=treasuresID}
return self.m_pDC:GetRecord(457, 0, pKey);
end
function GameScheme:RadarTaskLogic(iIndex)
return self.m_pDC:GetRecordByIndex(458, iIndex)
end
function GameScheme:RadarTaskLogic_nums()
return self.m_pDC:GetFileRecordNums(458)
end
function GameScheme:RadarTaskLogic_0(taskLogicTableID)
local pKey={taskLogicTableID=taskLogicTableID}
return self.m_pDC:GetRecord(458, 0, pKey);
end
function GameScheme:SandkastenBox(iIndex)
return self.m_pDC:GetRecordByIndex(459, iIndex)
end
function GameScheme:SandkastenBox_nums()
return self.m_pDC:GetFileRecordNums(459)
end
function GameScheme:SandkastenBox_0(boxID)
local pKey={boxID=boxID}
return self.m_pDC:GetRecord(459, 0, pKey);
end
function GameScheme:ChatSharing(iIndex)
return self.m_pDC:GetRecordByIndex(460, iIndex)
end
function GameScheme:ChatSharing_nums()
return self.m_pDC:GetFileRecordNums(460)
end
function GameScheme:ChatSharing_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(460, 0, pKey);
end
function GameScheme:DailyGameplay(iIndex)
return self.m_pDC:GetRecordByIndex(461, iIndex)
end
function GameScheme:DailyGameplay_nums()
return self.m_pDC:GetFileRecordNums(461)
end
function GameScheme:DailyGameplay_0(gameplayType)
local pKey={gameplayType=gameplayType}
return self.m_pDC:GetRecord(461, 0, pKey);
end
function GameScheme:SoldierMonster(iIndex)
return self.m_pDC:GetRecordByIndex(462, iIndex)
end
function GameScheme:SoldierMonster_nums()
return self.m_pDC:GetFileRecordNums(462)
end
function GameScheme:SoldierMonster_0(soldierID)
local pKey={soldierID=soldierID}
return self.m_pDC:GetRecord(462, 0, pKey);
end
function GameScheme:BuildingWall(iIndex)
return self.m_pDC:GetRecordByIndex(463, iIndex)
end
function GameScheme:BuildingWall_nums()
return self.m_pDC:GetFileRecordNums(463)
end
function GameScheme:BuildingWall_0(order)
local pKey={order=order}
return self.m_pDC:GetRecord(463, 0, pKey);
end
function GameScheme:Prescription(iIndex)
return self.m_pDC:GetRecordByIndex(464, iIndex)
end
function GameScheme:Prescription_nums()
return self.m_pDC:GetFileRecordNums(464)
end
function GameScheme:Prescription_0(prescriptionID)
local pKey={prescriptionID=prescriptionID}
return self.m_pDC:GetRecord(464, 0, pKey);
end
function GameScheme:WorldBOSSManagement(iIndex)
return self.m_pDC:GetRecordByIndex(465, iIndex)
end
function GameScheme:WorldBOSSManagement_nums()
return self.m_pDC:GetFileRecordNums(465)
end
function GameScheme:WorldBOSSManagement_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(465, 0, pKey);
end
function GameScheme:RankingRewards(iIndex)
return self.m_pDC:GetRecordByIndex(466, iIndex)
end
function GameScheme:RankingRewards_nums()
return self.m_pDC:GetFileRecordNums(466)
end
function GameScheme:RankingRewards_0(RewardID)
local pKey={RewardID=RewardID}
return self.m_pDC:GetRecord(466, 0, pKey);
end
function GameScheme:FightPowerDetails(iIndex)
return self.m_pDC:GetRecordByIndex(467, iIndex)
end
function GameScheme:FightPowerDetails_nums()
return self.m_pDC:GetFileRecordNums(467)
end
function GameScheme:FightPowerDetails_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(467, 0, pKey);
end
function GameScheme:ActivityMain(iIndex)
return self.m_pDC:GetRecordByIndex(468, iIndex)
end
function GameScheme:ActivityMain_nums()
return self.m_pDC:GetFileRecordNums(468)
end
function GameScheme:ActivityMain_0(AtyID)
local pKey={AtyID=AtyID}
return self.m_pDC:GetRecord(468, 0, pKey);
end
function GameScheme:PioneerCommander(iIndex)
return self.m_pDC:GetRecordByIndex(469, iIndex)
end
function GameScheme:PioneerCommander_nums()
return self.m_pDC:GetFileRecordNums(469)
end
function GameScheme:PioneerCommander_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(469, 0, pKey);
end
function GameScheme:Allianzboss(iIndex)
return self.m_pDC:GetRecordByIndex(470, iIndex)
end
function GameScheme:Allianzboss_nums()
return self.m_pDC:GetFileRecordNums(470)
end
function GameScheme:Allianzboss_0(difficulty)
local pKey={difficulty=difficulty}
return self.m_pDC:GetRecord(470, 0, pKey);
end
function GameScheme:AllianzbossDonation(iIndex)
return self.m_pDC:GetRecordByIndex(471, iIndex)
end
function GameScheme:AllianzbossDonation_nums()
return self.m_pDC:GetFileRecordNums(471)
end
function GameScheme:AllianzbossDonation_0(DonationLevel)
local pKey={DonationLevel=DonationLevel}
return self.m_pDC:GetRecord(471, 0, pKey);
end
function GameScheme:CampTrial(iIndex)
return self.m_pDC:GetRecordByIndex(472, iIndex)
end
function GameScheme:CampTrial_nums()
return self.m_pDC:GetFileRecordNums(472)
end
function GameScheme:CampTrial_0(TrialType)
local pKey={TrialType=TrialType}
return self.m_pDC:GetRecord(472, 0, pKey);
end
function GameScheme:CampTrial_1(Ranking)
local pKey={Ranking=Ranking}
return self.m_pDC:GetRecord(472, 1, pKey);
end
function GameScheme:CampTrialLevel(iIndex)
return self.m_pDC:GetRecordByIndex(473, iIndex)
end
function GameScheme:CampTrialLevel_nums()
return self.m_pDC:GetFileRecordNums(473)
end
function GameScheme:CampTrialLevel_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(473, 0, pKey);
end
function GameScheme:CampTrialLevel_1(TrialType,TrialDifficulty,LevelLayers)
local pKey={TrialType=TrialType,TrialDifficulty=TrialDifficulty,LevelLayers=LevelLayers}
return self.m_pDC:GetRecord(473, 1, pKey);
end
function GameScheme:PersonalTrial(iIndex)
return self.m_pDC:GetRecordByIndex(474, iIndex)
end
function GameScheme:PersonalTrial_nums()
return self.m_pDC:GetFileRecordNums(474)
end
function GameScheme:PersonalTrial_0(difficulty)
local pKey={difficulty=difficulty}
return self.m_pDC:GetRecord(474, 0, pKey);
end
function GameScheme:TrialleagueChallenge(iIndex)
return self.m_pDC:GetRecordByIndex(475, iIndex)
end
function GameScheme:TrialleagueChallenge_nums()
return self.m_pDC:GetFileRecordNums(475)
end
function GameScheme:TrialleagueChallenge_0(difficulty)
local pKey={difficulty=difficulty}
return self.m_pDC:GetRecord(475, 0, pKey);
end
function GameScheme:NewHeroTreasureBox(iIndex)
return self.m_pDC:GetRecordByIndex(476, iIndex)
end
function GameScheme:NewHeroTreasureBox_nums()
return self.m_pDC:GetFileRecordNums(476)
end
function GameScheme:NewHeroTreasureBox_0(AtyID)
local pKey={AtyID=AtyID}
return self.m_pDC:GetRecord(476, 0, pKey);
end
function GameScheme:GatheringActivities(iIndex)
return self.m_pDC:GetRecordByIndex(477, iIndex)
end
function GameScheme:GatheringActivities_nums()
return self.m_pDC:GetFileRecordNums(477)
end
function GameScheme:GatheringActivities_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(477, 0, pKey);
end
function GameScheme:GatheringActivities_1(actid,issue)
local pKey={actid=actid,issue=issue}
return self.m_pDC:GetRecord(477, 1, pKey);
end
function GameScheme:SandMapSearch(iIndex)
return self.m_pDC:GetRecordByIndex(478, iIndex)
end
function GameScheme:SandMapSearch_nums()
return self.m_pDC:GetFileRecordNums(478)
end
function GameScheme:SandMapSearch_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(478, 0, pKey);
end
function GameScheme:ActivityCommonUI(iIndex)
return self.m_pDC:GetRecordByIndex(479, iIndex)
end
function GameScheme:ActivityCommonUI_nums()
return self.m_pDC:GetFileRecordNums(479)
end
function GameScheme:ActivityCommonUI_0(he)
local pKey={he=he}
return self.m_pDC:GetRecord(479, 0, pKey);
end
function GameScheme:ArmsRaceRounds(iIndex)
return self.m_pDC:GetRecordByIndex(480, iIndex)
end
function GameScheme:ArmsRaceRounds_nums()
return self.m_pDC:GetFileRecordNums(480)
end
function GameScheme:ArmsRaceRounds_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(480, 0, pKey);
end
function GameScheme:ArmsRaceTarget(iIndex)
return self.m_pDC:GetRecordByIndex(481, iIndex)
end
function GameScheme:ArmsRaceTarget_nums()
return self.m_pDC:GetFileRecordNums(481)
end
function GameScheme:ArmsRaceTarget_0(BoxID)
local pKey={BoxID=BoxID}
return self.m_pDC:GetRecord(481, 0, pKey);
end
function GameScheme:ArmsRaceTheme(iIndex)
return self.m_pDC:GetRecordByIndex(482, iIndex)
end
function GameScheme:ArmsRaceTheme_nums()
return self.m_pDC:GetFileRecordNums(482)
end
function GameScheme:ArmsRaceTheme_0(ThemeId)
local pKey={ThemeId=ThemeId}
return self.m_pDC:GetRecord(482, 0, pKey);
end
function GameScheme:ArmsRaceThemeTime(iIndex)
return self.m_pDC:GetRecordByIndex(483, iIndex)
end
function GameScheme:ArmsRaceThemeTime_nums()
return self.m_pDC:GetFileRecordNums(483)
end
function GameScheme:ArmsRaceThemeTime_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(483, 0, pKey);
end
function GameScheme:TaskMain(iIndex)
return self.m_pDC:GetRecordByIndex(484, iIndex)
end
function GameScheme:TaskMain_nums()
return self.m_pDC:GetFileRecordNums(484)
end
function GameScheme:TaskMain_0(TaskID)
local pKey={TaskID=TaskID}
return self.m_pDC:GetRecord(484, 0, pKey);
end
function GameScheme:Ranking(iIndex)
return self.m_pDC:GetRecordByIndex(485, iIndex)
end
function GameScheme:Ranking_nums()
return self.m_pDC:GetFileRecordNums(485)
end
function GameScheme:Ranking_0(RankingID)
local pKey={RankingID=RankingID}
return self.m_pDC:GetRecord(485, 0, pKey);
end
function GameScheme:LeagueAchievements(iIndex)
return self.m_pDC:GetRecordByIndex(486, iIndex)
end
function GameScheme:LeagueAchievements_nums()
return self.m_pDC:GetFileRecordNums(486)
end
function GameScheme:LeagueAchievements_0(AchieveID)
local pKey={AchieveID=AchieveID}
return self.m_pDC:GetRecord(486, 0, pKey);
end
function GameScheme:Vip(iIndex)
return self.m_pDC:GetRecordByIndex(487, iIndex)
end
function GameScheme:Vip_nums()
return self.m_pDC:GetFileRecordNums(487)
end
function GameScheme:Vip_0(level)
local pKey={level=level}
return self.m_pDC:GetRecord(487, 0, pKey);
end
function GameScheme:ActivityServerPlan(iIndex)
return self.m_pDC:GetRecordByIndex(488, iIndex)
end
function GameScheme:ActivityServerPlan_nums()
return self.m_pDC:GetFileRecordNums(488)
end
function GameScheme:ActivityServerPlan_0(ServerPlanID,GroupID)
local pKey={ServerPlanID=ServerPlanID,GroupID=GroupID}
return self.m_pDC:GetRecord(488, 0, pKey);
end
function GameScheme:ActivityServerMatch(iIndex)
return self.m_pDC:GetRecordByIndex(489, iIndex)
end
function GameScheme:ActivityServerMatch_nums()
return self.m_pDC:GetFileRecordNums(489)
end
function GameScheme:ActivityServerMatch_0(ServerMatchID)
local pKey={ServerMatchID=ServerMatchID}
return self.m_pDC:GetRecord(489, 0, pKey);
end
function GameScheme:NpcConfig(iIndex)
return self.m_pDC:GetRecordByIndex(490, iIndex)
end
function GameScheme:NpcConfig_nums()
return self.m_pDC:GetFileRecordNums(490)
end
function GameScheme:NpcConfig_0(NPCID)
local pKey={NPCID=NPCID}
return self.m_pDC:GetRecord(490, 0, pKey);
end
function GameScheme:TruckBotSift(iIndex)
return self.m_pDC:GetRecordByIndex(491, iIndex)
end
function GameScheme:TruckBotSift_nums()
return self.m_pDC:GetFileRecordNums(491)
end
function GameScheme:TruckBotSift_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(491, 0, pKey);
end
function GameScheme:TruckDepot(iIndex)
return self.m_pDC:GetRecordByIndex(492, iIndex)
end
function GameScheme:TruckDepot_nums()
return self.m_pDC:GetFileRecordNums(492)
end
function GameScheme:TruckDepot_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(492, 0, pKey);
end
function GameScheme:TruckRefresh(iIndex)
return self.m_pDC:GetRecordByIndex(493, iIndex)
end
function GameScheme:TruckRefresh_nums()
return self.m_pDC:GetFileRecordNums(493)
end
function GameScheme:TruckRefresh_0(CurrentRefreshRate)
local pKey={CurrentRefreshRate=CurrentRefreshRate}
return self.m_pDC:GetRecord(493, 0, pKey);
end
function GameScheme:TruckOtherPeople(iIndex)
return self.m_pDC:GetRecordByIndex(494, iIndex)
end
function GameScheme:TruckOtherPeople_nums()
return self.m_pDC:GetFileRecordNums(494)
end
function GameScheme:TruckOtherPeople_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(494, 0, pKey);
end
function GameScheme:TruckSummary(iIndex)
return self.m_pDC:GetRecordByIndex(495, iIndex)
end
function GameScheme:TruckSummary_nums()
return self.m_pDC:GetFileRecordNums(495)
end
function GameScheme:TruckSummary_0(quality)
local pKey={quality=quality}
return self.m_pDC:GetRecord(495, 0, pKey);
end
function GameScheme:TruckTrade(iIndex)
return self.m_pDC:GetRecordByIndex(496, iIndex)
end
function GameScheme:TruckTrade_nums()
return self.m_pDC:GetFileRecordNums(496)
end
function GameScheme:TruckTrade_0(quality)
local pKey={quality=quality}
return self.m_pDC:GetRecord(496, 0, pKey);
end
function GameScheme:BuildPreProcess(iIndex)
return self.m_pDC:GetRecordByIndex(497, iIndex)
end
function GameScheme:BuildPreProcess_nums()
return self.m_pDC:GetFileRecordNums(497)
end
function GameScheme:BuildPreProcess_0(EventID)
local pKey={EventID=EventID}
return self.m_pDC:GetRecord(497, 0, pKey);
end
function GameScheme:BuildPreProcess_1(PreEvent)
local pKey={PreEvent=PreEvent}
return self.m_pDC:GetRecord(497, 1, pKey);
end
function GameScheme:AutomaticMail(iIndex)
return self.m_pDC:GetRecordByIndex(498, iIndex)
end
function GameScheme:AutomaticMail_nums()
return self.m_pDC:GetFileRecordNums(498)
end
function GameScheme:AutomaticMail_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(498, 0, pKey);
end
function GameScheme:Sandmapcompetition(iIndex)
return self.m_pDC:GetRecordByIndex(499, iIndex)
end
function GameScheme:Sandmapcompetition_nums()
return self.m_pDC:GetFileRecordNums(499)
end
function GameScheme:Sandmapcompetition_0(ActId)
local pKey={ActId=ActId}
return self.m_pDC:GetRecord(499, 0, pKey);
end
function GameScheme:SecretTask(iIndex)
return self.m_pDC:GetRecordByIndex(500, iIndex)
end
function GameScheme:SecretTask_nums()
return self.m_pDC:GetFileRecordNums(500)
end
function GameScheme:SecretTask_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(500, 0, pKey);
end
function GameScheme:SecretStar(iIndex)
return self.m_pDC:GetRecordByIndex(501, iIndex)
end
function GameScheme:SecretStar_nums()
return self.m_pDC:GetFileRecordNums(501)
end
function GameScheme:SecretStar_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(501, 0, pKey);
end
function GameScheme:AllianceDuelChest(iIndex)
return self.m_pDC:GetRecordByIndex(502, iIndex)
end
function GameScheme:AllianceDuelChest_nums()
return self.m_pDC:GetFileRecordNums(502)
end
function GameScheme:AllianceDuelChest_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(502, 0, pKey);
end
function GameScheme:AllianceDuelRewards(iIndex)
return self.m_pDC:GetRecordByIndex(503, iIndex)
end
function GameScheme:AllianceDuelRewards_nums()
return self.m_pDC:GetFileRecordNums(503)
end
function GameScheme:AllianceDuelRewards_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(503, 0, pKey);
end
function GameScheme:AllianceDuelTheme(iIndex)
return self.m_pDC:GetRecordByIndex(504, iIndex)
end
function GameScheme:AllianceDuelTheme_nums()
return self.m_pDC:GetFileRecordNums(504)
end
function GameScheme:AllianceDuelTheme_0(ThemeId)
local pKey={ThemeId=ThemeId}
return self.m_pDC:GetRecord(504, 0, pKey);
end
function GameScheme:AllianceDuelScience(iIndex)
return self.m_pDC:GetRecordByIndex(505, iIndex)
end
function GameScheme:AllianceDuelScience_nums()
return self.m_pDC:GetFileRecordNums(505)
end
function GameScheme:AllianceDuelScience_0(ScienceID)
local pKey={ScienceID=ScienceID}
return self.m_pDC:GetRecord(505, 0, pKey);
end
function GameScheme:HonorWall(iIndex)
return self.m_pDC:GetRecordByIndex(506, iIndex)
end
function GameScheme:HonorWall_nums()
return self.m_pDC:GetFileRecordNums(506)
end
function GameScheme:HonorWall_0(HonorWallID,WallLevel)
local pKey={HonorWallID=HonorWallID,WallLevel=WallLevel}
return self.m_pDC:GetRecord(506, 0, pKey);
end
function GameScheme:CongressOfficialPosition(iIndex)
return self.m_pDC:GetRecordByIndex(507, iIndex)
end
function GameScheme:CongressOfficialPosition_nums()
return self.m_pDC:GetFileRecordNums(507)
end
function GameScheme:CongressOfficialPosition_0(PositionID)
local pKey={PositionID=PositionID}
return self.m_pDC:GetRecord(507, 0, pKey);
end
function GameScheme:CongressOfficialPosition_1(SeasonID,PositionType)
local pKey={SeasonID=SeasonID,PositionType=PositionType}
return self.m_pDC:GetRecord(507, 1, pKey);
end
function GameScheme:CongressAwards(iIndex)
return self.m_pDC:GetRecordByIndex(508, iIndex)
end
function GameScheme:CongressAwards_nums()
return self.m_pDC:GetFileRecordNums(508)
end
function GameScheme:CongressAwards_0(AwardsID)
local pKey={AwardsID=AwardsID}
return self.m_pDC:GetRecord(508, 0, pKey);
end
function GameScheme:CongressAwards_1(AwardsType,SeasonID)
local pKey={AwardsType=AwardsType,SeasonID=SeasonID}
return self.m_pDC:GetRecord(508, 1, pKey);
end
function GameScheme:Congressbadge(iIndex)
return self.m_pDC:GetRecordByIndex(509, iIndex)
end
function GameScheme:Congressbadge_nums()
return self.m_pDC:GetFileRecordNums(509)
end
function GameScheme:Congressbadge_0(BadgeID)
local pKey={BadgeID=BadgeID}
return self.m_pDC:GetRecord(509, 0, pKey);
end
function GameScheme:GuideContent(iIndex)
return self.m_pDC:GetRecordByIndex(510, iIndex)
end
function GameScheme:GuideContent_nums()
return self.m_pDC:GetFileRecordNums(510)
end
function GameScheme:GuideContent_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(510, 0, pKey);
end
function GameScheme:CommanderWeek(iIndex)
return self.m_pDC:GetRecordByIndex(511, iIndex)
end
function GameScheme:CommanderWeek_nums()
return self.m_pDC:GetFileRecordNums(511)
end
function GameScheme:CommanderWeek_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(511, 0, pKey);
end
function GameScheme:CommanderWeek_1(AtyID,PhaseID)
local pKey={AtyID=AtyID,PhaseID=PhaseID}
return self.m_pDC:GetRecord(511, 1, pKey);
end
function GameScheme:DailySpecialGift(iIndex)
return self.m_pDC:GetRecordByIndex(512, iIndex)
end
function GameScheme:DailySpecialGift_nums()
return self.m_pDC:GetFileRecordNums(512)
end
function GameScheme:DailySpecialGift_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(512, 0, pKey);
end
function GameScheme:MiniSkill(iIndex)
return self.m_pDC:GetRecordByIndex(513, iIndex)
end
function GameScheme:MiniSkill_nums()
return self.m_pDC:GetFileRecordNums(513)
end
function GameScheme:MiniSkill_0(SkillID)
local pKey={SkillID=SkillID}
return self.m_pDC:GetRecord(513, 0, pKey);
end
function GameScheme:MiniObstacle(iIndex)
return self.m_pDC:GetRecordByIndex(514, iIndex)
end
function GameScheme:MiniObstacle_nums()
return self.m_pDC:GetFileRecordNums(514)
end
function GameScheme:MiniObstacle_0(EventID)
local pKey={EventID=EventID}
return self.m_pDC:GetRecord(514, 0, pKey);
end
function GameScheme:MiniUnit(iIndex)
return self.m_pDC:GetRecordByIndex(515, iIndex)
end
function GameScheme:MiniUnit_nums()
return self.m_pDC:GetFileRecordNums(515)
end
function GameScheme:MiniUnit_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(515, 0, pKey);
end
function GameScheme:MiniUnit_1(ID,UnitLevel)
local pKey={ID=ID,UnitLevel=UnitLevel}
return self.m_pDC:GetRecord(515, 1, pKey);
end
function GameScheme:MiniLevel(iIndex)
return self.m_pDC:GetRecordByIndex(516, iIndex)
end
function GameScheme:MiniLevel_nums()
return self.m_pDC:GetFileRecordNums(516)
end
function GameScheme:MiniLevel_0(MiniLevelID)
local pKey={MiniLevelID=MiniLevelID}
return self.m_pDC:GetRecord(516, 0, pKey);
end
function GameScheme:GWMapBtn(iIndex)
return self.m_pDC:GetRecordByIndex(517, iIndex)
end
function GameScheme:GWMapBtn_nums()
return self.m_pDC:GetFileRecordNums(517)
end
function GameScheme:GWMapBtn_0(BtnID)
local pKey={BtnID=BtnID}
return self.m_pDC:GetRecord(517, 0, pKey);
end
function GameScheme:GiftPrivilege(iIndex)
return self.m_pDC:GetRecordByIndex(518, iIndex)
end
function GameScheme:GiftPrivilege_nums()
return self.m_pDC:GetFileRecordNums(518)
end
function GameScheme:GiftPrivilege_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(518, 0, pKey);
end
function GameScheme:CumulList(iIndex)
return self.m_pDC:GetRecordByIndex(519, iIndex)
end
function GameScheme:CumulList_nums()
return self.m_pDC:GetFileRecordNums(519)
end
function GameScheme:CumulList_0(Id)
local pKey={Id=Id}
return self.m_pDC:GetRecord(519, 0, pKey);
end
function GameScheme:MiniPassiveSkill(iIndex)
return self.m_pDC:GetRecordByIndex(520, iIndex)
end
function GameScheme:MiniPassiveSkill_nums()
return self.m_pDC:GetFileRecordNums(520)
end
function GameScheme:MiniPassiveSkill_0(SkillID)
local pKey={SkillID=SkillID}
return self.m_pDC:GetRecord(520, 0, pKey);
end
function GameScheme:TradeShip(iIndex)
return self.m_pDC:GetRecordByIndex(521, iIndex)
end
function GameScheme:TradeShip_nums()
return self.m_pDC:GetFileRecordNums(521)
end
function GameScheme:TradeShip_0(level)
local pKey={level=level}
return self.m_pDC:GetRecord(521, 0, pKey);
end
function GameScheme:TradeShip_1(Type,Season)
local pKey={Type=Type,Season=Season}
return self.m_pDC:GetRecord(521, 1, pKey);
end
function GameScheme:TradeShip_2(PackagesID)
local pKey={PackagesID=PackagesID}
return self.m_pDC:GetRecord(521, 2, pKey);
end
function GameScheme:AccessoryAdorn(iIndex)
return self.m_pDC:GetRecordByIndex(522, iIndex)
end
function GameScheme:AccessoryAdorn_nums()
return self.m_pDC:GetFileRecordNums(522)
end
function GameScheme:AccessoryAdorn_0(adornID)
local pKey={adornID=adornID}
return self.m_pDC:GetRecord(522, 0, pKey);
end
function GameScheme:TradeShipCargo(iIndex)
return self.m_pDC:GetRecordByIndex(523, iIndex)
end
function GameScheme:TradeShipCargo_nums()
return self.m_pDC:GetFileRecordNums(523)
end
function GameScheme:TradeShipCargo_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(523, 0, pKey);
end
function GameScheme:TradeShipCargo_1(RewardRule,CarriageNumber,CarriageQuality)
local pKey={RewardRule=RewardRule,CarriageNumber=CarriageNumber,CarriageQuality=CarriageQuality}
return self.m_pDC:GetRecord(523, 1, pKey);
end
function GameScheme:TradeShipRefresh(iIndex)
return self.m_pDC:GetRecordByIndex(524, iIndex)
end
function GameScheme:TradeShipRefresh_nums()
return self.m_pDC:GetFileRecordNums(524)
end
function GameScheme:TradeShipRefresh_0(CurrentRefreshRate)
local pKey={CurrentRefreshRate=CurrentRefreshRate}
return self.m_pDC:GetRecord(524, 0, pKey);
end
function GameScheme:MiniGameMapping(iIndex)
return self.m_pDC:GetRecordByIndex(525, iIndex)
end
function GameScheme:MiniGameMapping_nums()
return self.m_pDC:GetFileRecordNums(525)
end
function GameScheme:MiniGameMapping_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(525, 0, pKey);
end
function GameScheme:MiniGameMapping_1(GameType)
local pKey={GameType=GameType}
return self.m_pDC:GetRecord(525, 1, pKey);
end
function GameScheme:HeroResourceIncrease(iIndex)
return self.m_pDC:GetRecordByIndex(526, iIndex)
end
function GameScheme:HeroResourceIncrease_nums()
return self.m_pDC:GetFileRecordNums(526)
end
function GameScheme:HeroResourceIncrease_0(heroSkillID,star)
local pKey={heroSkillID=heroSkillID,star=star}
return self.m_pDC:GetRecord(526, 0, pKey);
end
function GameScheme:MiracleBox(iIndex)
return self.m_pDC:GetRecordByIndex(527, iIndex)
end
function GameScheme:MiracleBox_nums()
return self.m_pDC:GetFileRecordNums(527)
end
function GameScheme:MiracleBox_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(527, 0, pKey);
end
function GameScheme:MiracleBox_1(RandomReward)
local pKey={RandomReward=RandomReward}
return self.m_pDC:GetRecord(527, 1, pKey);
end
function GameScheme:GoldZombie(iIndex)
return self.m_pDC:GetRecordByIndex(528, iIndex)
end
function GameScheme:GoldZombie_nums()
return self.m_pDC:GetFileRecordNums(528)
end
function GameScheme:GoldZombie_0(TypeID)
local pKey={TypeID=TypeID}
return self.m_pDC:GetRecord(528, 0, pKey);
end
function GameScheme:CampBuff(iIndex)
return self.m_pDC:GetRecordByIndex(529, iIndex)
end
function GameScheme:CampBuff_nums()
return self.m_pDC:GetFileRecordNums(529)
end
function GameScheme:CampBuff_0(AtyID)
local pKey={AtyID=AtyID}
return self.m_pDC:GetRecord(529, 0, pKey);
end
function GameScheme:SandMapSpecialAreas(iIndex)
return self.m_pDC:GetRecordByIndex(530, iIndex)
end
function GameScheme:SandMapSpecialAreas_nums()
return self.m_pDC:GetFileRecordNums(530)
end
function GameScheme:SandMapSpecialAreas_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(530, 0, pKey);
end
function GameScheme:leaguelanguage(iIndex)
return self.m_pDC:GetRecordByIndex(531, iIndex)
end
function GameScheme:leaguelanguage_nums()
return self.m_pDC:GetFileRecordNums(531)
end
function GameScheme:leaguelanguage_0(id)
local pKey={id=id}
return self.m_pDC:GetRecord(531, 0, pKey);
end
function GameScheme:DesertStormBuilding(iIndex)
return self.m_pDC:GetRecordByIndex(532, iIndex)
end
function GameScheme:DesertStormBuilding_nums()
return self.m_pDC:GetFileRecordNums(532)
end
function GameScheme:DesertStormBuilding_0(Building)
local pKey={Building=Building}
return self.m_pDC:GetRecord(532, 0, pKey);
end
function GameScheme:DesertStormReward(iIndex)
return self.m_pDC:GetRecordByIndex(533, iIndex)
end
function GameScheme:DesertStormReward_nums()
return self.m_pDC:GetFileRecordNums(533)
end
function GameScheme:DesertStormReward_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(533, 0, pKey);
end
function GameScheme:SecretTreasure(iIndex)
return self.m_pDC:GetRecordByIndex(534, iIndex)
end
function GameScheme:SecretTreasure_nums()
return self.m_pDC:GetFileRecordNums(534)
end
function GameScheme:SecretTreasure_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(534, 0, pKey);
end
function GameScheme:DesertStormConfig(iIndex)
return self.m_pDC:GetRecordByIndex(535, iIndex)
end
function GameScheme:DesertStormConfig_nums()
return self.m_pDC:GetFileRecordNums(535)
end
function GameScheme:DesertStormConfig_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(535, 0, pKey);
end
function GameScheme:MonstersApproaching(iIndex)
return self.m_pDC:GetRecordByIndex(536, iIndex)
end
function GameScheme:MonstersApproaching_nums()
return self.m_pDC:GetFileRecordNums(536)
end
function GameScheme:MonstersApproaching_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(536, 0, pKey);
end
function GameScheme:SrverShowdownCompetition(iIndex)
return self.m_pDC:GetRecordByIndex(537, iIndex)
end
function GameScheme:SrverShowdownCompetition_nums()
return self.m_pDC:GetFileRecordNums(537)
end
function GameScheme:SrverShowdownCompetition_0(ID)
local pKey={ID=ID}
return self.m_pDC:GetRecord(537, 0, pKey);
end
function GameScheme:TodoSchedule(iIndex)
return self.m_pDC:GetRecordByIndex(538, iIndex)
end
function GameScheme:TodoSchedule_nums()
return self.m_pDC:GetFileRecordNums(538)
end
function GameScheme:TodoSchedule_0(AtyID)
local pKey={AtyID=AtyID}
return self.m_pDC:GetRecord(538, 0, pKey);
end
function GameScheme:GearSupply(iIndex)
return self.m_pDC:GetRecordByIndex(539, iIndex)
end
function GameScheme:GearSupply_nums()
return self.m_pDC:GetFileRecordNums(539)
end
function GameScheme:GearSupply_0(AtyID,Level)
local pKey={AtyID=AtyID,Level=Level}
return self.m_pDC:GetRecord(539, 0, pKey);
end
function GameScheme:BuildingTypeMiniGame(iIndex)
return self.m_pDC:GetRecordByIndex(540, iIndex)
end
function GameScheme:BuildingTypeMiniGame_nums()
return self.m_pDC:GetFileRecordNums(540)
end
function GameScheme:BuildingTypeMiniGame_0(TypeID)
local pKey={TypeID=TypeID}
return self.m_pDC:GetRecord(540, 0, pKey);
end
function GameScheme:BuildMaincityMapMiniGame(iIndex)
return self.m_pDC:GetRecordByIndex(541, iIndex)
end
function GameScheme:BuildMaincityMapMiniGame_nums()
return self.m_pDC:GetFileRecordNums(541)
end
function GameScheme:BuildMaincityMapMiniGame_0(MapID)
local pKey={MapID=MapID}
return self.m_pDC:GetRecord(541, 0, pKey);
end
return GameScheme