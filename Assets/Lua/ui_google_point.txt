--@region FileHead
-- ui_google_point.txt ---------------------------------
-- author:  曾琳琳
-- date:    2023/11/21 17:25:11
-- ver:     1.0
-- desc:    谷歌商店积分弹窗
-------------------------------------------------
--@endregion 

--@region Require
local require   = require
local pairs     = pairs
local ipairs    = ipairs
local type    = type
local typeof    = typeof
local string    = string
local table     = table
local tonumber  = tonumber

local Button        = CS.UnityEngine.UI.Button
local Application   = CS.UnityEngine.Application
local CreateInstance = CS.System.Array.CreateInstance
local String = CS.System.String

local event					= require "event"
local class                 = require "class"
local ui_base               = require "ui_base"
local module_scroll_list    = require "scroll_list"
local net_route             = require "net_route"
local log                   = require "log"
local windowmgr             = require "ui_window_mgr"
local q1sdk                 = require "q1sdk"
local reward_mgr            = require "reward_mgr"
local goods_item            = require "goods_item_new"
local iui_item_detail       = require "iui_item_detail"
local item_data             = require "item_data"
--@endregion 

--@region ModuleDeclare
module("ui_google_point")
--local interface = require "iui_google_point"
local window = nil
local UIGooglePoint = {}
local curRewardItems= {}
local isTest = true
--@endregion 

--@region WidgetTable
UIGooglePoint.widget_table = {
	RewardRootRect = {path = "Frame/RewardRoot/Viewport/Content" , type = "RectTransform"},	--奖励显示
    getBtn = {path = "Frame/BtnGroup/BtnGet", type = "Button",event_name = "OnBtnGet"},--领取按钮
    exchangeBtn = {path = "Frame/BtnGroup/BtnExchange", type = "Button",event_name = "OnBtnExchange" },--更换账号按钮
	closeBtn = {path = "Mask", type = Button,event_name = "OnBtnClose" },
--@region User
--@endregion 
}
--@endregion 

--@region WindowCtor
function UIGooglePoint:ctor(selfType)
	self.__base:ctor(selfType)
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowInit
--[[窗口初始化]]
function UIGooglePoint:Init()
    local game_scheme 	= require "game_scheme"
	local priceCfg = game_scheme:InitBattleProp_0(4200)
    self.prices = {}
    if priceCfg and priceCfg.szParam and priceCfg.szParam.data then
        local count = priceCfg.szParam.count
        local data = priceCfg.szParam.data
        for i=0,count - 1 do
            if tonumber(data[i])~=0 then
                table.insert(self.prices,tonumber(data[i])/100)
            end
        end
    end
    local rewardIDCfg = game_scheme:InitBattleProp_0(4201)
    self.RewardDatas = {}
    local Idx = 1
    if rewardIDCfg and rewardIDCfg.szParam and rewardIDCfg.szParam.data then
        local count = rewardIDCfg.szParam.count
        local data = rewardIDCfg.szParam.data
        for i=0,count - 1 do
            if data[i]==0 then
                Idx = Idx + 1
            end
            if not self.RewardDatas[Idx] then
                self.RewardDatas[Idx] = {}
            end
            if tonumber(data[i])~=0 then
                table.insert(self.RewardDatas[Idx],tonumber(data[i]))
            end
        end
    end

    self:SubscribeEvents()
    net_route.RegisterMsgHandlers(MessageTable)
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function UIGooglePoint:OnShow()
    self:UpdateUIPage()
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowOnHide
--[[界面隐藏时调用]]
function UIGooglePoint:OnHide()
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowSetInputParam
--[[设置窗口的输入参数。该参数通常是由其它模块或者外部设置进来。需要注意的是，
当调用这个函数的时候，窗口资源可能还是没有加载完成的。
@param p 参数表
]]
function UIGooglePoint:SetInputParam(p)
	self.inputParam = p

    --如果正在显示，则更新一次窗口
    if self.UIRoot and self.UIRoot.activeSelf == true then
        self:UpdateUIPage()
    end
	
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowBuildUpdateData
--[[构建UI更新数据]]
function UIGooglePoint:BuildUpdateData()
    self.productIDs = q1sdk.GetProductIds()
    --com.xgame.asia_1.99_point
    --com.xgame.asia_4.99_point
    --谷歌后台配置了这两个商品ID，然后平台那边发送这两个商品ID到游戏这边
    if isTest and Application.isEditor then--Unity测试数据
        local t = {"com.xgame.asia_4.99_point"}
        local array=CreateInstance(typeof(String),#t)
        for k = 0, array.Length - 1 do
            array[k] = t[k+1]
        end
        self.productIDs = array
    end
    if self.productIDs then
        local rewardlist = {}
        local s = ""
        for i=0,self.productIDs.Length-1 do
            local v = self.productIDs[i]
            if v then
                for j=1,#self.prices do
                    s = string.format("com.xgame.asia_%s_point",self.prices[j])
                    if s==v then
                        if self.RewardDatas[j] then
                            for _j,k in ipairs(self.RewardDatas[j]) do
                                table.insert(rewardlist,k)
                            end
                        end
                    end
                end
            end
        end
        return rewardlist
    end
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowUpdateUI
--[[资源加载完成，被显示的时候调用]]
function UIGooglePoint:UpdateUIPage()
	local datas = self:BuildUpdateData()
    if datas then
        for i, reward in pairs(datas) do
            local rewardinfo = reward_mgr.GetRewardGoods(reward)
            local goodsEntity = curRewardItems[i] 
            if rewardinfo then
                if not goodsEntity then
                    goodsEntity = goods_item.CGoodsItem():Init(self.RewardRootRect,function (p)
                        if not p then return end
                        p:DisplayInfo()
                    end,0.6)
                    goodsEntity:SetGoods(nil, rewardinfo.id, rewardinfo.num, function()
                        iui_item_detail.Show(rewardinfo.id, nil, item_data.Item_Show_Type_Enum.Reward_Interface, nil)
                    end)
                    goodsEntity:SetCountEnable(true)
                    table.insert(curRewardItems,goodsEntity)
                else
                end
            else
                log.Error("不存在此奖励物品,rewardId="..reward)
            end
        end
    end
    --初始化奖励
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowClose
function UIGooglePoint:Close()
    net_route.UnregisterMsgHandlers(MessageTable)

    if self:IsValid() then
		self:UnsubscribeEvents()
            
        for i, v in pairs(curRewardItems) do
            if v then
                v:Dispose()
                v=nil
            end
        end
        curRewardItems= {}
	end
	self.__base:Close()
    window = nil
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowSubscribeEvents
function UIGooglePoint:SubscribeEvents()
    self.OnBtnGet = function()
        --请求领取奖励
        local player_mgr = require "player_mgr"
        local setting_server_data = require "setting_server_data"
        local serverID = setting_server_data.GetLoginWorldID() or ""
        local roleID = player_mgr.GetPlayerRoleID()
        if roleID and serverID then
            q1sdk.RequestGooglePoints(roleID,serverID)
            local google_point_data = require "google_point_data"
            google_point_data.ClearData()
        end
        windowmgr:UnloadModule("ui_google_point")
    end
    
    self.OnBtnExchange = function()
        --打开设置界面
        windowmgr:ShowModule("ui_setting_base")
    end

    self.OnBtnClose = function()
        windowmgr:UnloadModule("ui_google_point")
    end
end --///<<< function
--@endregion 

function UIGooglePoint:GotoUILoginMain()
	event.Trigger(event.RETURN_LOGIN)
	windowmgr:UnloadModule("ui_google_point")
	local login_module = require "net_login_module"
	login_module.ReturnLogin()
end

--@region WindowUnsubscribeEvents
--[[退订UI事件]]
function UIGooglePoint:UnsubscribeEvents()
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowBtnFunctions
--@endregion 

--@region ScrollItem
--@endregion 

--@region WindowInherited
local CUIGooglePoint = class(ui_base, nil, UIGooglePoint)
--@endregion 

--@region ModuleFunction
function Show()
    if window == nil then
        window = CUIGooglePoint()
        window._NAME = _NAME
        window:LoadUIResource("ui/prefabs/uigooglepoint.prefab", nil, nil, nil)
    end
    window:Show()
    return window
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end

function Close()
    if window ~= nil then
        Hide()
        window:Close()
        window = nil
    end
end

--@endregion 

--@region RegisterMsg
MessageTable =
{ --///<<< tableStart
} --///<<< tableEnd
--@endregion 
