-- ui_legend_championships_apply.txt ------------------------------------------
-- author:  赖嘉明
-- date:    2021.04.12
-- ver:     1.0
-- desc:    传奇锦标赛报名
--------------------------------------------------------------
local os = os
local pairs = pairs
local table = table
local print = print
local ipairs = ipairs
local string = string
local require = require
local dump = dump

local util = require "util"
local lang = require "lang"
local event = require "event"
local class = require "class"
local ui_base = require "ui_base"
local game_scheme = require "game_scheme"
local ui_window_mgr = require "ui_window_mgr"
local card_sprite_asset = require "card_sprite_asset"
local legend_championships_mgr = require "legend_championships_mgr"

local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable
local TextMeshProUGUI         = CS.TMPro.TextMeshProUGUI

module("ui_legend_championships_apply")
local window = nil
local parent = nil
local M = {}
M.widget_table = 
{	
	list = {path = "bg/List/Viewport/Content", type = ScrollRectTable},
	scrollRect = {path = "bg/List", type = "ScrollRect"},
	helpBtn = {path = "bg/helpBtn", type = "Button"},
	countDown = {path = "bg/countDown", type = TextMeshProUGUI},
}

function M:Init()
	self:SubscribeEvents()
	self.spriteAsset = card_sprite_asset.CreateArenaAsset()
end

function M:OnShow()
	self.__base:OnShow()
	
    self:InitUI()
    self:RefreshUI()
    self:InitList()
	self:UpdateList()
end

local descOutlineColor = 
{
	[1] = {r=33/255,g=94/255,b=137/255,a=1},--白银
	[2] = {r=108/255,g=73/255,b=151/255,a=1},--黄金
	[3] = {r=134/255,g=95/255,b=45/255,a=1},--钻石
	[4] = {r=171/255,g=59/255,b=70/255,a=1},--王者
	[5] = {r=123/255,g=79/255,b=183/255,a=1},--传奇
	[99] = {r=30/255,g=88/255,b=99/255,a=1},--实验
}

function onItemRender(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem

	local iconSS = scroll_rect_item:Get("iconSS")
	local icon = scroll_rect_item:Get("icon")
	local name = scroll_rect_item:Get("name")
	local desc = scroll_rect_item:Get("desc")
	local lockBg = scroll_rect_item:Get("lockBg")
	local lockDesc = scroll_rect_item:Get("lockDesc")
	local applyBtn = scroll_rect_item:Get("applyBtn")
	local applyBtnText = scroll_rect_item:Get("applyBtnText")
	local rewardBtn = scroll_rect_item:Get("rewardBtn")
	local rewardIcon = scroll_rect_item:Get("rewardIcon")
	local iconRoot1 = scroll_rect_item:Get("iconRoot1")
	local iconRoot2 = scroll_rect_item:Get("iconRoot2")
	local iconRoot = {iconRoot1, iconRoot2}
	local bg = scroll_rect_item:Get("bg")
	local lock = scroll_rect_item:Get("lock")
	local applied = scroll_rect_item:Get("applied")
	local nameOutline = scroll_rect_item:Get("nameOutline")
	local descOutline = scroll_rect_item:Get("descOutline")
	local lockDescOutline = scroll_rect_item:Get("lockDescOutline")
	local nameGradient = scroll_rect_item:Get("nameGradient")
	local ongoing = scroll_rect_item:Get("ongoing")
	local rewardIconSS = scroll_rect_item:Get("rewardIconSS")

	name.text = dataItem.name
	--nameGradient.BottomColor = legend_championships_mgr.nameColor[dataItem.type]

	local state = legend_championships_mgr.GetState()

	applyBtn:SetActive(dataItem.applied == nil or dataItem.applied == 0 and (state == 1 or dataItem.experience == true))
	applied:SetActive(dataItem.applied and (dataItem.applied == dataItem.type) or false)

	if dataItem.experience then
		bg:Switch(0)
		iconSS:Switch(0)
		lockBg:Switch(0)
		rewardIconSS:Switch()
	else
		bg:Switch(dataItem.type)
		iconSS:Switch(dataItem.type)
		lockBg:Switch(dataItem.type)
		rewardIconSS:Switch(dataItem.type)
	end
	rewardIcon:SetNativeSize()
	icon:SetNativeSize()


	if not dataItem.unlockState then
		--lockBg:SetActive(true)
		lock:SetActive(true)
		applyBtnText:SetActive(false)
		desc.text = ""
		lockDesc.text = string.format(lang.Get(8014), dataItem.unlockMapDesc) 
		ongoing:SetActive(false)
	else
		lockBg:SetActive(false)
		lock:SetActive(false)
		applyBtnText:SetActive(true)
		lockDesc.text = ""
		desc.text = string.format((lang.Get(8012).."\n"..lang.Get(8013)), dataItem.team, dataItem.heroNum, dataItem.heroStarDesc)
		ongoing:SetActive((dataItem.applied == nil or dataItem.applied == 0 or dataItem.applied == 99) and state == 2 and not dataItem.experience)
	end
	name.EnableOutLine = true
	desc.EnableOutLine = true
	lockDesc.EnableOutLine = true
	name.curOutlineColor = legend_championships_mgr.nameOutlineColor[dataItem.type]
	desc.curOutlineColor = descOutlineColor[dataItem.type]
	lockDesc.curOutlineColor = descOutlineColor[dataItem.type]
	name.curOutlineWidth = 0.8
	desc.curOutlineWidth = 0.8
	lockDesc.curOutlineWidth = 0.8
	 --print("index",index,"dataItem.type",dataItem.type,"descOutlineColor[dataItem.type].r",descOutlineColor[dataItem.type].r)
	dump(lockDesc.curOutlineColor)

	if window and window:IsValid() then
		if window.spriteAsset then
			window.spriteAsset:GetSprite(dataItem.iconId, function(sp)
				if window and window:IsValid() then
					icon.sprite = sp
					icon.enabled = true
					icon:SetNativeSize()
				end
			end)
		end
		if dataItem.lastGroup then
			window.guideObj = applyBtn.transform
		end
	end

	iconRoot1:SetActive(dataItem.exhibitRewardData[1] ~= nil)
	iconRoot2:SetActive(dataItem.exhibitRewardData[2] ~= nil)

	scroll_rect_item.data.iconUI = scroll_rect_item.data.iconUI or {}
	for k,v in ipairs(dataItem.exhibitRewardData) do
        local goods_item = require "goods_item_new"
		local iconUI = scroll_rect_item.data.iconUI[k] or goods_item.CGoodsItem():Init(iconRoot[k], function(p)
            if not p then return end
            p:DisplayInfo()
		end, 0.65)
		scroll_rect_item.data.iconUI[k] = iconUI

		iconUI:SetGoods(nil, v.id, v.num, function()
            local iui_item_detail = require "iui_item_detail"
            local item_data = require "item_data"
            iui_item_detail.Show(v.id, nil, item_data.Item_Show_Type_Enum.Reward_Interface, nil, nil ,nil, v.rewardID)
        end)
	end

    local func = {}
    local rewardBtnEvent = function()
		local win = ui_window_mgr:ShowModule("ui_legend_championships_group_reward")
    end
    func["rewardBtnEvent"] = rewardBtnEvent

	local applyBtnEvent = function()
		local ui_pop_mgr = require "ui_pop_mgr"
		if ui_pop_mgr.CheckIsUnlock(0, dataItem.mapID) then
			local unforced_guide_mgr = require "unforced_guide_mgr"
			if unforced_guide_mgr.IsGuiding(56, 431) then
				event.Trigger(event.CLICK_LEGEND_GROUP)
				window.scrollRect.enabled = true
			end

			local win = ui_window_mgr:ShowModule("ui_legend_championships_apply_detail")
			win:SetInputParam(dataItem.type)
			legend_championships_mgr.SetCacheApplyType(dataItem.type)
		end
	end
	func["applyBtnEvent"] = applyBtnEvent

	scroll_rect_item.InvokeFunc = function(funcname)
		func[funcname]()
    end
end

function M:UpdateList()
	if self.list == nil then
		return
	end

	local data = self:BuildListData()
	local num = #data

	if not self.toBottom then
		self.toBottom = true
		self.list.onItemRenderComplete = function()
			self.list:ScrollTo(num-5)
			self.list.onItemRenderComplete = nil
			if self.guideObj then
				local unforced_guide_mgr = require "unforced_guide_mgr"
				if unforced_guide_mgr.IsGuiding(56, 431) then
					window.scrollRect.enabled = false
					event.Trigger(event.ENTER_LEGEND, nil, nil, self.guideObj)
				end
			end
		end
	end

	self.list.data = data
	self.list:Refresh(0, -1)
end

function M:BuildListData()
	local applyType = legend_championships_mgr.GetApplyType()
	local listData = {}
	local typeList = {5,4,3,2,1,99}
	local num = 0
	for _, i in ipairs(typeList) do
		local show = true
		if i == 99 then
			--体验组特殊规则: 
			local state = legend_championships_mgr.GetState()
			if state == 1 then
				--报名阶段或者以前报过名，不显示
				show = false
			elseif state == 2 and legend_championships_mgr.GetHistoryApply() and applyType ~= 99 then
				--比赛阶段报的不是体验组，不显示
				show = false
			end
		end
		if show then
			local cfg = legend_championships_mgr.GetLegendConfig(i)
			if cfg then
				local data = {}
				data.type = i
				if i == 99 then
					--体验组当白银组处理
					data.experience = true
				end
				data.name = lang.Get(cfg.iNameID)
				data.mapID = cfg.iUnlockStage
				local ui_pop_mgr = require "ui_pop_mgr"
				local unlockState = ui_pop_mgr.CheckIsUnlock(0, cfg.iUnlockStage, nil, false)
				data.unlockState = unlockState
				if not unlockState then
					local cfg_map = game_scheme:HookLevel_0(cfg.iUnlockStage)
					data.unlockMapDesc = cfg_map.Name
				end
				data.team = cfg.iTeams
				data.heroNum = cfg.iHeroesNum
				local hero_mgr = require "hero_mgr"
				data.heroStarDesc = "<color=#"..hero_mgr.Hero_Star_Color[cfg.iHeroStar]..">"..lang.Get(hero_mgr.Hero_Star_Desc[cfg.iHeroStar]).."</color>"

				data.exhibitRewardData = {}
				local reward_mgr = require "reward_mgr"
				local rewardID1 = cfg.iExhibitReward.data[0]
				if rewardID1 then
					data.exhibitRewardData[1] = reward_mgr.GetRewardGoods(rewardID1)
				end
				local rewardID2 = cfg.iExhibitReward.data[1]
				if rewardID2 then
					data.exhibitRewardData[2] = reward_mgr.GetRewardGoods(rewardID2)
				end
				local cfg_item = game_scheme:Item_0(cfg.iCurrencyID)
				if cfg_item then
					data.itemIconID = cfg_item.icon
				end
				data.applied = applyType
				data.iconId = cfg.iconId
				num = num + 1
				table.insert(listData, data)
			end
		end
	end
	if num > 0 then
		listData[num].lastGroup = true
	end
	return listData 
end

function M:InitList()
	self.list.onItemRender = onItemRender
	self.list.onItemDispose = function(scroll_rect_item, index)
		if scroll_rect_item and scroll_rect_item.data and scroll_rect_item.data.iconUI ~= nil then
			for k, v in pairs(scroll_rect_item.data.iconUI) do
				scroll_rect_item.data.iconUI[k]:Dispose()
				scroll_rect_item.data.iconUI[k] = nil
			end
			scroll_rect_item.data.iconUI = nil
		end
	end
end

function M:InitUI()
	
end

function M:RefreshUI()
	local state = legend_championships_mgr.GetState()
	 --print("state==", state)
	if state == 1 then
		self.timeTicker = self.timeTicker or util.IntervalCall(1, function()
			if self:IsValid() then
				local sec = legend_championships_mgr.GetApplyEndTime() - os.server_time()
				--  --print("GetApplyEndTime==", legend_championships_mgr.GetApplyEndTime(), "serverTime==", os.server_time(), "countdown==", sec)
				if self.countDown and not util.IsObjNull(self.countDown) then
					if sec > 0 then	
						local mall_cfg = require "mall_cfg"
						self.countDown.text = lang.Get(8002).."<color=#FFDD22>"..mall_cfg.GetTimeStr(sec).."</color>"
					else
						self.countDown.text = ""
						return true
					end
				end
			end
		end)
	else
		self.countDown.text = ""
	end
end

function M:SubscribeEvents()
	self.helpBtnEvent = function()
        local ui_help = require "ui_help"
        ui_help.ShowWithDate(132)
	end
	self.widget_table["helpBtn"].event_name = "helpBtnEvent"

	self.OnLEGEND_CHAMPIONSHIPS_SEASON_DATA_UPDATE = function()
		self:RefreshUI()
		self:UpdateList()
	end
	self:RegisterEvent(event.LEGEND_CHAMPIONSHIPS_SEASON_DATA_UPDATE, self.OnLEGEND_CHAMPIONSHIPS_SEASON_DATA_UPDATE)
end

function M:UnsubscribeEvents()

end

function M:Close()
	if self:IsValid() then
		self:UnsubscribeEvents()

		local unforced_guide_mgr = require "unforced_guide_mgr"
		if unforced_guide_mgr.IsGuiding(56, 431) then	
			ui_window_mgr:UnloadModule("ui_pointing_target")
			ui_window_mgr:UnloadModule("ui_guide_assistant_new")
		end
	end
	if self.list then
		self.list:ItemsDispose()
	end
	if self.spriteAsset then
		self.spriteAsset:Dispose()
		self.spriteAsset = nil
	end
	if self.timeTicker then
		util.RemoveDelayCall(self.timeTicker)
		self.timeTicker = nil
	end
	parent = nil
	self.__base:Close()
	window = nil
end

local CM = class(ui_base, nil, M)

function Show()
	if window == nil then
		window = CM()
		window._NAME = _NAME;window:LoadUIResource("ui/prefabs/uilegendchampionshipsapply.prefab", nil, parent, nil, nil, true)
	else
		window:Show()
	end
	return window
end

function Hide()
	if window ~= nil then
		window:Hide()
	end
end

function Close()
	if window ~= nil then
		window:Close()
		window = nil
	end
end

function OnSceneDestroy()
	Close()
end
event.Register(event.SCENE_DESTROY, OnSceneDestroy)

function SetParent(p)
	parent = p
end