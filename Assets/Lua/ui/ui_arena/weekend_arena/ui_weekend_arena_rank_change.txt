-- ui_weekend_arena_rank_change.txt ------------------------------------------
-- author:  赖嘉明
-- date:    2021.07.13
-- ver:     1.0
-- desc:    巅峰竞技场排名变化弹窗
--------------------------------------------------------------
local pairs = pairs
local ipairs = ipairs
local require = require
local tonumber = tonumber

local util = require "util"
local lang = require "lang"
local event = require "event"
local class = require "class"
local ui_base = require "ui_base"
local ui_window_mgr = require "ui_window_mgr"

local SpriteSwitcher = CS.War.UI.SpriteSwitcher
local Gradient = CS.War.UI.Gradient

module("ui_weekend_arena_rank_change")
local window = nil
local lastRank = 0
local nowRank = 0
local callback = nil        --关闭界面的回调
local M = {}
M.widget_table =
{
    closeBtn = {path = "closeBtn", type = "Button", backEvent = true},
    bg = {path = "bg", type = "RectTransform"},
    title = {path = "bg/title", type = "Text"},
    titleOutline = {path = "bg/title", type = "Outline"},
    titleColor = {path = "bg/title", type = Gradient},
    icon = {path = "bg/icon", type = SpriteSwitcher},
    iconImage = {path = "bg/icon", type = "Image"},
    desc = {path = "bg/desc", type = "Text"},

    lastRankText = {path = "bg/rank/lastRank", type = "Text"},
    nowRankText = {path = "bg/rank/nowRank", type = "Text"},
    rankUp = {path = "bg/rank/rankUp", type = "RectTransform"},
    rankDown = {path = "bg/rank/rankDown", type = "RectTransform"},
    lastRewardRoot = {path = "bg/reward_last/lastReward", type = "RectTransform"},
    nowRewardRoot = {path = "bg/reward_now/nowReward", type = "RectTransform"},
}

function SetInputParam(last, now)
    lastRank = last
    nowRank = now
end

function SetCloseFun(closeFun)
    callback = closeFun
end

function M:Init()
    self:SubscribeEvents()
end

function M:OnShow()
    self.__base:OnShow()

    self:InitUI()
    self:RefreshUI()
end

function M:InitUI()
    local rewardData = nil
    local reward_mgr = require "reward_mgr"
    local cfg_util = require "cfg_util"
    rewardData = {}
    local game_scheme = require "game_scheme"
    local count = game_scheme:WeekendArena_nums()
    for i = 0, count - 1 do
        local cfg = game_scheme:WeekendArena(i)
        if cfg then
            local last,rank = cfg.arenaRanking.data[0], cfg.arenaRanking.data[1]
            local rewardIDGroup = cfg_util.ArrayToLuaArray(cfg.rewardID)
            if lastRank == rank or lastRank == last or  (lastRank > last and lastRank < rank) then
                rewardData[1] = reward_mgr.GetRewardGoodsList2(rewardIDGroup)
            end
            if nowRank == rank or nowRank == last or  (nowRank > last and nowRank < rank) then
                rewardData[2] = reward_mgr.GetRewardGoodsList2(rewardIDGroup)
            end
        end
    end

    if rewardData then
        self.lastRewardItem = self.lastRewardItem or {}
        self.nowRewardItem = self.nowRewardItem or {}
        for k, v in ipairs(rewardData[1] or {}) do
            local iconUI = self.lastRewardItem[k] or reward_mgr.GetRewardItemData(v, self.lastRewardRoot.transform, true, 0.55)
            self.lastRewardItem[k] = iconUI
        end
        for k, v in ipairs(rewardData[2] or {}) do
            local iconUI = self.nowRewardItem[k] or reward_mgr.GetRewardItemData(v, self.nowRewardRoot.transform, true, 0.55)
            self.nowRewardItem[k] = iconUI
        end
    end

    self.lastRankText.text = lastRank
    self.nowRankText.text = nowRank

    self.desc.text = lastRank < nowRank and lang.Get(8090) or lang.Get(8089)
    local shrinkTxt = require "shrinkTxt"
    shrinkTxt.auto_wrap_truncate(self.desc)
    self.rankDown:SetActive(nowRank > lastRank)
    self.rankUp:SetActive(nowRank < lastRank)
end

function M:RefreshUI()

end

function M:SubscribeEvents()
    self.closeBtnEvent = function()
        ui_window_mgr:UnloadModule("ui_weekend_arena_rank_change")
    end
    self.widget_table["closeBtn"].event_name = "closeBtnEvent"
end

function M:UnsubscribeEvents()

end

function M:Close()
    if self:IsValid() then
        self:UnsubscribeEvents()
    end
    if self.lastRewardItem then
        for k, v in pairs(self.lastRewardItem) do
            self.lastRewardItem[k]:Dispose()
            self.lastRewardItem[k] = nil
        end
        self.lastRewardItem = nil
    end
    if self.nowRewardItem then
        for k, v in pairs(self.nowRewardItem) do
            self.nowRewardItem[k]:Dispose()
            self.nowRewardItem[k] = nil
        end
        self.nowRewardItem = nil
    end
    lastRank = 0
    nowRank = 0
    if callback then
        callback()
        callback = nil
    end
    self.__base:Close()
    window = nil
end

local CM = class(ui_base, nil, M)

function Show()
    if window == nil then
        window = CM()
        window._NAME = _NAME;window:LoadUIResource("ui/prefabs/uiweekendarenarankchange.prefab", nil, nil, nil, true, true)
    else
        window:Show()
    end
    return window
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end

function Close()
    if window ~= nil then
        window:Close()
        window = nil
    end
end

function OnSceneDestroy()
    Close()
end
event.Register(event.SCENE_DESTROY, OnSceneDestroy)
