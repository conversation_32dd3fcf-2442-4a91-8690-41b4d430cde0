local require = require
local pairs = pairs
local ipairs = ipairs
local print = print
local tonumber = tonumber
local table = table
local event = require "event"
local player_mgr = require "player_mgr"
local prop_pb = require "prop_pb"
local lang = require "lang"
local game_scheme = require "game_scheme"
local sprite_asset = require "sprite_asset"
local log = require "log"
local type = type
local string = string

module("actor_face_data")

local saveEquipFaceID = 0
local savePropFaceID = 0
local saveEquipFrameID = nil
local saveActiveFaceID = {}
local tempEquipFaceID = 0
local tempPropFaceID = 0
local bModifyName = true
local gradePath = "ui/actor/grade.asset"
local saveTitleData = {} -- 自定义称号数据

--头像框属性
local playerFrameAttrID = 0
--玩家称号信息
local playerTitleID = 0
--玩家称号属性信息
local playerTitleAttrID = 0

function CreateAttrGradeAsset()
    return sprite_asset.CSpriteAsset(gradePath)
end

--1战士2法师3游侠4刺客5牧师
local progessName = {
    [1] = 4101,
    [2] = 4102,
    [3] = 4103,
    [4] = 4104,
    [5] = 4105
}

attrTextPer = 55500--属性lang表前缀

function IsPercentageProp(itype)
    return not (itype == prop_pb.enPro_HP or itype == prop_pb.enPro_Attack or itype == prop_pb.enPro_Defend or itype == prop_pb.enPro_Speed)
end

function GetPropText(arr)
    local propText = ""
    if IsPercentageProp(tonumber(arr[1])) then
        if tonumber(arr[3]) == 0 then
            propText = lang.Get(attrTextPer + tonumber(arr[1])) .. "    +" .. tonumber(arr[2]) / 10000 * 100 .. "%"
        else
            propText = lang.Get(progessName[tonumber(arr[3])]) .. lang.Get(attrTextPer + tonumber(arr[1])) .. "    +" .. tonumber(arr[2]) / 10000 * 100 .. "%"
        end
    else
        if tonumber(arr[3]) == 0 then
            propText = lang.Get(attrTextPer + tonumber(arr[1])) .. "    +" .. tonumber(arr[2])
        else
            propText = lang.Get(progessName[tonumber(arr[3])]) .. lang.Get(attrTextPer + tonumber(arr[1])) .. "    +" .. tonumber(arr[2])
        end
    end
    return propText
end

function GetPropText2(arr)
    local propText = ""
    local numText = ""
    if IsPercentageProp(tonumber(arr[1])) then
        if tonumber(arr[3]) == 0 then
            propText = lang.Get(attrTextPer + tonumber(arr[1]))
            numText = "+" .. tonumber(arr[2]) / 10000 * 100 .. "%"
        else
            propText = lang.Get(progessName[tonumber(arr[3])]) .. lang.Get(attrTextPer + tonumber(arr[1]))
            numText = "+" .. tonumber(arr[2]) / 10000 * 100 .. "%"
        end
    else
        if tonumber(arr[3]) == 0 then
            propText = lang.Get(attrTextPer + tonumber(arr[1]))
            numText = "+" .. tonumber(arr[2])
        else
            propText = lang.Get(progessName[tonumber(arr[3])]) .. lang.Get(attrTextPer + tonumber(arr[1]))
            numText = "+" .. tonumber(arr[2])
        end
    end
    return propText, numText
end

function isActiveRoleFace(id)
    local isActive = false
    if saveActiveFaceID[id] and saveActiveFaceID[id] == id then
        isActive = true
    end
    return isActive
end

function GetRoleFaceID()
    return saveEquipFaceID
end

function GetRoleFrameID()
    return saveEquipFrameID
end

function GetRoleFacePropID()
    return savePropFaceID
end

function GetAllActiveFaceData()
    local tempActiveFace = {}
    tempActiveFace = saveActiveFaceID
    return tempActiveFace
end

function HasModifiedName()
    return bModifyName
end

function SetModifiedName(val)
    bModifyName = val
end

function SetPlayerFrameAttrID(id)
    if id == nil then
        return
    end
    if id == playerFrameAttrID then
        return
    end
    local oldID = playerFrameAttrID
    playerFrameAttrID = id
    event.Trigger(event.ACTOR_FRAME_ATTR_UPDATE, oldID, playerFrameAttrID)
end

function GetPlayerFrameAttrID()
    if playerFrameAttrID then
        return playerFrameAttrID
    end
    return 0
end

function SetPlayerTitleID(id)
    if id == nil then
        return
    end
    local oldID = playerTitleID
    playerTitleID = id
    event.Trigger(event.ACTOR_TITLE_UPDATE, oldID, playerTitleID)
end

function GetPlayerTitleID()
    if playerTitleID then
        return playerTitleID
    end
    return 0
end

function SetPlayerTitleAttrID(id)
    if id == nil then
        return
    end
    if id == playerTitleAttrID then
        return
    end
    local oldID = playerTitleAttrID
    playerTitleAttrID = id
    event.Trigger(event.ACTOR_TITLE_ATTR_UPDATE, oldID, playerTitleAttrID)
end

function GetPlayerTitleAttrID()
    return playerTitleAttrID
end

--[[获取称号数据]]
function GetRoleTitleData(id)
    local allRoleData = {}
    local player_mgr = require("player_mgr")
    local item_data = require("item_data")
    local arrItemData = player_mgr.GetPacketGoods()
    local activeRoleDate = {}
    if #arrItemData > 0 then
        for k, v in pairs(arrItemData) do
            local itemData = v
            local itemId = itemData:GetGoodsID()
            local cfg = game_scheme:Item_0(itemId)
            if cfg then
                local cData = {}
                cData.id = itemId
                cData.type = cfg.type
                if cData.type == item_data.Item_Type_Enum.RoleTitle then
                    --已激活称号数据
                    table.insert(activeRoleDate, v)
                end
            end
        end
    end

    local count = game_scheme:RoleTitle_nums()
    for i = 0, count - 1 do
        local cfg = game_scheme:RoleTitle(i)
        local temp = {}
        temp.isActive = false
        temp.cfg = cfg
        for i, v in pairs(activeRoleDate) do
            if v:GetGoodsID() == cfg.TitleID then
                temp.isActive = true
            end
        end

        --类型6是联盟称号
        if cfg.LimitType ~= 6 then
            -- print("有VIP是:",cfg.IsShow)
            if temp.isActive or (not temp.isActive and cfg.IsShow == 1) then

                table.insert(allRoleData, temp)
            end
        end
    end
    if id then
        for i, v in ipairs(allRoleData) do
            if v.cfg.TitleID == id then
                return v
            end
        end
        return nil
    else
        table.sort(allRoleData, function(a, b)
            local isActiveA = a.isActive
            local isActiveB = b.isActive
            if isActiveA == true and isActiveB == false then
                return true
            elseif isActiveA == false and isActiveB == true then
                return false
            elseif isActiveA == false and isActiveB == false then
                return a.cfg.Priority > b.cfg.Priority
            end
        end)
        return allRoleData
    end
end

--[[获取头像框数据]]
function GetRoleFrameData()
    local player_mgr = require("player_mgr")
    local item_data = require("item_data")
    local arrItemData = player_mgr.GetPacketGoods()
    local activeRoleDate = {}
    local allRoleData = {}
    if #arrItemData > 0 then
        for k, v in pairs(arrItemData) do
            local itemData = v
            local itemId = itemData:GetGoodsID()
            local cfg = game_scheme:Item_0(itemId)
            if cfg then
                local cData = {}
                cData.id = itemId
                cData.type = cfg.type
                if cData.type == item_data.Item_Type_Enum.RoleFrame then
                    --已激活头像框数据
                    table.insert(activeRoleDate, v)
                end
            end
        end
    end

    local count = game_scheme:RoleFrame_nums()
    for i = 0, count - 1 do
        local cfg = game_scheme:RoleFrame(i)
        if cfg and cfg.isDelete == 0 then
            local temp = {}
            temp.isActive = false
            temp.cfg = cfg
            temp.expireTime = nil
            for i, v in pairs(activeRoleDate) do
                if v:GetGoodsID() == cfg.frameID then
                    temp.isActive = true
                    temp.expireTime = v:GetExpireTime()
                end
            end
            table.insert(allRoleData, temp)
        end
    end
    table.sort(allRoleData, function(a, b)
        local isActiveA = a.isActive
        local isActiveB = b.isActive
        if isActiveA == true and isActiveB == false then
            return true
        elseif isActiveA == false and isActiveB == true then
            return false
        elseif isActiveA == false and isActiveB == false then
            return a.cfg.Priority > b.cfg.Priority
        end
    end)
    return allRoleData
end

--------以下是服务器更新下来的数据------
function RoleFacePropUpdate(id)
    savePropFaceID = id
end

function RoleFaceUpdate(id)
    saveEquipFaceID = id
end

function RoleFrameUpdate(id)
    saveEquipFrameID = id
end

function AddRoleNewFace(id)
    saveActiveFaceID[id] = id
end

function OnActorEnterPartData(partData)
    saveActiveFaceID = {}
    bModifyName = partData.bModifyName
    for i = 1, #partData.activeFaceID do
        saveActiveFaceID[partData.activeFaceID[i]] = partData.activeFaceID[i]
    end

    --保存定制称号数据
    saveTitleData = {}
    saveTitleData.titles = {}
    --titles @type TCustomTitleContext
    local titles = partData.titledata.titles
    for i = 1, #titles do
        saveTitleData.titles[titles[i].titleid] = titles[i]
    end
    local custom_avatar_data = require "custom_avatar_data"
    local custom_avatar_mgr = require "custom_avatar_mgr"
    local facesData = partData.customFace.faceDatas
    if facesData then
        local listCount = facesData and #facesData or 0
        for i=1,listCount do
            custom_avatar_data.AddAvatar(facesData[i].imageId,facesData[i].curl,facesData[i].pos,facesData[i].status,facesData[i].used,true)
            custom_avatar_mgr.CheckAvatarLocalUrl(facesData[i].curl,facesData[i].imageId)
        end
    end
    custom_avatar_data.SetMyCustomAvatarData(partData.customFace)
end

--更新角色自定义称号数据
-- data @type TCustomTitleData
function UpdateRoleCustomTitleData(data)
    saveTitleData = {}
    saveTitleData.titles = {}
    --titles @type TCustomTitleContext
    local titles = data.titles
    for i = 1, #titles do
        log.Warning("titleId=" .. titles[i].titleid .. " name=" .. titles[i].name .. " backgroudidx=" .. titles[i].backgroudidx)

        saveTitleData.titles[titles[i].titleid] = titles[i]
    end

    -- 自定义称号数据改变
    event.Trigger(event.ACTOR_CUSTOM_TITLE_DATA_UPDATE)
end

--[[获取自定义称号数据]]
function GetCustomRoleTitleData()
    return saveTitleData
end

--处理faceID和faceStr通用方法  自定义头像
function ProcessFaceIDAndFaceStr(faceID,faceStr)
    local resFace = nil
    if faceID and type(faceID) == "number" then
        resFace = faceID
        if faceStr and type(faceStr) == "string" and not string.IsNullOrEmpty(faceStr) then 
            resFace = faceStr
            return resFace
        end
        return resFace
    end
    if faceStr and type(faceStr) == "string" and not string.IsNullOrEmpty(faceStr) then 
        resFace = faceStr
        return resFace
    end
    return resFace
end

function OnSceneDestroy()
    saveEquipFaceID = nil
    savePropFaceID = nil
    saveEquipFrameID = nil
    saveActiveFaceID = {}
    saveTitleData = {}
end
event.Register(event.SCENE_DESTROY, OnSceneDestroy)