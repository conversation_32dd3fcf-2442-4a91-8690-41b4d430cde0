local require  = require
local print    = print
local tostring = tostring
local type     = type
local os       = os
local string   = string
local tonumber = tonumber
local dump     = dump

local topic_pb         = require "topic_pb"
local util             = require "util"
local event            = require "event"
local game_scheme      = require "game_scheme"
local player_mgr       = require "player_mgr"
local lang             = require "lang"
local util             = require "util"
local topic_pb         = require"topic_pb"
local actor_face_data  = require "actor_face_data"
local player_prefs     = require "player_prefs"

module('tournament_mgr')
local tournamentlist = {}  --排行榜详细数据

local selfRankData
local nRankGroup    --存放排行榜分段    
local championshipslow --存放排行榜该分段的一行


local Rank       --存放分组信息的排名
local nRankReward --存放排行奖励
local Award={}       --存放奖励信息

INVALID_INT_VALUE = 0X7FFFFFFF      --32位二进制

local championships_rank = 0        --玩家当前排行
local championships_preRank = 0     --玩家上一次排名
local endtime = 0                   --结束时间
local championshipsId = 0           --锦标赛ID
local championshipsIdopen = 0       --开启条件
local championshipsHasget = 0       --玩家是否已领奖  1表示已领奖
local championships_groupId = 0     --玩家分组ID
local championships_value = 0       --过程中累计的值
local championships_period = 0      --第n届锦标赛
local championshipsBubblePreRank  = 0 --用于气泡弹出
local championshipsPreStar = 0      --星星数
local enterEndtime = 0 --入口关闭时间
local endCountDownTimer = 0 --入口结束计时器
local rewardCountDownTimer = nil    --刷新奖励红点倒计时

--初始化三星锦标赛  ntype 表示类型  nRank表示数据
function TournamentListInit(eventName,errCode,nType,nRank)
    -- 详细错误信息见 error_code_pb
    if errCode ~= 0 then
        local log = require "log"
        local lang_res_key = require "lang_res_key"
        local errId = lang_res_key.KEY_ERROR_CODE_SERVER_BASE + errCode
        local errStr = lang.Get(errId)
        if string.empty(errStr) then
            log.Warning("三星锦标赛排行榜网络错误 id:"..tostring(errCode)..",type:"..tostring(nType))
        else
            log.Warning("三星锦标赛排行榜网络错误 id:"..tostring(errCode)..",type:"..tostring(nType)..",msg:"..errStr)
        end
        if errCode ~= 2702 then --2702:排行榜已删除，但需要去更新自己数据
            return
        end
    end

    InitRankAwardData(errCode, nRank)

    -- 数据结束后继续显示自己的排行榜信息
    if selfRankData == nil then
        selfRankData =
        { 
            roleID  =  player_mgr.GetPlayerRoleID(),
            roleLv  = player_mgr.GetPlayerLV(),
            faceID  = actor_face_data.GetRoleFaceID(),
            name    = player_mgr.GetRoleName(),	
            frameID = player_mgr.GetPlayerFrameID(),
            rankKey = championships_value,	
            nRank   = championships_rank,
            Award   = GetMyReward(championships_rank),	        
        } 
    end
    event.Trigger(event.RANK_DETAIL_DATA_CHAMPIONSHIPS)
end

function IsValidPropertyValue(value)
    return (value and value ~= 0 and value ~= INVALID_INT_VALUE)
end

function InitRankAwardData(errCode, nRank)
    --初始化排行奖励配置
    local cfg = game_scheme:championships_0(championshipsId)
    if not cfg then 
        return
    end
    nRankGroup = cfg.nRankGroup
    for i = 1, nRankGroup.count do
        local subsectionIdx = nRankGroup.data[i - 1]
        local subsectionData = game_scheme:championshipsSubsection_0(subsectionIdx)
        if championshipsIdopen >= subsectionData.begin
        and championshipsIdopen <= subsectionData.End then
            Rank = subsectionData.nRank
            nRankReward = string.split(subsectionData.nRankReward, ';', tonumber)
            break
        end
    end
    --dump(nRankReward)
    if errCode ~= 0 then
        return
    end

    local rankData
    local playerRoleId = player_mgr.GetPlayerRoleID()

    for i = 1, #nRank do
        rankData = nRank[i]
        if Rank then
            for j = 0, Rank.count - 1 do
                if rankData.nRank <= Rank.data[j] and nRankReward then
                    Award[i] = nRankReward[j + 1]
                    break
                end
            end
        end

        if rankData.roleID == playerRoleId then
            selfRankData =             {
                roleID = rankData.roleID,
                roleLv = rankData.roleLv,
                faceID = rankData.faceID,
                name    = rankData.name,
                frameID = rankData.frameID,
                rankKey = rankData.rankKey,
                nRank = rankData.nRank,
                Award = Award[rankData.nRank],
            }
        end
    end

    tournamentlist.rankList = nRank
    tournamentlist.championshipsId = championshipsId
end

event.Register(event.RANK_DETAIL_CHAMPIONSHIPS,TournamentListInit)

function ClearData()
    tournamentlist = {}
    selfRankData = nil
    nRankGroup = nil
    Rank = nil
    nRankReward = nil
    Award = {}

    championships_rank = 0          --玩家排行
    championships_preRank = 0       --玩家登录时本地保存的排名
    endtime = 0                     --结束时间
    championshipsId = 0             --锦标赛ID
    championshipsIdopen = 0         --开启条件
    championshipsHasget = 0         --玩家是否已领奖  1表示已领奖
    championships_groupId = 0       --玩家分组ID
    championships_value = 0         --过程中累计的值
    championships_period = 0        --第n届锦标赛
    championshipsBubblePreRank = 0 --用于气泡弹出
    championshipsPreStar = 0

    if endCountDownTimer then
        util.RemoveDelayCall(endCountDownTimer)
        endCountDownTimer = nil
    end

    if rewardCountDownTimer then 
        util.RemoveDelayCall(rewardCountDownTimer)
        rewardCountDownTimer = nil
    end
end

--解析登录是派发的包  --首次登录
function SetWantedData(data)
    -- dump(data)
    ClearData()

    championships_rank = data[topic_pb.ETOPICNAME_CHAMPIONSHIPS_RANK + 1]
    SetEndTime(data[topic_pb.ETOPICNAME_CHAMPIONSHIPS_ENDTIME + 1])
    championshipsId    = data[topic_pb.ETOPICNAME_CHAMPIONSHIPS_ID + 1]
    championshipsIdopen = data[topic_pb.ETOPICNAME_CHAMPIONSHIPS_OPENVALUE + 1]
    championshipsHasget = data[topic_pb.ETOPICNAME_CHAMPIONSHIPS_HASGET + 1]
    championships_groupId = data[topic_pb.ETOPICNAME_CHAMPIONSHIPS_GROUPID + 1]
    championships_value = data[topic_pb.ETOPICNAME_CHAMPIONSHIPS_VALUE + 1]
    championships_period = data[topic_pb.ETOPICNAME_CHAMPIONSHIPS_PERIOD + 1]

    championshipsBubblePreRank = championships_rank
    championshipsPreStar = championships_value
    championships_preRank = GetSavedChampionshipRank()

    -- print("GetSavedChampionshipRank uuidKey:"..GetChampionUUID()..",preRank:"..tostring(championships_preRank)..",curRank:"..championships_rank)
    if IsShowChampionships() then
        event.Trigger(event.UPDATA_CHAMPIONSHIPSRANK_OPENNOVICE)
    end
    --活动结束倒计时，刷新活动入口
    if endCountDownTimer then
		util.RemoveDelayCall(endCountDownTimer)
		endCountDownTimer = nil
    end

    if rewardCountDownTimer then 
        util.RemoveDelayCall(rewardCountDownTimer)
        rewardCountDownTimer = nil
    end

    local serverTime = os.server_time()
    if enterEndtime == 0 or serverTime > enterEndtime then 
        --print("关卡锦标赛入口已关闭！！！！！！！！！！！！！！", enterEndtime - serverTime)
        return
    end
    local endSurplusTime = enterEndtime - serverTime + 5 --距离关闭入口的间隔时间，延迟5秒，避免误差导致刷新失败
    --print("<color=#ff0077>计算三星锦标赛活动时间</color>", "endtime", endtime, "enterEndtime", enterEndtime, "endSurplusTime", endSurplusTime, 
    --"server_time",serverTime)
    endCountDownTimer = util.DelayCall(endSurplusTime, function()
        --print("倒计时结束，自动刷新三星入口状态！！！！！！！！！！")
        event.Trigger(event.UPDATA_CHAMPIONSHIPSRANK_OPENNOVICE)
        event.Trigger(event.UPDATE_HOOK_LEVEL_TOURNAMENT_STATE)
    end)

    local rewardSurplusTime = endtime - serverTime + 5 --距离发奖励的间隔时间，延迟5秒，避免误差导致刷新失败
    if rewardSurplusTime > 0 then
        rewardCountDownTimer = util.DelayCall(rewardSurplusTime, function()
            --计算当前已展示时间
            print("奖励倒计时结束，刷新入口红点状态！！！！！！！！！！")
            event.Trigger(event.UPDATA_CHAMPIONSHIPSRANK_OPENNOVICE)
        end)
    end
end

function SetEndTime(time)
    endtime = time or 0

    if not endtime then
        return
    end
    
    --超出结束时间22小时后，不再显示入口
    --活动结束后入口保留时间
    local enterSaveTime = 22
    local cfg = game_scheme:InitBattleProp_0(634)
    if cfg then 
        enterSaveTime = cfg.szParam.data[0]
    end

    enterEndtime = endtime + enterSaveTime * 3600 --入口关闭时间
end

function SetData(topicKey,value)
    ------ print(topicKey,value)
    if topicKey == topic_pb.ETOPICNAME_CHAMPIONSHIPS_RANK then
        --如果之前排名数据无效，新收到的排名有效，则代表收到活动开启数据,需要请求关卡玩家信息
        if (championships_rank == 0 or championships_rank == INVALID_INT_VALUE) and value ~= INVALID_INT_VALUE then
            local hook_level_tournament_mgr = require "hook_level_tournament_mgr"
            hook_level_tournament_mgr.AutoReqHookLevelRoleDatas()
        end
        if championships_rank ~= value and IsValidPropertyValue(championships_rank) then
            championships_preRank = championships_rank
            -- SaveRankData 还依赖 ETOPICNAME_CHAMPIONSHIPS_PERIOD 数据，按目前逻辑，暂在收到排名数据时就保存
            SaveRankData()
        end
        championships_rank = value
        if championshipsBubblePreRank == 0 then
            championshipsBubblePreRank = championships_rank
        end
    elseif topicKey == topic_pb.ETOPICNAME_CHAMPIONSHIPS_ENDTIME then
        --endtime = value 
		SetEndTime(value)
     elseif topicKey == topic_pb.ETOPICNAME_CHAMPIONSHIPS_ID then 
        championshipsId = value 
     elseif topicKey == topic_pb.ETOPICNAME_CHAMPIONSHIPS_HASGET then 
        championshipsHasget = value
    elseif topicKey == topic_pb.ETOPICNAME_CHAMPIONSHIPS_GROUPID then
        championships_groupId = value
    elseif topicKey == topic_pb.ETOPICNAME_CHAMPIONSHIPS_VALUE then
        championships_value = value
        if championshipsPreStar == 0 then
            championshipsPreStar = championships_value
        end
    elseif topicKey == topic_pb.ETOPICNAME_CHAMPIONSHIPS_PERIOD then
        championships_period = value
    end

    if IsShowChampionships() then
        event.Trigger(event.UPDATA_CHAMPIONSHIPSRANK_OPENNOVICE)
    end
end

--获取锦标赛个人排名
function GetChampionshipsRank()
  return championships_rank 
end

function GetMyChampionships()
    return selfRankData
end

function GetPlayereGroupId()
    return championships_groupId
end

function GetPlayerChampionshipValue()
    return championships_value
end

--是否显示入口地址
function IsShowChampionships()
    if not endtime or not championshipsId then 
        return false
    end

    if championshipsId == 0 or endtime <= 0 then 
        return false
    end

    if enterEndtime == 0 or os.server_time() > enterEndtime then 
        return false
    end
    return true
end

--是否已领奖
function IsAward()
    return championshipsHasget == 1
 end

--是否还可以请求锦标赛数据（截榜时间）
function IsRequestRank()
   local cstiem= os.server_time() 
   if championshipsId then
        local timeinfo = game_scheme:championships_0(championshipsId).SaveRankTime--读表
        local showtime = endtime + timeinfo  *24 * 3600
        return cstiem < showtime
    end
    return false
end

--获取锦标赛结束时间
function GetChampionshipsEndTime()
    return endtime
end

--获取锦标赛ID
function GetChampionshipsID()
    return championshipsId
end

function GetChampionPeriod()
    return championships_period
end

--玩家角色id + 锦标赛ID + 锦标期数 才能唯一标识具体玩家的每一场锦标赛
function GetChampionUUID()
    local roleID = player_mgr.GetPlayerRoleID()
    if roleID == 0 then
        local log = require "log"
        log.Error("未能获取角色id")
        return
    end
    return roleID.."#"..championshipsId.."#"..championships_period
end

--本地持久化保存，考虑此排名数据量很小，暂未删除往期排名数据
function SaveRankData()
    if championships_preRank == nil or championships_preRank == 0 then
        return
    end
    local uuidKey = GetChampionUUID().."#preRank"
    player_prefs.SetInt(uuidKey, championships_preRank)
    player_prefs.DelaySave(2) --延时两秒保存
end

function GetBubblePreRank()
    return championshipsBubblePreRank
end

function ResetBubblePreRank()
    championshipsBubblePreRank = championships_rank
end

function GetPreStar()
    return championshipsPreStar
end

function ResetPreStar()
    championshipsPreStar = championships_value
end

--返回的玩家上一次排名
function GetPreRank()
    return championships_preRank
end

function GetSavedChampionshipRank()
    if championshipsId == nil or championshipsId == 0 or championships_period == nil or championships_period == 0 then
        return nil
    end
    local uuidKey = GetChampionUUID().."#preRank"
    local rankValue = player_prefs.GetInt(uuidKey, -1)
    if rankValue == -1 then
        return nil
    end
    return rankValue
end

--获取三星锦标赛排行榜数据
function GetTournamentList()
     if IsRequestRank() then
        return tournamentlist.rankList
     end
end

--获取个人奖励
function GetMyReward(rank)
    local myAward
    if Rank then
        for j=0,Rank["count"]-1 do
            if rank <= Rank.data[j] and nRankReward then
                myAward = nRankReward[j+1]
                break
            end
        end
    end
    return myAward
end
-- --存放分组信息的排名
-- function GetnRank()
--     return Rank 
-- end  
--返回奖励的列表 
function GetnRankReward() 
   return Award
end

function SetLastBubbleTime(time)
    if time == nil then
        return
    end
    local uuidKey = GetChampionUUID().."#lastbubbletime"
    player_prefs.SetInt(uuidKey,time)
end

function GetLastBubbleTime()
    local uuidKey = GetChampionUUID().."#lastbubbletime"
    local saved = player_prefs.GetInt(uuidKey,0)
    return saved
end

function SetBubbleMergeTime(time)
    if time == nil then
        return
    end
    local uuidKey = GetChampionUUID().."#bubblemergetime"
    player_prefs.SetInt(uuidKey,time)
end

function GetBubbleMergeTime()
    local uuidKey = GetChampionUUID().."#bubblemergetime"
    local saved = player_prefs.GetInt(uuidKey,0)
    return saved
end

-- --发配排行榜
-- function GetRankList()
--    return Rank
-- end
event.Register(event.SCENE_DESTROY, ClearData)
event.Register(event.ACCOUNT_CHANGE_WORLD_RSP, ClearData)

function BuildEventReportData()
    local endTime = GetChampionshipsEndTime()
    if endTime == 0 or endTime == INVALID_INT_VALUE then
        --锦标赛未开启
        log.Error("无效的锦标赛数据,endTime:"..tostring(endTime))
        return nil
    end

    local prop = {}
    prop.player_star = GetPlayerChampionshipValue()
    local laymain_data = require "laymain_data"
    prop.player_level = laymain_data.GetPassLevel()
    local playerChampionshipsData = GetMyChampionships()
    if playerChampionshipsData and playerChampionshipsData.nRank ~= 0 then
        prop.player_rank = playerChampionshipsData.nRank
    else
        prop.player_rank = GetChampionshipsRank()
    end
    prop.player_group = GetPlayereGroupId()
    prop.player_power = player_mgr:GetPlayerBattlePower()
    local roleName = tostring(player_mgr.GetRoleName()) or ""
    prop.role_name = roleName
    return prop
end