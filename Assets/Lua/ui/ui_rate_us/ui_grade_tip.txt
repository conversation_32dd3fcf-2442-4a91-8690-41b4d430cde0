--@region FileHead
-- ui_grade_tip.txt ---------------------------------
-- author:  李婉璐
-- date:    9/2/2020 5:26:57 PM
-- ver:     1.0
-- desc:    跳转评分功能
-------------------------------------------------
--@endregion 

--@region Require
local require   = require
local string    = string
local os        = os

local Button        = CS.UnityEngine.UI.Button
local Text          = CS.UnityEngine.UI.Text
local Image         = CS.UnityEngine.UI.Image
local RectTransform = CS.UnityEngine.RectTransform
local Application = CS.UnityEngine.Application
local RuntimePlatform = CS.UnityEngine.RuntimePlatform
local TextMeshProUGUI         = CS.TMPro.TextMeshProUGUI
local SpriteSwitcher = CS.War.UI.SpriteSwitcher

local event					= require "event"
local class                 = require "class"
local ui_base               = require "ui_base"
local module_scroll_list    = require "scroll_list"
local ui_window_mgr         = require "ui_window_mgr"
local game_scheme           = require "game_scheme"
local lang                  = require "lang"
local goods_item            = require "goods_item_new"
local url_mgr               = require "url_mgr"
local grade_data            = require "grade_data"
local net_script            = require "net_script"
local card_sprite_asset		= require"card_sprite_asset"
local oversea_res 			= require "oversea_res"

--@endregion 

--@region ModuleDeclare
module("ui_grade_tip")
--local interface = require "iui_grade_tip"
local window = nil
local UIGradeTip = {}
local goodsCount = game_scheme:InitBattleProp_0(573).szParam.data[0]
local lastShowTime = nil



local tmpAsset = nil
--@endregion 

--@region WidgetTable
UIGradeTip.widget_table = {
--@region User
    closeBtn = {path = "closeBT", type = Button,backEvent = true},
    firstTrans = {path = "firstTrans", type = RectTransform},
    secondTrans = {path = "secondTrans", type = RectTransform},
    thirdTrans = {path = "thirdTrans", type = RectTransform},
    fourthTrans = {path = "fourthTrans", type = RectTransform},
    likeBtn = {path = "firstTrans/likeBtn", type = Button},
    unLikeBtn = {path = "firstTrans/unLikeBtn", type = Button},
    laterBtn = {path = "firstTrans/laterBtn", type = Button},
    doBtn = {path = "secondTrans/likeBtn", type = Button},
    undoBtn = {path = "secondTrans/unLikeBtn", type = Button},
	goodsTxt = {path = "secondTrans/goodsText", type = Text},
    quitBtn = {path = "thirdTrans/closeBtn", type = Button},
    diamondTxt = {path = "fourthTrans/text2", type = TextMeshProUGUI},
    goodsIcon = {path = "fourthTrans/goodsIcon", type = RectTransform},
	receiveBtn = {path = "fourthTrans/receiveBtn", type = Button},
	
	-- 钻石奖励图标
	diamondImg = {path = "secondTrans/zuanshijiangli", type = Image},
	text = {path = "secondTrans/text2", type = Text},

	titlebg = {path = "titlebg", type = SpriteSwitcher},
	titleLang = {path = "titlebg", type = "Image"},
	
	
--@endregion 
}
--@endregion 

--@region WindowCtor
function UIGradeTip:ctor(selfType)
--@region User
	self._noBg = true

	self.festivalSpriteAsset = card_sprite_asset.CreateNormalActivityAsset()
	tmpAsset = self.festivalSpriteAsset
--@endregion 
end --///<<< function

--@endregion 

--@region WindowInit
--[[窗口初始化]]
function UIGradeTip:Init()
	-- 设置评分标记，只弹出一次
	if grade_data.GRADE_FLAG == 0 then
		self.firstTrans.gameObject:SetActive(true)
		-- net_script.RequestServerLuaFuncNew("Request_SetJumpScoreFlag", {flag = 1})
	elseif grade_data.GRADE_FLAG == 2 and grade_data.REWARD_FLAG == 0 then
		self.fourthTrans.gameObject:SetActive(true)
		self.closeBtn.gameObject:SetActive(true)
		self.firstTrans.gameObject:SetActive(false)
	else
		self.closeBtn.gameObject:SetActive(true)
	end

    self:SubscribeEvents()
	self.goodsTxt.text = string.format(lang.Get(80910), goodsCount)
	self.diamondTxt.text = string.format(lang.Get(80908), goodsCount)
	self.iconUI = self.iconUI or goods_item.CGoodsItem():Init(self.goodsIcon.transform, nil, 0.65)
	self.iconUI:SetGoods(nil, 2, goodsCount)
	self.titlebg:Switch(lang.USE_LANG == lang.ZH and 0 or 1)
	
--@region User
--@endregion 
end --///<<< function

--@endregion 

function UIGradeTip:ChangeLangRes(ImgPath, Img)
	local res = ImgPath
	local callBack = function(asset)
		if self:IsValid() then
			if asset then
				--加载多语言图片成功后的回调
				Img.sprite = asset
				Img.enabled = true
				Img:SetNativeSize()
			end
		end
	end
	return oversea_res.LoadSprite(res, "ui_grade_tip", callBack)
end



--@region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function UIGradeTip:OnShow()
    self:UpdateUIPage()
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowOnHide
--[[界面隐藏时调用]]
function UIGradeTip:OnHide()
--@region User
--@endregion 
end --///<<< function

--@region WindowUpdateUI
--[[资源加载完成，被显示的时候调用]]
function UIGradeTip:UpdateUIPage()

--@region User

	if oversea_res.ISOverSeaLang() then
		if(self:ChangeLangRes("pingjia_tanchuang_zi",self.titleLang))then
			--if(lang.USE_LANG ~= lang.ZH and self.UICfg and self.UICfg.en_banner and self.UICfg.en_banner~="" )then
			--	self.bannerImg = self.UICfg.en_banner
			--else
			--	self.bannerImg = "pingjia_tanchuang_zi"
			--end
			self.festivalSpriteAsset:GetSprite("pingjia_tanchuang_zi", function(sp)
				self.titleLang.sprite = sp
				self.titleLang.enabled = true
				self.titleLang:SetNativeSize()
			end)
		end
	else
		self.titlebg:Switch(lang.USE_LANG == lang.ZH and 0 or 1)
	end
	
--@endregion 
end --///<<< function

--@endregion 

--@region WindowClose
function UIGradeTip:Close()
    if self:IsValid() then
		self:UnsubscribeEvents()
    end
	
	if self.iconUI then
        self.iconUI:Dispose()
        self.iconUI = nil
    end
	
	if grade_data.GetReasonType() == grade_data.REASON_TYPE.firstRecharge then
		grade_data.SetRechargeTime()
	end
	grade_data.SetReasonData(grade_data.REASON_TYPE.firstRecharge)
	self.__base:Close()
    window = nil
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowSubscribeEvents
--[[订阅UI事件]]
function UIGradeTip:SubscribeEvents()
----///<<< Button Proxy Line >>>///-----
	self.closeBtnEvent = function()
		grade_data.SetShowTime()
		grade_data.SetCurReasonTypeShowTime()
		ui_window_mgr:UnloadModule("ui_grade_tip")
    end
    self.closeBtn.onClick:AddListener(self.closeBtnEvent)
	self.quitBtn.onClick:AddListener(self.closeBtnEvent)

	self.likeBtnEvent = function()
		-- self.firstTrans.gameObject:SetActive(false)
		-- self.secondTrans.gameObject:SetActive(true)
		self.doBtnEvent()
		-- local data = {
		-- 	rating_likeornot=1,
		-- 	rating_reason = grade_data.GetReasonType(),
		-- 	rating_numberofjumps = grade_data.GetRatingNum()
		-- }
		-- event.Trigger(event.GAME_EVENT_REPORT, "rating_likeornot", data)
	end
	self.likeBtn.onClick:AddListener(self.likeBtnEvent)
	
	self.unLikeBtnEvent = function()
		self.firstTrans.gameObject:SetActive(false)
		self.thirdTrans.gameObject:SetActive(true)
		self.closeBtn.gameObject:SetActive(true)
	
		local data = {
			rating_likeornot=2,
			rating_reason = grade_data.GetReasonType(),
			rating_numberofjumps = grade_data.GetRatingNum()
		}
		event.Trigger(event.GAME_EVENT_REPORT, "rating_likeornot", data)
	end
	self.unLikeBtn.onClick:AddListener(self.unLikeBtnEvent)
	
	self.laterBtnEvent = function()
		local data = {
			rating_likeornot=3,
			rating_reason = grade_data.GetReasonType(),
			rating_numberofjumps = grade_data.GetRatingNum()
		}
		event.Trigger(event.GAME_EVENT_REPORT, "rating_likeornot", data)
		self.closeBtnEvent()
	end
	self.laterBtn.onClick:AddListener(self.laterBtnEvent)
	
	self.doBtnEvent = function()
		-- 跳转评分界面
		local data = {
			rating_like_comornot=1,
			rating_reason = grade_data.GetReasonType(),
			rating_numberofjumps = grade_data.GetRatingNum()
		}
		event.Trigger(event.GAME_EVENT_REPORT, "rating_like_comornot", data)
		grade_data.SetJumpTime(os.time())
		net_script.RequestServerLuaFuncNew("Request_SetJumpScoreFlag",{flag = 2})
		self.firstTrans.gameObject:SetActive(false)
		local url = Application.platform == RuntimePlatform.IPhonePlayer and url_mgr.APPLE_URL or url_mgr.GOOGLE_URL
		local q1sdk = require "q1sdk"
		q1sdk.ApplicationOpenURL(url)
		ui_window_mgr:UnloadModule("ui_grade_tip")
	end
	self.doBtn.onClick:AddListener(self.doBtnEvent)

	self.undoBtnEvent = function()
		local data = {
			rating_like_comornot=2,
			rating_reason = grade_data.GetReasonType(),
			rating_numberofjumps = grade_data.GetRatingNum()
		}
		event.Trigger(event.GAME_EVENT_REPORT, "rating_like_comornot", data)
		self.closeBtnEvent()
	end
	self.undoBtn.onClick:AddListener(self.undoBtnEvent)
	
	self.receiveBtnEvent = function()
		if grade_data.GRADE_FLAG == 2 then
			net_script.RequestServerLuaFuncNew("Request_GetJumpScoreReward",{})
		end
        self.closeBtnEvent()
	end
	self.receiveBtn.onClick:AddListener(self.receiveBtnEvent)
end --///<<< function

--@endregion 

--@region WindowUnsubscribeEvents
--[[退订UI事件]]
function UIGradeTip:UnsubscribeEvents()
--@region User
	if self.festivalSpriteAsset then
		self.festivalSpriteAsset:Dispose()
		self.festivalSpriteAsset = nil
		tmpAsset = nil
	end
	
	self.closeBtn.onClick:RemoveListener(self.closeBtnEvent)
	self.quitBtn.onClick:RemoveListener(self.closeBtnEvent)
	self.likeBtn.onClick:RemoveListener(self.likeBtnEvent)
	self.unLikeBtn.onClick:RemoveListener(self.unLikeBtnEvent)
	self.laterBtn.onClick:RemoveListener(self.laterBtnEvent)
	self.doBtn.onClick:RemoveListener(self.doBtnEvent)
	self.undoBtn.onClick:RemoveListener(self.undoBtnEvent)
	self.receiveBtn.onClick:RemoveListener(self.receiveBtnEvent)
--@endregion 
end --///<<< function

--@region WindowInherited
local CUIGradeTip = class(ui_base, nil, UIGradeTip)
--@endregion 

--@region ModuleFunction
function Show()
    if window == nil then
        window = CUIGradeTip()
        window._NAME = _NAME
        window:LoadUIResource('ui/prefabs/uigradetip.prefab', nil, nil, nil, true)
    end
    window:Show()
    return window
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end

function Close()
    if window ~= nil then
        Hide()
        window:Close()
        window = nil
    end
end

function OnSceneDestroy()
    lastShowTime = nil
end
event.Register(event.SCENE_DESTROY, OnSceneDestroy)