-- ui_auto_login_impl.txt ------------------------------------------
-- author:  潘云霄
-- date:    2017.12.22
-- ver:     1.0
-- desc:    自动登录实现文件
--------------------------------------------------------------

local require = require
local ui_base = require "ui_base"
local class = require "class"
local event = require "event"
local game = require "game"

module("ui_loading_impl")
local window = nil
local ui_iauto_login = nil
local AutoLoginUI ={}

AutoLoginUI.widget_table = 
{
	
}

--注册和销毁事件
function AutoLoginUI:SubscribeEvent()
  
end


function AutoLoginUI:UnsubscribeEvent()

end

function OnAutoLoading(eventName, curState)
	if curState == game.STATE_CREATE_ACTOR then
		window:Close()
	end
	if curState == game.STATE_MAKE_MATCH_V then
		window:Close()
	end
end

function AutoLoginUI:Init()
  self:SubscribeEvent()
end

function AutoLoginUI:Close()
  if self and self:IsValid() then
		self:UnsubscribeEvent()
	end
	self.__base:Close()
end

function ShowImpl()
	if window == nil then
		window = AutoLogin()
		window._NAME = _NAME;window:LoadUIResource("ui/prefabs/activityindicator.prefab")
	end
  window:Show()
  event.Register(event.STATE_ENTER, OnAutoLoading)  
	return window
end

function HideImpl()
  if window ~= nil then
		window:Hide()
	end
end

function CloseImpl()
	if window ~= nil then
    window:UnsubscribeEvent()
		window:Close()
		window = nil
	end
end

AutoLogin = class(ui_base, nil, AutoLoginUI)
