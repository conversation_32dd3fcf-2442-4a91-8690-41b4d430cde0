--@region FileHead
-- ui_dress_decoration.txt ---------------------------------
-- author:  曾琳琳
-- date:    2022/1/8 14:39:11
-- ver:     1.0
-- desc:    装备饰品切页界面Description
-------------------------------------------------
--@endregion 

--@region Require
local print     = print
local require   = require
local pairs     = pairs
local ipairs    = ipairs
local string    = string
local table     = table
local tonumber  = tonumber

local Button        = CS.UnityEngine.UI.Button
local Text          = CS.UnityEngine.UI.Text
local Image         = CS.UnityEngine.UI.Image
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable
local SpriteSwitcher 		= CS.War.UI.SpriteSwitcher
local RectTransform 	= CS.UnityEngine.RectTransform
local screen_util = require "screen_util"

local event					= require "event"
local class                 = require "class"
local ui_base               = require "ui_base"
local log                   = require "log"
local hero_mgr 				= require "hero_mgr"
local const                 = require "const"
local decoration_mgr        = require "decoration_mgr"
local skill_mgr             = require "skill_mgr"
local windowMgr 	        = require "ui_window_mgr"
local game_scheme  	        = require "game_scheme"
local card_sprite_asset     = require "card_sprite_asset"
local lang 			        = require "lang"
local player_mgr	        = require "player_mgr"
local skep_mgr              = require "skep_mgr"
local ui_hero_type_tip 		= require "ui_hero_type_tip"
local flow_text             = require "flow_text"
local force_guide_system    = require"force_guide_system"
local force_guide_event     = require"force_guide_event"
local util                  = require "util"
local flow_prop_item        = require "flow_prop_item" 
local oversea_res           = require "oversea_res"
local calpro_mgr            = require "calpro_mgr"
--@endregion 

--@region ModuleDeclare
module("ui_dress_decoration")
--local interface = require "iui_dress_decoration"
local window = nil
local UIDressDecoration = {}
local HeroID = 0	  -- 英雄id
local HeroSID = 0	  -- 英雄Sid
local HeroEntity = nil--英雄实体
EnterType = const.EnterType
local enterType = EnterType.bag
local DecorateData = nil
local QuickEquipDecorateData = nil
local DecorateCount = nil
local HeroCSV = nil
local takeOff = nil
local whiteColor = {r=255/255,g=255/255,b=255/255,a=1}
local grayColor = {r=162/255,g=162/255,b=162/255,a=1}
local yellowColor = {r=255/255,g=234/255,b=19/255,a=1}
local yellowColor2 = {r=255/255,g=240/255,b=0/255,a=1}
-- local recordProp = {
--     heroSid = 0,
--     hp = 0,
--     attack = 0,
--     defence = 0,
--     speed = 0,
-- }
local recordProp={
    [5]=0,--暴击  5
    [6]=0,--爆伤  6
    [7]=0,--效果命中  7
    [8]=0,--效果抵抗  8
    [13]=0,--破甲  13
    [14]=0,--免伤  14
    [15]=0,--基础生命百分比 15
    [16]=0,--基础攻击百分比 16
    [17]=0,--护甲百分比  17
    [45]=0,--暴击抵抗  45
    [46]=0,--爆伤减免  46
    [49]=0,--生命  49
    [50]=0,-- 攻击  50
}
local InputDecorateData = nil--传入饰品数据
local InputBlessData = nil--传入祝福数据
local DecoPower = nil
local colorEnum = {
	white = "<color=#FFFFFF>%s</color>",--技能颜色
	yellow = "<color=#87F425>%s</color>",--技能描述
	black = "<color=#283D4B>%s</color>",--技能具体
    gray="<color=#4D6676>%s</color>"--置灰
    }
local outLines = {
        {r=33/255,g=55/255,b=100/255,a=1},
        {r=33/255,g=55/255,b=100/255,a=1},
        {r=144/255,g=71/255,b=0/255,a=1}
    }
local selectedIndex = nil     -- 当前选择的英雄序号
--@endregion tipTxt

--@region WidgetTable
UIDressDecoration.widget_table = {
	--新增有+的四个角
    bgRect = {path = "bg",type=RectTransform},
    imgRect = {path ="bg/Image" , type = RectTransform},
	wearAllRect = {path ="bg/wearBtn" , type = RectTransform},--一鍵穿戴
	dressAllRect = {path ="bg/undressBtn" , type = RectTransform},--脫裝
    skillListRect = { path = "bg/skillList", type = RectTransform, event_name = "" },
    tipRect = {path = "bg/tip",type = RectTransform},
	zuanshidikuang = {path ="bg/zuanshidikuang" , type = SpriteSwitcher},
    profType = {path ="profType" , type = "Image"},--职业图标
	img_zuanshi = {path ="bg/zuanshi" , type = "Image"},--钻石图标
	zuanshi = {path ="bg/zuanshi" , type = SpriteSwitcher},
	heroType = {path ="type" , type = SpriteSwitcher},--阵营图标
	wearAll = {path ="bg/wearBtn" , type = "Button"},--一鍵穿戴
	undressAll = {path ="bg/undressBtn" , type = "Button"},--脫裝
	yellowplus = {path ="bg/zuanshidengjiBJ/yellowplus" , type = "Image"},
	redplus = {path ="bg/zuanshidengjiBJ/redplus" , type = "Image"},
	blueplus = {path ="bg/zuanshidengjiBJ/blueplus" , type = "Image"},
	whiteplus = {path ="bg/zuanshidengjiBJ/whiteplus" , type = "Image"},
	purpleplus = {path ="bg/zuanshidengjiBJ/purpleplus" , type = "Image"},
	gradeBg = {path = "bg/zuanshidengjiBJ", type = SpriteSwitcher},
    scroll_table = { path = "bg/skillList/Viewport/Content", type = ScrollRectTable, event_name = "" },
    scroll_table_rect = { path = "bg/skillList/Viewport/Content", type = RectTransform, event_name = "" },

	profTypeBtn = {path ="profType" , type = "Button"},
	heroTypeBtn = {path ="type" , type = "Button"},
    tipTxt = {path="bg/tip",type= "Text"},
    power = {path = "bg/Image/Power",type = "Text"},
	flowProp = {path = "flowProp",type = RectTransform},--升级属性飘字

    arrow = {path = "arrow",type = RectTransform},
	leftArrow = {path = "arrow/leftArrow", type = "Button"},
	rightArrow = {path = "arrow/rightArrow", type = "Button"},
    --技能栏

    skillDesc ={path="bg/skillList/Viewport/Content/ListItem/content/skillDesc",type="Text",fitArabic=true},

--@region User
--@endregion 
}
--@endregion 

--@region WindowCtor
function UIDressDecoration:ctor(selfType)
	self.__base:ctor(selfType)
	self.cardSpriteAsset = card_sprite_asset.CreateSpriteAsset()
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowInit
--[[窗口初始化]]
function UIDressDecoration:Init()
    if not self.flowTextBattlePowerData then
        self.flowTextBattlePowerData ={heroSid = nil,prePower = 0,targetPower = 0,}
    end
    --战斗力
	if HeroEntity and HeroEntity.battleProp then
        -- recordProp["hp"] = HeroEntity.battleProp.hp
        -- recordProp["attack"] = HeroEntity.battleProp.attack
        -- recordProp["defence"] = HeroEntity.battleProp.defence
        -- recordProp["speed"] = HeroEntity.battleProp.speed
        self:ResetPropTable()

        self.setPower = function ()
            if self and self:IsValid() then
                if enterType~=EnterType.otherPlayer then
                    local faker = calpro_mgr.CreateFakerHeroEntity(HeroEntity,HeroEntity.numProp.starLv,HeroEntity.numProp.stepLV,HeroEntity.numProp.lv,nil,nil,nil,nil,true)
                    if faker then
                        local _power = calpro_mgr.CalculatePowerByEntity(faker)
                        self.power.text = util.Float2Int(HeroEntity.battleProp.power-_power)
                    else
                    self.power.text = util.Float2Int(HeroEntity.battleProp.power)
                end
            else
                -- --print("DecoPower",DecoPower,"power",util.Float2Int(HeroEntity.battleProp.power))
                self.power.text = DecoPower or util.Float2Int(HeroEntity.battleProp.power)
            end
        end
        end
	end
    self:SubscribeEvents()
    self.scroll_table.onItemRender= OnRenderItem
	self.scroll_table.onItemDispose=function(scroll_rect_item,index)
        if scroll_rect_item and scroll_rect_item.data and scroll_rect_item.data.delayCall then
            util.RemoveDelayCall(scroll_rect_item.data.delayCall)
            scroll_rect_item.data.delayCall = nil
        end
    end
    
	--新手强制引导事件
	force_guide_system.TriEnterEvent(force_guide_event.tEventEnterDressDecorate)
    --屏蔽按钮
    self.wearAll.gameObject:SetActive(enterType ~= EnterType.otherPlayer)
    self.undressAll.gameObject:SetActive(enterType ~= EnterType.otherPlayer)
--@region User
--@endregion 
end --///<<< function

function OnRenderItem(scroll_rect_item,index,dataItem)
	scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem

    local icon = scroll_rect_item:Get("icon")
    local skillLv = scroll_rect_item:Get("skillLv")
    local skillName = scroll_rect_item:Get("skillName")
    local skillDesc = scroll_rect_item:Get("skillDesc")
    local tip = scroll_rect_item:Get("tip")
    local coreSkill = scroll_rect_item:Get("coreSkill")
    local cn = scroll_rect_item:Get("cn")
    local en = scroll_rect_item:Get("en")
    local iconGray = scroll_rect_item:Get("iconGray")
    local rect = scroll_rect_item:Get("rect")
    local contentRect = scroll_rect_item:Get("contentRect")
    local topRect = scroll_rect_item:Get("topRect")
    local lineRect = scroll_rect_item:Get("lineRect")
    local skillDescRect = scroll_rect_item:Get("skillDescRect")
    local skillImgSS = scroll_rect_item:Get("skillImgSS")
    local skillImg = scroll_rect_item:Get("skillImg")
    local lineImg = scroll_rect_item:Get("lineImg")
    local skillLvOutline = scroll_rect_item:Get("skillLvOutline")
    local skillNameOL = scroll_rect_item:Get("skillNameOL")
    local  tipOL = scroll_rect_item:Get("tipOL")
    scroll_rect_item.gameObject:SetActive(true)
    lineImg.gameObject:SetActive(index~=3)
    window.cardSpriteAsset:GetSprite(dataItem.icon, function(sprite)
		icon.sprite = sprite
	end)
    skillLv.text = dataItem.lv
    skillLv.color = dataItem.isActive and (index==3 and yellowColor2 or yellowColor) or whiteColor 
    skillLvOutline.effectColor = outLines[index]
    
    local str = string.format(dataItem.isActive and colorEnum.white or colorEnum.gray,string.format("%s Lv.%s",dataItem.name,dataItem.lv))
    skillName.text = str
    skillNameOL.enabled =dataItem.isActive
    --skillDesc.text = string.format(dataItem.isActive and colorEnum.blue or colorEnum.gray,dataItem.desc)
    local WarpText = scroll_rect_item:Get("WarpText")
    WarpText:SetWarpText(string.format(dataItem.isActive and colorEnum.black or colorEnum.gray,dataItem.desc))

    skillImgSS:Switch(index==3 and 1 or 0)
    tip.text = string.format(dataItem.isActive and colorEnum.yellow or colorEnum.gray,dataItem.tip)
    tipOL.enabled=dataItem.isActive
    coreSkill.gameObject:SetActive(dataItem.core==1)
    cn.gameObject:SetActive(lang.USE_LANG == "zh")

    en.gameObject:SetActive(lang.USE_LANG ~= "zh")

    if lang.USE_LANG ~= "zh" then
        local res = "CoreSkill_Cn"
        local callBack = function(asset)
            if window and window:IsValid() then
                if asset then
                    en.sprite = asset
                end
            end
        end
        oversea_res.LoadSprite(res,"ui_dress_decoration",callBack) 
    end

    iconGray:SetEnable(not dataItem.isActive)
    skillImg:SetNativeSize()

    --设置ListItem的位置
    if scroll_rect_item.data.delayCall then
        util.RemoveDelayCall(scroll_rect_item.data.delayCall)
        scroll_rect_item.data.delayCall = nil
    end
    -- --print("index",index)
    --scroll_rect_item.data.delayCall = util.DelayCall(0.1,function()
        local height = 0
        local padding=7
        if not window.heights then window.heights = {} end
        window.heights[index] = rect.sizeDelta.y
        --TODO:40
        local topRectH = 53
        local diff = 12 -- 多语言做适配
        -- if skillName.preferredHeight > topRectH then
        --     topRectH = skillName.preferredHeight
        -- end
        if tip.preferredHeight > topRectH then
            topRectH = tip.preferredHeight
        end
        topRect.sizeDelta = {x=topRect.sizeDelta.x,y=topRectH}

        skillDescRect.localPosition = {x=skillDescRect.localPosition.x,y=-topRectH-diff}
        local contentRectH = skillDesc.preferredHeight+topRectH+diff
        contentRect.sizeDelta = {x=contentRect.sizeDelta.x,y=contentRectH}
        -- lineRect.localPosition = {x=lineRect.localPosition.x,y=-(contentRect.sizeDelta.y+12)}
        local y = contentRectH+40 --底边和顶边距离
        
        -- 罗华冠： 此处没考虑会切换  这里先用了size  后再根据实际情况修改 size 颠倒了
        y=y<130 and 130 or y
        rect.sizeDelta = {x=rect.sizeDelta.x,y=y}
        
        window.heights[index] = y

        if index>=2 then
            for i=1,index-1 do
                if window.heights[i] then
                    height = height+window.heights[i]+padding
                end
            end
        end
        rect.localPosition = {x=rect.localPosition.x,y=-height}
      
        -- contentRect.localPosition = {x=contentRect.localPosition.x,y=-12}
        --print("index",index,"height",height,"skillName",skillName.preferredHeight,"tip.preferredHeight",tip.preferredHeight,"skillDesc.preferredHeight",skillDesc.preferredHeight,"y",y,"window.heights[index]",window.heights[index],"rect.sizeDelta.y",rect.sizeDelta.y,"rect.localPosition",rect.localPosition)
    --end)

    local func = {}
    local openSkillBtn = function ()
        -- 打开技能面板
        windowMgr:ShowModule("ui_hero_skill")
        local ui_hero_skill = require "ui_hero_skill"
        ui_hero_skill.SetHeroSkill(HeroSID,nil,2,dataItem.lv,dataItem.id,dataItem.slotPos,HeroEntity.numProp.starLv,HeroEntity.heroID,dataItem.lv,InputDecorateData)
        if enterType == EnterType.maze or player_mgr.IsNotOrdinaryPal(HeroEntity.heroSid) then
            ui_hero_skill.SetMazeEnter()
        end
    end
    func["openSkillBtn"] = openSkillBtn

    scroll_rect_item.InvokeFunc = function(funcname)
		func[funcname]()
    end
end

function UIDressDecoration:InitHeroInfo()
	local heroEntity = HeroEntity
	if heroEntity == nil then
        log.Error("heroEntity不存在！！！！HeroSID=",HeroSID)
		return
	end
	local heroStarLv = heroEntity.numProp.starLv
    -- --print("heroStarLv",heroStarLv)
	if heroStarLv >= hero_mgr.Hero_Star.White then
		self.zuanshidikuang:Switch(5)
		if heroStarLv >= hero_mgr.Hero_Star.White and heroStarLv <= hero_mgr.Hero_Star.White_1 then
			self.zuanshi:Switch(5)
		elseif heroStarLv >= hero_mgr.Hero_Star.White_2 and heroStarLv <= hero_mgr.Hero_Star.White_4 then
			self.zuanshi:Switch(6)
		elseif heroStarLv == hero_mgr.Hero_Star.White_5 then
			self.zuanshi:Switch(7)
		end
	elseif heroStarLv >= hero_mgr.Hero_Star.Red and heroStarLv <= hero_mgr.Hero_Star.RedPlus then
		self.zuanshidikuang:Switch(4)
		self.zuanshi:Switch(4)
	elseif heroStarLv >= hero_mgr.Hero_Star.Yellow and heroStarLv <= hero_mgr.Hero_Star.YellowPlus then
		self.zuanshidikuang:Switch(3)
		self.zuanshi:Switch(3)
	elseif heroStarLv >= hero_mgr.Hero_Star.Purple and heroStarLv <= hero_mgr.Hero_Star.Purple then
		self.zuanshidikuang:Switch(2)
		self.zuanshi:Switch(2)
	elseif heroStarLv == hero_mgr.Hero_Star.PurplePlus then
		self.zuanshidikuang:Switch(2)
		self.zuanshi:Switch(2)
	elseif heroStarLv == hero_mgr.Hero_Star.BluePlus or heroStarLv == hero_mgr.Hero_Star.Blue then
		self.zuanshidikuang:Switch(1)
		self.zuanshi:Switch(1)
	elseif heroStarLv == hero_mgr.Hero_Star.Green then
		self.zuanshidikuang:Switch(0)
		self.zuanshi:Switch(0)
	end
	self.img_zuanshi:SetNativeSize()
	self.yellowplus.gameObject:SetActive(heroStarLv == hero_mgr.Hero_Star.YellowPlus)
	self.redplus.gameObject:SetActive(heroStarLv == hero_mgr.Hero_Star.RedPlus or heroStarLv == hero_mgr.Hero_Star.White_1)
	self.blueplus.gameObject:SetActive(heroStarLv == hero_mgr.Hero_Star.BluePlus)
	self.purpleplus.gameObject:SetActive(heroStarLv == hero_mgr.Hero_Star.PurplePlus)
	self.whiteplus.gameObject:SetActive(heroStarLv >= hero_mgr.Hero_Star.White_2 and heroStarLv <= hero_mgr.Hero_Star.White_5)
	self.gradeBg:Switch(heroStarLv <= hero_mgr.Hero_Star.White_1 and 0 or 1)

    --设置类型
    self.heroType:Switch(HeroCSV.type - 1)
    self.cardSpriteAsset:GetSprite("prof_"..HeroCSV.profession, function(sprite)
        self.profType.sprite = sprite
    end)
end

--@endregion 

--@region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function UIDressDecoration:OnShow()
    self:UpdateUIPage()
    self:GetActiveSkillData()
    local bgHeight = 357.2016
    local imgPosY = 239
    local btnPosY = 288.5
    local skillHeight = 240.51
    local tipHeight = 279.9824
    if screen_util.width/screen_util.height < 720/1280 then
        bgHeight = 490
        imgPosY = 345
        btnPosY = 390
        skillHeight = 350
        tipHeight = 380
    end
    self:UpdateBtnStateOfTrial()
    self:UpdateArrowUI()
end

function UIDressDecoration:UpdateBtnStateOfTrial()
    local hero_trial_mgr = require "hero_trial_mgr"
    if HeroEntity and HeroEntity.heroSid and hero_trial_mgr.isTrialHeroBySid(HeroEntity.heroSid) then
        self.isTrialHero = true
        self.dressAllRect.gameObject:SetActive(false)
        self.wearAllRect.gameObject:SetActive(false)
    else
        self.isTrialHero = false
        self.dressAllRect.gameObject:SetActive(true)
        self.wearAllRect.gameObject:SetActive(true)
    end
end

function UIDressDecoration:GetListData()
    if not self.listData then
        local listData = {}
        local data = hero_mgr.GetHeroSystemArr()
        if data then
            for k,v in ipairs(data)do
                local value = self:CheckNextHero(v)
                if value then
                    table.insert(listData,v)
                    local heroSid = v.heroSid
                    if not selectedIndex and heroSid == HeroSID then
                        selectedIndex = #listData
                    end
                end
            end
        end
        self.listData = listData
    end
end

function UIDressDecoration:UpdateArrowUI()
    if enterType ~= EnterType.otherPlayer then
        self:GetListData()
        self.arrow.gameObject:SetActive(true)
        local count = self.listData and #self.listData or 0
        self.leftArrow.interactable = count > 1 and selectedIndex ~= 1
        self.rightArrow.interactable = count > 1 and  selectedIndex ~= count
    else
        self.arrow.gameObject:SetActive(false)
    end
end

function UIDressDecoration:CheckNextHero(heroEntity)
    local value = false
    if heroEntity then
        local decoration_mgr = require "decoration_mgr"
        local condition1,condition2,condition3 = decoration_mgr.IsDecorateOpen(heroEntity)
        value = condition1 and condition2 and condition3
    end
    return value
end
--@endregion 

--@region WindowOnHide
--[[界面隐藏时调用]]
function UIDressDecoration:OnHide()
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowSetInputParam
--[[设置窗口的输入参数。该参数通常是由其它模块或者外部设置进来。需要注意的是，
当调用这个函数的时候，窗口资源可能还是没有加载完成的。
@param p 参数表
]]
function UIDressDecoration:SetInputParam(_heroSID,_heroEntity,_enterType,inputDecorateData,inputBlessData,decoPower)
    HeroSID = _heroSID
    HeroEntity = _heroEntity
    if HeroEntity and HeroEntity.battleProp then
        if not self.flowTextBattlePowerData then
            self.flowTextBattlePowerData ={heroSid = nil,prePower = 0,targetPower = 0,}
        end
        self.flowTextBattlePowerData.prePower = util.Float2Int(HeroEntity.battleProp.power or 0)
        self.flowTextBattlePowerData.heroSid = HeroEntity.heroSid
    end
    if HeroEntity then
        HeroID = HeroEntity.heroID
    else
        log.Warning("UIDressDecoration:SetInputParam数据异常，HeroEntity为空！")
    end
    enterType = _enterType
    InputDecorateData = inputDecorateData--传入饰品数据
    InputBlessData = inputBlessData--传入祝福数据
    DecoPower = decoPower
    -- --print("HeroEntity.heroID",HeroEntity.heroID,"HeroSID",HeroSID)

    --如果正在显示，则更新一次窗口
    if self.UIRoot and self.UIRoot.activeSelf == true then
        self:UpdateUIPage()
    end
	
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowBuildUpdateData
--[[构建UI更新数据]]
function UIDressDecoration:BuildUpdateData()
    --获取英雄技能信息
    self.dataList = {}
    if not HeroEntity or not HeroEntity.numProp then 
        log.Error("有数据不存在！！！HeroEntity",HeroEntity,"HeroEntity.numProp",HeroEntity and HeroEntity.numProp)
        return 
    end
    if enterType ~= EnterType.otherPlayer then
        QuickEquipDecorateData = decoration_mgr.GetQuickDecorateData(HeroEntity)--可一键穿戴饰品数据
        local decorateSkepID = HeroEntity and HeroEntity.numProp.decorateSkepID--饰品栏ID
        local decorateData = nil
        DecorateData = {}
        if not decorateSkepID then
            log.Error("heroEntity.numProp.decorateSkepID：饰品栏ID为空")
        else
            decorateData = skep_mgr.GetGoodsSidOfSkep(decorateSkepID)--当前英雄穿戴的饰品数据
            for key,sid in pairs(decorateData) do
                local goodsEntity = player_mgr.GetPacketPartDataBySid(sid)
                if goodsEntity then
                    local decorateCfg = decoration_mgr.GetDecorationCSVByID(goodsEntity:GetGoodsID())
                    if not decorateCfg then
                        log.Error("未在Decoration表找到饰品ID",goodsEntity:GetGoodsID(),"sid",sid)
                    end
                    local type = decorateCfg and decorateCfg.type
                    if not DecorateData then DecorateData = {} end
                    DecorateData[goodsEntity.numProp.skepPlace+1] = sid
                    -- --print("饰品sid",sid,"饰品ID",goodsEntity:GetGoodsID(),"饰品位置skepPlace",goodsEntity.numProp.skepPlace,"饰品类型type",type)
                end
            end
        end
    end
    -- 11/23 罗华冠 切换英雄时 清理缓存配置表
    HeroCSV = nil
	local hero_cfg = HeroCSV or game_scheme:Hero_0(HeroID)
    HeroCSV = hero_cfg
    local decoCount,decoCount1,decoCount2 = decoration_mgr.GetDecorationCountByHero(HeroSID)
    if enterType == EnterType.otherPlayer then
        decoCount,decoCount1,decoCount2 = decoration_mgr.GetDecorationCountByHeroID(HeroID,InputDecorateData)
    end
    --2022/2/18 曾琳琳/易闻/李毅：技能2调整为9件T0激活，技能3调整为9件T1红激活；T1为T0的上级，可以单独也可以和T0混搭激活技能2
    local limitDecoCount = {3,9,9}
    if hero_cfg then
        if hero_cfg.DecorationsSkill then--饰品技能
            local decorationsSkill = string.split(hero_cfg.DecorationsSkill,";")
            for i,v in ipairs(decorationsSkill) do
                local arr = string.split(v, "#")
                local skillID = tonumber(arr[1])
                local lv = tonumber(arr[2])
                local core = tonumber(arr[3])
                if skillID and skillID>0 then
                    local temp = {}
                    temp.id = skillID
                    temp.lv = lv--当前技能等级/最大技能等级
                    local cfg_heroskill = game_scheme:HeroSkill_0(temp.id, temp.lv)
                    if cfg_heroskill then
                        temp.name = lang.Get(cfg_heroskill.nameID)
                        temp.desc = lang.Get(cfg_heroskill.descID)
                        temp.icon = cfg_heroskill.icon
                        temp.core = core
                        temp.slotPos = skill_mgr.GetSkillPosByID(skillID,HeroID)--TODO:获取技能位置
                        temp.needDecorate = HeroID and decoration_mgr.GetSkillCountByID(HeroID,skillID)--件数
                        if not temp.needDecorate then
                            temp.needDecorate = limitDecoCount[i]
                        end
                        temp.tip = i==3 and string.format(lang.Get(255242),temp.needDecorate) or string.format(lang.Get(255037),temp.needDecorate)
                        if i==3 then
                            temp.isActive = decoCount2>=temp.needDecorate
                        else
                            temp.isActive = decoCount>=temp.needDecorate--TODO:技能是否激活（判断每个英雄身上是否穿戴对应数量专属饰品）
                        end
                        table.insert(self.dataList,temp)
                    else
                        log.Error("读取活动HeroSkill error，请检查配置！id=",temp.id,"lv",temp.lv)
                    end
                else
                    log.Error("skillID",skillID)
                end
            end
        end
    else
        log.Error("读取活动Hero error，请检查配置！heroID=",HeroID)
    end
--@region User
--@endregion 
end --///<<< function

function UIDressDecoration:CalculateHeroProp()
    local values = {}
    local equipProCfgCache = {}
    local cfg_hero = game_scheme:Hero_0(HeroEntity.heroID)
    local decorateSkepID = HeroEntity and HeroEntity.numProp.decorateSkepID--饰品栏ID
    local decorateData = decorateSkepID and skep_mgr.GetGoodsSidOfSkep(decorateSkepID)
    if decorateData then
        for key,sid in pairs(decorateData) do
            local goodsEntity = player_mgr.GetPacketPartDataBySid(sid)
            if goodsEntity then
                -- 计算饰品属性
                local decorateCfg = decoration_mgr.GetDecorationCSVByID(goodsEntity:GetGoodsID())
                if decorateCfg then
                    --判断饰品阵营/职业/英雄是否和当前英雄数据匹配，匹配时才计算
                    local isPropDataActive = {}
                    isPropDataActive[1] = decorateCfg.condition1 and cfg_hero.type and decorateCfg.condition1 == cfg_hero.type
                    isPropDataActive[2] = decorateCfg.condition2 and cfg_hero.profession and decorateCfg.condition2 == cfg_hero.profession
                    isPropDataActive[3] = decorateCfg.condition3 and HeroEntity.heroID and decorateCfg.condition3 == HeroEntity.heroID
                    local partLv = HeroEntity and HeroEntity.numProp and HeroEntity.numProp["decoratePartLv"..decorateCfg.type] or 0--对应饰品部位的等级
                    local props = decorateCfg.propDatas
                    for i, propID in ipairs(props) do
                        if propID ~= nil and propID ~= 0 and isPropDataActive[i] then
                            --装备
                            if not equipProCfgCache[propID] then
                                equipProCfgCache[propID] = game_scheme:EquipmentPro_0(propID)
                            end
                            local cfg_equip = equipProCfgCache[propID]
                            if cfg_equip and cfg_equip.lvPro.data[partLv] ~= nil then
                                if not values[cfg_equip.proType] then values[cfg_equip.proType] = 0 end
                                values[cfg_equip.proType] = cfg_equip.lvPro.data[partLv] + values[cfg_equip.proType]
                                -- --print("计算英雄饰品属性heroSid",HeroSID,"heroEntity.heroID",HeroEntity.heroID,"饰品SID",sid,"饰品ID",goodsEntity:GetGoodsID(),"饰品类型/部位",decorateCfg.type,"饰品部位等级",partLv,"属性propID",propID,"属性类型proType",cfg_equip.proType,"属性值proValue",cfg_equip.lvPro.data[partLv],"最终累加属性值",values[cfg_equip.proType])
                            end
                        end
                    end
                end
            end
        end
    end
    --祝福大师的属性
    local enhanceMasterLevel,enhanceMasterCSV = decoration_mgr.GetEnhanceMasterLevelByDecorate(HeroSID,InputBlessData)
    local isActive = enhanceMasterLevel>0
    if isActive and enhanceMasterCSV then
        local propList = {enhanceMasterCSV.Para1, enhanceMasterCSV.Para2, enhanceMasterCSV.Para3, enhanceMasterCSV.Para4}
        for k, v in ipairs(propList) do
            if v and v.data[0] and v.data[1] then
                local propType = v.data[0]
                local propValue = v.data[1]
                if not values[propType] then values[propType] = 0 end
                values[propType] = values[propType] + propValue
                -- --print("祝福大师的属性enhanceMasterLevel",enhanceMasterLevel,"propType",propType,"propValue",propValue,"values[propType]",values[propType])
            end
        end
    end
    return values
end

--获取当前激活的饰品数据
function UIDressDecoration:GetActiveSkillData()
    --记录技能数据
    local skillId = nil
    local nextSkillLevel = nil
    if self.dataList then
        for i,v in ipairs(self.dataList) do
            local temp = {}
            temp.id = v.id
            temp.lv = v.lv--当前技能等级/最大技能等级
            temp.name = v.name
            temp.desc = v.desc
            temp.tip = v.tip
            temp.icon = v.icon
            temp.core = v.core
            temp.slotPos = v.slotPos--获取技能位置
            temp.isActive = v.isActive--技能等級4是否激活
            if temp.isActive then
                if not self.recordSkill then self.recordSkill = {} end
                self.recordSkill[v.id] = {isUnlock=true,lv=v.lv,id=v.id}
                skillId = v.id
                nextSkillLevel = v.lv
            end
        end
    end
    if skillId and nextSkillLevel then
        self.skillData = {}
        self.skillData["isUnlock"] = true
        self.skillData["lv"] = nextSkillLevel
        self.skillData["id"] = skillId
    end
end

--@endregion 

--@region WindowUpdateUI
--[[资源加载完成，被显示的时候调用]]
function UIDressDecoration:UpdateUIPage()
	self:BuildUpdateData()

    -- 清理高度缓存
    self.heights = nil
	self.scroll_table.data = self.dataList
    self.scroll_table:Refresh(0, -1)
    if self.ticker then
        util.RemoveDelayCall(self.ticker)
        self.ticker = nil
    end

    if self.setPower then
        self.setPower()
    end

    self.ticker = util.DelayCall(0.15,function()
        local height = 0
        for i=1,3 do
            if not window.heights then 
                window.heights = {} 
            end
            if not window.heights[i] then window.heights[i] = 98 end
            height = height+window.heights[i]
        end
        self.scroll_table_rect.sizeDelta = {x=self.scroll_table_rect.sizeDelta.x,y=height}
    end)
    self:InitHeroInfo()
    self.tipTxt.gameObject:SetActive(self.dataList and #self.dataList==0)
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowClose
function UIDressDecoration:Close()
    if self:IsValid() then
		self:UnsubscribeEvents()
        if self.scroll_table then
            self.scroll_table:ItemsDispose()
        end
        if self.cardSpriteAsset then
			self.cardSpriteAsset:Dispose()
			self.cardSpriteAsset = nil
		end
        HeroID = nil
        HeroSID = nil
        HeroEntity = nil
        DecorateData = nil
        QuickEquipDecorateData = nil
        DecorateCount = nil
        HeroCSV = nil
        takeOff = nil
        -- recordProp = {
        --     heroSid = 0,
        --     hp = 0,
        --     attack = 0,
        --     defence = 0,
        --     speed = 0,
        -- }
        recordProp={
            [5]=0,--暴击  5
            [6]=0,--爆伤  6
            [7]=0,--效果命中  7
            [8]=0,--效果抵抗  8
            [13]=0,--破甲  13
            [14]=0,--免伤  14
            [15]=0,--基础生命百分比 15
            [16]=0,--基础攻击百分比 16
            [17]=0,--护甲百分比  17
            [45]=0,--暴击抵抗  45
            [46]=0,--爆伤减免  46
            [49]=0,--生命  49
            [50]=0,-- 攻击  50
        }
        if self.flow_prop_item then
            self.flow_prop_item:Dispose()
            self.flow_prop_item = nil
        end
        if self.DelayFlowBattlePower then
            self.DelayFlowBattlePower = nil
        end
        local flow_fx_text = require "flow_fx_text"
        flow_fx_text.ClearWaiting()
        self.recordSkill = {}
        if self.skillDelayTimeID then
            util.RemoveDelayCall(self.skillDelayTimeID)
            self.skillDelayTimeID = nil
        end
        selectedIndex = nil
        InputDecorateData = nil--传入饰品数据
        InputBlessData = nil--传入祝福数据
        DecoPower = nil
        if self.ticker then
            util.RemoveDelayCall(self.ticker)
            self.ticker = nil
        end
        
        self.isTrialHero = nil
	end
	self.__base:Close()
    window = nil
--@region User
--@endregion 
end --///<<< function

--@endregion 
function UIDressDecoration:SetShowTips(showType,size,parent)
	ui_hero_type_tip.SetHeroType(showType,HeroID)
	local win = windowMgr:ShowModule("ui_hero_type_tip")	
    if win then
        win:SetSize(size)
        win:SetPivot2Bottom()
    end
	return ui_hero_type_tip
end

--@region WindowSubscribeEvents
--[[订阅UI事件]]
function UIDressDecoration:SubscribeEvents()
----///<<< Button Proxy Line >>>///-----
    self.ProfTypeBtnFun = function()
        ui_hero_type_tip.SetParent(self.profTypeBtn.gameObject.transform) 
        ui_hero_type_tip.SetOccupationTipsPos({x=-75,y=-15})
        self:SetShowTips(1,{x = 150,y = 106})
    end
    self.HeroTypeBtnFun = function()
		ui_hero_type_tip.SetParent(self.heroTypeBtn.gameObject.transform)
        ui_hero_type_tip.SetGroupTipsPos({x=0,y=-15})
        self:SetShowTips(2,{x = 500,y = 106})
    end
    self.widget_table["profTypeBtn"].event_name = "ProfTypeBtnFun"
    self.widget_table["heroTypeBtn"].event_name = "HeroTypeBtnFun"

    self.wearAllEvent = function()
        --TODO:一件穿戴（新饰品SID数据筛选）
        local decorateGroup = nil
        local newDecorateIDs = {}
        local oldDecorateIDs = {}
        if QuickEquipDecorateData then
            for i,goodsEntity in pairs(QuickEquipDecorateData) do
                if goodsEntity then
                    local decorateCfg = decoration_mgr.GetDecorationCSVByID(goodsEntity:GetGoodsID())
                    if not decorateCfg then
                        log.Error("未在Decoration表找到饰品ID",goodsEntity:GetGoodsID(),"goodsSid",goodsEntity.goodsSid)
                    end
                    local type = decorateCfg and decorateCfg.type
                    local oldEntity = DecorateData and DecorateData[type] and DecorateData[type]>0 and player_mgr.GetPacketPartDataBySid(DecorateData[type])
                    local oldDecorateSID = oldEntity and oldEntity:GetGoodsSid() or 0
                    local oldDecorateID = oldEntity and oldEntity:GetGoodsID() or 0
                    if type then
                        local temp = {
                            bTakeOff=false,--是否脱下（服务器不需要）
                            newDecorateSid = goodsEntity.goodsSid,--新饰品id(没有数据给0,下同)
                            oldDecorateSid = oldDecorateSID,--旧饰品id
                            pos=type--饰品位置（服务器不需要）
                        }
                        -- --print("一件穿戴！！！！！新饰品sid",goodsEntity.goodsSid,"新饰品ID",goodsEntity:GetGoodsID(),"旧饰品sid",oldDecorateSID,"旧饰品ID",oldDecorateID,"饰品类型type",type)
                        table.insert(oldDecorateIDs,oldDecorateID)
                        table.insert(newDecorateIDs,goodsEntity:GetGoodsID())
                        if not decorateGroup then decorateGroup = {} end
                        table.insert(decorateGroup,temp)
                    end
                end
            end
        end
        local oldDecorateIDString = ""
        if oldDecorateIDs and #oldDecorateIDs>0 then
            oldDecorateIDString = table.concat(oldDecorateIDs, '#')
        end
        local newDecorateIDString = ""
        if newDecorateIDs and #newDecorateIDs>0 then
            newDecorateIDString = table.concat(newDecorateIDs, '#')
        end
        if not QuickEquipDecorateData or #QuickEquipDecorateData<=0 then
            flow_text.Add(lang.Get(255207))
            --穿戴失败退出强制引导
            local force_guide_fun = require "force_guide_fun"
            force_guide_fun.SetDecorateGuideCount()
            force_guide_system.ComGuide()
            force_guide_system.Over()
            return
        end
        local condition1,condition2,condition3 = decoration_mgr.IsDecorateOpen(HeroEntity)
        if not condition3 then
            flow_text.Add(lang.Get(255241))
            --穿戴失败退出强制引导
            local force_guide_fun = require "force_guide_fun"
            force_guide_fun.SetDecorateGuideCount()
            force_guide_system.ComGuide()
            force_guide_system.Over()
            return
        end
        decoration_mgr.Request_ReplaceDecorate(HeroSID,decorateGroup,HeroID,oldDecorateIDString,newDecorateIDString)

    end
    self.widget_table["wearAll"].event_name = "wearAllEvent"

    self.undressAllEvent = function()
        --TODO:卸下所有饰品
        local decorateGroup = nil
        local newDecorateIDs = {}
        local oldDecorateIDs = {}
        if DecorateData then
            for key,sid in pairs(DecorateData) do
                local goodsEntity = player_mgr.GetPacketPartDataBySid(sid)
                if goodsEntity then
					local decorateCfg = decoration_mgr.GetDecorationCSVByID(goodsEntity:GetGoodsID())
					if not decorateCfg then
                        log.Error("未在Decoration表找到饰品ID",goodsEntity:GetGoodsID(),"sid",sid)
                    end
                    local type = decorateCfg and decorateCfg.type
                    if type then
                        local temp = {
                            bTakeOff=true,--是否脱下（服务器不需要）
                            newDecorateSid = 0,--新饰品id(没有数据给0,下同)
                            oldDecorateSid = sid,--旧饰品id
                            pos=type--饰品位置（服务器不需要）
                        }
                        table.insert(newDecorateIDs,0)
                        table.insert(oldDecorateIDs,goodsEntity:GetGoodsID())
                        if not decorateGroup then decorateGroup = {} end
                        table.insert(decorateGroup,temp)
                    end
                    -- --print("卸下所有饰品！！！！旧饰品sid",sid,"旧饰品ID",goodsEntity:GetGoodsID(),"饰品类型type",type)

                end
            end
        end
        local oldDecorateIDString = ""
        if oldDecorateIDs and #oldDecorateIDs>0 then
            oldDecorateIDString = table.concat(oldDecorateIDs, '#')
        end
        local newDecorateIDString = ""
        if newDecorateIDs and #newDecorateIDs>0 then
            newDecorateIDString = table.concat(newDecorateIDs, '#')
        end
        if HeroSID and decorateGroup then
            takeOff = true
        end
        decoration_mgr.Request_ReplaceDecorate(HeroSID,decorateGroup,HeroID,oldDecorateIDString,newDecorateIDString)
    end
    self.widget_table["undressAll"].event_name = "undressAllEvent"

    self.HeroNumProEvent = function(eventName,sid,enErrCode)
        if window and window.UIRoot then
            if HeroSID and player_mgr.GetPalPartDataBySid(HeroSID) then
                self:UpdateUIPage()
                if takeOff then
                    --flow_text.Add(lang.Get(255205))
                    takeOff = nil
                    self.recordSkill = {}--脱下所有饰品时需要清空
                end
            else
                log.Error("英雄信息错误！无HeroSID英雄！heroID=",HeroID,"heroSID",HeroSID)
            end
            if eventName == "replace_decorate_response" then
                if enErrCode and enErrCode~=0 then
                    --穿戴失败退出强制引导
                    local force_guide_fun = require "force_guide_fun"
                    force_guide_fun.SetDecorateGuideCount()
                    force_guide_system.ComGuide()
                    force_guide_system.Over()
                    return
                end
                force_guide_system.TriComEvent(force_guide_event.cOnWearReceived)
            end
        end
    end
    event.Register(event.HERO_UPDATE,self.HeroNumProEvent)
    event.Register(event.REPLACE_DECORATE_RESPONSE,self.HeroNumProEvent)

    self.updatePower = function(eventName,_sid)
        if not window or not self:IsValid() then
            return
        end
		if HeroEntity and HeroEntity.heroSid == _sid and HeroEntity.battleProp then
			self:UpdatePower(util.Float2Int(HeroEntity.battleProp.power),_sid)
		end
	end
	event.Register(event.UPDATE_HERO_BATTLE_PROP,self.updatePower)--战斗属性变化

    self.LeftArrowEvent = function ()
        if windowMgr:IsModuleShown("net_reloading") then return end
        if selectedIndex > 1 then
            selectedIndex = selectedIndex - 1
            local nextSid = self.listData[selectedIndex].heroSid
            local heroID = self.listData[selectedIndex].heroID
            local heroEntity = player_mgr.GetPalPartDataBySid(nextSid)
            event.Trigger(event.CLICK_HERO_ITEM, nextSid)
            self:SetInputParam(nextSid,heroEntity,EnterType.bag)
            local baseObj = windowMgr:GetWindowObj("ui_decoration_base")
            if baseObj then
                baseObj:SetInputParam(nextSid,heroEntity,EnterType.bag)
            end

            local win = windowMgr:GetWindowObj("ui_decoration_node")
            if win then
                win:SetInputParam(nextSid,heroEntity,EnterType.bag,true)
            end
            if heroID then
                local music_contorller   = require "music_contorller"
                music_contorller.PlayHeroAudioOfHero(heroID, music_contorller.ENUM_HEROVOICE_TYPE.other)
            end
            self:UpdateArrowUI()
            self:ResetPropTable()
            self:UpdateBtnStateOfTrial()
        end
    end
    self.widget_table["leftArrow"].event_name = "LeftArrowEvent"

    self.RightArrowEvent = function ()
        if windowMgr:IsModuleShown("net_reloading") then return end
        self.listDataCount = self.listDataCount or #self.listData
        if selectedIndex < self.listDataCount then
            selectedIndex = selectedIndex + 1
            local nextSid = self.listData[selectedIndex].heroSid
            local heroID = self.listData[selectedIndex].heroID
            local heroEntity = player_mgr.GetPalPartDataBySid(nextSid)
            event.Trigger(event.CLICK_HERO_ITEM, nextSid)
            self:SetInputParam(nextSid,heroEntity,EnterType.bag)

            local baseObj = windowMgr:GetWindowObj("ui_decoration_base")
            if baseObj then
                baseObj:SetInputParam(nextSid,heroEntity,EnterType.bag)
            end

            local win = windowMgr:GetWindowObj("ui_decoration_node")
            if win then
                win:SetInputParam(nextSid,heroEntity,EnterType.bag,true)
            end
            if heroID then
                local music_contorller   = require "music_contorller"
                music_contorller.PlayHeroAudioOfHero(heroID, music_contorller.ENUM_HEROVOICE_TYPE.other)
            end
            self:UpdateArrowUI()
            self:ResetPropTable()
            self:UpdateBtnStateOfTrial()
        end
    end
    self.widget_table["rightArrow"].event_name = "RightArrowEvent"
end --///<<< function

--@endregion 

function UIDressDecoration:ResetPropTable()
        recordProp={
        [5]=0,--暴击  5
        [6]=0,--爆伤  6
        [7]=0,--效果命中  7
        [8]=0,--效果抵抗  8
        [13]=0,--破甲  13
        [14]=0,--免伤  14
        [15]=0,--基础生命百分比 15
        [16]=0,--基础攻击百分比 16
        [17]=0,--护甲百分比  17
        [45]=0,--暴击抵抗  45
        [46]=0,--爆伤减免  46
        [49]=0,--生命  49
        [50]=0,-- 攻击  50
    }
    local values = self:CalculateHeroProp()
    for propID,propValue in pairs(values) do
        if propID~=0 and propValue and propValue>0 then
            recordProp[tonumber(propID)] = propValue
        end
    end
end

function UIDressDecoration:UpdatePower(battlePower,heroSid)
    if enterType == EnterType.otherPlayer then return end
	self.flowTextBattlePowerData.targetPower = battlePower
	if self.DelayFlowBattlePower == nil then
		self.DelayFlowBattlePower = function ()
			if window and window:IsValid() then
				local flowData = self.flowTextBattlePowerData
				if HeroEntity == nil then
					return
				end

                if flowData.heroSid == HeroEntity.heroSid then
                    -- 同一英雄的玩家行为
                    if flowData.prePower == flowData.targetPower then
                        return
                    end
                end

				local addPower = flowData.targetPower - flowData.prePower
				flowData.prePower = flowData.targetPower
                local flow_fx_text = require "flow_fx_text"
                flow_fx_text.AddNumber(addPower,"FlowFxText", 1.65)       
                local faker = calpro_mgr.CreateFakerHeroEntity(HeroEntity,HeroEntity.numProp.starLv,HeroEntity.numProp.stepLV,HeroEntity.numProp.lv,nil,nil,nil,nil,true)
                local _power = calpro_mgr.CalculatePowerByEntity(faker)
                self.power.text = util.Float2Int(HeroEntity.battleProp.power-_power)
            end
		end
	end
	--激活饰品技能4界面打开时，不显示战力提升瓢字
	if not windowMgr:IsModuleShown("ui_hero_skill_tips") then 
		self.DelayFlowBattlePower()
	end
    --属性飘字
    local isActiveSkill = false
    if heroSid == HeroEntity.heroSid then
        
        local values = self:CalculateHeroProp()
        local needFlowProp = false
        local finalStr = ""
        for propID,propValue in pairs(recordProp) do
            if propID~=0 then
                if not values[tonumber(propID)] then
                    values[tonumber(propID)] = 0
                end
                if values and (values[tonumber(propID)] and values[tonumber(propID)]>=0 and values[tonumber(propID)]~=propValue) then
                    -- --print("propID",propID,"propValue",propValue,"values[tonumber(propID)]",values[tonumber(propID)])
                    needFlowProp = true
                    local csv = decoration_mgr.GetHeroBattlePropDesc(tonumber(propID))
                   local desc = lang.Get(csv.iLangId)
                    propValue = values[tonumber(propID)]-propValue
                    local str = SetTextColor(propValue, csv)
                    finalStr = string.format("%s%s",finalStr,str)
                else
                    --log.Error("propID",propID,"propValue",propValue,"values",values,"values[tonumber(propID)]",values[tonumber(propID)])
                end
            end
        end
        if needFlowProp then
            --激活饰品技能4界面打开时，不显示属性瓢字
            if not windowMgr:IsModuleShown("ui_hero_skill_tips") then
                self.flow_prop_item = self.flow_prop_item or flow_prop_item.CPropItem():Init(self.flowProp, finalStr)
            end
        end
        for propID,propValue in pairs(recordProp) do
            if propID~=0 then
                recordProp[tonumber(propID)] = values[tonumber(propID)] or 0
            end
        end
    end
    --打开激活新饰品技能
    if self.skillDelayTimeID then
        util.RemoveDelayCall(self.skillDelayTimeID)
        self.skillDelayTimeID = nil
    end
    local skillId = nil
    local nextSkillLevel = nil
    if self.dataList then
        for i,v in ipairs(self.dataList) do
            if v.isActive and self.recordSkill and not self.recordSkill[v.id] then
                isActiveSkill = true
                skillId = v.id
                nextSkillLevel = v.lv--当前技能等级
                self.recordSkill[v.id] = {isUnlock=true,lv=v.lv,id=v.id}
            elseif not v.isActive and self.recordSkill and self.recordSkill[v.id] then
                self.recordSkill[v.id] = nil
            end
        end
    end
    if skillId and nextSkillLevel then
        self.skillData = {}
        self.skillData["isUnlock"] = nextSkillLevel-1==0
        self.skillData["lv"] = nextSkillLevel
        self.skillData["id"] = skillId
    end
    if isActiveSkill then
        self.skillDelayTimeID = util.DelayCallOnce(1.15, function()
            if self:IsValid() == false then
                return
            end
            local win = windowMgr:ShowModule("ui_hero_skill_tips")
            if win then
                win:SetInputParam(self.skillData)
            end
        end)
    end
    
end

function SetTextColor(num,csv)
	local color = "#FFFFFF"
	local templet = ""
	if num > 1 then
		color = "#77D661"                  
		templet = "<color=%s>%s + %s</color>\n"
	elseif num <0 then
		color = "#F24B4B"
		templet = "<color=%s>%s %s</color>\n"
	end
    local desc = lang.Get(csv.iLangId)
    num = csv.isPercentage==1 and string.format("%s%%",num/100) or num
	local textex = string.format(templet, color, desc,num)
	return textex
end

--@region WindowUnsubscribeEvents
--[[退订UI事件]]
function UIDressDecoration:UnsubscribeEvents()
    event.Unregister(event.HERO_UPDATE,self.HeroNumProEvent)
    event.Unregister(event.REPLACE_DECORATE_RESPONSE,self.HeroNumProEvent)
	event.Unregister(event.UPDATE_HERO_BATTLE_PROP,self.updatePower)--战斗属性变化
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowBtnFunctions
--@endregion 

--@region ScrollItem
--@endregion 

--@region WindowInherited
local CUIDressDecoration = class(ui_base, nil, UIDressDecoration)
--@endregion 

local oParent
function Create(objParent)
    oParent = objParent
end

--@region ModuleFunction
function Show()
    if window == nil then
        window = CUIDressDecoration()
        window._NAME = _NAME
        window:LoadUIResource("ui/prefabs/uidressdecoration.prefab", nil, oParent)
    end
    window:Show()
    return window
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end

function Close()
    if window ~= nil then
        Hide()
        window:Close()
        window = nil
    end
end

