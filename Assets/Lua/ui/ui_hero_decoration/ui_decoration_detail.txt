--@region FileHead
-- ui_decoration_detail.txt ---------------------------------
-- author:  曾琳琳
-- date:    2022/1/8 17:21:32
-- ver:     1.0
-- desc:    饰品详情界面Description
-------------------------------------------------
--@endregion 

--@region Require
local print     = print
local require   = require
local pairs     = pairs
local ipairs    = ipairs
local string    = string
local table     = table
local tostring = tostring

local Button        = CS.UnityEngine.UI.Button
local Text          = CS.UnityEngine.UI.Text
local Image         = CS.UnityEngine.UI.Image
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable
local SpriteSwitcher = CS.War.UI.SpriteSwitcher
local VerticalLayoutGroup = CS.UnityEngine.UI.VerticalLayoutGroup
local Gradient      = CS.War.UI.Gradient
local Shadow       = CS.UnityEngine.UI.Shadow
local ContentSizeFitter 	= CS.UnityEngine.UI.ContentSizeFitter

local event					= require "event"
local class                 = require "class"
local ui_base               = require "ui_base"
local log                   = require "log"
local decoration_mgr        = require "decoration_mgr"
local decoration_item       = require "decoration_item"
local lang                  = require "lang"
local hero_mgr              = require "hero_mgr"
local card_sprite_asset     = require "card_sprite_asset"
-- local tavern_xingji         = require "tavern_xingji"
local skill_mgr             = require "skill_mgr"
local windowMgr 	        = require "ui_window_mgr"
local game_scheme  	        = require "game_scheme"
local player_mgr            = require "player_mgr"
local const                 = require "const"
local archive_data          = require "archive_data"
local util                  = require "util"
--@endregion 

--@region ModuleDeclare
module("ui_decoration_detail")
--local interface = require "iui_decoration_detail"
local window = nil
local UIDecorationDetail = {}
local itemId = nil--饰品id
local itemSid = nil--饰品序列号
local HeroSID = nil--英雄SID
local HeroID = nil--英雄ID
local HeroEntity = nil
local BlessLv = nil--对应饰品槽位等级
local EnterType = const.EnterType
local enterType = EnterType.heroBag
local blueColor = {r=38/255,g=102/255,b=139/255,a=1}
local grayColor = {r=162/255,g=162/255,b=162/255,a=1}
local colorEnum = {
	yellow = "<color=#FDFF7BFF>%s</color>",--技能名称验收
	gray = "<color=#A2A2A2FF>%s</color>",--置灰颜色
	blue = "<color=#26668BFF>%s</color>",--技能描述颜色
    white = "<color=#FBF9F9FF>%s</color>",--技能等级未激活颜色
    gold = "<color=#FFEA13FF>%s</color>",--技能等级激活颜色
    gold2 = "<color=#FFF000FF>%s</color>",--技能等级激活颜色
}
local topRectH = 40
local outLines = {
    {r=33/255,g=55/255,b=100/255,a=1},
    {r=33/255,g=55/255,b=100/255,a=1},
    {r=144/255,g=71/255,b=0/255,a=1}
 }
local InputDecorateData = nil
--@endregion 

--@region WidgetTable
UIDecorationDetail.widget_table = {
    Btn_closeBtn = { path = "Auto_closeBtn", type = "Button", event_name = "OnBtn_closeBtnClickedProxy" },
    Btn_shareBtn = { path = "Auto_Background/Auto_NameRoot/Auto_shareBtn", type = "Button", event_name = "OnBtn_shareBtnClickedProxy" },
    lockTog = { path = "Auto_Background/Background/Auto_IconRoot/lockTog",type = "Toggle", value_changed_event = "OnTog_lockClickedProxy" },
    Btn_undressBtn = { path = "Auto_Background/Auto_buttonRoot/Auto_undressBtn", type = "Button", event_name = "OnBtn_undressBtnClickedProxy" },
    Btn_upgradeBtn = { path = "Auto_Background/Auto_buttonRoot/Auto_upgradeBtn", type = "Button", event_name = "OnBtn_upgradeBtnClickedProxy" },
    upgradeRed = { path = "Auto_Background/Auto_buttonRoot/Auto_upgradeBtn/red", type = "Image", event_name = "" },
    Btn_replaceBtn = { path = "Auto_Background/Auto_buttonRoot/Auto_replaceBtn", type = "Button", event_name = "OnBtn_replaceBtnClickedProxy" },
    Text_Name = { path = "Auto_Background/Auto_NameRoot/Auto_Name", type = "Text", event_name = "" },
    -- Gradient_Name = { path = "Auto_Background/Auto_NameRoot/Auto_Name", type = Gradient, event_name = "" },
    -- Shadow_Name = { path = "Auto_Background/Auto_NameRoot/Auto_Name", type = Shadow, event_name = "" },
    Text_type = { path = "Auto_Background/Background/Auto_IconRoot/Auto_type", type = "Text", event_name = "" },
    Text_grade = { path = "Auto_Background/Background/Auto_IconRoot/Auto_grade", type = "Text", event_name = "" },
    Text_level = { path = "Auto_Background/Background/Auto_IconRoot/Auto_level", type = "Text", event_name = "" },
    campTxt = { path = "Auto_Background/Background/scrollrect/Viewport/Content/Auto_proRoot/content/Auto_camp/Auto_Text", type = "Text", event_name = "" },
    professionTxt = { path = "Auto_Background/Background/scrollrect/Viewport/Content/Auto_proRoot/content/Auto_profession/Auto_Text", type = "Text", event_name = "" },
    heroTxt = { path = "Auto_Background/Background/scrollrect/Viewport/Content/Auto_proRoot/content/Auto_hero/Auto_Text", type = "Text", event_name = "" },
    --Text_text = { path = "Auto_Background/Auto_buttonRoot/Auto_replaceBtn/Auto_text", type = "Text", event_name = "" },
    Rect_Background = { path = "Auto_Background", type = VerticalLayoutGroup, event_name = "" },--背景高度调整
    Img_Background1 = { path = "Auto_Background", type = SpriteSwitcher, event_name = "" },--切换背景T0/T1
    Img_Background2 = { path = "Auto_Background/Background", type = SpriteSwitcher, event_name = "" },--切换背景T0/T1
    --Img_proRoot = { path = "Auto_Background/Background/scrollrect/Viewport/Content/Auto_proRoot", type = "Image", event_name = "" },
    Rect_camp = { path = "Auto_Background/Background/scrollrect/Viewport/Content/Auto_proRoot/content/Auto_camp", type = "RectTransform", event_name = "" },
    Img_camp = { path = "Auto_Background/Background/scrollrect/Viewport/Content/Auto_proRoot/content/Auto_camp/camp", type = "Image", event_name = "" },
    campSS = { path = "Auto_Background/Background/scrollrect/Viewport/Content/Auto_proRoot/content/Auto_camp/camp", type = SpriteSwitcher, event_name = "" },
    Rect_profession = { path = "Auto_Background/Background/scrollrect/Viewport/Content/Auto_proRoot/content/Auto_profession", type = "RectTransform", event_name = "" },
    Img_profession = { path = "Auto_Background/Background/scrollrect/Viewport/Content/Auto_proRoot/content/Auto_profession/profession", type = "Image", event_name = "" },
    professionSS = { path = "Auto_Background/Background/scrollrect/Viewport/Content/Auto_proRoot/content/Auto_profession/profession", type = SpriteSwitcher, event_name = "" },
    Rtsf_NameRoot = { path = "Auto_Background/Auto_NameRoot", type = "RectTransform", event_name = "" },
    Rtsf_IconRoot = { path = "Auto_Background/Background/Auto_IconRoot/icon", type = "RectTransform", event_name = "" },
    Rtsf_hero = { path = "Auto_Background/Background/scrollrect/Viewport/Content/Auto_proRoot/content/Auto_hero", type = "RectTransform", event_name = "" },
    Rtsf_buttonRoot = { path = "Auto_Background/Auto_buttonRoot", type = "RectTransform", event_name = "" },
    Img_hero = { path = "Auto_Background/Background/scrollrect/Viewport/Content/Auto_proRoot/content/Auto_hero/Mask/Image", type = "Image", event_name = "" },

    backgroundVertical = { path = "Auto_Background/Background", type = VerticalLayoutGroup, event_name = "" },--切换背景T0/T1

    
    scrollrect = { path = "Auto_Background/Background/scrollrect", type = "RectTransform", event_name = "" },--设置高度
    content_rect = { path = "Auto_Background/Background/scrollrect/Viewport/Content", type = "RectTransform", event_name = "" },--设置高度
    Rtsf_skillRoot = { path = "Auto_Background/Background/scrollrect/Viewport/Content/Auto_skillRoot", type = "RectTransform", event_name = "" },
    scroll_table = { path = "Auto_Background/Background/scrollrect/Viewport/Content/Auto_skillRoot/Viewport/Content", type = ScrollRectTable, event_name = "" },
    scroll_table_rect = { path = "Auto_Background/Background/scrollrect/Viewport/Content/Auto_skillRoot/Viewport/Content", type = VerticalLayoutGroup, event_name = "" },
    contentSize = { path = "Auto_Background/Background/scrollrect/Viewport/Content/Auto_skillRoot/Viewport/Content", type = ContentSizeFitter, event_name = "" },--获取技能栏内容高度
    Rect_skillContent = { path = "Auto_Background/Background/scrollrect/Viewport/Content/Auto_skillRoot/Viewport/Content", type = "RectTransform", event_name = "" },--获取技能栏内容高度
    Rect_proRoot = { path = "Auto_Background/Background/scrollrect/Viewport/Content/Auto_proRoot", type = "RectTransform", event_name = "" },--获取属性根节点高度

    Rect_tipsRoot = { path = "Auto_Background/Background/scrollrect/Viewport/Content/Auto_tipsRoot", type = "RectTransform", event_name = "" },
    skillDesc ={path="Auto_Background/Background/scrollrect/Viewport/Content/Auto_skillRoot/Viewport/Content/ListItem/content/skillDesc",type="Text",fitArabic=true},
--@region User
--@endregion 
}
function UIDecorationDetail:FixMultiLang(fixMultiLangParam)
    if self:IsArabicEnvironment() then
        local util=require "util"
        local items={}
        table.insert(items,self.Rect_camp)
        table.insert(items,self.Rect_profession)
        table.insert(items,self.Rtsf_hero)
        for key,value in ipairs(items) do
            local text=value:GetChild(1)
            local icon=value:GetChild(0)
            if text then
                local textRT=text:GetComponent("RectTransform")
                if textRT then
                    textRT.anchoredPosition={x=250,y=-15.5}
                end
                local textComponent=text:GetComponent("Text")
                if textComponent then
                    textComponent.alignment=fixMultiLangParam.TextAnchor.MiddleRight
                end
            end
            if icon then
                local iconRT=icon:GetComponent("RectTransform")
                if iconRT then
                    iconRT.anchoredPosition={x=511,y=-26}
                end
            end
        end
    end
end
--@endregion 

--@region WindowCtor
function UIDecorationDetail:ctor(selfType)
	self.__base:ctor(selfType)
    self.spriteAsset = self.spriteAsset or card_sprite_asset.CreateHeroAsset()
	self.cardSpriteAsset = card_sprite_asset.CreateSpriteAsset()
--@region User
--@endregion 
end --///<<< function

--@endregion 
function OnRenderItem(scroll_rect_item,index,dataItem)
	scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem

    local icon = scroll_rect_item:Get("icon")
    local skillLv = scroll_rect_item:Get("skillLv")
    local skillName = scroll_rect_item:Get("skillName")
    local skillDesc = scroll_rect_item:Get("skillDesc")
    local tip = scroll_rect_item:Get("tip")
    local coreSkill = scroll_rect_item:Get("coreSkill")
    local cn = scroll_rect_item:Get("cn")
    local en = scroll_rect_item:Get("en")
    local iconGray = scroll_rect_item:Get("iconGray")
    local topRect = scroll_rect_item:Get("top")
    local contentSize = scroll_rect_item:Get("contentSize")
    local skillImgSS = scroll_rect_item:Get("skillImgSS")
    local skillImg = scroll_rect_item:Get("skillImg")
    local WarpText = scroll_rect_item:Get("WarpText")
    local skillLvOutline = scroll_rect_item:Get("skillLvOutline")
    local rect = scroll_rect_item:Get("rect")
    local contentRect = scroll_rect_item:Get("contentRect")
    local skillDescRect = scroll_rect_item:Get("skillDescRect")

    skillLvOutline.effectColor = outLines[index]
    WarpText:SetWarpText(string.format(not dataItem.isGray and colorEnum.blue or colorEnum.gray,dataItem.desc))
    skillImgSS:Switch(index==3 and 1 or 0)
    skillImg:SetNativeSize()
    scroll_rect_item.gameObject:SetActive(true)
    window.cardSpriteAsset:GetSprite(dataItem.icon, function(sprite)
		icon.sprite = sprite
	end)
    --TODO技能激活：当装备的专属技能和英雄不匹配时，相关描述颜色应该为灰色，只针对圣物界面的背包打开圣物弹窗
    skillLv.text = string.format(not dataItem.isGray and (index==3 and colorEnum.gold2 or colorEnum.gold) or colorEnum.white,dataItem.lv)
    skillName.text = string.format(not dataItem.isGray and colorEnum.yellow or colorEnum.gray,dataItem.name)--string.format("%s %s",dataItem.name,dataItem.lv)
    --skillDesc.text = string.format(not dataItem.isGray and colorEnum.blue or colorEnum.gray,dataItem.desc)
    tip.text = string.format(not dataItem.isGray and colorEnum.blue or colorEnum.gray,dataItem.tip)
    coreSkill.gameObject:SetActive(dataItem.core==1)
    cn.gameObject:SetActive(lang.USE_LANG == "zh")
    en.gameObject:SetActive(lang.USE_LANG ~= "zh")
    iconGray:SetEnable(dataItem.isGray)
    --TODO技能框自适应
    local height1 = skillName.preferredHeight
    local height2 = tip.preferredHeight
    -- if  height1 and height2 then
    --     if height1 >= height2 then
    --         topRectH =  height1
    --     elseif  height1 < height2 then
    --         topRectH =  height2
    --     end
    -- end
    if height2 > topRect.sizeDelta.y then
        topRectH = height2
    else
        topRectH = topRect.sizeDelta.y
    end
    -- topRect.sizeDelta = {x=topRect.sizeDelta.x,y=topRectH}
	contentSize.verticalFit = 0
	contentSize.verticalFit = 2
    --设置每个Item的位置
    local itemHeight = 120
    scroll_rect_item.transform.anchoredPosition = {x=scroll_rect_item.transform.anchoredPosition.x,y=itemHeight}

    if not window.heights then window.heights = {} end
    window.heights[index] = rect.sizeDelta.y
    -- topRect.sizeDelta = {x=topRect.sizeDelta.x,y=topRectH}
    skillDescRect.localPosition = {x=skillDescRect.localPosition.x,y=-topRectH-12}
    --lineRect.localPosition = {x=lineRect.localPosition.x,y=-(contentRect.sizeDelta.y+12)}
    contentRect.sizeDelta = {x=contentRect.sizeDelta.x,y=skillDesc.preferredHeight+topRectH+12}
    local y = skillDesc.preferredHeight+topRectH+20+24--20是底部线的宽度，24是内容和底部线之间的间隔
    if rect.sizeDelta.y<y then
        window.heights[index] = y
    end
    local rectHeight = 0
    if index>=2 then
        for i=1,index-1 do
            if window.heights[i] then
                rectHeight = rectHeight+window.heights[i]
            end
        end
    end
    -- --print("index",index,"y",y,"window.heights[index]",window.heights[index],"rectHeight",rectHeight)
    rect.localPosition = {x=rect.localPosition.x,y=-rectHeight}
    rect.sizeDelta = {x=rect.sizeDelta.x,y=y}
    contentRect.localPosition = {x=contentRect.localPosition.x,y=-12}

    local func = {}
    local openSkillBtn = function ()
        --TODO:打开技能面板
        --windowMgr:ShowModule("ui_hero_skill")
        --local ui_hero_skill = require "ui_hero_skill"
        --ui_hero_skill.SetHeroSkill(HeroSID,nil,2,dataItem.lv,dataItem.id,dataItem.slotPos,HeroEntity.numProp.starLv,HeroEntity.heroID,dataItem.lv)
    end
    func["openSkillBtn"] = openSkillBtn

    scroll_rect_item.InvokeFunc = function(funcname)
		func[funcname]()
    end
end

--@region WindowInit
--[[窗口初始化]]
function UIDecorationDetail:Init()
    self.contentSize.verticalFit = 0
    self.scroll_table.onItemRender= OnRenderItem
	self.scroll_table.onItemDispose=function(scroll_rect_item,index) end
    self:SubscribeEvents()
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function UIDecorationDetail:OnShow() 
    self.__base:OnShow()
    self:UpdateUIPage()
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowOnHide
--[[界面隐藏时调用]]
function UIDecorationDetail:OnHide()
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowSetInputParam
--[[设置窗口的输入参数。该参数通常是由其它模块或者外部设置进来。需要注意的是，
当调用这个函数的时候，窗口资源可能还是没有加载完成的。
@param p 参数表
]]
function UIDecorationDetail:SetInputParam(sid, id, herosid,_heroEntity,lv,_enterType,inputDecorateData)
    itemSid = sid--饰品序列号
    itemId = id--饰品id
    local goodsEntity = player_mgr.GetPacketPartDataBySid(itemSid)
    HeroSID = herosid --or(goodsEntity and goodsEntity:GetDecorateHeroSID())--英雄SID
    HeroEntity = _heroEntity or player_mgr.GetPalPartDataBySid(HeroSID)--英雄实体(红色饰品精炼需要用到)
    HeroID = HeroEntity and HeroEntity.heroID
    if lv then
        BlessLv = lv--对应槽位等级
    end
    enterType = _enterType or EnterType.heroBag
    InputDecorateData = inputDecorateData--传入饰品数据
    -- --print("itemSid",itemSid,"itemId",itemId,"HeroSID",HeroSID,"HeroID",HeroID)

    --如果正在显示，则更新一次窗口
    if self.UIRoot and self.UIRoot.activeSelf == true then
        self:UpdateUIPage()
    end
	
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowBuildUpdateData
--[[构建UI更新数据]]
function UIDecorationDetail:BuildUpdateData()
    self.heroCSV = self.heroCSV or archive_data.GetDatasByHeroID(HeroID)
    local data = decoration_mgr.GetDecorationCSVByID(itemId)
    --TODO
    local goodsEntity = itemSid and player_mgr.GetPacketPartDataBySid(itemSid)
    if goodsEntity then
        data.isLock = goodsEntity:GetDecorateLock() --锁住状态（服务器）
    end
    if not data then  
        log.Error("itemId",itemId,"itemSid",itemSid,"data",data)
        return 
    end
    data.goodsEntity = goodsEntity
    data.blessLv = BlessLv or (HeroEntity and HeroEntity.numProp and HeroEntity.numProp["decoratePartLv"..data.type] or 0) or 0--该饰品对应的祝福等级
    --属性1的描述
    if data.pro1 then
        data.baseResult1= decoration_mgr.GetEquipPropDesc(data.pro1,0)--属性1初始数值（祝福等级为0）
        data.result1 = decoration_mgr.GetEquipPropDesc(data.pro1,data.blessLv)--属性1名称和数值（祝福等级为lv）
        if data.result1 and data.baseResult1 then
            local changeNum = data.result1.value-data.baseResult1.value
            local percentage = data.result1.percentage and "%" or "" 
            local s = string.format("%s%s",changeNum,percentage)
            data.proValue1 = changeNum>0 and string.format("%s【%s+%s】",data.baseResult1.num,lang.Get(5111),s) or data.baseResult1.num
            if lang.USE_LANG and lang.USE_LANG==lang.AR then
                data.result1.desc = " " .. data.result1.desc 
            end
            -- data.proTxt1 = string.format(lang.Get(255051),tavern_xingji.GetName(4, data.condition1),data.result1.desc,data.proValue1)
        end
    end
    --属性2的描述
    if data.pro2 and data.pro2>0 then
        local decoCfg = decoration_mgr.GetallDecorationCSV2ByParam(data.type,0,data.condition3)--T0的饰品
        data.baseResult2= decoration_mgr.GetEquipPropDesc(decoCfg.pro2,0)--T0饰品的属性2初始数值
        data.result2= decoration_mgr.GetEquipPropDesc(data.pro2,0)--属性2初始数值
        if data.result2 and data.baseResult2 then
            local changeNum = data.result2.value-data.baseResult2.value
            local percentage = data.result2.percentage and "%" or "" 
            local s = string.format("%s%s",changeNum,percentage)
            data.proValue2 = changeNum>0 and string.format("%s【%s+%s】",data.baseResult2.num,lang.Get(255048),s) or data.baseResult2.num
            if lang.USE_LANG and lang.USE_LANG==lang.AR then
                data.result2.desc = " " .. data.result2.desc 
            end
            -- data.proTxt2 = string.format(lang.Get(255051),tavern_xingji.GetName(2, data.condition2), data.result2.desc, data.proValue2)
        end
    end
    --属性3的描述
    if data.pro3 and data.pro3>0 then
        local decoCfg = decoration_mgr.GetallDecorationCSV2ByParam(data.type,0,data.condition3)--T0的饰品
        data.baseResult3= decoration_mgr.GetEquipPropDesc(decoCfg.pro3,0)--T0饰品的属性2初始数值
        data.result3 = decoration_mgr.GetEquipPropDesc(data.pro3,0)--属性2名称和数值（祝福等级为lv）
        if data.result3 and data.baseResult3 then
            local changeNum = data.result3.value-data.baseResult3.value
            local percentage = data.result3.percentage and "%" or "" 
            local s = string.format("%s%s",changeNum,percentage)
            data.proValue3 = changeNum>0 and string.format("%s【%s+%s】",data.baseResult3.num,lang.Get(255048),s) or data.baseResult3.num
            local heroCfg = archive_data.GetDatasByHeroID(data.condition3)
            data.heroName = heroCfg and heroCfg.nameID and lang.Get(heroCfg.nameID) or "未配置"
            if lang.USE_LANG and lang.USE_LANG==lang.AR then
                data.heroName = data.heroName .." "
            end
            data.proTxt3 = string.format(lang.Get(255051),data.heroName, data.result3.desc, data.proValue3)
            if heroCfg then
                data.starsLv = heroCfg.starLv
            end
        end
    end
    --技能数据
	local skills = HeroID and skill_mgr.GetAllSkillIDs(HeroID)
    if data.condition3 and data.condition3>0 then
        local skillDatas = decoration_mgr.GetHeroDecorationsSkill(data.condition3)
        local decoCount,decoCount1,decoCount2 = decoration_mgr.GetDecorationCountByHero(HeroSID)
        if enterType == EnterType.otherPlayer then
            decoCount,decoCount1,decoCount2 = decoration_mgr.GetDecorationCountByHeroID(HeroID,InputDecorateData)
        end
        data.skillData = {}
        if skillDatas then
            for i,v in pairs(skillDatas) do
                local temp = {}
                temp.id = v.id
                temp.lv = v.lv--当前技能等级/最大技能等级
                temp.core = v.core--核心技
                temp.needDecorate = v.needDecorate--技能lv激活需要的饰品件数
                local cfg_heroskill = game_scheme:HeroSkill_0(temp.id, v.lv)
                if cfg_heroskill then
                    temp.name = lang.Get(cfg_heroskill.nameID)
                    temp.desc = lang.Get(cfg_heroskill.descID)
                    temp.tip = v.index==3 and string.format(lang.Get(255242),v.needDecorate) or string.format(lang.Get(255037),v.needDecorate)
                    temp.icon = cfg_heroskill.icon
                    temp.core = 0
                    temp.slotPos = skill_mgr.GetSkillPosByID(v.id,HeroID)--获取技能位置
                    if v.index==3 then
                        temp.isActive = v.needDecorate<=decoCount2
                    else
                        temp.isActive = v.needDecorate<=decoCount--TODO:技能是否激活（判断每个英雄身上是否穿戴对应数量专属饰品）
                    end
                    --TODO技能是否置灰（技能激活：当装备的专属技能和英雄不匹配时，相关描述颜色应该为灰色，只针对圣物界面的背包打开圣物弹窗）
                    temp.specialSkill = skills and skills[v.id] and skills[v.id] == v.id--当前英雄穿戴的专属饰品
                    if enterType == EnterType.heroBag or enterType == EnterType.otherPlayer then
                        temp.isGray = (not temp.specialSkill) or (not temp.isActive)
                    else
                        temp.isGray = false
                    end
                    temp.index = v.index
                    -- --print("v.index",v.index,"v.id",v.id,"v.needDecorate",v.needDecorate,"temp.isActive",temp.isActive,"skills[v.id]",skills and skills[v.id],"temp.specialSkill",temp.specialSkill,"enterType",enterType,"EnterType.heroBag",EnterType.heroBag)
                    table.insert(data.skillData,temp)
                else
                    log.Error("读取活动HeroSkill error，请检查配置！id=",temp.id,"lv",temp.lv)
                end
            end
            table.sort(data.skillData,function(a,b)
                if a.index and b.index then
                    return a.index<b.index
                end
            end)
        end
    end

    self.data = data
end --///<<< function

--@endregion 

--@region WindowUpdateUI
--[[资源加载完成，被显示的时候调用]]
function UIDecorationDetail:UpdateUIPage()
    local goodsEntity = player_mgr.GetPacketPartDataBySid(itemSid)
    local decorateHeroSID = (goodsEntity and goodsEntity:GetDecorateHeroSID())--英雄SID
    if not HeroEntity then
        HeroEntity = player_mgr.GetPalPartDataBySid(decorateHeroSID)--英雄实体(红色饰品精炼需要用到)
        HeroID = HeroEntity and HeroEntity.heroID
    end
    if not self.heroCSV then
        self.heroCSV =  archive_data.GetDatasByHeroID(HeroID)
    end
	self:BuildUpdateData()
    if not self.data then
        log.Error("没有查找到itemId数据",itemId)
        return
    end
    self.iconUI = self.iconUI or decoration_item.CDecorationItem():Init(self.Rtsf_IconRoot.transform)
    self.iconUI:SetGoods(self.data.goodsEntity, itemId, 1, function()
    end)   
    if self.data.type then    
        local str="%s%s"
        if lang.USE_LANG == lang.AR then
            str="%s %s"
        end
        self.Text_type.text = string.format(str,lang.Get(255043),lang.Get(255000+self.data.type))--类型（9个部位）
    end
    if self.data.colourType then
        local nameColor = "<color=#"..decoration_mgr.GetDecorateColor(self.data.colourType)..">%s</color>"
        if self.data.nameKey then
            --self.Text_Name.text = string.format(nameColor,lang.Get(self.data.nameKey)) 
            self.Text_Name.text = lang.Get(self.data.nameKey)
        end
        self.Text_grade.text = string.format("%s%s",lang.Get(255044),string.format(nameColor,lang.Get(255019+self.data.colourType)))-- --档次（罕见/稀有/传说）
    end
    self.Text_level.text = self.data.refine>0 and string.format("%s%s",lang.Get(255045),string.format(lang.Get(255030),self.data.refine)) or ""
    --TODO切换背景，
    -- self.Img_Background1:Switch(self.data.refine)
    -- self.Img_Background2:Switch(self.data.refine)
    -- self.Gradient_Name.enabled = self.data.refine>0
    -- self.Shadow_Name.enabled = self.data.refine>0
    -- self.Rect_Background.padding.top = self.data.refine>0 and 0 or 10
    -- self.backgroundVertical.padding.bottom = self.data.refine>0 and 20 or (self.data.condition2 and self.data.condition2<=0 and 80 or 20)
    self.lockTog.isOn = self.data.isLock
    --祝福属性
    self.Rect_camp.gameObject:SetActive(self.data.pro1 and self.data.pro1>0)
    self.Rect_profession.gameObject:SetActive(self.data.pro2 and self.data.pro2>0)
    self.Rtsf_hero.gameObject:SetActive(self.data.pro3 and self.data.pro3>0)
    local isInGoodsPack = (not decorateHeroSID or decorateHeroSID==0) and enterType== EnterType.goodsBag
    local proHeight = 36
    if self.data.condition1 and self.data.condition1>0 then
        self.campSS:Switch(self.data.condition1-1)--阵营
        local isActive = self.heroCSV and self.data.condition1==self.heroCSV.type
        self.Img_camp:SetGray(false)--(not isActive)
        if self.data.pro1 and self.data.pro1>0 then
            self.campTxt.text = self.data.proTxt1
            self.campTxt.color = (isInGoodsPack or isActive) and blueColor or grayColor
        end
        local height = self.campTxt.preferredHeight>52 and self.campTxt.preferredHeight or 52
        self.Rect_camp.sizeDelta = {x=self.Rect_camp.sizeDelta.x,y=height}
        proHeight = proHeight + height + 13
    end
    if self.data.condition2 and self.data.condition2>0 then
        self.professionSS:Switch(self.data.condition2-1)--职业
        local isActive = self.heroCSV and self.data.condition2==self.heroCSV.profession
        self.Img_profession:SetGray(false)--(not isActive)
        if self.data.pro2 and self.data.pro2>0 then
            self.professionTxt.text = self.data.proTxt2
            self.professionTxt.color = (isInGoodsPack or isActive) and blueColor or grayColor
        end
        local height = self.professionTxt.preferredHeight>52 and self.professionTxt.preferredHeight or 52
        self.Rect_profession.sizeDelta = {x=self.Rect_profession.sizeDelta.x,y=height}
        proHeight = proHeight + height + 13
    end
    if self.data.condition3 and self.data.condition3>0 then
        local faceID = hero_mgr.ChangeHeroIcon(self.data.condition3,self.data.starsLv)--英雄头像id
        local isActive = HeroID and self.data.condition3==HeroID
        self.spriteAsset:GetSprite(faceID,function(sprite)
            self.Img_hero.sprite = sprite
            self.Img_hero:SetGray(false)--(not isActive)
        end)
        if self.data.pro3 and self.data.pro3>0 then
            self.heroTxt.text = self.data.proTxt3
            self.heroTxt.color = (isInGoodsPack or isActive) and blueColor or grayColor
        end
        local height = self.heroTxt.preferredHeight>48 and self.heroTxt.preferredHeight or 48
        self.Rtsf_hero.sizeDelta = {x=self.Rtsf_hero.sizeDelta.x,y=height}
        proHeight = proHeight + height + 13
    end
    --TODO：不满足条件是需要置灰的
    --技能等级提升
    self.Rtsf_skillRoot.gameObject:SetActive(self.data.skillData and #self.data.skillData>0)
	self.scroll_table.data = self.data.skillData
    self.scroll_table:Refresh(-1, -1)
    --[[
    self.scroll_table_rect.enabled = false
    self.scroll_table_rect.enabled = true
	if self.contentSize then
		self.contentSize.verticalFit = 0
		self.contentSize.verticalFit = 2
	end
    ]]--
    if self.ticker then
        util.RemoveDelayCall(self.ticker)
        self.ticker= nil
    end
    self.ticker = util.DelayCall(0.1,function()
        if self:IsValid() then
            self.contentSize.verticalFit = 2
        end
    end)
    self.Btn_shareBtn.gameObject:SetActive(goodsEntity)
    self.lockTog.gameObject:SetActive(goodsEntity)
    if enterType == EnterType.heroBag then
        self.Btn_undressBtn.gameObject:SetActive(HeroSID and decorateHeroSID and HeroSID==decorateHeroSID)--TODO英雄饰品部位有饰品时
        self.Btn_replaceBtn.gameObject:SetActive(HeroSID and goodsEntity and (not decorateHeroSID or decorateHeroSID==0))--TODO英雄饰品部位无饰品时
        self.Btn_upgradeBtn.gameObject:SetActive(self.data.refine==0 and self.data.colourType==3 and goodsEntity)
        self.Rtsf_buttonRoot.gameObject:SetActive(self.Btn_undressBtn.gameObject.activeSelf or self.Btn_replaceBtn.gameObject.activeSelf or self.Btn_upgradeBtn.gameObject.activeSelf)
    elseif enterType == EnterType.goodsBag then
        self.Btn_replaceBtn.gameObject:SetActive(false)
        self.Btn_undressBtn.gameObject:SetActive(false)
        self.Btn_upgradeBtn.gameObject:SetActive(self.data.refine==0 and self.data.colourType==3 and goodsEntity)
        self.Rtsf_buttonRoot.gameObject:SetActive(self.data.refine==0 and self.data.colourType==3 and goodsEntity)
    elseif enterType == EnterType.chat or enterType == EnterType.otherPlayer then
        self.Btn_replaceBtn.gameObject:SetActive(false)
        self.Btn_undressBtn.gameObject:SetActive(false)
        self.Btn_upgradeBtn.gameObject:SetActive(false)
        self.Rtsf_buttonRoot.gameObject:SetActive(false) 
    end
    self.upgradeRed.gameObject:SetActive(decoration_mgr.IsDecorateRefineUp(itemId,itemSid))
    self.backgroundVertical.enabled = false
    self.Rect_Background.enabled = false
    local isDecorationOpen = decoration_mgr.isDecorationOpen()
    self.Rect_tipsRoot.gameObject:SetActive(not isDecorationOpen)
    self.Rect_proRoot.anchoredPosition = {x = self.Rect_proRoot.anchoredPosition.x, y = isDecorationOpen and 0 or -124}
    self.Rtsf_skillRoot.anchoredPosition = {x = self.Rtsf_skillRoot.anchoredPosition.x, y = isDecorationOpen and -269 or -393}
    local tipsHeight = isDecorationOpen and 0 or 125
    if self.ticker2 then
        util.RemoveDelayCall(self.ticker2)
        self.ticker2= nil
    end
    self.ticker2 = util.DelayCall(0.01,function( ... )
        if window and window:IsValid() then
            local skillHeiht = self.Rect_skillContent.sizeDelta.y--该数据不准确
            local height = 0
            for i=1,3 do
                if not window.heights then 
                    window.heights = {} 
                end
                if not window.heights[i] then window.heights[i] = 0 end
                height = height+window.heights[i]
            end
            if height>skillHeiht then
                skillHeiht = height+2*12
            end
            self.Rect_skillContent.sizeDelta = {x=self.Rect_skillContent.sizeDelta.x,y=skillHeiht}
            proHeight = proHeight + 13
            -- --print("skillHeiht",skillHeiht,"proHeight",proHeight)
            self.Rect_proRoot.sizeDelta = {x=self.Rect_proRoot.sizeDelta.x,y=proHeight}
            self.scrollrect.sizeDelta = {x=self.scrollrect.sizeDelta.x,y=skillHeiht>0 and proHeight+244 or proHeight}
            self.content_rect.sizeDelta = {x=self.content_rect.sizeDelta.x,y=proHeight+skillHeiht+50 + tipsHeight}--50是技能激活标题大约高度
            self.backgroundVertical.enabled = true
            self.Rect_Background.enabled = true
        end
    end)
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowClose
function UIDecorationDetail:Close()
    if self:IsValid() then
		self:UnsubscribeEvents()
        if self.scroll_table then
            self.scroll_table:ItemsDispose()
        end
        if self.iconUI then
            self.iconUI:Dispose()
            self.iconUI = nil
        end
        if self.spriteAsset then
            self.spriteAsset:Dispose()
            self.spriteAsset = nil
        end
        if self.cardSpriteAsset then
			self.cardSpriteAsset:Dispose()
			self.cardSpriteAsset = nil
		end
        if self.ticker then
            util.RemoveDelayCall(self.ticker)
            self.ticker= nil
        end
        if self.ticker2 then
            util.RemoveDelayCall(self.ticker2)
            self.ticker2= nil
        end
        itemId = nil
        itemSid = nil
        HeroSID = nil
        HeroEntity = nil
        BlessLv = nil
        enterType = EnterType.heroBag
        InputDecorateData = nil--传入饰品数据
	end
	self.__base:Close()
    window = nil
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowSubscribeEvents
--[[订阅UI事件]]
function UIDecorationDetail:SubscribeEvents()
    self.OnBtn_closeBtnClickedProxy = function()
        self:OnBtn_closeBtnClicked()
    end

    self.OnBtn_shareBtnClickedProxy = function()
        self:OnBtn_shareBtnClicked()
    end

    self.OnTog_lockClickedProxy = function(isOn)
        self:OnTog_lockClicked(isOn)
    end

    self.OnBtn_undressBtnClickedProxy = function()
        self:OnBtn_undressBtnClicked()
    end

    self.OnBtn_upgradeBtnClickedProxy = function()
        self:OnBtn_upgradeBtnClicked()
    end

    self.OnBtn_replaceBtnClickedProxy = function()
        self:OnBtn_replaceBtnClicked()
    end

    self.decorateUpEvent = function (eventName,blessID)
        --TODO饰品强化成功
        --self:UpdateUIPage()
        windowMgr:UnloadModule("ui_decoration_detail")
    end
    event.Register(event.ENHANCE_DECORATE_RESPONSE,self.decorateUpEvent)
----///<<< Button Proxy Line >>>///-----

end --///<<< function

--@endregion 

--@region WindowUnsubscribeEvents
--[[退订UI事件]]
function UIDecorationDetail:UnsubscribeEvents()
    event.Unregister(event.ENHANCE_DECORATE_RESPONSE,self.decorateUpEvent)
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowBtnFunctions
function UIDecorationDetail:OnBtn_closeBtnClicked()
    windowMgr:UnloadModule("ui_decoration_detail")
end

function UIDecorationDetail:OnBtn_shareBtnClicked()
    --TODO：分享饰品
    if itemSid then
        local ui_share = require "ui_share"
        local mq_common_pb = require "mq_common_pb"
        ui_share.Open(mq_common_pb.enSpeak_ShareDecorate, itemSid,self.data.blessLv)
    end
end

function UIDecorationDetail:OnTog_lockClicked(isOn)
    --TODO：上锁饰品
    local lockState = self.data.isLock
     --print("lockState",lockState,"isOn",isOn)
    if lockState ~= isOn then
        lockState = not lockState
        decoration_mgr.Requst_Decorate_LOCK(itemSid,lockState)
    end
end


function UIDecorationDetail:OnBtn_undressBtnClicked()
    if HeroSID then
        local temp = {}
        temp.bTakeOff = true--是否脱下
        temp.newDecorateSid = 0--新饰品id(没有数据给0,下同)
        temp.oldDecorateSid = itemSid--旧饰品id
        temp.pos=self.data.type--饰品位置
        local decorateGroup = {}
        decorateGroup[temp.pos] = temp
        decoration_mgr.Request_ReplaceDecorate(HeroSID,decorateGroup,HeroID,tostring(itemId),"0")
        windowMgr:UnloadModule("ui_decoration_detail")
        windowMgr:UnloadModule("ui_decoration_package")
    end
end

function UIDecorationDetail:OnBtn_upgradeBtnClicked()
    local win = windowMgr:ShowModule("ui_decoration_strengthen")
    if win then
        win:SetInputParam(itemSid,itemId,HeroSID,HeroID)
    end
end

function UIDecorationDetail:OnBtn_replaceBtnClicked()
    if HeroSID then
        local temp = {}
        temp.bTakeOff = false--是否脱下
        temp.newDecorateSid = itemSid--新饰品id(没有数据给0,下同)
        temp.oldDecorateSid = 0--旧饰品id
        temp.pos=self.data.type--饰品位置
        local decorateGroup = {}
        decorateGroup[temp.pos] = temp
        decoration_mgr.Request_ReplaceDecorate(HeroSID,decorateGroup,HeroID,"0",tostring(itemId))
        windowMgr:UnloadModule("ui_decoration_detail")
        windowMgr:UnloadModule("ui_decoration_package")
    end
end

--@endregion 

--@region ScrollItem
--@endregion 

--@region WindowInherited
local CUIDecorationDetail = class(ui_base, nil, UIDecorationDetail)
--@endregion 

--@region ModuleFunction
function Show()
    if window == nil then
        window = CUIDecorationDetail()
        window._NAME = _NAME
        window:LoadUIResource("ui/prefabs/uidecorationdetail.prefab", nil, nil, nil,true)
    end
    window:Show()
    return window
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end

function Close()
    if window ~= nil then
        Hide()
        window:Close()
        window = nil
    end
end