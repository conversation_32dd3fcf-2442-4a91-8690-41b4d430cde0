--@region FileHead
-- ui_decoration_package.txt ---------------------------------
-- author:  曾琳琳
-- date:    2022/1/8 18:26:30
-- ver:     1.0
-- desc:    饰品2背包界面Description
-------------------------------------------------
--@endregion 

--@region Require
local print     = print
local require   = require
local pairs     = pairs
local ipairs    = ipairs
local typeof    = typeof
local string    = string
local table     = table
local math      = math

local Button        = CS.UnityEngine.UI.Button
local Text          = CS.UnityEngine.UI.Text
local Image         = CS.UnityEngine.UI.Image
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable
local RectTransform = CS.UnityEngine.RectTransform
local Toggle 		= typeof(CS.UnityEngine.UI.Toggle)

local event					= require "event"
local class                 = require "class"
local ui_base               = require "ui_base"
local windowMgr 	        = require "ui_window_mgr"
local decoration_item       = require "decoration_item"
local decoration_mgr        = require "decoration_mgr"
local player_mgr            = require "player_mgr"
local lang                  = require "lang"
--@endregion p

--@region ModuleDeclare
module("ui_decoration_package")
--local interface = require "iui_decoration_package"
local window = nil
local UIDecorationPackage = {}
local partID = nil
local HeroSID = nil--英雄SID
local HeroEntity = nil
--@endregion 

--@region WidgetTable
UIDecorationPackage.widget_table = {
    Btn_closeBtn = { path = "Auto_closeBtn", type = "Button", event_name = "OnBtn_closeBtnClickedProxy" },
    menuOpenBtn = { path = "menuPanel/TypeTogList/menuOpenBtn", type = "Button", event_name = "OnBtn_menuOpenBtnClickedProxy" },
    Text_title = { path = "Auto_Background/Auto_title", type = "Text", event_name = "" },
    Img_Background = { path = "Auto_Background", type = "Image", event_name = "" },
    Rtsf_list = { path = "Auto_Background/Auto_list", type = "RectTransform", event_name = "" },
    scroll_table = { path = "Auto_Background/Auto_list/Viewport/Content", type = ScrollRectTable, event_name = "" },
    openPanel = {path ="menuPanel", type = "Image"},
    MaskBtn = {path = "Mask", type = "Button"},
    profTopRoot = {path = "menuPanel/campTopList", type = RectTransform},
    menuCloseBtn = {path = "menuPanel/campTopList/menuCloseBtn", type = "Button"},
    profTog_1 = {path = "menuPanel/campTopList/All", type = Toggle},
    profTog_2 = {path = "menuPanel/campTopList/zhanshiCZ", type = Toggle},
    profTog_3 = {path = "menuPanel/campTopList/fashiCZ", type = Toggle},
    profTog_4 = {path = "menuPanel/campTopList/youxiaCZ", type = Toggle},
    profTog_5 = {path = "menuPanel/campTopList/cikeCZ", type = Toggle},
    profTog_6 = {path = "menuPanel/campTopList/fuzhuCZ", type = Toggle},
    typeTogRoot = {path = "menuPanel/TypeTogList", type = "Image"},
    typeTog_1 = {path = "menuPanel/TypeTogList/Tog1", type = Toggle},
    typeTog_2 = {path = "menuPanel/TypeTogList/Tog2", type = Toggle},
    typeTog_3 = {path = "menuPanel/TypeTogList/Tog3", type = Toggle},
    typeTog_4 = {path = "menuPanel/TypeTogList/Tog4", type = Toggle},
    typeTog_5 = {path = "menuPanel/TypeTogList/Tog5", type = Toggle},
    typeTog_6 = {path = "menuPanel/TypeTogList/Tog6", type = Toggle},
    typeTog_7 = {path = "menuPanel/TypeTogList/Tog7", type = Toggle},
--@region User
--@endregion 
}
--@endregion 

--@region WindowCtor
function UIDecorationPackage:ctor(selfType)
	self.__base:ctor(selfType)
--@region User
--@endregion 
end --///<<< function

--@endregion 

function OnRenderItem(scroll_rect_item,index,dataItem)
	scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem

    local root = scroll_rect_item:Get("root")
    --饰品实例化
    scroll_rect_item.data.iconUI =  scroll_rect_item.data.iconUI or decoration_item.CDecorationItem():Init(root.transform)
    local itemIcon = scroll_rect_item.data.iconUI
    itemIcon:SetGoods(dataItem, dataItem.goodsID, 1, function()
        --TODO:点击选择饰品
        local win = windowMgr:ShowModule("ui_decoration_detail")
        if win then
            win:SetInputParam(dataItem.goodsSid,dataItem.goodsID,HeroSID,HeroEntity)
        end
    end,false,false,false,HeroEntity.heroID)
    local isRefineUp = decoration_mgr.IsDecorateRefineUp(dataItem.goodsID,dataItem.goodsSid)
    itemIcon:SetRedVisible(isRefineUp)
end

--@region WindowInit
--[[窗口初始化]]
function UIDecorationPackage:Init()
    --英雄阵营筛选
    self.quality = nil
    self.toggleGroup = {
        self.typeTog_1,
        self.typeTog_2,
        self.typeTog_3,
        self.typeTog_4,
        self.typeTog_5,
        self.typeTog_6,
        self.typeTog_7,
    }
    --英雄职业筛选
    self.profIndex = nil
    self.profToggleGroup = {
        self.profTog_1,
        self.profTog_2,
        self.profTog_3,
        self.profTog_4,
        self.profTog_5,
        self.profTog_6,
    }
    self.scroll_table.onItemRender=OnRenderItem
    self.scroll_table.onItemDispose=function(scroll_rect_item,index)
        if scroll_rect_item and scroll_rect_item.data and scroll_rect_item.data.iconUI then
            scroll_rect_item.data.iconUI:Dispose()
            scroll_rect_item.data.iconUI = nil
        end
    end
    self:SubscribeEvents()
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function UIDecorationPackage:OnShow()
    self:UpdateUIPage()
    self:SetFilterDecorate()
    local bless_csv = decoration_mgr.GetBlessCSVByID(partID)
    self.Text_title.text = string.format("%s:%s",lang.Get(255198),lang.Get(bless_csv.langID))
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowOnHide
--[[界面隐藏时调用]]
function UIDecorationPackage:OnHide()
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowSetInputParam
--[[设置窗口的输入参数。该参数通常是由其它模块或者外部设置进来。需要注意的是，
当调用这个函数的时候，窗口资源可能还是没有加载完成的。
@param p 参数表
]]
function UIDecorationPackage:SetInputParam(p,herosid,_heroEntity)
	self.inputParam = p
    partID = p    
    HeroSID = herosid--英雄SID
    HeroEntity = _heroEntity or player_mgr.GetPalPartDataBySid(HeroSID)--英雄实体(红色饰品精炼需要用到)
    --如果正在显示，则更新一次窗口
    if self.UIRoot and self.UIRoot.activeSelf == true then
        self:UpdateUIPage()
    end
	
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowBuildUpdateData
--[[构建UI更新数据]]
function UIDecorationPackage:BuildUpdateData()
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowUpdateUI
--[[资源加载完成，被显示的时候调用]]
function UIDecorationPackage:UpdateUIPage()
	self:BuildUpdateData()
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowClose
function UIDecorationPackage:Close()
    if self:IsValid() then
		self:UnsubscribeEvents()
        if self.scroll_table then
            self.scroll_table:ItemsDispose()
        end
        HeroSID = nil
        HeroEntity = nil
	end
	self.__base:Close()
    window = nil
--@region User
--@endregion 
end --///<<< function

--@endregion 
function UIDecorationPackage:GetFilteredDecorateData(quality,profIndex)
    --筛选饰品数据
    local data = {}
    local partData = decoration_mgr.GetHeroPartDecorate(partID)
    local csv = nil
    for i,goodsEntity in pairs(partData) do
        if goodsEntity then
            csv = decoration_mgr.GetDecorationCSVByID(goodsEntity.goodsID)
            local isFitType = not quality or (quality==0) or  (quality and csv.condition1 and quality==csv.condition1)
            local isFitProp = not profIndex or (profIndex==0) or (profIndex and csv.condition2 and csv.condition2>0 and profIndex==csv.condition2)
            -- --print("quality",quality,"profIndex",profIndex,"csv.condition1",csv.condition1,"csv.condition2",csv.condition2,"isFitType",isFitType,"isFitProp",isFitProp,"goodsEntity.goodsID",goodsEntity.goodsID)
            if isFitType and isFitProp then
                table.insert(data,goodsEntity)
            end
        end
    end
    data = decoration_mgr.GetSortDecoratePack(type,HeroEntity.heroID,data)
    return data
end

--筛选满足条件的饰品
function UIDecorationPackage:SetFilterDecorate(quality,profIndex)
    if quality  then
        self.quality = quality
    end
    if profIndex then
        self.profIndex = profIndex
    end
    DecorateData = self:GetFilteredDecorateData(self.quality,self.profIndex)
    self.scroll_table.pageSize = math.min(50, #DecorateData)
    self.scroll_table.data= DecorateData
    self.scroll_table:Refresh(-1,-1)
end

--@region WindowSubscribeEvents
--[[订阅UI事件]]
function UIDecorationPackage:SubscribeEvents()
    self.OnBtn_closeBtnClickedProxy = function()
        self:OnBtn_closeBtnClicked()
    end

    self.OnBtn_menuOpenBtnClickedProxy = function()
        self:OnBtn_menuOpenBtnClicked()
    end

    for i, toggle in ipairs(self.toggleGroup) do
        self["toggleValueChanged"..i] = function(isOn)
            if not isOn then
                return
            end 
            if window and window:IsValid() then
                window:SetFilterDecorate(i-1 ,self.profIndex)
            end
        end
        toggle.onValueChanged:AddListener(self["toggleValueChanged"..i])
    end

    for i,toggle in ipairs(self.profToggleGroup) do
        self["profToggleValueChanged"..i] = function(isOn)
            if not isOn then
                return
            end 
            if window and window:IsValid() then
                window:SetFilterDecorate(nil ,i-1)
            end
        end
        toggle.onValueChanged:AddListener(self["profToggleValueChanged"..i])
    end

    self.OnCloseMenu = function()
        self.openPanel.enabled = (false)  
        self.profTopRoot.gameObject:SetActive(false)
        self.MaskBtn.gameObject:SetActive(false)
        self.typeTogRoot.enabled = true
        self.menuOpenBtn.gameObject:SetActive(true)
    end
    self.widget_table["MaskBtn"].event_name = "OnCloseMenu"
    self.widget_table["menuCloseBtn"].event_name = "OnCloseMenu"

    
    self.decorateUpEvent = function (eventName,blessID)
        if window and window:IsValid() then
            window:SetFilterDecorate(self.quality ,self.profIndex)
        end
    end
    event.Register(event.ENHANCE_DECORATE_RESPONSE,self.decorateUpEvent)
end --///<<< function

--@endregion 

--@region WindowUnsubscribeEvents
--[[退订UI事件]]
function UIDecorationPackage:UnsubscribeEvents()
    event.Unregister(event.ENHANCE_DECORATE_RESPONSE,self.decorateUpEvent)

--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowBtnFunctions
function UIDecorationPackage:OnBtn_closeBtnClicked()
    windowMgr:UnloadModule("ui_decoration_package")
end

function UIDecorationPackage:OnBtn_menuOpenBtnClicked()
    self.openPanel.enabled = (true)  
    self.profTopRoot.gameObject:SetActive(true)
    self.MaskBtn.gameObject:SetActive(true)
    self.typeTogRoot.enabled = false
    self.menuOpenBtn.gameObject:SetActive(false)
end

--@endregion 

--@region ScrollItem
--@endregion 

--@region WindowInherited
local CUIDecorationPackage = class(ui_base, nil, UIDecorationPackage)
--@endregion 

--@region ModuleFunction
function Show()
    if window == nil then
        window = CUIDecorationPackage()
        window._NAME = _NAME
        window:LoadUIResource("ui/prefabs/uidecorationpackage.prefab", nil, nil, nil)
    end
    window:Show()
    return window
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end

function Close()
    if window ~= nil then
        Hide()
        window:Close()
        window = nil
    end
end

