-- ui_7day_challenge.txt -----------------------------------
-- author:  刘志远
-- date:    2019-6-21
-- ver:     1.0
-- desc:    7天挑战
----------------------------------------------------------------
local print = print
local require = require
local typeof = typeof
local dump=dump
local string=string
local math=math
local pairs=pairs
local tostring = tostring
local table = table

local util=require"util"
local lang = require "lang"
local item_data = require "item_data"
local game_scheme = require "game_scheme"
local reward_mgr = require "reward_mgr"
local goods_item = require "goods_item_new"
local iui_item_detail = require "iui_item_detail"
local event=require"event"
local flow_text=require"flow_text"
local ui_window_mgr = require "ui_window_mgr"
local class = require "class"
local ui_base = require "ui_base"
local player_mgr 	= require "player_mgr"
local day7_challenge_data = require "day7_challenge_data"
local json = require "dkjson"
local model_item = require "model_item"
local hero_item = require "hero_item_new"
local day7_challenge_mgr = require "day7_challenge_mgr"


local GameObject = CS.UnityEngine.GameObject
local Slider = CS.UnityEngine.UI.Slider
local Text = CS.UnityEngine.UI.Text
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable
local SortingGroup = CS.UnityEngine.Rendering.SortingGroup
local LeanTween = CS.LeanTween
local LeanTweenType = CS.LeanTweenType
local Canvas = CS.UnityEngine.Canvas
local SpriteMask = CS.UnityEngine.SpriteMask
local Image         = CS.UnityEngine.UI.Image
local TextMeshProUGUI         = CS.TMPro.TextMeshProUGUI
local Color         = CS.UnityEngine.Color

module("ui_7day_challenge_three")
local window = nil
local M = {}

local surplusTime = nil     --剩余时间 
local sDay = 0              --剩余天数
local lastSelect = 1        --最后选择的day切页
local dayBtnState = {}      --day按钮的解锁状态
local rewards = {}          --记录icon和数据
local first_day = 1         --第一个有红点得天数
local first_page = 1         --第一个有红点得切页
local is_set_day = false         --第一个有红点得切页


local parent = nil
local parent_order = nil
local groupModel = nil
local assetBundleName = "art/battleplayer/herocardview.prefab"
local modelPath = "animations/characters/57_shefanvyao/edit_57_shefanvyao1.prefab"
local curModelId = nil
local weekType = 3

M.widget_table =
{
    closeBt = { path = "closeBtn", type = "Button"},
    time = { path = "Title/Time/Text", type = "Text", },
    countRoot = { path = "Top/Count/Mask/numRoot", type = "RectTransform", },
    count1 = { path = "Top/Count/Mask/numRoot/num1", type = "RectTransform", },
    count2 = { path = "Top/Count/Mask/numRoot/num2", type = "RectTransform", },
    -- progress = { path = "Top/Progress", type = typeof(Slider), },
    targetList = { path = "TargetList/Viewport/Content", type = ScrollRectTable, },
    reward1 = { path = "Top/Rewards/1", type = "RectTransform", },
    reward2 = { path = "Top/Rewards/2", type = "RectTransform", },
    reward3 = { path = "Top/Rewards/3", type = "RectTransform", },
    reward4 = { path = "Top/Rewards/4", type = "RectTransform", },
    reward5 = { path = "Top/Rewards/5", type = "RectTransform", },

    progress_1 = { path = "Top/progressList/Progress_1", type = Slider},
    progress_2 = { path = "Top/progressList/Progress_2", type = Slider},
    progress_3 = { path = "Top/progressList/Progress_3", type = Slider},
    progress_4 = { path = "Top/progressList/Progress_4", type = Slider},
    progress_5 = { path = "Top/progressList/Progress_5", type = Slider},
    
    textListNum_1 = { path = "Top/TextList/num_1", type = "Text"},
    textListNum_2 = { path = "Top/TextList/num_2", type = "Text"},
    textListNum_3 = { path = "Top/TextList/num_3", type = "Text"},
    textListNum_4 = { path = "Top/TextList/num_4", type = "Text"},
    textListNum_5 = { path = "Top/TextList/num_5", type = "Text"},

    day1 = { path = "DayTabs/Viewport/Content/1", type = "Button", },
    day2 = { path = "DayTabs/Viewport/Content/2", type = "Button", },
    day3 = { path = "DayTabs/Viewport/Content/3", type = "Button", },
    day4 = { path = "DayTabs/Viewport/Content/4", type = "Button", },
    day5 = { path = "DayTabs/Viewport/Content/5", type = "Button", },
    day6 = { path = "DayTabs/Viewport/Content/6", type = "Button", },
    day7 = { path = "DayTabs/Viewport/Content/7", type = "Button", },

    day1_red = { path = "DayTabs/Viewport/Content/1/red", type = "Image", },
    day2_red = { path = "DayTabs/Viewport/Content/2/red", type = "Image", },
    day3_red = { path = "DayTabs/Viewport/Content/3/red", type = "Image", },
    day4_red = { path = "DayTabs/Viewport/Content/4/red", type = "Image", },
    day5_red = { path = "DayTabs/Viewport/Content/5/red", type = "Image", },
    day6_red = { path = "DayTabs/Viewport/Content/6/red", type = "Image", },
    day7_red = { path = "DayTabs/Viewport/Content/7/red", type = "Image", },
    
    title_cn = { path = "Title/zhongwen", type = "RectTransform", },
    title_en = { path = "Title/yingwen", type = "RectTransform", },
    titleImageEN = { path = "Title/yingwen/jiqirenyingwen", type = "Image", },

    spriteMask = {path = "maskObj/GameObject", type = SpriteMask},
    longEffectMask = {path = "maskObj/longEffectMask", type = SpriteMask},
    rawImage = {path = "Bg/heroImage", type = "RawImage"},--英雄模型
}

function M:ctor( )
    surplusTime = day7_challenge_data.GetSurplusTime3()
    sDay = math.modf( surplusTime/(24*3600) ) 
    dayBtnState={} 
    for i=1,7 do
        dayBtnState[i] = i<=(game_scheme:InitBattleProp_0(551).szParam.data[weekType-1]-sDay)
    end

    lastSelect = 1 --初始选中第一个
    day7_challenge_data.SetCurSelectDay(lastSelect+7*(weekType-1))

    rewards = {}
    local num = game_scheme:ChallengeReward_nums()
	for i=0,num-1 do 
        local cfg = game_scheme:ChallengeReward(i)		
        if cfg.nWeek == weekType then		
            rewards[cfg.nRewardIndex] = {nNeedScore =cfg.nNeedScore,nRewardID = cfg.nRewardID,nRewardIndex = cfg.nRewardIndex}
        end
    end
end

--[[设置英雄模型]]
function M:LoadHeroModel()
    local hero_mgr = require("hero_mgr")
    local modelId = hero_mgr.ChangeHeroModel(95, 5)--夜之魔女
    local modulCfg = game_scheme:Modul_0(modelId)
    modelPath = modulCfg and modulCfg.modelPath or modelPath
	local cfg_hero = game_scheme:Hero_0(95)

    if self.model and curModelId == modelId then
        return
    end
    local cfg_background = game_scheme:ActorBackground_1(1,cfg_hero and cfg_hero.type)

	self.heroModelPath = modulCfg.modelPath
	if self.model then
		self.model:ChangeModel(1,modulCfg.modelPath)
		if cfg_background then
			self.model:SetBackground(cfg_background.bPath)
		end
        self.model:SetBackgroundActive(false)
    else
		self.model = model_item.CModelItem():InitSingle(modulCfg.modelPath,function(_rt)
			if self:IsValid() then
				self.rawImage.texture = _rt
				if cfg_background then
					self.model:SetBackground(cfg_background.bPath)
				end

				self.model:SetBackgroundActive(false)
				self.model:SetMaskActive(1,0)
				self.model:SetNodeAngle()
			end
		end, true)
	end
	curModelId = modelId
end

function M:Init()
    self:SubscribeEvents()
    self.targetList.onItemRender=onItemRenderBottom
    self.targetList.onItemDispose=function(scroll_rect_item,index)
        if scroll_rect_item and scroll_rect_item.data then
            if scroll_rect_item.data.itemArr then
                for k, v in pairs(scroll_rect_item.data.itemArr) do
                    v:Dispose()
                end
            end

            if scroll_rect_item.data.rIdArr then
                for k, v in pairs(scroll_rect_item.data.rIdArr) do
                    v:Dispose()
                end
            end
            -- for i=1,5 do
            --     if scroll_rect_item.data.itemArr[i]  then
            --         scroll_rect_item.data.itemArr[i]:Dispose()
            --         scroll_rect_item.data.itemArr[i] = nil
            --     end
            --     if scroll_rect_item.data.rIdArr[i] then
            --         scroll_rect_item.data.rIdArr[i]:Dispose()
            --         scroll_rect_item.data.rIdArr[i] = nil
            --     end
            -- end
            if scroll_rect_item.data.vipIcon then
                scroll_rect_item.data.vipIcon:Dispose()
                scroll_rect_item.data.vipIcon = nil
            end
        end
	end
    self:ChangeLangRes()
    --进入打点
    local roleName = tostring(player_mgr.GetRoleName()) or ""
    local json_str = json.encode({
        role_name = roleName
    })
    event.Trigger(event.GAME_EVENT_REPORT, "seven_day_Goal_attention", json_str)
end

function M:ChangeLangRes()
    local oversea_res = require "oversea_res"
    local res = "qitian_biaoti_02"
    local callBack = function(asset)
        if self:IsValid() then
            if asset then
                self.titleImageEN.sprite = asset
                self.titleImageEN:SetNativeSize()
            end
        end
    end
    oversea_res.LoadSprite(res,"ui_7day_challenge_three",callBack) 
end

function M:OnShow()
    self.__base:OnShow()
	event.Trigger(event.GAME_EVENT_REPORT, "seven_day_Goal_attention", {})
    self:RefreshUI()
    --self:LoadHeroModel()
    --新手强制引导事件
    local force_guide_system=require"force_guide_system"
    local force_guide_event=require"force_guide_event"
    force_guide_system.TriEnterEvent(force_guide_event.tEventEnter7day)
end

-- 策划需求 展示最早有红点得那天
function M:SetPage(day)
    self["day"..day.."click_event"]()
end

function M:RefreshUI()
    self:UpdateStaticInfo()
    UpdatePage()
    self:UpdateTime()
    self:UpdatePoint()
    self:UpdateTargetList()
end

function M:Close()
	if window.UIRoot and window:IsValid() then
        window:UnsubscribeEvents()
        if window.targetList then
            window.targetList:ItemsDispose()
        end
    end
    
    for i=1,#rewards do  
        if rewards[i].icon then
            rewards[i].icon:Dispose()
            rewards[i].icon=nil
        end
    end

    for i=1,7 do
        self["day"..i.."click_event"] = nil
    end 
    is_set_day = false
    first_day = 1
    first_page = 1
    parent = nil

    if self.model then
        self.model:Dispose()
        self.model = nil
    end
    groupModel = nil
    curModelId = nil
	window.__base:Close()
	window = nil
end

function M:SubscribeEvents()
    --close
    self.closeBt.onClick:AddListener(function(  )
        -- Close()
        ui_window_mgr:HideModule("ui_7day_challenge_three")
        ui_window_mgr:HideModule("ui_7day_challenge_list")
    end)

    for i=1,7 do
        self["day"..i.."click_event"] = function()
            local totelday = game_scheme:InitBattleProp_0(196).szParam.data[0]
            if i+sDay-totelday < 1 and lastSelect == i then return end
            if dayBtnState[i] then
                self["day"..lastSelect].transform:Find("select").gameObject:SetActive(false) 
                self["day"..i].transform:Find("select").gameObject:SetActive(true) 
                lastSelect=i 
                local day = i
                day7_challenge_data.SetCurSelectDay(day+7*(weekType-1))
                self:UpdateTargetList()
                window.targetList:Refresh(0,-1)
            else
                
                flow_text.Add(string.format(lang.Get(36064),(i+sDay-totelday)))
            end
        end
        self["day"..i].onClick:AddListener(self["day"..i.."click_event"])
    end

    --countdown
    self.OnTimeTicker = function ( )
        if window and window:IsValid() then
            window:UpdateTime()
        end
    end
    event.Register(event.ACTIVITY_PER_SECOND, self.OnTimeTicker)

    self.OnUpdateAchiData = function ( )
        if not window or not window:IsValid() then return end

        window:UpdateTargetList()
        window.targetList:Refresh(0,-1)
        
        for i=1,7 do
            window:UpdateRedDot_single(i)
        end
    end
    event.Register(event.UPDATE_ACHI_DATA,self.OnUpdateAchiData)
    event.Register(event.UPDATE_SEVEN_NTF,self.OnUpdateAchiData)

    self.OnUpdateSevenScore = function ( )
        if not window or not window:IsValid() then return end
        window:UpdatePoint(true)
        window:UpdateRewardState()
        for i=1,7 do
            window:UpdateRedDot_single(i)
        end
        flow_text.Add(lang.Get(36062))
        event.Trigger(event.SEVEN_RED_DOT)
    end
    event.Register(event.UPDATE_SEVEN_SCORE,self.OnUpdateSevenScore)

    self.OnUpdateRewardState = function (eventName)
        if window and window:IsValid() then
            window:UpdateRewardState()
            -- if topicKey then
            --     day7_challenge_mgr.ShowReward(eventName,topicKey,rewards)
            -- end
            event.Trigger(event.SEVEN_RED_DOT)
        end
    end
    event.Register(event.UPDATE_SEVEN_REWARD_STATE,self.OnUpdateRewardState)
end

function M:UnsubscribeEvents()
    self.closeBt.onClick:RemoveAllListeners()
    for i=1,7 do
        self["day"..i].onClick:RemoveAllListeners()
    end 
    event.Unregister(event.ACTIVITY_PER_SECOND, self.OnTimeTicker)
    event.Unregister(event.UPDATE_ACHI_DATA, self.OnUpdateAchiData)
    event.Unregister(event.UPDATE_SEVEN_SCORE, self.OnUpdateSevenScore)
    event.Unregister(event.UPDATE_SEVEN_REWARD_STATE, self.OnUpdateRewardState)
    event.Unregister(event.UPDATE_SEVEN_NTF,self.OnUpdateAchiData)
end

function UpdateTopRewardInfo(order)
    if window and window:IsValid() then
        window:UpdateStaticInfo(order)
    end
end

--只会在初始化的时候调用一次
function  M:UpdateStaticInfo(order)
    --///dayToggle
    lastSelect = first_day

    self["day"..lastSelect].transform:Find("select").gameObject:SetActive(true) 
    for i=1,7 do
        local day = i + 14
        self["day"..i].transform:Find("normal/Text"):GetComponent(typeof(TextMeshProUGUI)).text = string.format(lang.Get(36078),day)
        self["day"..i].transform:Find("select/Text"):GetComponent(typeof(TextMeshProUGUI)).text = string.format(lang.Get(36078),day)
        self["day"..i].transform:Find("no/Text"):GetComponent(typeof(TextMeshProUGUI)).text = string.format(lang.Get(36078),day)
        self["day"..i].transform:Find("no").gameObject:SetActive(not dayBtnState[i])
        self["day"..i].transform:Find("normal").gameObject:SetActive(dayBtnState[i])
        self:UpdateRedDot_single(i)
    end

    -- lastSelect=first_day   
    day7_challenge_data.SetCurSelectDay(lastSelect+7*(weekType-1))
    self:SetPage(first_day)
    
    self.title_cn.gameObject:SetActive( lang.USE_LANG == lang.ZH)
    self.title_en.gameObject:SetActive( lang.USE_LANG ~= lang.ZH)

    local set_order = order and order or parent_order
    --积分奖励图标
    local sort_order = require "sort_order"
    local orderStart,orderEnd =sort_order.ApplyBaseIndexs(self,nil, 3)
    for i=1,#rewards do  
        local rewardData = reward_mgr.GetRewardGoods(rewards[i].nRewardID)
        local lastOne = i == #rewards
        if rewardData.nType == 1 then
            rewards[i].icon = goods_item.CGoodsItem()
            self["reward"..i].gameObject:SetActive(true)
            rewards[i].icon:Init(self["reward"..i].transform,function()
                rewards[i].icon:GoodsEffectEnable(true, set_order, 1, 0.6)
                rewards[i].icon:SetGoods(nil, rewardData.id, rewardData.num, function()
                    window:OnClickReward(rewards[i],rewardData,nil,nil,lastOne)
                end)
            end,0.7)

            --调整特效层级
            self["reward"..i]:Find("canGet"):GetComponent(typeof(SortingGroup)).sortingOrder = set_order
            self["reward"..i]:Find("canGet/red"):GetComponent(typeof(Canvas)).sortingOrder = set_order+1
        elseif rewardData.nType == 2 then
            -- 英雄
            rewards[i].icon = hero_item.CHeroItem()
            self["reward"..i].gameObject:SetActive(true)
            rewards[i].icon:Init(self["reward"..i].transform, function()
                rewards[i].icon:DisplayInfo()
                    -- window:OnClickReward(rewards[i],rewardData)
            end,0.77)

            rewards[i].icon:SetHero({heroID=rewardData.id}, function()
                window:OnClickReward(rewards[i],rewardData,2,rewards[i].icon,lastOne)
            end)
            
            --调整特效层级
            self["reward"..i]:Find("canGet"):GetComponent(typeof(SortingGroup)).sortingOrder = set_order
            self["reward"..i]:Find("canGet/red"):GetComponent(typeof(Canvas)).sortingOrder = set_order+1
        else
----             --print("error:积分奖励未支持英雄的类型，请查看表是否填错~~~~~")
        end
    
    end

    self.spriteMask.isCustomRangeActive = true
    self.spriteMask.frontSortingOrder = orderStart + 2
    self.spriteMask.backSortingOrder = orderStart-1
    self.longEffectMask.isCustomRangeActive = true
    self.longEffectMask.frontSortingOrder = orderStart + 2
    self.longEffectMask.backSortingOrder = orderStart-1
    self:UpdateRewardState()
end

function UpdatePage()
    local ui_activity_new_player = require "ui_activity_new_player"
    ui_activity_new_player.SetShowType(first_page)
end

function M:OnClickReward( reward,rewardCfg ,type,icon,lastOne)
    local getStates = day7_challenge_data.GetrewardGetState3()
    local hasScore = day7_challenge_data.GetPoint3()
    -- dump(getStates)
    if hasScore>= reward.nNeedScore and getStates[reward.nNeedScore] ~= 1 then
        day7_challenge_data.C2SGetScoreReward(reward.nNeedScore,weekType)
        if lastOne then
            local grade_data = require "grade_data"
            day7_challenge_data.ShowGradeTipWindow(grade_data.REASON_TYPE.day21BigReward)
        end
    else
        if not type or type == item_data.Reward_Type_Enum.Item then
            iui_item_detail.Show(rewardCfg.id, nil, item_data.Item_Show_Type_Enum.Reward_Interface, rewardCfg.num, nil, nil, reward.rewardid)
        else
            local heroData = {}
            table.insert(heroData, {heroSid=0,heroID=rewardCfg.id,starLv=rewardCfg.starLevel})
            local ui_show_hero_card = require "ui_show_hero_card"
            ui_show_hero_card.ShowWithData(ui_show_hero_card.ParseArrHeroList(heroData),nil,nil,nil,true)
            -- icon:DisplayInfo()
        end
    end
end

function M:UpdateTime( )
    if surplusTime >=0 then
        self.time.text = util.FormatTime2(surplusTime, "#D"..lang.Get(36061).."#H:#M:#S")
        surplusTime = surplusTime - 1
    else
        UnloadSelfAndParent()
        event.Trigger(event.SEVEN_RED_DOT)
    end
end

function  M:UpdateRewardState(  )
    local getStates = day7_challenge_data.GetrewardGetState3()
    local hasScore = day7_challenge_data.GetPoint3()
    for i=1,#rewards do  
        if hasScore>= rewards[i].nNeedScore then
            if getStates[rewards[i].nNeedScore] == 1 then
                -- 已领取
                rewards[i].icon:SetBattleMaskEnable(true)
                rewards[i].icon:SetMaskEnable(true)
                self["reward"..i]:Find("canGet").gameObject:SetActive(false)
            else
                -- 
                rewards[i].icon:SetBattleMaskEnable(false)
                rewards[i].icon:SetMaskEnable(false)
                self["reward"..i]:Find("canGet").gameObject:SetActive(true)
            end
            self["reward"..i].transform:Find("lightPoint").gameObject:SetActive(false)
        else
            self["reward"..i].transform:Find("lightPoint").gameObject:SetActive(false)
        end
    end
end

function  M:UpdatePoint( playAnim)
    local hasScore = day7_challenge_data.GetPoint3()
    -- self.progress.value = hasScore/rewards[#rewards].nNeedScore
    -- local hasScore = 80
    local progressValueArr = {0,0,0,0,0}

    local progress_score = hasScore >= 75 and 75 or hasScore
    if progress_score <= 10 then
        progressValueArr =  {hasScore,0,0,0,0}
    elseif progress_score >10 and progress_score <=20 then
        progressValueArr =  {10,progress_score-10,0,0,0}
    elseif progress_score >20 and progress_score <=40 then
        progressValueArr =  {10,10,progress_score-20,0,0}
    elseif progress_score >40 and progress_score <=60 then
        progressValueArr =  {10,10,20,progress_score-40,0}
    elseif progress_score >60 and progress_score <=75 then
        progressValueArr =  {10,10,20,20,progress_score-55}
    end

    for i=1,5 do
        self["progress_"..i].value = progressValueArr[i]
        if self["progress_"..i].value >= self["progress_"..i].maxValue then
            self["textListNum_"..i].color = Color(255/255,229/255,83/255,1)
        else
            self["textListNum_"..i].color = Color(255/255,239/255,245/255,1)
        end
    end
    self.count2:GetComponent(typeof(TextMeshProUGUI)).text = hasScore

    -- local textScore = self.count2:GetComponent(typeof(TextMeshProUGUI)).text
    -- self.countRoot.transform.localPosition = {x = 0, y = 0, z = 0} 
    -- if playAnim then
    --     self.count1.text = textScore
    --     self.count2:GetComponent(typeof(TextMeshProUGUI)).text = hasScore
    --     LeanTween.moveLocalY(self.countRoot.gameObject, 47, 0.4):setEase(LeanTweenType.easeOutSine)
    -- else
    --     self.count1:GetComponent(typeof(TextMeshProUGUI)).text = hasScore
    --     self.count2:GetComponent(typeof(TextMeshProUGUI)).text = hasScore
    -- end
end

function M:UpdateRedDot_single(index )
    local isShowReddot,page = day7_challenge_data.GetRedDotByDay(index+((weekType-1)*7),weekType)
    if isShowReddot and page and not is_set_day then
        is_set_day = true
        first_day = index
        first_page = page
    end    
    self["day"..index.."_red"].gameObject:SetActive(isShowReddot)
end

function  M:UpdateTargetList(type)
    local curDayCfgData = day7_challenge_data.SortCfg_receivedToBottom(lastSelect+((weekType-1)*7),type,weekType)
    self.targetList.data = curDayCfgData	
end

function ShowByParent(_parent,_order)
    parent = _parent
    parent_order = _order + 1
    ui_window_mgr:ShowModule("ui_7day_challenge_three")
end

function SwitchPage(type)
    day7_challenge_data.SetToggleType(type)
    if window and window:IsValid() then
        window:UpdateTargetList(type)
        window.targetList:Refresh(0,-1)
    end
end


local mClass = class(ui_base, nil, M)

function Show()
	if window == nil then
        window = mClass()
        ------  --print("ShowByParent>>>>>>",parent.gameObject.name)
		window._NAME = _NAME;window:LoadUIResource("ui/prefabs/ui7daychallengethree.prefab", nil,parent)
	end
	window:Show()
	return window
end

function Hide()
	if window ~= nil then
		window:Hide()
	end
end

function Close()
	if window ~= nil then
		window:Close()
		window = nil
	end
end


function onItemRenderBottom(scroll_rect_item,index,dataItem)
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem
    if not scroll_rect_item.data["itemArr"] then
        scroll_rect_item.data["itemArr"] = {}
    end
    if not scroll_rect_item.data["rIdArr"] then
        scroll_rect_item.data["rIdArr"] = {}
    end
    day7_challenge_mgr.SetOnItemRenderBottom(scroll_rect_item,index,dataItem,lastSelect)
end

function UnloadSelfAndParent()
    ui_window_mgr:UnloadModule("ui_7day_challenge_three")
    ui_window_mgr:UnloadModule("ui_activity_new_player")
    ui_window_mgr:UnloadModule("ui_7day_challenge_list")
end