--@region FileHead
-- ui_treasure_rare_detail.txt ---------------------------------
-- author:  陈泳冰
-- date:    2022/7/12 20:50:31
-- ver:     1.0
-- desc:    秘宝详情
-------------------------------------------------

--@region Require
local print     = print
local require   = require
local pairs     = pairs
local typeof    = typeof
local string    = string
local type = type
local os = os

local Button        = CS.UnityEngine.UI.Button
local Text          = CS.UnityEngine.UI.Text
local Image         = CS.UnityEngine.UI.Image
local RectTransform = CS.UnityEngine.RectTransform
local VerticalLayoutGroup = CS.UnityEngine.UI.VerticalLayoutGroup

local class                 = require "class"
local ui_base               = require "ui_base"
local module_scroll_list    = require "scroll_list"

local treasure_rare_mgr = require "treasure_rare_mgr"
local game_scheme           = require "game_scheme"
local windowMgr             = require "ui_window_mgr"
local util 			        = require "util"
local time_util 			= require "time_util"
local lang                  = require "lang"
local goods_item            = require "goods_item_new"
local color_palette         = require"color_palette"
local card_sprite_asset     = require "card_sprite_asset"
local ImageGray         = CS.War.UI.ImageGray
local SpriteSwitcher    = CS.War.UI.SpriteSwitcher
local ParticleSystem    = CS.UnityEngine.ParticleSystem
local Vector3           = CS.UnityEngine.Vector3
local Gradient          = CS.War.UI.Gradient
local CanvasGroup	    = CS.UnityEngine.CanvasGroup
local LayoutRebuilder   = CS.UnityEngine.UI.LayoutRebuilder
local Outline           = CS.UnityEngine.UI.Outline
--@region ModuleDeclare
module("ui_treasure_rare_detail")
local window = nil
local UITreasureRareDetail = {}

local TreasureRareID
local IsShare
local ShareLevel
local buyEvent = nil
local shopItemID = nil

local Have = false
local NameColor = {
    [3] = "1F98F9",
    [4] = "D670F8",
    [5] = "FFDF4B",
    [6] = "FB2929",
    [7] = "E1DFF6",
}

--@region WidgetTable
UITreasureRareDetail.widget_table = {
    closeBtn = { path = "closeBtn", type = Button, event_name = "OnBtn_closeBtn" },

    MyCanvasGroup = { path = "", type = CanvasGroup},

    VerticalBackgroundSwitcher = { path = "Background", type = SpriteSwitcher},   --背景换图
    VerticalBackgroundRect = { path = "Background", type = RectTransform},   --排序组件
    VerticalBackground = { path = "Background", type = VerticalLayoutGroup},   --排序组件
    shareBtn = { path = "Background/BaseRoot/shareBtn", type = Button, event_name = "OnshareBtn"},   --分享按钮
    Tname = { path = "Background/BaseRoot/Name", type = Text},   --名字
    TnameGradient = { path = "Background/BaseRoot/Name", type = Gradient},   --名字渐变
    nameBg = { path = "Background/BaseRoot/nameBg", type = RectTransform},   --名字背景

    enhanceMasterBtn = { path = "Background/Background1/IconBg/enhanceMaster", type = Button, event_name = "OnEnhanceMaster"},   --强化大师按钮
    enhanceMasterSW = { path = "Background/Background1/IconBg/enhanceMaster", type = SpriteSwitcher},   --强化大师icon
    TypeText = { path = "Background/Background1/IconBg/Module", type = Text},   --类型
    IconImg = { path = "Background/Background1/IconBg/Icon", type = RectTransform},   --icon
    GradeText = { path = "Background/Background1/IconBg/Grade", type = Text},   -- 品级 档次
    EnhanceText = { path = "Background/Background1/IconBg/enhance", type = Text},   -- 强化等级

    baseProRoot = { path = "Background/Background1/Background2/baseProRoot", type = RectTransform},   -- 基础属性组
    enhanceProRoot = { path = "Background/Background1/Background2/enhanceProRoot", type = RectTransform},   -- 被动属性组

    wakeupTopRoot = { path = "Background/Background1/Background2/wakeupTopRoot", type = RectTransform},   -- 觉醒属性标题
    wakeupTopRootText = { path = "Background/Background1/Background2/wakeupTopRoot/Text", type = Text},   -- 觉醒属性标题
    wakeupProRoot = { path = "Background/Background1/Background2/wakeupProRoot", type = RectTransform},   -- 觉醒属性组

    TimeRoot = { path = "Background/Background1/Background2/Time", type = RectTransform},   -- 剩余时间

    buyButton = {path = "Background/buttonRoot/buyBtn", type = Button},--秘宝购买按钮
    buyNum = {path = "Background/buttonRoot/buyBtn/text", type = Text},--秘宝购买价格
    buyIcon = {path = "Background/buttonRoot/buyBtn/text/icon", type = Image},--秘宝购买图标

    MaxstrengthenBtn = { path = "Background/buttonRoot/MaxstrengthenButton", type = RectTransform},   --强化按钮灰色
    strengthenBtn = { path = "Background/buttonRoot/strengthenButton", type = Button, event_name = "OnstrengthenBtn" },   --强化按钮
    MaxrefinedButton = { path = "Background/buttonRoot/MaxrefinedButton", type = RectTransform},   --进阶按钮灰色
    refinedButton = { path = "Background/buttonRoot/refinedButton", type = Button, event_name = "OnrefinedButton" },   --进阶按钮
    refinedButtonText = { path = "Background/buttonRoot/refinedButton/Text", type = Text},   --进阶文字

    strengthenBtnRed = { path = "Background/buttonRoot/strengthenButton/red", type = Image},   --强化红点
    refinedButtonRed = { path = "Background/buttonRoot/refinedButton/red", type = Image},   --进阶红点

    buttonRoot = { path = "Background/buttonRoot", type = RectTransform},
}

--@region WindowCtor
function UITreasureRareDetail:ctor(selfType)
	self.__base:ctor(selfType)
    self.spriteAsset = card_sprite_asset.CreateSpriteAsset()
end

--@region WindowInit
--[[窗口初始化]]
function UITreasureRareDetail:Init()
    self:SubscribeEvents()
end

--@region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function UITreasureRareDetail:OnShow()
    self:UpdateUIPage()

    if Have then
        self.strengthenBtnRed.gameObject:SetActive(treasure_rare_mgr.SingleTreasureRareStrengthen(TreasureRareID))
        self.refinedButtonRed.gameObject:SetActive(treasure_rare_mgr.SingleTreasureRareRefined(TreasureRareID))
    end
end

--@region WindowOnHide
--[[界面隐藏时调用]]
function UITreasureRareDetail:OnHide()
end

--@region WindowSetInputParam
--[[设置窗口的输入参数。该参数通常是由其它模块或者外部设置进来。需要注意的是，
当调用这个函数的时候，窗口资源可能还是没有加载完成的。
@param p 参数表
]]
function UITreasureRareDetail:SetInputParam(id,share,lv, callBack, itemID)
	TreasureRareID = id
    IsShare = share
    ShareLevel = lv
    buyEvent = callBack
    shopItemID = itemID
    --如果正在显示，则更新一次窗口
    if self.UIRoot and self.UIRoot.activeSelf == true then
        self:UpdateUIPage()
    end
end


--@region WindowBuildUpdateData
--[[构建UI更新数据]]
function UITreasureRareDetail:BuildUpdateData()
    local Tdata = treasure_rare_mgr.GetTreasureRareCfg(TreasureRareID)
    if Tdata == nil then return end
    Have = treasure_rare_mgr.IsOwnTreasureRare(TreasureRareID)
    if Have then
        if Tdata.refineCount == 4 then
            self.refinedButtonText.text = lang.Get(244025)
        else
            self.refinedButtonText.text = lang.Get(244024)
        end

        if Tdata.awaken == 1 then
            self.refinedButton.gameObject:SetActive(false)
            -- self.MaxrefinedButton.gameObject:SetActive(true)
        else
            self.refinedButton.gameObject:SetActive(true)
            self.MaxrefinedButton.gameObject:SetActive(false)
        end
        local level = IsShare and ShareLevel or  treasure_rare_mgr.GetTreasureEnhanceLv(TreasureRareID)
        -- print("强化上限制:", Tdata.refine,"当前等级：",level)
        if level >= Tdata.refine then
            self.strengthenBtn.gameObject:SetActive(false)
            self.MaxstrengthenBtn.gameObject:SetActive(true)
        else
            self.strengthenBtn.gameObject:SetActive(true)
            self.MaxstrengthenBtn.gameObject:SetActive(false)
        end
    end

    local EnhanceNum = IsShare and ShareLevel or  treasure_rare_mgr.GetTreasureEnhanceLv(TreasureRareID)
    local treasureCfg = game_scheme:TreasureRare_0(TreasureRareID)
    if treasureCfg and treasureCfg.prodis1 and treasureCfg.prodis1.data then
        if EnhanceNum >= treasureCfg.prodis1.data[0] and EnhanceNum < treasureCfg.prodis1.data[1] then
            self.enhanceMasterSW:Switch(0)
        elseif EnhanceNum >= treasureCfg.prodis1.data[1] and EnhanceNum < treasureCfg.prodis1.data[2] then
            self.enhanceMasterSW:Switch(1)
        elseif EnhanceNum >= treasureCfg.prodis1.data[2] then
            self.enhanceMasterSW:Switch(2)
        else
            self.enhanceMasterSW:Switch(0)
        end
    end

    -- print("秘宝名字remark:",Tdata.remark)
    self.Tname.text = lang.Get(Tdata.remark)   --名字
    self.Tname.color = color_palette.HexToColor(NameColor[Tdata.colourType])
    if Tdata.colourType == 7 then
        self.TnameGradient.enabled = true
    else
        self.TnameGradient.enabled = false
    end
    
    self.TypeText.text = string.format(lang.Get(5046),lang.Get(244030))   --类型
    if not self.itemIcon then
        self.itemIcon = goods_item.CGoodsItem()
        self.itemIcon:Init(self.IconImg.transform,nil,1)
    end
    self.itemIcon:SetGoods(nil, Tdata.ID)

    self.GradeText.text = lang.Get(5501)..":"..Tdata.colourType --（3.蓝 4.紫 5.橙 6.红 7.白）
    self.VerticalBackgroundSwitcher:SwitchSpriteRenderer(Tdata.colourType >= 6 and 1 or 0)
    self.nameBg.gameObject:SetActive(Tdata.colourType < 6)
    self.EnhanceText.text = string.format(lang.Get(9065),EnhanceNum)  -- 强化等级
    local baseProTexts = self.baseProRoot:GetComponentsInChildren(typeof(Text))
    local y = "EAB527"
    local r = "F6443F"
    local pro2color = y
    local pro3color = r
    if Tdata.colourType == 5 then
        pro2color = y
    else
        pro2color = r
    end
    baseProTexts[1].color =  color_palette.HexToColor(pro2color)
    baseProTexts[2].color =  color_palette.HexToColor(pro3color)
    SetProText(baseProTexts[0],Tdata.pro1,EnhanceNum)
    SetProText(baseProTexts[1],Tdata.pro2,EnhanceNum)
    SetProText(baseProTexts[2],Tdata.pro3,EnhanceNum)

    local enhanceProTexts = self.enhanceProRoot:GetComponentsInChildren(typeof(Text))
    if Tdata.unlockPro4.data and Tdata.unlockPro4.data[0] then
        local desc = string.format(lang.Get(Tdata.unlockProDesc2), treasure_rare_mgr.GetTreasureRarePropDataByid(TreasureRareID))
        --属于需要替换的属性
        enhanceProTexts[0].text = desc
    else
        enhanceProTexts[0].text = lang.Get(Tdata.unlockProDesc2)
    end
    enhanceProTexts[0].gameObject:GetComponent(typeof(Gradient)).enabled = false
    enhanceProTexts[1].gameObject:GetComponent(typeof(Gradient)).enabled = false
    --被动词条评分（1E 2D 3C 4B 5A 6S 7S+）
    local ScrolColor = "<color=#1F98F9>E</color>"
    local ScrolType = Tdata.Score
    if ScrolType == 1 then
        ScrolColor = "<color=#1F98F9>E</color>"
    elseif ScrolType == 2 then
        ScrolColor = "<color=#D670F8>D</color>"
    elseif ScrolType == 3 then
        ScrolColor = "<color=#D670F8>C</color>"
    elseif ScrolType == 4 then
        ScrolColor = "<color=#FFDF4B>B</color>"
    elseif ScrolType == 5 then
        ScrolColor = "<color=#FFDF4B>A</color>"
    elseif ScrolType == 6 then
        ScrolColor = "<color=#FF2B2B>S</color>"
    elseif ScrolType == 7 then
        ScrolColor = "<color=#FFFFFF>S+</color>"
        local rect = enhanceProTexts[0].gameObject:GetComponent(typeof(RectTransform))
        local offset = rect.sizeDelta.y / 28
        self:ShowTopProEffect(enhanceProTexts[0].transform,offset)
        enhanceProTexts[0].gameObject:GetComponent(typeof(Gradient)).enabled = true
        enhanceProTexts[0].gameObject:GetComponent(typeof(Outline)).enabled = true
        enhanceProTexts[1].gameObject:GetComponent(typeof(Gradient)).enabled = true
    end
    enhanceProTexts[1].text  = ScrolColor
    local expiredTime = treasure_rare_mgr.GetTreasureExpiredTime(TreasureRareID)
    local TimeRoots = self.TimeRoot:GetComponentsInChildren(typeof(Text))
    if expiredTime and expiredTime ~= 0 then
        self.TimeRoot.gameObject:SetActive(true)
        -- 计时器S
        if self.timeReset then
            util.RemoveDelayCall(self.timeReset)
        end
        local ctime = os.server_time()
        local lastTime = expiredTime - ctime
        self.timeReset = util.IntervalCall(1,function()
            if window:IsValid() then
                if TimeRoots[0] and not TimeRoots[0]:IsNull() then
                    local timeStr = time_util.FormatTime2(lastTime, "#H:#M:#S")
                    TimeRoots[0].text = string.format(lang.Get(244140),timeStr)
                end
                lastTime = lastTime - 1
                if lastTime <= 0 then
                    if window then
                        windowMgr:UnloadModule("ui_treasure_rare_detail")
                    end
                    util.RemoveDelayCall(self.timeReset)
                end
            end
        end)
        --计时器E
    else
        self.TimeRoot.gameObject:SetActive(false)
    end

    local wakeupProTexts = self.wakeupProRoot:GetComponentsInChildren(typeof(Text))
    if Tdata.awakenPro1 == 0 and Tdata.awakenPro2 == 0 then
        self.wakeupTopRoot.gameObject:SetActive(false)
        self.wakeupProRoot.gameObject:SetActive(false)
    else
        self.wakeupTopRoot.gameObject:SetActive(true)
        self.wakeupProRoot.gameObject:SetActive(true)
    end
    local awakenColor = "F6443F"
    local iswakeupText = lang.Get(244022)
    if Tdata.awaken == 1 then
        iswakeupText = lang.Get(244032)
        awakenColor = "F6443F"
    else
        iswakeupText = lang.Get(244022)
        awakenColor = "8E8E8E"
    end
    self.wakeupTopRootText.text = iswakeupText
    SetProText(wakeupProTexts[0],Tdata.awakenPro1,EnhanceNum,awakenColor)
    SetProText(wakeupProTexts[1],Tdata.awakenPro2,EnhanceNum,awakenColor)
    if IsShare and shopItemID then --商店购买秘宝显示购买按钮
        local itemInfo = game_scheme:Shopping_0(shopItemID)
        if itemInfo then
            -- 设置物品图标
            local item_cfg = game_scheme:Item_0(itemInfo.iItem)
            if item_cfg then
                self.spriteAsset:GetSprite(item_cfg.icon, function(sprite)
                    self.buyIcon.sprite = sprite
                end)
                self.buyNum.text = util.PriceConvert(itemInfo.iValue)
            end
            self.shareBtn.gameObject:SetActive(false)
            self.buttonRoot.gameObject:SetActive(true)
            self.enhanceMasterBtn.gameObject:SetActive(false)
            self.strengthenBtn.gameObject:SetActive(false)
            self.MaxstrengthenBtn.gameObject:SetActive(false)
            self.refinedButton.gameObject:SetActive(false)
            self.MaxrefinedButton.gameObject:SetActive(false)
            self.buyButton.gameObject:SetActive(true)
        end
    else
        self.buyButton.gameObject:SetActive(false)
        if IsShare or (not Have) then
            self.shareBtn.gameObject:SetActive(false)
            self.buttonRoot.gameObject:SetActive(false)
            self.enhanceMasterBtn.gameObject:SetActive(false)
        else
            self.shareBtn.gameObject:SetActive(true)
            if (Tdata.awaken == 1) and (EnhanceNum >= Tdata.refine)then
                self.buttonRoot.gameObject:SetActive(false)
            else
                self.buttonRoot.gameObject:SetActive(true)
            end
            self.enhanceMasterBtn.gameObject:SetActive(true)
        end
    end
    
    if treasureCfg.colourType >= 7 then
        -- 2023/4/10 策划：张寒夫 要求白色秘宝隐藏强化大师功能
        self.enhanceMasterBtn.gameObject:SetActive(false)
    end

    if EnhanceNum < Tdata.prodis1.data[0] then
        self.enhanceMasterBtn:GetComponent(typeof(ImageGray)):SetEnable(true)
    end
    self.VerticalBackgroundRect.gameObject:SetActive(true)
    -- self.VerticalBackground.enabled = true
    util.DelayCall(0.1, function ()
        if window and window:IsValid() then
            window.MyCanvasGroup.alpha = 1
        end
    end)
    
    util.DelayCall(0.1, function ()
        if window and window:IsValid() then
            LayoutRebuilder.ForceRebuildLayoutImmediate(window.VerticalBackgroundRect.gameObject.transform)
        end
    end)
end

function UITreasureRareDetail:ShowTopProEffect(rootTrans, size)
    self.topProEffect = self.topProEffect or {}
    if self.topProEffect[rootTrans] then
        return
    end
    local CModelViewer = require "modelviewer"
    self.topProEffect[rootTrans] = CModelViewer()
    self.topProEffect[rootTrans]:Init(rootTrans.transform, function()
        self.topProEffect[rootTrans]:ShowGameObject("art/effects/prefabs/ui/ui_equipprostar.prefab", function(goEffect)
            goEffect.transform.localPosition = Vector3(-50,0,0)
            local particle = goEffect.transform:GetChild(0):GetComponent(typeof(ParticleSystem))
            particle.shape.position = {x=0, y=-(0.1*(size-1)), z=0}
            particle.shape.scale = {x=5, y=0.25*size, z=1}
        end)
    end)
end

function SetProText(Text,pro,level,color)
    if type(pro) ~= "number" or pro == 0 then
        Text.gameObject:SetActive(false)
        return
    end
    local cfg = treasure_rare_mgr.GetEquipmentProCfg(pro)
    if cfg ~= nil then
        --属性名
        local proCfg = game_scheme:ProToLang_0(cfg.proType)
        local isPercentage = proCfg.isPercentage and proCfg.isPercentage==1
        local percentage = isPercentage and "%" or "" 
        local props = cfg.lvPro.data
        local propValue = props[level==0 and level or level-1]--当前等级对应的数值
        local Pname 
        if proCfg then
            Pname = lang.Get(proCfg.iLangId)--属性名
        else
            Text.gameObject:SetActive(false)
            return
        end
        if color then
            Text.color =  color_palette.HexToColor(color)
        end
        Text.text = Pname..":"..(isPercentage and (propValue / 100) or propValue)..percentage
    end
end

--@region WindowUpdateUI
--[[资源加载完成，被显示的时候调用]]
function UITreasureRareDetail:UpdateUIPage()
	self:BuildUpdateData()
end

--@region WindowClose
function UITreasureRareDetail:Close()
    if self:IsValid() then
		self:UnsubscribeEvents()
	end
    if self.itemIcon then
        self.itemIcon:Dispose()
        self.itemIcon = nil
    end
    if self.topProEffect then
        for k,v in pairs(self.topProEffect) do
            v:Dispose()
            v = nil
        end
        self.topProEffect = nil
    end
    if self.spriteAsset then
        self.spriteAsset:Dispose()
        self.spriteAsset = nil
    end
	self.__base:Close()
    window = nil
end

--@region WindowSubscribeEvents
--[[订阅UI事件]]
function UITreasureRareDetail:SubscribeEvents()
----///<<< Button Proxy Line >>>///-----
    self.OnBtn_closeBtn = function()
        windowMgr:UnloadModule("ui_treasure_rare_detail")
    end
    self.OnEnhanceMaster = function()
        local win =  windowMgr:ShowModule("ui_treasure_rare_enhance_master")
        if win then
            win:SetInputParam(treasure_rare_mgr.GetTreasureEnhanceLv(TreasureRareID),TreasureRareID)
        end
    end
    self.OnshareBtn = function()
		local ui_share = require "ui_share"
        local mq_common_pb       = require "mq_common_pb"
		ui_share.Open(mq_common_pb.enSpeak_ShareTreasureRare, TreasureRareID)
    end

    self.OnstrengthenBtn = function()
        local win = windowMgr:ShowModule("ui_treasure_rare_enhance",function() windowMgr:UnloadModule("ui_treasure_rare_detail") end)
        if win then
            win:SetInputParam(1,TreasureRareID)
        end
    end
    
    self.OnrefinedButton = function()
        local win = windowMgr:ShowModule("ui_treasure_rare_enhance",function() windowMgr:UnloadModule("ui_treasure_rare_detail") end)
        if win then
            win:SetInputParam(2,TreasureRareID)
        end
    end

    self.buyButtonEvent = function()
        if buyEvent then
            buyEvent()
        end
        windowMgr:UnloadModule("ui_treasure_rare_detail")
    end
    self.buyButton.onClick:AddListener(self.buyButtonEvent)
end

--@region WindowUnsubscribeEvents
--[[退订UI事件]]
function UITreasureRareDetail:UnsubscribeEvents()
    if self.timeReset then
        util.RemoveDelayCall(self.timeReset)
    end
    if self.buyButtonEvent then
        self.buyButton.onClick:RemoveAllListeners()
        self.buyButtonEvent=nil
    end
end

--@region WindowBtnFunctions

--@region ScrollItem

--@region WindowInherited
local CUITreasureRareDetail = class(ui_base, nil, UITreasureRareDetail)

--@region ModuleFunction
function Show()
    if window == nil then
        window = CUITreasureRareDetail()
        window._NAME = _NAME
        window:LoadUIResource("ui/prefabs/uitreasureraredetail.prefab", nil, nil, nil)
    end
    window:Show()
    return window
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end

function Close()
    if window ~= nil then
        Hide()
        window:Close()
        window = nil
    end
end

