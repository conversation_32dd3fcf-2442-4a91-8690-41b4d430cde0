local require 		= require
local typeof 		= typeof
local table 		= table

local ui_base 		= require "ui_base"
local class 		= require "class"
local event 		= require "event"
local ui_tips_mgr   = require "ui_tips_mgr"
local hero_lottery_data = require "hero_lottery_data"
local sort_order = require "sort_order"
local goods_item = require("goods_item_new")
local iui_item_detail = require "iui_item_detail"
local item_data = require "item_data"
local LeanTween		= CS.LeanTween
local LeanTweenType = CS.LeanTweenType
local SpriteMask = CS.UnityEngine.SpriteMask

local GameObject 	= CS.UnityEngine.GameObject
local RectTransform = CS.UnityEngine.RectTransform
local Canvas 		= CS.UnityEngine.Canvas

module("ui_reward_tips")
local window = nil
local UIRewardTips ={}
local wardTipsItems = {}
local rewardData = nil
local pos = nil
local pivot = nil

UIRewardTips.widget_table =
{
	spriteMask          = {path = "SpriteMask", type = SpriteMask},
	rewardTipsItem      = {path = "content/items/item_1", type = RectTransform},
	rect      			= {path = "", type = RectTransform},
}

function UIRewardTips:Init()
	self.orderStart, self.orderEnd = sort_order.ApplyBaseIndexs(self,nil, 2)
	self.spriteMask.isCustomRangeActive = true
	self.spriteMask.frontSortingOrder = self.orderStart + 2
	self.spriteMask.backSortingOrder = self.orderStart -1
	ui_tips_mgr.ShowUITip("ui_reward_tips", self.UIRoot:GetComponent(typeof(Canvas)))
	self:SubscribeEvent()
end

--[[资源加载完成，被显示的时候调用]]
function UIRewardTips:OnShow()
	self.__base:OnShow()
	self.spriteMask.isCustomRangeActive = true
	self.spriteMask.frontSortingOrder = self.curOrder + 2
	self.spriteMask.backSortingOrder = self.curOrder -1
	self:ShowRewardTips()
	self.UIRoot.transform.localScale = {x = 0.5, y = 0.5, z = 1};
	LeanTween.scale(self.UIRoot.gameObject, {x = 1, y = 1, z = 1}, 0.2):setEase(LeanTweenType.easeOutBack)
	self.UIRoot.transform.position = pos
	if not rewardData then 
		self.UIRoot.transform.localPosition = {x=self.UIRoot.transform.localPosition.x-70,y=self.UIRoot.transform.localPosition.y-70}
	else
		self.UIRoot.transform.pivot = pivot
		local posX =self.UIRoot.transform.localPosition.x
		if self.UIRoot.transform.pivot.x==1 then
			posX=self.UIRoot.transform.localPosition.x-70
		end
		self.UIRoot.transform.localPosition = {x=posX,y=self.UIRoot.transform.localPosition.y+90}
	end
end

--外部传参设置数据
function SetUIPos(rectRoot,rectPivot)
	--设置UI生成位置
	pos = rectRoot
	pivot = rectPivot
end

function SetRewardData(value)
	if not rewardData then rewardData = {} end
	rewardData = value
end

function UIRewardTips:ShowRewardTips()
	local data = rewardData or hero_lottery_data.GetCurrentStageRewards()
	if data == nil then
		return
	end
	if #wardTipsItems > #data then
		for i = #data + 1, #wardTipsItems do
			wardTipsItems[i].good:Dispose()
			GameObject.Destroy(wardTipsItems[i].item)
			table.remove(wardTipsItems, i)
		end
	else
		for i = #wardTipsItems + 1, #data do
			local go = GameObject.Instantiate(self.rewardTipsItem.gameObject)
			go:SetActive(true)
			go.transform:SetParent(self.rewardTipsItem.parent)
			go.transform.localScale = {x=1.2, y= 1.2, z= 1.2}
			table.insert(wardTipsItems, {item = go})
		end
	end
	for i=1,#data do
		local item = wardTipsItems[i]
		if not wardTipsItems[i].good then
			wardTipsItems[i].good = goods_item.CGoodsItem()
			wardTipsItems[i].good:Init(item.item.transform, function ()
				wardTipsItems[i].good:SetFrameBg(3)
				--wardTipsItems[i].good:SetGoods(nil, data[i].id, data[i].num, OnShowDetail, data[i].rewardid)
			end,0.5)
		else
			--wardTipsItems[i].good:SetGoods(nil, data[i].id, data[i].num, OnShowDetail, data[i].rewardid)
		end
		wardTipsItems[i].good:GoodsEffectEnable(true, self.orderStart, 1,0.5)
		wardTipsItems[i].good:SetGoods(nil, data[i].id, data[i].num, function ()
			iui_item_detail.Show(data[i].id,nil, item_data.Item_Show_Type_Enum.Reward_Interface, data[i].num, nil, nil, data[i].rewardid,nil,nil,nil,function( ... )
				event.Trigger(event.SUPPER_EFFECT_ENABLE,false)
			end,function( ... )
				event.Trigger(event.SUPPER_EFFECT_ENABLE,true)
			end)
		end, data[i].rewardid)
	end
end

function OnShowDetail(entity,id,attachData)
	iui_item_detail.Show(id,nil, item_data.Item_Show_Type_Enum.Reward_Interface, nil, nil, nil, attachData,nil,nil,nil,function( ... )
		event.Trigger(event.SUPPER_EFFECT_ENABLE,false)
	end,function( ... )
		event.Trigger(event.SUPPER_EFFECT_ENABLE,true)
	end)
end


--注册事件
function UIRewardTips:SubscribeEvent()

end

 
--销毁事件
function UIRewardTips:UnsubscribeEvent()

end

function UIRewardTips:Close()
	if self:IsValid() then
		self:UnsubscribeEvent()
	end
	ui_tips_mgr.CloseUITip()
	for i = 1, #wardTipsItems do
		if wardTipsItems[i].good ~= nil then
			wardTipsItems[i].good:Dispose()
			GameObject.Destroy(wardTipsItems[i].item)
		end
	end
	wardTipsItems = {}
	rewardData = nil
	pos = nil
	pivot = nil
	self.__base:Close()
end

function Show()
	if window == nil then
		window = CUIRewardTips()
		window._NAME = _NAME;window:LoadUIResource("ui/prefabs/uirewardtips.prefab", nil,nil, nil)
	end
	
	window:Show()
	return window
end

function Hide()
	if window ~= nil then
		window:Hide()
	end
end

function Close()
	if window ~= nil then
		window:Close()
		window = nil
	end
end

CUIRewardTips = class(ui_base, nil, UIRewardTips)
