-- ui_select_language_impl.txt ------------------------------------------
-- author:  袁森峰
-- date:    2017.12.01
-- ver:     1.0
-- desc:    选择语言实现文件
--------------------------------------------------------------
local require = require
local ipairs = ipairs
local dump = dump
local ui_base = require "ui_base"
local class = require "class"
local Button = CS.UnityEngine.UI.Button
local GameObject = CS.UnityEngine.GameObject
local Text = CS.UnityEngine.UI.Text
local RectTransform = CS.UnityEngine.RectTransform
local typeof = typeof
local table = table
local lang = require "lang"
local mudule_scroll_list = require "scroll_list"
local CScrollList = mudule_scroll_list.CScrollList
local CScrollListItemBase = mudule_scroll_list.CScrollListItemBase
local com_class = require "com_class"
local tonumber = tonumber
local ui_setting_cfg = require "ui_setting_cfg"
local ui_setting_attr_enum = require "ui_setting_attr_enum"
local EnSubModel = ui_setting_attr_enum.EnSubModel
local EnBaseAttrKey = ui_setting_attr_enum.EnBaseAttrKey

module("ui_select_language_impl")
local window = nil
local SelectLanguageUI ={}
--local isFirstSelect = true
local selectedItem = nil

--语种映射，暂时只留中文[1]和英文[3]
local LANG_MAPPING = ui_setting_cfg.LANG_MAPPING

SelectLanguageUI.widget_table = 
{
	titleText = {path = "loginTitle", type = "Text"},

  --关闭
	btnClose = {path = "closeBtn", type = "Button",backEvent = true},
	--btnBg = {path = "btnBg", type = "Button"},
  
   --语言列表
   languageList = {path = "LanguageList", type = RectTransform},
}

local languageListCLS = com_class.CreateClass(CScrollListItemBase)
function languageListCLS:Create(goSkin)
	languageListCLS.__super.Create(self, goSkin)
	
	self.selectLanguageEvent = function()
		if selectedItem ~= nil and selectedItem ~= self then
			selectedItem:FindChild("Highlight").gameObject:SetActive(false)
			selectedItem:FindChild("Normal").gameObject:SetActive(true)
			
			selectedItem = self
			
			selectedItem:FindChild("Normal").gameObject:SetActive(false)
			selectedItem:FindChild("Highlight").gameObject:SetActive(true)
			
			local ui_setting_data = require "ui_setting_data"
			ui_setting_data.SetLanguage(selectedItem.itemLangIndex)
			
--			if true == isFirstSelect then
--				isFirstSelect = false
--			else
--				local windowMgr = require "ui_window_mgr"
--				windowMgr:HideModule("ui_iselect_language")
--			end
			window:Update()
		end
	end

	local normalBtn = self:FindChild("Normal"):GetComponent(typeof(Button))
	normalBtn.onClick:AddListener(self.selectLanguageEvent)

	local highlightBtn = self:FindChild("Highlight"):GetComponent(typeof(Button))
	highlightBtn.onClick:AddListener(self.selectLanguageEvent)
end

function languageListCLS:Destroy()
	local normalBtn = self:FindChild("Normal"):GetComponent(typeof(Button))
	normalBtn.onClick:RemoveListener(self.selectLanguageEvent)

	local highlightBtn = self:FindChild("Highlight"):GetComponent(typeof(Button))
	highlightBtn.onClick:RemoveListener(self.selectLanguageEvent)
	
	self.selectLanguageEvent = nil
	
	languageListCLS.__super.Destroy(self)
end

function languageListCLS:Draw(data,i,list)
	self.itemLangIndex = LANG_MAPPING[i]
	
	self:FindChild("Normal").gameObject:SetActive(selectedItem ~= self)
	
	local normalText = self:FindChild("Normal/Text"):GetComponent(typeof(Text))
	normalText.text = lang.Get(data.key)
	
	self:FindChild("Highlight").gameObject:SetActive(selectedItem == self)
	
	local highlightText = self:FindChild("Highlight/Text"):GetComponent(typeof(Text))
	highlightText.text = lang.Get(data.key)
end


--------注册和销毁事件-------

function SelectLanguageUI:SubscribeEvent()
  window:UnsubscribeEvent()
  self.btnClose.onClick:AddListener(self.BtnCloseEvent)
  --self.btnBg.onClick:AddListener(self.BtnCloseEvent)
end


function SelectLanguageUI:UnsubscribeEvent()
	self.btnClose.onClick:RemoveListener(self.BtnCloseEvent)
	--self.btnBg.onClick:RemoveListener(self.BtnCloseEvent)

end

--关闭按钮触发事件
function SelectLanguageUI:BtnCloseEvent()
	local windowMgr = require "ui_window_mgr"
	windowMgr:HideModule("ui_iselect_language")
end



function  SelectLanguageUI:Update()
	self.titleText.text = lang.Get(lang.KEY_CHANGE_LANGUAGE_TITLE)
	
    local listData = lang.GetLangListData()
    if listData == nil then
		return
	end
	
	--self.languageListInstance:SetListData(listData)
	
	local langData = {}
	for i, langIndex in ipairs(LANG_MAPPING) do
		table.insert(langData, listData[langIndex])
	end
	--dump(langData)
	self.languageListInstance:SetListData(langData)
	
	local ui_setting_data = require "ui_setting_data"
	local selLang = ui_setting_data.GetAttrData(EnSubModel.En_Model_Base, EnBaseAttrKey.En_AttrKey_Lang)
	--self.languageListInstance:SetSelectIndex(tonumber(selLang))
	local selectedIndex = tonumber(selLang)
	for i, langIndex in ipairs(LANG_MAPPING) do
		if langIndex == selectedIndex then
			selectedItem = self.languageListInstance:GetListItem(i)
			selectedItem:Draw(langData[i], i, self.languageListInstance)
			break
		end
	end
end

function SelectLanguageUI:Init()
  self:SubscribeEvent()
  local scrollListLanguage = self.languageList
  if scrollListLanguage then
		self.languageListInstance = CScrollList:CreateInstance({})
		self.languageListInstance:Create(scrollListLanguage, languageListCLS)
		
	end
  self:Update()
end

function SelectLanguageUI:Close()
  if self.UIRoot and self:IsValid() then
		self:UnsubscribeEvent()
		if self.languageListInstance ~= nil then
			self.languageListInstance:ReleaseInstance()
			self.languageListInstance = nil
		end
		
	end

	self.__base:Close()
end


function CloseImpl()
	if window ~= nil then
		window:UnsubscribeEvent()
		window:Close()
		window = nil
--		isFirstSelect = true
	end
end



function ShowImpl()
	if window == nil then
		window = SelectLanguage()
		window._NAME = _NAME;window:LoadUIResource("ui/prefabs/uiselectlanguage.prefab",nil,GameObject.Find("UIRoot/Canvas").transform)
--		isFirstSelect = true
	end
	window:Show()
	return window
end

function HideImpl()
  if window ~= nil then
		window:Hide()
	end
end



SelectLanguage = class(ui_base, nil, SelectLanguageUI)