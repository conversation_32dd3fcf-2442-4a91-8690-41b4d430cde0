----------------------------------------------------------------
--列表配置文件
--
---------------------------------------------------------------
local require = require
local Lang = require "lang_res_key"
local lang = require "lang"
local ui_setting_attr_enum = require "ui_setting_attr_enum"
--local GoodsIcon = require "goods_icon"
local game_config = require "game_config"
local platform_pb = require "platform_pb"
local build_diff_setting = require "build_diff_setting"
local SystemLanguage = CS.UnityEngine.SystemLanguage
local Application = CS.UnityEngine.Application
module("ui_setting_cfg")

local EnSubModel = ui_setting_attr_enum.EnSubModel
local EnBaseAttr<PERSON>ey = ui_setting_attr_enum.EnBaseAttrKey
local EnVoiceAttrKey = ui_setting_attr_enum.EnVoiceAttrKey
-------------------------------------基础设置-----------------------------------------------------------
LANG_MAPPING = { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21 }

LANGUAGE_TYPE = {
    HK = 2, --港澳台地区(主要繁体字)
    EN = 3, --英语地区（美洲包/东南亚包/欧洲包）
    KR = 9, --韩国地区
    JP = 16, --日本地区
    Asia = 17, --亚太地区
    ME = 4, --中东地区
    XMan = 5, --XMan通用
}

LangMapKey = {
    KEY_CHINESE = 1, --中文简体
    KEY_LANGUAGE_FA = 2, --繁体
    KEY_ENGLISH = 3, --英语
    KEY_LANGUAGE_IND = 4, --印尼语
    KEY_LANGUAGE_PI = 5, --菲律宾语
    KEY_LANGUAGE_VI = 6, --越南语
    KEY_LANGUAGE_TH = 7, --泰语
    KEY_LANGUAGE_PO = 8, --葡萄牙
    KEY_LANGUAGE_KO = 9, --韩语
    KEY_LANGUAGE_FR = 10, --法语
    KEY_LANGUAGE_DE = 11, --德语
    KEY_LANGUAGE_MA = 12, --马来西亚语
    KEY_LANGUAGE_RU = 13, --俄语
    KEY_LANGUAGE_ES = 14, --西班牙语
    KEY_LANGUAGE_IN = 15, --印度语
    KEY_LANGUAGE_JA = 16, --日语
    KEY_LANGUAGE_TR = 17, --土耳其语
    KEY_LANGUAGE_IT = 18, --意大利
    KEY_LANGUAGE_PL = 19, --波兰
    KEY_LANGUAGE_AR = 20, --阿拉伯
    KEY_LANGUAGE_NL = 21, --荷兰
}

--每个地区可以设置的语言种类
Languagetype2DisplayMap = {
    [LANGUAGE_TYPE.HK] = { 2, 3, 4, 7, 9, 10, 11, 16 }, --英/繁/韩/日/泰/法/德/印尼语（数值含义可见LangMap语言映射表的key）
    [LANGUAGE_TYPE.EN] = { 1, 2, 3, 4, 7, 8, 9, 10, 11, 14, 16, 17, 18 }, --简体中文、繁体中文、英语、泰语、韩语、日语、法语、德语、土耳其,印尼语,意大利
    [LANGUAGE_TYPE.KR] = { 1, 3, 4, 9, 10, 11, 16 }, --中/英/韩/日/法/德/印尼语
    [LANGUAGE_TYPE.JP] = { 1, 2, 3, 4, 7, 8, 9, 10, 11, 14, 16, 17 }, --中/英/韩/日/法/德/印尼语
    [LANGUAGE_TYPE.Asia] = { 2, 3, 4, 7, 9, 10, 11, 16 }, --亚太包语言入口为繁体中文、韩语、日语、英语、泰语、法语、德语,印尼语
    [LANGUAGE_TYPE.ME] = { 1, 2, 3, 4, 7, 8, 9, 10, 11, 14, 16, 17, 18, 20 }, --中东语言列表   简体中文、繁体中文、英语、泰语、韩语、日语、法语、德语、土耳其,印尼语,意大利,阿拉伯
    [LANGUAGE_TYPE.XMan] = { 3, 8, 10, 11, 14, 18, 19, 21, 1, 2 }  --XMan通用 简体，繁体，英语，葡萄牙，发育，德语，西班牙，意大利，波兰，荷兰
}

function GetLanguageMapping(languageType)
    local map = Languagetype2DisplayMap[languageType]
    if map == nil then
        return LANG_MAPPING
    else
        return map
    end
end

--包跟语言列表的映射
package_languageMap = {
    --["com.xman.sea.gp"] = LANGUAGE_TYPE.IND
}

--获取语言类型（取到的数值是LANGUAGE_TYPE定义的）
function GetLanguageType()
    local log = require "log"
    local const = require "const"
    if Application.isEditor then
        return 5
    end

    local util = require "util"
    local channel_tag = util.GetChannelTag()
    if package_languageMap[channel_tag] then
        return package_languageMap[channel_tag]
    end

    if const.IsHuaWeiChannel() then
        return LANGUAGE_TYPE.HK
    elseif const.IsSEAChannel() or const.IsAmericaChannel() or const.IsEuropeChannel() then
        --美洲包/东南亚包/欧洲包
        -- log.Warning("获取语言类型",LANGUAGE_TYPE.EN)
        return LANGUAGE_TYPE.EN
    elseif const.IsKoreaChannel() then
        --韩国包
        -- log.Warning("获取语言类型",LANGUAGE_TYPE.KR)
        return LANGUAGE_TYPE.KR
    elseif const.IsJapanChannel() then
        --日本包
        -- log.Warning("获取语言类型", LANGUAGE_TYPE.JP)
        return LANGUAGE_TYPE.JP
    elseif const.IsAsiaOfficialChannel() then
        --亚太官网包
        -- log.Warning("获取语言类型",LANGUAGE_TYPE.Asia)
        return LANGUAGE_TYPE.Asia
    elseif build_diff_setting.IsMiddleEastChannel() then
        return LANGUAGE_TYPE.ME--中东
    elseif game_config.LANGUAGE_TYPE then
        --Jenkins有配置
        -- log.Warning("获取语言类型",game_config.LANGUAGE_TYPE)
        return game_config.LANGUAGE_TYPE
    end
    -- log.Warning("获取语言类型",1)
    return 1--默认显示全部语言
end

-- ui_setting_data -> GetPhoneLanguage 获取语言时，会从 LANG_MAPPING 查找，若找不到会默认使用英文，所以这里替换成渠道包定制的语言列表
LANG_MAPPING = GetLanguageMapping(GetLanguageType())


--语言映射表
LangMap = {
    [1] = { Lang = lang.ZH, key = Lang.KEY_CHINESE, platformLangId = platform_pb.enLang_zh, iso = "zh-CN" }, --中文
    [2] = { Lang = lang.FA, key = Lang.KEY_LANGUAGE_FA, platformLangId = platform_pb.enLang_fzh, iso = "zh-TW" }, --繁体
    [3] = { Lang = lang.EN, key = Lang.KEY_ENGLISH, platformLangId = platform_pb.enLang_en, iso = "en" }, --英语
    [4] = { Lang = lang.IND, key = Lang.KEY_LANGUAGE_IND, platformLangId = platform_pb.enLang_yinni, iso = "id" }, --印尼语
    [5] = { Lang = lang.PI, key = Lang.KEY_LANGUAGE_PI, platformLangId = platform_pb.enLang_flb, iso = "tl" }, --菲律宾语
    [6] = { Lang = lang.VI, key = Lang.KEY_LANGUAGE_VI, platformLangId = platform_pb.enLang_yuenan, iso = "vi" }, --越南语
    [7] = { Lang = lang.TH, key = Lang.KEY_LANGUAGE_TH, platformLangId = platform_pb.enLang_ty, iso = "th" }, --泰语
    [8] = { Lang = lang.PO, key = Lang.KEY_LANGUAGE_PO, platformLangId = platform_pb.enLang_pty, iso = "pt" }, --葡萄牙
    [9] = { Lang = lang.KO, key = Lang.KEY_LANGUAGE_KO, platformLangId = platform_pb.enLang_hy, iso = "ko" }, --韩国	
    [10] = { Lang = lang.FR, key = Lang.KEY_LANGUAGE_FR, platformLangId = platform_pb.enLang_fran, iso = "fr" }, --法语
    [11] = { Lang = lang.DE, key = Lang.KEY_LANGUAGE_DE, platformLangId = platform_pb.enLang_germ, iso = "de" }, --德语
    [12] = { Lang = lang.MA, key = Lang.KEY_LANGUAGE_MA, platformLangId = platform_pb.enLang_mala, iso = "ms" }, --马来西亚语
    [13] = { Lang = lang.RU, key = Lang.KEY_LANGUAGE_RU, platformLangId = platform_pb.enLang_ru, iso = "ru" }, --俄语
    [14] = { Lang = lang.ES, key = Lang.KEY_LANGUAGE_ES, platformLangId = platform_pb.enLang_es, iso = "es" }, --西班牙语
    [15] = { Lang = lang.IN, key = Lang.KEY_LANGUAGE_IN, platformLangId = platform_pb.enLang_in, iso = "in" }, --印度语
    [16] = { Lang = lang.JA, key = Lang.KEY_LANGUAGE_JA, platformLangId = platform_pb.enLang_ja, iso = "ja" }, --日语
    [17] = { Lang = lang.TR, key = Lang.KEY_LANGUAGE_TR, platformLangId = platform_pb.enLang_tr, iso = "tr" }, --土耳其语
    [18] = { Lang = lang.IT, key = Lang.KEY_LANGUAGE_IT, platformLangId = platform_pb.enLang_in, iso = "it" }, --意大利
    [19] = { Lang = lang.PL, key = Lang.KEY_LANGUAGE_PL, platformLangId = platform_pb.enLang_ja, iso = "pl" }, --波兰
    [20] = { Lang = lang.AR, key = Lang.KEY_LANGUAGE_AR, platformLangId = platform_pb.enLang_tr, iso = "ar" }, --阿拉伯语
    [21] = { Lang = lang.NL, key = Lang.KEY_LANGUAGE_NL, platformLangId = platform_pb.enLang_nl, iso = "nl" }, --阿拉伯语
}


-- 机器和语言映射表
PhoneLangMap = {
    [1] = { langInfo = LangMap[1], langMapKey = { SystemLanguage.Chinese, SystemLanguage.ChineseSimplified, "zh" } },
    [2] = { langInfo = LangMap[2], langMapKey = { SystemLanguage.Chinese, SystemLanguage.ChineseTraditional, "zh-cn" } },
    [3] = { langInfo = LangMap[3], langMapKey = { SystemLanguage.English, "en" } },
    -- [4] = {langInfo = LangMap[4],langMapKey = {SystemLanguage.Indonesian,"in"}},
    -- [5] = {langInfo = LangMap[5],langMapKey = {"ph"}},
    -- [6] = {langInfo = LangMap[6],langMapKey = {SystemLanguage.Vietnamese,"vn"}},
    [7] = { langInfo = LangMap[7], langMapKey = { SystemLanguage.Thai, "th" } },
    [8] = { langInfo = LangMap[8], langMapKey = { SystemLanguage.Portuguese, "pt" } },
    [9] = { langInfo = LangMap[9], langMapKey = { SystemLanguage.Korean, "ko" } },
    [10] = { langInfo = LangMap[10], langMapKey = { SystemLanguage.French, "fr" } },
    [11] = { langInfo = LangMap[11], langMapKey = { SystemLanguage.German, "de" } },
    -- [12] = {langInfo = LangMap[12],langMapKey = {"ms"}},
    -- [13] = {langInfo = LangMap[13],langMapKey = {SystemLanguage.Russian,"ru"}},
    [14] = { langInfo = LangMap[14], langMapKey = { SystemLanguage.Spanish, "es" } },
    [16] = { langInfo = LangMap[16], langMapKey = { SystemLanguage.Japanese, "ja" } },
    [17] = { langInfo = LangMap[17], langMapKey = { SystemLanguage.Turkish, "tr" } },
    [20] = { langInfo = LangMap[20], langMapKey = { SystemLanguage.Arabic, "ar" } },
    [21] = { langInfo = LangMap[21], langMapKey = { SystemLanguage.Dutch, "nl" } },
}

local login_pb = require "login_pb"
--[[新增：由于客户端和服务器用的枚举不一样，重新定义一下映射]]
LangMapToServer = {
    [1] = login_pb.enLang_ZH, --中文
    [2] = login_pb.enLang_FA, --繁体
    [3] = login_pb.enLang_EN, --英语
    [4] = login_pb.enLang_IND, --印尼语
    [5] = login_pb.enLang_PI, --菲律宾语(机翻)
    [6] = login_pb.enLang_VI, --越南语(机翻)
    [7] = login_pb.enLang_TH, --泰语
    [8] = login_pb.enLang_PO, --葡萄牙语
    [9] = login_pb.enLang_KO, --韩语
    [10] = login_pb.enLang_FR, --法语
    [11] = login_pb.enLang_DE, --德语
    [12] = login_pb.enLang_MA, --马来语
    [13] = login_pb.enLang_RU, --俄语(机翻)
    [14] = login_pb.enLang_ES, --西班牙语
    [15] = login_pb.enLang_IN, --印度语(机翻)
    [16] = login_pb.enLang_JA, --日语
    [17] = login_pb.enLang_TR, --土耳其语(机翻)
    [18] = login_pb.enLang_IT, --意大利(机翻)
    [19] = login_pb.enLang_PL, --波兰(机翻)
    [20] = login_pb.enLang_AR, --阿拉伯(机翻)
    [21] = login_pb.enLang_NL, --荷兰语(机翻)
}

LangAssetName = {
    [1] = { assetBundleName = "gamescp_lang_6", dsrName = "GameScheme_Lang_6.dsr" },
    [2] = { assetBundleName = "gamescp_lang_10", dsrName = "GameScheme_Lang_10.dsr" },
    [3] = { assetBundleName = "gamescp_lang_7", dsrName = "GameScheme_Lang_7.dsr" },
    [4] = { assetBundleName = "gamescp_lang_1", dsrName = "GameScheme_Lang_1.dsr" },
    [5] = { assetBundleName = "gamescp_lang_20", dsrName = "GameScheme_Lang_20.dsr" },
    [6] = { assetBundleName = "gamescp_lang_15", dsrName = "GameScheme_Lang_15.dsr" }, --越南
    [7] = { assetBundleName = "gamescp_lang_2", dsrName = "GameScheme_Lang_2.dsr" },
    [8] = { assetBundleName = "gamescp_lang_13", dsrName = "GameScheme_Lang_13.dsr" },
    [9] = { assetBundleName = "gamescp_lang_11", dsrName = "GameScheme_Lang_11.dsr" },
    [10] = { assetBundleName = "gamescp_lang_3", dsrName = "GameScheme_Lang_3.dsr" },
    [11] = { assetBundleName = "gamescp_lang_4", dsrName = "GameScheme_Lang_4.dsr" },
    [12] = { assetBundleName = "gamescp_lang_5", dsrName = "GameScheme_Lang_5.dsr" },
    [13] = { assetBundleName = "gamescp_lang_8", dsrName = "GameScheme_Lang_8.dsr" },
    [14] = { assetBundleName = "gamescp_lang_9", dsrName = "GameScheme_Lang_9.dsr" },
    [16] = { assetBundleName = "gamescp_lang_12", dsrName = "GameScheme_Lang_12.dsr" },
    [17] = { assetBundleName = "gamescp_lang_14", dsrName = "GameScheme_Lang_14.dsr" },

    [15] = { assetBundleName = "gamescp_lang_19", dsrName = "GameScheme_Lang_19.dsr" },
    [18] = { assetBundleName = "gamescp_lang_16", dsrName = "GameScheme_Lang_16.dsr" },
    [19] = { assetBundleName = "gamescp_lang_17", dsrName = "GameScheme_Lang_17.dsr" },
    [20] = { assetBundleName = "gamescp_lang_18", dsrName = "GameScheme_Lang_18.dsr" },
    [21] = { assetBundleName = "gamescp_lang_18", dsrName = "GameScheme_Lang_18.dsr" },
}

LangFontAssetName = {
    --中文
    ["zh"] = { [1] = { ab = "ui/fonts/fzy4jw.ttf", name = "FZY4JW" },
               [2] = { ab = "ui/fonts/fzy4jw.ttf", name = "FZY4JW" } },
    --繁体
    ["fa"] = { [1] = { ab = "ui/fonts/fzy4jw_2.ttf", name = "FZY4JW_2" },
               [2] = { ab = "ui/fonts/fzy4jw_2.ttf", name = "FZY4JW_2" } },
    --英文
    ["en"] = { [1] = { ab = "ui/fonts/fzy4jw.ttf", name = "FZY4JW" },
               [2] = { ab = "ui/fonts/fzy4jw.ttf", name = "FZY4JW" } },
    --印尼语
    ["ind"] = { [1] = { ab = "ui/fonts/fzy4jw.ttf", name = "FZY4JW" },
                [2] = { ab = "ui/fonts/fzy4jw.ttf", name = "FZY4JW" } },

    --菲律宾语
    ["pi"] = { [1] = { ab = "ui/fonts/fzy4jw.ttf", name = "FZY4JW" },
               [2] = { ab = "ui/fonts/fzy4jw.ttf", name = "FZY4JW" } },
    --泰语
    ["th"] = { [1] = { ab = "ui/fonts/tahoma.ttf", name = "Tahoma" },
               [2] = { ab = "ui/fonts/tahoma.ttf", name = "Tahoma" },
               [3] = { ab = "ui/fonts/tahoma.ttf", name = "Tahoma" } },
    --法语
    ["fr"] = { [1] = { ab = "ui/fonts/fzy4jw.ttf", name = "FZY4JW"},
               [2] = { ab = "ui/fonts/fzy4jw.ttf", name = "FZY4JW"} },
    --德语
    ["de"] = { [1] = { ab = "ui/fonts/fzy4jw.ttf", name = "FZY4JW" },
               [2] = { ab = "ui/fonts/fzy4jw.ttf", name = "FZY4JW" } },
    --马来语
    ["ma"] = { [1] = { ab = "ui/fonts/fzy4jw.ttf", name = "FZY4JW" },
               [2] = { ab = "ui/fonts/fzy4jw.ttf", name = "FZY4JW" } },
    --俄语
    ["ru"] = { [1] = { ab = "ui/fonts/timesnewromanpsboldmt.ttf", name = "Timesnewroman" },
               [2] = { ab = "ui/fonts/timesnewromanpsboldmt.ttf", name = "Timesnewroman" } },
    --西班牙语
    ["es"] = { [1] = { ab = "ui/fonts/fzy4jw_2.ttf", name = "FZY4JW_2", lineSpacing = 0.8  },
               [2] = { ab = "ui/fonts/fzy4jw_2.ttf", name = "FZY4JW_2", lineSpacing = 0.8  } },
    --韩语
    ["kr"] = { [1] = { ab = "ui/fonts/fangzhengzongyi_gbk.ttf", name = "FangZhengZongYi_GBK" },
               [2] = { ab = "ui/fonts/fangzhengzongyi_gbk.ttf", name = "FangZhengZongYi_GBK" } },
    --日语
    ["jp"] = { [1] = { ab = "ui/fonts/jp_black.ttf", name = "JP_black" },
               [2] = { ab = "ui/fonts/jp_black.ttf", name = "JP_black" },
               [3] = { ab = "ui/fonts/jp_black.ttf", name = "JP_black" } },
    --葡萄牙语
    ["po"] = { [1] = { ab = "ui/fonts/fzy4jw_2.ttf", name = "FZY4JW_2", lineSpacing = 0.8 },
               [2] = { ab = "ui/fonts/fzy4jw_2.ttf", name = "FZY4JW_2", lineSpacing = 0.8 } },

    --土耳其语
    ["tr"] = { [1] = { ab = "ui/fonts/fzy4jw.ttf", name = "FZY4JW" },
               [2] = { ab = "ui/fonts/fzy4jw.ttf", name = "FZY4JW" } },

    --越南语
    ["vi"] = { [1] = { ab = "ui/fonts/fzy4jw.ttf", name = "FZY4JW" },
               [2] = { ab = "ui/fonts/fzy4jw.ttf", name = "FZY4JW" },
               [3] = { ab = "ui/fonts/fzy4jw.ttf", name = "FZY4JW" } },
    --意大利语
    ["it"] = { [1] = { ab = "ui/fonts/fzy4jw.ttf", name = "FZY4JW" },
               [2] = { ab = "ui/fonts/fzy4jw.ttf", name = "FZY4JW" } },

    --波兰语
    ["pl"] = { [1] = { ab = "ui/fonts/fzy4jw.ttf", name = "FZY4JW" },
               [2] = { ab = "ui/fonts/fzy4jw.ttf", name = "FZY4JW" } },

    --印度语
    ["in"] = { [1] = { ab = "ui/fonts/fzy4jw.ttf", name = "FZY4JW" },
               [2] = { ab = "ui/fonts/fzy4jw.ttf", name = "FZY4JW" } },

    --阿拉伯语
    ["ar"] = { [1] = { ab = "ui/fonts/ibmarabicbold.ttf", name = "IBMArabicBold" },
               [2] = { ab = "ui/fonts/ibmarabicbold.ttf", name = "IBMArabicBold" },
               [3] = { ab = "ui/fonts/ibmarabicbold.ttf", name = "IBMArabicBold" } },
    --荷兰语
    ["nl"] = { [1] = { ab = "ui/fonts/fzy4jw.ttf", name = "FZY4JW" },
               [2] = { ab = "ui/fonts/fzy4jw.ttf", name = "FZY4JW" } },
}

--tmp字体多语言切换
TMPLangFontAssetName = {
    --中文
    ["zh"] = { [1] = { ab = "ui/fonts/fzy4jw_sdf.asset", name = "FZY4JW_SDF" },
               [2] = { ab = "ui/fonts/clashroyale_sdf.asset", name = "ClashRoyale_SDF" } },
    --繁体
    ["fa"] = { [1] = { ab = "ui/fonts/fzy4jw_sdf.asset", name = "FZY4JW_SDF" },
               [2] = { ab = "ui/fonts/clashroyale_sdf.asset", name = "ClashRoyale_SDF" } },
    --英文
    ["en"] = { [1] = { ab = "ui/fonts/fzy4jw_sdf.asset", name = "FZY4JW_SDF" },
               [2] = { ab = "ui/fonts/clashroyale_sdf.asset", name = "ClashRoyale_SDF" } },
    --印尼语
    ["ind"] = { [1] = { ab = "ui/fonts/fzy4jw_sdf.asset", name = "FZY4JW_SDF" },
                [2] = { ab = "ui/fonts/clashroyale_sdf.asset", name = "ClashRoyale_SDF" } },

    --菲律宾语
    ["pi"] = { [1] = { ab = "ui/fonts/fzy4jw_jp_sdf.asset", name = "FZY4JW_JP_SDF" },
               [2] = { ab = "ui/fonts/fzy4jw_jp_sdf.asset", name = "FZY4JW_JP_SDF" } },
    --泰语
    ["th"] = { [1] = { ab = "ui/fonts/fzy4jw_sdf.asset", name = "FZY4JW_SDF" },
               [2] = { ab = "ui/fonts/clashroyale_sdf.asset", name = "ClashRoyale_SDF" } },
    --法语
    ["fr"] = { [1] = { ab = "ui/fonts/fzy4jw_sdf.asset", name = "FZY4JW_SDF" },
               [2] = { ab = "ui/fonts/clashroyale_sdf.asset", name = "ClashRoyale_SDF" } },
    --德语
    ["de"] = { [1] = { ab = "ui/fonts/fzy4jw_sdf.asset", name = "FZY4JW_SDF" },
               [2] = { ab = "ui/fonts/clashroyale_sdf.asset", name = "ClashRoyale_SDF" } },
    --马来语
    ["ma"] = { [1] = { ab = "ui/fonts/fzy4jw_sdf.asset", name = "FZY4JW_SDF" },
               [2] = { ab = "ui/fonts/clashroyale_sdf.asset", name = "ClashRoyale_SDF" } },
    --俄语
    ["ru"] = { [1] = { ab = "ui/fonts/fzy4jw_sdf.asset", name = "FZY4JW_SDF" },
               [2] = { ab = "ui/fonts/clashroyale_sdf.asset", name = "ClashRoyale_SDF" } },
    --西班牙语
    ["es"] = { [1] = { ab = "ui/fonts/fzy4jw_sdf.asset", name = "FZY4JW_SDF" },
               [2] = { ab = "ui/fonts/clashroyale_sdf.asset", name = "ClashRoyale_SDF" } },
    --韩语
    ["kr"] = { [1] = { ab = "ui/fonts/fzy4jw_sdf.asset", name = "FZY4JW_SDF" },
               [2] = { ab = "ui/fonts/clashroyale_sdf.asset", name = "ClashRoyale_SDF" } },
    --日语
    ["jp"] = { [1] = { ab = "ui/fonts/fzy4jw_jp_sdf.asset", name = "FZY4JW_JP_SDF" },
               [2] = { ab = "ui/fonts/fzy4jw_jp_sdf.asset", name = "FZY4JW_JP_SDF" } },

    --葡萄牙语
    ["po"] = { [1] = { ab = "ui/fonts/fzy4jw_jp_sdf.asset", name = "FZY4JW_JP_SDF" },
               [2] = { ab = "ui/fonts/fzy4jw_jp_sdf.asset", name = "FZY4JW_JP_SDF" } },

    --土耳其语
    ["tr"] = { [1] = { ab = "ui/fonts/fzy4jw_jp_sdf.asset", name = "FZY4JW_SDF" },
               [2] = { ab = "ui/fonts/fzy4jw_jp_sdf.asset", name = "ClashRoyale_SDF" } },

    --越南语
    ["vi"] = { [1] = { ab = "ui/fonts/fzy4jw_sdf.asset", name = "FZY4JW_SDF" },
               [2] = { ab = "ui/fonts/fzy4jw_sdf.asset", name = "FZY4JW_SDF" }, },

    --意大利语
    ["it"] = { [1] = { ab = "ui/fonts/fzy4jw_jp_sdf.asset", name = "FZY4JW_SDF" },
               [2] = { ab = "ui/fonts/fzy4jw_jp_sdf.asset", name = "ClashRoyale_SDF" } },

    --波兰语
    ["pl"] = { [1] = { ab = "ui/fonts/fzy4jw_jp_sdf.asset", name = "FZY4JW_SDF" },
               [2] = { ab = "ui/fonts/fzy4jw_jp_sdf.asset", name = "ClashRoyale_SDF" } },

    --印度语
    ["in"] = { [1] = { ab = "ui/fonts/fzy4jw_jp_sdf.asset", name = "FZY4JW_SDF" },
               [2] = { ab = "ui/fonts/fzy4jw_jp_sdf.asset", name = "ClashRoyale_SDF" } },

    --阿拉伯语
    ["ar"] = { [1] = { ab = "ui/fonts/fzy4jw_jp_sdf.asset", name = "FZY4JW_SDF" },
               [2] = { ab = "ui/fonts/fzy4jw_jp_sdf.asset", name = "ClashRoyale_SDF" } },
    --荷兰语
    ["nl"] = { [1] = { ab = "ui/fonts/fzy4jw_jp_sdf.asset", name = "FZY4JW_SDF" },
               [2] = { ab = "ui/fonts/fzy4jw_jp_sdf.asset", name = "ClashRoyale_SDF" } },
}

LangTypeMap = {
    ["FZY4JW"] = 1,
    ["FZY4JW"] = 2,
    ["ClashRoyale"] = 3,
}

--tmp字体多语言切换
TMPLangTypeMap = {
    ["FZY4JW_SDF"] = 1,
    ["ClashRoyale_SDF"] = 2,
}
EVERSION_TYPE = {
    DOMESTIC = 0, --国内版
    FOREIGN = 1, --国外版
}

--设置等级映射表
LevelMap = {
    [1] = { value = 0, key = Lang.KEY_LOW }, --低
    [2] = { value = 50, key = Lang.KEY_MIDDEL }, --中
    [3] = { value = 100, key = Lang.KEY_HIGH }, --高
}
EnBaseListType = {
    En_ListStyle_Lang = 1,
    En_ListStyle_Draw = 2,
    En_ListStyle_Gyroscope = 3,
}
BaseListDatas = {
    [EnBaseListType.En_ListStyle_Lang] = {
        {
            textKey = Lang.KEY_LANG,
            text2Key = 0,
            attrKey = EnBaseAttrKey.En_AttrKey_Lang,
            bIsShowSlider = false,
            bIsShowDropdown = true,
            arrOption = { LangMap[1].key, LangMap[2].key, LangMap[3].key, LangMap[4].key, LangMap[5].key, LangMap[6].key, LangMap[7].key, LangMap[8].key, LangMap[9].key },
            selOption = 1,
            nValue = 0,
            bEnabled = true,
        },
    },


}

if EVERSION_TYPE.DOMESTIC == game_config.VERSION_TYPE then
    BaseListDatas[EnBaseListType.En_ListStyle_Lang][1].arrOption = { LangMap[1].key }
elseif EVERSION_TYPE.FOREIGN == game_config.VERSION_TYPE then
    BaseListDatas[EnBaseListType.En_ListStyle_Lang][1].arrOption = { LangMap[1].key, LangMap[2].key, LangMap[3].key, LangMap[4].key, LangMap[5].key, LangMap[6].key, LangMap[7].key, LangMap[8].key, LangMap[9].key, }
end

--------------------------------------声音设置------------------------------------------------------
EnVoiceItemStyle = {
    En_Style_Toggle = 1, --勾选风格
    En_Style_Slider = 2, --滑动条风格
}

EnVoiceListType = {
    En_Type_Left = 1, --左列表
    En_Type_Right = 2, --列表
}

VoiceListData = {
    [EnVoiceListType.En_Type_Left] = {
        {
            enStyle = EnVoiceItemStyle.En_Style_Slider,
            attrSwitch = EnVoiceAttrKey.En_AttrKey_MainVolumeSwitch,
            attrKey = EnVoiceAttrKey.En_AttrKey_MainVolume,
            title = Lang.KEY_MAIN_VOLUME,
            value = 0.3,
            isOn = true
        },
        {
            enStyle = EnVoiceItemStyle.En_Style_Slider,
            attrSwitch = EnVoiceAttrKey.En_AttrKey_VoiceEffectSwitch,
            attrKey = EnVoiceAttrKey.En_AttrKey_VoiceEffect,
            title = Lang.KEY_VOICE_EFFECT,
            value = 0.3,
            isOn = true
        },
        {
            enStyle = EnVoiceItemStyle.En_Style_Slider,
            attrSwitch = EnVoiceAttrKey.En_AttrKey_InterfaceSwitch,
            attrKey = EnVoiceAttrKey.En_AttrKey_Interface,
            title = Lang.KEY_INTERFACE,
            value = 0.3,
            isOn = true
        },
        {
            enStyle = EnVoiceItemStyle.En_Style_Slider,
            attrSwitch = EnVoiceAttrKey.En_AttrKey_BGMSwitch,
            attrKey = EnVoiceAttrKey.En_AttrKey_BGM,
            title = Lang.KEY_BACKGROUND_MUSIC,
            value = 0.3,
            isOn = true },
    },
    [EnVoiceListType.En_Type_Right] = {
        {
            enStyle = EnVoiceItemStyle.En_Style_Slider,
            attrSwitch = EnVoiceAttrKey.En_AttrKey_MyHandsetSwitch,
            attrKey = EnVoiceAttrKey.En_AttrKey_MyHandset,
            title = Lang.KEY_MY_HANDSET,
            value = 0.3,
            isOn = true
        },
        {
            enStyle = EnVoiceItemStyle.En_Style_Slider,
            attrSwitch = EnVoiceAttrKey.En_AttrKey_MyMicriphoneSwitch,
            attrKey = EnVoiceAttrKey.En_AttrKey_MyMicriphone,
            title = Lang.KEY_MY_MICRIPHONE,
            value = 0.3,
            isOn = false
        },
    },
}
