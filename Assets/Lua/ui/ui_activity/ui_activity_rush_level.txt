--@region FileHead
-- ui_activity_rush_level.txt ---------------------------------
-- author:  liang<PERSON><PERSON>n 
-- date:    12/9/2019 12:00:00 AM
-- ver:     1.0
-- desc:    冲级活动抽离
-------------------------------------------------


--@region Require
local require   = require
local pairs     = pairs
local table     = table
local tonumber = tonumber

local GameObject    = CS.UnityEngine.GameObject
local Button        = CS.UnityEngine.UI.Button
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable
local SpriteMask = CS.UnityEngine.SpriteMask

local class                 = require "class"
local ui_base               = require "ui_base"
local log                   = require "log"
local activity_mgr = require "activity_mgr"
local game_scheme = require "game_scheme"
local ui_window_mgr = require "ui_window_mgr"
local sort_order = require "sort_order"
local Lang = require "lang"
local goods_item = require "goods_item_new"
local iui_item_detail = require "iui_item_detail"
local item_data = require "item_data"
local util = require 'util'
--@region ModuleDeclare
module("ui_activity_rush_level")
--local interface = require "iui_activity_rush_level"
local window = nil
local UIActivityRushLevel = {}
local activityID = nil
local parent = nil

--@region WidgetTable
UIActivityRushLevel.widget_table = {
	bgImage = {path = "bg", type = "RectTransform"},
	scroll_table = {path = "content/itemlist/Viewport/Content", type = ScrollRectTable},
	spriteMask = {path = "GameObject", type = SpriteMask}
}


--@region WindowCtor
function UIActivityRushLevel:ctor(selfType)
	self.activityID = activity_mgr.rushLevelActivityID
    self.ActivityEntity = {}
    self.contentArr = {}

end 



--@region WindowInit
--[[窗口初始化]]
function UIActivityRushLevel:Init()
    self:SubscribeEvents()
    self.scroll_table.onItemRender= onRenderItem
    self.scroll_table.onItemDispose=function(scroll_rect_item,index)
        if scroll_rect_item.data then
            for k,item in pairs(scroll_rect_item.data["items"]) do
                item:Dispose()
            end
        end
	end
end 



--@region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function UIActivityRushLevel:OnShow()
    self.curOrder = sort_order.ApplyBaseIndexs(self,nil, 2)
	self.spriteMask.isCustomRangeActive = true
    self.spriteMask.frontSortingOrder = self.curOrder + 1
    self.spriteMask.backSortingOrder = self.curOrder -1

    util.DelayCall(0.01,function()
        if self:IsValid() then
            self:UpdateUI()
            local activity_title = require("activity_title")
            self.title = activity_title.CActivityTitle()
            self.title:Init(self.bgImage.gameObject,self.ActivityEntity)
        end
    end)
end 


--@region WindowUpdateUI
--[[资源加载完成，被显示的时候调用]]
function UIActivityRushLevel:UpdateUI()
	self.ActivityEntity = activity_mgr.GetUpgradeData()
	if not self.ActivityEntity then
		return
	end
	local activityConfig = activity_mgr.GetActivityCsvByTypeAndId(self.ActivityEntity.type,self.activityID)
	for i=1,#self.ActivityEntity.content do
		local contentConfig = game_scheme:ActivityContent_0(activityConfig.contentId.data[i-1])
		
		local content = self.ActivityEntity.content[i]
		local cdata = {
			remark = contentConfig.remark,--活动描述
			rewardGroup = activity_mgr.GetActivityContentRewards(contentConfig,self.ActivityEntity.nLevel),--活动奖励rewardID
			activityConditions = contentConfig.conditionTimes,--活动条件
			contentID = content.contentID,--内容id
			value = content.value,--值
		}
		table.insert(self.contentArr, cdata)
	end

	table.sort( self.contentArr, function(a,b)
		local a_progress = a.value / a.activityConditions.data[0]
		local b_progress = b.value / b.activityConditions.data[0]
		if (a_progress >= 1 and b_progress >= 1) or (a_progress < 1 and b_progress < 1) then
			return a.contentID < b.contentID
		else
			return a_progress < 1
		end
    end)
    
    self.scroll_table.data = self.contentArr
    self.scroll_table:Refresh(0,-1)
end 



--@region WindowClose
function UIActivityRushLevel:Close()
    if self:IsValid() then
        self:UnsubscribeEvents()
    end
    self.scroll_table:ItemsDispose()
    parent = nil
	self.__base:Close()
    window = nil


end 



--@region WindowSubscribeEvents
--[[订阅UI事件]]
function UIActivityRushLevel:SubscribeEvents()
----///<<< Button Proxy Line >>>///-----
    self.OnShowDetail = function (entity,id,attachData)
        iui_item_detail.Show(id,nil, item_data.Item_Show_Type_Enum.Reward_Interface, nil, nil, nil, attachData)
    end
end 



--@region WindowUnsubscribeEvents
--[[退订UI事件]]
function UIActivityRushLevel:UnsubscribeEvents()


end 

--============================任务列表===========================================
function onRenderItem(scroll_rect_item,index,dataItem)
	scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem

    scroll_rect_item:Get("title").text = Lang.Get(tonumber(dataItem.remark))

    local targetValue = dataItem.activityConditions.data[0]

    scroll_rect_item:Get("Count").text = dataItem.value.."/"..dataItem.activityConditions.data[0]
    local progress = dataItem.value / tonumber(dataItem.activityConditions.data[0])
	if progress >= 1 then
        progress = 1
        scroll_rect_item:Get("Count").text = dataItem.activityConditions.data[0].."/"..dataItem.activityConditions.data[0]
    end

    scroll_rect_item:Get("ProgressFull").gameObject:SetActive(progress==1)
    scroll_rect_item:Get("CountBg").value = progress
    
    if scroll_rect_item.data["items"] then
		for k,item in pairs(scroll_rect_item.data["items"]) do
			item:Dispose()
		end
	end
    
    scroll_rect_item.data["items"] = {}
    for i=1,dataItem.rewardGroup.count do
		local cfg = game_scheme:Reward_0(dataItem.rewardGroup.data[i])
        if cfg ~= nil then
            scroll_rect_item.data.items["goods"..i] = goods_item.CGoodsItem()
			scroll_rect_item.data.items["goods"..i]:Init(scroll_rect_item:Get("item_"..i-1),function ()
				scroll_rect_item.data.items["goods"..i]:SetGoods(nil, cfg.arrParam[0], cfg.arrParam[1], window.OnShowDetail, cfg.iRewardID)
				scroll_rect_item.data.items["goods"..i]:SetBattleMaskEnable(progress >= 1)
				scroll_rect_item.data.items["goods"..i]:SetMaskEnable(progress >= 1)
				scroll_rect_item.data.items["goods"..i]:GoodsEffectEnable(true, window.curOrder, 1,0.6)
			end,0.8)
		else
			log.Error("Reward表中找不到 id = "..dataItem.rewardGroup.data[i].."，请检查相关配置")
        end
    end
end



--@region WindowBtnFunctions


--@region ScrollItem


--@region WindowInherited
local CUIActivityRushLevel = class(ui_base, nil, UIActivityRushLevel)


--@region ModuleFunction
function Show()
    if window == nil then
        window = CUIActivityRushLevel()
        window._NAME = _NAME;window:LoadUIResource("ui/prefabs/uiactivityrushlevel.prefab", nil, parent, nil, nil, true)
    end
    window:Show()
    return window
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end

function Close()
    if window ~= nil then
        Hide()
        window:Close()
        window = nil
    end
end

function ShowByParent(_parent)
    parent = _parent
    ui_window_mgr:ShowModule("ui_activity_rush_level")
end

--@region RegisterMsg


