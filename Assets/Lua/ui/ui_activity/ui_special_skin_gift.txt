--@region FileHead
-- ui_special_skin_gift.txt ---------------------------------
-- author:  曾琳琳 ==> (liangqixian)
-- date:    1/20/2021 10:10:13 AM
-- ver:     1.0
-- desc:    虫母皮肤礼包弹窗 ==> 特惠礼包
-------------------------------------------------
--@endregion 

--@region Require
local print     = print
local require   = require
local pairs     = pairs
local string    = string
local tonumber  = tonumber
local os        = os
local coroutine = coroutine

local Button        = CS.UnityEngine.UI.Button
local Text          = CS.UnityEngine.UI.Text
local ImageGray 	= CS.War.UI.ImageGray
local LeanTween     = CS.LeanTween

local base_object           = require "base_object"
local event					= require "event"
local class                 = require "class"
local ui_base               = require "ui_base"
local module_scroll_list    = require "scroll_list"
local lang                  = require "lang"
local util                  = require "util"
local activity_mgr          = require "activity_mgr"
local goods_item            = require "goods_item_new"
local item_data             = require "item_data"
local model_item            = require "model_item"
local model_res             = require "model_res"
local game_scheme           = require "game_scheme"
local windowMgr             = require "ui_window_mgr"
local iui_item_detail       = require "iui_item_detail"
local controller_skill_preview = require "controller_skill_preview"
local game_config           = require "game_config"
-- local festival_activity_mgr = require "festival_activity_mgr"
local mall_cfg              = require "mall_cfg"
local special_gift_mgr = require "special_gift_mgr"

module("ui_special_skin_gift")
local window = nil
local UISkinGift = {}
local groupModel = nil
local uiAnimationData = 
{
    scaleCenterPosition = nil,-- UI界面关闭等状态动画配置数据
}
local assetBundleName = "art/battleplayer/herocardview.prefab"
local rechargeID = nil
local isShowViewBtn = true

local curGiftID = nil
local curIndexID = nil
local curIndex = nil--当前选中第几个礼包
local parentUI = nil
local paerentSortOder = nil--上一级层级
local delayTimeId = nil

local postionY_offset = 150

UISkinGift.widget_table = {
    itemlist = {path = "bg/itemlist", type = "RectTransform"},
    buyBtn = {path = "bg/anniu/buyBtn", type = "Button",event_name = "BuyButtonHandler"},
    buyBtnText = {path = "bg/anniu/num", type = "Text"},
    vertRect = {path = "bg/anniu/vert", type = "RectTransform"},
    prePriceText = {path = "bg/anniu/vert/pre", type = "Text"},
    nowPriceText = {path = "bg/anniu/vert/now", type = "Text"},
    saleRect = {path = "bg/sale", type = "RectTransform"},
    discountText = {path = "bg/sale/off", type = "Text"},
    closeBtn = {path = "closeBtn", type = "Button", event_name = "CloseButtonHandler",backEvent = true},
    blankCloseBtn = {path = "bg/blankCloseBtn", type = "Button", event_name = "CloseButtonHandler"},
    bgImage = {path = "bg/Bg", type = "RawImage"},
    rawImage = {path = "bg/Bg/heroImage", type = "RawImage"},
    butBtnGray = {path = "bg/anniu/bg", type = ImageGray},
    bg = { path = "bg", type = "RectTransform", },

    title_zh = {path = "bg/title_zh", type = "RectTransform"},
    title_en = {path = "bg/title_en", type = "RectTransform"},
    img_zh = {path = "bg/title_zh/title", type = "RawImage"},
    img_en = {path = "bg/title_en/title", type = "RawImage"},
    viewBtn = {path = "bg/viewBtn", type = "Button",event_name = "ViewBtnHandler"},
    countDownText = {path = "bg/Text", type = "Text"},

    --用于移动位置
    rect_sale = {path = "bg/sale", type = "RectTransform"},
    rect_countDownText = {path = "bg/Text", type = "RectTransform"},
    rect_anniu = {path = "bg/anniu", type = "RectTransform"},
    rect_skillview = {path = "bg/viewBtn", type = "RectTransform"},
    rect_jiangliBg = {path = "bg/jiangliBg", type = "RectTransform"},
    rect_itemlist = {path = "bg/itemlist", type = "RectTransform"},
}
--@endregion 

--@region WindowCtor
function UISkinGift:ctor(selfType)
	self.__base:ctor(selfType)
    self.activityID = nil
    self.activityCfg = nil
    self.ActivityEntity = {}
    self.RmbCostID = nil
    self.iPrice = nil--价格（RMB）
    self.iOldPrice = nil--原价（RMB）
    self.GoodsUI = {}
    self.topicID = nil
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowInit
--[[窗口初始化]]
function UISkinGift:Init()
    self:SubscribeEvents()
    self.title_zh.gameObject:SetActive(lang.USE_LANG == lang.ZH)
    self.title_en.gameObject:SetActive(lang.USE_LANG ~= lang.ZH)

    self.buyBtn.interactable =true
    self.vertRect.gameObject:SetActive(true)
    self.saleRect.gameObject:SetActive(true)
    self.buyBtnText.gameObject:SetActive(false)

    self.rect_sale.localPosition = {x=self.rect_sale.localPosition.x,y=self.rect_sale.localPosition.y+postionY_offset+20}
    self.rect_countDownText.localPosition = {x=self.rect_countDownText.localPosition.x,y=self.rect_countDownText.localPosition.y+postionY_offset+20}
    self.rect_anniu.localPosition = {x=self.rect_anniu.localPosition.x,y=self.rect_anniu.localPosition.y+postionY_offset+20}
    self.rect_skillview.localPosition = {x=self.rect_skillview.localPosition.x,y=self.rect_skillview.localPosition.y+postionY_offset}
    self.rect_jiangliBg.localPosition = {x=self.rect_jiangliBg.localPosition.x,y=self.rect_jiangliBg.localPosition.y+postionY_offset}
    self.rect_itemlist.localPosition = {x=self.rect_itemlist.localPosition.x,y=self.rect_itemlist.localPosition.y+postionY_offset}
--@region User
--@endregion 
end 

--[[设置英雄模型]]
function UISkinGift:LoadHeroModel(cfg_model)
    if groupModel then
        self.rawImage.color = {r=self.rawImage.color.r, g= self.rawImage.color.g, b= self.rawImage.color.b, a= 1}
        return
    end
    local res = model_res.GetResPath(cfg_model.modelPath)
    if not self.model then
        self.model = model_item.CModelItem():Init(assetBundleName,{res},function(_rt)
            if self:IsValid() then
                self.rawImage.texture = _rt
                self.model:SetBackgroundActive(1)
                self.model:SetMaskActive(1,0)
                --self.model:SetNodeAngle()
				self.rawImage.color = {r=self.rawImage.color.r, g= self.rawImage.color.g, b= self.rawImage.color.b, a= 1}
            end
        end)
    else
        self.model:ChangeModel(1,cfg_model.modelPath)
    end
end

--初始化奖励
function UISkinGift:InitRewardData()
    if not self.activityCfg then return end
    local reward = util.SplitString(self.activityCfg.rewardGroup, "#", tonumber)
    local length = #reward
    for i=1, length do
        local cfg = game_scheme:Reward_0(reward[i])
        if cfg then
            if not self.GoodsUI[i] then
                self.GoodsUI[i] = goods_item.CGoodsItem():Init(self.itemlist.transform,nil,0.7)
            end
            self.GoodsUI[i]:SetGoods(nil, cfg.arrParam[0], cfg.arrParam[1], self.OnShowDetail, cfg.iRewardID)
            self.GoodsUI[i]:SetEquipLvTxt(cfg.arrParam[3])
            self.GoodsUI[i]:GoodsEffectEnable(true, paerentSortOder, 1, 0.7)
            if cfg.arrParam[0] == 2 then
                self.GoodsUI[i]:SetCountEnable(util.PriceConvertGiftDiamond(cfg.arrParam[1]))
            end
        end
	end
end


--[[资源加载完成，被显示的时候调用]]
function UISkinGift:OnShow()
    paerentSortOder = paerentSortOder or self.curOrder
    self:UpdateUIPage()
end 

--[[界面隐藏时调用]]
function UISkinGift:OnHide()

end 

function UpdateUI()
end

--[[设置窗口的输入参数。该参数通常是由其它模块或者外部设置进来。需要注意的是，
当调用这个函数的时候，窗口资源可能还是没有加载完成的。
@param p 参数表
]]
function UISkinGift:SetInputParam(p)
	self.inputParam = p

    --如果正在显示，则更新一次窗口
    if self.UIRoot and self.UIRoot.activeSelf == true then
        self:UpdateUIPage()
    end
end

--[[构建UI更新数据]]
function UISkinGift:BuildUpdateData()
    local curSpecialGiftCfg = special_gift_mgr.GetCurSpecialGiftCfg()
    local giftData,taskData = special_gift_mgr.GetSpecialGiftData()
    self.SurplusTime =  taskData[curIndex].time
    if not curIndex then curIndex = 1 end
    if giftData then
        for i,v in pairs(giftData) do
            if curIndex and i == curIndex then
                curSpecialGiftCfg = v
                break
            end
        end
    end
    self.specialGiftConfig = curSpecialGiftCfg
end 

--[[资源加载完成，被显示的时候调用]]
function UISkinGift:UpdateUIPage()
    self:BuildUpdateData() 
    local cfg = game_scheme:GiftPopUp_0(9)
    if not cfg then return end
    local modelID = cfg.Param.data[0] or 3010
    local cfg_model = game_scheme:Modul_0(modelID)
    self:LoadHeroModel(cfg_model)--英雄模型

    if self.specialGiftConfig then
        self.activityID = self.specialGiftConfig.rewardID.data[0]
        self.activityCfg = game_scheme:ActivityContent_0(self.activityID)

        self.discountText.text=self.specialGiftConfig.GiftValue

        rechargeID = activity_mgr.GetRechargeID(self.activityCfg)
        self:InitRewardData()--奖励
        local rechargeCfg = rechargeID and game_scheme:Recharge_0(rechargeID)
        self.RmbCostID = rechargeID
        self.iPrice = rechargeCfg and rechargeCfg.iPrice
        local net_recharge_module = require "net_recharge_module"
        local originPriceStr = ""
        if game_config.Q1SDK_DOMESTIC then
            originPriceStr = self.specialGiftConfig.OldPrice
        else
            local recharge_data = require "recharge_data"
            originPriceStr = string.format("%.2f", recharge_data.GetExchangeCfgYuanByCurrencyType(self.specialGiftConfig.OldPrice*100))
        end
        self.nowPriceText.text = net_recharge_module.GetMoneyStrByGoodsID(rechargeID)
        self.prePriceText.text = string.format("%s%s",net_recharge_module.GetMoneyTypeByGoodId(rechargeID),originPriceStr) 
        -- print(396,"特惠礼包 ：原价>>>",originPriceStr,"/",net_recharge_module.GetMoneyStrByGoodsID(rechargeCfg.iGoodsID))
    end


    self:StartCountDown(self.SurplusTime)
end 

 --[[刷新倒计时]]
 function UISkinGift:StartCountDown(time)
	if self.refreshTimer then
		self.refreshTimer:Dispose()
		self.refreshTimer = nil
	end
	local remainTime = time - os.server_time()
    self.refreshTimer = base_object()
    
	self.refreshTimer:CreateTimeTicker(0, function()
		local last = os.server_time()
		while true do
			local now = os.server_time()
			local pass = now - last
			local t = remainTime - pass	
			self.countDownText.text = string.format(lang.Get(9251), mall_cfg.GetTimeStr(t))
			if t <= 0 then
				break
			end
			coroutine.yield(1)
        end
        event.Trigger(event.ACTIVITY_TIME_UP)
		if self.refreshTimer then
			self.refreshTimer:Dispose()
			self.refreshTimer = nil
		end
	end)
end

function SetScaleCenterPos(position)
    uiAnimationData.scaleCenterPosition = position
end

function UISkinGift:Close()
    if self:IsValid() then
        self:UnsubscribeEvents()
        self.activityID = nil
        if self.rawImage then
            self.rawImage.color = {r=self.rawImage.color.r, g= self.rawImage.color.g, b= self.rawImage.color.b, a= 0}
        end
        if self.model then
            self.model:Dispose()
            self.model = nil
        end
        rechargeID = nil
        if self.GoodsUI then
            for i,v in pairs(self.GoodsUI) do
                v:Dispose()
                v = nil
            end
            self.GoodsUI = {}
        end
        if uiAnimationData.scaleCenterPosition then
            uiAnimationData.scaleCenterPosition = nil
        end
        if self.refreshTimer then
            self.refreshTimer:Dispose()
            self.refreshTimer = nil
        end
        curGiftID = nil
        curIndex = nil
        curIndexID = nil
        parentUI = nil
        paerentSortOder = nil--上一级层级
	end
	self.__base:Close()
    window = nil
	event.Trigger(event.HALL_SCENE_SILDING,true)
--@region User
--@endregion 
end 

function C2SContentRmbBuyREQ(iGoodsID,iPrice)
    local net_recharge_module = require "net_recharge_module"
    local ui_select_diamond = require "ui_select_diamond"
    if not ui_select_diamond.AccountIsBinded() then return end
    net_recharge_module.Send_New_Recharge_REQ(iGoodsID, 1, iPrice)
end


--[[订阅UI事件]]
function UISkinGift:SubscribeEvents()
    self.CloseButtonHandler = function()
        if not parentUI then
            windowMgr:UnloadModule("ui_special_skin_gift")
        end
    end

    self.BuyButtonHandler = function()    
        C2SContentRmbBuyREQ(self.RmbCostID,self.iPrice)
        windowMgr:UnloadModule("ui_specialg_gift_array")
    end

    self.OnShowDetail = function (entity,id,attachData)
		iui_item_detail.Show(id,nil, item_data.Item_Show_Type_Enum.Reward_Interface, nil, nil, nil, attachData)
    end

    self.ViewBtnHandler = function()
        --TODO
        local lastIndex = curIndex
        if parentUI then
            controller_skill_preview.ShowChongmuSkin_SpecialGift(function()
                local ui_specialg_gift_array = require "ui_specialg_gift_array"
                ui_specialg_gift_array.PreOpenPage(lastIndex)
                windowMgr:ShowModule("ui_specialg_gift_array")
            end)
        else
            controller_skill_preview.ShowChongmuSkin_SpecialGift(function()
                local ui_special_skin_gift = require "ui_special_skin_gift"
                ui_special_skin_gift.SetCurIndex(lastIndex)
                windowMgr:ShowModule("ui_special_skin_gift")
            end)
        end
    end
end

--[[退订UI事件]]
function UISkinGift:UnsubscribeEvents()
    event.Unregister(event.UPDATE_FESTIVAL_DATA,self.OnActivityContentUpdate)
end

local CUISkinGift = class(ui_base, nil, UISkinGift)
--@endregion 

--@region ModuleFunction
function Show()
    if window == nil then
        window = CUISkinGift()
        window._NAME = _NAME
        window:LoadUIResource("ui/prefabs/uiskingift.prefab", nil, parentUI, nil)
    end
    window:Show()
	event.Trigger(event.HALL_SCENE_SILDING,false)
    return window
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end

function Close()
    if window ~= nil and window:IsValid() and not window.UIRoot:IsNull() then
        if uiAnimationData.scaleCenterPosition then
            LeanTween.cancel(window.UIRoot)
            windowMgr:AnimateScaleClose(uiAnimationData.scaleCenterPosition, window.bg.transform, function ()
                if window then
                    window:Close()
                    window = nil
                end
            end)
        else
            window:Close()
            window = nil
        end
	end
end

function SetCurIndex(v)
    curIndex = v
end

function SetPearent(_trans,_sortoder)
    parentUI = _trans
    paerentSortOder = _sortoder + 1
end


--换区事件
function ClearDataOnChangeWorld( )
    windowMgr:UnloadModule("ui_special_skin_gift")
end
event.Register(event.ACCOUNT_CHANGE_WORLD_RSP,ClearDataOnChangeWorld)

