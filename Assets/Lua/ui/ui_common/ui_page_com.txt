-- ui_page_com.txt ------------------------------------------
-- author:  云佳杰
-- date:    2022.02.18
-- ver:     1.0
-- desc:    翻页功能(目前只有水平翻页，如果有竖直翻页功能，需要自己加)
-- desc:    页码一般较少，暂时没考虑做复用

---1.默认以content下面的第一个子物体为原型，进行copy
---2.默认原型为第一页，往右页数递增
---3.默认content的Anchors（0，1， 0， 1）
---4.item的Anchors是左边
--------------------------------------------------------------

local print     = print
local require   = require
local pairs     = pairs
local ipairs    = ipairs
local typeof    = typeof
local tostring  = tostring
local math      = math

local GameObject    = CS.UnityEngine.GameObject
local Vector2       = CS.UnityEngine.Vector2
local RectTransform     = CS.UnityEngine.RectTransform
local CScrollRect       = typeof(CS.War.UI.CScrollRect)
local LeanTween         = CS.LeanTween
local EventTriggerListener  = typeof(CS.War.UI.EventTriggerListener)

local class                 = require "class"
local log                   = require "log"

module("ui_page_com")

---@class ui_page_com
---@field CUIPageCom function 
---@field Init function 初始化page
---@field ScrollTo function 滑动到指定页码
---@field Dispose function 回收

---@class ui_page_item
---@field item RectTransform 当前item
---@field pageIndex number 页码(等同于数据索引)
---@field dependItem RectTransform 依赖的item，用于位置计算
---@field posDiff number 当前移动多少距离，用于复用计算

local UIPageCom = {}

---@see 渲染函数直接赋值
UIPageCom.onItemRender = nil
---@see 页签改变
UIPageCom.onPageChanged = nil
---@see 开始拖拽
UIPageCom.onStartDrag = nil
UIPageCom.onFinishDrag = nil

---@see 初始化page
---@param scrollRoot CScrollRect节点
---@param data 页码数据
---@param space content间距默认为10
function UIPageCom:Init(scrollRoot, data, space)
    self.cScrollRect = scrollRoot.gameObject:GetComponent(CScrollRect)
    -- self.dragCom = scrollRoot.gameObject:GetComponent(EventTriggerListener)
    -- if self.dragCom == nil then
    --     self.dragCom = scrollRoot.gameObject:AddComponent(EventTriggerListener)
    -- else        
    --     self.dragCom.onBeginDrag:RemoveAllListeners()
    --     self.dragCom.onDrag:RemoveAllListeners()
    -- end

    self.cScrollRect.decelerationRate = 0
    self.space = space or 10
    self.data = data

    ---@see 页码总数
    self.pageCount = #data
    self.curPage = 1

    ---@see 当前copy出来的预制体需要删除
    ---@type ui_page_item[]
    self.allPrefabs = {}
    
    self.rectContent = self.cScrollRect.content

    --默认以content下面的第一个子物体为原型，进行copy
    local prefabTrans = self.rectContent:GetChild(0)
    if prefabTrans == nil then
        log.Error("scrollRoot组件没有找到prefab！")
        return
    end
    local prefabObj = prefabTrans.gameObject
    if prefabObj.activeSelf == false then
        prefabObj:SetActive(true)
    end
    self.prefab = prefabObj:GetComponent("RectTransform")


    if self.cScrollRect.horizontal then
        self.prefab.anchoredPosition = Vector2.zero

        self.itemWidth = self.prefab.sizeDelta.x

        local copyPrefab = function(posX, pageIndex)
            local copyPrefab = GameObject.Instantiate(self.prefab, self.rectContent):GetComponent("RectTransform")
            copyPrefab.name = tostring(pageIndex)
            copyPrefab.anchoredPosition = {x = posX, y = 0}
            ---@type ui_page_item
            local item = {}
            item.item = copyPrefab
            item.pageIndex = pageIndex
            item.posDiff = 0
            self.allPrefabs[pageIndex] = item

            if self.onItemRender ~= nil then
                self.onItemRender(item, self.data[pageIndex])
            else
                print("kasug---没有函数")
            end
        end

        -- 计算位置对应pageIndex
        self.pagePos = {}

        local halfWidth = self.itemWidth * 0.5
        for index = 1, self.pageCount do
            local minusIndex = index - 1
            local pos = minusIndex * self.itemWidth + halfWidth +  minusIndex * self.space
            copyPrefab(pos, index)
            self.pagePos[index] = -(minusIndex * self.itemWidth +  minusIndex * self.space)
        end

        -- content宽度
        local factor = self.pageCount
        local contentWidth = self.itemWidth * factor + (factor - 1) * self.space
        self.rectContent.sizeDelta = {x = contentWidth , y = self.rectContent.sizeDelta.y}


        self.beginPoxX = 0        
        self.onBeginDrag = function()
            self.beginPoxX = self.rectContent.anchoredPosition.x
            if self.onStartDrag then
                self.onStartDrag()
            end
        end

        self.onEndDrag = function()
            local curPos = self.rectContent.anchoredPosition.x
            
            -- UI往左移是-1
            local posDiff = self.beginPoxX - curPos
            local direction = posDiff > 0 and -1 or 1

            local toPage = -1
            if self.curPage <= 1 and direction == 1 then
                toPage = 1
            elseif self.curPage >= self.pageCount and direction == -1 then
                toPage = self.pageCount
            end
            if toPage > 0 then
                self:ScrollTo(toPage)
                return
            end

            if curPos <= self.pagePos[self.pageCount] then
                toPage = self.pageCount
            elseif curPos >= self.pagePos[1] then
                toPage = 1
            else
                for index = 1, self.pageCount - 1 do
                    local lastPos = self.pagePos[index]
                    local nextPos = self.pagePos[index + 1]
                    -- 靠近谁就滑向谁
                    if curPos < lastPos and curPos > nextPos then
                        if math.abs(curPos - lastPos) > math.abs(curPos - nextPos) then
                            toPage = index + 1
                        else
                            toPage = index
                        end
                        break
                    end
                end
                -- 保证滑动效果，轻微滑动也要滑向下一个目标
                if math.abs(posDiff) <= 25 then
                    toPage = self.curPage                
                elseif toPage == self.curPage then
                    if direction == -1 then
                        toPage = self.curPage + 1
                    else
                        toPage = self.curPage - 1
                    end
                end
            end

            self:ScrollTo(toPage)
            if self.onFinishDrag then
                self.onFinishDrag()
            end
        end

        -- self.dragCom:onDown('+', self.onBeginDrag)
        -- self.dragCom:onUp('+', self.onEndDrag)

        self.cScrollRect.onBeginDrag = self.onBeginDrag
        self.cScrollRect.onEndDrag = self.onEndDrag
   
    else 
        log.Warning("暂时不支持竖直翻页！")
    end

    --隐藏原型
    prefabObj:SetActive(false)

    return self
end

function UIPageCom:SetMoveTime(duration)
    self.duration = duration
end
---@see 滚动到页码
function UIPageCom:ScrollTo(pageIndex)
    if pageIndex < 0 or pageIndex > self.pageCount then return end
    local pos = self.pagePos[pageIndex]
    -- self.rectContent.anchoredPosition = {x = pos, y = self.rectContent.anchoredPosition.y}

    local tweenComplete = function()
        self.curPage = pageIndex
        if self.onPageChanged then
            self.onPageChanged(pageIndex)
        end
    end
    LeanTween.moveX(self.rectContent, pos, self.duration or 0.25):setOnComplete(tweenComplete)
    
end

function UIPageCom:NextPage()
    local page = self.curPage + 1
    self:ScrollTo(page)
end

function UIPageCom:LastPage()
    local page = self.curPage - 1
    self:ScrollTo(page)
end

---@see 刷新所有数据
function UIPageCom:RefreshAll(data)
    if data then
        self.data = data
    end
    for pageIndex, item in ipairs(self.allPrefabs or {}) do
        self.onItemRender(item, self.data[pageIndex])
    end
end

function UIPageCom:Dispose()
    for _,item in pairs(self.allPrefabs or {}) do
        if item.gameObject then
            GameObject.Destroy(item.gameObject)
        end
    end
    -- self.dragCom:onDown('-', self.onBeginDrag)
    -- self.dragCom:onUp('-', self.onEndDrag)

    self.cScrollRect.onBeginDrag = nil
    self.cScrollRect.onEndDrag = nil

    self.allPrefabs = nil

    self.cScrollRect.onValueChanged:RemoveAllListeners()
    self.cScrollRect = nil
    
    self.data = nil

    self.onItemRender = nil
end

function UIPageCom:GetSectionNumber(num, resetValue)
    num = math.mod(num, 4)
    if num == 0 then
        num = resetValue
    end

    return num
end

function UIPageCom:GetCurItem(curPage)
    return self.allPrefabs[curPage].item
end

local class = require "class"
local object = require "object"
CUIPageCom = class(object, nil, UIPageCom)