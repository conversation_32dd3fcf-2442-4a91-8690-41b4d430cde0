-- .txt ------------------------------------------
-- author:  黄君睿
-- date:    2022.6.8
-- ver:     1.0
-- desc:    选择遗物公共组件
--------------------------------------------------------------
local require = require
local typeof = typeof
local table = table
local ipairs = ipairs
local pairs = pairs
local tonumber = tonumber
local string = string

local class = require "class"
local ui_base = require "ui_base"
local event = require "event"
local ui_window_mgr = require "ui_window_mgr"
local game_scheme = require "game_scheme"
local lang = require "lang"
local card_assets = require "card_sprite_asset"
local util = require "util"
local hero_mgr = require "hero_mgr"
local player_mgr = require "player_mgr"
local log = require "log"
local hero_item = require "hero_item_new"
-- local secretplace_mgr=require "secretplace_mgr"
local SpriteSwitcher = CS.War.UI.SpriteSwitcher
local Image = CS.UnityEngine.UI.Image
local Text = CS.UnityEngine.UI.Text
local Button = CS.UnityEngine.UI.Button
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable
local ScrollRect    = CS.UnityEngine.UI.ScrollRect
local TextMeshProUGUI         = CS.TMPro.TextMeshProUGUI

module("ui_relic_select")
local window = nil
local selectedIndex = 0
local selectedItem = nil
local lastEffect = nil

local relicData = nil
local onSelectRelic = nil
local relicHeroes = nil
local closeCallback = nil
local autoSelect = nil
local autoSelectWaitSecond = nil
local autoSelectTask = nil

local color =
{
    [1] = {r=0x0c/0xFF,g=0x32/0xFF,b=0x5f/0xFF,a=1},
    [2] = {r=0x57/0xFF,g=0x03/0xFF,b=0x72/0xFF,a=1},
    [3] = {r=0x97/0xFF,g=0x52/0xFF,b=0x24/0xFF,a=1},
}

local pos =
{
    [1] = -232,
    [2] = 0,
    [3] = 232,
}

local RelicSelect = {}
RelicSelect.widget_table = 
{	
    selectBtn = {path = "selectBtn", type = "Button"},
    relic1 = {path = "relic1", type = "RectTransform"},
    relic2 = {path = "relic2", type = "RectTransform"},
    relic3 = {path = "relic3", type = "RectTransform"},
    relicSelected1 = {path = "selected/relicSelected1", type = "RectTransform"},
    relicSelected2 = {path = "selected/relicSelected2", type = "RectTransform"},
    relicSelected3 = {path = "selected/relicSelected3", type = "RectTransform"},
    effect1 = {path = "effect1", type = "RectTransform"},
    effect2 = {path = "effect2", type = "RectTransform"},
    effect3 = {path = "effect3", type = "RectTransform"},

    relicHerosTrans = {path = "relicHeros", type = "RectTransform"},
    relicHeroTips = {path = "relicHeros/tips", type = "Text"},
    relicHeroScrollRect = {path = "relicHeros/List", type = ScrollRect},
    relicHeroBg1 = {path = "relicHeros/bg1", type = "RectTransform"},
    relicHeroBg2 = {path = "relicHeros/bg2", type = "RectTransform"},
    relicHerosList = {path = 'relicHeros/List/Viewport/Content', type = ScrollRectTable},
    relicHerosListMask = {path = 'relicHeros/List/Viewport', type = "Image"},

    desc1 ={path="relic1/desc",type="TextMeshProUGUI",fitArabic=true},
    desc2 ={path="relic2/desc",type="TextMeshProUGUI",fitArabic=true},
    desc3 ={path="relic3/desc",type="TextMeshProUGUI",fitArabic=true},
    desc1Selected ={path="selected/relicSelected1/desc",type="TextMeshProUGUI",fitArabic=true},
    desc2Selected ={path="selected/relicSelected2/desc",type="TextMeshProUGUI",fitArabic=true},
    desc3Selected ={path="selected/relicSelected3/desc",type="TextMeshProUGUI",fitArabic=true},

    tipsText ={path="tipsbg/tipsText",type="Text",fitArabic=true},
}
function RelicSelect:FixMultiLang()
    local oversea_res=require "oversea_res"
    oversea_res.SetTextParam(self.desc1Selected,lang.AR,{enableAutoSizing=true,fontSizeMax=32,fontSizeMin=10})
    oversea_res.SetTextParam(self.desc2Selected,lang.AR,{enableAutoSizing=true,fontSizeMax=32,fontSizeMin=10})
    oversea_res.SetTextParam(self.desc3Selected,lang.AR,{enableAutoSizing=true,fontSizeMax=32,fontSizeMin=10})
end
function RelicSelect:Init()
    self.spriteAsset = self.spriteAsset or card_assets.CreateSpriteAsset()  
    self:SubscribeEvents()
    -- self.relicHeros.gameObject:SetActive(false)

    self.relicHerosList.onItemRender = onItemRenderBottom
    
    self.relicHerosList.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data and scroll_rect_item.data.items ~= nil then
            for k,item in pairs(scroll_rect_item.data["items"]) do
                item:Dispose()
                item = nil
            end
        end
    end
end

function RelicSelect:OnShow()
    if not relicData then
        log.Error("open relic select ui,but relicData is nil")
        ui_window_mgr:UnloadModule("ui_relic_select")
    end

    if autoSelect then
        autoSelectTask = util.DelayCall(autoSelectWaitSecond,function()
            selectedIndex = autoSelect
            self:ConfirmSelect()
        end)
    end
    
    self.selectBtn.gameObject:SetActive(false)
    self:RefreshUI()
end

function RelicSelect:RefreshUI()
    for i=1,3 do
        local data = relicData and relicData[i]
        if data then
            Draw(i, self["relic"..i], data)
            Draw(i, self["relicSelected"..i], data)
        end
    end
end

function onItemRenderBottom(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or {index, dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem

    local trans = scroll_rect_item:Get('trans')
    
    local itemIcon = nil
    if scroll_rect_item.data["items"] then
		scroll_rect_item.data["items"] :Dispose()
        scroll_rect_item.data["items"]  = nil
        itemIcon = hero_item.CHeroItem()
    else
        itemIcon = hero_item.CHeroItem()
    end

    scroll_rect_item.data["items"] = itemIcon
    itemIcon:Init(trans.gameObject.transform, function() 
        itemIcon:SetHero(dataItem)
    end,0.55)
end

function Draw(index, item, dataItem)
    local img1 = item:Find("img1"):GetComponent(typeof(Image))
    local img2 = item:Find("img2"):GetComponent(typeof(Image))
    local img3 = item:Find("img3"):GetComponent(typeof(Image))
    local bg = item:Find("bg")
    if bg then
        local selectBtn = bg:GetComponent(typeof(Button))
        if selectBtn then
            selectBtn.onClick:AddListener(function()
                if selectedIndex ~= index then
                    selectedIndex = index
                    if selectedItem then
                        selectedItem.gameObject:SetActive(false)
                    end
                    if lastEffect and not util.IsObjNull(lastEffect) then
                        lastEffect.gameObject:SetActive(false)
                    end
                    if window and window:IsValid() then
                        selectedItem = window["relicSelected"..index]
                        window["effect"..dataItem.grade].gameObject:SetActive(true)
                        lastEffect = window["effect"..dataItem.grade]
                        lastEffect.transform.localPosition = {x=pos[index],y=0,z=0}
                        -- window.selectBtn.transform.localPosition = {x=pos[index],y=-272.98,z=0}
                        window:SetRelic(index)
                    end
                    if selectedItem and not util.IsObjNull(selectedItem) then
                        selectedItem.gameObject:SetActive(true)
                    end
                    if window and window.selectBtn.gameObject.activeSelf == false then
                        window.selectBtn.gameObject:SetActive(true)
                    end
                end
            end)
        end
    end

    local iconImg = item:Find("icon"):GetComponent(typeof(Image))
    if window and window:IsValid() then
        window.spriteAsset:GetSprite(dataItem.iconID, function(sp)
            iconImg.sprite = sp
            iconImg.gameObject:SetActive(true)
        end)
    end
    if dataItem.icon[1] == "0" then
        local all = item:Find("iconFrame/All"):GetComponent(typeof(SpriteSwitcher))
        all.gameObject:SetActive(true)
        all:Switch(dataItem.grade-1)
    elseif dataItem.icon[1] == "1" then
        local camp = item:Find("iconFrame/camp"):GetComponent(typeof(SpriteSwitcher))
        camp:Switch(tonumber(dataItem.icon[2])-1)
        camp:GetComponent(typeof(Image)):SetNativeSize()
        camp.gameObject:SetActive(true)
    elseif dataItem.icon[1] == "2" then
        local icon = item:Find("iconFrame/mask/icon"):GetComponent(typeof(Image))
        if window and window:IsValid() then
            local heroID = tonumber(dataItem.icon[2])
            window.spriteAsset:GetSprite(hero_mgr.ChangeHeroIcon(heroID, 5), function(sp)
                icon.sprite = sp
            end)
            icon.gameObject:SetActive(true)
        end
    elseif dataItem.icon[1] == "3" then
        local prof = item:Find("iconFrame/prof"):GetComponent(typeof(SpriteSwitcher))
        prof:Switch(tonumber(dataItem.icon[2])-1)
        prof.gameObject:SetActive(true)
    end
    local name1 = item:Find("name1"):GetComponent(typeof(TextMeshProUGUI))
    local name2 = item:Find("name2"):GetComponent(typeof(TextMeshProUGUI))
    local name3 = item:Find("name3"):GetComponent(typeof(TextMeshProUGUI))
    name1.gameObject:SetActive(false)
    name2.gameObject:SetActive(false)
    name3.gameObject:SetActive(false)

    if dataItem.grade == 2 then
        name = name2
    elseif dataItem.grade == 3 then
        name = name3
    else
        name = name1
    end
    name.gameObject:SetActive(true)
    
    local desc = item:Find("desc"):GetComponent(typeof(TextMeshProUGUI))
    local grade = item:Find("grade"..dataItem.grade):GetComponent(typeof(TextMeshProUGUI))
    
    img1.gameObject:SetActive(dataItem.grade == 1)
    img2.gameObject:SetActive(dataItem.grade == 2)
    img3.gameObject:SetActive(dataItem.grade == 3)
    desc.text = dataItem.desc
    name.text = dataItem.name
    desc.curOutlineColor = color[dataItem.grade]
    grade.gameObject:SetActive(true)
    grade.text = dataItem.gradeDesc
end

function RelicSelect:SetRelic(selectedIndex)
    local data = relicHeroes[selectedIndex]
    self.relicHerosTrans.gameObject:SetActive(true)
    for i = 1, 3 do
        if self["relic"..i] then
            self["relic"..i]:SetActive(selectedIndex ~= i)
        end
    end
    if data.target == 1 then
        -- 全体
        self.relicHeroBg1.gameObject:SetActive(true)
        self.relicHeroBg2.gameObject:SetActive(false)
        
        self.relicHerosList.gameObject:SetActive(false)
        self.relicHerosList.data = nil
        self.relicHerosList:Refresh()
        self.relicHeroScrollRect.enabled = false
        self.relicHerosListMask.enabled = false
        self.relicHeroTips.gameObject:SetActive(true)
        if data.type == 3 then
            self.relicHeroTips.text = lang.Get(15674)
        elseif data.type == 4 then
            self.relicHeroTips.text = lang.Get(15673)
        elseif data.type == 6 then
            self.relicHeroTips.text = lang.Get(15675)
        else
            self.relicHeroTips.text = lang.Get(15671)
        end
    else
        if data.heros then
            self.relicHeroBg1.gameObject:SetActive(#data.heros<=6)
            self.relicHeroBg2.gameObject:SetActive(#data.heros>6)
        end

        local len = util.get_len(data.heros)
        -- 没有该类英雄
        self.relicHeroTips.gameObject:SetActive(len <= 0)
        self.relicHeroTips.text = lang.Get(15672)

        self.relicHerosList.gameObject:SetActive(true)
        self.relicHerosList.data = data.heros
        self.relicHerosList:Refresh()
        self.relicHeroScrollRect.enabled = len>6
        self.relicHerosListMask.enabled = len>6
    end
end

function RelicSelect:ConfirmSelect()
    if onSelectRelic then
        onSelectRelic(relicData[selectedIndex].id,selectedIndex)
    end
end

function RelicSelect:SubscribeEvents()
    self.selectBtnEvent = function()
        self:ConfirmSelect()
    end
    self.selectBtn.onClick:AddListener(self.selectBtnEvent)
end

function RelicSelect:UnsubscribeEvents()
    self.selectBtn.onClick:RemoveListener(self.selectBtnEvent)
end

function RelicSelect:Close()
	if self:IsValid() then
		self:UnsubscribeEvents()
    end
    if self.relicHerosList then
        self.relicHerosList:ItemsDispose()
    end
    lastEffect = nil
	if self.spriteAsset then
        self.spriteAsset:Dispose()
        self.spriteAsset = nil
	end
    selectedIndex = 0
    self.relicsID = nil
    selectedItem = nil
    relicHeroes = nil
    relicData = nil
    onSelectRelic = nil
    autoSelect = nil
    if autoSelectTask then
        util.RemoveDelayCall(autoSelectTask)
        autoSelectTask = nil
    end
    if closeCallback then
        closeCallback()
        closeCallback = nil
    end
	self.__base:Close()
	window = nil
end

local CMazeRelic = class(ui_base, nil, RelicSelect)

function Show()
	if window == nil then
		window = CMazeRelic()
		window._NAME = _NAME;window:LoadUIResource("ui/prefabs/uirelicselect.prefab", nil, nil, nil, false)
	end
	window:Show()
	return window
end

function Hide()
	if window ~= nil then
		window:Hide()
	end
end

function Close()
	if window ~= nil then
		window:Close()
		window = nil
	end
end

function OnSceneDestroy()
	Close()
end

-- relics 要显示的遗物
-- heroes 受影响的英雄
-- selectRelic 选择后的回调
function InitRelicSelect(relics,heroes,selectRelic,getRelicHeroes)
    relicData = {}
    relicHeroes = {}
    
    for k,v in ipairs(relics) do
        local relicCfg = game_scheme:TrialTreasure_0(v)
        local data = {}
        if relicCfg then
            data.id = v
            data.name = lang.Get(relicCfg.langName)
            data.desc = lang.Get(relicCfg.langDest)
            data.gradeDesc = lang.Get(73000-1+relicCfg.grade)
            data.grade = relicCfg.grade
            data.icon = util.SplitString(relicCfg.Icon, "#")
            data.iconID = relicCfg.iconID
            getRelicHeroes = getRelicHeroes or GetRelicHeroes
            relicHeroes[k] = getRelicHeroes(relicCfg,heroes)
        end
        table.insert(relicData, data)
    end
    
    onSelectRelic = selectRelic
end

function SetCloseCallback(callback)
    closeCallback = callback
end

function SetAutoSelect(waitSecond,selectId)
    autoSelect = selectId or 2
    autoSelectWaitSecond = waitSecond or 15 
end

function GetRelicHeroes(cfg,heroes)
    local data = {}
    if cfg then
        local th = cfg.targetType
        local arr = string.split(th, "#", tonumber)
        local target = tonumber(arr[1])
        local val = tonumber(arr[2])
        data.target = target
        data.type = val
        data.heros = {}
        if target == 1 then
            -- 全体
        elseif target == 2 then
            -- 指定英雄
            table.insert(data.heros,heroes[val])
        elseif target == 3 then
            -- 指定类型
            for heroID,entity in pairs(heroes)do
                if entity.type then
                    if entity.type == val then
                        table.insert(data.heros,entity)
                    end
                else
                    local heroCfg = game_scheme:Hero_0(heroID)
                    if heroCfg then
                        if heroCfg.type == val then
                            table.insert(data.heros,entity)
                        end
                    end
                end
            end
        elseif target == 4 then
            -- 指定职业
            for heroID,entity in pairs(heroes)do
                if entity.profession then
                    if entity.profession == val then
                        table.insert(data.heros,entity)
                    end
                else
                    local heroCfg = game_scheme:Hero_0(heroID)
                    if heroCfg then
                        if heroCfg.profession == val then
                            table.insert(data.heros,entity)
                        end
                    end
                end
            end
        end
        table.sort( data.heros, function (a,b)
            if not a or not b or not a.battleProp or not b.battleProp then
                return false
            end
            return a.battleProp.power > b.battleProp.power
        end)
    end
    
    return data
end

function GetSecretPlaceRelicHeros(cfg)
    local data = {}
    if cfg then
        local allheros = player_mgr.GetPalPartData()
        local th = cfg.targetType
        local arr = string.split(th, "#", tonumber)
        local target = tonumber(arr[1])
        local val = tonumber(arr[2])
        data.target = target
        data.type = val
        data.heros = {}
        if target == 1 then
            -- 全体
        elseif target == 2 then
            -- 指定英雄
            --遗物选择时需要筛选只有紫色以上英雄才出来
            if allheros[val].numProp.starLv >= hero_mgr.Hero_Star.Purple then
                table.insert(data.heros,allheros[val])
            end
        elseif target == 3 then
            -- 指定类型
            for heroID,entity in pairs(allheros)do
                local heroCfg = game_scheme:Hero_0(entity.heroID)
                if heroCfg then
                    if heroCfg.type == val then
                        if entity.numProp.starLv >= hero_mgr.Hero_Star.Purple then
                            table.insert(data.heros,entity)
                        end
                    end
                else
                end
            end
        elseif target == 4 then
            -- 指定职业
            for heroID,entity in pairs(allheros)do
                local heroCfg = game_scheme:Hero_0(entity.heroID)
                if heroCfg then
                    if heroCfg.profession == val then
                        if entity.numProp.starLv >= hero_mgr.Hero_Star.Purple then
                            table.insert(data.heros,entity)
                        end
                    end
                else
                end
            end
        elseif target == 9 then
            --指定阵营
            -- local campDatas = secretplace_mgr.GetCampType(val)
            -- for heroID,entity in pairs(allheros)do
            --     local heroCfg = game_scheme:Hero_0(entity.heroID)
            --     if heroCfg then
            --         if heroCfg.type == campDatas[heroCfg.type] and heroCfg.tFlag==1 then
            --             if entity.numProp.starLv >= hero_mgr.Hero_Star.Purple then
            --                 table.insert(data.heros,entity)
            --             end
            --         end
            --     else
            --     end
            -- end
        end
        table.sort( data.heros, function (a,b)
            if not a.battleProp and b.battleProp then
                return true
            elseif a.battleProp and not b.battleProp then
                return false
            elseif not a.battleProp and not b.battleProp then
                return a.heroID > b.heroID
            else
                return a.battleProp.power > b.battleProp.power
            end
        end)
        local t = {}
        for i,v in pairs(data) do
            for j,k in pairs(data.heros) do
                if k.heroID and not t[k.heroID] then
                    t[k.heroID] = k
                end
            end
        end
        data.heros = {}
        for j,k in pairs(t) do
            table.insert(data.heros,k)
        end
    end
    return data
end

event.Register(event.SCENE_DESTROY, OnSceneDestroy)
event.Register(event.RESET_MAZE_MAP, OnSceneDestroy)
