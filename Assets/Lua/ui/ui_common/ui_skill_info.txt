--@region FileHead
-- ui_skill_info.txt ---------------------------------
-- author:  罗华冠
-- date:    8/3/2020 11:13:08 AM
-- ver:     1.0
-- desc:    通用技能信息展示tip
-------------------------------------------------
 

local require   = require
local ipairs    = ipairs
local typeof    = typeof
local string    = string
local tonumber  = tonumber

local Text 			    = CS.UnityEngine.UI.Text
local RectTransform     = CS.UnityEngine.RectTransform
local Canvas            = CS.UnityEngine.Canvas

local class                 = require "class"
local ui_base               = require "ui_base"
local module_scroll_list    = require "scroll_list"
local ui_tips_mgr           = require "ui_tips_mgr"
local util                  = require "util"
local lang                  = require "lang"
local game_scheme           = require "game_scheme"
-- local faction_wanted_mgr    = require "faction_wanted_mgr"
local prop_pb               = require"prop_pb"
 

--@region ModuleDeclare
module("ui_skill_info")
--local interface = require "iui_skill_info"
local window = nil
local UISkillInfo = {}

local career = 0
local union_skill_index = 0
local faction_type = 0
local faction_skill_index = 0
local isShare = false -- 查看他人英雄
local shareData = nil -- 他人玩家数据
local other_data = nil -- 用于其他系统入口的数据管理

PAGE_TYPE = {
    union = 1,
    faction_skill = 2,
    faction_type = 3,
    talent = 4,
}
 
local page = PAGE_TYPE.union

 
UISkillInfo.widget_table = {
	tipsLine2 = {path ="bg/tipsLine2" , type = RectTransform},			
	nameAndLV = {path ="bg/nameAndLV" , type = "Text"},				--技能名字
	skillPointTxt = {path ="bg/upgradeInfo/skillPoint" , type = "Text"},			--技能占用点
	skillDesc1 = {path ="bg/SkillDesc1" , type = "Text"},				--1级技能描述
	skillDesc2 = {path ="bg/SkillDesc2" , type = "Text"},				--2级技能描述
	skillDesc3 = {path ="bg/SkillDesc3" , type = "Text"},				--3级技能描述
}
 

--@region WindowCtor
function UISkillInfo:ctor(selfType)
 
 
end  

function UISkillInfo:Init()
	ui_tips_mgr.ShowUITip("ui_skill_info", self.UIRoot:GetComponent(typeof(Canvas)))	
end  

 

--@region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function UISkillInfo:OnShow()
    self:RefreshUI()
end  

-- 阵营精通技能入口
function SetFactionSkillInfo(_factionType,_index,_isShare,_shareData)
    page = PAGE_TYPE.faction_skill
    faction_type = _factionType
    faction_skill_index = _index
    isShare = _isShare
    shareData = _shareData
end

-- 阵营精通阵营入口
function SetFactionTypeInfo(_factionType,_isShare,_shareData)
    page = PAGE_TYPE.faction_type
    faction_type = _factionType
    isShare = _isShare
    shareData = _shareData
end

-- 联盟科技技能入口
-- param : 职业，技能序号
function SetUnionSkillInfo(_career,_index,_isShare,_shareData)
    page = PAGE_TYPE.union
    career = _career
    union_skill_index = _index
    isShare = _isShare
    shareData = _shareData
end

-- 天赋技能详情入口
function SetTalentSkillInfo(nameID,descID)
    page = PAGE_TYPE.talent
    other_data = {}
    other_data.nameID = nameID
    other_data.descID = descID
end

function IsPercentageProp(type)
    return not (type == prop_pb.enPro_HP or type == prop_pb.enPro_Attack or type == prop_pb.enPro_Defend or type == prop_pb.enPro_Speed or type == prop_pb.enPro_Extra_Skill_Points_Rate or type == prop_pb.enPro_Energy)
end

function UISkillInfo:RefreshUI()
    if page == PAGE_TYPE.faction_skill then
    --     -- 阵营精通技能提示
    --     local curLevel = nil
    --     local skillCfg = nil
    --     local curSkillCfg = nil
    --     if isShare then
    --         curLevel = shareData[faction_type].stage
    --     else
    --         curLevel = faction_wanted_mgr.GetWantedDataLevel(faction_type)
    --     end
    --     skillCfg = faction_wanted_mgr.GetHaloSkillCfgByType(faction_type)
    --     curSkillCfg = skillCfg[faction_skill_index]
    --     if curSkillCfg then
    --         self.nameAndLV.text = lang.Get(curSkillCfg.nameID)
    --         local desc = lang.Get(curSkillCfg.descID)
    --         if curLevel < curSkillCfg.needLevel then
    --             if faction_type < 5 then
    --                 local typeName = lang.Get(4105+faction_type)
    --                 desc = desc..'\n'..string.format(lang.Get(180217),typeName,"Lv."..curSkillCfg.needLevel)
    --             else
    --                 desc = desc..'\n'..string.format(lang.Get(180218),"Lv."..curSkillCfg.needLevel)
    --             end
    --          end
    --         self.skillDesc1.text = desc
    --     end
    -- elseif page == PAGE_TYPE.faction_type then
    --     -- 阵营精通阵营提示
    --     local level = nil
    --     if isShare then
    --         level = shareData[faction_type].lv
    --     else
    --         level = faction_wanted_mgr.GetWantedDataMasterLevel(faction_type)
    --     end
    --     local desc = ""
    --     if level > 0 then
    --         local propHaloCfg = game_scheme:FactionHalo_0(faction_type,level)
    --         local propHalo = propHaloCfg.propHalo
    --         local propGroup = util.SplitString(propHalo, ";")
    --         for k,v in ipairs(propGroup) do
    --             local property = util.SplitString(v, "#")
    --             local name = tonumber(property[1])
    --             local value = tonumber(property[2])
    --             local percentage = ""
    --             if IsPercentageProp(name) then
    --                 percentage = "%"
    --                 value = value / 100
    --             end
    --             local str = "%s: +%s"
    --             if string.len(desc) > 0 then
    --                 desc = desc..'\n'..string.format(str, lang.Get(5000 + name), value..percentage)
    --             else
    --                 desc = string.format(str, lang.Get(5000 + name), value..percentage)
    --             end
    --         end
    --     else
    --         desc = lang.Get(16370)
    --     end
        
    --     self.nameAndLV.text = faction_type < 5 and lang.Get(180207+faction_type) or lang.Get(180216)
    --     self.skillDesc1.text = desc
    elseif page == PAGE_TYPE.union then
        -- 联盟科技提示
        local projData = nil
        local tech_data_Mgr = require "tech_data_Mgr"
        if isShare then
            projData = shareData[career][union_skill_index]
        else
            local projDataList = tech_data_Mgr.GetProjDataByCareer(career)
            if projDataList~=nil then
                projData = projDataList[union_skill_index]
            end
        end
        if projData then
            self.nameAndLV.text = lang.Get(projData.name)
            local skillDesc1 = ""
            skillDesc1= string.format(lang.Get(15775), projData.curLv, projData.maxLv)
            local skillDesc2 = nil
            
            local isMax = projData.curLv == projData.maxLv
            local curLvProDesc = tech_data_Mgr.GetTechProDesc(career,union_skill_index,projData.curLv,projData.projId)
            local nextLvProDesc = nil
            if not isMax then
                if isShare then
                    nextLvProDesc = tech_data_Mgr.GetTechProDesc(career,union_skill_index,projData.curLv+1,projData.projId)
                else
                    nextLvProDesc = tech_data_Mgr.GetTechProDesc(career,union_skill_index,projData.curLv+1)
                end
            end
            for i=1,#curLvProDesc do
                local curDesc = curLvProDesc[i].nameText.."+"..curLvProDesc[i].valueText
                skillDesc1 = skillDesc1..'\n'..curDesc
                if (not isMax) and nextLvProDesc and nextLvProDesc[i] then
                    if not skillDesc2 then
                        skillDesc2 = lang.Get(180212)
                    end
                    local nextDesc = curLvProDesc[i].nameText.."+"..nextLvProDesc[i].valueText
                    skillDesc2 = skillDesc2..'\n'..nextDesc
                end
            end
            self.skillDesc1.text = skillDesc1
            if skillDesc2 then
                self.skillDesc2.gameObject:SetActive(true)
                self.skillDesc2.text = skillDesc2
            end
        end
    elseif page == PAGE_TYPE.talent then
        -- 天赋详情
        if not other_data then
            return 
        end
        self.nameAndLV.text = lang.Get(other_data.nameID)
        self.skillDesc1.text = lang.Get(other_data.descID)
    end
end

function UISkillInfo:SetInputParam(p)
	self.inputParam = p

    --如果正在显示，则更新一次窗口
    if self.UIRoot and self.UIRoot.activeSelf == true then
        self:UpdateUIPage()
    end
end

--@region WindowClose
function UISkillInfo:Close()
	self.__base:Close()
    ui_tips_mgr.CloseUITip()
    career = 0
    union_skill_index = 0
    shareData = nil
    isShare = nil
    window = nil
    other_data = nil
end  
 
local CUISkillInfo = class(ui_base, nil, UISkillInfo)
 

function Show()
    if window == nil then
        window = CUISkillInfo()
        window._NAME = _NAME
        window:LoadUIResource("ui/prefabs/uiskillinfo.prefab", nil, nil, nil,true)
    end
    window:Show()
    return window
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end

function Close()
    if window ~= nil then
        Hide()
        window:Close()
        window = nil
    end
end

 

MessageTable =
{ --///<<< tableStart
} --///<<< tableEnd
 

