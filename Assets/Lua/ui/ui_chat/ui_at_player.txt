-- ui_at_player.txt -----------------------------------
-- author:  刘志远
-- date:    2018-11-21
-- ver:     1.0
-- desc:    先知之树置换
----------------------------------------------------------------
local require = require
local string=string
local table=table

local class = require "class"
local ui_base = require "ui_base"
local chat_mgr_new = require "chat_mgr_new"
local ScrollRectTable    = CS.UI.UGUIExtend.ScrollRectTable
local ui_window_mgr = require "ui_window_mgr"

module("ui_at_player")
local window = nil
local M = {}

local callback = nil

M.widget_table =
{
    closeBtn = {path = "closeBtn", type = "Button",backEvent = true},
    list = {path = "list/Viewport/Content", type = ScrollRectTable },
    inputField = {path = "inputField", type = "InputField"},
    okBtn = {path = "okBtn", type = "Button"},
}

--///List
function M:ListCreate()
	self.list.onItemRender=function(scroll_rect_item,index,dataItem  )
        if not scroll_rect_item.data then    
			scroll_rect_item.data = {}
			scroll_rect_item.data.name = scroll_rect_item:Get("name")
			scroll_rect_item.data.itemBtn = scroll_rect_item:Get("itemBtn")
            scroll_rect_item.data.itemBtn.onClick:AddListener(function()
                callback(scroll_rect_item.data.drawData)
                ui_window_mgr:UnloadModule("ui_at_player")
            end)
        end
        --初始化状态
        scroll_rect_item.data.drawData=dataItem
        scroll_rect_item.data.name.text = "@"..dataItem
		
    end
    self.list.onItemDispose=function(scroll_rect_item,index)
		if scroll_rect_item.data then
		end
	end
end
function M:ListDispose()
    self.list:ItemsDispose()
end


function M:Init()
    self:ListCreate()
    self:SubscribeEvents()
    self:UpDateUI()
    self.okBtn:Select()
end

function M:Close()
	if self.UIRoot and self:IsValid() then
		self:ListDispose()
		self:UnsubscribeEvents()
	end
	self.__base:Close()
	self = nil
end

function M:UpDateUI()
    local pageState = chat_mgr_new.GetPageState()
    local enumState = chat_mgr_new.enum_pState
    if not(pageState == enumState.lang or pageState == enumState.world or
    pageState == enumState.guide or pageState == enumState.territory) then
        return
    end
    
    local msgArrData = chat_mgr_new.GetMsgByType(pageState)
    local nameArr ={}
    local nameFlag = {}
    for i=#msgArrData,1,-1 do
        local name = msgArrData[i].name
        local player_mgr = require "player_mgr"
        local selfName = player_mgr.GetRoleName()
        if not (nameFlag[name] or (self.inputField.text ~="" and string.find(name,self.inputField.text)~=1) or name==selfName) then
            table.insert(nameArr,name)
            nameFlag[name]=true
        end
    end

	self.list.data=nameArr
	self.list:Refresh(-1,-1)
end

function M:SubscribeEvents()
    self.closeUIEvent = function()
		ui_window_mgr:UnloadModule("ui_at_player")
	end
    self.closeBtn.onClick:AddListener(self.closeUIEvent)
    
    self.okBtnEvent = function()
        if callback then
            callback(self.inputField.text)
        end
		ui_window_mgr:UnloadModule("ui_at_player")
	end
    self.okBtn.onClick:AddListener(self.okBtnEvent)

    self.InputFieldHandler = function()
        self:UpDateUI()
    end
    self.inputField.onValueChanged:AddListener(self.InputFieldHandler)
end

function M:UnsubscribeEvents()
    self.closeBtn.onClick:RemoveListener(self.closeUIEvent)
    self.okBtn.onClick:RemoveListener(self.okBtnEvent)
    self.inputField.onValueChanged:RemoveListener(self.InputFieldHandler)

end


--UI事件-------------------


local mClass = class(ui_base, nil, M)

function Show()
	if window == nil then
		window = mClass()
		window._NAME = _NAME;window:LoadUIResource("ui/prefabs/uiatplayer.prefab", nil,nil, nil,true)
	end
	window:Show()
	return window
end

function Hide()
	if window ~= nil then
		window:Hide()
	end
end

function Close()
	if window ~= nil then
		window:Close()
		window = nil
	end
end

function Open(_callback)
    local pageState,curSessionData,isClose = chat_mgr_new.GetPageState()
    local enumState = chat_mgr_new.enum_pState

    if isClose or not(pageState == enumState.lang or pageState == enumState.world or
    pageState == enumState.guide or pageState == enumState.territory) then
        return
    end

    callback = _callback
    ui_window_mgr:ShowModule("ui_at_player")

end