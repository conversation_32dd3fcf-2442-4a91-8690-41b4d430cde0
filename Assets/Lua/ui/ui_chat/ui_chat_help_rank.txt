--@region FileHead
-- ui_chat_help_rank.txt ---------------------------------
-- author:  孙健
-- date:    3/17/2020 12:00:00 AM
-- ver:     1.0
-- desc:    Description
-------------------------------------------------
--@endregion 

--@region Require
local require   = require
local ipairs    = ipairs
local string    = string
local tostring    = tostring
local table     = table

local Button        = CS.UnityEngine.UI.Button
local Text          = CS.UnityEngine.UI.Text
local RectTransform       = CS.UnityEngine.RectTransform
local ScrollRectTable    = CS.UI.UGUIExtend.ScrollRectTable
local TextMeshProUGUI  = CS.TMPro.TextMeshProUGUI


local event					= require "event"
local class                 = require "class"
local ui_base               = require "ui_base"
local com_class             = require "com_class"
local module_scroll_list    = require "scroll_list"
local CScrollListItemBase   = module_scroll_list.CScrollListItemBase
local net_route             = require "net_route"
local util                  = require"util"
local player_mgr            = require "player_mgr"
local net_arena_module      = require "net_arena_module"
local ui_player_detail_info_ex = require "ui_player_detail_info_ex"
local ui_window_mgr         = require "ui_window_mgr"
local common_new_pb         = require "common_new_pb"
local face_item             = require "face_item_new"
local chat_mgr_new          = require "chat_mgr_new"
local mq_common_pb = require "mq_common_pb"
local hero_item = require "hero_item_new"
local goods_item = require "goods_item_new"
local item_data = require "item_data"
local game_scheme = require "game_scheme"
local iui_item_detail = require "iui_item_detail"
local lang = require "lang"
local json = require "dkjson"

--@endregion 

--@region ModuleDeclare
module("ui_chat_help_rank")
--local interface = require "iui_chat_help_rank"
local window = nil
local UIChatHelpRank = {}

ViewType =
{
    RankType = 1,
    HelpType = 2,
}

--@endregion 
local Scroll_Rect_Item = com_class.CreateClass(CScrollListItemBase)
--@region WidgetTable
UIChatHelpRank.widget_table = {
    ContentHelp = {path = "page/bg/contentBG/ContentHelp", type = RectTransform},
    ContentRank = {path = "page/bg/contentBG/ContentRank", type = RectTransform},
    list = {path = "page/bg/contentBG/ContentRank/ranking/Viewport/Content", type = ScrollRectTable, },
    helpList = {path = "page/bg/contentBG/ContentHelp/helpList/Viewport/Content", type = ScrollRectTable, },
    myRankIcon = { path = "page/bg/contentBG/ContentRank/my_rank/rankIcon", type = RectTransform, },
    myRankingNum = { path = "page/bg/contentBG/ContentRank/my_rank/ranking_num", type = Text, },
    myRankingTopNum = { path = "page/bg/contentBG/ContentRank/my_rank/top_num", type = Text, },
    myPlayerIconRoot = { path = "page/bg/contentBG/ContentRank/my_rank/playericon_root", type = RectTransform, },
    name = { path = "page/bg/contentBG/ContentRank/my_rank/name", type = Text },
    myRankKey = { path = "page/bg/contentBG/ContentRank/my_rank/text_leiji", type = TextMeshProUGUI},
    closeBtn = { path = "page/closeBtn", type = "Button",backEvent = true},
    BtnHelp = { path = "page/ToggleGroupType2/help", type = "Toggle"},
    BtnHelpText = { path = "page/ToggleGroupType2/help/Background/Checkmark/Text", type = RectTransform},
    BtnRank = { path = "page/ToggleGroupType2/rank", type = "Toggle"},
    BtnRankText = { path = "page/ToggleGroupType2/rank/Background/Checkmark/Text", type = RectTransform},
    titleRank = { path = "page/bg/titleBG/titleRank", type = RectTransform},
    titleHelp = { path = "page/bg/titleBG/titleHelp", type = RectTransform},
    EmptyPart = { path = "page/bg/contentBG/ContentHelp/EmptyPart", type = RectTransform},
    HelpRed = { path = "page/ToggleGroupType2/help/Tips", type = RectTransform},
    HelpRedNum = { path = "page/ToggleGroupType2/help/Tips/Text", type = Text},
}
--@endregion 

--@region WindowCtor
function UIChatHelpRank:ctor(selfType)

    self.CurType = ViewType.RankType
    self.matchHomeIndicator = true

end --///<<< function

--@endregion 

--@region WindowInit
--[[窗口初始化]]
function UIChatHelpRank:Init()
    self:SubscribeEvents()
    net_route.RegisterMsgHandlers(MessageTable)

    --util.DelayCall(0.1, function()
    --    RankDataRquest()
    --end)
    self:InitList()
    self:RefreshHelpList()
    self:RefreshView()
    self:RefreshHelpRed()
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function UIChatHelpRank:OnShow()
    self:UpdateUI()
end --///<<< function

--@endregion 

--@region WindowOnHide
--[[资源加载完成，被显示的时候调用]]
function UIChatHelpRank:OnHide()
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowSetInputParam
--[[设置窗口的输入参数。该参数通常是由其它模块或者外部设置进来。需要注意的是，
当调用这个函数的时候，窗口资源可能还是没有加载完成的。
@param p 参数表
]]
function UIChatHelpRank:SetInputParam(p)
	self.inputParam = p

    --如果正在显示，则更新一次窗口
    if self.UIRoot and self.UIRoot.activeSelf == true then
        self:UpdateUI()
    end
	
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowBuildUpdateData
--[[构建UI更新数据]]
function UIChatHelpRank:BuildUpdateData()
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowUpdateUI
--[[资源加载完成，被显示的时候调用]]
function UIChatHelpRank:UpdateUI()
	self:BuildUpdateData()
--@region User
--@endregion 
end --///<<< function

--@endregion 

function UIChatHelpRank:RefreshHelpRed()
    if not self:IsValid() then
        return
    end

    local data = chat_mgr_new.GetMsgByType(chat_mgr_new.ENUM_CHANNEL.GUIDE)
    local count = 0
    for i,v in ipairs(data) do
        if v.sType == mq_common_pb.enSpeak_LeagueMutualHelp then
            local helpInfo = chat_mgr_new.GetHelpInfoByID(v.roleid)
            local selfRoleID = player_mgr.GetPlayerRoleID()
            if helpInfo and v.roleid ~= selfRoleID and helpInfo.helpRoleID[selfRoleID] == nil then
                local progress = helpInfo and helpInfo.prog or 0
                local progressEnd = 0
                local cfg_help = game_scheme:LeagueHelp_0(v.leagueHelp.helpID)
                if cfg_help then
                    progressEnd = cfg_help.helpNum
                    if progress < progressEnd then
                        count = count + 1
                    end
                end
            end
        end
    end
    self.HelpRed.gameObject:SetActive(count>0)
    self.HelpRedNum.text = count
end

--@region WindowClose
function UIChatHelpRank:Close()
    net_route.UnregisterMsgHandlers(MessageTable)

    if self.itemUI then
        self.itemUI:Dispose()
        self.itemUI = nil
    end
    if self.list then
        self.list:ItemsDispose()
    end
    if self.helpList then
        self.helpList:ItemsDispose()
    end
    if self:IsValid() then
        self:UnsubscribeEvents()
    end
	self.__base:Close()
    window = nil
--@region User
--@endregion 
end --///<<< function

--@endregion 

function UIChatHelpRank:RefreshView()
    if not self:IsValid() then
        return
    end
    if self.CurType == ViewType.HelpType then
        self.ContentHelp.gameObject:SetActive(true)
        self.ContentRank.gameObject:SetActive(false)
        self.BtnHelpText.gameObject:SetActive(true)
        self.BtnRankText.gameObject:SetActive(false)
        self.titleRank.gameObject:SetActive(false)
        self.titleHelp.gameObject:SetActive(true)
    elseif self.CurType == ViewType.RankType then
        RankDataRquest()
        self.ContentHelp.gameObject:SetActive(false)
        self.ContentRank.gameObject:SetActive(true)
        self.BtnHelpText.gameObject:SetActive(false)
        self.BtnRankText.gameObject:SetActive(true)
        self.titleRank.gameObject:SetActive(true)
        self.titleHelp.gameObject:SetActive(false)
    end
end

--@region WindowSubscribeEvents
--[[订阅UI事件]]
function UIChatHelpRank:SubscribeEvents()
    ----///<<< Button Proxy Line >>>///-----
    self.OnRANK_DETAIL_NTF = function(eventname, type, _, detail)
        local rank_pb = require "rank_pb"
        if type == rank_pb.enLeagueHelp then
            self.rankData = detail
        end
        self:UpdateRank()
    end
    if self.OnRANK_DETAIL_NTF then
        event.Register(event.RANK_DETAIL_NTF, self.OnRANK_DETAIL_NTF)
    end

    self.OnClickClose = function()
        ui_window_mgr:UnloadModule("ui_chat_help_rank")
    end

    if not util.IsObjNull(self.closeBtn) then
        self.closeBtn.onClick:AddListener(self.OnClickClose)
    end

    self.OnClickBtnHelp = function(isOn)
        if isOn then
            self.CurType = ViewType.HelpType
            self:RefreshView()
        end
    end
    self.widget_table["BtnHelp"].value_changed_event = "OnClickBtnHelp"

    self.OnClickBtnRank = function(isOn)
        if isOn then
            self.CurType = ViewType.RankType
            self:RefreshView()
        end
    end
    self.widget_table["BtnRank"].value_changed_event = "OnClickBtnRank"
    self.EventRefreshHelp = function()
        self:RefreshHelpList()
    end
    self:RegisterEvent(event.UPDATE_CHAT_MSG,self.EventRefreshHelp)

    --聊天内容变化
    self.OnChatMsgUpdate = function( event,channelType,singleMsg,isHelp)
        self:RefreshHelpRed()
    end
    event.Register(event.UPDATE_CHAT_MSG,self.OnChatMsgUpdate)
end --///<<< function

--@endregion 

--@region WindowUnsubscribeEvents
--[[退订UI事件]]
function UIChatHelpRank:UnsubscribeEvents()
    if self.OnRANK_DETAIL_NTF then
        event.Unregister(event.RANK_DETAIL_NTF, self.OnRANK_DETAIL_NTF)
    end
    event.Unregister(event.UPDATE_CHAT_MSG,self.OnChatMsgUpdate)

    if not util.IsObjNull(self.closeBtn) then
        self.closeBtn.onClick:RemoveListener(self.OnClickClose)
    end
end --///<<< function

--@endregion 

function UIChatHelpRank:UpdateRank()
    if not self:IsValid() then
        return
    end
    self:UpdateList()
    if self.myRankData == nil then
        self.myRankData = {}
        self.myRankData.roleID = player_mgr.GetPlayerRoleID()
        self.myRankData.roleLv = player_mgr.GetPlayerLV()
        self.myRankData.faceID = player_mgr.GetPlayerProp().faceID
        self.myRankData.name = player_mgr.GetRoleName()
        self.myRankData.rankKey = 0
        self.myRankData.rank = "--"
    end
    local dataItem = self.myRankData
    self.itemUI = self.itemUI or face_item.CFaceItem():Init(self.myPlayerIconRoot)
    self.itemUI:SetFaceInfo(dataItem.faceID, function()
        if dataItem.roleID ~= player_mgr.GetPlayerRoleID() then
            net_arena_module.Send_PLAYER_DETAILS(dataItem.roleID, common_new_pb.emDetailType_Friend)
            ui_player_detail_info_ex.SetHeroInfo(dataItem.faceID, dataItem.roleLv, dataItem.name, dataItem.roleID, nil)
            ui_player_detail_info_ex.SetShowType(ui_player_detail_info_ex.Show_Type_Enum.Common,nil,true)
            ui_window_mgr:ShowModule("ui_player_detail_info_ex")
        end
    end)
    self.itemUI:SetNewBg(true)
    self.itemUI:SetActorLvText(true, dataItem.roleLv)
    self.name.text = dataItem.name

    if dataItem.rank ~= "--" and dataItem.rank <= 3 then
        self.myRankIcon.gameObject:SetActive(true)
        self.myRankingNum.gameObject:SetActive(false)
        self.myRankingTopNum.gameObject:SetActive(true)
        self.myRankingTopNum.text = dataItem.rank
    else
        self.myRankIcon.gameObject:SetActive(false)
        self.myRankingNum.gameObject:SetActive(true)
        self.myRankingTopNum.gameObject:SetActive(false)
        self.myRankingNum.text = dataItem.rank
    end

    local helpInfo = chat_mgr_new.GetHelpInfoByID(dataItem.roleID)
    local cbtAll = helpInfo and helpInfo.cbtAll or 0
    self.myRankKey.text = string.format("%d/%d",dataItem.rankKey,cbtAll)
end

function UIChatHelpRank:UpdateList()
    if not self:IsValid() then
        return
    end
    if self.list == nil then
        return
    end
    local data = self:BuildListData()
    self.list.data = data
    self.list:Refresh(0, -1)
end

function UIChatHelpRank:RefreshHelpList()
    if not self:IsValid() then
        return
    end
    local data = chat_mgr_new.GetMsgByType(chat_mgr_new.ENUM_CHANNEL.GUIDE)
    local tempList = {}
    local selfRoleID = player_mgr.GetPlayerRoleID()
    for i,v in ipairs(data) do
        if v.sType == mq_common_pb.enSpeak_LeagueMutualHelp then
            local helpInfo = chat_mgr_new.GetHelpInfoByID(v.roleid)
            if helpInfo and v.roleid ~= selfRoleID then
                local progress = helpInfo and helpInfo.prog or 0
                local progressEnd = 0
                local cfg_help = game_scheme:LeagueHelp_0(v.leagueHelp.helpID)
                if cfg_help then
                    progressEnd = cfg_help.helpNum
                    if progress < progressEnd then
                        local curdata = {}
                        curdata.data = v
                        if helpInfo.helpRoleID[selfRoleID] == nil then
                            curdata.canHelp = 1
                        else
                            curdata.canHelp = 0
                        end
                        table.insert(tempList,curdata)
                    end
                end
            end
        end
    end
    table.sort(tempList,function(a,b)
        if a and b then
            return a.canHelp > b.canHelp
        end
    end)
    if #tempList == 0 then
        self.EmptyPart.gameObject:SetActive(true)
    else
        self.EmptyPart.gameObject:SetActive(false)
    end
    self.helpList.data = tempList
    self.helpList:Refresh(0, -1)
end

function UIChatHelpRank:BuildListData()
    local dataList = {}
    if not self.rankData then
        return dataList
    end
    self.myRankData = nil
    for k,v in ipairs(self.rankData) do
        local data = {}
        data.roleID = v.roleID
        data.roleLv = v.roleLv
        data.faceID = v.faceID
        data.name = v.name
        data.time = v.time
        data.rank = v.rank or "--"
        data.rankKey = v.rankKey or 0
        if v.roleID == player_mgr.GetPlayerRoleID() then
            self.myRankData = data
        end
        table.insert(dataList, data)
    end
    table.sort(dataList, function(v1, v2)
        return v1.rankKey > v2.rankKey
    end)
    for k, v in ipairs(dataList) do
        dataList[k].rank = k
        if self.myRankData and dataList[k].roleID == self.myRankData.roleID then
            self.myRankData.rank = k
        end
    end
    return dataList
end

function RankDataRquest()
    if window then
        local net = require "net"
        local rank_pb = require "rank_pb"
        local msg = rank_pb.TMSG_RANK_DETAIL_REQ()
        msg.enType = rank_pb.enLeagueHelp
        local msg_pb = require "msg_pb"
        net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_RANK_DETAIL_REQ, msg)
    end
end

function UIChatHelpRank:InitList()
    self.list.onItemRender = onItemRenderBottom
    self.list.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data then
            if scroll_rect_item.data.itemUI ~= nil then
                scroll_rect_item.data.itemUI:Dispose()
                scroll_rect_item.data.itemUI = nil
            end
        end
    end

    self.helpList.onItemRender = onHelpItemRenderBottom
    self.helpList.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data then
            if scroll_rect_item.data.goodItemUI ~= nil then
                scroll_rect_item.data.goodItemUI:Dispose()
                scroll_rect_item.data.goodItemUI = nil
            end
            if scroll_rect_item.data.heroItemUI ~= nil then
                scroll_rect_item.data.heroItemUI:Dispose()
                scroll_rect_item.data.heroItemUI = nil
            end
            local helpBtn = scroll_rect_item:Get("helpBtn")
            if helpBtn then
                helpBtn.onClick:RemoveAllListeners()
            end
        end
    end
end

function onHelpItemRenderBottom(scroll_rect_item,index,dataItem_value)
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem_value}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem_value
    local content = scroll_rect_item:Get("content")
    local itemPos = scroll_rect_item:Get("playericon_root")
    local helpBtn = scroll_rect_item:Get("helpBtn")
    local ProLab = scroll_rect_item:Get("ProLab")
    local ProBg = scroll_rect_item:Get("ProBg")
    local helpBtnGray = scroll_rect_item:Get("helpBtnGray")
    local Lab = scroll_rect_item:Get("Lab")
    local dataItem = dataItem_value.data
    local cfg_help = game_scheme:LeagueHelp_0(dataItem.leagueHelp.helpID)
    local helpInfo = chat_mgr_new.GetHelpInfoByID(dataItem.roleid)
    local progress = helpInfo and helpInfo.prog or 0
    local progressEnd = 0
    if cfg_help then
        progressEnd = cfg_help.helpNum
        local cfg_reward = game_scheme:Reward_0(cfg_help.seekHelpReward.data[0])
        if cfg_reward then
            if cfg_reward.iRewardType == 1 then
                --物品
                local iconUI = scroll_rect_item.data["goodItemUI"] or goods_item.CGoodsItem():Init(itemPos, nil, 1)
                scroll_rect_item.data["goodItemUI"] = iconUI
                iconUI:SetGoods(nil, cfg_reward.arrParam[0], cfg_reward.arrParam[1], function()
                    iui_item_detail.Show(cfg_reward.arrParam[0], nil, item_data.Item_Show_Type_Enum.Reward_Interface, nil, nil, nil, cfg_help.seekHelpReward.data[0])
                end)
                iconUI:SetFrameBg(3)
            elseif cfg_reward.iRewardType == 2 then
                --英雄
                local heroUI = scroll_rect_item.data["heroItemUI"] or hero_item.CHeroItem():Init(itemPos, function()
                    heroUI:DisplayInfo()
                end, 1)
                scroll_rect_item.data["heroItemUI"] = heroUI
                heroUI:SetHero({ heroID = cfg_reward.arrParam[0] }, function()
                    local ui_archive_detail = require "ui_archive_detail"
                    ui_archive_detail.SetData({ { heroID = cfg_reward.arrParam[0], starLv = cfg_reward.arrParam[1] } }, 1)
                    ui_window_mgr:ShowModule("ui_archive_detail")
                end)
            end
        end
    end
    ProLab.text = string.format("%d/%d", progress, progressEnd)
    ProBg.value = progress/progressEnd
    content.text = string.format(lang.Get(16750), dataItem.name)
    Lab.text = lang.Get(16751)
    local selfRoleID = player_mgr.GetPlayerRoleID()
    if helpInfo.helpRoleID[selfRoleID] ~= nil then
        Lab.color = {r=0x6E/0xFF,g=0x6E/0xFF,b=0x6E/0xFF,a=1}
        helpBtnGray:Switch(1)
    else
        Lab.color = {r=0x2A/0xFF,g=0x63/0xFF,b=0x84/0xFF,a=1}
        helpBtnGray:Switch(0)
    end
    helpBtn.onClick:RemoveAllListeners()
    helpBtn.onClick:AddListener(function()
        local net_chat_module_new = require "net_chat_module_new"
        net_chat_module_new.Send_PLEAGUEHELP_AIDALLIES(dataItem.roleid)

        local roleName = tostring(player_mgr.GetRoleName()) or ""
        local sociaty_data = require "sociaty_data"
        local baseData = sociaty_data.GetLeagueData()
        if baseData and baseData.id then
            local json_str = json.encode({
                guild_id = baseData.id,
                guild_name = baseData.strName,
                role_name = roleName
            })
            event.Trigger(event.GAME_EVENT_REPORT, "Unhelp_clickehelp", json_str)
        end
    end)
end

function onItemRenderBottom(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem

    local top = scroll_rect_item:Get("top_num")
    local rank = scroll_rect_item:Get("ranking_num")
    local iconRoot = scroll_rect_item:Get("playericon_root")
    local rankImg = scroll_rect_item:Get("rankIcon")
    local itemNum = scroll_rect_item:Get("text_leiji")
    local name = scroll_rect_item:Get("name")

    local iconUI = scroll_rect_item.data["itemUI"] or face_item.CFaceItem():Init(iconRoot)
    scroll_rect_item.data["itemUI"] = iconUI
    iconUI:SetFaceInfo(dataItem.faceID, function()
        if dataItem.roleID ~= player_mgr.GetPlayerRoleID() then
            net_arena_module.Send_PLAYER_DETAILS(dataItem.roleID, common_new_pb.emDetailType_Friend)

            ui_player_detail_info_ex.SetHeroInfo(dataItem.faceID, dataItem.roleLv, dataItem.name, dataItem.roleID, nil)
            ui_player_detail_info_ex.SetShowType(ui_player_detail_info_ex.Show_Type_Enum.Common,nil,true)
            ui_window_mgr:ShowModule("ui_player_detail_info_ex")
        end
    end)
    iconUI:SetNewBg(true)
    iconUI:SetActorLvText(true, dataItem.roleLv)
    name.text = dataItem.name

    if dataItem.rank ~= "--" and dataItem.rank <= 3 then
        rankImg.gameObject:SetActive(true)
        top.gameObject:SetActive(true)
        top.text = dataItem.rank
        rank.gameObject:SetActive(false)
    else
        top.gameObject:SetActive(false)
        rankImg.gameObject:SetActive(false)
        rank.gameObject:SetActive(true)
        rank.text = dataItem.rank
    end
    local helpInfo = chat_mgr_new.GetHelpInfoByID(dataItem.roleID)
    local cbtAll = helpInfo and helpInfo.cbtAll or 0
    itemNum.text = string.format("%d/%d",dataItem.rankKey,cbtAll)
end

--@region WindowBtnFunctions
--@endregion 

--@region ScrollItem

--@endregion 

--@region WindowInherited
local CUIChatHelpRank = class(ui_base, nil, UIChatHelpRank)
--@endregion 

--@region ModuleFunction
function Show()
    if window == nil then
        window = CUIChatHelpRank()
        window._NAME = _NAME
        window.isBlurBg = true
        window:LoadUIResource("ui/prefabs/uichathelprank.prefab", nil, nil, nil,false,true)
    end
    window:Show()
    return window
end

function IsFullUI()
	return util.CanUseBlurBg()
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end

function Close()
    if window ~= nil then
        Hide()
        window:Close()
        window = nil
    end
end

--@endregion

