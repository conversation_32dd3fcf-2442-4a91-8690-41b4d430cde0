--  ui_help_reward_choose.txt ------------------------------------------
-- author:  吕培玥
-- date:    2020.12.23
-- ver:     1.0
-- desc:   	求助物品选择界面
--------------------------------------------------------------

local require = require
local table = table
local ipairs = ipairs
local tonumber = tonumber
local string = string
local class = require 'class'
local ui_base = require 'ui_base'
local event = require 'event'
local util = require "util"
local game_scheme = require "game_scheme"
local ui_window_mgr = require "ui_window_mgr"
local RectTransform = CS.UnityEngine.RectTransform
local TextMeshProUGUI  = CS.TMPro.TextMeshProUGUI
local ScrollRectTable    = CS.UI.UGUIExtend.ScrollRectTable
local help_reward_choose_data = require "help_reward_choose_data"
local task_data = require "task_data"
local lang = require "lang"
local goods_item = require "goods_item_new"
local net_login_module = require "net_login_module"
local ui_utils = require "ui_utils"
local reward_mgr = require "reward_mgr"
local flow_text = require "flow_text"
local os = os
local player_mgr  = require "player_mgr" 
local math = math



module("ui_help_reward_choose")
local window = nil
local UIContent = {}
local net_chat_module_new = require "net_chat_module_new"
local curChooseData = nil
IsChooseHero = false

UIContent.widget_table = {
    closeBtn = { path = "closeBtn", type = "Button", backEvent = true },
    RefreshTimeLab = { path = "RefreshTime", type = TextMeshProUGUI},
    sureBtn = { path = "sureBtn", type = "Button"},
    CurChoose = { path = "UIContent/CurChoose", type = RectTransform},
    NeedCount = { path = "UIContent/NeedCount", type = TextMeshProUGUI},
    TipsTra = { path = "UIContent/Tips", type = RectTransform},
    RewardList = {path = "List/Reward/RewardList", type = RectTransform},
    RewardTable = {path = "List/Reward/RewardList/Viewport/Auto_Content", type = ScrollRectTable},
    HeroList = {path = "List/SPlus/HeroList", type = RectTransform},
    HeroMask = {path = "List/SPlus/HeroMask", type = RectTransform},
    HeroMaskLab = {path = "List/SPlus/HeroMask/Lab", type = "Text"},
    SLab = {path = "List/SPlus/Layout/SLab", type = TextMeshProUGUI},
    HeroTable = {path = "List/SPlus/HeroList/Viewport/Auto_Content", type = ScrollRectTable},
    helpBtn={path="helpBtn",type = "Button"}
}

--[[构造函数]]
function UIContent:ctor(selfType)
    self.__base:ctor(selfType)

end

--[[窗口初始化]]
function UIContent:Init()
    --拉取奖励数据
    net_chat_module_new.C2S_PLEAGUEHELP_SEEKHELP_POOL_REQ()
    self:SubscribeEvents()
    self:InitUI()
end

function UIContent:RefreshRewardTime()
    if not self:IsValid() then
        return
    end
    local nextTime = help_reward_choose_data.GetRewardList().zeroTime
    local curTime = 24*3600 - (net_login_module.GetServerTime() - nextTime)
    if curTime > 0 then
        self.RefreshTimeLab.gameObject:SetActive(true)
        self.RefreshTimeLab.text = lang.Get(16765)..ui_utils.TimeCountdown(curTime)
        self.Timer = self:CreateTimer(1,function ()
            if not self:IsValid() then
                return
            end
            local time = 24*3600 - (net_login_module.GetServerTime() - nextTime)
            if time > 0 then
                self.RefreshTimeLab.text = lang.Get(16765)..ui_utils.TimeCountdown(time)
            else
                self.RefreshTimeLab.gameObject:SetActive(false)
                self:RemoveTimer(self.Timer)
            end
        end)
    else
        self.RefreshTimeLab.gameObject:SetActive(false)
    end
end

function UIContent:InitUI()
    self.NeedCount.gameObject:SetActive(false)
    self.TipsTra.gameObject:SetActive(true)
    local activityValue = task_data.GetActivityByID(task_data.GetTaskChestInitData(1))
    local cfgValue = game_scheme:InitBattleProp_0(649).szParam.data[0]
    self.HeroMask.gameObject:SetActive(activityValue<cfgValue)
    self.HeroMaskLab.text = string.format(lang.Get(16771),cfgValue)
    self.RewardTable.onItemRender = onRewardItemRenderBottom
    self.RewardTable.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data then
            if scroll_rect_item.data["goodItem"] then
                scroll_rect_item.data["goodItem"]:Dispose()
            end
        end
    end

    self.HeroTable.onItemRender = onHelpItemRenderBottom
    self.HeroTable.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data then
            if scroll_rect_item.data["goodItem"] then
                scroll_rect_item.data["goodItem"]:Dispose()
            end
        end
    end
end

function onHelpItemRenderBottom(scroll_rect_item,index,dataItem)
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem
    local pos = scroll_rect_item:Get("Pos").transform
    local choose = scroll_rect_item:Get("ChooseIcon")
    local profile = scroll_rect_item.data["goodItem"] or goods_item.CGoodsItem():Init(pos, nil, 0.9)
    scroll_rect_item.data["goodItem"] = profile
    local onClickEvent = function()
        AddItemData(dataItem)
    end
    profile:SetGoods(nil, dataItem.rewardData.id,dataItem.rewardData.num, onClickEvent)
    profile:SetEnableState(ItemHeroIsShow(dataItem))
    choose:SetActive(ItemIsChoose(dataItem))
end

function onRewardItemRenderBottom(scroll_rect_item,index,dataItem)
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem
    local pos = scroll_rect_item:Get("Pos").transform
    local choose = scroll_rect_item:Get("Choose")
    local effect = scroll_rect_item:Get("effect")
    local profile = scroll_rect_item.data["goodItem"] or goods_item.CGoodsItem():Init(pos,nil,1)
    scroll_rect_item.data["goodItem"] = profile
    local onClickEvent = function()
        AddItemData(dataItem)
    end
    profile:SetGoods(nil, dataItem.rewardData.id,dataItem.rewardData.num, onClickEvent)
    profile:SetEnableState(ItemIsShow(dataItem))
    choose:SetActive(ItemIsChoose(dataItem))
    effect.gameObject:SetActive(false)
    effect.gameObject:SetActive(dataItem.data.ident == 1 and (not dataItem.choose))
end

function ItemHeroIsShow(dataItem)
    if IsChooseHero then
        return false
    end
--    if curChooseData == nil then
        if dataItem.choose then
            return false
        else
            return true
        end
    --end
    --return true
    --if curChooseData.id == dataItem.id then
    --    return true
    --else
    --    return false
    --end
end

function ItemIsShow(dataItem)
--    if curChooseData == nil then
        if dataItem.choose then
            return false
        else
            return true
        end
    --end
    --return true

    --if curChooseData.id == dataItem.id then
    --    return true
    --else
    --    return false
    --end
end

function ItemIsChoose(dataItem)
    if curChooseData == nil then
        return false
    end
    if curChooseData.id == dataItem.id then
        return true
    else
        return false
    end
end

function AddItemData(dataItem)
    if curChooseData and curChooseData.id == dataItem.id then
        DeleteItemData()
        window:RefreshList()
        return
    end
    curChooseData = dataItem
    window.NeedCount.text = lang.Get(16764)..curChooseData.data.helpNum
    window.NeedCount.gameObject:SetActive(true)
    window.TipsTra.gameObject:SetActive(false)
    if window.CurItem ~= nil then
        window.CurItem:Dispose()
    end
    window.CurItem = goods_item.CGoodsItem()
    window.CurItem:Init(window.CurChoose)
    local onClickEvent = function()
        DeleteItemData(dataItem)
        window:RefreshList()
    end
    window.CurItem:SetGoods(nil, curChooseData.rewardData.id,curChooseData.rewardData.num, onClickEvent)
    window:RefreshList()
end

function DeleteItemData()
    curChooseData = nil
    window.NeedCount.gameObject:SetActive(false)
    window.TipsTra.gameObject:SetActive(true)
    if window.CurItem ~= nil then
        window.CurItem:Dispose()
    end
end

function UIContent:RefreshList()
    if not self:IsValid() then
        return
    end

    --[[
    --下标10 限制途径11联盟互助
    ]]
    local num = game_scheme:HeroLimit_nums()
    local LimitData = {}
    for i=0, num-1 do
        local cfg = game_scheme:HeroLimit(i)
        if cfg then
            if cfg.Approach[10] and cfg.Approach[10] ~= 0 then --下标10 限制途径11联盟互助
                if cfg.Approach[10] == 1 then    -- 有限制
                    if cfg.Type then
                        local time = cfg.Times
                        local currServerTime = os.server_time()
                        local openServerTime = player_mgr.GetRoleOpenSvrTime()
                        local roleCreateTime = player_mgr.GetRoleCreateTime()
                        local sq = util.GetTimeArea()*3600
                        if cfg.Type == 1 and time then   -- 1、按角色创建时间
                            local stamp  = self:GetTimeStamp2(time)
                            if (roleCreateTime + stamp) < currServerTime then
                            else
                                LimitData[cfg.HeroRewardID] = cfg
                            end
                        elseif cfg.Type == 2 and time then   -- 2、按指定时间
                            local stamp = self:GetTimeStamp(time)-sq
                            if math.floor(stamp -currServerTime) < 0 then
                            else
                                LimitData[cfg.HeroRewardID] = cfg
                            end
                        elseif cfg.Type == 3 and time then   -- 3、按开服时间
                            local stamp  = self:GetTimeStamp2(time)
                            if (openServerTime + stamp) < currServerTime then
                            else
                                LimitData[cfg.HeroRewardID] = cfg
                            end
                        end
                    end
                end
            end
        end
    end

    local data = help_reward_choose_data.GetRewardList()
    local heroData = {}
    for i,v in ipairs(data.helpIDData1) do
        local limitcfg = game_scheme:LeagueHelp_0(v.helpID)
        if LimitData[limitcfg.seekHelpReward.data[0]] then
        else
             local temp = {}
            temp.id = v.helpID
            temp.data = game_scheme:LeagueHelp_0(v.helpID)
            temp.rewardData = reward_mgr.GetRewardGoods(temp.data.seekHelpReward.data[0])
            temp.choose = v.bSel
            if v.bSel then
                IsChooseHero = true
            end
            table.insert(heroData,temp)
        end
    end
    if IsChooseHero then
        self.SLab.text = "1/1"
    else
        self.SLab.text = "0/1"
    end
    local goodData = {}
    for i,v in ipairs(data.helpIDData2) do
        local temp = {}
        temp.id = v.helpID
        temp.data = game_scheme:LeagueHelp_0(v.helpID)
        temp.rewardData = reward_mgr.GetRewardGoods(temp.data.seekHelpReward.data[0])
        temp.choose = v.bSel
        table.insert(goodData,temp)
    end

    self.HeroTable.data = heroData
    self.HeroTable:Refresh(0, -1)

    self.RewardTable.data = goodData
    self.RewardTable:Refresh(0, -1)
end

--[[时间戳转换 字符床形式为"2022-06-28-11:00"样式的转换]]
function UIContent:GetTimeStamp(time)
    local timeDate = util.SplitString(time, "-")
    local mTime = util.SplitString(timeDate[4], ":")
    local Stamp =  util.GetTimeStamp(tonumber(timeDate[1]), tonumber(timeDate[2]), tonumber(timeDate[3]), tonumber(mTime[1]), tonumber(mTime[2]),0)
    return Stamp
end

--[[时间戳转换2 字符床形式为"712-0:00"样式的转换]]

function UIContent:GetTimeStamp2(time)
    local timeDate = util.SplitString(time, "-")
    local mTime = util.SplitString(timeDate[2], ":")
    local Stamp = (tonumber(timeDate[1]) * 24 * 3600) + (mTime[1] or 0 )*3600 + (tonumber(mTime[2]) or 0 )* 60 +  tonumber(mTime[3] or 0)
    return Stamp
end

--[[订阅UI事件]]
function UIContent:SubscribeEvents()
    self.OnBtn_closeBtn = function()
        ui_window_mgr:UnloadModule("ui_help_reward_choose")
    end
    self.widget_table["closeBtn"].event_name = "OnBtn_closeBtn"
    self.OnBtn_sureBtn = function()
        if curChooseData == nil then
            flow_text.Add(lang.Get(16772))
            return
        end
        net_chat_module_new.Send_PLEAGUEHELP_SEL_SEEKHELP(curChooseData.id)
        ui_window_mgr:UnloadModule("ui_help_reward_choose")
    end
    self.widget_table["sureBtn"].event_name = "OnBtn_sureBtn"
    self.RewardDataRefresh = function()
        self:RefreshRewardTime()
        self:RefreshList()
    end

    self.OnBtn_helpBtn = function()
        local ui_help = require "ui_help"
        ui_help.ShowWithDate(174)  
    end
    self.widget_table["helpBtn"].event_name = "OnBtn_helpBtn"

    self:RegisterEvent(event.PLEAGUEGELP_HELP_REWARD_DATA_CHANGE,self.RewardDataRefresh)

 
end

--[[退订UI事件]]
function UIContent:UnsubscribeEvents()

end

--[[窗口加载完成调用]]
function UIContent:OnShow()
    self.__base:OnShow()
end

--[[窗口隐藏时调用]]
function UIContent:Close()
    self.__base:Close()
    if self:IsValid() then
        self:UnsubscribeEvents()
    end
    if self.HeroTable then
        self.HeroTable:ItemsDispose()
    end
    if self.RewardTable then
        self.RewardTable:ItemsDispose()
    end
    if self.CurItem ~= nil then
        self.CurItem:Dispose()
    end
    curChooseData = nil
    IsChooseHero = false

end

local CUIContent = class(ui_base, nil, UIContent)
function Show()
    if window == nil then
        window = CUIContent()
        window._NAME = _NAME
        window.isBlurBg = true
        window:LoadUIResource("ui/prefabs/uihelprewardchoose.prefab", nil, nil)
    end
    window:Show()
    return window
end

function IsFullUI()
	return util.CanUseBlurBg()
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end

function Close()
    if window ~= nil then
        window:Close()
        window = nil
    end
end

--场景销毁事件
function OnSceneDestroy()
    --场景销毁的时候我们要关闭UI，清除相关数据
    Close()
end
event.Register(event.SCENE_DESTROY, OnSceneDestroy)