--@region FileHead
-- ui_chat_help_record.txt ---------------------------------
-- author:  孙健
-- date:    3/23/2020 12:00:00 AM
-- ver:     1.0
-- desc:    Description
-------------------------------------------------
--@endregion 

--@region Require
local require   = require
local ipairs    = ipairs
local string    = string
local table     = table

local Button        = CS.UnityEngine.UI.Button
local ScrollRectTable    = CS.UI.UGUIExtend.ScrollRectTable

local face_item             = require "face_item_new"
local class                 = require "class"
local ui_base               = require "ui_base"
local module_scroll_list    = require "scroll_list"
local net_route             = require "net_route"
local net_arena_module      = require "net_arena_module"
local ui_player_detail_info_ex = require "ui_player_detail_info_ex"
local common_new_pb         = require "common_new_pb"
local lang = require "lang"
local ui_window_mgr = require "ui_window_mgr"
local player_mgr = require "player_mgr"

--@endregion 

--@region ModuleDeclare
module("ui_chat_help_record")
--local interface = require "iui_chat_help_record"
local window = nil
local UIChatHelpRecord = {}
local helpData = {}
--@endregion 

--@region WidgetTable
UIChatHelpRecord.widget_table = {
    list = { path = "Auto_List/Viewport/Content", type = ScrollRectTable},
    closeBtn = { path = "closeBtn", type = "Button" ,backEvent = true},
--@region User
--@endregion 
}
--@endregion 

--@region WindowCtor
function UIChatHelpRecord:ctor(selfType)
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowInit
--[[窗口初始化]]
function UIChatHelpRecord:Init()
    self:SubscribeEvents()
    net_route.RegisterMsgHandlers(MessageTable)

    self:InitList()
    self:UpdateList()
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function UIChatHelpRecord:OnShow()
    self:UpdateUIPage()
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowOnHide
--[[资源加载完成，被显示的时候调用]]
function UIChatHelpRecord:OnHide()
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowSetInputParam
--[[设置窗口的输入参数。该参数通常是由其它模块或者外部设置进来。需要注意的是，
当调用这个函数的时候，窗口资源可能还是没有加载完成的。
@param p 参数表
]]
function SetInputParam(data)
	helpData = data
end --///<<< function

--@endregion 

--@region WindowBuildUpdateData
--[[构建UI更新数据]]
function UIChatHelpRecord:BuildUpdateData()
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowUpdateUI
--[[资源加载完成，被显示的时候调用]]
function UIChatHelpRecord:UpdateUIPage()
	self:BuildUpdateData()
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowClose
function UIChatHelpRecord:Close()
    net_route.UnregisterMsgHandlers(MessageTable)
    if self.list then
        self.list:ItemsDispose()
    end
    if self:IsValid() then
        self:UnsubscribeEvents()
    end
	self.__base:Close()
    helpData = {}
    window = nil
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowSubscribeEvents
--[[订阅UI事件]]
function UIChatHelpRecord:SubscribeEvents()
    self.OnClickClose = function()
        ui_window_mgr:UnloadModule("ui_chat_help_record")
    end

    if self.closeBtn then
        self.closeBtn.onClick:AddListener(self.OnClickClose)
    end
end --///<<< function

--@endregion 

--@region WindowUnsubscribeEvents
--[[退订UI事件]]
function UIChatHelpRecord:UnsubscribeEvents()
    if self.closeBtn then
        self.closeBtn.onClick:RemoveListener(self.OnClickClose)
    end
end --///<<< function

--@endregion 

--@region WindowBtnFunctions
--@endregion 

--@region ScrollItem
--[[初始化列表]]
function UIChatHelpRecord:InitList()
    self.list.onItemRender = OnItemRender
    self.list.onItemDispose = OnItemDispose
end

--[[渲染子项时调用，用于初始化item]]
function OnItemRender(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem

    local iconRoot = scroll_rect_item:Get("playericon_root")
    local textHelpInfo = scroll_rect_item:Get("help_info")

    local iconUI = scroll_rect_item.data["itemUI"] or face_item.CFaceItem():Init(iconRoot)
    scroll_rect_item.data["itemUI"] = iconUI
    iconUI:SetFaceInfo(dataItem.faceID, function()
        if dataItem.roleID ~= player_mgr.GetPlayerRoleID() then
            net_arena_module.Send_PLAYER_DETAILS(dataItem.roleID, common_new_pb.emDetailType_Friend)

            ui_player_detail_info_ex.SetHeroInfo(dataItem.faceID, dataItem.roleLv, dataItem.name, dataItem.roleID, nil)
            ui_player_detail_info_ex.SetShowType(ui_player_detail_info_ex.Show_Type_Enum.Common,nil,true)
            ui_window_mgr:ShowModule("ui_player_detail_info_ex")
        end
    end)
    iconUI:SetNewBg(true)
    iconUI:SetActorLvText(true, dataItem.roleLv)
    textHelpInfo.text = string.format(lang.Get(16761),dataItem.name,dataItem.helpTimes)
end

--[[销毁子项时调用]]
function OnItemDispose(scroll_rect_item, index)
    if scroll_rect_item and scroll_rect_item.data then
        if scroll_rect_item.data.itemUI ~= nil then
            scroll_rect_item.data.itemUI:Dispose()
            scroll_rect_item.data.itemUI = nil
        end
    end
end

function UIChatHelpRecord:UpdateList()
    if self.list == nil then
        return
    end
    local data = self:BuildListData()
    self.list.data = data
    self.list:Refresh(0, -1)
end

function UIChatHelpRecord:BuildListData()
    local dataList = {}
    for _,v in ipairs(helpData) do
        local data = {}
        data.roleID = v.nRoleID
        data.roleLv = v.nLevel
        data.faceID = v.nFaceID
        data.name = v.name
        data.helpTimes = v.helpTimes
        data.frame = v.nFrame
        table.insert(dataList, data)
    end
    return dataList
end

--@endregion 

--@region WindowInherited
local CUIChatHelpRecord = class(ui_base, nil, UIChatHelpRecord)
--@endregion 

--@region ModuleFunction
function Show()
    if window == nil then
        window = CUIChatHelpRecord()
        window._NAME = _NAME
        window:LoadUIResource("ui/prefabs/uichathelprecord.prefab", nil, nil, nil)
    end
    window:Show()
    return window
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end

function Close()
    if window ~= nil then
        Hide()
        window:Close()
        window = nil
    end
end

--@endregion

