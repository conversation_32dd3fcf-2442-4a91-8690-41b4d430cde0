-- button_tips_trigger.txt -----------------------------------------------
-- author:  熊旋
-- date:    2019.03.23
-- ver:     1.0
-- desc:    按钮提示(按钮提示)
-------------------------------------------------------------------

local require = require
local typeof = typeof
local print = print
local dump = dump
local pairs = pairs
local ipairs = ipairs
local math = math
local debug = debug

local event = require "event"
local windowMgr = require "ui_window_mgr"
local util = require "util"
local game_scheme = require "game_scheme"
local player_mgr = require "player_mgr"
local lang = require "lang"
local log = require("log")

local Canvas = CS.UnityEngine.Canvas
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
local GameObject = CS.UnityEngine.GameObject
local Button = CS.UnityEngine.UI.Button
local Random = CS.UnityEngine.Random

module("button_tips_trigger")

local curPriority = nil
local inLobby = true --判断是否在大厅
loginTips = 0
local curNameId = nil
local pastNameId = nil
local cacheLevel = nil -- 用于 战斗引导 特殊处理的 缓存关卡进度
local parentModuleName = nil -- 父对象模块名

emptyBgSource = "art/maps/battle/guide/emptyguidebg.prefab" -- 用于强制引导时的背景

--[[回调函数在关闭时回调指示按钮函数即可关闭特效同时触发按钮
    nameId   =  编号ID
    parent   =  父物体
    texts    =  气泡框文字
    callback =  回调函数
	uiParentTrans = 需要显示涟漪气泡的UI，用来设置涟漪层级
	
]]
function Show(pName,nameId, ...)
    if moduleRelate[nameId] then
        ------ --print("Skip",nameId)
        return
    end
    return ShowReal(pName,nameId, ...)
end
function ShowReal(pName,nameId, parent, texts, callback, isDown, _showFinger, isRot, uiParentTrans, notInLobby, hideCanvas,order)
    ------ --print("ShowBTIPS",nameId, parent, texts, callback, isDown,_showFinger,isRot, uiParentTrans, notInLobby)
    local force_guide_system = require "force_guide_system"
    if force_guide_system.GetState() == 2 then
        --处于强引导,不显示按钮提示
        if not CheckIsIndependent(nameId) then
            return
        end
    end

    local unforced_guide_mgr = require "unforced_guide_mgr"
	if unforced_guide_mgr.IsGuiding() then
        if not CheckIsIndependent(nameId) then
            return
        end
    end
    --[[
	if not inLobby and not notInLobby then
		------ --print("因为判断不在需要加载特效的界面所以不继续加载特效")
		return
	end
--]]
    local isBroken = IsCanExcute(nameId)
    ------ --print("ShowBTIPS2",isBroken,nameId, parent, texts, callback, isDown,_showFinger,isRot, uiParentTrans, notInLobby)
    if not isBroken then
        ------ --print("因为判断优先级低/点击次数/等级所以不继续加载特效")
        -- curPriority = nil
        return
    else
        ------ --print("加载特效==================================")
    end

    local path = nil
    local heroCfg = game_scheme:BtnTips_0(nameId)
    local sortOrder = 0

    if heroCfg.eftType == 3 then
        path = "ui/prefabs/temp_qipao.prefab"
    elseif heroCfg.eftType == 1 or 4 then
        path = "ui/prefabs/temp_lianyi.prefab"
    elseif heroCfg.eftType == 5 then
        path = "ui/prefabs/temp_kuang.prefab"
    end
    if heroCfg.eftType == 1 or 4 or 5 then
        local canvas = nil
        if uiParentTrans and not util.IsObjNull(uiParentTrans) then
            canvas = uiParentTrans:GetComponent(typeof(Canvas))
        else
            canvas = parent:GetComponentInParent(typeof(Canvas))
        end
        if order then -- 用于处理base界面为父节点 取不到order的问题
            sortOrder = order
        else
            if canvas and not util.IsObjNull(canvas) then
                sortOrder = canvas.sortingOrder + 1 or 0
            end
        end
    end

    
    if heroCfg.eftType ~= 5 then
        local ui_button_tips = require "ui_button_tips"
        if windowMgr:IsModuleShown("ui_button_tips") and ui_button_tips.GetCurType() ~= heroCfg.eftType then
            windowMgr:UnloadModule("ui_button_tips")
        end
        local menu_bot_data = require "menu_bot_data"
        local show = (not pageExclude[nameId]) or (pageExclude[nameId] ~= menu_bot_data.GetOriginPage())
        if not show then
            curPriority = nil
            return
        end
        ------ --print("Showw",show)
        local ui_button_tips = require "ui_button_tips"
        ui_button_tips.SetTipsData(parent, nameId, texts, isDown, _showFinger, isRot, sortOrder, path, callback, heroCfg.eftType, nil, hideCanvas)

        CheckStartEvent(nameId, parent)
        if heroCfg.eftType == 6 or heroCfg.eftType == 7 then -- 强制引导
            local layout = heroCfg.eftType == 6 and "Top" or "Default"
            local ui_guide_assistant_new = require "ui_guide_assistant_new"
            local pData = heroCfg.paramAssistant.data
            if pData then
                ui_guide_assistant_new.ShowWithParam(pData[0],pData[1],pData[2],layout,sortOrder,heroCfg.eftType)
            end
        end
    else
        -- CheckStartEvent(nameId)
        local ui_button_tips_kuang = require "ui_button_tips_kuang"
        ui_button_tips_kuang.SetTipsData(parent, nameId, texts, isDown, _showFinger, isRot, sortOrder, path, callback, heroCfg.eftType)
        windowMgr:ShowModule("ui_button_tips_kuang")
    end
    parentModuleName = pName
    return isBroken
end

GuidePosition = {
    [30] = {x=1,y=3,z=0}
}

function Show3DGuide(nameID)
    local isBroken = IsCanExcute(nameId)
    if not isBroken then
        return
    end
    curNameId = nameID
    pastNameId = nameID
    local scene_pointer = require"scene_pointer"
    scene_pointer.SetParentPath(moduleRelate[nameID][1])
    local pos = GuidePosition[nameID]
    scene_pointer.SetPosition(pos.x,pos.y,pos.z)
    scene_pointer.SetGuideType(1)
    scene_pointer.Load()
end

local preconditions = {}

-- 设置对应引导的前置条件
function SetPrecondition(nameId,value)
    preconditions[nameId] = value
end

-- 检查是否满足前置条件
function CheckPrecondition(nameId)
    return preconditions[nameId]
end

function OnSceneDestroy()
    preconditions = {}
end
event.Register(event.SCENE_DESTROY, OnSceneDestroy)
event.Register(event.ACCOUNT_CHANGE_WORLD_RSP,OnSceneDestroy)

-- 检测是否符合配置表条件
function IsCanExcute(nameId)
    local player_mgr = require "player_mgr"
    local laymain_data = require "laymain_data"
    local heroCfg = game_scheme:BtnTips_0(nameId)
    
    if not heroCfg then
        ------ --print("ban",2)
        return false
    end

    if heroCfg.shield == 1 then
        return false
    end


    local force_guide_system = require "force_guide_system"
    if force_guide_system.GetState() == 2 then
        --处于强引导,不显示按钮提示
        if not CheckIsIndependent(nameId) then
            return
        end
    end

    local unforced_guide_mgr = require "unforced_guide_mgr"
	if unforced_guide_mgr.IsGuiding() then
        if not CheckIsIndependent(nameId) then
            return
        end
    end

    local prioritys = heroCfg.priority
    -- --print("按钮点击次数>>>>>>>>>>>>>>>>>",GetPlaySpeedKey(nameId),heroCfgheroCfg.clicks,PlayerPrefs.GetFloat(GetPlaySpeedKey(nameId), 0))
    local clickTime = -1
    if heroCfg.eftType ~= 5 then
        --非对话框引导
        local ui_button_tips = require "ui_button_tips"
        clickTime = ui_button_tips.GetLocalClickTime(nameId)
    else
        local ui_button_tips_kuang = require "ui_button_tips_kuang"
        clickTime = ui_button_tips_kuang.GetLocalClickTime(nameId)
    end
    if heroCfg.clicks <= clickTime then
        -- --print("ban",nameId,3,heroCfg.clicks,PlayerPrefs.GetFloat(GetPlaySpeedKey(nameId), 0))
        return false
    end

    if laymain_data.GetNextLevelCfg().checkPointID == 1 then
        return false
    end

    local target_level = nameId == 26 and cacheLevel or laymain_data.GetNextLevelCfg().checkPointID -- 兼容战斗引导 数据不同步下发问题
    if heroCfg.minLv > player_mgr.GetPlayerLV() or heroCfg.minMapLv > target_level then
          ------ --print("ban",42)
        return false
    end

    if heroCfg.maxLv <= player_mgr.GetPlayerLV() or heroCfg.maxMapLv <= target_level then
        return false
    end

    if curPriority ~= nil then
        if curPriority <= prioritys then
            
            curPriority = prioritys
        else
            ------ --print("ban",7)
            return false
        end
    else
        curPriority = prioritys
    end
    if not Check(nameId) then
        return false
    end
    return true
end

--用于执行引导的前置步骤
function CheckStartEvent(nId, parent)
    if nId == 25 then --三星竞标赛引导 需要先打开滑动条 并移到底部
        local ui_novice_tournament = require "ui_novice_tournament"
        ui_novice_tournament.SetScaleCenterPos(parent.transform.position)
        util.DelayCallOnce(0.2,function ()
            ShowGuide(nId)
        end)
    else
        ShowGuide(nId)
    end
end

function ShowGuide(nameId)
    curNameId = nameId
    windowMgr:ShowModule("ui_button_tips")
    pastNameId = nameId
end

function SetCacheLevel(level)
    cacheLevel = level
end

function SetClickTime(nameId)
    local time = PlayerPrefs.GetFloat(GetPlaySpeedKey(nameId), 0)
    time = time + 1
    PlayerPrefs.SetFloat(GetPlaySpeedKey(nameId), time)
end

function GetPlaySpeedKey(nameId)
    if not nameId then
        return ""
    end
    local cfg = game_scheme:BtnTips_0(nameId)
    if not cfg or not cfg.title then
        return ""
    end

    local name = cfg.title .. (player_mgr.GetPlayerRoleID() or "") .. "clicks"
    return name
end

function Close()
    ------ --print("CloseBTIPS")
    ------ --print("Close111>>>>>>>>>>>>>>>>>>>>>>>")
    --print(debug.traceback())
    CheckCloseEvent()
    parentModuleName = nil
    curPriority = nil
    curNameId = nil
    if windowMgr:IsModuleShown("ui_button_tips") then
        windowMgr:UnloadModule("ui_button_tips")
    end
end

function CheckParentUIClose(eventname,uiModuleName,abName,uiroot,recycleUI,obj)
    if parentModuleName == uiModuleName then
        Close()
    end
end
event.Register(event.UI_MODULE_PREPARE_CLOSE, CheckParentUIClose)


--用于带小秘书的 弱引导 关闭事件处理
function CheckCloseEvent()
    --关闭处理
    if curNameId == 26 or curNameId == 25 then
        -- local ui_guide_assistant_new = require "ui_guide_assistant_new"
        -- ui_guide_assistant_new.Close()
        windowMgr:UnloadModule("ui_guide_assistant_new")

    end
end

function CloseEffect(eventName, onLobby)
    --打开了其他界面,关闭按钮效果
    if not onLobby then
        ------ --print("打开了其他界面,关闭按钮效果")
        inLobby = false
        windowMgr:UnloadModule("ui_button_tips")
    else
        ------ --print("回到大厅")
        inLobby = true
    end
end
event.Register(event.ON_GUIDE_BUTTON_TIPS, CloseEffect)

function ClearLoginTips()
    loginTips = 0 -- 每次登陆只显示一次
    Close()
end
event.Register(event.SCENE_DESTROY, ClearLoginTips)
event.Register(event.ACCOUNT_CHANGE_WORLD_RSP, ClearLoginTips)
event.Register(event.FORCE_GUIDE_ENTER, Close)
event.Register(event.UNFORCED_GUIDE_POINTER_SHOW, Close)

moduleRelate = {
    [2] = {"/UIRoot/CanvasWithMesh/UIMenuBot(Clone)/bottom/Auto_Quests","ui_menu_bot"},
    [3] = {"/UIRoot/CanvasWithMesh/UIMenuBot(Clone)/bottom/mainButtons/Hunt/Auto_Hunt","ui_menu_bot"},
    [9] = {"/UIRoot/CanvasWithMesh/UIMenuBot(Clone)/bottom/Auto_Home","ui_menu_bot"},
    [10] = {"/UIRoot/CanvasWithMesh/UIMenuBot(Clone)/bottom/Auto_Fight","ui_menu_bot"},
    [11] = {"/UIRoot/CanvasWithMesh/UIMenuBot(Clone)/bottom/Auto_Home","ui_menu_bot"},
    [18] = {"/UIRoot/CanvasWithMesh/UIMenuBot(Clone)/bottom/Auto_WorldMap","ui_menu_bot"},
    -- [21] = {"/UIRoot/CanvasWithMesh/UIMenuBot(Clone)/bottom/StarChestBtn","ui_menu_bot"},
    [23] = {"/UIRoot/CanvasWithMesh/UIMenuBot(Clone)/bottom/Auto_Next","ui_menu_bot"},
    [24] = {"",""},--动态关卡宝箱
    [25] = {"/UIRoot/CanvasWithMesh/UILobby(Clone)/top/Left_scroll_view/Viewport/left/Auto_NoviceTournament","ui_lobby"},
    [30] = {"/scenes/edit_yewai/ui/11","edit_yewai"},
}
pageExclude = {
    [2] = 2,
    [3] = 3,
}

--与强制引导 非强制引导  相对独立的引导
independentGuide = {
    [26] = "",
    [38] = "",
}

function CheckIsIndependent(nId)
    local independent = independentGuide[nId] ~= nil
    return independent
end

function GetCurNameId()
    return curNameId
end

--获得上一个执行过的 引导id
function GetPassNameId()
    return pastNameId
end

function GetTips(nameId)
    if nameId == 9 then
        return lang.Get(9339)
    elseif nameId == 11 then
        local tipsid = math.floor(Random.Range(19, 29))
        local tip_cfg = game_scheme:Tips_0(tipsid)
        if tip_cfg and tip_cfg.nLangID then
            return lang.Get(tip_cfg.nLangID)
        end
    else
        return ""
    end
    return ""
end

function GetRot(nameId)
    if nameId == 9 then
        return true
    elseif nameId == 11 then
        return true
    else
        return false
    end
end

function callback(nameId)
    if nameId == 11 then
        loginTips = 1
    elseif nameId == 21 then --领取大宝箱结束回调
        
    elseif nameId == 25 then
    end
end

function RefreshOrder(order)
    if windowMgr:IsModuleShown("ui_button_tips") then
        local ui_button_tips = require "ui_button_tips"
        ui_button_tips.SetOrder(order)
    end
end

local SaveCfgs = nil
function Init()
    -- statements
    if SaveCfgs then
        return
    end

    SaveCfgs = {}
    local max = game_scheme:BtnTips_nums()
    for i = 0, max do
        -- statements
        local cfg = game_scheme:BtnTips(i)
		if cfg then
            SaveCfgs[i + 1] = cfg
        end
    end
    util.sort(SaveCfgs, {"priority"})
    ------print(#SaveCfgs)
    --dump(SaveCfgs)
end

function CheckValid(heroCfg)
    -- statements
    local prioritys = heroCfg.priority
    -- --print("按钮点击次数>>>>>>>>>>>>>>>>>",GetPlaySpeedKey(nameId),heroCfgheroCfg.clicks,PlayerPrefs.GetFloat(GetPlaySpeedKey(nameId), 0))
    local clickTime = -1
    if heroCfg.eftType ~= 5 then
        --非对话框引导
        local ui_button_tips = require "ui_button_tips"
        clickTime = ui_button_tips.GetLocalClickTime(heroCfg.id)
    else
        local ui_button_tips_kuang = require "ui_button_tips_kuang"
        clickTime = ui_button_tips_kuang.GetLocalClickTime(heroCfg.id)
    end

    if heroCfg.id == 22 then
----        log.Log(" 人马引导次数log检测 ",clickTime)
    end
	if heroCfg.clicks <= clickTime then
        return false
    end
    
    local laymain_data = require "laymain_data"
    local target_level = nameId == 26 and cacheLevel or laymain_data.GetNextLevelCfg().checkPointID -- 兼容战斗引导 数据不同步下发问题
    if heroCfg.minLv > player_mgr.GetPlayerLV() or heroCfg.maxMapLv <= target_level  then
        return false
    end

    if heroCfg.maxLv <= player_mgr.GetPlayerLV() or heroCfg.minMapLv > target_level then
        return false
    end
    return true
end

--对应引导的业务逻辑（开启条件）
function Check(nameId)
    if nameId == 2 then
        local task_data = require "task_data"
        return task_data.CheckComposite() or task_data.CheckAchiComposite() or task_data.CheckWeekTaskComposite()
    elseif nameId == 10 then
        local menu_bot_data = require "menu_bot_data"
        return menu_bot_data.IsHuntPage()
    elseif nameId == 11 then
		return loginTips < 1
    elseif nameId == 12 then
        local battle_manager = require "battle_manager"
        local speed =  battle_manager.GetPlaySpeed()
        speed = speed *10
        speed = math.floor(speed)
        speed = speed/10
        return speed<=1.2
	elseif nameId == 23 then --世界地图的按钮引导 (现用于下一章节引导)
        local laymain_data = require "laymain_data"
        local menu_bot_data = require "menu_bot_data"
        local curPassId = laymain_data.GetPassLevel()
        if not menu_bot_data.IsHuntPage() then return false end
		if curPassId then
			local world_map_data = require "world_map_data"
            if world_map_data.ShowNextBtn(curPassId) and not world_map_data.GetStartNext()  then
				return true
            end
		end
		return false
    elseif nameId == 21 then --大宝箱领取引导
        local laymain_data = require "laymain_data"
            --星星宝箱可以领取
        if laymain_data.GetStarShowState() then
            return true
        else
            return  false
        end
    elseif nameId == 24 then --关卡大宝箱领取引导
        local world_map_data = require "world_map_data"
        local laymain_data = require "laymain_data"
        
        local localChapterId = world_map_data.GetLocalChapterID()
        return  laymain_data.IsShowHookShowGuide(localChapterId)
    elseif nameId == 25 then -- 三星锦标赛
        local tournament_mgr = require "tournament_mgr"
        return tournament_mgr.IsShowChampionships()
    -- elseif nameId == 30 then -- 遗落之境引导
    --     local time = PlayerPrefs.GetFloat(GetPlaySpeedKey(nameId), 0)

    --     local conform = true
    --     local dungeon_mgr = require "dungeon_mgr"
    --     local stageInfo = dungeon_mgr.GetStageInfo()
    --     if stageInfo and stageInfo.mapType > 6 then
    --         conform = false
    --     end
    --     return time <= 0 and conform
    end
    return true
end

--用于兼容  动态的引导对象
function GetObj(nId)
	local Find = GameObject.Find
    local obj = Find(moduleRelate[nId][1])
    if not obj then
        obj = GetDynamicObj(nId)
    end
    return obj
end

function GetDynamicObj(nId)
    if nId == 10 then   --关卡最新对象
        local new_hook_scene = require "new_hook_scene"
        local obj = new_hook_scene.GetLastestLevelObj()
        return obj
    elseif nId == 24 then
        --关卡宝箱
        local new_hook_scene = require "new_hook_scene"
        local obj = new_hook_scene.GetShowBoxObj()
        return obj
    end
end

--兼容弱引导 各自特殊的业务逻辑
function CheckCallback(nId)
    if nId == 10 then
      local ui_menu_bot = require "ui_menu_bot"
      ui_menu_bot.OnBtn_FightClicked()
    elseif nId == 25 then
      windowMgr:ShowModule("ui_novice_tournament")
    end
end

--判断切页  正确切页才显示引导
function CheckPage(cfg)
    local menu_bot_data = require("menu_bot_data")

    local conform = false
    if cfg.page.data then
        if #cfg.page.data == 0 then -- 因为lua 只算从1开始的 所以如果索引为[0]  长度也为0
            if cfg.page.data[0] and cfg.page.data[0] ~= 0 then
                conform = menu_bot_data.GetOriginPage() == cfg.page.data[0]
            else
                conform = true
            end
        else
            for key, value in pairs(cfg.page.data)do
                if menu_bot_data.GetOriginPage() == value then
                    conform = true
                    break
                end
            end
        end
    else
        conform = true
    end
    return conform
end

--执行常驻ui的引导
function CheckTask()
    Init()                    
    -- --print("CheckTask()")
    -- curPriority = nil
    -- statements
    local execute
    for ind, cfg in ipairs(SaveCfgs) do
        local nId = cfg.id
        ------print(nId,1)
        if CheckValid(cfg) then
            ------print(nId,2)
            if moduleRelate[nId] then
              local obj = GetObj(nId)
                -- and obj.activeInHierarchy
                ------print(nId,3)

                if obj and not util.IsObjNull(obj) and Check(nId) and CheckPage(cfg) then
                    ------print(nId,4)
                    if cfg.eftType ~= 8 then
                        local btn = obj:GetComponent(typeof(Button))
                        local canvas = obj:GetComponentInParent(typeof(Canvas))
                        execute = ShowReal(
                            moduleRelate[nId][2],
                            nId,
                            obj.transform,
                            GetTips(nId),
                            function(isClickSelf, call)
                                if isClickSelf then
                                    btn.onClick:Invoke()
                                    CheckCallback(nId)
                                end
                                if call then
                                    callback(nId)
                                end
                            end,
                            nil,
                            nil,
                            GetRot(nId),
                            canvas.transform
                        )
                        if execute then
                            return
                        end
                    else
                        Show3DGuide(nId)
                        return
                    end
                    
                end
            end
        end
    end
    if not curNameId then
        Close()
    end
end
