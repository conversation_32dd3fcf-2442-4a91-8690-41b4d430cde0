-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local V=protobuf.EnumValueDescriptor
local F=protobuf.FieldDescriptor
local D=protobuf.Descriptor
local E=protobuf.EnumDescriptor
local M=protobuf.Message
module('rechargeGift_pb')


V1M=V(4,"enRechargeGiftHeadingCode_PaidTask",0,102)
V2M=V(4,"enRechargeGiftHeadingCode_Festival",1,104)
V3M=V(4,"enRechargeGiftHeadingCode_DualBattlePass",2,1001)
V4M=V(4,"enRechargeGiftHeadingCode_TripleBattlePass",3,1002)
V5M=V(4,"enRechargeGiftHeadingCode_NoMissionBattlePass",4,1003)
V6M=V(4,"enRechargeGiftHeadingCode_Commander",5,1008)
V7M=V(4,"enRechargeGiftHeadingCode_DailySpecials",6,1010)
V8M=V(4,"enRechargeGiftHeadingCode_WeeklyCard",7,1011)
V9M=V(4,"enRechargeGiftHeadingCode_MonthlyCard",8,1012)
V10M=V(4,"enRechargeGiftHeadingCode_DiamondMall",9,1015)
E1M=E(3,"enRechargeGiftHeadingCode",".CSMsg.enRechargeGiftHeadingCode")
V11M=V(4,"enRechargeGiftType_None",0,0)
V12M=V(4,"enRechargeGiftType_Festival",1,1)
V13M=V(4,"enRechargeGiftType_WeeklyCard",2,2)
V14M=V(4,"enRechargeGiftType_MonthlyCard",3,3)
V15M=V(4,"enRechargeGiftType_SpecialGift",4,4)
V16M=V(4,"enRechargeGiftType_InheritGift",5,5)
V17M=V(4,"enRechargeGiftType_Scientific_Res",6,6)
V18M=V(4,"enRechargeGiftType_Max",7,7)
E2M=E(3,"enRechargeGiftType",".CSMsg.enRechargeGiftType")
V19M=V(4,"enRechargeGiftFreshType_None",0,0)
V20M=V(4,"enRechargeGiftFreshType_Daily",1,1)
V21M=V(4,"enRechargeGiftFreshType_Weekly",2,2)
V22M=V(4,"enRechargeGiftFreshType_WeeklyByPerson",3,3)
V23M=V(4,"enRechargeGiftFreshType_Monthly",4,4)
V24M=V(4,"enRechargeGiftFreshType_MonthlyByPerson",5,5)
V25M=V(4,"enRechargeGiftFreshType_Naturally",6,6)
V26M=V(4,"enRechargeGiftFreshType_Max",7,7)
E3M=E(3,"enRechargeGiftFreshType",".CSMsg.enRechargeGiftFreshType")
V27M=V(4,"enRechargeGiftPrivilegeType_None",0,0)
V28M=V(4,"enRechargeGiftPrivilegeType_Buff",1,1)
V29M=V(4,"enRechargeGiftPrivilegeType_Function",2,2)
V30M=V(4,"enRechargeGiftPrivilegeType_Building",3,3)
V31M=V(4,"enRechargeGiftPrivilegeType_Max",4,4)
E4M=E(3,"enRechargeGiftPrivilegeType",".CSMsg.enRechargeGiftPrivilegeType")
V32M=V(4,"enRechargeGiftInitCfgId_EveryDayInheritGift",0,8252)
V33M=V(4,"enRechargeGiftInitCfgId_EveryWeekInheritGift",1,8253)
V34M=V(4,"enRechargeGiftInitCfgId_NaturallyInheritGift",2,8254)
E5M=E(3,"enRechargeGiftInitCfgId",".CSMsg.enRechargeGiftInitCfgId")
F1D=F(2,"rechargeId",".CSMsg.TPbRechargeGiftData.rechargeId",1,0,2,false,0,4,4)
F2D=F(2,"giftType",".CSMsg.TPbRechargeGiftData.giftType",2,1,2,false,0,13,3)
F3D=F(2,"indexId",".CSMsg.TPbRechargeGiftData.indexId",3,2,2,false,0,13,3)
F4D=F(2,"buyTimes",".CSMsg.TPbRechargeGiftData.buyTimes",4,3,2,false,0,13,3)
F5D=F(2,"startTime",".CSMsg.TPbRechargeGiftData.startTime",5,4,2,false,0,13,3)
F6D=F(2,"endTime",".CSMsg.TPbRechargeGiftData.endTime",6,5,2,false,0,13,3)
F7D=F(2,"freshType",".CSMsg.TPbRechargeGiftData.freshType",7,6,2,false,0,13,3)
F8D=F(2,"freshTime",".CSMsg.TPbRechargeGiftData.freshTime",8,7,2,false,0,13,3)
F9D=F(2,"rewardLastTime",".CSMsg.TPbRechargeGiftData.rewardLastTime",9,8,2,false,0,13,3)
F10D=F(2,"rewardTimes",".CSMsg.TPbRechargeGiftData.rewardTimes",10,9,2,false,0,13,3)
M1G=D(1,"TPbRechargeGiftData",".CSMsg.TPbRechargeGiftData",false,{},{},nil,{})
F11D=F(2,"data",".CSMsg.TPbRechargeGiftPart.data",1,0,3,false,{},11,10)
M2G=D(1,"TPbRechargeGiftPart",".CSMsg.TPbRechargeGiftPart",false,{},{},nil,{})
F12D=F(2,"data",".CSMsg.TMSG_RECHARGE_GIFT_DATA_NTF.data",1,0,3,false,{},11,10)
M3G=D(1,"TMSG_RECHARGE_GIFT_DATA_NTF",".CSMsg.TMSG_RECHARGE_GIFT_DATA_NTF",false,{},{},nil,{})
F13D=F(2,"rechargeId",".CSMsg.TMSG_RECHARGE_GIFT_GET_FREE_REQ.rechargeId",1,0,2,false,0,13,3)
M4G=D(1,"TMSG_RECHARGE_GIFT_GET_FREE_REQ",".CSMsg.TMSG_RECHARGE_GIFT_GET_FREE_REQ",false,{},{},nil,{})
F14D=F(2,"errorCode",".CSMsg.TMSG_RECHARGE_GIFT_GET_FREE_RSP.errorCode",1,0,2,false,0,13,3)
F15D=F(2,"rewardId",".CSMsg.TMSG_RECHARGE_GIFT_GET_FREE_RSP.rewardId",2,1,3,false,{},13,3)
M5G=D(1,"TMSG_RECHARGE_GIFT_GET_FREE_RSP",".CSMsg.TMSG_RECHARGE_GIFT_GET_FREE_RSP",false,{},{},nil,{})
F16D=F(2,"selectId",".CSMsg.TMSG_RECHARGE_GIFT_SELECT_REQ.selectId",1,0,2,false,0,13,3)
M6G=D(1,"TMSG_RECHARGE_GIFT_SELECT_REQ",".CSMsg.TMSG_RECHARGE_GIFT_SELECT_REQ",false,{},{},nil,{})
F17D=F(2,"errorCode",".CSMsg.TMSG_RECHARGE_GIFT_SELECT_RSP.errorCode",1,0,2,false,0,13,3)
M7G=D(1,"TMSG_RECHARGE_GIFT_SELECT_RSP",".CSMsg.TMSG_RECHARGE_GIFT_SELECT_RSP",false,{},{},nil,{})
F18D=F(2,"rechargeId",".CSMsg.TMSG_RECHARGE_GIFT_GET_FOLLOWUP_REQ.rechargeId",1,0,2,false,0,13,3)
M8G=D(1,"TMSG_RECHARGE_GIFT_GET_FOLLOWUP_REQ",".CSMsg.TMSG_RECHARGE_GIFT_GET_FOLLOWUP_REQ",false,{},{},nil,{})
F19D=F(2,"errorCode",".CSMsg.TMSG_RECHARGE_GIFT_GET_FOLLOWUP_RSP.errorCode",1,0,2,false,0,13,3)
F20D=F(2,"rechargeId",".CSMsg.TMSG_RECHARGE_GIFT_GET_FOLLOWUP_RSP.rechargeId",2,1,2,false,0,13,3)
F21D=F(2,"rewardId",".CSMsg.TMSG_RECHARGE_GIFT_GET_FOLLOWUP_RSP.rewardId",3,2,3,false,{},13,3)
M9G=D(1,"TMSG_RECHARGE_GIFT_GET_FOLLOWUP_RSP",".CSMsg.TMSG_RECHARGE_GIFT_GET_FOLLOWUP_RSP",false,{},{},nil,{})
F22D=F(2,"rechargeId",".CSMsg.TMSG_RECHARGE_GIFT_BUY_NTF.rechargeId",1,0,2,false,0,13,3)
F23D=F(2,"giftType",".CSMsg.TMSG_RECHARGE_GIFT_BUY_NTF.giftType",2,1,2,false,0,13,3)
M10G=D(1,"TMSG_RECHARGE_GIFT_BUY_NTF",".CSMsg.TMSG_RECHARGE_GIFT_BUY_NTF",false,{},{},nil,{})

E1M.values = {V1M,V2M,V3M,V4M,V5M,V6M,V7M,V8M,V9M,V10M}
E2M.values = {V11M,V12M,V13M,V14M,V15M,V16M,V17M,V18M}
E3M.values = {V19M,V20M,V21M,V22M,V23M,V24M,V25M,V26M}
E4M.values = {V27M,V28M,V29M,V30M,V31M}
E5M.values = {V32M,V33M,V34M}
M1G.fields={F1D, F2D, F3D, F4D, F5D, F6D, F7D, F8D, F9D, F10D}
F11D.message_type=M1G
M2G.fields={F11D}
F12D.message_type=M1G
M3G.fields={F12D}
M4G.fields={F13D}
M5G.fields={F14D, F15D}
M6G.fields={F16D}
M7G.fields={F17D}
M8G.fields={F18D}
M9G.fields={F19D, F20D, F21D}
M10G.fields={F22D, F23D}

TMSG_RECHARGE_GIFT_BUY_NTF =M(M10G)
TMSG_RECHARGE_GIFT_DATA_NTF =M(M3G)
TMSG_RECHARGE_GIFT_GET_FOLLOWUP_REQ =M(M8G)
TMSG_RECHARGE_GIFT_GET_FOLLOWUP_RSP =M(M9G)
TMSG_RECHARGE_GIFT_GET_FREE_REQ =M(M4G)
TMSG_RECHARGE_GIFT_GET_FREE_RSP =M(M5G)
TMSG_RECHARGE_GIFT_SELECT_REQ =M(M6G)
TMSG_RECHARGE_GIFT_SELECT_RSP =M(M7G)
TPbRechargeGiftData =M(M1G)
TPbRechargeGiftPart =M(M2G)
enRechargeGiftFreshType_Daily = 1
enRechargeGiftFreshType_Max = 7
enRechargeGiftFreshType_Monthly = 4
enRechargeGiftFreshType_MonthlyByPerson = 5
enRechargeGiftFreshType_Naturally = 6
enRechargeGiftFreshType_None = 0
enRechargeGiftFreshType_Weekly = 2
enRechargeGiftFreshType_WeeklyByPerson = 3
enRechargeGiftHeadingCode_Commander = 1008
enRechargeGiftHeadingCode_DailySpecials = 1010
enRechargeGiftHeadingCode_DiamondMall = 1015
enRechargeGiftHeadingCode_DualBattlePass = 1001
enRechargeGiftHeadingCode_Festival = 104
enRechargeGiftHeadingCode_MonthlyCard = 1012
enRechargeGiftHeadingCode_NoMissionBattlePass = 1003
enRechargeGiftHeadingCode_PaidTask = 102
enRechargeGiftHeadingCode_TripleBattlePass = 1002
enRechargeGiftHeadingCode_WeeklyCard = 1011
enRechargeGiftInitCfgId_EveryDayInheritGift = 8252
enRechargeGiftInitCfgId_EveryWeekInheritGift = 8253
enRechargeGiftInitCfgId_NaturallyInheritGift = 8254
enRechargeGiftPrivilegeType_Buff = 1
enRechargeGiftPrivilegeType_Building = 3
enRechargeGiftPrivilegeType_Function = 2
enRechargeGiftPrivilegeType_Max = 4
enRechargeGiftPrivilegeType_None = 0
enRechargeGiftType_Festival = 1
enRechargeGiftType_InheritGift = 5
enRechargeGiftType_Max = 7
enRechargeGiftType_MonthlyCard = 3
enRechargeGiftType_None = 0
enRechargeGiftType_Scientific_Res = 6
enRechargeGiftType_SpecialGift = 4
enRechargeGiftType_WeeklyCard = 2

