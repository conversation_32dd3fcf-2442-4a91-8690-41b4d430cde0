---
--- Created by: yuan<PERSON>.
--- DateTime: 2025/3/11.
--- Desc: 迁城选择Hud
-- todo 后续支持range不一样的城池显示不同的底座区域 

local require = require
local newclass = newclass
local string = string
local tostring = tostring

local SpriteRenderer = CS.UnityEngine.SpriteRenderer
local SpriteSwitcher = CS.War.UI.SpriteSwitcher
local SimpleTextOutline = CS.TextMeshUtil.SimpleTMOutline
local SpriteFitSize = CS.TextMeshUtil.SpriteFitSize

local game_scheme = require "game_scheme"
local data_personalInfo = require "data_personalInfo"
local sand_hud_base = require "sand_hud_base"

module("gw_comp_move_chose_hud")

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
---@class GWCompMoveChoseHud : GWDisposableObject
local GWCompMoveChoseHud = newclass("gw_comp_move_chose_hud", sand_hud_base)

GWCompMoveChoseHud.widget_table = {
    sp_range = { path = "listBg/sp_range", type = SpriteRenderer },
    sw_range = { path = "listBg/sp_range", type = SpriteSwitcher },
    sw_1 = { path = "node/1", type = SpriteSwitcher },
    sw_2 = { path = "node/2", type = SpriteSwitcher },
    sw_3 = { path = "node/3", type = SpriteSwitcher },
    sw_4 = { path = "node/4", type = SpriteSwitcher },

    -- 跨服用玩家城池展示
    obj_crossService = { path = "obj_crossService", type = "GameObject" },
    obj_city = { path = "obj_crossService/obj_city", type = "Transform" },
    txt_level = { path = "obj_crossService/level/txt_level", type = "TextMesh" },
    sfs_name = { path = "obj_crossService/name", type = SpriteFitSize },
    txt_name = { path = "obj_crossService/name/txt_name", type = "TextMesh" },
    txt_name_outline = { path = "obj_crossService/name/txt_name", type = SimpleTextOutline },
}

function GWCompMoveChoseHud:InitHudComp(hudData, data)
    sand_hud_base.InitHudData(self, hudData)
    self:InstantiateModelAsync(data.resPath, data.parent)
end

---实例化成功 （现必须基成设置名字和组件Id）
function GWCompMoveChoseHud:InstantiateSuccess(_obj)
    sand_hud_base.InstantiateSuccess(self, _obj)
    self:SwitchSpriteRenderer(self.canMove)
    self:UpdateChangeTargetPos(self.hudData.targetPos)
    self:UpdateBaseRelocationState(self.isBaseRelocation)
end

function GWCompMoveChoseHud:SwitchSpriteRenderer(canMove)
    self.canMove = canMove
    if self:IsValid() then
        local curIndex = canMove and 0 or 1
        if not self.curIndex or self.curIndex ~= curIndex then
            self.curIndex = curIndex
            self.sw_range:SwitchSpriteRenderer(curIndex)
            self.sw_1:SwitchSpriteRenderer(curIndex)
            self.sw_2:SwitchSpriteRenderer(curIndex)
            self.sw_3:SwitchSpriteRenderer(curIndex)
            self.sw_4:SwitchSpriteRenderer(curIndex)
        end
    end
end

function GWCompMoveChoseHud:UpdateBaseRelocationState(isBaseRelocation)
    self.isBaseRelocation = isBaseRelocation
    if self:IsValid() then
        self.obj_crossService:SetActive(self.isBaseRelocation and true or false)
        if self.isBaseRelocation then
            self.txt_level.text = tostring(data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleLevel))
            local alliance_mgr = require "alliance_mgr"
            local allianceName = alliance_mgr.GetUserAllianceShortName()
            local userName = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleName)
            self.txt_name.text = string.format("[%s]%s", allianceName, userName)
            self.txt_name_outline:ManualUpdateOutline()
            self.sfs_name:UpdateBackground()

            -- 设置基地模型
            local schlossId = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.CityID)
            if self.schlossId and self.schlossId == schlossId then
                return
            end
            self.schlossId = schlossId
            self:LoadRoleSchloss(schlossId)
        end
    end
end

function GWCompMoveChoseHud:LoadRoleSchloss(schlossId)
    if self.roleSchloss then
        self.roleSchloss:Dispose()
        self.roleSchloss = nil
    end
    if schlossId then
        local schlossCfg = game_scheme:RoleSchloss_0(schlossId)
        if schlossCfg then
            local base_game_object = require "base_game_object"
            self.roleSchloss = base_game_object()
            self.roleSchloss:LoadResource(schlossCfg.SandModuleRoute, "", function()

            end, false, self.obj_city)
        end
    end
end

function GWCompMoveChoseHud:Dispose(unloadShowOnly)
    self.isBaseRelocation = false
    self.schlossId = nil
    self:LoadRoleSchloss(nil)
    sand_hud_base.Dispose(self, unloadShowOnly)
end

return GWCompMoveChoseHud
