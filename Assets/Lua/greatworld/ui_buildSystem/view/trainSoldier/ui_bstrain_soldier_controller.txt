
--- ui_bstrain_soldier_controller.txt
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by nyz.
--- DateTime: 
--- desc:    
---
local require = require
local pairs = pairs
local string = string
local table = table
local newclass = newclass

local event = require "event"
local module_scroll_list = require "scroll_list"
local log = require "log"
local controller_base = require "controller_base"
local player_mgr = require "player_mgr"
local flow_text = require "flow_text"
local game_scheme = require "game_scheme"
local windowMgr = require "ui_window_mgr"
local gw_home_common_data = require "gw_home_common_data"
local technology_data = require "technology_data"
local gw_home_building_data = require"gw_home_building_data"
local net_SpeedUp_module = require "net_speed_up_module"
local net_city_module = require "net_city_module"
local ui_bstrain_soldier = require "ui_bstrain_soldier"
local evt_sourceSupply_define = require "evt_sourceSupply_define"
local gw_home_soldier_data = require "gw_home_soldier_data"
local os = os
local lang = lang
local GWG = GWG
local tonumber = tonumber
local gw_ed = require "gw_ed"
local GWConst = GWConst
module("ui_bstrain_soldier_controller")
--TODO  类的实现方式后续需要优化，或者不用基类 现在的class的继承很耗时，
local controller = nil
local UIController = newclass("ui_bstrain_soldier_controller", controller_base)

--[[窗口初始化]]
function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self,view_name,controller_name)
    --data数据：
    --buildingId:当前选择的建筑
    --下面为临时数据。
    --[[
    data = {}
    data.sid = 2;
    ]]
    if data == nil then
        log.Error("兵营的data为nil！！！")
        return;
    end
    self.data = data;
    
    local SoldierData = {}
    local soldierList = gw_home_soldier_data.GetTrainningCenterData(); --当前的士兵列表
    local soldierListHelper = {} -- 协助查询
    if soldierList ~= nil and soldierList.soldiers ~= nil then
        for i,v in pairs(soldierList.soldiers) do --校场数据是唯一的，如果存在重复的士兵id那就是服务器数据有问题。
            if soldierListHelper[v.nSoldierID] ~= nil then
                log.Error("ID为"..v.nSoldierID.."的士兵数据在校场数组内出现了多次！")
            end
            soldierListHelper[v.nSoldierID] = v;
        end
    end
    
    --数据结构：nSoldierID：士兵ID；nCount:士兵数量；uDoneTime:完成时间；uStartTime：开始时间
    --[[
    SoldierData.trainingData =
    {
        nSoldierID = 311;
        nCount = 100;
        uDoneTime = 1723675214;
        uStartTime = ...;
    }
]]
    local fromCampus = data.fromCampus or false --是否来自校场
    local isTraining = gw_home_soldier_data.GetMilitaryIsRecruitBySid(data.sid) or gw_home_soldier_data.GetMilitaryIsUpgradeBySid(data.sid)--是否正在训练或者晋升
    self.buildingData = gw_home_building_data.GetBuildingDataBySid(data.sid);--当前的建筑信息
    SoldierData.soldierList = {}
    if self.buildingData ~= nil then
        SoldierData.buildingLevel = self.buildingData.nLevel;
    else
        SoldierData.buildingLevel = 1;--0;
    end
    SoldierData.buildingSid = data.sid;
    local campusLv = -1;--当前解锁的最高等级的士兵，从-1开始算起
    local selectSoldierIndex = -1;--当前选中的士兵的id，从-1开始。
    local selectSoldierLevel = 0;
    if isTraining then
        SoldierData.trainingData = gw_home_soldier_data.GetMilitaryDataBySid(data.sid);--当前训练的士兵信息
        selectSoldierIndex = SoldierData.trainingData.nSoldierID;
        selectSoldierLevel = game_scheme:Soldier_0(selectSoldierIndex).level
    end
    if fromCampus then
        selectSoldierIndex = data.selectSoldierIndex
        selectSoldierLevel = data.selectSoldierLevel
    end
    local soldierNums = game_scheme:Soldier_nums()
    local maxSoldierLv = -1
    for i = 1, soldierNums do
        local soldierNum = 0
        local inCount = 0
        --datas.campusLv = 3 --当前解锁的士兵最高等级.
        local soldierData = game_scheme:Soldier_1(i);
        local soldierID = soldierData.soldierID --311为起始Id 
        
        if soldierData.unlock.count > 1 then
            local unlock = soldierData.unlock.data;
            if unlock[1] <= SoldierData.buildingLevel then
                if unlock[2] == nil then
                    if campusLv < soldierData.level then
                        campusLv = soldierData.level;
                        if not isTraining and not fromCampus then --如果正在训练并且不来自校场
                            selectSoldierIndex = soldierID;
                            selectSoldierLevel = soldierData.level
                        end
                    end
                else
                    if unlock[3] <= technology_data.GetScientificLevelByIndex(unlock[2]) then
                        if campusLv < soldierData.level then
                            campusLv = soldierData.level;
                            if not isTraining and not fromCampus then
                                selectSoldierIndex = soldierID;
                                selectSoldierLevel = soldierData.level
                            end
                        end
                    end
                end
            end
            if soldierListHelper[soldierID] ~= nil then
                soldierNum = soldierListHelper[soldierID].nInCount + soldierListHelper[soldierID].nOutCount;--(datas.soldierID == 312 and 33) or (datas.soldierID == 313 and 34) or 0
                inCount = soldierListHelper[soldierID].nInCount or 0
            else
                soldierNum = 0;
            end
            --[[
            datas.bread = 2000
            datas.iron = 2000
            ]]
            local tempData =
            {
                soldierID = soldierID;
                soldierNum = soldierNum;
                inCount = inCount;
                level = soldierData.level;
            }
            SoldierData.soldierList[soldierID] = tempData;
            --table.insert(SoldierData.soldierList, datas)
        end
        if soldierData.level > maxSoldierLv then
            maxSoldierLv = soldierData.level
        end
    end
    SoldierData.campusLv = campusLv;
    SoldierData.selectSoldierIndex = selectSoldierIndex;
    SoldierData.selectSoldierLevel = selectSoldierLevel;
    SoldierData.maxLv = maxSoldierLv;
    
    self:TriggerUIEvent( "SetPanelInfo", SoldierData)
    
    self.SoldierData = SoldierData;
    --self:TriggerUIEvent( "UpdateAttributeList", data[1].soldierID)
    --self:TriggerUIEvent( "UpdateSoldierScrollList", data)
end

--[[界面被显示的时候调用]]
function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close()
    self.__base.Close(self)
    controller = nil
end

--会基类自动调用
function UIController:AutoSubscribeEvents()
    -- 注意 事件建议使用self:RegisterEvent(event_name,func)来注册，不需要手动维护注销了  
    self.updatePanel = function()
        self:TriggerUIEvent( "UpdateItemCount")
    end
    self:RegisterEvent(evt_sourceSupply_define.Evt_RefreshSourceNum,self.updatePanel)
    self.onFinishTrainingEvent = function(eventName,extraInfo)
        --windowMgr:UnloadModule(self.view_name)
        local tempStr = {}
        for part in string.gmatch(extraInfo,"([^#]+)")do
            table.insert(tempStr,tonumber(part));
        end
        local upgradeType = tempStr[1]
        if upgradeType == 2 then --2表示晋升
            flow_text.Add(lang.Get(601388))
            self:OnUpdatePanel()
            windowMgr:UnloadModule("ui_speed_up_panel")
        else
            --TODO lang表缺失，后面要补上
            flow_text.Add(lang.Get(601387))
            self:OnUpdatePanel()
            windowMgr:UnloadModule("ui_bs_promote")
            windowMgr:UnloadModule("ui_speed_up_panel")
        end
        
    end
    self:RegisterEvent(event.FINISH_SOLDIER_TRAINING,self.onFinishTrainingEvent)
    self.updateAllData = function(eventName,openData)
        if openData.uSid == self.data.sid then
            self:OnUpdatePanel()
        end
    end
    gw_ed.mgr:Register(gw_ed.GW_HOME_BUILDING_UPGRADE, self.updateAllData)
    --self:RegisterEvent(event.FINISH_SOLDIER_TREATMENT,self.onFinishTrainingEvent)
end
--会基类自动调用
function UIController:AutoUnsubscribeEvents()
    gw_ed.mgr:Unregister(gw_ed.GW_HOME_BUILDING_UPGRADE, self.updateAllData)
end
---********************功能函数区**********---

function UIController:OnBtnClose()
    windowMgr:UnloadModule(self.view_name)
end

function UIController:OnBuilding()
    local buildQueue = GWG.GWHomeMgr.buildingQueueData.GetQueueBySid(self.data.sid)
    if buildQueue == nil then
        local buildingData = gw_home_building_data.GetBuildingDataBySid(self.data.sid);
        windowMgr:ShowModule("ui_bs_upgrade",nil,nil,buildingData)
        windowMgr:UnloadModule(self.view_name)
    end
end
--刷新界面数据
function UIController:OnUpdatePanel()
    --先重新获取
    local soldierList = gw_home_soldier_data.GetTrainningCenterData(); --当前的士兵列表
    local soldierListHelper = {} -- 协助查询
    if soldierList ~= nil and soldierList.soldiers ~= nil then
        for i,v in pairs(soldierList.soldiers) do --校场数据是唯一的，如果存在重复的士兵id那就是服务器数据有问题。
            if soldierListHelper[v.nSoldierID] ~= nil then
                log.Error("ID为"..v.nSoldierID.."的士兵数据在校场数组内出现了多次！")
            end
            soldierListHelper[v.nSoldierID] = v;
        end
    end
    local isTraining = gw_home_soldier_data.GetMilitaryIsRecruitBySid(self.data.sid) or gw_home_soldier_data.GetMilitaryIsUpgradeBySid(self.data.sid)--是否正在训练或者晋升
    self.buildingData = gw_home_building_data.GetBuildingDataBySid(self.data.sid);--当前的建筑信息
    self.SoldierData.soldierList = {}
    if isTraining then
        self.SoldierData.trainingData = gw_home_soldier_data.GetMilitaryDataBySid(self.data.sid);--当前训练的士兵信息
    end
    local campusLv = -1;--当前解锁的最高等级的士兵，从-1开始算起
    if self.buildingData ~= nil then
        self.SoldierData.buildingLevel = self.buildingData.nLevel;
    else
        self.SoldierData.buildingLevel = 1;--0;
    end
    local soldierNums = game_scheme:Soldier_nums()
    for i = 1, soldierNums do
        local soldierNum = 0
        local inCount = 0
        --datas.campusLv = 3 --当前解锁的士兵最高等级.
        local soldierData = game_scheme:Soldier_1(i);
        local soldierID = soldierData.soldierID 
        
        if soldierData.unlock.count > 1 then
            local unlock = soldierData.unlock.data;
            if unlock[1] <= self.SoldierData.buildingLevel then
                if unlock[2] == nil then
                    if campusLv < soldierData.level then
                        campusLv = soldierData.level;
                    end
                else
                    if unlock[3] <= technology_data.GetScientificLevelByIndex(unlock[2]) then
                        if campusLv < soldierData.level then
                            campusLv = soldierData.level;
                        end
                    end
                end
            end
            if soldierListHelper[soldierID] ~= nil then
                soldierNum = soldierListHelper[soldierID].nInCount + soldierListHelper[soldierID].nOutCount;--(datas.soldierID == 312 and 33) or (datas.soldierID == 313 and 34) or 0
                inCount = soldierListHelper[soldierID].nInCount
            else
                soldierNum = 0;
            end
            --[[
            datas.bread = 2000
            datas.iron = 2000
            ]]
            local tempData =
            {
                soldierID = soldierID;
                soldierNum = soldierNum;
                inCount = inCount;
                level = soldierData.level;
            }
            self.SoldierData.soldierList[soldierID] = tempData;
            --table.insert(SoldierData.soldierList, datas)
        end

    end
    self.SoldierData.campusLv = campusLv;
    self:TriggerUIEvent( "SetPanelInfo", self.SoldierData,true)
end

--一键训练士兵
function UIController:OnFinishTraining()
    --log.Error("一键训练士兵")
    local soldierTrainingData = ui_bstrain_soldier:GetTrainingData()
    if soldierTrainingData.count <= 0 then
        flow_text.Add(lang.Get(130320))
        return
    end
    if soldierTrainingData.isEnough then
        local result,timeLeft = gw_home_common_data.GetSoldierSpeedUpItemList(soldierTrainingData.needTime)
        if timeLeft <= 0 then
            local arrItems = {}
            local accTipDataList = {}
            for i,v in pairs(result.data) do
                local useItemData =
                {
                    itemid = v.id,
                    nums = v.cost,
                }
                table.insert(arrItems,useItemData);
                local temp = {}
                temp.goodId = v.id;
                temp.goodCount = v.cost;
                temp.cfgData = game_scheme:Item_0(v.id);
                temp.itemCount = v.totalCount;
                temp.type = v.type;--排序用
                temp.time = v.time;--排序用
                table.insert(accTipDataList,temp);
            end
            local data2 =
            {
                moudleid = 3;
                extraInfo = "1#"..self.data.sid.."#"..soldierTrainingData.id.."#0#"..soldierTrainingData.count;
                isBegin = false;
                arrItems = arrItems;
            }

            local accTipData =
            {
                InfoData = {
                    title = 15512;
                    time = result.totalTime;--totalTime;
                    color = "#319F38";
                },
                ListData = accTipDataList,
                confirmCallback = function()
                    net_SpeedUp_module.MSG_SPEEDUPONECLICKUSEITEM_REQ(data2);
                end
            }

            table.sort(accTipData.ListData,function(a, b)
                if a.type ~= b.type then
                    return a.type ~= 5 and b.type == 5
                else
                    return a.time > b.time
                end
            end)
            windowMgr:ShowModule("ui_bs_acctip",nil,nil,accTipData)
        else
            local GWAdmin = require "gw_admin"
            local needDiamond = GWAdmin.HomeCommonUtil.GetDiamondCostBySecond(soldierTrainingData.needTime)
            local callback = function()
                local data3 =
                {
                    moudleid = 3;
                    extraInfo = "1#"..self.data.sid.."#"..soldierTrainingData.id.."#0#"..soldierTrainingData.count;
                    isBegin = false;
                    diamondNums = needDiamond;
                }
                net_SpeedUp_module.MSG_SPEEDUPONECLICKUSEDIAMOND_REQ(data3);
            end
            local value = player_mgr.GetDiamondTips();
            if value then
                local message_box = require "message_box"
                message_box.Open(string.format2(lang.Get(601260),needDiamond), message_box.STYLE_YESNO, function(d, r)
                    if r == message_box.RESULT_YES then
                        callback()
                    else

                    end
                end, nil, lang.Get(30708), lang.Get(30707), lang.KEY_SYSTEM_TIPS, nil, true, nil, nil, nil, nil, nil, nil, lang.Get(182147),
                        function(isOn)
                            player_mgr.SetDiamondTips(not isOn)
                        end)
            else
                callback()
            end
        end
    else
        --物品不足
        --log.Error("物品不足")
        local needData = {
            list={},
        }
        table.insert(needData.list,{
            goodsId = 35, --粮食
            needNum = soldierTrainingData.needBread,
        })
        table.insert(needData.list,{
            goodsId = 36, --铁矿
            needNum = soldierTrainingData.needIron,
        })
        local result,timeLeft = gw_home_common_data.GetSoldierSpeedUpItemList(soldierTrainingData.needTime)
        if timeLeft <= 0 then --物品不足，加速足
            event.Trigger(evt_sourceSupply_define.Evt_ShowSupplyPanel,needData);
        else --物品加速都不足，走一键钻石
            local GWAdmin = require "gw_admin"
            local needBread = soldierTrainingData.needBread - player_mgr.GetPlayerFood()
            local needIron = soldierTrainingData.needIron - player_mgr.GetPlayerIron()
            local needDiamond = GWAdmin.HomeCommonUtil.GetDiamondCostBySecond(soldierTrainingData.needTime)
            if needBread > 0 then
                needDiamond = needDiamond + GWG.GWHomeMgr.commonData.GetResourceToDiamond({id = 35,needNumber = needBread})
            end
            if needIron > 0 then
                needDiamond = needDiamond + GWG.GWHomeMgr.commonData.GetResourceToDiamond({id = 36,needNumber = needIron})
            end

            local callback = function()
                local data3 =
                {
                    moudleid = 3;
                    extraInfo = "1#"..self.data.sid.."#"..soldierTrainingData.id.."#0#"..soldierTrainingData.count;
                    isBegin = false;
                    diamondNums = needDiamond;
                }
                net_SpeedUp_module.MSG_SPEEDUPONECLICKUSEDIAMOND_REQ(data3);
            end

            local value = player_mgr.GetDiamondTips();
            local value2 = player_mgr.GetDiamondItemTips();
            if value then
                local message_box = require "message_box"
                message_box.Open(string.format2(lang.Get(601260),needDiamond), message_box.STYLE_YESNO, function(d, r)
                    if r == message_box.RESULT_YES then
                        if not value2 then
                            callback()
                        else
                            player_mgr.SetDiamondItemTips(false)
                            message_box.Open(lang.Get(601530), message_box.STYLE_YESNO, function(j, k)
                                if k == message_box.RESULT_YES then
                                    event.Trigger(evt_sourceSupply_define.Evt_ShowSupplyPanel,needData);
                                else
                                    callback()
                                end
                            end, nil, lang.Get(601532), lang.Get(601531), lang.Get(601529), nil, true, nil, nil, nil, nil, nil, nil, nil,
                                    nil)
                        end
                    else

                    end
                end, nil, lang.Get(30708), lang.Get(30707), lang.KEY_SYSTEM_TIPS, nil, true, nil, nil, nil, nil, nil, nil, lang.Get(182147),
                        function(isOn)
                            player_mgr.SetDiamondTips(not isOn)
                        end)
            else
                callback()
            end
        end
        
    end
    
end

function UIController:OnScrollMoveToTarget()
    self:TriggerUIEvent( "OnScrollToTargetPosByAnim")
end

function UIController:OnGoToTechnology()
    local buildingData = GWG.GWHomeMgr.buildingData.GetMaxLevelBuildingDataByBuildingID(GWConst.ScientificBuildingId.Building_1); --第一科研中心肯定会先于第二科研中心创建
    if buildingData then
        windowMgr:ShowModule("ui_technology_main_panel")
    else
        buildingData = GWG.GWHomeMgr.buildingData.GetMaxLevelBuildingDataByBuildingID(GWConst.ScientificBuildingId.Building_2);--虽然我不觉得应该会出现在这里，但还是补一下吧
        if buildingData then
            windowMgr:ShowModule("ui_technology_main_panel")
        else
            flow_text.Add(lang.Get(1718))
        end
    end
    
end

function UIController:OnStartTraining()
    --log.Error("训练士兵")
    local soldierTrainingData = ui_bstrain_soldier:GetTrainingData()
    if soldierTrainingData.count <= 0 then
        flow_text.Add(lang.Get(130320))
        return
    end
    if self.data.sid <= 0 then
        log.Error("建筑的sid不符合要求！sid："..self.data.sid)
        return
    end
    if soldierTrainingData.isEnough then
        net_city_module.MSG_CITY_MILITARY_RECRUIT_SOLDIER_REQ(self.data.sid,soldierTrainingData.id,soldierTrainingData.count)
    else
        local needData = {
            list={},
        }
        table.insert(needData.list,{
            goodsId = 35, --粮食
            needNum = soldierTrainingData.needBread,
        })
        table.insert(needData.list,{
            goodsId = 36, --铁矿
            needNum = soldierTrainingData.needIron,
        })
        event.Trigger(evt_sourceSupply_define.Evt_ShowSupplyPanel,needData);
    end
end

function UIController:OnTrainingCallback()
    windowMgr:UnloadModule(self.view_name)
end

function UIController:OnSpeedUp()
    --log.Error("加速")
    local ui_speed_up_panel_controller = require "ui_speed_up_panel_controller"
    local timeLeft = self.SoldierData.trainingData.uDoneTime - os.server_time()
    
    local sendData = {}
    if gw_home_soldier_data.GetMilitaryIsRecruitBySid(self.data.sid) then
        sendData.type = ui_speed_up_panel_controller.SpeedUpTypeEnum.TrainingSoldier;
    elseif gw_home_soldier_data.GetMilitaryIsUpgradeBySid(self.data.sid) then
        sendData.type = ui_speed_up_panel_controller.SpeedUpTypeEnum.UpgradeSoldier;
    end
    sendData.soldierID = self.SoldierData.trainingData.nSoldierID;
    sendData.count = self.SoldierData.trainingData.nCount;
    sendData.curResearchCompleteTime = timeLeft;
    sendData.sid = self.data.sid;
    sendData.cfgData = game_scheme:Soldier_0(sendData.soldierID);

    windowMgr:ShowModule("ui_speed_up_panel",nil,nil,sendData)
end

function UIController:OnIconUp()
    local upgradeSoldierData = ui_bstrain_soldier:GetUpgradeData()
    upgradeSoldierData.campusLv = self.buildingData.nLevel;
    upgradeSoldierData.uSid = self.data.sid;
    windowMgr:ShowModule("ui_bs_promote",nil, nil, upgradeSoldierData)
end

function UIController:OnIconUpGray()
    flow_text.Add(lang.Get(601512))
end

function UIController:OnButtonAdd()
    self:TriggerUIEvent( "ResCalculation", 1)
end

function UIController:OnButtonMinus()
    self:TriggerUIEvent( "ResCalculation", -1)
end

function UIController:OnValueChanged()
    self:TriggerUIEvent( "ResCalculation")
end

function UIController:OnHideTips()
    self:TriggerUIEvent( "HideTips")
end

function UIController:OnScrollMove()
    self:TriggerUIEvent( "CheckUpdateArrow")
end

---********************end功能函数区**********---
--region ModuleFunction 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
