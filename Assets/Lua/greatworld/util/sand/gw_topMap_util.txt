---
--- Created by: yuan<PERSON>.
--- DateTime: 2024/8/16.
--- Desc: 沙盘顶层界面工具，管理顶层界面预制的动态加载和修改色值
---
local ipairs = ipairs
local mathRandom = math.random

local gw_comp_name = require "gw_comp_name"
local gw_admin = require "gw_admin"
local gw_const = require "gw_const"
local gw_mgr = require "gw_mgr"
local gw_sand_node = require "gw_sand_node"
local res_pool = require "res_pool"
local game_scheme = require "game_scheme"
local sandbox_pb = require "sandbox_pb"

local prefabPath = "art/greatworld/sand/scenenew/top/gwsandtopzoom.prefab"

local GWTopMapUtil = {}
local GWTopMapComp

function GWTopMapUtil.SyncTopMapState(level)
    if gw_mgr.curScene ~= gw_const.ESceneType.Sand then
        return
    end
    if level <= gw_const.SandTopMapLevel then
        GWTopMapUtil.OpenTopMapView()
    else
        GWTopMapUtil.CloseTopMapView()
    end
end

function GWTopMapUtil.OnSandEntityNtf(data)
    if data.SandboxEntity then
        for k, v in ipairs(data.SandboxEntity) do
            if v then
                GWTopMapUtil.UpdateTopMapView(v)
            end
        end
    end
end

function GWTopMapUtil.OpenTopMapView()
    if GWTopMapComp and GWTopMapComp.entity then
        GWTopMapComp:Open()
    else
        GWTopMapUtil.CreateComp(true)
    end
end

function GWTopMapUtil.CloseTopMapView()
    if GWTopMapComp and GWTopMapComp.entity then
        GWTopMapComp:Close()
    end
end

function GWTopMapUtil.UpdateTopMapView(sandboxEntity)
    GWTopMapUtil.CreateComp(false)
    GWTopMapComp:SetNeutralCityState(sandboxEntity)
end

function GWTopMapUtil.CreateComp(state)
    if not GWTopMapComp then
        local gw_id_mgr = require "gw_id_mgr"
        local compId = gw_id_mgr:AllocComponentId()
        GWTopMapComp = gw_admin.PopComponent(gw_comp_name.comp_topMap, compId)
        GWTopMapComp.isDisposed = false
        GWTopMapComp.isLoaded = false
    end
    if state then
        GWTopMapComp.entity = GWTopMapComp.entity or res_pool.get_res(prefabPath, function(res_vo1)
            if GWTopMapComp.isDisposed then
                res_vo1.cb = nil
                return
            end
            GWTopMapComp.isLoaded = true
            local gameObject = res_vo1.go
            gameObject.name = gw_comp_name.comp_topMap
            local transform = gameObject.transform
            transform:SetParent(gw_sand_node.top1Node(), false)

            GWTopMapComp:Bind(res_vo1.go)
            GWTopMapComp:Open()
            --使用假数据
            --GWTopMapUtil.CreateFakeData()
        end)
    end
end

--- 清理map
function GWTopMapUtil.Dispose()
    if not GWTopMapComp then
        return
    end

    if GWTopMapComp.entity then
        res_pool.return_res(GWTopMapComp.entity)
        GWTopMapComp.entity = nil
    end
    gw_admin.PushComponent(GWTopMapComp)
    GWTopMapComp.isDisposed = true
    GWTopMapComp.isLoaded = false
    GWTopMapComp = nil
end

--region假数据
function GWTopMapUtil.CreateFakeData()
    if not GWTopMapComp then
        return
    end

    for i = 1, 30 do
        local regionIndex = mathRandom(1, 90)
        local regionCfg = game_scheme:SandMapRegion_0(regionIndex)
        local posStr = regionCfg.CityPos
        -- 使用 string.match 函数将字符串拆分并捕获两个数字
        local x, y = string.match(posStr, "(%d+)%#(%d+)")
        -- Convert captured strings to numbers
        x = tonumber(x)
        y = tonumber(y)
        local sandBoxEntity = {
            type = sandbox_pb.enSandboxEntity_NeutralCity,
            pos = {
                x = x,
                y = y
            },
            props = {
                { propID = sandbox_pb.enSXEntityProp_AllianceID, value = 100 },
                { propID = sandbox_pb.enSXEntityProp_Neutral_AllianceIdx, value = i % 14 }
            }
        }
        GWTopMapComp:SetNeutralCityState(sandBoxEntity)
    end
end
--endregion

return GWTopMapUtil