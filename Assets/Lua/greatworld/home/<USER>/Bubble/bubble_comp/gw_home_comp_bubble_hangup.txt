---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON><PERSON>ua)
--- Created by ha<PERSON><PERSON><PERSON>.
--- DateTime: 2024/8/16 16:53
--- Des: 功能类型气泡
local require = require
local newclass = newclass
local os = os
local math = require "math"
local time_util = require "time_util"
local gw_hangup_mgr = require "gw_hangup_mgr"
local gw_hangup_data = require "gw_hangup_data"
local util = require "util"
local gw_home_comp_bubble_base = require "gw_home_comp_build_bubble_base"
local gw_home_card_sprite_asset_mgr = require "gw_home_card_sprite_asset_mgr"
local HangUpStautsEnum = gw_hangup_mgr.HangUpStautsEnum
local UIUtil = CS.Common_Util.UIUtil
module("gw_home_comp_bubble_hangup")
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
---@class gw_home_comp_bubble_hangup : GWDisposableObject
local GWHomeCompBubbleHangUp = newclass("gw_home_comp_bubble_hangup", gw_home_comp_bubble_base)

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

function GWHomeCompBubbleHangUp:InitData(buildData, bindParent, parent, bubbleType)
    gw_home_comp_bubble_base.InitData(self, buildData, bindParent, bubbleType)
    --读表 获取模型
    self:InstantiateModelAsync("ui/prefabs/gw/buildsystem/bubbleui/bubblehangupui.prefab", parent);
end

function GWHomeCompBubbleHangUp:InstantiateSuccess(_obj)
    if util.IsObjNull(self.spriteAsset) then
        self.spriteAsset = gw_home_card_sprite_asset_mgr.GetOrCreateCardSpriteAsset("gwbubble")
    end
    self.bgImage = UIUtil.GetComponent(_obj.transform, "Image", "Image")
    gw_home_comp_bubble_base.InstantiateSuccess(self, _obj)
    self.TimeText = UIUtil.GetComponent(_obj.transform, "Text", "Text")
    self.hangUpCurTime = 0
    self.hangUpRefreshTime = 60
    self:RefreshTime()
end

function GWHomeCompBubbleHangUp:OnUpdate(timer)
    if self.hangUpCurTime then
        self.hangUpCurTime = self.hangUpCurTime + timer
        if self.hangUpCurTime >= self.hangUpRefreshTime then
            self.hangUpCurTime = self.hangUpCurTime - self.hangUpRefreshTime
            self:RefreshTime()
        end
    end
    return gw_home_comp_bubble_base.OnUpdate(self, timer)
end

function GWHomeCompBubbleHangUp:ClearData()
    self.spriteAsset = nil
    self._data = nil
    self.hangUpIcon = nil
    gw_home_comp_bubble_base.ClearData(self)
end

function GWHomeCompBubbleHangUp:RefreshTime()
    if util.IsObjNull(self.TimeText) or util.IsObjNull(self.bgImage) then
        return
    end

    local HangUpDate = gw_hangup_data.GetHangUpDate()
    local hangUpstartTime = HangUpDate.timeStamp
    local refashStaut = HangUpStautsEnum.Empty
    if hangUpstartTime and hangUpstartTime > 0 then
        local curHangUpCfg = gw_hangup_mgr.GetCurHangUpCfg()
        local curTime = os.server_time()
        local intervalTime = curTime - hangUpstartTime
        local maxHangUpTime = gw_hangup_mgr.GetCurHangUpTime()
        -- log.Error("当前挂机时间小于最小间隔时间",intervalTime,curHangUpCfg.minInterval,gw_hangup_mgr.GetCurHangUpTime())
        if intervalTime >= maxHangUpTime then
            refashStaut = HangUpStautsEnum.Full
            self.TimeText.text = time_util.FormatTimeXManToMin(math.ceil(maxHangUpTime))
            if self.hangUpIcon ~= 10000 then
                self.hangUpIcon = 10000
                self.spriteAsset:GetSprite(self.hangUpIcon, function(sprite)
                    if not util.IsObjNull(self.bgImage) and not util.IsObjNull(sprite) then
                        self.bgImage.sprite = sprite
                    end
                end)
            end
        end
        if intervalTime < maxHangUpTime and intervalTime > curHangUpCfg.minInterval then
            local diff = intervalTime / maxHangUpTime
            if 0 < diff and diff < 1 / 3 then
                refashStaut = HangUpStautsEnum.Little
            elseif 1 / 3 < diff and diff < 2 / 3 then
                refashStaut = HangUpStautsEnum.Lots
            end
            self.TimeText.text = time_util.FormatTimeXManToMin(math.ceil(intervalTime))
            if self.hangUpIcon ~= 23 then
                self.hangUpIcon = 23
                self.spriteAsset:GetSprite(self.hangUpIcon, function(sprite)
                    if not util.IsObjNull(self.bgImage) and not util.IsObjNull(sprite) then
                        self.bgImage.sprite = sprite
                    end
                end)
            end
        end
    end
    gw_hangup_mgr.RefeshCompByHangUpStauts(refashStaut)
end

return GWHomeCompBubbleHangUp
