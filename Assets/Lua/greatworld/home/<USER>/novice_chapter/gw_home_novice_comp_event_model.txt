---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by ha<PERSON><PERSON><PERSON>.
--- DateTime: 2024/9/2 16:57
--- Desc: 事件模型
local LeanTween = CS.LeanTween
local require = require
local newclass = newclass
local GWG = GWG
local table = table
local pairs = pairs
local typeof = typeof
local gw_sand_animator_helper = require "gw_sand_animator_helper"
local log = require "log"
local util = require "util"
local GWConst = require "gw_const"
local gw_home_grid_data = require "gw_home_grid_data"
local game_scheme = require "game_scheme"
local unit_base_object = require "unit_base_object"
local UIUtil = CS.Common_Util.UIUtil
local Vector3 = CS.UnityEngine.Vector3
local Time = CS.UnityEngine.Time
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
---@class GWNoviceEventModel : unit_base 事件基类
module("gw_home_novice_comp_event_model")
local M = newclass("gw_home_novice_comp_event_model", unit_base_object)

local SPEED = 2;
local DEFAULT_SCALE = 1
local UPDATE_INTERVAL = 0.01
local PLAYER_OFFSET = { x = 0.5, z = 0.5 }
local CAMERA_MOVE_TIME = { initial = 500, fastMove = 100 }
--- 构造器
function M:ctor()
    unit_base_object.ctor(self)
end
function M:InitData(eventId, cityMapId, parent, ResPath)
    local mapCfg = game_scheme:BuildMaincityMap_0(cityMapId)
    local eventCfg = game_scheme:BuildPreProcess_0(eventId)

    if not mapCfg then
        GWG.GWAdmin.SwitchUtility.Error("GWEventObject mapCfg = nil", cityMapId)
        return false
    end
    if not eventCfg then
        GWG.GWAdmin.SwitchUtility.Error("GWEventObject eventCfg = nil", eventId)
        return false
    end
    if not ResPath then
        GWG.GWAdmin.SwitchUtility.Error("GWEventObject eventCfg.ResPath = nil", eventId)
        return
    end
    self.mapCfg = mapCfg
    self.eventCfg = eventCfg
    self.eventType = eventCfg.type
    self:InstantiateModelAsync(ResPath, parent)

end

---@public 基类方法实例化模型
---@see override
function M:InstantiateModelAsync(path, parent)
    unit_base_object.InstantiateModelAsync(self, path, parent)
end

--- 实例化成功 （现必须基成设置名字和组件Id）
---@see override
function M:InstantiateSuccess(_obj)
    if self.eventType ~= GWConst.BuildEventType.StartPoint then
        local modelTr = _obj.transform:GetChild(0)
        if util.IsObjNull(modelTr) then
            log.Error("InstantiateSuccess modelTr = nil")
            return
        end
        local model = modelTr:GetChild(1)
        if util.IsObjNull(model) then
            log.Error("InstantiateSuccess model = nil")
            return
        end
        self._animator = gw_sand_animator_helper.new()
        self._animator:AddAnimator(model)
--[[        if self._animator then
            self._animator:SetDissolve(0)
        end]]
        if self.otherAnimators == nil then
            self.otherAnimators = {}
        end
        local list = UIUtil.GetChildren(modelTr)
        for i = 2, list.Count - 1 do
            local childModel = modelTr:GetChild(i)
            if util.IsObjNull(childModel) then
                break
            end
            local _animator = gw_sand_animator_helper.new()
            _animator:AddAnimator(childModel.gameObject)
            table.insert(self.otherAnimators, _animator)
        end
    end
    unit_base_object.InstantiateSuccess(self, _obj)
    local x, y, z = gw_home_grid_data.GetPosByGridXY(self.mapCfg.x, self.mapCfg.y)
    self:SetScale(self.eventCfg.ResScale, true)
    if self.eventCfg.EventID < 7 then
        y = y + 0.5
    end
    self:SetPosition(x + 0.5, y, z - 0.6)
    if self.eventType ~= GWConst.BuildEventType.StartPoint then
        self:SetRotation(0, self.mapCfg.angel, 0)
    end
    if self.callBack then
        self.callBack()
    end
end

function M:SetInstantiateSuccessCallback(_callback)
    self.callBack = _callback
end

function M:SetLayer(gridX, gridY)
    if gridX == nil or gridY == nil then
        gridX = self.serData.x
        gridY = self.serData.y
    end
    --设置对应的层级
    if self.sortingGroup then
        self.sortingGroup.sortingOrder = GWG.GWAdmin.HomeCommonUtil.GetCurSortingOrder(gridX, gridY)
    end
end
function M:ShowModel(value)
    if self.eventType == GWConst.BuildEventType.Reward then
        return
    end
    --log.Error("Create")
    self:AddLoadEvent(function()
        self:PlayAnimator("Stand")
        if not util.IsObjNull(self.card) then
            if value then
                LeanTween.value(self.gameObject, function(value)
                    self:UpdateValue(value)
                end, 1, 0, 1);
            end
            --[[util.DelayCallOnce(3, function()
                self:ShowStandTrigger()
            end)]]
        end
    end)
end

function M:PlayAnimator(state)
    if self._animator then
        self._animator:SetTrigger(state)
    end
    if self.otherAnimators then
        for i, animator in pairs(self.otherAnimators) do
            animator:SetTrigger(state)
        end
    end
end

---@public 设置移动事件
function M:MoveNextEvent(time, moveCallback)
    self:AddLoadEvent(function()
        local cameraMoveCallBack = function()
            GWG.GWAdmin.HomeSceneUtil.SetCameraOperate(false)
            self:PlayAnimator("Run")
            if not self.eventCfg then
                log.Error("self.eventCfg == nil")
                return
            end
            local x, y, z = gw_home_grid_data.GetPosByGridXY(self.eventCfg.SpecialParam1.data[0], self.eventCfg.SpecialParam1.data[1])
            x = x + PLAYER_OFFSET.x
            --- z-1原因  有重叠模型风险 减去1

            z = z - PLAYER_OFFSET.z - 1

            time = time * 0.2

            -- 计算目标位置
            local startPos = self.transform.position
            --log.Error("startPos",startPos.x,startPos.z)
            local targetPos = Vector3(x, y, z)
            local distance = Vector3.Distance(startPos, targetPos)
            local cameraTime = distance / SPEED * 1000
            local startTime = (Time.realtimeSinceStartup * 1000);

            local cameraPos01 = Vector3.Lerp(startPos, targetPos, 0.5)

            --计算结束时间基于距离和速度
            local endTime = startTime + cameraTime
            self:endTimer()
            local moveEnd = false
            local function cameraCallBack()
                moveEnd = true
            end

            -- 定时器进行位置更新
            self.timer = util.IntervalCall(UPDATE_INTERVAL, function()
                local currentTime = (Time.realtimeSinceStartup * 1000);
                if currentTime >= endTime and moveEnd then
                    self:SetPosition(x, y, z, false) -- 到达目标位置
                    self:PlayAnimator("Stand", false)
                    if moveCallback then
                        moveCallback() -- 调用回调函数
                    end
                    GWG.GWAdmin.HomeSceneUtil.SetCameraOperate(true)
                    GWG.GWHomeMgr.noviceChapterData.DisposeMoveNextAction()
                    self:endTimer()
                else
                    local num = (currentTime - startTime) / (endTime - startTime);
                    UIUtil.SetLerpPosition(self.transform, startPos, targetPos, num)
                end
            end)
            local cameraMove2 = function()
                self:CreateBubbleHud()
                GWG.GWAdmin.HomeCameraUtil.GWCameraDoMoveToGridPos(x, z, cameraTime / 2, false, function()
                    moveEnd = true
                end)
            end
            GWG.GWAdmin.HomeCameraUtil.GWCameraDoMoveToGridPos(cameraPos01.x, cameraPos01.z, cameraTime / 2, false, cameraMove2)
        end
        local cameraMoveTime = CAMERA_MOVE_TIME.initial
        cameraMoveTime = CAMERA_MOVE_TIME.fastMove
        cameraMoveCallBack()
    end)
end

---@public 创建气泡HUD 显示
function M:CreateBubbleHud()
    --新手剧情
    local langId = self.eventCfg.SpecialParam2.data[0]
    local iconId = self.eventCfg.SpecialParam2.data[1]
    local compName = GWG.GWCompName.gw_home_comp_hud_head_bubble
    if not self:GetComponent(compName) then
        local id, comp = GWG.GWAdmin.GWHomeHudUtil.InitMoveHudComponent(compName, self.transform)
        comp:SetOffset(60, 130)
        comp:SetOffsetScale(1)
        comp:SetText(langId, iconId, GWConst.EHomeCityBubbleIconType.Common)
        self:AddComponent(compName, comp)
    end
    self:AddCustomTimer(2, function()
        self:RemoveComponent(compName)
    end)
end

function M:endTimer()
    if self.timer then
        util.RemoveDelayCall(self.timer)
        self.timer = nil
    end
end

function M:ShowStandTrigger()
    self:PlayAnimator("Stand")
end

function M:ShowAbilityTrigger()
    self:PlayAnimator("Attack_Loop")
--[[    if self._animator then
        self._animator:SetAttackLoopInterval(false)
    end]]
end

function M:DeadModel(callBack, destroyCallBack)
    self:AddLoadEvent(function()
        local function timeOverFunc()
            self:StopTimer()
            if callBack then
                callBack()
            end
            if destroyCallBack then
                destroyCallBack()
            end
        end
        if not util.IsObjNull(self.card) or self._animator ~= nil then
            self:PlayAnimator("Dead")
            self:StopTimer()
            --创建定时器 倒计时进行气泡生产
            self.LeanTimeTicker = util.DelayCallOnce(1, function()
                if self.isDisposed then
                    return
                end
                LeanTween.value(self.gameObject, function(value)
                    self:UpdateValue(value)
                end, 0, 1, 1);
            end)
            self.TimeTicker = util.DelayCallOnce(2, function()
                if self.isDisposed then
                    return
                end
                timeOverFunc()
            end)
        else
            timeOverFunc()
        end
    end)
end

function M:SetVisible(show)
    self:AddLoadEvent(function()
        if not show then
            return
        end
        UIUtil.SetActive(self.gameObject, show)
    end)
end

function M:UpdateValue(value)
    if self._animator then
        self._animator:SetDissolve(value)
    end
    if self.otherAnimators then
        for i, _animator in pairs(self.otherAnimators) do
            _animator:SetDissolve(value)
        end
    end
end

function M:StopTimer()
    if self.TimeTicker then
        util.RemoveDelayCall(self.TimeTicker)
        self.TimeTicker = nil
    end
end

function M:ClearData()
    self.eventCfg = nil
    self.defaultZ = nil
    self.mapCfg = nil
    self.eventType = nil

    if self._animator then
        self._animator:SetAttackLoopInterval(false)
        self._animator:Dispose()
        self._animator = nil
    end
    if self.otherAnimators then
        for i, _animator in pairs(self.otherAnimators) do
            _animator:Dispose()
        end
        self.otherAnimators = nil
    end
    self:StopTimer()
end
--- 重置为了循环利用
---@see override
function M:Recycle()
    self:ClearData()
    unit_base_object.Recycle(self)
end

function M:Dispose()
    self:ClearData()
    unit_base_object.Dispose(self)
end

return M