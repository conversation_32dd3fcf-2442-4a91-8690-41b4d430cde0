---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON>Lua)
--- Created by ha<PERSON><PERSON><PERSON>.
--- DateTime: 2024/9/13 11:00
--- Desc: HUD基类
local require = require
local newclass = newclass
local UIUtil = CS.Common_Util.UIUtil
local util = require "util"
local unit_base_object = require "unit_base_object"
local GWG = GWG
local cc = cc
local Vector3 = CS.UnityEngine.Vector3
local Space = CS.UnityEngine.Space
local Transform = CS.UnityEngine.Transform
local typeof = typeof
local LayerMask = CS.UnityEngine.LayerMask
local GWSandHudNode = require("gw_sand_hud_node")
local RectTransformUtility = CS.UnityEngine.RectTransformUtility
local val = require("val")
local isPerf = val.IsTrue("sw_home_bubble_new", 0)
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
---@class GWHUDObject : unit_base HUD基础对象
---@field __base GWHUDObject
module("gw_home_comp_hud_base")
local GWHUDObject = newclass("gw_home_comp_hud_base", unit_base_object)

--- 构造器
function GWHUDObject:ctor()
    self:ClearData()
    cc(self):addComponent("BaseSubset"):exportMethods()
    self:_InitSubSet()
end

---@public 设置数据 （现必须基成设置数据）
---@param bindParent table Transform 绑定的父节点
---@see override
function GWHUDObject:InitData(bindParent, data)
    if not util.IsObjNull(bindParent) then
        self.bindParent = bindParent    --绑定的父节点
        self.parentTrans = bindParent.transform
    end
    self.data = data
    self.entityState = GWG.GWConst.EHomeEntityStateType.Normal --状态
end

--- @public 基类方法实例化模型
---@param path string 模型路径
---@param parent table Transform 父节点 (不设置 默认为管理的父物体节点)
---@see override
function GWHUDObject:InstantiateModelAsync(path, parent)
    unit_base_object.InstantiateModelAsync(self, path, parent)
end

---实例化成功 （现必须基成设置名字和组件Id）
---@param _obj table GameObject 模型对象
---@see override
function GWHUDObject:InstantiateSuccess(_obj)
    --初始化组件
    self.rectTransform = UIUtil.GetComponent(_obj.transform, "RectTransform")
    unit_base_object.InstantiateSuccess(self, _obj)
    
	if isPerf then
        --估算初始状态，气泡大小需要缩放ScreenToWorldScale
        self.scale = self.scale* GWG.GWConst.ScreenToWorldScale
        self:SetScale(self.scale, true)
        self:ResetHudPosition()

        local allTrans = self.gameObject:GetComponentsInChildren(typeof(Transform))
        local layerNum = LayerMask.NameToLayer("BraveMapScene")
        
        if allTrans and allTrans.Length > 0 then
            for i = 0, allTrans.Length - 1 do
                allTrans[i].gameObject.layer = layerNum
            end
        end
	else
		self:MoveTransform()
    end
end

---@public 更新时间
---@param timer number 时间间隔
---@see override
function GWHUDObject:OnUpdate(timer)
    if self.entityState == GWG.GWConst.EHomeEntityStateType.WaitingDispose then
        return true
    end
    return false
end

---@public lateUpdate更新位置
---@see override
function GWHUDObject:OnLateUpdate()
    if isPerf then
        return
    end
    self:MoveTransform()
end

---@public 移动位置
---@see override
function GWHUDObject:MoveTransform()
	if isPerf then
		return
	end
    if not self.isLoaded then
        return
    end
    if util.IsObjNull(self.transform) then
        return
    end
    if util.IsObjNull(self.bindParent) then
        return
    end
    if not self.gwCameraComp then
        self.gwCameraComp = GWG.GWMgr.comp:GetCameraComponent()
    end
    if util.IsObjNull(self.gwCameraComp) then
        return
    end

    local screenPos = RectTransformUtility.WorldToScreenPoint(self.gwCameraComp, self.parentTrans.position)
    if not util.IsObjNull(self.rectTransform) then
        if not self.bubbleCanvas or not self.hudCamera then
            self.bubbleCanvas = GWSandHudNode.HudCanvas()
            self.hudCamera = GWSandHudNode.HudCamera()
        end
        UIUtil.SetAncoPosition(self.rectTransform, self.bubbleCanvas, self.hudCamera, screenPos.x, screenPos.y, 0, self.offset_x * self.scale, self.offset_y * self.scale)
        return
    end
    UIUtil.SetUIScreenPos(self.transform, screenPos.x + self.offset_x, screenPos.y + self.offset_y)
end

function GWHUDObject:ResetHudPosition()
    --先设置到需要跟随的父物体的位置，然后旋转到与屏幕平行，最后根据offset在Self空间平移，z轴朝向屏幕移动4.5避免气泡插入到模型中
    self.rectTransform.position = self.parentTrans.position
    UIUtil.SetRotation(self.rectTransform, 40, -45, 0)
    self.rectTransform:Translate(Vector3(self.offset_x*self.scale, self.offset_y*self.scale, -4.5), Space.Self)
end

--- @public基类方法设置缩放
---@see override
function GWHUDObject:SetHudScale(scale)
	if isPerf then
		return
	end
    self:SetScale(scale)
    self.scale = scale
end

---@public 更新数据
---@param data table
---@see override
function GWHUDObject:OnUpdateData(data)

end

---@public 设置移除状态
---@see override
function GWHUDObject:Remove()
    self.entityState = GWG.GWConst.EHomeEntityStateType.WaitingDispose
end
---@public 隐藏
---@see override
function GWHUDObject:OnHide()
    self:SetActive(false)
end
---@public 显示
---@see override
function GWHUDObject:OnShow()
    self:SetActive(true)
end

---清楚基类数据
---@see override
function GWHUDObject:ClearData()
    self.bindParent = nil       --绑定的父节点
    self.rectTransform = nil    --当前rectTransform 
    self.offset_x = 0           --偏移x
    self.offset_y = 0           --偏移y
    self.data = nil             --数据
    self.scale = 1              --缩放

    --UI跟随使用的临时数据
    self.parentTrans = nil
    self.bubbleCanvas = nil
    self.hudCamera = nil
    self.gwCameraComp = nil
end

--- 弃置
---@see override
function GWHUDObject:Dispose()
    self.entityState = GWG.GWConst.EHomeEntityStateType.Dispose
    self:DisposeAllSubset()
    self:ClearData()
    unit_base_object.Dispose(self)
end

--- 重置为了循环利用
---@see override
function GWHUDObject:Recycle()
    self.entityState = GWG.GWConst.EHomeEntityStateType.Dispose
    self:ClearData()
    unit_base_object.Recycle(self)
end
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

return GWHUDObject
