---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by ha<PERSON><PERSON><PERSON>.
--- DateTime: 2024/9/13 11:00
--- Desc: HUD升级 Icon对话气泡
local require = require
local newclass = newclass
local UIUtil = CS.Common_Util.UIUtil
local GWConst = require "gw_const"
local util = require "util"
local card_assets = require "card_sprite_asset"
local lang = require "lang"
local gw_home_comp_hud_base = require "gw_home_comp_hud_base"
local typeof = typeof
local Animator = CS.UnityEngine.Animator
local val = require("val")
local isPerf = val.IsTrue("sw_home_bubble_new", 0)
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
---@class GWHUDBubble : unit_base HUD基础对象
module("gw_home_comp_hud_head_bubble")
local GWHUDBubbleObject = newclass("gw_home_comp_hud_head_bubble", gw_home_comp_hud_base)
local XOffset = 89
local yOffset = 60
--- 构造器
function GWHUDBubbleObject:ctor()
    gw_home_comp_hud_base.ctor(self)
end

---@public 设置数据 （现必须基成设置数据）
---@param bindParent table Transform 绑定的父节点
---@see override
function GWHUDBubbleObject:InitData(bindParent, data)
    gw_home_comp_hud_base.InitData(self, bindParent, data)
    self:InstantiateModelAsync("art/greatworld/home/<USER>/hud/gwhomeheadbubblehud.prefab", data.parent)
end

--- @public 基类方法实例化模型
---@param path string 模型路径
---@param parent table Transform 父节点 (不设置 默认为管理的父物体节点)
---@see override
function GWHUDBubbleObject:InstantiateModelAsync(path, parent)
    gw_home_comp_hud_base.InstantiateModelAsync(self, path, parent)
end

---实例化成功 （现必须基成设置名字和组件Id）
---@param _obj table GameObject 模型对象
---@see override
function GWHUDBubbleObject:InstantiateSuccess(_obj)
    self.parentTr = UIUtil.GetComponent(_obj.transform, "RectTransform", "parent")
    self.headCanvas = UIUtil.GetComponent(_obj.transform, "Canvas")
    self.iconImg = UIUtil.GetComponent(_obj.transform, "Image", "parent/IconParent/Icon")
    self.iconParent = UIUtil.GetComponent(_obj.transform, "RectTransform", "parent/IconParent")
    self.animators = UIUtil.GetComponent(_obj.transform, typeof(Animator), "")
    --self.contentLayout = UIUtil.GetComponent(_obj.transform, "VerticalLayoutGroup", "parent/duihuakuan")
    self.infoText = UIUtil.GetComponent(_obj.transform, "Text", "parent/duihuakuan/content")
    self.infoText2 = UIUtil.GetComponent(_obj.transform, "Text", "parent/duihuakuan/content2")
    gw_home_comp_hud_base.InstantiateSuccess(self, _obj)
    if isPerf then
        --设置聊天气泡在世界空间跟随
        self.transform.parent = self.parentTrans
    end
    self:PlayAnim("ShowGuideBubble")
end

function GWHUDBubbleObject:PlayAnim(animName)
    if util.IsObjNull(self.animators) then
        return
    end
    self.animators:Play(animName)
end

function GWHUDBubbleObject:SetText(langId, iconId, iconType)
    self:AddLoadEvent(function()
        if self.spriteAsset then
            self.spriteAsset:Dispose()
            self.spriteAsset = nil
        end
        if iconType and iconType == GWConst.EHomeCityBubbleIconType.Common then
            self.spriteAsset = self.spriteAsset or card_assets.CreateSpriteAsset()
        else
            self.spriteAsset = self.spriteAsset or card_assets.CreateSurvivorHeadAsset()
        end
        
        if iconId == nil then
            self:RefreshIconLayout(false)
            self.infoText2.text = lang.Get(langId)
        else
            self:RefreshIconLayout(true)
            self.infoText.text = lang.Get(langId)
            if not util.IsObjNull(self.iconImg) then
                self.spriteAsset:GetSprite(iconId, function(sp)
                    self.iconImg.sprite = sp
                end)
            end
        end
    end)
end

function GWHUDBubbleObject:SetOffset(x, y,order)
    XOffset = 89 + x
    yOffset = 60 + y
    self.order = order
    self:SetCanvasOrder()
end

function GWHUDBubbleObject:SetCanvasOrder()
    self:AddLoadEvent(function()
        if util.IsObjNull(self.headCanvas) then
            return
        end
        self.headCanvas.sortingOrder = self.order
    end)
end

---@public 判断是不是需要显示图标 设置left的值
function GWHUDBubbleObject:RefreshIconLayout(isIcon)
    --[[if util.IsObjNull(self.contentLayout) then
        return
    end
    UIUtil.SetActive(self.iconImg.gameObject, isIcon)
    self.contentLayout.padding.left = isIcon and 75 or 15]]
    if not util.IsObjNull(self.iconParent) then
        UIUtil.SetActive(self.iconParent.gameObject, isIcon)    
    end
    if util.IsObjNull(self.infoText) or util.IsObjNull(self.infoText2) then
        return
    end
    UIUtil.SetActive(self.infoText, isIcon)
    UIUtil.SetActive(self.infoText2, not isIcon)
end

function GWHUDBubbleObject:SetOffsetScale(scale)
    self.offset_y = yOffset * scale
    self.offset_x = XOffset * scale
end

---清楚基类数据
---@see override
function GWHUDBubbleObject:ClearData()
    if self.spriteAsset then
        self.spriteAsset:Dispose()
        self.spriteAsset = nil
    end
    if not util.IsObjNull(self.parentTr) then
        UIUtil.SetAncoPosition(self.parentTr, -110, -32)
    end
    gw_home_comp_hud_base.ClearData(self)
    XOffset = 89
    yOffset = 60
    self.offset_x = XOffset                 --偏移x
    self.offset_y = yOffset           --偏移y
    self.order = 0
end
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

return GWHUDBubbleObject
