--- Des:建筑-大本营
local require = require
local math = math
local tostring = tostring
local buildingBaseClass = require "gw_home_comp_building_base"
local newclass = newclass
local GWG = GWG
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

module("gw_home_comp_building_default")
---@class GWHomeCompBuildingDefault: gw_home_comp_building_base
--当前统一都用GWHomeCompBuilding
local GWHomeCompBuilding = newclass("gw_home_comp_building_default", buildingBaseClass)

--常见的重载函数，按自己需要重载
--- 构造器
---@see  override
function GWHomeCompBuilding:ctor()
    buildingBaseClass.ctor(self)
end
--- 初始化数据 
---@see  override
function GWHomeCompBuilding:Init()
    buildingBaseClass.Init(self)
end

---@public 设置建筑贴图表现
---@see  override
function GWHomeCompBuilding:SetBuildingTextureInfo()
    buildingBaseClass.SetBuildingTextureInfo(self)
    if self.buildingCfg.TypeID == GWG.GWConst.enBuildingType.enBuildingType_Arena then
        local net_arena_module = require "net_arena_module"
        local arena_data = require "arena_data"
        local isOpen = arena_data.IsAreaNewOpen()
        if isOpen then
            local common_new_pb = require "common_new_pb"
            net_arena_module.Send_ARENA_ENTER(common_new_pb.CrystalCrown) 
        end
    end
end

---@see 设置能否升级
---@see override
function GWHomeCompBuilding:SetCanUpgrade(canUpgrade, needCheck)
    buildingBaseClass.SetCanUpgrade(self,canUpgrade,needCheck)
end
function GWHomeCompBuilding:RegisterListener()
    buildingBaseClass.RegisterListener(self)
end
function GWHomeCompBuilding:UnregisterListener()
    buildingBaseClass.UnregisterListener(self)
end

--- 回收
---@see  override
function GWHomeCompBuilding:Recycle()
    buildingBaseClass.Recycle(self)
end

--- 弃用
---@see  override
function GWHomeCompBuilding:Dispose()
    buildingBaseClass.Dispose(self)
end
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

return GWHomeCompBuilding