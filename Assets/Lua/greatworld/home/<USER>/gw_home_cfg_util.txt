---
--- Created by ha<PERSON><PERSON><PERSON>.
--- DateTime: 2024/8/14 16:56
--- Desc : 配置读表工具、
local require = require
local table = table
local string = string
local tonumber = tonumber
local pairs = pairs
local log = log
local os = os
local math = math
local gw_home_common_data = require "gw_home_common_data"
local util = require "util"
local laymain_data = require "laymain_data"
local prop_pb = require "prop_pb"
local gw_attribute_bonus = require "gw_attribute_bonus"
local player_mgr = require "player_mgr"
local gw_game_force_util = require "gw_game_force_util"
local game_bonus_const = require "game_bonus_const"
local GWConst = require "gw_const"
local city_pb = require "city_pb"
local game_scheme = require "game_scheme"
local GWG = GWG

module("gw_home_cfg_util")
local M = {}

--所有栅栏物体的列表，它们会在第一次新手引导结束时依次出现。
local FenceList =
{
    [231] = true,
    [10032] = true,
    [10033] = true,
    [10034] = true,
    [10035] = true,
    [10036] = true,
    [10037] = true,
    [10038] = true,
    [10039] = true,
    [10040] = true,
}

local function GetShowTypeData(buildShowType, data)
    if buildShowType == GWConst.EBuildingParaType.Percentage or
            buildShowType == GWConst.EBuildingParaType.Technology then
        data.ori = string.format("%g%%", data.ori / 100)
        if data.to then
            data.to = string.format("%g%%", data.to / 100)
        end
    elseif buildShowType == GWConst.EBuildingParaType.Time then
        data.isCountTime = true
    elseif buildShowType == GWConst.EBuildingParaType.GWMapEffect then
        if data.ori then
            local oriCfg = game_scheme:GWMapEffect_0(data.ori)
            if oriCfg then
                data.ori = oriCfg.strParam[0]
            end
        end
        if data.to then
            local toCfg = game_scheme:GWMapEffect_0(data.to)
            if toCfg then
                data.to = toCfg.strParam[0]
            end
        end
    end
end

---获取建筑升级数据
---@param curBuildCfg table 当前等级数据
---@param nexBuildCfg table 下一升级数据
---@return table buildData 建筑升级数据
function M.GetBuildingIndexData(curBuildCfg, nexBuildCfg)
    local buildData = {}
    local buildShowTypeGroup = game_bonus_const.GetBuildingParamType(curBuildCfg.TypeID)
    for i = 1, 6 do
        local data = {}
        local buildShowType = buildShowTypeGroup[i]
        if nexBuildCfg then
            if nexBuildCfg["DesLang" .. i] and curBuildCfg["BuildingPara" .. i] and nexBuildCfg["BuildingPara" .. i] and nexBuildCfg["DesLang" .. i] ~= 0 then
                data.info = nexBuildCfg["DesLang" .. i]
                data.ori = curBuildCfg["BuildingPara" .. i]
                data.to = nexBuildCfg["BuildingPara" .. i]
                GetShowTypeData(buildShowTypeGroup[i], data)
                table.insert(buildData, data)
            end
        else
            if curBuildCfg["DesLang" .. i] ~= 0 then
                data.info = curBuildCfg["DesLang" .. i]
                data.ori = curBuildCfg["BuildingPara" .. i]
                GetShowTypeData(buildShowTypeGroup[i], data)
                table.insert(buildData, data)
            end
        end
    end
    return buildData
end

---获取建筑生产数据
---@param curBuildCfg table 当前等级数据
---@param nexBuildCfg table 下一升级数据
---@return table ProductData
function M.GetProduceData(curBuildCfg, produceId, nextProduceId)
    local cur_Cfg = game_scheme:BuildProduce_0(produceId)
    local next_Cfg = nil
    if nextProduceId then
        next_Cfg = game_scheme:BuildProduce_0(nextProduceId)
    end
    local ProductData = {}
    local data = {}
    data.info = curBuildCfg.DesLang1
    --无人机组件工厂比较特殊 需要特殊处理
    if next_Cfg and next_Cfg.BuildType == GWConst.enBuildingType.enBuildingType_DroneFactory then
        data.isCountTime = true
    end
    data.ori = cur_Cfg and cur_Cfg.ProduceSpeed or 0
    data.to = next_Cfg and next_Cfg.ProduceSpeed or nil
    table.insert(ProductData, data)
    local timeData = {}
    timeData.info = curBuildCfg.DesLang2
    timeData.ori = cur_Cfg and cur_Cfg.ProduceTime or 0
    timeData.to = next_Cfg and next_Cfg.ProduceTime or nil
    timeData.isCountTime = true
    table.insert(ProductData, timeData)
    return ProductData
end

---获取升级、生产提升数据
---@param nBuildingID number 建筑Id
---@param nLevel number 当前等级
---@param nNextLevel number 下一级等级
---@return table 升级展示数据
function M.GetPromoteData(nBuildingID, nLevel, nNextLevel)
    --[[ table 数据
        info = lang表id
        ori = 原始值
        to = 升级后
    ]]
    local cur_Build_Cfg = game_scheme:Building_0(nBuildingID, nLevel)
    local next_Build_Cfg = game_scheme:Building_0(nBuildingID, nNextLevel)
    if cur_Build_Cfg then
        local buildData = nil
        if cur_Build_Cfg.TypeID == city_pb.enBuildingType_Farm or
                cur_Build_Cfg.TypeID == city_pb.enBuildingType_Iron or
                cur_Build_Cfg.TypeID == city_pb.enBuildingType_Gold or
                cur_Build_Cfg.TypeID == city_pb.enBuildingType_SmelterFactory or
                cur_Build_Cfg.TypeID == city_pb.enBuildingType_Material or
                cur_Build_Cfg.TypeID == city_pb.enBuildingType_DroneFactory or
                cur_Build_Cfg.TypeID == city_pb.enBuildingType_Exp then
            local cfg = cur_Build_Cfg.DesLang1 and cur_Build_Cfg or next_Build_Cfg
            if next_Build_Cfg and next_Build_Cfg.BuildingPara1 then
                buildData = M.GetProduceData(next_Build_Cfg, cur_Build_Cfg.BuildingPara1, next_Build_Cfg.BuildingPara1)
            else
                buildData = M.GetProduceData(cfg, cur_Build_Cfg.BuildingPara1, nil)
            end

        elseif cur_Build_Cfg.TypeID == city_pb.enBuildingType_FarmWareHouse or
                cur_Build_Cfg.TypeID == city_pb.enBuildingType_IronWareHouse or
                cur_Build_Cfg.TypeID == city_pb.enBuildingType_GoldWareHouse then
            buildData = {}
            local data = {}
            data.info = next_Build_Cfg and next_Build_Cfg.DesLang1 or cur_Build_Cfg.DesLang1
            data.ori = cur_Build_Cfg.BuildingPara2
            data.to = next_Build_Cfg and next_Build_Cfg.BuildingPara2 or nil
            table.insert(buildData, data)
        elseif cur_Build_Cfg.TypeID == city_pb.enBuildingType_ResourceTruck then
            buildData = {}
            local data = {}
            data.info = cur_Build_Cfg.DesLang1
            local gw_hangup_mgr = require "gw_hangup_mgr"
            local cfg = gw_hangup_mgr.GetCurHangUpCfg()
            if cfg then
                data.ori = cfg.LimitTime
                data.isCountTime = true
                table.insert(buildData, data)
            end
        elseif cur_Build_Cfg.TypeID > 1000 then --装饰建筑
            local buildingData = {
                nBuildingID = nBuildingID,
                nLevel = nLevel
            }
            GWG.GWHomeMgr.buildingData.SetDecorateBuildingData({data = buildingData})
            local decorateData = GWG.GWHomeMgr.buildingData.GetDecorateBuildingData()
            if not decorateData then
                return
            end
            buildData = {}
            local propDataTable = decorateData.propDataTable
            local powerData = {}
            for i =1, #propDataTable do
                local prop =  propDataTable[i]
                if nLevel == 0 then
                    prop.nextPropID = prop.curPropID
                    prop.curPropID = nil
                end
                local curID = prop.curPropID
                local curPropValue = 0
                local curProp = nil
                if curID and curID > 0 then
                    curProp =  game_scheme:GWMapEffect_0(curID)
                    curPropValue = curProp.strParam[0]
                end

                local nextProp = nil
                local nextPropValue = 0
                local data = {}
                
                data.ori = curPropValue
                local nextPropID = prop.nextPropID
                if nextPropID then
                    nextProp =  game_scheme:GWMapEffect_0(nextPropID)
                    nextPropValue = nextProp.strParam[0]
                    data.to = nextPropValue
                end

                local typeId = curPropValue > 0 and curProp.nGroupID or nextProp.nGroupID
                local proData = GWG.GWHomeMgr.buildingData.GetDecorateProTable(typeId)
                local curPropDes =  proData.proLang
                data.info = curPropDes
                
                local proType = game_scheme:ProToLang_0(typeId)
                
                local isPercentage = proType.isPercentage and proType.isPercentage==1
                local percentage = isPercentage and "%" or ""
                data.ori = (isPercentage and (data.ori / 100) or data.ori)..percentage
                if data.to then
                    data.to = (isPercentage and (data.to / 100) or data.to)..percentage    
                end                
                
                table.insert(buildData,data)
                --bugId =7182 要求升级 向上取整
                local power_coe = proType.Power
                powerData.ori = powerData.ori or 0
                powerData.ori = powerData.ori + (curPropValue * power_coe *  player_mgr.GetPalPartCount())
                powerData.ori = math.ceil(powerData.ori / 100)
                powerData.to = powerData.to or 0
                powerData.to = powerData.to + (nextPropValue * power_coe *  player_mgr.GetPalPartCount())
                powerData.to = math.ceil( powerData.to / 100)
            end
            powerData.info = 602021
            if powerData.to and powerData.to > 0 then
                table.insert(buildData,powerData)
            end

            return buildData
        else
            buildData = M.GetBuildingIndexData(cur_Build_Cfg, next_Build_Cfg);
        end
        if buildData  then
            local powerData = {}
            powerData.info = 602021
            powerData.ori = cur_Build_Cfg.power
            if next_Build_Cfg then
                powerData.to = next_Build_Cfg.power
            end
            powerData.isPower = true
            table.insert(buildData, powerData)
        end
        return buildData
    else
        log.Error("GetPromoteData not cur_Build_Cfg", nBuildingID, nLevel)
    end
    return nil
end

local buildMainCityMapCache = nil

---通过buildId 获取 修复表数据
---@param nBuildingID number 建筑Id
---@return table
function M.GetBuildMainCityMapData(nBuildingID)
    if not buildMainCityMapCache then
        buildMainCityMapCache = {}
        local buildingLen = game_scheme:BuildMaincityMap_nums()
        for i = 0, buildingLen - 1 do
            local cfg = game_scheme:BuildMaincityMap(i)
            if cfg and cfg.buildingID and cfg.buildingID.data then
                local buildId = cfg.buildingID.data[0]
                if buildId~=nil then
                    buildMainCityMapCache[buildId] = cfg
                end
            end
        end
    end
    return buildMainCityMapCache[nBuildingID] or nil
end

---@private 获取建筑是否可以修复
local function CheckConditionTypeBuilding(data)
    -- 确保data表有足够的元素来避免索引越界
    if #data < 3 then
        log.Error("数据不足")
        return
    end
    local needBuildId = tonumber(data[2])
    local needBuildLevel = tonumber(data[3])
    --是否可以修复
    local maxLevel = GWG.GWHomeMgr.buildingData.GetBuildingDataMaxLevel(needBuildId)
    return maxLevel >= needBuildLevel
end
---@public 获取建筑是否可以修复
---@param maincityMapId number 建筑Id
---@return boolean
function M.IsCanRepair(mainCityMapId)
    local cfg = game_scheme:BuildMaincityMap_0(mainCityMapId)
    local isRepair = false
    if not cfg then
        log.Error("未找到修复数据", mainCityMapId)
        return isRepair
    end
    isRepair = true
    if cfg.FixCondition and cfg.FixCondition ~= "" then
        local data = string.split(cfg.FixCondition, "#")
        local conditionType = tonumber(data[1])
        if conditionType == GWConst.FixConditionType.BuildingIDAndLevel then
            isRepair = CheckConditionTypeBuilding(data)
        elseif conditionType == GWConst.FixConditionType.StageID then
            -- 处理关卡id的条件
            isRepair = laymain_data.GetPassLevel() >= tonumber(data[2])
        elseif conditionType == GWConst.FixConditionType.MiniGameStageID then
            -- 处理小游戏关卡id的条件
            local puzzlegame_mgr = require "puzzlegame_mgr"
            isRepair = puzzlegame_mgr.getIsFinishByLevelId(tonumber(data[2]))
        elseif conditionType == GWConst.FixConditionType.GiftPackID then
            -- 处理礼包id的条件
            local month_card_data = require "month_card_data"
            local privilege_data = require "privilege_data"
            local privilege_define = require "privilege_define"
            isRepair = (month_card_data.GetMonthCardIsOpen() and privilege_data.GetPrivilegeIsOpenByID(privilege_define.MONTH_FOUR_TEAM))
        else
            isRepair = false
            log.Error("IsCanRepair 未知的修复条件类型",conditionType)
        end
    end
    return isRepair
end

function M.GetProduceNumberBySid(sid)
    local serData = GWG.GWHomeMgr.buildingData.GetBuildingDataBySid(sid)
    local buildCfg = game_scheme:Building_0(serData.nBuildingID, serData.nLevel)
    if not buildCfg and not buildCfg.BuildingPara1 then
        GWG.GWAdmin.SwitchUtility.Error("GetProduceNumberBySerData InitResourceData self.buildCfg = nil")
        return
    end
    return M.GetProduceNumber(sid, serData.resource, buildCfg.BuildingPara1)
end

---@public 获取建筑产出数量
---@param resourceData table 建筑资源数据
---@param produceId number 产出id
function M.GetProduceNumber(sid, resourceData, produceId)
    local effectGroup = GWG.GWHomeMgr.survivorData.GetSurvivorAdditionListByBuildingSid(sid)
    local produceCfg = game_scheme:BuildProduce_0(produceId)
    local produceNumber = 0
    local maxProduce = 0
    if not produceCfg then
        log.Error("未找到建筑产出配置", produceId)
        return produceNumber,maxProduce,produceCfg
    end
    if not resourceData then
        log.Error(" resourceData == nil")
        return produceNumber,maxProduce,produceCfg
    end
    --开始时间  最后一次领取的时间
    local lastTime = resourceData.uLastGetTime
    --最后一次变动时间
    local lastChangeTime = 0
    if resourceData.uLastChangeTime and resourceData.uLastChangeTime > 0 then
        lastChangeTime = resourceData.uLastChangeTime
    end
    --算出多少秒
    local intervalT = 0
    local maxChangeTime = 0
    if lastChangeTime > 0 then
        intervalT = os.server_time() - lastChangeTime
        if lastTime > 0 then
            maxChangeTime = lastChangeTime - lastTime    
        end        
    else
        intervalT = os.server_time() - lastTime
    end
    local maxTime = produceCfg.ProduceTime - maxChangeTime
    --如果当前时间大于最大时间
    if intervalT > maxTime then
        intervalT = maxTime
    end
    --资源数量
    local resourceCount = 0
    --判断中间是否有变动
    if resourceData.nResourceCount and resourceData.nResourceCount > 0 then
        --资源数量
        resourceCount = resourceData.nResourceCount
        local resourceLastTime = os.server_time() - lastTime
        --如果满了 直接return 防止算上加成数值
        if resourceLastTime >= produceCfg.ProduceTime then
            return resourceCount, resourceCount, produceCfg
        end
    end
    if produceCfg.ProduceType == 1 then
        --查询效果
        local speedAdditionPercent = game_bonus_const.GetBuildTypeAdditionId(produceCfg.BuildType)
        for i, v in pairs(effectGroup) do
            speedAdditionPercent = speedAdditionPercent + v
        end
        --算出加成
        local produceSpeed = gw_game_force_util.game_resource_add_force(produceCfg.ProduceSpeed, speedAdditionPercent)
        --最大产量
        maxProduce = (produceCfg.ProduceTime / 3600) * produceSpeed
        --如果产出时间小于生产一圈的时间 则=0
        if intervalT < produceCfg.Typeparm1 then
            return 0, maxProduce, produceCfg
        end
        --设置总产量
        local resourceNumber = (intervalT / 3600 * produceSpeed) + resourceCount
        --向下取整
        produceNumber = math.floor(resourceNumber)
    elseif produceCfg.ProduceType == 2 then
        --最大产量
        maxProduce = (produceCfg.ProduceTime / produceCfg.ProduceSpeed)
        --向下取整
        local produceCount = math.floor(intervalT / produceCfg.ProduceSpeed)
        produceNumber = produceCount + resourceCount
    else
        log.Error("未知的建筑产出类型", produceCfg.ProduceType)
    end
    if produceNumber < 0 then
        log.Error("建筑产出数量小于0", produceNumber)
        produceNumber = 0
    end
    return produceNumber, maxProduce, produceCfg
end

local function CreateBuildUpgradeItemData(id, needLevel, number, isPre)
    local itemData = {}
    itemData.id = id
    itemData.number = number
    if isPre then
        itemData.isPre = true
        itemData.needLevel = needLevel
        itemData.isSatisfy = itemData.needLevel <= itemData.number
    else
        local percent = gw_attribute_bonus.GetGroupEffect(prop_pb.PERSON_PROP_CONST_COST_REDUCE_RATE)
        itemData.needNumber = gw_game_force_util.game_building_resource_reduction_force(needLevel, percent)
        itemData.isSatisfy = itemData.needNumber <= itemData.number
    end
    return itemData
end
---@public 获取建筑升级消耗
---@param data table 建筑数据
---@param isCheck boolean 是否是检查
function M.GetBuildingUpgradeData(data, isCheck)
    local preConsumeData = {}
    local next_Build_Cfg = game_scheme:Building_0(data.nBuildingID, data.nLevel + 1)
    if next_Build_Cfg then
        if next_Build_Cfg.Preconditions then
            local Arr = string.split(next_Build_Cfg.Preconditions, ";")
            for _, v in pairs(Arr) do
                local arr = string.split(v, "#")
                local id = tonumber(arr[1])
                local needLevel = tonumber(arr[2])
                local number = GWG.GWHomeMgr.buildingData.GetBuildingDataMaxLevel(id)
                local itemData = CreateBuildUpgradeItemData(id, needLevel, number, true)
                if isCheck and not itemData.isSatisfy then
                    return false
                end
                table.insert(preConsumeData, itemData)
            end
        end
        if next_Build_Cfg.food and next_Build_Cfg.food > 0 then
            local itemData = CreateBuildUpgradeItemData(35, next_Build_Cfg.food, player_mgr.GetPlayerFood())
            if isCheck and not itemData.isSatisfy then
                return false
            end
            table.insert(preConsumeData, itemData)
        end
        if next_Build_Cfg.iron and next_Build_Cfg.iron > 0 then
            local itemData = CreateBuildUpgradeItemData(36, next_Build_Cfg.iron, player_mgr.GetPlayerIron())
            if isCheck and not itemData.isSatisfy then
                return false
            end
            table.insert(preConsumeData, itemData)
        end
        if next_Build_Cfg.money and next_Build_Cfg.money > 0 then
            local itemData = CreateBuildUpgradeItemData(1, next_Build_Cfg.money, player_mgr.GetPlayerCoin())
            if isCheck and not itemData.isSatisfy then
                return false
            end
            table.insert(preConsumeData, itemData)
        end
        if next_Build_Cfg.cost then
            local Arr = string.split(next_Build_Cfg.cost, ";")
            for _, v in pairs(Arr) do
                local arr = string.split(v, "#")
                local id = tonumber(arr[1])
                local needNumber = tonumber(arr[2])
                local number = player_mgr.GetPlayerOwnNum(id)
                local itemData = CreateBuildUpgradeItemData(id, needNumber, number)
                if isCheck and not itemData.isSatisfy then
                    return false
                end
                table.insert(preConsumeData, itemData)
            end
        end
    else
        if isCheck then
            return false
        end
    end
    if isCheck then
        return isCheck
    end
    return preConsumeData
end

function M.GetNoviceIsRepair(buildId)
    local gw_home_novice_chapter_data = require "gw_home_novice_chapter_data"

    if not gw_home_novice_chapter_data.GetNoviceHomeOpen() then
        return true
    end
    local mainLevel = GWG.GWHomeMgr.buildingData.GetBuildingMainLevel()
    if mainLevel > 0 then
        return true
    elseif (not gw_home_novice_chapter_data.GetNoviceAndNoCheckPassLevel() and buildId == GWG.GWHomeMgr.buildingData.GetBuildingIdByBuildingType(GWConst.enBuildingType.enBuildingType_Main)) then
        local typeId = GWG.GWHomeMgr.buildingData.GetBuildingTypeByBuildingId(buildId)
        local eventTypeList = GWG.GWHomeMgr.noviceChapterData.GetBuildEventTypeList()
        for i, data in pairs(eventTypeList) do
            if data.typeId == typeId then
                return true, data.scale
            end
        end
        return true
    end
    local typeId = GWG.GWHomeMgr.buildingData.GetBuildingTypeByBuildingId(buildId)
    local eventTypeList = GWG.GWHomeMgr.noviceChapterData.GetBuildEventTypeList()
    for i, data in pairs(eventTypeList) do
        if data.typeId == typeId then
            return true, data.scale
        end
    end
    return false
end
---@public 获取建筑升级时间
---@param time number 建筑原始升级时间` 
function M.GetBuildingUpgradeTime(time)
    local speedRate = gw_attribute_bonus.GetEffectValue(prop_pb.enProdPro_Const_Speed_Rate)
    local expendTime = gw_game_force_util.game_building_acceleration_time(time, speedRate)
    return expendTime
end
---@public 获取建筑升级总减少时间
---@param time number 建筑原始升级时间` 
function M.GetBuildingUpgradeReduceTime(time)
    local allTime = M.GetBuildingUpgradeTime(time)
    local freeTime = gw_home_common_data.GetBuildingSpeedRate()
    local reduceTime = allTime - freeTime
    return math.ceil(reduceTime)
end

function M.GetMapCityShow(cityId)
    local cfg = game_scheme:BuildMaincityMap_0(cityId)
    if cfg and cfg.RegionEvent and cfg.RegionEvent.count > 0 then
        local type = cfg.RegionEvent.data[0]
        local eventType = cfg.RegionEvent.data[1]
        local chapterDataEntity = nil
        if eventType == 1 then
            chapterDataEntity = GWG.GWHomeMgr.chapterData
        else
            chapterDataEntity = GWG.GWHomeMgr.noviceChapterData
        end
        local eventId = cfg.RegionEvent.data[2]
        if type == 1 then
            --判断当前事件是否完成 如果完成 隐藏建筑
            return not chapterDataEntity.CheckPassEvent(eventId)
        elseif type == 2 then
            --判断当前事件是否完成 如果完成 显示建筑
            return chapterDataEntity.CheckPassEvent(eventId)
        else
            log.Error("GetBuildMainCityIsShow type error", type)
        end
    end
    return true
end

--快速获得该道具是否为栅栏，如果是栅栏的话显隐要特殊处理
function M.GetItemIsFence(itemId)
    return itemId and FenceList[itemId]
end

function M.GetBuildMainCityIsShow(nBuildingID)
    local cfg = M.GetBuildMainCityMapData(nBuildingID)
    if not cfg then
        return true
    end
    return M.GetMapCityShow(cfg.MapID)
end

---@public 根据建筑类型获取可以建造的建筑 (获取建筑是第一个可以建造的)
function M.GetOneCanBuildByTypeId(typeId)
    local cfg = game_scheme:BuildingType_0(typeId)
    if cfg and cfg.BuildingSubtype and cfg.BuildingSubtype > 0 then
        local data = {}
        data.type = cfg.TypeID
        data.iron = player_mgr.GetPlayerIron()
        data.food = player_mgr.GetPlayerFood()
        data.buildId = GWG.GWHomeMgr.cfg.GetBuildId(cfg.TypeID)
        local Cfg_Build = game_scheme:Building_0(data.buildId, 1)
        data.needBuildId = GWG.GWHomeMgr.cfg.GetBuildId(cfg.Preconditions)
        data.level = GWG.GWHomeMgr.buildingData.GetBuildingDataMaxLevel(data.needBuildId)
        local buildList = GWG.GWHomeMgr.buildingData.GetBuildingDataListByBuildingID(data.buildId)
        local index = util.get_len(buildList)
        data.curBuild = index
        data.isNew = false
        local cfg_buidType = game_scheme:BuildingType_0(data.type)
        local Arr = string.split(cfg_buidType.buildable, ";")
        data.maxBuild = 0
        for _, v in pairs(Arr) do
            local arr = string.split(v, "#")
            local needLevel = tonumber(arr[1])
            if needLevel > data.level then
                break
            end
            data.maxBuild = tonumber(arr[2])
        end
        if data.iron < Cfg_Build.iron and data.food < Cfg_Build.food then
            return false
        end
        if data.maxBuild >= 1 and data.curBuild <= 0 then
            return true
        end
    end
    return false
end

---@public 获取伪造建筑队列数据 （0-1）等级
function M.GetForgeRepairBuildQueueData(uSid, buildId)
    local buildCfg = game_scheme:Building_0(buildId, 1)
    if not buildCfg then
        return nil
    end
    local upgradeTime = M.GetBuildingUpgradeReduceTime(buildCfg.time)
    local time = os.server_time() + upgradeTime
    return {
        uDoneTime = time,
        uOverTime = 0,
        uSid = uSid,
    }
end

function M.GetBuildExtraBuildNum(type)
    local homeAddTypeId = GWConst.EHomeExtraBuildingType[type]
    local extraBuildNum = 0
    if homeAddTypeId then
        extraBuildNum = gw_attribute_bonus.GetEffectValue(homeAddTypeId)
    end
    return extraBuildNum
end

return M
