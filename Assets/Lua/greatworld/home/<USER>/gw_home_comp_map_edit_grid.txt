--- Created by lzx.
--- DateTime: 2024/7/26 14:33
--- Des: 家园的地图，可以理解成家园的全局管理器

local require = require
local tostring = tostring

local gw_home_comp_base_class = require "gw_home_comp_base_class"
local Color  = CS.UnityEngine.Color
local newclass = newclass
local GWG = GWG
local UIUtil = UIUtil
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -
module("gw_home_comp_map_edit_grid")
---@class GWHomeCompMapEditGrid : GWDisposableObject 
local GWHomeCompMapEditGrid = newclass("gw_home_comp_map_edit_grid",gw_home_comp_base_class)

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

function GWHomeCompMapEditGrid:Bind(baseObj)
    self.gameObject = baseObj
    self.transform= baseObj.transform
    self.gameObject.name = self.data.posId   
    self:SetInfo(self.data)
end

function GWHomeCompMapEditGrid:OnLoad()
    gw_home_comp_base_class.OnLoaded(self)
    self:SetInfo(self.data)
end

function GWHomeCompMapEditGrid:UpdateData(type)
    local gridX, gridY =   GWG.GWHomeMgr.gridData.GetGridXYByGridPosId(self.posId)   
    if type == 0 then
        UIUtil.SetTextMesh(self.transform,gridX..",".. gridY,"text")
    elseif type == 1 then
        local area =  GWG.GWHomeMgr.gridData.GetAreaId(gridX, gridY)
        area = area or 0
        UIUtil.SetTextMesh(self.transform,tostring(area),"text")
    elseif type == 2 then
        local state =  GWG.GWHomeMgr.gridData.GetGridState(gridX, gridY)
        state = state or 0
        local text  = UIUtil.GetComponent(self.transform,"TextMesh","text")
        local spriteRender = UIUtil.GetComponent(self.transform,"SpriteRenderer","bg")
        --判断一下 建筑是否是城墙        
        local build 
        if state ~=0 then 
             build  = GWG.GWAdmin.GetBSComponentByID(state)
        end 
        if build and build.serData and build.serData.nBuildingID == 37000 then          
            text.color = Color.red
            spriteRender.color = Color.red
        else
            text.color = Color.white
            spriteRender.color = Color.white
        end
        UIUtil.SetTextMesh(self.transform,tostring(state),"text")
    end
end


function GWHomeCompMapEditGrid:SetInfo(info)
    self.posId = info.posId
    GWG.GWAdmin.SwitchUtility.HomeLog("EditGrid setInfo" .. info.posId)
    local x,y,z =   GWG.GWHomeMgr.gridData.GetPosByGridPosId(info.posId)
    local gridX, gridY =   GWG.GWHomeMgr.gridData.GetGridXYByGridPosId(info.posId)
    UIUtil.SetLocalPos(self.transform,x,y,z)
    UIUtil.SetTextMesh(self.transform,gridX..",".. gridY,"text")
end
return GWHomeCompMapEditGrid
