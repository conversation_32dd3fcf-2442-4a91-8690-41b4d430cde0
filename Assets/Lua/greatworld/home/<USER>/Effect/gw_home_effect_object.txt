--- Created by fgy.
--- Changed by connan.
--- DateTime: 2024/9/8 15:05
--- Des:特效加载容器 

local typeof = typeof
local require = require
local UIUtil = UIUtil
local ParticalOrder = CS.War.UI.ParticalOrder
local Renderer = CS.UnityEngine.Renderer
local util = require "util"
module("gw_home_effect_object")

local M = {}

function M:ctor(selfType)
    self.__base:ctor(selfType, "gw_home_effect_object")
end

function M:Init(id, parent, effectParam, offsetPos, callback, offsetRot, offsetScale)
    self:LoadResource(effectParam.path, "", function()
        self.id = id
        self.parent = parent
        self.offsetPos = offsetPos
        self.offsetRot = offsetRot
        self.offsetScale = offsetScale
        self.particleParent = UIUtil.GetComponent(self.transform, "ParticleSystem")
        if not util.IsObjNull(self.particleParent) then
            self.particleParent:Play();
        end
        self:InitEffectParam(effectParam)
        if callback then
            callback(self)
        end
    end, false, parent)
    return self
end

function M:CallFuncName(name, ...)
    if self[name] then
        self[name](self, ...)
    end
end

function M:InitEffectParam(effectParam)
    local initPos = effectParam.position
    if not initPos then
        initPos = { x = 0, y = 0, z = 0 }
    end
    local pos = { x = initPos.x, y = initPos.y, z = initPos.z }
    if self.offsetPos then
        pos.x = pos.x + self.offsetPos.x
        pos.y = pos.y + self.offsetPos.y
        pos.z = pos.z + self.offsetPos.z
    end
    self:SetLocalPosition(pos.x, pos.y, pos.z)

    self:InitRotation(effectParam)
    self:InitScale(effectParam)
    if effectParam.order then
        self:SetRenderOrder(effectParam.order)
    end
end

function M:InitRotation(effectParam)
    if not effectParam.rotation and not self.offsetRot then
        return
    end
    local rotation = effectParam.rotation or { x = 0, y = 0, z = 0 }
    local initRot = { x = rotation.x, y = rotation.y, z = rotation.z }
    if self.offsetRot then
        initRot.x = initRot.x + self.offsetRot.x
        initRot.y = initRot.y + self.offsetRot.y
        initRot.z = initRot.z + self.offsetRot.z
    end
    self:SetRotate(initRot.x, initRot.y, initRot.z)
end

function M:InitScale(effectParam)
    local initScale = self.offsetScale or effectParam.scale
    if not initScale then
        return
    end
    self:SetLocalScale(initScale.x, initScale.y, initScale.z)
end


--[[设置特效模型的orderIndex]]
function M:SetRenderOrder(order, useInterment)
    self.sort_order = order
    self.useInterment = useInterment or false
    if useInterment then
        local children = self.UIRoot:GetComponentsInChildren(typeof(Renderer))
        for i = 0, children.Length - 1 do
            children[i].sortingOrder = order + children[i].sortingOrder
        end
    else
        local particleOrder = self.UIRoot:GetComponent(typeof(ParticalOrder))
        if not particleOrder then
            particleOrder = self.UIRoot:AddComponent(typeof(ParticalOrder))
        end
        particleOrder:SetUseIncrement(true)
        particleOrder:SetOrder(order)
    end
end

--[[设置特效模型的旋转]]
function M:SetRotate(x, y, z)
    if self:IsValid() then
        UIUtil.SetRotation(self.transform, x, y, z)
    end
end

--[[设置特效模型的缩放大小]]
function M:SetLocalScale(x, y, z)
    if self:IsValid() then
        UIUtil.SetLocalScale(self.transform, x, y, z)
    end
end

--[[设置特效模型的缩放大小]]
function M:SetLocalPosition(x, y, z)
    if self:IsValid() then
        UIUtil.SetLocalPos(self.transform, x, y, z)
    end
end

function M:Dispose()
    self.offsetPos = nil
    if not util.IsObjNull(self.particleParent) then
        self.particleParent:Stop()
        self.particleParent = nil
    end
    self.__base:Dispose()
end

local class = require "class"
local base_game_object = require "base_game_object"
CM = class(base_game_object, nil, M)