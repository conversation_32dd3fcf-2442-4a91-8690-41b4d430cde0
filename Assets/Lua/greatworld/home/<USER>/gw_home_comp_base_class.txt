--- Created by lzx.
--- DateTime: 2024/7/26 14:33
--- Des: 家园实体基类

local require = require
local tonumber = tonumber
local game_scheme = require "game_scheme"
local log = require "log"
local util = require "util"
local gw_disposable_object = require "gw_disposable_object"
local newclass = newclass
local GWG = GWG
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

module("gw_home_comp_base_class")
---@class GWHomeCompBaseClass : GWDisposableObject
local GWHomeCompBaseClass = newclass("gw_home_comp_base_class", gw_disposable_object)

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

--- 构造器
function GWHomeCompBaseClass:ctor()
    gw_disposable_object.ctor(self)
    --GWG.GWAdmin.SwitchUtility.HomeLog("GWHomeCompBaseClass ctor")
    self:Init()
end
--- 初始化数据
---@see override
function GWHomeCompBaseClass:Init()
    --GWG.GWAdmin.SwitchUtility.HomeLog("GWHomeCompBaseClass Init"..(self.compName and self.compName or "nil") )
    ---客户端生成得compId
    self.compId = nil
    self.compName = nil
    self.eHomeEntityType = nil
    ---其他数据
    self.entity = nil
    self.isLoaded = false
    ---------服务器下发数据--------------
    self.serData = nil
end

--- 绑定节点
---@param baseObj "base_game_object" 节点
function GWHomeCompBaseClass:Bind(baseObj)
    self.gameObject = baseObj
    self.transform = baseObj.transform
    --注释 不需要改名字了
    --local name = self.compName .. "_" .. self.compId
    --if self.serData and self.serData.uSid then
    --    name = name .. "_" .. self.serData.uSid
    --end
    --self.gameObject.name = name
    self:OnLoaded()
end
---@public 加载完成回调
---@see override
function GWHomeCompBaseClass:OnLoaded()
    self.isLoaded = true
    --GWG.GWAdmin.SwitchUtility.HomeLog("GWHomeCompBaseClass OnLoaded"..self.compName)
    self:RegisterListener()
end

---@public 创建气泡HUD 显示
function GWHomeCompBaseClass:CreateBubbleHud(cityMapId, eventId, time)
    local mapCityCfg = game_scheme:BuildMaincityMap_0(cityMapId)
    if not mapCityCfg then
        return
    end
    if mapCityCfg.bubbleParam.count <= 0 then
        return
    end
    local bubbleAppearStr = mapCityCfg.bubbleAppearID
    local bubbleAppearList = util.split(bubbleAppearStr, ";")
    local bubbleAPpearLen = util.get_len(bubbleAppearList)

    local isShow = false
    local selectId = -1
    for i = 1, bubbleAPpearLen do
        local bubbleAppearID = util.split(bubbleAppearList[i], "#")
        local bubbleAppearIdLen = util.get_len(bubbleAppearID)
        --log.Error(bubbleAppearIdLen)
        if bubbleAppearIdLen >= 2 then
            local eventType = tonumber(bubbleAppearID[1])
            local needEventId = tonumber(bubbleAppearID[2])
            local chapterDataEntity = nil
            if eventType == 3 then
                --严格来说3类型不应该进入这里，但还是做一下处理
            elseif eventType == 1 then
                chapterDataEntity = GWG.GWHomeMgr.chapterData
            else
                chapterDataEntity = GWG.GWHomeMgr.noviceChapterData
            end
            if chapterDataEntity.CheckJustPassEvent(needEventId) then
                isShow = true
                selectId = i
                break
            end
        end
    end
    if not isShow then
        return
    end
    local langId = mapCityCfg.bubbleLang.data[selectId - 1]
    local iconId = mapCityCfg.bubbleID.data[0]
    local iconType = mapCityCfg.bubbleID.data[1]
    local compName = GWG.GWCompName.gw_home_comp_hud_head_bubble
    self:RemoveComponent(compName)
    local showBubbleFun = function()
        if self:GetComponent(compName) then
            self:RemoveComponent(compName)
        end
        local id, comp = GWG.GWAdmin.GWHomeHudUtil.InitMoveHudComponent(compName, self.transform)
        local x = 0
        local y = 0
        local order = 0
        if mapCityCfg.bubblePos and mapCityCfg.bubblePos.count > 0 then
            if mapCityCfg.bubblePos.data[0] then
                x = mapCityCfg.bubblePos.data[0]
            end
            if mapCityCfg.bubblePos.data[1] then
                y = mapCityCfg.bubblePos.data[1]
            end
            if mapCityCfg.bubblePos.data[2] then
                order = mapCityCfg.bubblePos.data[2]
            end
        end
        comp:SetOffset(35 + x, 10 + y,order)
        comp:SetOffsetScale(1)
        comp:SetText(langId, iconId, iconType)
        --log.Error(langId)
        self:AddComponent(compName, comp)
    end
    if mapCityCfg.bubbleParam.data[0] > 0 then
        self.timer = util.DelayCallOnce(mapCityCfg.bubbleParam.data[0], showBubbleFun)
    else
        showBubbleFun()
    end

    self:AddCustomTimer(mapCityCfg.bubbleParam.data[0] + mapCityCfg.bubbleParam.data[1], function()
        local comp = self:GetComponent(compName)
        if comp then
            comp:PlayAnim("HideGuideBubble")
        end
        if self.timer then
            util.RemoveDelayCall(self.timer)
            self.timer = nil
        end
    end)
    self:AddCustomTimer(mapCityCfg.bubbleParam.data[0] + mapCityCfg.bubbleParam.data[1] + 0.5, function()
        self:RemoveComponent(compName)
    end, "DeleteHudBubble")
end

---@public 注册监听相关
---@see override
function GWHomeCompBaseClass:RegisterListener()
    
end
---@public  注销监听相关
---@see override
function GWHomeCompBaseClass:UnregisterListener()
    
end

--- 展示类型3的气泡
function GWHomeCompBaseClass:ShowType3Bubble(cityMapId)
    local mapCityCfg = game_scheme:BuildMaincityMap_0(cityMapId)
    if not mapCityCfg then
        return
    end
    if mapCityCfg.bubbleParam.count <= 0 then
        return
    end
    local cfg_util = require "cfg_util"
    local bubbleAppearID = cfg_util.StringToArray(mapCityCfg.bubbleAppearID,";","#")
    local bubbleAppearLen = util.get_len(bubbleAppearID)
    local selectId = -1
    for i = 1, bubbleAppearLen do
        local eventType = tonumber(bubbleAppearID[i][1])
        if eventType == 3 then
            selectId = i - 1
            break;
        end
    end
    if selectId > -1 then
        local langId = mapCityCfg.bubbleLang.data[selectId]
        local iconId = mapCityCfg.bubbleID.data[0]
        local iconType = mapCityCfg.bubbleID.data[1]
        local compName = GWG.GWCompName.gw_home_comp_hud_head_bubble
        if self:GetComponent(compName) then
            self:RemoveComponent(compName)
        end
        local showBubbleFun = function()
            local id, comp = GWG.GWAdmin.GWHomeHudUtil.InitMoveHudComponent(compName, self.transform)
            local x = 0
            local y = 0
            local order = 0
            if mapCityCfg.bubblePos and mapCityCfg.bubblePos.count > 0 then
                if mapCityCfg.bubblePos.data[0] then
                    x = mapCityCfg.bubblePos.data[0]
                end
                if mapCityCfg.bubblePos.data[1] then
                    y = mapCityCfg.bubblePos.data[1]
                end
                if mapCityCfg.bubblePos.data[2] then
                    order = mapCityCfg.bubblePos.data[2]
                end
            end
            comp:SetOffset(35 + x, 10 + y, order)
            comp:SetOffsetScale(1)
            comp:SetText(langId, iconId, iconType)
            self:AddComponent(compName, comp)
            if self.timer then
                util.RemoveDelayCall(self.timer)
                self.timer = nil
            end
        end
        if mapCityCfg.bubbleParam.data[0] > 0 then
            if self.timer then
                util.RemoveDelayCall(self.timer)
                self.timer = nil
            end
            self.timer = util.DelayCallOnce(mapCityCfg.bubbleParam.data[0], showBubbleFun)
        else
            showBubbleFun()
        end
        local bubbleTime1 = mapCityCfg.bubbleParam.data[0] or 0
        local bubbleTime2 = mapCityCfg.bubbleParam.data[1] or 0
        self:AddCustomTimer(bubbleTime1 + bubbleTime2, function()
            local comp = self:GetComponent(compName)
            if comp then
                comp:PlayAnim("HideGuideBubble")    
            end            
            if self.timer then
                util.RemoveDelayCall(self.timer)
                self.timer = nil
            end
            self.timer = util.DelayCallOnce(0.5, function()
                self:RemoveComponent(compName)
                if self.timer then
                    util.RemoveDelayCall(self.timer)
                    self.timer = nil
                end
            end)
        end)
    end
end

--- 回收
function GWHomeCompBaseClass:Recycle()
    --GWG.GWAdmin.SwitchUtility.HomeLog("GWHomeCompBaseClass Recycle"..(self.compName and self.compName or "nil"))
    self:Init()
    gw_disposable_object.Recycle(self)
end

--- 弃用
function GWHomeCompBaseClass:Dispose()
    --GWG.GWAdmin.SwitchUtility.HomeLog("GWHomeCompBaseClass Dispose"..(self.compName and self.compName or "nil"))    
    -- 销毁节点
    if self.entity then
        self.entity:Dispose()
    end
    if self.timer then
        util.RemoveDelayCall(self.timer)
        self.timer = nil
    end
    local compName = GWG.GWCompName.gw_home_comp_hud_head_bubble
    if self:GetComponent(compName) then
        self:RemoveComponent(compName)
    end
    self.isLoaded = false
    self:UnregisterListener()
    self:Init()
    gw_disposable_object.Dispose(self)
end
return GWHomeCompBaseClass
