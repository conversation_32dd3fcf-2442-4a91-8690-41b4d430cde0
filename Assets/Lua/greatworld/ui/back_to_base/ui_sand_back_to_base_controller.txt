--- ui_sand_back_to_base_controller.txt
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by .
--- DateTime: 
--- desc:    
---
local require = require
local newclass = newclass
local Vector3 = CS.UnityEngine.Vector3
local gw_admin = require "gw_admin"
local gw_sand_data = require "gw_sand_data"
local gw_const = require "gw_const"
local gw_mgr = require "gw_mgr"
local math = require "math"
local screen_util = require "screen_util"
local gw_sand_mgr = require "gw_sand_mgr"
local gw_common_util = require "gw_common_util"
local event = require "event"
local controller_base = require "controller_base"

module("ui_sand_back_to_base_controller")
local controller = nil
local UIController = newclass("ui_sand_back_to_base_controller", controller_base)

--[[窗口初始化]]
function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self, view_name, controller_name)

    self.screenCenter = { x = screen_util.width / 2, y = screen_util.height / 2 }
    self.YMax = screen_util.GetCanvasWithMesh().sizeDelta.y
    self.YMin = 200
    self.XMax = screen_util.width
    self.XMin = 0
end

--[[界面被显示的时候调用]]
function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close(data)
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

-- 计算与屏幕边缘的交点
function UIController:CalculateIntersection(screenWidth, screenHeight, direction)
    -- 确保方向向量是单位向量
    local normalizedDir = Vector3.Normalize(direction)
    normalizedDir.y = normalizedDir.z
    -- 屏幕中心
    local screenCenter = self.screenCenter

    -- 计算与屏幕边缘的交点
    if normalizedDir.x == 0 then
        tX = math.huge  -- x不参与计算，只观察y方向
    elseif normalizedDir.x > 0 then
        tX = (screenWidth - screenCenter.x) / normalizedDir.x
    else
        tX = -screenCenter.x / normalizedDir.x
    end

    -- 判断 normalizedDir.y 是否为 0，避免除以 0 的错误
    if normalizedDir.y == 0 then
        tY = math.huge  -- y不参与计算，只观察x方向
    elseif normalizedDir.y > 0 then
        tY = (screenHeight - screenCenter.y) / normalizedDir.y
    else
        tY = -screenCenter.y / normalizedDir.y
    end

    local intersectionPoint
    if tX < tY then
        intersectionPoint = {
            x = screenCenter.x + normalizedDir.x * tX,
            y = screenCenter.y + normalizedDir.y * tX
        }
    else
        intersectionPoint = {
            x = screenCenter.x + normalizedDir.x * tY,
            y = screenCenter.y + normalizedDir.y * tY
        }
    end
    return intersectionPoint
end

function UIController:IsWorldPointInScreenBounds(worldPoint)
    -- 获取屏幕四个角的世界坐标
    local bottomLeft = gw_sand_mgr.GetWorldPosByScreenPos({ x = 0, y = 0 })
    local bottomRight = gw_sand_mgr.GetWorldPosByScreenPos({ x = screen_util.width, y = 0 })
    local topLeft = gw_sand_mgr.GetWorldPosByScreenPos({ x = 0, y = screen_util.height })
    local topRight = gw_sand_mgr.GetWorldPosByScreenPos({ x = screen_util.width, y = screen_util.height })

    -- 创建一个包围盒，使用各个角的 x 和 z 来确定范围
    local minBound = Vector3(math.min(bottomLeft.x, bottomRight.x, topLeft.x, topRight.x), 0,
            math.min(bottomLeft.z, bottomRight.z, topLeft.z, topRight.z))
    local maxBound = Vector3(math.max(bottomLeft.x, bottomRight.x, topLeft.x, topRight.x), 0,
            math.max(bottomLeft.z, bottomRight.z, topLeft.z, topRight.z))

    -- 检查目标世界坐标是否在包围盒内
    return (worldPoint.x >= minBound.x and worldPoint.x <= maxBound.x and
            worldPoint.z >= minBound.z and worldPoint.z <= maxBound.z)
end

--会基类自动调用
function UIController:AutoSubscribeEvents()
    local lastPos = nil
    local lastCurCenterPos = nil
    self.ChangeMoveBtnGridPos = function()
        if not gw_common_util.CheckInSand_All() then
            return
        end

        self.HomeData = self.HomeData or {}
        -- 如果视野和基地不再一起则隐藏,迁城状态下也隐藏
        if gw_common_util.CheckInSand_Sand() then
            if not gw_common_util.GetSandVisualAndBaseCoServiceState() or gw_admin.SandRelocationEntityUtil.GetRelocationEntityState() then
                self.HomeData.isHide = true
                self:TriggerUIEvent("SetHomeButtonData", self.HomeData)
                return
            end
        end
       
        -- 屏幕中心
        local curCenterPos = gw_sand_mgr.GetWorldPosByScreenPos(self.screenCenter)
        --不动的时候不刷新
        if lastCurCenterPos and curCenterPos and lastCurCenterPos.x == curCenterPos.x and lastCurCenterPos.y == curCenterPos.y then
            return
        end

        local gridPos = gw_sand_data.selfData.GetSelfVisualSandBoxPosition()
        if not gridPos then
            return
        end

        lastCurCenterPos = curCenterPos
        local gridWorldPos = Vector3(gridPos.x, 0, gridPos.y)
        local dir = gridWorldPos - curCenterPos
        --计算是否需要显示
        self.HomeData.isHide = self:IsWorldPointInScreenBounds(gridWorldPos)
        if not self.HomeData.isHide then
            -- 计算与屏幕边缘的交点
            self.HomeData.screenPos = self:CalculateIntersection(screen_util.width, screen_util.height, dir)
        end
        local centerPos = gw_sand_data.GetGridPos()
        --每帧调用，字符串开销大，只变化格子数的时候才计算
        if lastPos and centerPos and lastPos.x == centerPos.x and lastPos.y == centerPos.y then

        else
            self.HomeData.distanceStr = gw_common_util.GetKilometerStrByGrid(gridPos, centerPos)
            self.HomeData.distance = gw_common_util.GetKilometerByGrid(gridPos, centerPos)
        end
        lastPos = centerPos
        self:TriggerUIEvent("SetHomeButtonData", self.HomeData)
    end

    self.GW_SCENE_CHANGE_SUCCESS = function()
        if not gw_common_util.CheckInSand_All() and self.HomeData then
            self.HomeData = self.HomeData or {}
            self.HomeData.isHide = true
            self:TriggerUIEvent("SetHomeButtonData", self.HomeData)
        end
    end

    self.changeFullScreen = function(eventName, state)
        if gw_common_util.CheckInSand_All() then
            self.HomeData = self.HomeData or {}
            self.HomeData.isHide = state
            self.HomeData.isCross = not gw_common_util.GetSandVisualAndBaseCoServiceState()
            self:TriggerUIEvent("SetHomeButtonData", self.HomeData)
        end
    end

    -- 语言变更
    self.languageChange = function()
        if gw_common_util.CheckInSand_All() and self.HomeData then
            local gridPos = gw_common_util.GetSandBasePosition()
            local centerPos = gw_sand_data.GetGridPos()
            if not gridPos or not centerPos then
                return
            end
            self.HomeData.distanceStr = gw_common_util.GetKilometerStrByGrid(gridPos, centerPos)
            self:TriggerUIEvent("SetHomeButtonData", self.HomeData)
        end
    end

    self:RegisterEvent(event.LANGUAGE_SETTING_CHANGED, self.languageChange)
    self:RegisterEvent(event.GW_SCENE_CHANGE_SUCCESS, self.GW_SCENE_CHANGE_SUCCESS)
    self:RegisterEvent(event.UI_FULL_SCREEN_STATE_CHANGE, self.changeFullScreen)
    self:RegisterEvent(event.CSUpdate, self.ChangeMoveBtnGridPos)
end

function UIController:OnBtn_Btn_HomeClickedProxy()
    gw_common_util.JumpToBase()
end

--region ModuleFunction 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
