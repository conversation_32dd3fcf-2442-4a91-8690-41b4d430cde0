--region FileHead
--- ui_object_overlap_list_show.txt
-- author:  hym
-- ver:     1.0
-- desc:    重叠列表
-------------------------------------------------
--endregion 

--region Require
local require = require
local type = type
local ipairs = ipairs
local typeof = typeof
local string = string
local GWG = GWG

local GameObject = CS.UnityEngine.GameObject
local RectTransformUtility = CS.UnityEngine.RectTransformUtility
local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local Image = CS.UnityEngine.UI.Image

local enum_define = require "enum_define"
local class = require "class"
local ui_base = require "ui_base"
local windowMgr = require "ui_window_mgr"
--endregion 




--region ModuleDeclare
module("ui_object_overlap_list_show")
local window = nil
local UIView = {}
--endregion 

--region WidgetTable
UIView.widget_table = {
    maskBtn = {path = "Mask", type = "Button", backEvent = true, event_name = "closeBtnEvent"},--返回
    overlapList = {path = "OverlapList", type = "RectTransform"},
    titleText = {path = "OverlapList/TitleText", type = "Text"},--标题
    objectSingleUI = {path = "OverlapList/ObjectSingleUI", type = "RectTransform"},
}
--endregion 

--function 设置View-Controller模式的UI
-- return type  ---- 未定义/VC/纯V   
-- 注意，View-Controller模式的ui必须要重写这个接口
function UIView:SetVCTypeUI()
    return self.__base:SetVCTypeUI(enum_define.enum_ui_vc_type.vc)
end

--endregion 


--region WindowInit
--[[窗口初始化]]

--region WindowInit
--[[窗口初始化]]
function UIView:Init()
    self:SetVCTypeUI()
    self.__base:Init(self)
    --region User
    --endregion 
end --///<<< function

--endregion 


--region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function UIView:OnShow()

end --///<<< function

--endregion 

--region WindowOnHide
--[[界面隐藏时调用]]
function UIView:OnHide()
    --region User
    --endregion 
end --///<<< function

--endregion 


--region WindowClose
function UIView:Close()
    self.__base:Close()
    window = nil    
    --region User
    --endregion 
end --///<<< function

--endregion 
---********************功能函数区**********---
function UIView:InitList()
    for i, n in ipairs(self.data) do
        local singleObject = GameObject.Instantiate(self.objectSingleUI, self.overlapList.transform)
        singleObject.gameObject:SetActive(true)
        singleObject.name = self.data[i].objectName

        local turnBtn = singleObject:GetComponent(typeof(Button))
        turnBtn.onClick:AddListener(self.data[i].onBtnClicked)


        local objectName = singleObject.transform:Find("ObjectName"):GetComponent(typeof(Text))
        local objectIcon = singleObject.transform:Find("ObjectIcon"):GetComponent(typeof(Image))

        objectName.text = self.data[i].objectName
        --[[local objectName_tmp = data[i].objectName  or ""
        if dataItem.objectName then
            objectName_tmp = lang.Get(data[i].objectName)
        end

        local length = (lang.USE_LANG == lang.ZH) and 15 or 25
        local name = string.truncate(objectName_tmp, length, 1)
        if string.clen(objectName_tmp, 1) > length then
            name = name .. "..."
        end
        objectName.text = name or objectName_tmp]]

        GWG.GWAssetMgr:LoadSandMinMapIcon(objectIcon,self.data[i].objectIcon)

    end
end
function UIView:SetListData(data)
    self.data = data
    self:InitList()
end

function UIView:SetListShowPosition(screenPos)

    self.camera = self.camera or windowMgr:GetUICamera()
    local bOk , localpoint = RectTransformUtility.ScreenPointToLocalPointInRectangle( self.transform.parent , screenPos, self.camera)
    self.transform.anchoredPosition = localpoint
end

---********************end功能函数区**********---

--region WindowInherited
local CUIView = class(ui_base, nil, UIView)
--endregion 

--region static ModuleFunction 
-- 特别注意，当前并不是由controller层来驱动ui的生命流程的 
-- 当前因为需要view层 也就是ui_base来驱动ui的init  加载完成，show等流程，所以流程仍然保留，而controller层的流程逻辑受view流程影响，
-- view对应的Init/Show 加载完后，当前会通过事件同步调用controller层的Init/Show流程，controller层的流程逻辑才会执行。
--当前仍然保留了静态的Show，Close接口流程
function Show(data)
    if data == nil then
        data = {}
    end
    if data["uipath"] == nil then
        data["uipath"] = "ui/prefabs/sandbox/uiobjectoverlaplistshow.prefab"
    end
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
        window:LoadUIResource(data["uipath"], nil, nil, nil)
    end
    --这里调用window:Show()  会造成多次调用 但hide后却又需要 uiwindowmgr有问题 TODO
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close()
        window = nil
    end
end
--endregion
