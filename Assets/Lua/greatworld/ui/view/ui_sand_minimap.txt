-- author:  yuannan
-- ver:     1.0
-- desc:    沙盘地图小地图展示界面

--region Require
local require = require
local pairs = pairs
local string = string
local stringFormat = string.format
local math = math

local data_personalInfo = require "data_personalInfo"
local enumDefine = require "enum_define"
local class = require "class"
local uiBase = require "ui_base"
local util = require "util"
local gwSandMgr = require "gw_sand_mgr"
local spriteAsset = require "card_sprite_asset"
local faceItem = require "face_item_new"
local gameScheme = require "game_scheme"
local lang = require "lang"
local tostring = tostring
local texture2D = CS.UnityEngine.Texture2D
local textureFormat = CS.UnityEngine.TextureFormat
local textureFilter = CS.UnityEngine.FilterMode
local uObject = CS.UnityEngine.Object
local vector2 = CS.UnityEngine.Vector2
local vector3 = CS.UnityEngine.Vector3
local color = CS.UnityEngine.Color
local rectTransformUtility = CS.UnityEngine.RectTransformUtility
--endregion 

--region Module Declare
module("ui_sand_minimap")
local ui_path = "ui/prefabs/sandbox/uisandminimap.prefab"
local window = nil
local UISandMiniMap = {}

local clearColor = color.clear
local memberColor = color(0.2, 0.69, 1, 1)
local leaderColor = color.magenta

UISandMiniMap.widget_table = {
    Tran_FaceIcon = { path = "TopShow/Auto_FaceIcon", type = "RectTransform", event_name = "" },

    Tran_FrayInfo = { path = "TopShow/Auto_FrayInfo", type = "RectTransform", event_name = "" },
    Text_FrayName = { path = "TopShow/Auto_FrayInfo/fray/Auto_FrayName", type = "TextMeshProUGUI" },
    Text_KingName = { path = "TopShow/Auto_FrayInfo/king/Auto_KingName", type = "Text" },

    GuidanceIcon_1 = { path = "TopShow/guidanceList/table_1/Auto_Icon", type = "Image" },
    GuidanceText_1 = { path = "TopShow/guidanceList/table_1/Auto_Title", type = "Text" },
    GuidanceIcon_2 = { path = "TopShow/guidanceList/table_2/Auto_Icon", type = "Image" },
    GuidanceText_2 = { path = "TopShow/guidanceList/table_2/Auto_Title", type = "Text" },
    GuidanceIcon_3 = { path = "TopShow/guidanceList/table_3/Auto_Icon", type = "Image" },
    GuidanceText_3 = { path = "TopShow/guidanceList/table_3/Auto_Title", type = "Text" },

    Btn_BaseBtn = { path = "BottomShow/Auto_BaseBtn", type = "Button", event_name = "OnBaseButtonEvent" },

    Tran_MiniMap = { path = "TopShow/Auto_MiniMap", type = "RectTransform" },
    Tran_Mask = { path = "TopShow/Auto_MiniMap/Auto_Mask", type = "RectTransform" },
    Btn_Masked = { path = "TopShow/Auto_MiniMap/Auto_Masked", type = "Button", event_name = "OnMaskedButtonEvent" },
    RImg_Masked = { path = "TopShow/Auto_MiniMap/Auto_Masked", type = "RawImage" },
    Tran_Area = { path = "TopShow/Auto_MiniMap/Auto_Masked/Auto_Area", type = "RectTransform" },
    Tran_PlayerMasked = { path = "TopShow/Auto_MiniMap/Auto_Masked/Auto_PlayerMasked", type = "RectTransform" },
}
--endregion 

--region Module Life
-- 需要指定vc的类型
function UISandMiniMap:SetVCTypeUI()
    return self.__base:SetVCTypeUI(enumDefine.enum_ui_vc_type.vc)
end

-- 资源初始化的时候调用
function UISandMiniMap:Init()
    self:SetVCTypeUI()
    self.__base:Init(self)

    self.disposables = {}
    self.minimapData = {}
    self:InitStaticViewData()
end

-- 资源显示的时候调用
function UISandMiniMap:OnShow()
    self.__base:OnShow()
end

--界面隐藏时调用
function UISandMiniMap:OnHide()
    self.__base:OnHide()
end

-- 界面销毁时调用
function UISandMiniMap:Close()
    if self.faceItem then
        self.faceItem:Dispose()
        self.faceItem = nil
    end

    if self.disposables then
        for _, v in pairs(self.disposables) do
            v:Dispose()
        end
        self.disposables = nil
    end

    if self.minimapData ~= nil then
        if util.IsObjNull(self.minimapData.maskedTexture) then
            uObject:Destroy(self.minimapData.maskedTexture)
            self.minimapData.maskedTexture = nil
        end
        self.minimapData = nil
    end
    self.__base:Close()
    window = nil
end
--endregion 

-- 初始化静态数据
function UISandMiniMap:InitStaticViewData()
    -- 初始化多语言等，静态信息
    self.GuidanceText_1.text = tostring(lang.Get(560108))
    local baseMinIcon = gameScheme:SandMapModelResource_0(101).minIcon1
    local leaderMinIcon = gameScheme:SandMapModelResource_0(102).minIcon1
    local allyMinIcon = gameScheme:SandMapModelResource_0(103).minIcon1

    self:LoadModuleIcon(baseMinIcon, self.GuidanceIcon_1)
    self.GuidanceText_2.text = tostring(lang.Get(560110))
    self:LoadModuleIcon(leaderMinIcon, self.GuidanceIcon_2)
    self.GuidanceText_3.text = tostring(lang.Get(560109))
    self:LoadModuleIcon(allyMinIcon, self.GuidanceIcon_3)
end

-- 设置头像数据
function UISandMiniMap:SetPlayerInfo(data)
    if data == nil then
        return
    end
    if self.faceItem == nil then
        self.faceItem = faceItem.CFaceItem()
        self.faceItem:Init(self.Tran_FaceIcon, nil, 1)
    end
    self.faceItem:SetFaceInfo(data.faceID, function()
        local mgr_personalInfo = require "mgr_personalInfo"
        mgr_personalInfo.ShowPersonalInfoView()
    end)
    self.faceItem:SetActorLvText(true, data.playerLevel)
    self.faceItem:SetFrameID(data.frameID, true)
end

-- 设置战区数据
function UISandMiniMap:SetFrayInfo(data)
    if data == nil or not data.isOccupy then
        self.Tran_FrayInfo.gameObject:SetActive(false)
        return
    end
    self.Tran_FrayInfo.gameObject:SetActive(true)
    self.Text_FrayName.text = stringFormat("%s %s", lang.Get(560111), tostring(data.frayId))
    self.Text_KingName.text = tostring(data.memberName)
end

-- 设置小地图基本信息
local defaultUIGridSize = 190;
function UISandMiniMap:SetMinimapBaseInfo(gridSize, gridZoom, maxLevel)
    if (gridSize and gridZoom and maxLevel) then
        self.minimapData.maxLevel = maxLevel
        self.minimapData.gridSize = gridSize
        self.minimapData.gridZoom = gridZoom
        self.minimapData.textureSize = gridSize / gridZoom
        self.minimapData.maskedTexture = texture2D(self.minimapData.textureSize, self.minimapData.textureSize, textureFormat.RGBA32, false, true)
        self.minimapData.maskedTexture.filterMode = textureFilter.Point
        local colors = {}
        for i = 1, self.minimapData.textureSize * self.minimapData.textureSize do
            colors[i] = clearColor
        end
        self.minimapData.maskedTexture:SetPixels(colors)
        self.minimapData.maskedTexture:Apply()
        -- 设置纹理
        self.RImg_Masked.texture = self.minimapData.maskedTexture
        -- 设置大小缩放
        local maskedScale = defaultUIGridSize / gridSize
        self.RImg_Masked.rectTransform.sizeDelta = vector2(gridSize, gridSize)
        self.RImg_Masked.rectTransform.localScale = vector3(maskedScale, maskedScale, maskedScale)
        self.Tran_Mask.localScale = self.RImg_Masked.rectTransform.localScale
    end
end

-- 设置玩家位置
function UISandMiniMap:SetPlayerPoint(playerPos)
    if playerPos and self.minimapData.pPosition ~= playerPos then
        self.minimapData.pPosition = playerPos
        self.Tran_PlayerMasked.anchoredPosition = vector2(playerPos.x, playerPos.y)
    end
end

-- 设置联盟成员位置
function UISandMiniMap:SetMemberPoint(memberInfo, leaderId)
    if memberInfo then
        if self.minimapData.memberInfoLength and self.minimapData.memberInfoLength > 0 then
            for i = 1, self.minimapData.memberInfoLength do
                self:SetGridPosColors(self.minimapData.memberInfo[i].pos, clearColor)
            end
            self.minimapData.memberInfo = nil
        end

        self.minimapData.memberInfo = memberInfo
        self.minimapData.memberInfoLength = #memberInfo

        local leaderItem = nil
        local roleId = data_personalInfo.GetPersonalInfoValue(data_personalInfo.PropEnum.RoleID)
        for i = 1, self.minimapData.memberInfoLength do
            local memberItem = self.minimapData.memberInfo[i]
            if roleId ~= memberItem.dbID then
                if leaderId == memberItem.dbID then
                    leaderItem = memberItem
                else
                    self:SetGridPosColors(memberItem.pos, memberColor)
                end
            end
        end
        -- 盟主最后上色
        if leaderItem then
            self:SetGridPosColors(leaderItem.pos, leaderColor)
        end

        self.minimapData.maskedTexture:Apply()
    end
end

-- 设置相机位置和坐标
function UISandMiniMap:SetCameraPositionAndSize(cameraPos, cameraSize, level)
    if (cameraPos and self.minimapData.cPosition ~= cameraPos) then
        self.minimapData.cPosition = cameraPos
        self.Tran_Area.anchoredPosition = cameraPos
        self.Tran_Mask.position = self.Tran_Area.position
    end

    if (cameraSize and self.minimapData.cSize ~= cameraSize) then
        self.minimapData.cSize = cameraSize
        local sizeDelta = vector2(cameraSize.w, cameraSize.h)
        self.Tran_Area.sizeDelta = sizeDelta
        self.Tran_Mask.sizeDelta = sizeDelta
    end
    if (level and level <= self.minimapData.maxLevel) then
        self.Tran_Mask.sizeDelta = self.RImg_Masked.rectTransform.sizeDelta
        self.Tran_Mask.anchoredPosition = self.RImg_Masked.rectTransform.anchoredPosition
    end
end

--region 工具逻辑    

--加载模块图标
function UISandMiniMap:LoadModuleIcon(icon, image)
    local sAsset = self.disposables.systemSpriteAsset
    if (not sAsset) then
        sAsset = spriteAsset.CreateGWSandMinMap()
        self.disposables.systemSpriteAsset = sAsset
    end
    sAsset:GetSprite(icon, function(sprite)
        if (not self:IsValid()) or util.IsObjNull(image) then
            return
        end
        image.sprite = sprite
    end)
end

-- 设置像素颜色
function UISandMiniMap:SetGridPosColors(pos, colorValue)
    if not util.IsObjNull(self.minimapData.maskedTexture) then
        local gridX = math.floor(pos.x / self.minimapData.gridZoom + 0.5)
        local gridY = math.floor(pos.y / self.minimapData.gridZoom + 0.5)
        --self.minimapData.maskedTexture:SetPixel(gridX, gridY, colorValue)
        -- 为了明显一点设置大
        for i = -1, 1 do
            for j = -1, 1 do
                local x = math.clamp(gridX + i, 0, self.minimapData.textureSize - 1)
                local y = math.clamp(gridY + j, 0, self.minimapData.textureSize - 1)
                self.minimapData.maskedTexture:SetPixel(x, y, colorValue)
            end
        end
    end
end

-- 设置像素颜色
function UISandMiniMap:JumpToWorldGrid(screenPoint, camera)
    local rectTransform = self.RImg_Masked.rectTransform
    local success, localPoint2 = rectTransformUtility.ScreenPointToLocalPointInRectangle(rectTransform, vector2(screenPoint.x, screenPoint.y), camera)
    if success then
        local gridX = math.floor(localPoint2.x + rectTransform.sizeDelta.x / 2 + 0.5)
        local gridY = math.floor(localPoint2.y + rectTransform.sizeDelta.y / 2 + 0.5)
        gwSandMgr.JumpToTargetGrid({ x = gridX, y = gridY })
    end
end
--endregion

--region Window Inherited
local CUISandMinimapView = class(uiBase, nil, UISandMiniMap)

function Show(data)
    if data and data["uipath"] then
        ui_path = data["uipath"];
    end
    if window == nil then
        window = CUISandMinimapView()
        window._NAME = _NAME
        window:LoadUIResource(ui_path, nil, nil, nil, nil, nil, nil, false)
    end
    --这里调用window:Show()  会造成多次调用 但hide后却又需要 uiwindowmgr有问题 TODO
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close()
        window = nil
    end
end
--endregion
