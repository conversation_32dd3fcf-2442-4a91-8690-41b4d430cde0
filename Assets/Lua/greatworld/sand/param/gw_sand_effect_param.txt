--- Created by fgy.
--- Changed by connan
--- DateTime: 2024/10/09 
--- Des:vision参数定义 视野变更

local GWSandEffectConst = require "gw_sand_effect_const"
local Effect = GWSandEffectConst.Effect
local EffectBase = GWSandEffectConst.EffectBase

local GWSandEffectParam = {}

---@see 特效默认参数定义
function GWSandEffectParam.CreateEffectParam(delayTime, showTime, autoRemove)
    local paramValue = { delayTime = delayTime, showTime = showTime, autoRemove = autoRemove }
    return paramValue
end

function GWSandEffectParam.NewEffectParam(key, paramValue)
    local effectParam = Effect[key]
    if effectParam == nil then
        return nil
    end
    local tableParam = {
        resPath = effectParam.resPath, -- 特效路径,不可更改
        delayTime = paramValue and paramValue.delayTime or effectParam.delayTime,
        showTime = paramValue and paramValue.showTime or effectParam.showTime,
        autoRemove = paramValue and paramValue.autoRemove or effectParam.autoRemove
    }
    return tableParam
end

---@see 特效基础参数定义
function GWSandEffectParam.CreateEffectBaseParam(position, offset, rotate, scale, order, layer)
    local paramValue = { position = position, offset = offset, rotate = rotate, scale = scale, order = order, layer = layer }
    return paramValue
end

function GWSandEffectParam.NewEffectBaseParam(key, paramValue)
    local effectBaseParam = EffectBase[key]
    if effectBaseParam == nil then
        return nil
    end

    local tableParam = {
        position = paramValue and paramValue.position or effectBaseParam.position,
        offset = paramValue and paramValue.offset or effectBaseParam.offset,
        rotate = paramValue and paramValue.rotate or effectBaseParam.rotate or nil,
        scale = paramValue and paramValue.scale or effectBaseParam.scale,
        order = paramValue and paramValue.order or effectBaseParam.order,
        layer = paramValue and paramValue.layer or effectBaseParam.layer,
        localRotate = paramValue and paramValue.localRotate or effectBaseParam.localRotate or nil
    }
    return tableParam
end

return GWSandEffectParam