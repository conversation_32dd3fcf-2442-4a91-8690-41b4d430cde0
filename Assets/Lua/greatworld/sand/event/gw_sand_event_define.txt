---
--- Created by: y<PERSON><PERSON>.
--- DateTime: 2025/3/8.
--- Desc: 沙盘的事件模块
---
module("gw_sand_event_define")

---@public 沙盘事件触发
GW_SAND_INIT_SELF_DATA_FINISH = "GW_SAND_INIT_SELF_DATA_FINISH"

---@public 沙盘进入事件
GW_SAND_ENTER = "GW_SAND_ENTER"

---@public 沙盘离开事件
GW_SAND_EXIT = "GW_SAND_EXIT"

---@public 沙盘事件触发
GW_SAND_FUNCTION_TRIGGER = "GW_SAND_FUNCTION_TRIGGER"

---@public 沙盘输入拖动事件
GW_SAND_INPUT_MOVE = "GW_SAND_INPUT_MOVE"

---@public 沙盘重新进入事件
GW_SAND_RE_ENTER = "GW_SAND_RE_ENTER"

---@public 沙盘重新简化模式事件
GW_SAND_SIMPLE_LEVEL_CHANGED = "GW_SAND_SIMPLE_LEVEL_CHANGED"