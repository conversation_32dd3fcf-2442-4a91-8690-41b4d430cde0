--- Created by fgy.
--- Changed by <PERSON><PERSON>.
--- DateTime: 2025/3/27
--- Des: 作为内部发出的综合处理中心,处理进入沙盘后的事件,涉及到多模块之间调度的,在这边处理

local require = require
local type = type

local gw_comp_name = require "gw_comp_name"
local gw_sand_event_define = require "gw_sand_event_define"
local sand_ui_event_define = require "sand_ui_event_define"

local gw_admin = require "gw_admin"
local event = require "event"

local gw_ed = require "gw_ed"
local Event = gw_ed.mgr

local gw_switch_utility = require "gw_switch_utility"
local OnSandLuaErr = gw_switch_utility.OnSandLuaErr

---@class GWSandInternalMgr
local GWSandInternalMgr = {}

local gw_sand_camera_mgr
local sandbox_ui_mgr

local function requireModule()
    gw_sand_camera_mgr = gw_sand_camera_mgr or require "gw_sand_camera_mgr"
    sandbox_ui_mgr = sandbox_ui_mgr or require "sandbox_ui_mgr"
end

local EventNameToFunctionName = {
    [gw_ed.GW_SAND_VIEW_LEVEL_CHANGE] = "OnViewLevelChange",
    [gw_ed.GW_SAND_CLICK_SCENE_EVENT] = "OnClickSceneEntity",
    [event.LANGUAGE_SETTING_CHANGED] = "OnLanguageChange",
}

---@see 初始化
function GWSandInternalMgr.InitSand()
    requireModule()

    Event:Register(gw_ed.GW_SAND_INTERNAL_DATA_CHANGE, GWSandInternalMgr.SandEventCenter)
    Event:Register(gw_ed.GW_SAND_CLICK_SCENE_EVENT, GWSandInternalMgr.CommonEventCenter)
    Event:Register(gw_ed.GW_SAND_VIEW_LEVEL_CHANGE, GWSandInternalMgr.CommonEventCenter)

    event.Register(gw_sand_event_define.GW_SAND_FUNCTION_TRIGGER, GWSandInternalMgr.SandEventCenter)
    event.Register(event.LANGUAGE_SETTING_CHANGED, GWSandInternalMgr.CommonEventCenter)
end

--- 通用事件处理中心
function GWSandInternalMgr.CommonEventCenter(eventName, ...)
    local funcName = EventNameToFunctionName[eventName]
    local luaFunction = GWSandInternalMgr[funcName]
    if luaFunction and type(luaFunction) == "function" then
        xpcall(luaFunction, OnSandLuaErr, ...)
    end
end

--- 沙盘事件处理中心
function GWSandInternalMgr.SandEventCenter(eventName, funcName, ...)
    local luaFunction = GWSandInternalMgr[funcName]
    if luaFunction and type(luaFunction) == "function" then
        xpcall(luaFunction, OnSandLuaErr, ...)
    end
end

--region Input Internal Event
function GWSandInternalMgr.OnViewLevelChange(newLod, oldLod)
    if gw_admin.SandRelocationEntityUtil.GetRelocationEntityState() then
        -- 刷新一下按钮的显示
        gw_admin.SandRelocationEntityUtil.SetMoveChoseHudState()
        return
    end
    sandbox_ui_mgr.GW_SAND_VIEW_LEVEL_CHANGE(newLod)
end

function GWSandInternalMgr.OnCameraZoomChange(distance)
    gw_sand_camera_mgr.SetCameraZoom(distance)
end
--endregion

--region Relocation Internal Event
function GWSandInternalMgr.OnSandRelocationEntityCompleted(state, gridPos)
    gw_admin.SandRelocationEntityUtil.RelocationEntityCompleted(state, gridPos)
    gw_admin.SandRelocationEntityUtil.CancelRelocationEntitySid(not state)

    local level = gw_sand_camera_mgr.GetViewLevel()
    sandbox_ui_mgr.GW_SAND_VIEW_LEVEL_CHANGE(level)
end

function GWSandInternalMgr.OnSandAllianceBossRelocationSuccess(gridPos)
    if gridPos then
        gw_sand_camera_mgr.OnResetDxfAndJump(gridPos)
    end
end

--endregion

function GWSandInternalMgr.OnClickSceneEntity(_, funcName, tab, compID)
    gw_admin.MarchOperateUtil.OnRemoveOperateHudByClick(compID)
    gw_admin.SandRelocationEntityUtil.HideMoveBaseHud()
end

function GWSandInternalMgr.OnInputBegin()
    gw_admin.MarchOperateUtil.OnRemoveOperateHud()
    gw_sand_camera_mgr.SetFollowStatus(false)
    event.Trigger(sand_ui_event_define.GW_CLICK_EMPTY)
end

function GWSandInternalMgr.OnInputMove()
    gw_admin.SandRelocationEntityUtil.HideMoveBaseHud()
    event.Trigger(gw_sand_event_define.GW_SAND_INPUT_MOVE)
end

function GWSandInternalMgr.OnInputEnd()

end

function GWSandInternalMgr.OnClickUpEvent()
    event.Trigger(sand_ui_event_define.GW_CLICK_EMPTY_STRICT)
end

function GWSandInternalMgr.OnClickTeamBySid(tab)
    if tab.sid and tab.sid > 0 then
        local comp = gw_admin.MapUtil.GetMarchCompBySid(tab.sid)
        if comp then
            if comp:GetComponent(gw_comp_name.comp_march_item) then
                gw_admin.MarchOperateUtil.OnShowOperateHud(comp.serData, comp:GetComponent(gw_comp_name.comp_march_item))
            else
                OnSandLuaErr("GWSandInternalMgr.OnClickTeamBySid error sid:", gw_comp_name.comp_march_item)
            end
        else
            OnSandLuaErr("GWSandInternalMgr.OnClickTeamBySid error sid:", tab.sid)
        end
    else
        local gw_common_util = require "gw_common_util"
        gw_common_util.JumpToSid(tab.targetSid)
    end
end

function GWSandInternalMgr.OnLanguageChange()
    gw_admin.SandRelocationEntityUtil.HideMoveBaseHud()
end

function GWSandInternalMgr.UpdateSandBoxBaseInfo_Finish()
    local gw_common_util = require "gw_common_util"
    if gw_common_util.CheckInSand_All() then
        gw_admin.MapUtil.OnChangedSandBaseEntity()
        gw_sand_camera_mgr.OnResetToBase()
    end
end

function GWSandInternalMgr.Dispose()
    Event:Unregister(gw_ed.GW_SAND_INTERNAL_DATA_CHANGE, GWSandInternalMgr.SandEventCenter)
    Event:Unregister(gw_ed.GW_SAND_CLICK_SCENE_EVENT, GWSandInternalMgr.CommonEventCenter)
    Event:Unregister(gw_ed.GW_SAND_VIEW_LEVEL_CHANGE, GWSandInternalMgr.CommonEventCenter)

    event.Unregister(gw_sand_event_define.GW_SAND_FUNCTION_TRIGGER, GWSandInternalMgr.SandEventCenter)
    event.Unregister(event.LANGUAGE_SETTING_CHANGED, GWSandInternalMgr.CommonEventCenter)
end

return GWSandInternalMgr