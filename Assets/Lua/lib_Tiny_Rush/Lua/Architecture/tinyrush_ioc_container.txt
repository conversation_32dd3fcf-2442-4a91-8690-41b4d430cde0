---@class tinyrush_ioc_container : TRClass @IOC容器
local tinyrush_ioc_container = TinyRush_CreateClass("tinyrush_ioc_container")
-- 记录别名的表
tinyrush_ioc_container.mInstances = nil
-- 记录方法名的表
tinyrush_ioc_container.mInstancesWithClass = nil
tinyrush_ioc_container.__index = function(t, k)
    local result = tinyrush_ioc_container[k]
    if result == nil then
        result = t:get(k)
    end
    return result
end
function tinyrush_ioc_container:register(class, alias)
    if alias ~= nil then
        self.mInstances[alias] = class
    else
        self.mInstancesWithClass[class.__name] = class
    end
end
function tinyrush_ioc_container:get(name)
    local result = self.mInstancesWithClass[name]
    if result == nil then
        result = self.mInstances[name]
    end
    return result
end
function tinyrush_ioc_container:ctor(...)
    self.mInstances = {}
    self.mInstancesWithClass = {}
end
function tinyrush_ioc_container:dispose()
    self.mInstances = nil
    self.mInstancesWithClass = nil
end
return tinyrush_ioc_container
