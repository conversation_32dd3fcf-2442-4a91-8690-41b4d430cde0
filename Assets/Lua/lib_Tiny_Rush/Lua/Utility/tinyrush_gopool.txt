---@class tinyrush_gopool : TRClass @GameObject对象池
local goPool = TinyRush_CreateClass("tinyrush_gopool")
goPool.poolTable = nil  -- 物体池
goPool.poolTableCount = nil
goPool.cacheTable = nil -- 已取出的物体
goPool.cacheTableCount = nil
goPool.poolParent = nil
goPool.strFileName = nil
goPool.prefab = nil
---@type boolean 是否绑定了Lua脚本
goPool.bindFlag = nil
---@type boolean 不需要再SetParent,只在实例化的时候设置一次
goPool.noSetParent = nil
---@type boolean 拿出放入时设置物体激活状态
goPool.setActive = nil

-- 新建对象
function goPool:allocate(go, ...)
    return require(self.strFileName).new(go, ...)
end

--- 是否绑定了Lua脚本
function goPool:isBindLua()
    return type(self.strFileName) == "string"
end

--- 预加载
---@param count number @预加载数量
function goPool:preload(count, ...)
    for i = 1, count, 1 do
        local go = bc_CS_GameObject.Instantiate(self.prefab, self.poolParent)
        if self.bindFlag then
            self.poolTable[i] = self:allocate(go, ...)
        else
            self.poolTable[i] = go
        end
        if self.setActive then
            go:SetActive(false)
        end
    end
    self.poolTableCount = count
end

--- 从池里去除一个目标
---@return tinyrush_gopoolitem
function goPool:popOne()
    local result = nil
    if self.poolTableCount > 0 then
        result = table.remove(self.poolTable, self.poolTableCount)
        self.poolTableCount = self.poolTableCount - 1
    else
        if self.noSetParent then
            result = bc_CS_GameObject.Instantiate(self.prefab, self.poolParent)
        else
            result = bc_CS_GameObject.Instantiate(self.prefab)
        end
        if self:isBindLua() then
            result = self:allocate(result)
        end
    end
    if self.setActive then
        result.gameObject:SetActive(true)
    end
    self.cacheTableCount = self.cacheTableCount + 1
    self.cacheTable[self.cacheTableCount] = result
    return result
end

--- 装回到池内
---@param item tinyrush_gopoolitem
function goPool:pushOne(item)
    if self.bindFlag then
        item = item.__entity
    end
    local cacheIndex = nil
    for i = 1, self.cacheTableCount, 1 do
        if item == self.cacheTable[i] then
            cacheIndex = i
            break
        end
    end
    if self.bindFlag then
        item:recycle()
    end
    if self.setActive then
        item.gameObject:SetActive(false)
    end
    if not self.noSetParent then
        item.transform:SetParent(self.poolParent)
    end
    self.cacheTableCount = self.cacheTableCount - 1
    table.remove(self.cacheTable, cacheIndex)
    self.poolTableCount = self.poolTableCount + 1
    self.poolTable[self.poolTableCount] = item
end

--- 清空已用列表，全回到池内
function goPool:clear()
    if self.cacheTableCount > 0 then
        for _, value in ipairs(self.cacheTable) do
            if self.bindFlag then
                value:recycle()
            end
            if self.setActive then
                value.gameObject:SetActive(false)
            end
            if not self.noSetParent then
                value.transform:SetParent(self.poolParent)
            end
            self.poolTableCount = self.poolTableCount + 1
            self.poolTable[self.poolTableCount] = value
        end
        self.cacheTable = {}
        self.cacheTableCount = 0
    end
end

--- 构造
---@param poolRoot table @GameObject 池父物体
---@param prefab table @GameObject 预制
---@param strFileName string @可以绑定lua脚本,继承 tinyrush_gopoolitem
---@param noSetParent boolean 不需要SetParent,只在实例化的时候设置一次
---@param setAtc boolean 拿出放入时设置物体激活状态
function goPool:ctor(poolRoot, prefab, strFileName, noSetParent, setAtc)
    self.poolTable = {}
    self.cacheTable = {}
    self.poolTableCount = 0
    self.cacheTableCount = 0
    self.strFileName = strFileName
    self.poolParent = poolRoot.transform
    self.prefab = prefab
    self.bindFlag = self:isBindLua()
    self.noSetParent = noSetParent or false
    self.setActive = setAtc or false
end

--- 析构
function goPool:dispose()
    if self.cacheTable ~= nil and self.cacheTableCount > 0 then
        for i = 1, self.cacheTableCount, 1 do
            if self.bindFlag then
                self.cacheTable[i]:dispose()
            else
                bc_CS_GameObject.Destroy(self.cacheTable[i].gameObject)
            end
        end
    end
    if self.poolTable ~= nil and self.poolTableCount > 0 then
        for i = 1, self.poolTableCount, 1 do
            if self.bindFlag then
                self.poolTable[i]:dispose()
            else
                bc_CS_GameObject.Destroy(self.poolTable[i].gameObject)
            end
        end
    end
    self.poolTable = nil
    self.cacheTable = nil
    self.poolParent = nil
    self.strFileName = nil
    self.prefab = nil
end

return goPool
