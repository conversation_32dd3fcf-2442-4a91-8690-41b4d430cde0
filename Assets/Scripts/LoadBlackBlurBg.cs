using System.Collections;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.UI;
public class LoadBlackBlurBg : MonoBehaviour
{
    public Camera uiCamera;

    public RawImage image;

    public Color color;
    void Awake()
    {
        if(uiCamera == null)
        {
            GameObject cameraObj = GameObject.Find("UIRoot/UICamera");
            uiCamera = cameraObj.GetComponent<Camera>();
        }
        image = this.gameObject.GetComponent<RawImage>();
    }
    void OnEnable()
    {
        if(uiCamera == null)
        {
            GameObject cameraObj = GameObject.Find("UIRoot/UICamera");
            uiCamera = cameraObj.GetComponent<Camera>();
            Debug.LogError("摄像机为空，获取摄像机:" + uiCamera.name);
        }
        OnRenderingBlackBlurBgByCPU();
    }
    public void OnRenderingBlackBlurBgByCPU()
    {
        int x = 0, y = 0 ,width = Screen.width, height = Screen.height;
        RenderTexture rt = RenderTexture.GetTemporary(Screen.width,Screen.height,24);
        uiCamera.targetTexture = rt;
        uiCamera.Render();
        Texture2D t2d = new Texture2D(rt.width, rt.height, TextureFormat.RGB24, false);
        var oldRT = RenderTexture.active;
        RenderTexture.active = rt;
        t2d.ReadPixels(new Rect(x, y, Screen.width,  Screen.height), 0, 0, false);
        t2d.Apply();
        RenderTexture.active = oldRT;
        uiCamera.targetTexture = null;
        image.texture = t2d;
        RenderTexture.ReleaseTemporary(rt);
        t2d = null;
        //将图片置灰
        image.color = color;
    }
    public static Texture2D ScaleTexture(Texture2D source, float targetWidth, float targetHeight)
    {
        Texture2D result = new Texture2D((int)targetWidth, (int)targetHeight, source.format, false);
 
        float incX = (1.0f / targetWidth);
        float incY = (1.0f / targetHeight);
 
        for (int i = 0; i < result.height; ++i)
        {
            for (int j = 0; j < result.width; ++j)
            {
                Color newColor = source.GetPixelBilinear((float)j / (float)result.width, (float)i / (float)result.height);
                result.SetPixel(j, i, newColor);
            }
        }
 
        result.Apply();
        return result;
    }
    private IEnumerator OnRenderingBlackBlurBg()
    {
        yield return new WaitForEndOfFrame();
        Texture2D texture = ScreenCapture.CaptureScreenshotAsTexture();
        //读取纹理像素输出到tex2D上
        image.texture = texture;
        //将图片置灰
        image.color = color;
    }
    IEnumerator OnRenderingBlackBlurBgByGPU()
    {
        int x = 0, y = 0 ,width = Screen.width, height = Screen.height;
        RenderTexture rt = RenderTexture.GetTemporary(Screen.width,Screen.height,24);
        uiCamera.targetTexture = rt;
        uiCamera.Render();
        Texture2D t2d = new Texture2D(rt.width, rt.height, TextureFormat.RGB24, false);
        if(SystemInfo.supportsAsyncGPUReadback)
        {
            AsyncGPUReadbackRequest req = AsyncGPUReadback.Request(rt, 0, x, width, y, height, 0, 1, TextureFormat.RGBA32);
            while(!req.done)
            {
                yield return null;
            }
            if(!req.hasError)
            {
                var colorArray = req.GetData<Color32>().ToArray();
                t2d.SetPixels32(colorArray);
                t2d.Apply();
            }
            else
            {
                Debug.LogError("Error AsyncGPUReadbackRequest.hasError");
            }
        }
        else
        {
            var oldRT = RenderTexture.active;
            RenderTexture.active = rt;
            t2d.ReadPixels(new Rect(x, y, Screen.width,  Screen.height), 0, 0, false);
            t2d.Apply();
            RenderTexture.active = oldRT;
        }
        uiCamera.targetTexture = null;
        image.texture = t2d;
        RenderTexture.ReleaseTemporary(rt);
        t2d = null;
        //将图片置灰
        image.color = color;
    }
}
