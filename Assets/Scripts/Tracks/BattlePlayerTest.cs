using System;
using System.Collections.Generic;
using UnityEngine;

namespace War.Battle
{
#if UNITY_EDITOR
    public class BattlePlayerTest : MonoBehaviour
    {
        public BattlePlayer player = null;
        public BattleTracksBinding binding = null;

        private Dictionary<int, ActorData> data;


        string common_atk = "art/skill/common/common_atk.playable";
        string common_hit = "art/skill/common/common_hit.playable";
        string common_die = "art/skill/common/common_die.playable";


        private void Awake()
        {
            data = InitializeActorData();

            if (player == null)
                player = GetComponent<BattlePlayer>();

            if (player == null)
                player = FindObjectOfType<BattlePlayer>();

            binding = BattleAssistant.trackbinding;

            BattleEvent.OnLoadingBegin += OnLoadingBegin;
            BattleEvent.OnLoadingCompleted += OnLoadingCompolete;
            BattleEvent.OnLoadingProgressChanged += OnLoadingProgressChanged;
            BattleEvent.OnBattleEvent += OnBattleEvent;
        }

        private void Start()
        {
            //Test1();
            RandomTest(5);
        }


        void OnLoadingBegin()
        {

        }

        void OnLoadingProgressChanged(float progress)
        {

        }

        void OnLoadingCompolete()
        {
            player.Play();
        }

        void OnBattleEvent(string evt)
        {
            //if (evt == "BattleEnd")
            //{
            //    player.ClearBattle();
            //    player.Unload();
            //}
        }

        void RandomTest(int round)
        {
            player.BattleBegin();

            ActorData actor = data[16];
            string model = actor.res;

            player.RegisterActor(1, 0, data[70].res);
            player.RegisterActor(2, 1, data[70].res);
            player.RegisterActor(3, 2, data[70].res);
            player.RegisterActor(4, 3, data[70].res);
            player.RegisterActor(5, 4, data[70].res);
            player.RegisterActor(6, 5, data[70].res);
            player.RegisterActor(7, 6, data[70].res);
            player.RegisterActor(8, 7, data[70].res);
            player.RegisterActor(9, 8, data[70].res);
            player.RegisterActor(10, 9, data[70].res);
            player.RegisterActor(11, 10, data[70].res);
            player.RegisterActor(12, 11, data[70].res);

            player.SetActorHudData(1, 100, 50, 100, 100, 100, "youan", false, 1,false);
            player.SetActorHudData(2, 100, 50, 100, 100, 100, "youan", false, 1, false);
            player.SetActorHudData(3, 100, 50, 100, 100, 100, "youan", false, 1, false);
            player.SetActorHudData(4, 100, 50, 100, 100, 100, "youan", false, 1, false);
            player.SetActorHudData(5, 100, 50, 100, 100, 100, "youan", false, 1, false);
            player.SetActorHudData(6, 100, 50, 100, 100, 100, "youan", false, 1, false);
            player.SetActorHudData(7, 100, 50, 100, 100, 100, "youan", false, 1, false);
            player.SetActorHudData(8, 100, 50, 100, 100, 100, "youan", false, 1, false);
            player.SetActorHudData(9, 100, 50, 100, 100, 100, "youan", false, 1, false);
            player.SetActorHudData(10, 100, 50, 100, 100, 100, "youan", false, 1, false);
            player.SetActorHudData(11, 100, 50, 100, 100, 100, "youan", false, 1, false);
            player.SetActorHudData(12, 100, 50, 100, 100, 100, "youan", false, 1, false);


            //player.BeginSkill(1, data[1].attack);
            //player.AppendAction(7, common_hit);
            //player.AppendAddSustainedBuffFx(7, 1, "art/effects/prefabs/characters_common/buff_bingdong.prefab");
            //player.EndSkill();

            List<uint> targets = new List<uint>();

            for (uint i = 1; i < 6; i++)
            {
                targets.Clear();
                targets.Add(7);

                player.BeginSkill(i, targets, data[(int)i].skill);
                player.AppendAction(7, common_hit);
                player.AppendFloatText(7, "normal", "-50", 0, 0, 0);
                player.AppendHud(7, -5, 0, 10);
                player.AppendHud(i, 0, 0, 50);
                //player.AppendBuffFx(12, "art/effects/prefabs/characters_effects/ef_pg_baozha.prefab");
                player.EndSkill();
            }

            for (uint i = 1; i < 4; i++)
            {
                player.BeginSkill(i, targets, data[(int)i].skill);
                player.AppendAction(7, common_hit);
                player.AppendFloatText(7, "normal", "-50", 0, 0, 0);
                player.AppendHud(7, -25, 0, 10);
                player.AppendHud(i, 0, 0, 50);
                //player.AppendBuffFx(12, "art/effects/prefabs/characters_effects/ef_pg_baozha.prefab");
                player.EndSkill();
            }

            player.BattleEnd();
            player.Load();
        }

        void Unload()
        {
            player.Unload();
            player.ClearBattle();
        }

        public void Replay()
        {
            for (int i = 0; i < 12; ++i)
            {
                Role role = player.GetRole((uint)i + 1, false);
                if (role != null)
                {
                    role.node.card.gameObject.SetActive(true);
                    if (role.node.Hud)
                    {
                        role.node.Hud.ResetHud();
                        role.node.Hud.gameObject.SetActive(true);
                    }
                }
            }
            player.Replay();
        }

        public class ActorData
        {
            public int modelId;
            public int heroId;
            public string name;
            public string res;
            public string attack;
            public string skill;


            public ActorData(int modelId, int heroId, string name)
            {
                this.modelId = modelId;
                this.heroId = heroId;
                this.name = name;

                res = string.Format("animations/characters/{0:D2}_{1:D2}/edit_{0:D2}_{1:D2}.prefab", modelId, name);
                attack = string.Format("art/skill/{0}_{1}/{0}_{1}_attack.playable", heroId, name);
                skill = string.Format("art/skill/{0}_{1}/{0}_{1}_skill.playable", heroId, name);
            }
        }


        public Dictionary<int, ActorData> InitializeActorData()
        {
            Dictionary<int, ActorData> data = new Dictionary<int, ActorData>();
            data[1] = new ActorData(1, 6, "xingqiwuxiansheng");
            data[2] = new ActorData(2, 96, "tiankongzhishen");
            data[3] = new ActorData(3, 79, "chuziyingxiong");
            data[4] = new ActorData(4, 55, "t-2080");
            data[5] = new ActorData(5, 84, "hundun");
            data[6] = new ActorData(6, 62, "pikemao");
            data[7] = new ActorData(7, 63, "cibaozhanxiong");
            data[8] = new ActorData(8, 83, "zhixu");
            data[9] = new ActorData(9, 3, "shenghualangren");
            data[10] = new ActorData(10, 54, "lingshi");
            data[11] = new ActorData(11, 7, "kongjubenyuan");
            data[13] = new ActorData(13, 50, "xingjiyouxia");
            data[16] = new ActorData(16, 59, "saibojianxin");
            data[17] = new ActorData(17, 34, "shikongzhizhe");
            data[25] = new ActorData(25, 33, "meiguoxiaojie");
            data[35] = new ActorData(35, 2, "baozoushaonv");
            data[71] = new ActorData(71, 0, "71_haiyangzhishen");
            data[70] = new ActorData(70, 82, "shang");
            return data;
        }


    }
#endif
}
