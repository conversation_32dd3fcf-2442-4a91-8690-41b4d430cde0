using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;

[Serializable]
public class CardSkillBehaviour : PlayableBehaviour
{
    [NonSerialized]
    public SkillEffectConfig config;

    private bool isInit = true;

    public override void OnBehaviourPlay(Playable playable, FrameData info)
    {
        isInit = true;
    }
    public override void ProcessFrame(Playable playable, FrameData info, object playerData)
    {
        if (isInit && config && config.baseNode && config.baseNode.card)
        {
            isInit = false;
            config.baseNode.card.LoadStandEffect();
        }
    }
    
    public override void OnGraphStop(Playable playable)
    {
        if (config)
        {
            config.SetWaitShowEffect(false);
        }
    }
}
