using System;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;

[TrackColor(0.159f, 0.588f, 0.785f)]
[TrackClipType(typeof(CardSkillClip))]
[TrackBindingType(typeof(BattleActorNode))]

public class CardSkillTrack : TrackAsset
{
    public override Playable CreateTrackMixer(PlayableGraph graph, GameObject go, int inputCount)
    {
        PlayableDirector director = go.GetComponent<PlayableDirector>();

        foreach (TimelineClip clip in GetClips())
        {
            CardSkillClip playableAsset = clip.asset as CardSkillClip;

            if (playableAsset)
            {
                playableAsset.config = go.GetComponent<SkillEffectConfig>();
                if (playableAsset.config)
                    playableAsset.config.SetWaitShowEffect(true);
            }
        }
        return ScriptPlayable<DefaultMixerBehaviour>.Create(graph, inputCount);
    }
}
