using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;

[Serializable]
class TransformAnimationClip : PlayableAsset, ITimelineClipAsset
{
    public TransformAnimationBehaviour template = new TransformAnimationBehaviour();

    [NonSerialized]
    public BattleActorNode node = null;

    public ClipCaps clipCaps
    {
        get { return ClipCaps.None; }
    }

    public override Playable CreatePlayable(PlayableGraph graph, GameObject owner)
    {
        var playable = ScriptPlayable<TransformAnimationBehaviour>.Create(graph, template);
        TransformAnimationBehaviour behaviour = playable.GetBehaviour();
        behaviour.node = node;
        return playable;
    }
}