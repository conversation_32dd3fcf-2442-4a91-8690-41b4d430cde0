using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;

[Serializable]
class CameraShakeClip : PlayableAsset, ITimelineClipAsset
{
    public CameraShakeBehaviour template = new CameraShakeBehaviour();

    [NonSerialized]
    public Animator animator = null;

    public ClipCaps clipCaps
    {
        get { return ClipCaps.None; }
    }

    public override Playable CreatePlayable(PlayableGraph graph, GameObject owner)
    {
        var playable = ScriptPlayable<CameraShakeBehaviour>.Create(graph, template);
        CameraShakeBehaviour behaviour = playable.GetBehaviour();
        behaviour.animator = animator;
        return playable;
    }
}
