using System;
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.Timeline;

[Serializable]
public class ShrinkClip : PlayableAsset, ITimelineClipAsset
{
    public ShrinkBehaviour template = new ShrinkBehaviour();

    public ClipCaps clipCaps
    {
        get { return ClipCaps.None; }
    }

    public override Playable CreatePlayable(PlayableGraph graph, GameObject owner)
    {
        var playable = ScriptPlayable<ShrinkBehaviour>.Create(graph, template);
        //ShrinkBehaviour clone = playable.GetBehaviour();
        return playable;
    }
}
