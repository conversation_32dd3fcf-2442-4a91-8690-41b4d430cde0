using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering;


public class StencilRenderer
{
    public Renderer renderer;
    public List<StencilParameter> stencilParameters;

    public void SetStencil(int stencilID, CompareFunction compareFunction, StencilOp stencilOp = StencilOp.Keep)
    {
        if(renderer == null)
        {
            return;
        }
        SetMaterialStencil(renderer.materials, stencilID, compareFunction, stencilOp);
    }

    void SetMaterialStencil(Material[] materials, int stencilID, CompareFunction compareFunction, StencilOp stencilOp = StencilOp.Keep)
    {
        if(materials == null)
        {
            return;
        }
        StencilParameter stencilParameter;

        int count = materials.Length;
        for (int i = 0; i < count; i++)
        {
            stencilParameter = stencilParameters[i];
            stencilParameter.SetMaterialStencil(materials[i], stencilID, compareFunction, stencilOp);
        }
    }

    public void ResetStencil()
    {
        if (renderer == null)
        {
            return;
        }
        var materials = renderer.materials;
        if (materials == null)
        {
            return;
        }
        StencilParameter stencilParameter;

        int count = materials.Length;
        for (int i = 0; i < count; i++)
        {
            stencilParameter = stencilParameters[i];
            stencilParameter.SetMaterialStencil(materials[i], stencilParameter.orgStencilID, stencilParameter.orgCompareFunction, stencilParameter.orgStencilOp);
        }
    }

    public void Clear()
    {
        ResetStencil();

        renderer = null;
        stencilParameters.Clear();
    }
}
