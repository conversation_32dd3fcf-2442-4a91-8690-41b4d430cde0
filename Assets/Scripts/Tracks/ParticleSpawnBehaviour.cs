using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;

[Serializable]
public class ParticleSpawnBehaviour : PlayableBehaviour
{
    public GameObject prefab;
    [NonSerialized]
    public Transform parent;
    [NonSerialized]
    public List<Transform> parents = null;
    public Vector3 offset;

    [NonSerialized]
    public List<ParticleSystemDriver> drivers = null;

    public override void OnGraphStart(Playable playable)
    {
        if (parents != null && parents.Count != 0)
        {
            foreach(Transform parent in parents)
            {
                ParticleSystemDriver driver = new ParticleSystemDriver(prefab);
                driver.Attach(parent, offset);
                if (Application.isPlaying)
                {
                    driver.PrepareParticleSystem((float)playable.GetDuration());
                }
                drivers.Add(driver);
            }
        }
        else if (parent != null)
        {
            ParticleSystemDriver driver = new ParticleSystemDriver(prefab);
            driver.Attach(parent, offset);
            if (Application.isPlaying)
            {
                driver.PrepareParticleSystem((float)playable.GetDuration());
            }
            drivers.Add(driver);
        }
    }

    public override void OnGraphStop(Playable playable)
    {
        foreach (ParticleSystemDriver driver in drivers)
            driver.Destroy();

        drivers.Clear();
    }

    public override void OnBehaviourPause(Playable playable, FrameData info)
    {
        foreach (ParticleSystemDriver driver in drivers)
            driver.Clear();
    }

    public override void ProcessFrame(Playable playable, FrameData info, object playerData)
    {
        float time = (float)playable.GetTime();
        float duration = (float)playable.GetDuration();

        foreach (ParticleSystemDriver driver in drivers)
            driver.Simulate(time, duration);
    }

    public override void OnBehaviourPlay(Playable playable, FrameData info)
    {
        foreach (ParticleSystemDriver driver in drivers)
            driver.Play();
    }
}
