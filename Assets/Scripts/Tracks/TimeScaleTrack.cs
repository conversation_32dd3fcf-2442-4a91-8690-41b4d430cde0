using System;
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.Timeline;
using War.Battle;

[TrackColor(0.1f, 0.8f, 1f)]
[TrackClipType(typeof(TimeScaleClip))]
//[TrackBindingType(typeof(Episode))]
public class TimeScaleTrack : TrackAsset
{
    public override Playable CreateTrackMixer(PlayableGraph graph, GameObject go, int inputCount)
    {
        var scriptPlayable = ScriptPlayable<DefaultMixerBehaviour>.Create(graph, inputCount);
        return scriptPlayable;
    }
}
