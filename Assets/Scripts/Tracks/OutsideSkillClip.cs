using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.Timeline;
using War.Battle;
[Serializable]
public class OutsideSkillClip : PlayableAsset, ITimelineClipAsset
{
    public OutsideSkillBehaviour template = new OutsideSkillBehaviour();
    public GameObject SkillObjectNormal;
    public GameObject SkillObjectQuick;
    [Header("����")]
    public bool testMaxSpeed;//���ڷ�����ԣ��������ǹ�����ʹ��
    [NonSerialized]
    public BattleOutsideSkillManager manager;
    [NonSerialized]
    public BattlePlayer player;
    public ClipCaps clipCaps
    {
        get { return ClipCaps.None; }
    }

    public override Playable CreatePlayable(PlayableGraph graph, GameObject owner)
    {
        var playable = ScriptPlayable<OutsideSkillBehaviour>.Create(graph, template);
        OutsideSkillBehaviour clone = playable.GetBehaviour();
        clone.manager = manager;
        clone.skillObject = SkillObjectQuick == null
            ? SkillObjectNormal
            : SkillObjectNormal == null ? SkillObjectQuick : (player.isMaxSpeed || testMaxSpeed) ? SkillObjectQuick : SkillObjectNormal;

        double clipduration = clone.GetEndTiming();

        Debug.Log($"CurDuration={playable.GetDuration()}��NewDuration={clipduration}");
        playable.SetDuration(clipduration);

        playable.SetTime(clipduration);

        return playable;
    }
}
