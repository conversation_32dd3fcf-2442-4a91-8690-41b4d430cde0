using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Playables;
using War.Battle;

// A behaviour that is attached to a playable
[System.Serializable]
public class PlayableColorBehaviour : PlayableBehaviour
{
    public Color sourceColor = Color.white;
    public Color destColor = new Color(1, 1, 1, 0);
    public AnimationCurve curve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f);

    [System.NonSerialized]
    public GameObject go;
    [System.NonSerialized]
    public Card card;

    public List<Material> psMat = null;

    int proprty_color = Shader.PropertyToID("_Color");
    int property_tint = Shader.PropertyToID("_TintColor");

    // Called when the owning graph starts playing
    public override void OnGraphStart(Playable playable)
    {
        if (go)
        {
            card = go.GetComponent<Card>();
            if (card && Application.isPlaying == false)
            {
                card.Initialize();

                psMat = new List<Material>();
                if (Application.isPlaying)
                {
                    foreach (ParticleSystem ps in card.GetComponentsInChildren<ParticleSystem>())
                    {
                        ParticleSystemRenderer renderer = ps.GetComponent<ParticleSystemRenderer>();
                        {
                            Material mat = renderer.material;
                            psMat.Add(mat);
                        }
                    }
                }
                else
                {
                    foreach (ParticleSystem ps in card.GetComponentsInChildren<ParticleSystem>())
                    {
                        ParticleSystemRenderer renderer = ps.GetComponent<ParticleSystemRenderer>();
                        if (renderer && renderer.sharedMaterial)
                        {
                            //renderer.sharedMaterial = new Material(renderer.sharedMaterial);
                            psMat.Add(renderer.sharedMaterial);
                            renderer.sharedMaterial.hideFlags |= HideFlags.DontSave;
                        }
                    }
                }
            
            }
        }
    }

    // Called when the owning graph stops playing
    public override void OnGraphStop(Playable playable)
    {
        if (psMat == null)
            return;

        if (Application.isPlaying)
        {
            foreach (Material mat in psMat)
            {
                Material.Destroy(mat);
            }
        }
        else
        {
            foreach (Material mat in psMat)
            {
                if (mat.HasProperty(proprty_color))
                {
                    Color c = mat.GetColor(proprty_color);
                    c.a = 1;
                    mat.SetColor(proprty_color, c);
                }
                else if (mat.HasProperty(property_tint))
                {
                    Color c = mat.GetColor(property_tint);
                    c.a = 1;
                    mat.SetColor(property_tint, c);
                }
                mat.hideFlags &= ~HideFlags.DontSave;
            }
        }
    
        psMat = null;
    }

    // Called when the state of the playable is set to Play
    public override void OnBehaviourPlay(Playable playable, FrameData info)
    {

    }

    // Called when the state of the playable is set to Paused
    public override void OnBehaviourPause(Playable playable, FrameData info)
    {

    }

    // Called each frame while the state is set to Play
    public override void PrepareFrame(Playable playable, FrameData info)
    {
        if (card)
        {
            float time = (float)playable.GetTime() / (float)playable.GetDuration();
            Color color = Color.Lerp(sourceColor, destColor, curve.Evaluate(time));
            card.SetSpriteColor(color, -1);

       

            foreach (Material mat in psMat)
            {
                if (mat.HasProperty(proprty_color))
                {
                    Color c = mat.GetColor(proprty_color);
                    c.a = color.a;
                    mat.SetColor(proprty_color, c);
                }
                else if (mat.HasProperty(property_tint))
                {
                    Color c = mat.GetColor(property_tint);
                    c.a = color.a;
                    mat.SetColor(property_tint, c);
                }
            }
        }
    }
}
