using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Playables;
using War.Base;
using War.Battle;

// A behaviour that is attached to a playable
[System.Serializable]
public class PlayablePrefabBehaviour : PlayableBehaviour
{
    public GameObject prefab;
    public GameObject source;

    [System.NonSerialized]
    public GenericObjectPool.GameObjectInfo instance;
  
    // Called when the owning graph stops playing
    public override void OnGraphStop(Playable playable)
    {
        if (instance != null)
        {
            GameObjectPool.ReleasePrefabPoolGameObject(PoolType.ParticleSystemDriver, instance);
            instance = null;
        }
    }

    // Called when the state of the playable is set to Play
    public override void OnBehaviourPlay(Playable playable, FrameData info)
    {
        if (prefab != null)
        {
            Transform parent = source != null ? source.transform : null;
            instance = GameObjectPool.GetPrefabPoolGameObject(prefab, PoolType.ParticleSystemDriver, parent, false);
        }
    }
}
