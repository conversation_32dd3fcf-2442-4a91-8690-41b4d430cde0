using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Playables;
using War.Battle;

[System.Serializable]
public class PlayablePrefabAsset : PlayableAsset
{
    public PlayablePrefabBehaviour template = new PlayablePrefabBehaviour();
    public ExposedReference<GameObject> source;


    // Factory method that generates a playable based on this asset
    public override Playable CreatePlayable(PlayableGraph graph, GameObject go)
    {
        //PlayableColorBehaviour behaviour = new PlayableColorBehaviour();
        //behaviour.card = card.Resolve(graph.GetResolver());
        //return ScriptPlayable<PlayableColorBehaviour>.Create(graph, behaviour);

        var playable = ScriptPlayable<PlayablePrefabBehaviour>.Create(graph, template);
        PlayablePrefabBehaviour behaviour = playable.GetBehaviour();
        behaviour.source = source.Resolve(graph.GetResolver());
        return playable;
    }
}
