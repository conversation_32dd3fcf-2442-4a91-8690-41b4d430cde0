using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Playables;
using War.Battle;

[System.Serializable]
public class PlayableColorAsset : PlayableAsset
{
    public PlayableColorBehaviour template = new PlayableColorBehaviour();
    public ExposedReference<GameObject> card;


    // Factory method that generates a playable based on this asset
    public override Playable CreatePlayable(PlayableGraph graph, GameObject go)
    {
        //PlayableColorBehaviour behaviour = new PlayableColorBehaviour();
        //behaviour.card = card.Resolve(graph.GetResolver());
        //return ScriptPlayable<PlayableColorBehaviour>.Create(graph, behaviour);

        var playable = ScriptPlayable<PlayableColorBehaviour>.Create(graph, template);
        PlayableColorBehaviour behaviour = playable.GetBehaviour();
        behaviour.go = card.Resolve(graph.GetResolver());
        return playable;
    }
}
