/****************************************************
 *  Copyright © 2018-2022 冰川网络  All rights reserved.
 *  文件：FixedBugMenu.cs
 *  作者：wjy
 *  日期：2023-04-05
 *  功能：
*****************************************************/

#if UNITY_EDITOR

using System.IO;
using UnityEditor;
using UnityEngine;
using War.Base;

namespace MGameToolKit.Editor
{
    public class FixedBugMenu
    {
        public const string MenuRootPath = "Assets/MGameToolKit/FixedBug/";
        public const int MenuPriority = 40;


        [MenuItem(MenuRootPath + "UpdateScene", false, MenuPriority)]
        public static void UpdateScene()
        {
            CreateFileByStr("Windows");
            CreateFileByStr("Android");
        }

        public static void CreateFileByStr(string platform)
        {
            string root = Application.streamingAssetsPath;
            string dir = Path.Combine(root, "AssetBundles");
            dir = Path.Combine(dir, platform);
            Debug.LogWarning(dir);
            if (!Directory.Exists(dir))
            {
                Directory.CreateDirectory(dir);
            }
            string fileName = Path.Combine(dir, "update.json");
            if (!File.Exists(fileName))
            {
                string valaue = "{\"reviewing_server_info_url\":\"https://update1.q1.com/marvelous/marvelous_update/test3/reviewing_server_info/server_info.json\",\"use_inner_files\":\"true\",\"reviewing_version\":-1,\"downdload_url\":\"http://**************:3122/zHero_android_For_Mgame_001/Android/hero.1.0.1.apk\",\"resource_ver\":630,\"channelName\":\"zHero_android_For_Mgame_001\",\"files_url\":\"http://**************:3122/zHero_android_For_Mgame_001/Android/resource/630/files.txt\",\"version\":636,\"patch_url\":\"http://**************:3122/zHero_android_For_Mgame_001/Android/patch/$version/patch.json\",\"kb_size\":\"148986\",\"update_urls\":[\"http://**************:3122/zHero_android_For_Mgame_001/Android/update.json\",\"http://**************:3122/zHero_android_For_Mgame_001/Android/update.json\"],\"must_reinstall_versions\":[\"1.0.0\"],\"resource_url\":\"http://**************:3122/zHero_android_For_Mgame_001/Android/resource/$file_ver/\",\"kb_size\":\"148986\",\"update_url\":\"http://**************:3122/zHero_android_For_Mgame_001/Android/update.json\",\"server_info_url\":\"http://**************:3122/zHero_android_For_Mgame_001/Android/server_info.json\",\"patch_zip\":[7769025]}";
                File.WriteAllText(fileName, valaue);
            }
            
            string fileName2 = Path.Combine(dir, "package.json");
            if (!File.Exists(fileName2))
            {
                string valaue = "{\"useMiniGameGuide\":true,\"openMiniGame\":true,\"openGameLobby\":false,\"use_specific_updatejson\":false,\"specific_updatejson\":\"\",\"table_mark\":\"cn\",\"bSockPackage\":false,\"table\":\"\",\"enableEncryptionNet\":true,\"enableNewRoleRewards\":false,\"enableGarbleAbPath\":false,\"curExternalVersion\":\"2023-11-06 14:58:52\",\"p_GuideGroupID\":\"0\",\"p_stand_alone_res_key\":\"SaveDog\",\"p_stand_alone_server_root_url\":\"http://************:8001/CasualGame_Pure_Res_105_002/TinyRes/\",\"p_ABTestID\":\"0\"}";
                File.WriteAllText(fileName2, valaue);
            }
            AssetDatabase.Refresh();
        }


    }

}
#endif