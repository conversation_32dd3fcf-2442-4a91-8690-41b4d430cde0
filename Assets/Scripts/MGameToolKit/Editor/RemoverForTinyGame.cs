/****************************************************
 *  Copyright © 2018-2023 冰川网络  All rights reserved.
 *  文件：RemoverForTinyGame.cs
 *  作者：wjy
 *  日期：2023-02-10
 *  功能：提供删除小游戏资源的功能（解决unity编辑器在打apk阶段卡在il2cpp缓存超过1G报错中断的问题）
*****************************************************/

#if UNITY_EDITOR

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Newtonsoft.Json;
using Sirenix.Utilities;
using UnityEditor;
using UnityEngine;

namespace War.Base
{
    public class RemoverForTinyGame
    {
        private const bool DataSimulate = false;

        /// <summary>
        /// 是否只删除小游戏的csharp脚本
        /// </summary>
        public const string JenkinsDeleteTypeString = "only_delete_tiny_game_csharp";

        /// <summary>
        /// 小游戏的reskey
        /// </summary>
        public const string JenkinsDeleteResKeys = "delete_res_keys_raw";

        /// <summary>
        /// 是否使用工程内的文件来作为删除的参数,跟JenkinsDeleteResKeys参数二选一，jenkins工程有勾选using_project_file_2_delete，则delete_res_keys_raws会失效
        /// </summary>
        public const string JenkinsUsingProjectFile2Delete = "using_project_file_2_delete";

        [MenuItem("Assets/MGameToolKit/RemoveTinyGameResAndRefreshEditMenu")]
        public static void RemoveTinyGameFormConfig()
        {
            // 1.读key2mark，
            var key2MarkData = ReadKey2MarkExceptAllMark();
            // 2.读删除列表,
            var deleteResKeys = DeleteResKeysConfig().ToList();
            ////3.获取工程的CSharp数
            //int csCount = GetAllCSharpNum();
            //int astrict = 12000;
            //Debug.Log("==总脚本数="+ csCount);
            //Debug.Log("==限制脚本数="+ astrict);
            //if (csCount> astrict)
            //{
            //    Debug.Log("==超出限定脚本数=");
            //    int configCSNum = 0;
            //    //获取默认删除列表
            //    foreach (var item in deleteResKeys)
            //    {
            //        string path = "Assets/CasualGame" + item;
            //        string [] fs = Directory.GetFiles(path, "*.cs", SearchOption.AllDirectories);
            //        if (fs!=null)
            //        {
            //            configCSNum += fs.Length;
            //        }
            //    }
            //    Debug.Log("==删除配置的游戏后，总脚本数为="+(csCount - configCSNum));
            //    //默认删除列表的脚本数不足，在key2mark添加
            //    while (csCount - configCSNum> astrict)
            //    {
            //        foreach (var item in key2MarkData)
            //        {
            //            string path = "Assets/CasualGame" + item.Key;
            //            string[] fs = Directory.GetFiles(path, "*.cs", SearchOption.AllDirectories);
            //            if (fs != null && !deleteResKeys.Contains(item.Key))
            //            {
            //                configCSNum += fs.Length;
            //                deleteResKeys.Add(item.Key);
            //                Debug.Log($"==添加删除小游戏={item.Key}，包含脚本数={fs.Length}");
            //            }
            //        }
            //    }
            //}




            if (deleteResKeys != null && deleteResKeys.Count > 0)
            {
                // 3.取出当前没删游戏的所有依赖的全集
                var staySets = GetDependence(key2MarkData, key2MarkData.Keys.Except(deleteResKeys).ToArray());
                // 4.取出当前要删依赖的全集
                var deleteSets = GetDependence(key2MarkData, deleteResKeys.ToArray());
                // Debug.LogWarning(string.Join("\r\n", staySets));
                // Debug.LogWarning(string.Join("\r\n", deleteSets));
                // 5.从要删的全集中剔除与步骤3的交集
                var finalDeleteSets = deleteSets.Except(staySets.Intersect(deleteSets));
                finalDeleteSets.Select((s) => !s.StartsWith("Common") && !s.StartsWith("lib_"));
                // Debug.LogWarning(string.Join("\r\n", finalDeleteSets));
                // 6.执行删除操作。
                RemoveTinySets(finalDeleteSets, true);
            }


        }
        
        [MenuItem("Assets/MGameToolKit/调试删除批量内置小游戏的csharp")]
        public static void DebugRemoveTinyGameCsharpFormProjectDeleteResKeysRaw()
        {
            // 1.读key2mark，
            var key2MarkData = ReadKey2MarkExceptAllMark();
            // 2.读删除列表,
            var deleteResKeys = ReadProjectDeleteResKeysRaw().ToList();

            if (deleteResKeys != null && deleteResKeys.Count > 0)
            {
                // 3.取出当前没删游戏的所有依赖的全集
                var staySets = GetDependence(key2MarkData, key2MarkData.Keys.Except(deleteResKeys).ToArray());
                // 4.取出当前要删依赖的全集
                var deleteSets = GetDependence(key2MarkData, deleteResKeys.ToArray());
                // Debug.LogWarning(string.Join("\r\n", staySets));
                // Debug.LogWarning(string.Join("\r\n", deleteSets));
                // 5.从要删的全集中剔除与步骤3的交集
                var finalDeleteSets = deleteSets.Except(staySets.Intersect(deleteSets));
                finalDeleteSets.Select((s) => !s.StartsWith("Common") && !s.StartsWith("lib_"));
                // Debug.LogWarning(string.Join("\r\n", finalDeleteSets));
                // 6.执行删除操作。
                RemoveTinySets(finalDeleteSets, true);
            }
        }


        // [MenuItem("TTTTTTTTTTTTTTTTTTTTT/RemoveTinyGameResBeforeBuildAPK")]
        public static void RemoveTinyGameResBeforeBuildAPK()
        {
            // 1.读key2mark，
            var key2MarkData = ReadKey2MarkExceptAllMark();
            // 2.读删除列表,
            var deleteResKeys = ReadDeleteResKeysFormJenkins();
            if (deleteResKeys != null && deleteResKeys.Length > 0)
            {
                // 3.取出当前没删游戏的所有依赖的全集
                var staySets = GetDependence(key2MarkData, key2MarkData.Keys.Except(deleteResKeys).ToArray());
                // 4.取出当前要删依赖的全集
                var deleteSets = GetDependence(key2MarkData, deleteResKeys);
                // Debug.LogWarning(string.Join("\r\n", staySets));
                // Debug.LogWarning(string.Join("\r\n", deleteSets));
                // 5.从要删的全集中剔除与步骤3的交集
                var finalDeleteSets = deleteSets.Except(staySets.Intersect(deleteSets));
                //finalDeleteSets.Select((s) => !s.StartsWith("Common") && !s.StartsWith("lib_"));
                // Debug.LogWarning(string.Join("\r\n", finalDeleteSets));
                // 6.执行删除操作。
                RemoveTinySets(finalDeleteSets, OnlyDeleteTinyCSharp());
            }
        }


        public static bool OnlyDeleteTinyCSharp()
        {
            if (DataSimulate)
            {
                return true;
            }

            return JenkinsEnv.Instance.GetBool(JenkinsDeleteTypeString, true);
        }

        public static string[] ReadProjectDeleteResKeysRaw()
        {
            string raw = @"Assets/CasualGame/delete_res_keys_raw.txt";
            var text = File.ReadAllText(raw);
            return AnalyzeFormKeysString(text);
        }

        public static Dictionary<string, string[]> ReadKey2MarkExceptAllMark()
        {
            string key2MarkFilePath = @"Assets/CasualGame/key2mark.json";
            var text = File.ReadAllText(key2MarkFilePath);
            // Debug.LogWarning($"text={text}");
            var raw = JsonConvert.DeserializeObject<Dictionary<string, string[]>>(text);
            if (raw.ContainsKey("allmarks"))
            {
                raw.Remove("allmarks");
            }

            return raw;
        }
        public static string[] DeleteResKeysConfig()
        {

             return new[] { 
                 "DrawPunch" ,
                 "RaccoonRescue",
                 "CastleRaid" ,
                 "StackLadder" ,
                 "BrickRunner" ,
                 "GoodMiner" ,
                 "LastPieces" ,
                 "NumberDuel" ,
                 "SharpShooter" ,
                 "LZPopBlock" ,
                 "LZDtz" ,
                 "BrushStroke" ,
                 "WeaponStorm" ,
                 "NinjaAssassin" ,
                 "StealthMaster" ,
                 "TinyFish" ,
                 "MasterAssassin",
                 "LZParkingMaster" ,
                 "Downthehole" ,
                 "AnimalBridge" ,
                 "PolyBridge" ,
                 "aquapark",
                 "CyBuildUp",
                 "CyDogeWorm",
                 "CyRescueMachine",
                 "CyPullThePin",
                 "CySpillIt",
                 "BallRun2048",
                 "DeliverIt3D",
                 "ZYSheJi",
                 "DLYMonster",
                 "Weigher",
                 "DLYShuiGuan",
                 "DLYJuJiJuRen",
                 "SaveTheFish",
                 "Helpcopter",
                 "RopeAndDemolish",
                 "OneStroke",
                 "WanMeiSheJi",
                 "BazookaBoy",
                 "TableFootball",
                 "JewelSancient"
             };

        }
        public static string[] ReadDeleteResKeysFormJenkins()
        {
            if (DataSimulate)
            {
                return new[] {"CrazyEraser"};
            }

            if (JenkinsEnv.Instance.GetBool(JenkinsUsingProjectFile2Delete, true))
            {
                return ReadProjectDeleteResKeysRaw();
            }
            else
            {
                var deleteResKeysRaw = JenkinsEnv.Instance.Get(JenkinsDeleteResKeys, string.Empty);
                return AnalyzeFormKeysString(deleteResKeysRaw);
            }
        }

        public static string[] AnalyzeFormKeysString(string rawString)
        {
            if (string.IsNullOrEmpty(rawString))
            {
                return null;
            }

            rawString = rawString.Replace("，", ",");
            rawString = rawString.Replace("\r\n", ",");
            var raw = rawString.Split(',');
            HashSet<string> final = new HashSet<string>();
            foreach (var s in raw)
            {
                if (!string.IsNullOrEmpty(s))
                {
                    final.Add(s.Trim());
                }
            }

            Debug.LogWarning(
                $" ==============================112211\r\n{string.Join("\r\n", final)}");
            return final.ToArray();
        }

        public static HashSet<string> GetDependence(Dictionary<string, string[]> key2Mark, string[] reskeys)
        {
            var dependences = new HashSet<string>();
            foreach (var reskey in reskeys)
            {
                if (key2Mark.TryGetValue(reskey, out var vs))
                {
                    dependences.AddRange(vs);
                }
            }

            return dependences;
        }

        public static void RemoveTinySets(IEnumerable<string> finalDeleteSets, bool onlyDeleteCSharp)
        {
            try
            {
                string rootDir = @"Assets/CasualGame";
                foreach (var finalDeleteSet in finalDeleteSets)
                {
                    if (string.IsNullOrEmpty(finalDeleteSet) || finalDeleteSet.Trim() == ""|| finalDeleteSet.StartsWith("lib_Animancer") || finalDeleteSet.StartsWith("lib_") || finalDeleteSet.StartsWith("Common"))
                    {
                        //空目录跳过，避免整个小游戏目录Assets\CasualGame都给删除了
                        continue;
                    }

                    var tempPath = Path.Combine(rootDir, finalDeleteSet);
                    if (!Directory.Exists(tempPath))
                    {
                        Debug.LogWarning($"dir is not exist,path=={tempPath}");
                        continue;
                    }
                    
                    if (onlyDeleteCSharp)
                    {
                        var cSharpFiles = Directory.GetFiles(tempPath, "*.cs", SearchOption.AllDirectories);
                        Debug.Log("DelectOnlyCS="+ tempPath);
                        foreach (var cSharpFile in cSharpFiles)
                        {
                            if (File.Exists(cSharpFile))
                            {
                                File.Delete(cSharpFile);
                            }

                            if (File.Exists($"{cSharpFile}.meta"))
                            {
                                File.Delete($"{cSharpFile}.meta");
                            }
                        }
                    }
                    else
                    {
                        Debug.Log("Delect=" + tempPath);
                        if (Directory.Exists(tempPath))
                        {
                            Directory.Delete(tempPath, true);
                        }
                    }
                }
                AssetDatabase.Refresh();
            }
            catch (Exception e)
            {
                Debug.LogError("kuai dian kan kan shan chu guo cheng you yi chang ");
                Debug.LogError(e);
                throw;
            }
        }

        private static int GetAllCSharpNum() 
        {
            return Directory.GetFiles(Application.dataPath, "*.cs", SearchOption.AllDirectories).Length;
        }

        private static int GetCSharpNum(string path) 
        {
            string [] css = Directory.GetFiles(path, "*.cs", SearchOption.AllDirectories);
            return css.Length;
        }

        [MenuItem("Assets/MGameToolKit/RemoveUnuseTinyGameResAndRefreshEditMenu")]
        public static void RemoveUnuseTinyGameFormConfig()
        {
            Debug.LogError("start   RemoveUnuseTinyGameFormConfig");
            // 2.读删除列表,
            var deleteResKeys = ReadProjectUnuseResKeysRaw().ToList();

            if (deleteResKeys != null && deleteResKeys.Count > 0)
            {
                RemoveTinyList(deleteResKeys);
            }

            Debug.LogError("end   RemoveUnuseTinyGameFormConfig");

        }
        public static string[] ReadProjectUnuseResKeysRaw()
        {
            string raw = @"Assets/CasualGame/unuse_res_keys_raw.txt";
            var text = File.ReadAllText(raw);
            //var aa = AnalyzeFormKeysString(text);
            //Debug.LogError(text);
            //Debug.LogError(string.Join("_",aa));
            return AnalyzeFormKeysString(text);
        }
        public static void RemoveTinyList(List<string> finalDeleteSets)
        {
            try
            {
                string curplatfom = BuildScript.GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget);
                string rootDir = @"Assets/CasualGame";
                string rootDir_1 = Path.Combine("Assets/StreamingAssets/AssetBundles/", curplatfom, "casualgame");
                string rootDir_2 = Path.Combine("AssetBundles", curplatfom, "casualgame");
                foreach (var finalDeleteSet in finalDeleteSets)
                {
                    DeleteUnuseFolder(finalDeleteSet, rootDir, false);
                    DeleteUnuseFolder(finalDeleteSet, rootDir_1, true);
                    DeleteUnuseFolder(finalDeleteSet, rootDir_2, true);
                }
                AssetDatabase.Refresh();
            }
            catch (Exception e)
            {
                Debug.LogError("DeleteUnuseGame error ");
                Debug.LogError(e);
                throw;
            }
        }
        public static void DeleteUnuseFolder(string name, string rootDir, bool tolower)
        {
            if (tolower)
            {
                name = name.ToLower();
            }
            else
            {
                var tempPath = Path.Combine(rootDir, name);
                if (Directory.Exists(tempPath) && Directory.GetFiles(tempPath).Length >= 5)
                {
                    Debug.LogWarning("need revert mini game ？ connect  tanrongkun haha==" + tempPath);
                    EditorApplication.Exit(1);
                    return;
                }
            }
            if (string.IsNullOrEmpty(name) || name.Trim() == "" || name.StartsWith("lib_Animancer") || name.StartsWith("lib_") || name.StartsWith("Common"))
            {
                //空目录跳过，避免整个小游戏目录Assets\CasualGame都给删除了
                Debug.LogWarning($"finalDeleteUnuseGameSkip{Path.Combine(rootDir, name)}");
            }
            else
            {
                var tempPath = Path.Combine(rootDir, name);
                Debug.LogWarning($"finalDeleteUnuseGame =={tempPath}");
                if (Directory.Exists(tempPath))
                {
                    Directory.Delete(tempPath, true);
                }
                else
                {
                    Debug.LogWarning($"dir is not exist,path=={tempPath}");
                }
                string matefile = tempPath + ".meta";
                if (File.Exists(matefile))
                {
                    File.Delete(matefile);
                }
                else
                {
                    Debug.LogWarning($"dir.meta is not exist,path=={matefile}");
                }
            }
        }

    }
}
#endif