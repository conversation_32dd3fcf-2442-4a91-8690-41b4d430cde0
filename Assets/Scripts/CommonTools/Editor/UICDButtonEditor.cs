using UnityEditor;
using UnityEngine;
using UnityEditor.UI;

namespace Common_Util
{
    [CustomEditor(typeof(UICDButton))]
    public class UICDButtonEditor : ButtonEditor
    {
        public override void OnInspectorGUI()
        {
            // 绘制默认的Button组件Inspector
            base.OnInspectorGUI();

            // 绘制自定义的intervalTime字段
            UICDButton myButton = (UICDButton)target;
            myButton.intervalTime = EditorGUILayout.FloatField("Interval Time", myButton.intervalTime);

            if (GUI.changed)
            {
                // 标记需要保存更改
                EditorUtility.SetDirty(myButton);
            }
        }
    }
}