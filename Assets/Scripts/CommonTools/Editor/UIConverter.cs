using UnityEditor;
using UnityEngine;
using Common_Util;
using SuperTools;
using UnityEngine.UI;

public class UIConverter : EditorWindow
{
    [SuperTMenuItem(SuperTools.EMenuType.Main, "GameObject/蓝湖/点击任意位置关闭", false, 1)]
    public static void SetTextClose()
    {
        string path = "Assets/UI/Prefabs/GW/GW_Common/Com/ClickCloseText.prefab";
        // 获取选中的对象
        GameObject selectedObject = Selection.activeGameObject;

        if (selectedObject == null)
        {
            Debug.LogWarning("请先选择一个带有Image组件的游戏对象。");
            return;
        }

        // 获取该对象的Image组件
        RectTransform rtf = selectedObject.GetComponent<RectTransform>();

        if (rtf != null)
        {
            // 加载Sprite
            GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
            if (prefab != null)
            {
                GameObject go = GameObject.Instantiate(prefab, rtf.transform);
                RectTransform goRtf = go.GetComponent<RectTransform>();

                float posY = -rtf.rect.height / 2 - 30;

                goRtf.anchoredPosition = new Vector2(0, posY);
                EditorUtility.SetDirty(selectedObject);
                Debug.Log($"已添加 点击任意位置关闭");
            }
        }
    }
    [SuperTMenuItem(SuperTools.EMenuType.Main, "GameObject/UITool/Text to UIFixText", false, 1)]
    public static void SetTextToUIFixText()
    {
        GameObject textObject = Selection.activeGameObject;
        if (textObject == null || textObject.GetComponent<Text>() == null)
        {
            Debug.LogError("请选中一个包含Text组件的对象");
            return;
        }

        Text originalText = textObject.GetComponent<Text>();
        DestroyImmediate(originalText);

        UIFitText newText = textObject.AddComponent<UIFitText>();
        newText.text = originalText.text;
        newText.font = originalText.font;
        newText.fontStyle = originalText.fontStyle;
        newText.fontSize = originalText.fontSize;
        newText.lineSpacing = originalText.lineSpacing;
        newText.alignment = originalText.alignment;
        newText.color = originalText.color;
        newText.supportRichText = originalText.supportRichText;
        newText.resizeTextForBestFit = originalText.resizeTextForBestFit;
        newText.resizeTextMinSize = originalText.resizeTextMinSize;
        newText.resizeTextMaxSize = originalText.resizeTextMaxSize;
        newText.verticalOverflow = originalText.verticalOverflow;
        newText.horizontalOverflow = originalText.horizontalOverflow;
        newText.raycastTarget = originalText.raycastTarget;
        EditorUtility.SetDirty(textObject);
        Debug.Log("Text属性已成功复制到UIFitText!");
    }

    [SuperTMenuItem(EMenuType.Main, "GameObject/UITool/Button to UICDButton", false, 1)]
    public static void SetButtonToUICDButton()
    {
        GameObject buttonObject = Selection.activeGameObject;
        if (buttonObject == null || buttonObject.GetComponent<Button>() == null)
        {
            Debug.LogError("请选中一个包含Button组件的对象");
            return;
        }

        Button originalButton = buttonObject.GetComponent<Button>();
        DestroyImmediate(originalButton);

        UICDButton newButton = buttonObject.AddComponent<UICDButton>(); // 假设有一个UIFixButton类
        // 复制Button的属性
        newButton.transition = originalButton.transition;
        newButton.colors = originalButton.colors;
        newButton.spriteState = originalButton.spriteState;
        newButton.animationTriggers = originalButton.animationTriggers;
        newButton.targetGraphic = originalButton.targetGraphic;
        newButton.navigation = originalButton.navigation;
        EditorUtility.SetDirty(buttonObject);
        Debug.Log("Button属性已成功复制到UICDButton!");
    }
}