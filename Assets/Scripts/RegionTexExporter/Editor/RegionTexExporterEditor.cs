using System;
using UnityEngine;
using UnityEditor;
using Object = UnityEngine.Object;
using UnityEngine.WSA;

using Vegetation;

public class RegionTexExporterWindow : EditorWindow
{
    #region const
    private const string FOLD_PATH = "FOLD_PATH";
    private const string PREFBA_PATH = "PREFAB_PATH";
    private const string REGION_MAT_PATH = "REGION_MAT_PATH";
    #endregion

    #region private filed
    private Object m_folder;
    private string m_savePath;
    private Object m_lod0LineRenderPrefab;
    private string m_cachePrefabPath;
    private Material m_regionMat;
    private string m_cacheRegionMatPath;
    #endregion

    #region private method
    private void DrawProperty()
    {
        EditorGUI.BeginChangeCheck();
        m_regionMat = (Material)EditorGUILayout.ObjectField("Region Mat", m_regionMat, typeof(Material), true);
        if(EditorGUI.EndChangeCheck())
        {
            string matPath = AssetDatabase.GetAssetPath(m_regionMat);
            EditorPrefs.SetString(REGION_MAT_PATH, matPath);
        }

        EditorGUI.BeginChangeCheck();
        m_lod0LineRenderPrefab = (GameObject)EditorGUILayout.ObjectField("LOD 0 Line Render Object", m_lod0LineRenderPrefab, typeof(GameObject), true);

        GUILayout.Space(10);

        if (EditorGUI.EndChangeCheck())
        {
            string prefabPath = AssetDatabase.GetAssetPath(m_lod0LineRenderPrefab);
            EditorPrefs.SetString(PREFBA_PATH, prefabPath);
        }
    }

    private void DisplayProgress(float progress,string messgage)
    {
        EditorUtility.DisplayProgressBar("Exporter All Region Texture", messgage, progress);

        if (progress >= 1.0f)
            EditorUtility.ClearProgressBar();
    }
    #endregion

    #region GUI Window
    [MenuItem("SToolCollection/Tool/Region Texture Exporter Window")]
    static void CreateRegionExporterWindow()
    {
        GetWindow<RegionTexExporterWindow>("Region Tex Exporter Window");
    }
    #endregion

    #region Private Method
    void LoadPrefsSetting()
    {
        m_savePath = EditorPrefs.GetString(FOLD_PATH, "");
        if (!string.IsNullOrEmpty(m_savePath))
        {
            m_folder = AssetDatabase.LoadAssetAtPath<Object>(m_savePath);
            m_savePath = m_savePath + "/";        
        }

        m_cachePrefabPath = EditorPrefs.GetString(PREFBA_PATH, "Assets/Art/GreatWorld/Sand/ReginTest/ReginTest.prefab");
        m_lod0LineRenderPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(m_cachePrefabPath);

        m_cacheRegionMatPath = EditorPrefs.GetString(REGION_MAT_PATH, "");
        if(!string.IsNullOrEmpty(m_cacheRegionMatPath))
        {
            m_regionMat = AssetDatabase.LoadAssetAtPath<Material>(m_cacheRegionMatPath);
        }
    }

    void ExporterAll()
    {
        GameObject lod0LineRender = GameObject.Instantiate(m_lod0LineRenderPrefab) as GameObject;
        lod0LineRender.transform.position = Vector3.zero;
        lod0LineRender.transform.localEulerAngles = new Vector3(90, 0, 0);
        lod0LineRender.transform.localScale = Vector3.one;

        var lineRenders = lod0LineRender.GetComponentsInChildren<LineRenderer>();
        if(lineRenders!=null&&lineRenders.Length>0)
        {
            
            for(int i =0; i<lineRenders.Length;i++)
            {
                DisplayProgress((float)((i + 1) / lineRenders.Length), "Cur executing index:" + i + " Texture");
                var lineRederer = lineRenders[i];
                RegionTexExporter exporter = lineRederer.gameObject.AddComponent<RegionTexExporter>();
                exporter.FolderPath = m_savePath;
                exporter.SpriteMat = m_regionMat;
                exporter.ExportRegionTex();           
            }

            EditorUtility.ClearProgressBar();
        }

        DestroyImmediate(lod0LineRender.gameObject);
    }

    private void FoldPathSetting()
    {
        EditorGUILayout.LabelField("Save RegionTex Path (Specific path of folder)");
        EditorGUI.BeginChangeCheck();
        m_folder = EditorGUILayout.ObjectField(m_folder, typeof(Object), false);
        if (EditorGUI.EndChangeCheck())
        {
            if (m_folder)
            {
                var id = m_folder.GetInstanceID();
                m_savePath = AssetDatabase.GetAssetPath(id) + "/";
                EditorPrefs.SetString(FOLD_PATH, AssetDatabase.GetAssetPath(id));
            }
        }
    }
    #endregion

    #region Unity loop
    private void OnEnable()
    {
        LoadPrefsSetting();
    }

    private void OnGUI()
    {
        FoldPathSetting();
        if (m_savePath == null)
            return;

        DrawProperty();

        if (m_lod0LineRenderPrefab == null)
            return;

        if(GUILayout.Button("Exporter All RegionTexture"))
        {
            ExporterAll();
        }

    }
    #endregion
}

[CustomEditor(typeof(RegionTexExporter))]
public class RegionTexExporterEditor : Editor
{
    #region const
    private const string FOLD_PATH = "FOLD_PATH";
    #endregion

    #region private field
    private Object m_folder;
    private string m_savePath;
    private RegionTexExporter m_regionTexExporter;
    #endregion

    #region Unity loop
    private void OnEnable()
    {
        m_regionTexExporter = (RegionTexExporter)target;
        LoadPrefsSetting();
    }
    #endregion

    #region GUI Loop
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();
        FoldPathSetting();
        if( string.IsNullOrEmpty(m_regionTexExporter.FolderPath))
        {
            return;
        }
        DrawProperty();
        if (GUILayout.Button("������ǰ Region Tex"))
        {
            ExportTex();
        }
    }

    #endregion

    #region Private Method
    void DrawProperty()
    {
        EditorGUILayout.LabelField("Debug");
        m_regionTexExporter.DebugMesh = EditorGUILayout.Toggle("Show Generate Mesh", m_regionTexExporter.DebugMesh);
        EditorGUILayout.Space(10);

        EditorGUILayout.Space(10);

        EditorGUILayout.LabelField("SDF");
        m_regionTexExporter.BasicMaxDistance = EditorGUILayout.FloatField("Basic Max SDF Distance", m_regionTexExporter.BasicMaxDistance);
        m_regionTexExporter.GenerateSprite = EditorGUILayout.Toggle("Generate Sprite", m_regionTexExporter.GenerateSprite);
        m_regionTexExporter.SpriteMat = (Material)EditorGUILayout.ObjectField("Sprite Mat", m_regionTexExporter.SpriteMat,typeof(Material),true);
        m_regionTexExporter.MAX_Texture_Size = (TextureSize)EditorGUILayout.EnumPopup("Max Texture Size", m_regionTexExporter.MAX_Texture_Size);
        EditorGUILayout.Space(10);

        m_regionTexExporter.TextureSize = EditorGUILayout.Vector2Field("Tex Size", m_regionTexExporter.TextureSize);
        m_regionTexExporter.FrustPlaneDisOffset = EditorGUILayout.FloatField("Frustum Size Scale", m_regionTexExporter.FrustPlaneDisOffset);
    }

    void ExportTex()
    {
        m_regionTexExporter.ExportRegionTex();
    }

    private void FoldPathSetting()
    {
        EditorGUILayout.LabelField("Save RegionTex Path (Specific path of folder)");
        EditorGUI.BeginChangeCheck();
        m_folder = EditorGUILayout.ObjectField(m_folder, typeof(Object), false);
        if(EditorGUI.EndChangeCheck())
        {
            if(m_folder)
            {
                var id = m_folder.GetInstanceID();
                m_savePath = AssetDatabase.GetAssetPath(id) + "/";
                m_regionTexExporter.FolderPath = m_savePath;
                EditorPrefs.SetString(FOLD_PATH, AssetDatabase.GetAssetPath(id));
            }
        }
    }

    void LoadPrefsSetting()
    {
        m_savePath = EditorPrefs.GetString(FOLD_PATH, "");
        if(!string.IsNullOrEmpty(m_savePath))
        {
            m_regionTexExporter.FolderPath = m_savePath + "/";
            m_folder = AssetDatabase.LoadAssetAtPath<Object>(m_savePath);
        }
    }
    #endregion
}
