
using System;
using System.Collections.Generic;
using UnityEngine.Sprites;


namespace UnityEngine.UI.Extensions
{


    [AddComponentMenu("UI/Extensions/UI Polygon Image"), ExecuteInEditMode]
    public class UIPolygonImage : Image
    {
#if UNITY_EDITOR
        [NonSerialized]
        public PolygonCollider2D polygonCollider;
#endif

        public List<Vector2> polygonImagePoints = new List<Vector2>();
        [Range(-1, 1)]
        // ���� x �������Χ
        public float xOffset;
        [Range(-1, 1)]
        // ���� y �������Χ
        public float yOffset;
        [Range(-1, 1)]
        // ���� y ��ƽ��
        public float yShift;

        [NonSerialized]
        public List<Vector2> meshPoints = new List<Vector2>();
        [NonSerialized]
        readonly List<UIVertex> meshVertices = new List<UIVertex>();
        [NonSerialized]
        readonly Vector3[] corners = new Vector3[4];
        [NonSerialized]
        public Vector2 localPositionOffset = Vector2.zero;

        protected override void Awake()
        {
            base.Awake();

            InitPolygonInfo();
        }

        void InitPolygonInfo()
        {
#if UNITY_EDITOR
            if (!Application.isPlaying)
            {
                if (polygonCollider == null)
                {
                    polygonCollider = GetComponent<PolygonCollider2D>();
                    if (!polygonCollider)
                    {
                        polygonCollider = gameObject.AddComponent<PolygonCollider2D>();
                        polygonCollider.enabled = false;
                        polygonCollider.hideFlags = HideFlags.DontSaveInEditor | HideFlags.DontSaveInBuild;
                    }
                }
            }
            else
            {
                if (polygonCollider)
                {
                    GameObject.Destroy(polygonCollider);
                    polygonCollider = null;
                }
            }
            if (polygonCollider)
            {
                var colliderPoints = CreateColliderPolygonPoints();
                if (colliderPoints.Count > 0)
                {
                    polygonCollider.SetPath(0, colliderPoints);
                }
            }
#endif
        }

        protected override void OnEnable()
        {
            base.OnEnable();

            InitPolygonInfo();
        }

#if UNITY_EDITOR
        public List<Vector2> CreateColliderPolygonPoints()
        {
            List<Vector2> colliderPoints = new List<Vector2>();

            var rect = rectTransform.rect;
            var halfWidth = rect.width / 2;
            var halfHeight = rect.height / 2;

            for (var idx = 0; idx < polygonImagePoints.Count; idx++)
            {
                var polygonPoint = polygonImagePoints[idx];
                polygonPoint.x = polygonPoint.x * rect.width - halfWidth;
                polygonPoint.y = polygonPoint.y * rect.height - halfHeight;
                colliderPoints.Add(polygonPoint);
            }

            return colliderPoints;
        }
#endif

        protected override void OnPopulateMesh(VertexHelper vh)
        {
            base.OnPopulateMesh(vh);

            meshVertices.Clear();
            meshPoints.Clear();

            bool hasSprite = (sprite != null);
            Vector4 uvRange = Vector4.zero;

            rectTransform.GetLocalCorners(corners);
            Vector3 bottomLeft = corners[0];
            Vector3 topRight = corners[2];

            float width = topRight.x - bottomLeft.x;
            float height = topRight.y - bottomLeft.y;

            var xAtlasScale = 0f;
            var yAtlasScale = 0f;

            if (hasSprite)
            {
                uvRange = DataUtility.GetOuterUV(sprite);

                xAtlasScale = (uvRange.z - uvRange.x);
                yAtlasScale = (uvRange.w - uvRange.y);

                Vector4 padding = DataUtility.GetPadding(sprite);

                uvRange.x -= padding.x / width * xAtlasScale;
                uvRange.z += padding.z / width * xAtlasScale;

                uvRange.y -= padding.y / height * yAtlasScale;
                uvRange.w += padding.w / height * yAtlasScale;

                xAtlasScale = (uvRange.z - uvRange.x);
                yAtlasScale = (uvRange.w - uvRange.y);
            }

            var minX = bottomLeft.x;
            var maxX = topRight.x;
            var minY = bottomLeft.y;
            var maxY = topRight.y;

            var xLocalPositionOffset = localPositionOffset.x / width * xAtlasScale;
            var yLocalPositionOffset = yShift * yAtlasScale + localPositionOffset.y / height * yAtlasScale;

            for (var i = 0; i < polygonImagePoints.Count; i++)
            {
                var polygonPoint = polygonImagePoints[i];
                Vector2 position;
                position.x = Mathf.Lerp(minX, maxX, polygonPoint.x);
                position.y = Mathf.Lerp(minY, maxY, polygonPoint.y);
                meshPoints.Add(position);

                UIVertex uiVertex = UIVertex.simpleVert;
                uiVertex.position = position;

                if(hasSprite)
                {
                    var xAtlasOfsset = xOffset * xAtlasScale;
                    var yAtlasOfsset = yOffset * yAtlasScale;
                    var x = Mathf.Lerp(uvRange.x + xAtlasOfsset, uvRange.z - xAtlasOfsset, polygonPoint.x) + xLocalPositionOffset;
                    var y = Mathf.Lerp(uvRange.y + yAtlasOfsset, uvRange.w - yAtlasOfsset, polygonPoint.y) + yLocalPositionOffset;

                    uiVertex.uv0.x = x;
                    uiVertex.uv0.y = y;

                    uiVertex.tangent = uvRange;
                }

                meshVertices.Add(uiVertex);
            }

            var indices = Triangulator.Triangulate(meshPoints);

            vh.Clear();
            vh.AddUIVertexStream(meshVertices, indices);
        }
    }
}