using UnityEngine;
using UnityEditor;
using System.IO;
using System.Collections.Generic;
using System.Linq;
using EmojiText.Taurus;
using SuperTools;

/// <summary>
/// Emoji图集生成器 - 将散图合并为单一图集并生成UV映射信息
/// </summary>
public class EmojiAtlasGenerator : EditorWindow
{
    #region 字段
    private string sourceFolderPath = "Assets/Scripts/EmojiText/Editor/Emojis";
    private string outputFolderPath = "Assets/UI/liaotian";
    private string atlasName = "unicodeEmoji";
    private int columnSpacing = 1;
    private int rowSpacing = 1;
    private int fixedSize = 32;
    private int richQuadSize = 32;
    private bool isProcessing = false;
    private int spriteAssetId = 3;
    //是否大写
    private bool isUpperCase = true; 
    
    private List<EmojiData> emojiDataList = new List<EmojiData>();
    private Vector2 scrollPosition;
    #endregion
    [SuperTMenuItem(EMenuType.Main, "Tools/Emoji图集和图文混排资源生成器")]
    public static void ShowWindow()
    {
        GetWindow<EmojiAtlasGenerator>("Emoji图集和图文混排资源生成器");
    }

    private void OnGUI()
    {
        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
        
        DrawHeader();
        DrawPaths();
        DrawSettings();
        DrawButtons();
        
        EditorGUILayout.EndScrollView();
    }

    private void DrawHeader()
    {
        EditorGUILayout.Space();
    }

    private void DrawPaths()
    {
        EditorGUILayout.BeginHorizontal();
        sourceFolderPath = EditorGUILayout.TextField("源目录", sourceFolderPath);
        if (GUILayout.Button("浏览", GUILayout.Width(80)))
        {
            BrowseSourceFolder();
        }
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.BeginHorizontal();
        outputFolderPath = EditorGUILayout.TextField("输出目录", outputFolderPath);
        if (GUILayout.Button("浏览", GUILayout.Width(80)))
        {
            BrowseOutputFolder();
        }
        EditorGUILayout.EndHorizontal();
    }

    private void DrawSettings()
    {
        spriteAssetId = EditorGUILayout.IntField("SpriteAssetId", spriteAssetId);
        atlasName = EditorGUILayout.TextField("图集名称", atlasName);
        columnSpacing = EditorGUILayout.IntField("列间距", columnSpacing);
        rowSpacing = EditorGUILayout.IntField("行间距", rowSpacing);
        fixedSize = EditorGUILayout.IntField("图片大小", fixedSize);
        richQuadSize = EditorGUILayout.IntField("富文本大小", richQuadSize);
        isUpperCase = EditorGUILayout.Toggle("是否大写", isUpperCase);
        EditorGUILayout.Space();
    }

    private void DrawButtons()
    {
        EditorGUILayout.BeginHorizontal();
        GUI.enabled = !isProcessing && !string.IsNullOrEmpty(sourceFolderPath) && !string.IsNullOrEmpty(outputFolderPath);
        if (GUILayout.Button("生成图集"))
        {
            GenerateAtlas();
        }
        GUI.enabled = true;
        EditorGUILayout.EndHorizontal();
    }

    #region 核心功能
    private void BrowseSourceFolder()
    {
        string fullPath = EditorUtility.OpenFolderPanel("选择源文件夹", Application.dataPath, "");
        if (!string.IsNullOrEmpty(fullPath) && fullPath.StartsWith(Application.dataPath))
        {
            sourceFolderPath = "Assets" + fullPath.Substring(Application.dataPath.Length);
        }
    }

    private void BrowseOutputFolder()
    {
        string fullPath = EditorUtility.OpenFolderPanel("选择输出文件夹", Application.dataPath, "");
        if (!string.IsNullOrEmpty(fullPath) && fullPath.StartsWith(Application.dataPath))
        {
            outputFolderPath = "Assets" + fullPath.Substring(Application.dataPath.Length);
        }
    }

    private void GenerateAtlas()
    {
        isProcessing = true;
        try
        {
            LoadImages();
            if (emojiDataList.Count == 0)
            {
                EditorUtility.DisplayDialog("错误", "没有找到有效的图片文件", "确定");
                return;
            }

            CalculateLayout(out int atlasWidth, out int atlasHeight);
            List<Rect> emojiRects = CalculateEmojiRects(emojiDataList.Count, atlasWidth, atlasHeight);
            
            Texture2D atlas = CreateAtlas(atlasWidth, atlasHeight);
            SpriteAsset spriteAsset = CollectAtlasAndSpriteAsset(emojiRects, atlasWidth, atlasHeight, atlas);
            
            SaveAtlasAndSpriteAsset(atlas, spriteAsset, outputFolderPath);
        }
        finally
        {
            isProcessing = false;
        }
    }

    private void LoadImages()
    {
        // 清理旧数据
        foreach (var emoji in emojiDataList)
        {
            if (emoji.Texture != null) DestroyImmediate(emoji.Texture);
        }
        emojiDataList.Clear();

        string absolutePath = GetAbsolutePath(sourceFolderPath);
        foreach (string filePath in GetImageFiles(absolutePath))
        {
            Texture2D texture = LoadTexture(filePath);
            if (texture != null)
            {
                if (texture.width != fixedSize || texture.height != fixedSize)
                {
                    texture = ResizeTexture(texture, fixedSize, fixedSize);
                }
                emojiDataList.Add(new EmojiData
                {
                    Texture = texture,
                    FileName = Path.GetFileNameWithoutExtension(filePath)
                });
            }
        }
    }

    private void CalculateLayout(out int width, out int height)
    {
        int count = emojiDataList.Count;
        int cols = Mathf.CeilToInt(Mathf.Sqrt(count));
        int rows = Mathf.CeilToInt((float)count / cols);
        width = cols * fixedSize + (cols - 1) * columnSpacing;
        height = rows * fixedSize + (rows - 1) * rowSpacing;
    }

    private List<Rect> CalculateEmojiRects(int count, int width, int height)
    {
        List<Rect> rects = new List<Rect>();
        int cols = Mathf.CeilToInt(Mathf.Sqrt(count));
        for (int i = 0; i < count; i++)
        {
            int x = (i % cols) * (fixedSize + columnSpacing);
            int y = (i / cols) * (fixedSize + rowSpacing);
            rects.Add(new Rect(x, y, fixedSize, fixedSize));
        }
        return rects;
    }

    private Texture2D CreateAtlas(int width, int height)
    {
        Texture2D atlas = new Texture2D(width, height, TextureFormat.RGBA32, false);
        Color[] clearColors = new Color[width * height];
        for (int i = 0; i < clearColors.Length; i++) clearColors[i] = Color.clear;
        atlas.SetPixels(clearColors);
        return atlas;
    }

    private SpriteAsset CollectAtlasAndSpriteAsset(List<Rect> rects, int width, int height, Texture2D atlas)
    {
        
        for (int i = 0; i < emojiDataList.Count; i++)
        {
            Rect rect = rects[i];
            atlas.SetPixels(
                (int)rect.x, 
                (int)(height - rect.y - rect.height), 
                (int)rect.width, 
                (int)rect.height, 
                emojiDataList[i].Texture.GetPixels());
        }
        atlas.Apply();
        
        //创建序列化文件
        string spriteAssetPath = outputFolderPath + "/" + $"{atlasName}.asset";
        SpriteAsset spriteAsset = (SpriteAsset)AssetDatabase.LoadAssetAtPath(spriteAssetPath, typeof(SpriteAsset));
        if (spriteAsset == null)
        {
            spriteAsset = CreateInstance<SpriteAsset>();
            if (spriteAsset!= null)
                AssetDatabase.CreateAsset(spriteAsset, spriteAssetPath);
            spriteAsset = (SpriteAsset)AssetDatabase.LoadAssetAtPath(spriteAssetPath, typeof(SpriteAsset));
        }
        spriteAsset.Id = spriteAssetId;
        spriteAsset.TexSource = atlas;
        spriteAsset.IsStatic = true;
        spriteAsset.RealCount = emojiDataList.Count;
        spriteAsset.Speed = 0;
        int count = emojiDataList.Count;
        int cols = Mathf.CeilToInt(Mathf.Sqrt(count));
        int rows = Mathf.CeilToInt((float)count / cols);
        spriteAsset.Row = rows;
        spriteAsset.Column = cols;
        spriteAsset.ListSpriteGroup = new List<SpriteInforGroup>();
        Vector2 texSize = new Vector2(atlas.width,atlas.height);
        for (int i = 0; i < emojiDataList.Count; i++)
        {
            Rect rect = rects[i];
            rect.Set(rect.x, height - rect.y - rect.height, rect.width, rect.height);
            var spriteInforGroup = new SpriteInforGroup()
            {
                Tag = isUpperCase ? emojiDataList[i].FileName.ToUpper() : emojiDataList[i].FileName.ToLower(),
                ListSpriteInfor = new List<SpriteInfor>(),
                Width = 1,
                Size = richQuadSize,
            };
            SpriteInfor infor = GetSpriteInfo(i, texSize, rect);
            spriteInforGroup.ListSpriteInfor.Add(infor);
            spriteAsset.ListSpriteGroup.Add(spriteInforGroup);
            // spriteAsset.Id
            // uvInfo.Add($"{emojiDataList[i].FileName},{uvX},{uvY},{rect.width/width},{rect.height/height}");
        }
        
        return spriteAsset;
    }
    
    //获取精灵信息
    private SpriteInfor GetSpriteInfo(int index, Vector2 texSize, Rect spriteRect)
    {
        SpriteInfor infor = Pool<SpriteInfor>.Get();
        infor.Id = index;
        infor.Rect = spriteRect;
        infor.DrawTexCoord = new Rect(infor.Rect.x / texSize.x, infor.Rect.y / texSize.y
            , infor.Rect.width / texSize.x, infor.Rect.height / texSize.y);
        infor.Uv = GetSpriteUV(texSize, infor.Rect);
        return infor;
    }
    
    //获取uv信息
    private static Vector2[] GetSpriteUV(Vector2 texSize, Rect _sprRect)
    {
        Vector2[] uv = new Vector2[4];
        uv[0] = new Vector2(_sprRect.x / texSize.x, (_sprRect.y + _sprRect.height) / texSize.y);
        uv[1] = new Vector2((_sprRect.x + _sprRect.width) / texSize.x, (_sprRect.y + _sprRect.height) / texSize.y);
        uv[2] = new Vector2((_sprRect.x + _sprRect.width) / texSize.x, _sprRect.y / texSize.y);
        uv[3] = new Vector2(_sprRect.x / texSize.x, _sprRect.y / texSize.y);
        return uv;
    }

    private void SaveAtlasAndSpriteAsset(Texture2D atlas, SpriteAsset sp, string outputPath)
    {
        var absFolderPath = GetAbsolutePath(outputFolderPath);
        // 保存图集
        string atlasPath = Path.Combine(absFolderPath, $"{atlasName}.png");
        File.WriteAllBytes(atlasPath, atlas.EncodeToPNG());

        // 保存UV信息
        // File.WriteAllLines(Path.Combine(outputPath, $"{atlasName}_UVInfo.csv"), uvInfo);
        
        // spriteAsset.ListSpriteGroup = GetAssetSpriteInfor(sourceTex);
        //路径从Assets开始
        
        // 设置纹理导入设置并选中文件
        string assetPath = Path.Combine(outputFolderPath, $"{atlasName}.png");
        TextureImporter importer = AssetImporter.GetAtPath(assetPath) as TextureImporter;
        if (importer != null)
        {
            importer.alphaIsTransparency = true;
            importer.SaveAndReimport();
        }
        AssetDatabase.Refresh();
        // 选中生成的图集
        UnityEngine.Object obj = AssetDatabase.LoadAssetAtPath(assetPath, typeof(Texture2D));
        Selection.activeObject = obj;
        string spriteAssetPath = Path.Combine(outputFolderPath, $"{atlasName}.asset");
        SpriteAsset spriteAsset = (SpriteAsset)AssetDatabase.LoadAssetAtPath(spriteAssetPath, typeof(SpriteAsset));
        if (spriteAsset != null)
        {
            spriteAsset.TexSource = (Texture2D)obj;
        }
        EditorUtility.SetDirty(spriteAsset);
        AssetDatabase.SaveAssets();
        EditorGUIUtility.PingObject(obj);
        
    }
    #endregion

    #region 工具方法
    private string GetAbsolutePath(string relativePath)
    {
        return Path.Combine(Application.dataPath, relativePath.Substring("Assets/".Length));
    }

    private string[] GetImageFiles(string path)
    {
        return Directory.GetFiles(path, "*.*", SearchOption.TopDirectoryOnly)
            .Where(f => f.EndsWith(".png") || f.EndsWith(".jpg") || f.EndsWith(".jpeg"))
            .ToArray();
    }

    private Texture2D LoadTexture(string path)
    {
        Texture2D texture = new Texture2D(2, 2);
        return texture.LoadImage(File.ReadAllBytes(path)) ? texture : null;
    }

    private Texture2D ResizeTexture(Texture2D source, int width, int height)
    {
        RenderTexture rt = RenderTexture.GetTemporary(width, height);
        Graphics.Blit(source, rt);
        RenderTexture.active = rt;
        
        Texture2D result = new Texture2D(width, height);
        result.ReadPixels(new Rect(0, 0, width, height), 0, 0);
        result.Apply();
        
        RenderTexture.ReleaseTemporary(rt);
        return result;
    }
    #endregion

    #region 资源清理
    private void OnDestroy()
    {
        foreach (var emoji in emojiDataList)
        {
            if (emoji.Texture != null) DestroyImmediate(emoji.Texture);
        }
        emojiDataList.Clear();
    }

    private class EmojiData
    {
        public Texture2D Texture;
        public string FileName;
    }
    #endregion
}