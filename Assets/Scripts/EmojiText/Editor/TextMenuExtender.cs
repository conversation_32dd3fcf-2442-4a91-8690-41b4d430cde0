using UnityEngine;
using System.Collections;
using UnityEditor;
using UnityEngine.UI;
using UnityEngine.EventSystems;

namespace EmojiText.Taurus
{
	public class TextMenuExtender
	{
		[MenuItem("GameObject/UI/TextInline", false, 10)]
		static void CreateCustomGameObject(MenuCommand menuCommand)
		{
			GameObject go = null;
			InlineManager inline = AssetDatabase.LoadAssetAtPath<InlineManager>("Assets/TextInlineSprite/Prefabs/TextInline.prefab");
			if (inline)
			{
				go = GameObject.Instantiate(inline).gameObject;
			}
			else
			{
				go = new GameObject();
				go.AddComponent<InlineText>();
			}
			go.name = "InlinText";
			GameObject parent = menuCommand.context as GameObject;
			if (parent == null)
			{
				parent = new GameObject("Canvas");
				parent.layer = LayerMask.NameToLayer("UI");
				parent.AddComponent<Canvas>().renderMode = RenderMode.ScreenSpaceOverlay;
				parent.AddComponent<CanvasScaler>();
				parent.AddComponent<GraphicRaycaster>();

				EventSystem _es = GameObject.FindObjectOfType<EventSystem>();
				if (!_es)
				{
					_es = new GameObject("EventSystem").AddComponent<EventSystem>();
					_es.gameObject.AddComponent<StandaloneInputModule>();
				}
			}
			GameObjectUtility.SetParentAndAlign(go, parent);
			//注册返回事件
			Undo.RegisterCreatedObjectUndo(go, "Create " + go.name);
			Selection.activeObject = go;
		}
	}

}