using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;
using System.Text;

public class ReimportAssetEditor : EditorWindow
{
    Rect pathRect;
    string folderPath = "Assets/";
    string assetType = "t:Texture";

    [MenuItem("Tools/Reimport Asset")]
    static void Open()
    {
        GetWindow<ReimportAssetEditor>("Reimport Asset");
    }


    void OnDestroy()
    {
        Resources.UnloadUnusedAssets();
    }

    void OnGUI()
    {
        OnGUIReimportAsset();
    }

    void OnGUIReimportAsset()
    {
        //获得一个长300的框
        pathRect = EditorGUILayout.GetControlRect();
        //将上面的框作为文本输入框
        folderPath = EditorGUI.TextField(pathRect, folderPath);

        //如果鼠标正在拖拽中或拖拽结束时，并且鼠标所在位置在文本输入框内
        if ((Event.current.type == EventType.DragUpdated
          || Event.current.type == EventType.DragExited)
          && pathRect.Contains(Event.current.mousePosition))
        {
            //改变鼠标的外表
            DragAndDrop.visualMode = DragAndDropVisualMode.Generic;
            if (DragAndDrop.paths != null && DragAndDrop.paths.Length > 0)
            {
                folderPath = DragAndDrop.paths[0];
            }
        }

        assetType = EditorGUILayout.TextField("资源类型:", assetType);

        if (GUILayout.Button("重新导入路径下所有指定类型资源"))
        {
            AssetDatabase.StartAssetEditing();
            string[] allPath = AssetDatabase.FindAssets(assetType, new string[] { folderPath });
            string filePath;
            int assetCount = allPath.Length;
            for(int i = 0; i < assetCount; i++)
            {
                filePath = AssetDatabase.GUIDToAssetPath(allPath[i]);
                AssetDatabase.ImportAsset(filePath, ImportAssetOptions.ForceUpdate);
                EditorUtility.DisplayProgressBar("导入资源 ", filePath, (float)i / assetCount);
            }
            EditorUtility.ClearProgressBar();
            AssetDatabase.StopAssetEditing();
            Debug.LogWarning("重新导入" + allPath.Length + "个资源,type:" + assetType);
        }
    }
}
