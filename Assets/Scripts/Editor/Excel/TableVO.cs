using System.Collections.Generic;
using System.IO;

namespace War.Base
{
    public partial class TableManager
    {
        public class TableVO
        {
            public string filename;
            public bool isfakecsv=false;
            public int start_row;
            public List<DicCache> colHeaders = new List<DicCache>();
            public List<List<KeyValuePair<string, int>>> main_keyes = new List<List<KeyValuePair<string, int>>>();
            public List<Dictionary<string, int>> key2rows = new List<Dictionary<string,int>>();
            public List<List<string>> values = new List<List<string>>();
            public int length;

            public IFileExe fileExe;
            public int GetColInd(string k)
            {
                var cc = colHeaders.FindIndex((c) => c.GetKey(k) != null);
                return cc;
            }
            public int GetColLastInd(string k)
            {
                var cc = colHeaders.FindLastIndex((c) => c.<PERSON><PERSON><PERSON>("name") == k);
                return cc;
            }
        } 
    }

}
