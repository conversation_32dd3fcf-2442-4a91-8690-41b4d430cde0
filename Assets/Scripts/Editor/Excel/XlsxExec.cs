using ExcelDataReader;
using System.Data;
using System.IO;

namespace War.Base
{
    public partial class TableManager
    {
        public class XlsxExec : IFileExe
        {

            private ExcelDataSetConfiguration createDataSetReadConfig(int headerRow)
            {
                var tableConfig = new ExcelDataTableConfiguration()
                {
                    // Gets or sets a value indicating whether to use a row from the 
                    // data as column names.
                    UseHeaderRow = true,

                    // Gets or sets a callback to determine whether to include the 
                    // current row in the DataTable.
                    //FilterRow = (rowReader) => {
                    //    return rowReader.Depth > headerRow - 1;
                    //},
                };

                return new ExcelDataSetConfiguration()
                {
                    // Gets or sets a value indicating whether to set the DataColumn.DataType
                    // property in a second pass.
                    UseColumnDataType = true,

                    // Gets or sets a callback to obtain configuration options for a DataTable. 
                    ConfigureDataTable = (tableReader) => { return tableConfig; },
                };
            }
            bool IFileExe.Iter(TableVO vo, object proc_line, object proc_col)
            {
                DataSet mData = null;
                string filePath = vo.filename;
                using (var stream = File.Open(filePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                {
                    // Auto-detect format, supports:
                    //  - Binary Excel files (2.0-2003 format; *.xls)
                    //  - OpenXml Excel files (2007 format; *.xlsx)
                    try
                    {
                        using (var exreader = ExcelReaderFactory.CreateReader(stream))
                        {
                            // Use the AsDataSet extension method
                            // The result of each spreadsheet is in result.Tables
                            var result = exreader.AsDataSet(createDataSetReadConfig(vo.start_row));
                            mData = result;
                        }
                    }
                    catch(System.Exception e)
                    {
                        "".PrintThread("finally", e.ToString(),e.StackTrace);
                        return false;
                    }
                    finally
                    {
                        "".PrintThread("finally",filePath);
                    }
                }

                var p_line = (System.Func<object, int, bool>)proc_line;
                var p_col = (System.Func<object, int, bool>)proc_col;

                string line = null;
                var sheet = mData.Tables[0];

                "".PrintThread(filePath);

                for (int iRow = 0; iRow < sheet.Rows.Count; iRow++)
                {
                    var row = sheet.Rows[iRow];

                    var status = p_line(line, iRow+1);


                    for (int i = 0; i < sheet.Columns.Count; i++)
                    {
                        var col = row[i];
                        var c_status = p_col(col, i+1);
                        if (!c_status) break;
                    }

                    if (!status) break;
                }

                return true;
            }
        }
    }

}
