using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using UnityEngine;

namespace War.Base
{
    public class ThreadScheduler:Singleton<ThreadScheduler>
    {
        public CancellationTokenSource Schedule(IEnumerable<object> data, Action<object> scheduleDelegate, Action callbackDelegate,string tag = "")
        {
            var cts = new CancellationTokenSource();
            CancellationToken cancellationToken = cts.Token;

            var threadWeakReferences = new List<WeakReference<CancellationTokenSource>>();
            threadWeakReferences.Add(new WeakReference<CancellationTokenSource>(cts));
            tag2threadWR[tag] = threadWeakReferences;
            var count = 0;
            var total = data.Count();
            //var total = Mathf.Min(data.Count(),50000);

            //int cpuCoreCount = Parallel.MaxDegreeOfParallelism;
            //ThreadPool.GetMaxThreads(out var mWTh   , out var cpTH);
            //ThreadPool.GetAvailableThreads(out var mWThav   , out var cpTHav);
            //"".PrintThread("ThreadPool", mWTh, mWThav, cpTH, cpTHav);

            foreach (var item in data)
            {
                cancellationToken.ThrowIfCancellationRequested();

                ThreadPool.QueueUserWorkItem(state =>
                {
                    if (cancellationToken.IsCancellationRequested) return;
                    try
                    {
                        scheduleDelegate(item);
                    }
                    catch (Exception e)
                    {
                        Debug.LogException(e);
                    }

                    if (Interlocked.Increment(ref count) == total)
                    {
                        try
                        {
                            callbackDelegate();
                        }
                        catch (Exception e)
                        {
                            Debug.LogException(e);
                        }
                            cts.Dispose();
                    }
                }, cancellationToken);
            }
            return cts;
        }
        public void Dispose(string tag = "")
        {
            if (tag2threadWR.TryGetValue(tag, out var ob) == false) return;

            var threadWeakReferences = (List<WeakReference<CancellationTokenSource>>)ob;
            foreach (var t in threadWeakReferences)
            {
                if (t !=null&& t.TryGetTarget(out var tokenSource))
                {
                    tokenSource.Cancel(false);
                }
            }
            threadWeakReferences.Clear();
        }
        //List<WeakReference<CancellationTokenSource>> threadWeakReferences = new List<WeakReference<CancellationTokenSource>>();

        Dictionary<string, object> tag2threadWR = new Dictionary<string, object>();
    }

}
