using System.Collections.Generic;

namespace War.Base
{
    public partial class TableManager
    {
        public class DicCache
        {
            public Dictionary<string,string> dic = new Dictionary<string, string>();

            public string GetKey(string keyName)
            {
                dic.TryGetValue(keyName, out var v);
                return v;
            }
            public int start = -1;
            public int end=-1;
        }
    }

}
