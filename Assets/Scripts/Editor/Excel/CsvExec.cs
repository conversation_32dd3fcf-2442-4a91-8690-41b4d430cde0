using System.IO;
using System.Text;

namespace War.Base
{
    public partial class TableManager
    {
        public class CsvExec : IFileExe
        {
            public bool Iter(TableVO vo, object proc_line, object proc_col)
            {
                var p_line = (System.Func<object,int,bool>)proc_line;
                var p_col = (System.Func<object,int,bool>)proc_col;

                TextReader reader = null;
                /// 存在表格csv 包含 ‘\n’兼容处理
                if (vo.isfakecsv)
                {
                    "".PrintThread("vo.isfakecsv", vo.filename);
                    reader = new StreamReaderEx(vo.filename, encoding: Encoding.GetEncoding("gb2312")); 
                }
                else
                {
                    reader = new StreamReader(vo.filename, encoding: Encoding.GetEncoding("gb2312")); 
                }
                string line;
                using (reader)
                {
                    var ind = 0;
                    while ((line = reader.ReadLine()) != null)
                    {
                        ind++;
                        var status = p_line(line, ind);

                        string[] cols = line.Split(',');

                        for (int i = 0; i < cols.Length; i++)
                        {
                            string col = cols[i];
                            var c_status = p_col(col, i+1);
                            if (!c_status) break;
                        }
                        if (!status) break;
                    }
                }
                return true;
            } 
        }
    }

}
