using NUnit;
using NUnit.Framework;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using TMPro;
using U3D.Threading;
using UnityEditor;
using UnityEngine;
using War.Script;
using static DG.DemiEditor.DeEditorUtils;
using static War.Base.TableManager;
using static War.UI.TextSpacing;

namespace War.Base
{
    /// <summary>
    /// Summary description for Class1
    /// </summary>
    public partial class TableManager :Singleton<TableManager>
    {
        public TableManager()
        {
        }
        ConcurrentQueue<TableVO> queue = new ConcurrentQueue<TableVO>();
        Dictionary<string, TableVO> tableDic = new Dictionary<string, TableVO>();

        public static void Print(params object[] str)
        {
            "".Print(str);
        }
        public TableVO GetTableVO(string csv_name)
        {
            if (!tableDic.TryGetValue(csv_name, out var vO))
            {
                Print("GetTableVO", csv_name);
                return null;
            }

            return vO;
        }
        public int GetTableLen(string csv_name)
        {
            if (!tableDic.TryGetValue(csv_name, out var vO))
            {
                Print("GetTableLen err", csv_name);
                return 0;
            }

            return vO.values.Count;
        }
        public string GetTableInd(string csv_name, int r, string key,out int type)
        {
            r = r - 1;

            type = 0;

            if (!tableDic.TryGetValue(csv_name, out var vO))
            {
                Print("GetTableInd", csv_name);
                return "";
            }
            var c = vO.GetColLastInd(key);
            "".PrintThread(csv_name, r, key, c);
            string v = vO.values[r][c];


            if (v.Contains("\n\r"))
            {
                type = 1;
            }
            else
            {
                var ch = vO.colHeaders[c];
                if (!string.IsNullOrEmpty(ch.GetKey("array"))|| !string.IsNullOrEmpty(ch.GetKey("arraytype")))
                {
                    type = 2;
                }
            }

            return v;
        }
    //    name = "{name}",
    //type = "{type}",
    //desc = "{desc}",
    //filter = "{filter}",
    //array = "{array}",
    //arraytype = "{arraytype}",
    //index = "{index}"
        public int GetTableRowInd(string csv_name, int kind, string mkey)
        {
            if (!tableDic.TryGetValue(csv_name, out var vO))
            {
                Print("GetTableRowInd v0", csv_name);
                return -1;
            }
            if (vO.key2rows.Count <= kind)
            {
                Print("GetTableRowInd key2rows", csv_name, kind, mkey, vO.key2rows.Count);
            } 
            if (!vO.key2rows[kind].TryGetValue(mkey, out var vRow))
            {
                Print("GetTableRowInd vRow", csv_name,kind,mkey);
                return -1;
            }
            return vRow;
        }




        /////////////



        public void ToMainThread(System.Action ac)
        {
            EditorApplication.delayCall += () =>
            {
                ac();
            }; 
        } 
        
        
        private static DicCache __TryAddHeader(Dictionary<int, DicCache> colheaders, int index)
        {
            if (!colheaders.TryGetValue(index, out var ch))
            {
                ch = new DicCache();
                //ch.name = Path.GetFileName(f);
                colheaders[index] = ch;
            }

            return ch;
        }


        public void Exe(System.Action<object> cb=null)
        {
            FetchAllCSV((o) =>
            {
                BuildCsves(o, cb);
            });
        }


        public void BuildCsves(object o, System.Action<object> cb)
        {
            var count = 0;
            Init(o, (o) =>
            {
                //var list = new List<TableVO>();
                //var o = (object) list
                BuildHeaders(o, (o) =>
                {
                    //BuildCaches(queue, cb);
                }, (o) =>
                {
                    ToMainThread(() =>
                    {
                        count++;
                    });
                    BuildCache(o, (o) =>
                    {
                        var vo = (TableVO)o;
                        //"".PrintThread(vo.filename, UIHelper.ToJson(vo));

                        ToMainThread(() =>
                        {
                            count--;
                            if (count == 0)
                            {
                                cb?.Invoke(null);
                            }
                        });
                    });
                });
            });
        }


        public void BuildCache(object o, System.Action<object> cb)
        {
            var vo = (TableVO)o;


            string line;
            var star_row = vo.start_row;

            var listValue = new List<string>();
            var index = 0;
            Dictionary<string, int> key2index = null;

            var main_keyes = vo.main_keyes;
            List<Dictionary<string, int>> key2rows = vo.key2rows;
            foreach (var dic in main_keyes)
            {
                key2index = new Dictionary<string, int>();
                key2rows.Add(key2index);
            }
            int row_ind = 0;

            var col_cache = new DicCache();

            var colCount = vo.colHeaders.Count;
            var keylist = new List<string>();
            var sb = new StringBuilder();

            System.Func<object, int, bool> proc_col = (object o, int i) =>
            {
                if (row_ind == 0) return true;

                string col = o + "";

                //var ch = vo.colHeaders[i-1];
                //var name = ch.GetKey("name") ?? "";
                //var v = $"{col_cache.GetKey(name)}\n\r{col}";

                //col_cache.dic[name] = v;

                var lstr = vo.values[row_ind-1];
                lstr.Add(col);


                if (colCount == i)
                {
                    for (int i1 = 0; i1 < main_keyes.Count; i1++)
                    {
                        keylist.Clear();
                        List<KeyValuePair<string, int>> keyes = main_keyes[i1];
                        for (int i2 = 0; i2 < keyes.Count; i2++)
                        {
                            KeyValuePair<string, int> kp = keyes[i2];
                            keylist.Add(vo.values[row_ind - 1][kp.Value - 1]);
                        }
                        var mkey = string.Join("|", keylist);

                        //"".PrintThread(UIHelper.ToJson(keyes), mkey, UIHelper.ToJson(keylist));
                        key2rows[i1][mkey] = row_ind;
                    }
                }

                return true;
            };

            System.Func<object, int, bool> proc_line = (object o, int i) =>
            {
                col_cache.dic.Clear();
                //last_len += line.Length;
                if (--star_row > 0)
                {
                    return true;
                }

                row_ind++;

                if (vo.values.Count < row_ind)
                {
                    vo.values.Add(new List<string>());
                }
                return true;
            };
            vo.fileExe.Iter(vo, proc_line, proc_col);
            cb.Invoke(vo);
        }


        public void BuildCaches(object o, System.Action<object> cb)
        {

            ThreadScheduler.Instance.Schedule((IEnumerable<object>)o, (o) =>
            {
                var vo = (TableVO)o;

                BuildCache(vo, (o) =>
                {
                    if (o != null)
                    {
                        "".PrintThread(vo.filename, UIHelper.ToJson(vo));
                        //File.WriteAllText(vo.filename + ".json", UIHelper.ToJson(vo));
                    }
                });
            }, () =>
            {
                LogHelp.Instance.Log("BuildCache end");

                ToMainThread(() =>
                {
                    cb?.Invoke(null);
                });
            });
        }


        public void BuildHeaders(object o, System.Action<object> cb, System.Action<object> onecb =null)
        {
            ThreadScheduler.Instance.Schedule((IEnumerable<object>)o, (o) =>
            {
                var vo = (TableVO)o;

                FillHeader(vo, (o) =>
                {

                    if (o != null)
                    {
                        queue.Enqueue((TableVO)o);
                    }
                    onecb?.Invoke(vo);
                });
            }, () =>
            {
                LogHelp.Instance.Log("FillHeader end");

                ToMainThread(() =>
                {
                    cb(null);
                });
            });

        }


        public void FetchAllCSV(System.Action<object> cb)
        {
            string[] fs = GetCSVList();

            cb.Invoke(fs);
        }

        public string[] GetCSVList()
        {
            string path = GetCsvRoot();
            var tmp_path = "build/table/";
            string[] fs = Directory.GetFiles(path, "*");
            return fs;
        }

        public string GetCsvRoot()
        {
            var tmark = Enum.GetName(typeof(LuaManager.SEL_TABLE), LuaManager.Instance?.TabelMarkCn ?? LuaManager.SEL_TABLE.en_oumei);
            var path = $"../../Tools/csv_script/{tmark}";
            return path;
        }

        public void Init(object o, System.Action<object> cb)
        {
            //var tmark = Enum.GetName(typeof(LuaManager.SEL_TABLE), LuaManager.Instance?.TabelMarkCn ?? LuaManager.SEL_TABLE.cn); 
            //var path = $"../../Tools/csv_script/{tmark}";
            //var tmp_path = "build/table/";
            //string[] fs = Directory.GetFiles(path, "*");
            LogHelp.Instance.Log("FillHeader start");

            IEnumerable<string> fs = (IEnumerable<string>)o;

            var list = new List<TableVO>();
            foreach (var f in fs)
            {
                //if (f.Contains("HeroAdvance") == false) continue;
                //if (f.Contains("TrialTreasure") == false) continue;

                //if (f.Contains("ItemPr") == false) continue; 
                if (f.Contains("~"))
                {
                    continue;
                }
                var vo = new TableVO();
                vo.filename = f;
                vo.isfakecsv = EditorPrefs.GetBool($"Fake_CSV_{Path.GetFileNameWithoutExtension(vo.filename)}", false);

                if (f.Contains(".xls"))
                {
                    vo.fileExe = new XlsxExec();
                }
                else
                {
                    vo.fileExe = new CsvExec();
                }
                list.Add(vo);

                tableDic[Path.GetFileNameWithoutExtension(f)] = vo;
            }
            cb?.Invoke(list);
        }
 


        public void FillHeader(object o, System.Action<object> cb)
        {
            var vo = (TableVO)o;

            var tmp_path = "build/table/";
            string f = vo.filename;
            var t_f = tmp_path + Path.GetFileName(f);

            EditorHelp.CheckDir(t_f);

            //备份
            File.Copy(f, t_f, true);
            f = t_f;
            vo.filename = f;

            string line;
            var row = 0;
            var index = 0;
            var start_row = 0;
            var key = "";
            var value = "";

            var colheaders = new Dictionary<int, DicCache>();
            var indexs = new List<List<KeyValuePair<string, int>>>();
            List<KeyValuePair<string, int>> indexs_list = null;
            var keyMark = new Dictionary<string, string>();



            System.Func<object, int, bool> proc_col = (object o, int i) =>
            {

                string col = o + "";
                DicCache ch = null;

                if (i == 1)
                {
                    //var key = "";
                    Regex regex = new Regex(@"\{(\w+)\}(\w*)");
                    MatchCollection matches = regex.Matches(col);
                    foreach (Match match in matches)
                    {
                        key = match.Groups[1].Value;
                        if (match.Groups.Count > 1)
                        {
                            value = match.Groups[2].Value;
                        }
                    }
                    //"".PrintThread(f, matches.Count, key, value);
                    if (string.IsNullOrEmpty(key)) return true;

                    ++index;
                    ch = __TryAddHeader(colheaders, index);
                    ch.dic[key] = value;
                    if (key == "index")
                    {
                        indexs_list = new List<KeyValuePair<string, int>>();
                        indexs.Add(indexs_list);

                        if (string.IsNullOrEmpty(value) == false)
                        {
                            indexs_list.Add(new KeyValuePair<string, int>(ch.GetKey("name") ?? "null", index));
                            start_row = row;
                        }
                    }

                    //var name = ch.GetKey("name");
                    //if(string.IsNullOrEmpty(name) == false)
                    //if (keyMark.ContainsKey(name) == false)
                    //{
                    //    keyMark[name] = i;
                    //    ch.start = i;
                    //}
                }
                else
                {
                    ++index;
                    ch = __TryAddHeader(colheaders, index);

                    if (string.IsNullOrEmpty(col) == false)
                    {
                        if (key == "index")
                        {
                            indexs_list.Add(new KeyValuePair<string, int>(ch.GetKey("name") ?? "null", index));
                            start_row = row;
                        }
                    }

                    ch.dic[key] = col.Trim();

                    //var name = ch.GetKey("name");
                    //if(string.IsNullOrEmpty(name) == false)
                    //if (keyMark.ContainsKey(name) == false)
                    //{
                    //    keyMark[name] = i;
                    //    ch.start = i;
                    //}
                    //else
                    //{
                    //    ch.start = keyMark[name]; 
                    //}
                }
                return true;
            };

            System.Func<object, int, bool> proc_line = (object o, int i) =>
            {
                index = 0;
                key = "";
                value = "";
                if (++row > 10) return false;
                return true;
            };

            if (vo.fileExe == null) return;

            vo.fileExe.Iter(vo, proc_line, proc_col);

            vo.filename = f;
            vo.start_row = start_row + 1;
            for (int i = 0; i < colheaders.Count; i++)
            {
                if (colheaders.TryGetValue(i+1, out var colHeader))
                {
                    vo.colHeaders.Add(colHeader);
                }
            }
            vo.main_keyes = indexs;

            cb.Invoke(vo);
        }
    }

}
