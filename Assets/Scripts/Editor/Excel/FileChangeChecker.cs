using System;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;

namespace War.Base
{
    public class FileChangeChecker:Singleton<FileChangeChecker>
    { 
        HashSet<string> files = new HashSet<string>();

        public Dictionary<string,DateTime> modify_cache = new Dictionary<string,DateTime>();

        public System.Action<List<string>> on_change;

        public void Check(string filter = "*", params string[] dirs)
        {
            foreach (var dir in dirs)
            {
                var fs = Directory.GetFiles(dir, filter);
                foreach (var f in fs)
                {
                    files.Add(f);
                }
            }

            foreach (var f in files)
            {
                var finfo = new FileInfo(f);
                modify_cache[f] = finfo.LastWriteTimeUtc;
            }
 
            EditorApplication.update -= Tick;
            EditorApplication.update += Tick;
        }
         
        public void Dispose()
        {
            EditorApplication.update -= Tick;
            files.Clear();
        }

        DateTime dt;
        public void Tick()
        {
            //if (EditorApplication.isPlaying == false)
            //{
            //    EditorApplication.update -= Tick;
            //}

            if (DateTime.UtcNow - dt > TimeSpan.FromSeconds(3) )
            {
                dt = DateTime.UtcNow;
            }
            else
            {
                return;
            }

            if (files == null) return;

            try
            {
                var clist = new List<string>();
                var max = 1000;
                foreach (var f in files)
                {
                    if(--max < 0)
                    {
                        Debug.Log("break when reach max limit");
                        break;
                    }

                    modify_cache.TryGetValue(f, out var dateTime);

                    if (dateTime != null)
                    {
                        var finfo = new FileInfo(f);
                        if (finfo.LastWriteTimeUtc - dateTime > TimeSpan.FromSeconds(2))
                        {
                            "".Print("change", f);
                            clist.Add(f);
                            modify_cache[f] = finfo.LastWriteTimeUtc;
                        }
                    }
                }
                if (clist.Count > 0)
                {
                    //EditorApplication.update -= Tick;
                    on_change?.Invoke(clist);
                }
            }
            catch (Exception e)
            {
                EditorApplication.update -= Tick;
                Debug.LogException(e);
            }
        }
    }
}
