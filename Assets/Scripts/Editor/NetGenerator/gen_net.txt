function Exe()
    -- 配置这三个项-- 配置这三个项
    local client_path = clientPath
    -- local root_path = client_path .. '..\\..\\Src\\Proto\\Message\\arena.proto'
    local root_path = rootPath

    -- net.SendMessage 的两个参数
    local net_endPoint = 'net.Endpoint_Client, net.Endpoint_AccountLogin'
    -- local net_endPoint = "net.Endpoint_Client, net.Endpoint_Zone"

    local requires = {}
    -- {{key=, lines={}}, ...}
    local blocks = {}
    local this_blocks = {}

    local msg_header = nil

    function find(array, key)
        for i, v in ipairs(array) do
            if v.key == key then
                return v.lines
            end
        end
    end

    function process_block(path, include_pb)
        print(path)
        local file = io.open (path, 'r')
        if file == nil then return end

        local block = {}
        local msg_comment=''
        for line in file:lines() do
            local pb = string.match(line, '^import "(.+)%.proto"')
            if pb ~= nil then
                table.insert(requires, pb)
            end

            -- message或enum
            local msg = string.match(line, '^message%s+([%a%d_]+)') or string.match(line, '^enum%s+([%a%d_]+)')

            if msg ~= nil then
                if include_pb ~= nil then
                    line = line .. '	' .. include_pb
                end
                if msg_header ~= nil then	-- 容错。前面的没有正确的 } 结尾
                    table.insert(blocks, {key=msg_header, lines=block})
                    if include_pb == nil then
                        table.insert(this_blocks, {key=msg_header, lines=block})
                    end
                    --blocks[msg_header] = block
                end
                block = {}
                line = line .. msg_comment	-- 上一行注释拼接在一起
                msg_header = msg
            end

            if msg_header ~= nil then
                table.insert(block, line)
            end

            if string.match(line, '^}%s*$') ~= nil then
                --blocks[msg_header] = block
                table.insert(blocks, {key=msg_header, lines=block})
                if include_pb == nil then
                    table.insert(this_blocks, {key=msg_header, lines=block})
                end
                msg_header = nil
            end
            msg_comment = line
        end
        file:close()
    end

    -- 工作路径
    local base_path, module_name = string.match(root_path, '(.+\\)([^/\\]+)%.proto$')
    process_block(root_path)

    for i, v in ipairs(requires) do
        local path = string.gsub(root_path, '(.+\\)[^/\\]+(%.proto)$', '%1'..v..'%2')
        process_block(path, v..'_pb')
    end

    local lua_headers = {
        'local require = require',
        'local print = print',
        'local ipairs = ipairs',
        'local assert = assert',
        'local table = table',
        'local dump = dump',
        'local string = string',
        '',
        'module("%s")',
        '',
        'local event = require "event"',
        'local msg_pb = require "msg_pb"',
        'local %s = require "%s"',
        'local net = require "net"',
        'local net_route = require "net_route"\n',
    }

    -- 依赖项加进来
    for i,v in ipairs(requires) do
        table.insert(lua_headers, string.format('local %s = require "%s"', v .. '_pb', v .. '_pb'))
    end

    local lua_header = table.concat(lua_headers, '\n')
    lua_header = string.format(lua_header, 'net_'..module_name..'_module', module_name..'_pb', module_name..'_pb')

    local recv_funcs = {}
    local current_func = nil
    -- 是否是更新
    local r_file = io.open(client_path..'Assets\\Lua\\net_module\\' .. 'net_'..module_name..'_module.txt', 'r')
    if r_file ~= nil then
        lua_headers = {}
        local is_header = true
        for line in r_file:lines() do
            if string.match(line, '^end') then
                current_func = nil
            end

            if current_func ~= nil then
                table.insert(recv_funcs[current_func], line)
            end

            if string.match(line, '^--@region ') then
                is_header = false
                --table.remove(lua_headers, #lua_headers)
            end
            local recv_func_name = string.match(line, '^function (%a%a%a%a_[%w_]+)')	-- Send_ Recv_
            if recv_func_name ~= nil then
                is_header = false
                --table.remove(lua_headers, #lua_headers)

                recv_funcs[recv_func_name] = {}
                current_func = recv_func_name
            end

            if is_header then
                table.insert(lua_headers, line)
            end
        end
        lua_header = table.concat(lua_headers, '\n')
        r_file:close()
    end


    local pattern = '%s*(%a+)%s*([%w_]+)%s*([%w_]+)%s*='
    local comment_dependency = nil
    comment_dependency = function(out, msg_name, processed, fields)
        local define_type = {}
        local lines = find(blocks, msg_name)
        if --[[blocks[msg_name] ]]lines ~= nil and not processed[msg_name] then
            processed[msg_name] = true
            for i, v in ipairs(--[[blocks[msg_name] ]]lines) do
                table.insert(out, v)

                local _8, _type, field = string.match(v, pattern)
                if (_8 == 'required' or _8 == 'repeated' or _8 == 'optional') then
                    table.insert(define_type, _type)
                    if fields then
                        table.insert(fields, field)
                    end
                end
            end

            for i, v in ipairs(define_type) do
                if v ~= 'EnErrorCode' then
                    comment_dependency(out, v, processed, nil)
                end
            end
        end
    end

    -- 保留原来的Send代码
    function build_send(full_msg, msg)
        local send = {
            '\n\n--@region \n--[[',
            '%s',
            '--]]\n--@endregion',

            'function %s(%s)',
            '    local msg = %s.%s()',
            '    net.SendMessage(%s, 0, msg_pb.%s, msg)',
            'end'
        }

        local msg_prefix = 'Send_' .. msg
        if recv_funcs[msg_prefix] ~= nil then
            table.remove(send, 6)
            table.remove(send, 5)

            for i, v in ipairs(recv_funcs[msg_prefix]) do
                table.insert(send, 4+i, v)
            end
        end

        local lines = {}
        local fields = {}
        comment_dependency(lines, full_msg, {}, fields)
        for i, v in ipairs(fields) do
            local comment = ''
            for _i, _v in ipairs(lines) do
                if string.find(_v, v) then
                    comment = string.gsub(_v, '[^/]*//([^\r\n]+)', '--%1')
                    break
                end
            end

            if recv_funcs[msg_prefix] == nil then
                table.insert(send, 6, string.format('    msg.%s = %s', v, v, comment))
            end
        end

        local concated = table.concat(send, '\n')
        lua_header = lua_header .. string.format(concated, table.concat(lines, '\n'), 'Send_'..msg, table.concat(fields, ', '), module_name..'_pb', full_msg, net_endPoint, string.sub(full_msg, 2))
    end

    local recv = {
        '\n\n--@region \n--[[',
        '%s',
        '--]]\n--@endregion',

        'function %s(msg)',
        '%s',
        'end'
    }

    function build_recv(full_msg, msg)
        local concated = table.concat(recv, '\n')
        local lines = {}
        comment_dependency(lines, full_msg, {})
        lua_header = lua_header .. string.format(concated, table.concat(lines, '\n'), 'Recv_'..msg, table.concat(recv_funcs['Recv_' .. msg] or {}, '\n'))
    end

    local recv_msg = {}
    for _k, _v in ipairs(this_blocks) do
        local k = _v.key
        local v = _v.lines
        local msg_name, REQ_RSP = string.match(k, '^TMSG_([%w_]+)_([%w]+)$')
        if msg_name ~= nil then
            msg_name = msg_name .. '_' .. REQ_RSP
            --print(msg_name, REQ_RSP)
            if REQ_RSP == "REQ" then
                build_send(k, msg_name)
            elseif REQ_RSP == "RSP" or REQ_RSP == "NTF" then
                build_recv(k, msg_name)
                table.insert(recv_msg, {k, msg_name})
            else
                print('unrecognize', k)
            end
        end
    end

    function padding(str, len)
        len = len or 34
        return str .. string.rep(' ', len - string.len(str))
    end

    local tail = {
        '\n\n-- /// 注册消息',
        '-------------------------------------------------------------------',
        'local MessageTable = ',
        '{',
        '}',
        '',
        'net_route.RegisterMsgHandlers(MessageTable)',
    }
    local register = '	{msg_pb.%s,			%s,			%s.%s},'
    for i, v in ipairs(recv_msg) do
        table.insert(tail, 5, string.format(register, padding(string.sub(v[1], 2)), padding('Recv_'..v[2]), module_name..'_pb', v[1]))
    end
    lua_header = lua_header .. table.concat(tail, "\n")


    local w_file = io.open(client_path..'Assets\\Lua\\net_module\\' .. 'net_'..module_name..'_module.txt', 'w')
    w_file:write(lua_header)
    w_file:close()
    CS.UnityEditor.AssetDatabase.Refresh()
end
