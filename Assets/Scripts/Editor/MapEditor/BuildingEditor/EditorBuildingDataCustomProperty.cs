using UnityEditor;
using UnityEngine;
using War.Base;
using War.Script;

[CustomEditor(typeof(EditorBuildingData))]
public class EditorBuildingDataCustomProperty : Editor
{
    public override void OnInspectorGUI()
    {
        EditorGUI.BeginChangeCheck();
        serializedObject.Update();
        var property = serializedObject.FindProperty("Name");
        EditorGUILayout.PropertyField(property, new GUIContent("城池名称"));
        property = serializedObject.FindProperty("Id");
        EditorGUILayout.PropertyField(property, new GUIContent("城池ID"));
        property = serializedObject.FindProperty("CityLevel");
        EditorGUILayout.PropertyField(property, new GUIContent("城池等级"));
        property = serializedObject.FindProperty("CityType");
        EditorGUILayout.PropertyField(property, new GUIContent("城池类型"));
        property = serializedObject.FindProperty("CityBuff");
        EditorGUILayout.PropertyField(property, new GUIContent("城池Buff"));
        property = serializedObject.FindProperty("NoCome");
        EditorGUILayout.PropertyField(property, new GUIContent("禁止迁城范围"));
        property = serializedObject.FindProperty("FirstreWard");
        EditorGUILayout.PropertyField(property, new GUIContent("首占奖励"));
        property = serializedObject.FindProperty("Power");
        EditorGUILayout.PropertyField(property, new GUIContent("推荐战力"));
        property = serializedObject.FindProperty("Defenders");
        EditorGUILayout.PropertyField(property, new GUIContent("守军配置"));
        property = serializedObject.FindProperty("CityHP");
        EditorGUILayout.PropertyField(property, new GUIContent("城池耐久"));
        property = serializedObject.FindProperty("Condition");
        EditorGUILayout.PropertyField(property, new GUIContent("解锁条件"));
        
        
        property = serializedObject.FindProperty("Size");
        EditorGUILayout.PropertyField(property, new GUIContent("大小"));
        property = serializedObject.FindProperty("OffSet");
        EditorGUILayout.PropertyField(property, new GUIContent("偏移"));
        property = serializedObject.FindProperty("ModelPath");

        var path = property.stringValue;
        if (string.IsNullOrEmpty(path))
        {
            //为null时给个默认只
            property.stringValue = "editorconfig/mapeditorconfig/buildingmodle/defaultbuild.prefab";
            //property.stringValue = "EditorConfig/MapEditorConfig/BuildingModle/defaultBuild.prefab";
        }
        //ab路径处理
        var editorModelPath = AssetDatabase.GetAssetPathsFromAssetBundle(path);
        GameObject model = null;
        if (editorModelPath != null && editorModelPath.Length > 0)
        {
            model = AssetDatabase.LoadAssetAtPath<GameObject>(editorModelPath[0]);
        }

        var newModel = (GameObject)EditorGUILayout.ObjectField("模型", model, typeof(GameObject), false);
        if (newModel)
        {
            if (newModel != model)
            {
                model = newModel;
                path = AssetDatabase.GetAssetPath(model);
                path = EditorUtil.GetAssetBundlePath(path);
                property.stringValue = path;
            }
        }

        if (EditorGUI.EndChangeCheck())
        {
            serializedObject.ApplyModifiedProperties();
        }
    }
}