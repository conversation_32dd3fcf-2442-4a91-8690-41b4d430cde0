using System;
using System.Collections.Generic;
using System.Reflection;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine;
using UnityEngine.SceneManagement;

[System.Serializable]
public class BuildingEditorWindow : Editor
{
    // private const string BUILDINGEDITORSCENE = "Assets/Scenes/BuildingEditorScene.unity";

    private const string BUILDPREFABPATH = "Assets/EditorConfig/MapEditorConfig/BuildIng.prefab";

    // [MenuItem("Tool/BuildingEditor")]
    // private static void ShowWindow()
    // {
    //     // CheckScene();
    //     var window = GetWindow<BuildingEditorWindow>();
    //     window.titleContent = new GUIContent("BuildingEditor");
    //     window.Show();
    // }

    // private static void CheckScene()
    // {
    //     if (EditorApplication.isPlaying)
    //     {
    //         return;
    //     }
    //
    //     if (!BUILDINGEDITORSCENE.Equals(SceneManager.GetActiveScene().path))
    //     {
    //         EditorSceneManager.OpenScene(BUILDINGEDITORSCENE);
    //     }
    //
    //     EditorApplication.isPlaying = true;
    // }

    private Vector2 scroll;
    private List<EditorBuildingData> m_buildingDatas;
    private EditorBuildingData m_selectData;
    private EditorBuildingDataTemp m_tempData;
    private string m_savePath;
    public string SavePath => EditorUtil.GetAssetPath(m_savePath);

    //预览相关
    private GameObject m_buildPreviewGo;
    private GameObject m_modelObj;

    public event Action<EditorBuildingData> OnBuildingDelete;

    public void Awake()
    {
        m_savePath = @"Assets/EditorConfig/MapEditorConfig";
        if (!m_tempData)
        {
            m_savePath = EditorUtil.GetAssetPath(m_savePath);
            m_tempData = AssetDatabase.LoadAssetAtPath<EditorBuildingDataTemp>(m_savePath + "/BuildingDataTemp.asset");
            if (!m_tempData)
            {
                m_tempData = ScriptableObject.CreateInstance<EditorBuildingDataTemp>();
                AssetDatabase.CreateAsset(m_tempData, m_savePath + "/BuildingDataTemp.asset");
                return;
            }

            m_buildingDatas = new List<EditorBuildingData>();
            m_buildingDatas.AddRange(m_tempData.BuildingDatas);
        }
    }

    private void OnGUI()
    {
        EditorGUILayout.BeginHorizontal();
        // DrawBuildList();
        DrawBuildDesc();
        EditorGUILayout.EndHorizontal();
        DrawBottomButton();
    }

    public void DrawBuildList(Action<EditorBuildingData> onClick)
    {
        // GUILayout.BeginArea(new Rect(Vector2.zero, new Vector2(200, position.height - 100)));
        GUILayout.Space(5);
        EditorGUILayout.BeginVertical();
        GUILayout.Label("选择建筑");

        //绘制建筑列表
        scroll = EditorGUILayout.BeginScrollView(scroll);

        if (m_buildingDatas != null)
        {
            foreach (var item in m_buildingDatas)
            {
                if (!item) continue;
                if (GUILayout.Button($"{item.Name}_Lv{item.CityLevel}"))
                {
                    OnBuildHandle(item);
                    onClick?.Invoke(item);
                }
            }
        }

        EditorGUILayout.EndScrollView();

        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("+"))
        {
            AddBuildingData();
        }

        if (GUILayout.Button("-"))
        {
            RemoveBuildingData();
        }

        EditorGUILayout.EndHorizontal();

        EditorGUILayout.EndVertical();

        // GUILayout.EndArea();
    }

    private void OnBuildHandle(EditorBuildingData item)
    {
        // Debug.Log($"点击{item.BuildingId}");
        m_selectData = item;
    }

    public void DrawBuildDesc()
    {
        GUILayout.Space(5);
        EditorGUILayout.BeginVertical();
        if (!m_selectData)
        {
            return;
        }

        EditorUtil.DrawSo(m_selectData);

        EditorGUILayout.BeginHorizontal();
        // if (GUILayout.Button("预览"))
        // {
        //     PreviewBuilding();
        // }

        EditorGUILayout.EndHorizontal();

        EditorGUILayout.EndVertical();
    }

    public void DrawBottomButton()
    {
        // GUILayout.BeginArea(new Rect(new Vector2(0, position.height - 100), new Vector2(position.width, 100)));
        EditorGUILayout.BeginHorizontal();
        GUILayout.Label("临时保存路径");
        GUILayout.Label(m_savePath);
        // if (GUILayout.Button("选择路径"))
        // {
        //     var newPath = EditorUtility.OpenFolderPanel("选择路径", "", "");
        //     if (string.IsNullOrEmpty(m_savePath) || m_savePath != newPath)
        //     {
        //         EditorPrefs.SetString("BuildingEditorTempSavePath", newPath);
        //         m_savePath = newPath;
        //         m_tempData =
        //             AssetDatabase.LoadAssetAtPath<EditorBuildingDataTemp>(m_savePath + "/BuildingDataTemp.asset");
        //     }
        // }

        // if (GUILayout.Button("创建所需文件"))
        // {
        //     var path = EditorUtil.GetAssetPath(m_savePath);
        //
        //     m_tempData = ScriptableObject.CreateInstance<EditorBuildingDataTemp>();
        //     AssetDatabase.CreateAsset(m_tempData, path + "/BuildingDataTemp.asset");
        // }

        EditorGUILayout.EndHorizontal();

        EditorGUILayout.BeginHorizontal();
        // if (GUILayout.Button("导出csv"))
        // {
        //     ExportCsv();
        // }
        //
        // if (GUILayout.Button("导出Json"))
        // {
        //     ExportJson();
        // }
        //
        // if (GUILayout.Button("导入csv"))
        // {
        //     ImportCsv();
        // }
        //
        // if (GUILayout.Button("导入json"))
        // {
        //     ImportJson();
        // }

        if (GUILayout.Button("临时保存建筑"))
        {
            SaveBuilding();
        }

        EditorGUILayout.EndHorizontal();
        // GUILayout.EndArea();
    }

    public void ImportCsv()
    {
        var exportPath = EditorUtility.OpenFilePanel("选择文件", "", "csv");
        List<EditorBuildingData> list = EditorUtil.ImportCsv<List<EditorBuildingData>>(exportPath);
        if (list == null || list.Count == 0)
        {
            EditorUtility.DisplayDialog("提示", "导入失败", "确定");
            return;
        }

        //删除旧数据
        foreach (var data in m_buildingDatas)
        {
            AssetDatabase.DeleteAsset(AssetDatabase.GetAssetPath(data));
        }

        m_buildingDatas.Clear();
        m_selectData = null;
        m_buildingDatas.AddRange(list);
        EditorUtility.DisplayDialog("提示", "导入成功", "确定");
    }

    private void AddBuildingData()
    {
        m_buildingDatas.Add(ScriptableObject.CreateInstance<EditorBuildingData>());
    }

    private void RemoveBuildingData()
    {
        if (!m_selectData) return;
        m_buildingDatas.Remove(m_selectData);
        AssetDatabase.DeleteAsset(AssetDatabase.GetAssetPath(m_selectData));
        OnBuildingDelete?.Invoke(m_selectData);
        m_selectData = null;
    }

    #region 导入导出相关

    private void ImportJson()
    {
        var exportPath = EditorUtility.OpenFilePanel("选择文件", "", "json");
        List<EditorBuildingData> list = EditorUtil.ImportJson<List<EditorBuildingData>>(exportPath);
        if (list == null || list.Count == 0)
        {
            EditorUtility.DisplayDialog("提示", "导入失败", "确定");
            return;
        }

        //删除旧数据
        foreach (var data in m_buildingDatas)
        {
            AssetDatabase.DeleteAsset(AssetDatabase.GetAssetPath(data));
        }

        m_buildingDatas.Clear();
        m_selectData = null;
        m_buildingDatas.AddRange(list);
        EditorUtility.DisplayDialog("提示", "导入成功", "确定");
    }

    private void ExportJson()
    {
        var exportPath = EditorUtility.SaveFilePanel("选择路径", "", "BuildingData.json", "json");
        EditorUtility.DisplayDialog("提示",
            EditorUtil.ExportJson(exportPath, m_buildingDatas) ? "导出成功" : "导出失败", "确定");
    }

    public void ExportCsv()
    {
        var exportPath = EditorUtility.SaveFilePanel("选择路径", "", "BuildingData.csv", "csv");
        EditorUtility.DisplayDialog("提示",
            EditorUtil.ExportCsv(exportPath, m_buildingDatas) ? "导出成功" : "导出失败", "确定");
    }

    private void SaveBuilding()
    {
        if (!m_tempData) return;
        m_tempData.BuildingDatas.Clear();
        m_tempData.BuildingDatas.AddRange(m_buildingDatas);

        foreach (var data in m_tempData.BuildingDatas)
        {
            if (string.IsNullOrEmpty(AssetDatabase.GetAssetPath(data)))
            {
                if (data.Id <= 0)
                {
                    continue;
                }

                if (!AssetDatabase.IsValidFolder($"{EditorUtil.GetAssetPath(m_savePath)}/BuildingConfig"))
                {
                    AssetDatabase.CreateFolder(EditorUtil.GetAssetPath(m_savePath), "BuildingConfig");
                }

                AssetDatabase.CreateAsset(data,
                    $"{EditorUtil.GetAssetPath(m_savePath)}/BuildingConfig/{data.Id}_{data.CityLevel}.asset");
            }
        }

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
    }

    #endregion

    private void PreviewBuilding()
    {
        // CheckScene();
        var rootRt = GameObject.Find("m_preView").transform;
        if (!rootRt)
        {
            Debug.LogError("找不到m_preView");
            return;
        }

        if (m_modelObj)
        {
            GameObject.Destroy(m_modelObj);
            m_modelObj = null;
        }

        GameObject buildPrefab = null;
        //不存在创建
        if (!m_buildPreviewGo)
        {
            buildPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(BUILDPREFABPATH);
            if (!buildPrefab)
            {
                Debug.LogError("找不到预览预制体");
                return;
            }

            m_buildPreviewGo = GameObject.Instantiate(buildPrefab, rootRt);
        }

        var editorModelPath = AssetDatabase.GetAssetPathsFromAssetBundle(m_selectData.ModelPath);

        if (editorModelPath == null || editorModelPath.Length <= 0)
        {
            Debug.LogError("找不到模型");
            return;
        }

        var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(editorModelPath[0]);
        if (!prefab)
        {
            Debug.LogError("找不到模型");
            return;
        }

        var model = m_buildPreviewGo.transform.Find("Model");
        if (!model)
        {
            Debug.LogError("建筑预制体错误");
        }

        m_modelObj = GameObject.Instantiate(prefab, model);
    }

    private void OnDestroy()
    {
        if (m_buildPreviewGo)
        {
            GameObject.Destroy(m_buildPreviewGo);
        }

        if (m_modelObj)
        {
            GameObject.Destroy(m_modelObj);
        }
    }

    public void ReSetSelectBuild()
    {
        m_selectData = null;
    }

    public EditorBuildingData GetBuildingData(int cityID, int cityLevel)
    {
        foreach (var data in m_buildingDatas)
        {
            if (data.CityLevel == cityLevel && data.Id == cityID)
            {
                return data;
            }
        }

        return null;
    }
}