using System.Collections.Generic;
using Newtonsoft.Json;
using UnityEngine;

public enum CityType
{
    Gold,
    Farm
}

[System.Serializable]
public class EditorBuildingData : ScriptableObject
{
    [ExportCsv("城池id", true, CsvFilter.sc)]
    public int Id;
    [ExportCsv("城池等级", true, CsvFilter.sc)] 
    public int CityLevel;
    [ExportCsv("名称", false, CsvFilter.None)] 
    public string Name;
    [ExportCsv("城池类型", false, CsvFilter.sc)]
    public CityType CityType;
    [ExportCsv("城池Buffs", false, CsvFilter.sc)]
    public string CityBuff;
    [ExportCsv("禁止迁城范围", false, CsvFilter.sc)]
    public int NoCome;
    [ExportCsv("首占奖励", false, CsvFilter.sc)]
    public string FirstreWard;
    [ExportCsv("推荐战力", false, CsvFilter.sc)]
    public int Power;
    [ExportCsv("守军配置", false, CsvFilter.sc)]
    public string Defenders;
    [ExportCsv("城池耐久", false, CsvFilter.sc)]
    public int CityHP;
    [ExportCsv("解锁条件", false, CsvFilter.sc)]
    public string Condition;
    
    [ExportCsv("大小", false, CsvFilter.sc)] 
    public int Size;
    [ExportCsv("偏移", false, CsvFilter.c)] 
    public Vector2 OffSet;
    [ExportCsv("模型路径", false, CsvFilter.c)]
    public string ModelPath;
}