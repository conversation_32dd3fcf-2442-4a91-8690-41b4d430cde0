using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;

public static class MathUtil 
{
    public static List<Vector2> GetLinePoints(int x0, int y0, int x1, int y1)
    {
        List<Vector2> points = new List<Vector2>();
        
        int dx = Mathf.Abs(x1 - x0);
        int dy = Mathf.Abs(y1 - y0);
        
        int sx = x0 < x1 ? 1 : -1;
        int sy = y0 < y1 ? 1 : -1;
        int err = dx - dy;
        
        while (true)
        {
            points.Add(new Vector2(x0, y0));
            if (x0 == x1 && y0 == y1)
                break;
            int e2 = 2 * err;
            if (e2 > -dy)
            {
                err -= dy;
                x0 += sx;
            }
            if (e2 < dx)
            {
                err += dx;
                y0 += sy;
            }
        }
        return points;
    }
    
    public static bool IsPointInside( Vector2[] points, Vector2 point)
    {
        bool isInside = false;
        for (int i = 0, j = points.Length - 1; i < points.Length; j = i++)
        {
            if (((points[i].y > point.y) != (points[j].y > point.y)) &&
                (point.x < (points[j].x - points[i].x) * (point.y - points[i].y) / (points[j].y - points[i].y) + points[i].x))
            {
                isInside = !isInside;
            }
        }

        return isInside;
    }
    
    /// <summary> 
    /// 判断点是否在多边形内. 
    /// ----------原理---------- 
    /// 注意到如果从P作水平向左的射线的话，如果P在多边形内部，那么这条射线与多边形的交点必为奇数， 
    /// 如果P在多边形外部，则交点个数必为偶数(0也在内)。 
    /// 所以，我们可以顺序考虑多边形的每条边，求出交点的总个数。还有一些特殊情况要考虑。假如考虑边(P1,P2)， 
    /// 1)如果射线正好穿过P1或者P2,那么这个交点会被算作2次，处理办法是如果P的从坐标与P1,P2中较小的纵坐标相同，则直接忽略这种情况 
    /// 2)如果射线水平，则射线要么与其无交点，要么有无数个，这种情况也直接忽略。 
    /// 3)如果射线竖直，而P0的横坐标小于P1,P2的横坐标，则必然相交。 
    /// 4)再判断相交之前，先判断P是否在边(P1,P2)的上面，如果在，则直接得出结论：P再多边形内部。 
    /// </summary> 
    /// <param name="checkPoint">要判断的点</param> 
    /// <param name="polygonPoints">多边形的顶点</param> 
    /// <returns></returns> 
    public static bool IsInPolygon( List<Vector2> polygonPoints,Vector2 checkPoint)
    {
        int counter = 0;
        int i;
        double xinters;
        Vector2 p1, p2;
        int pointCount = polygonPoints.Count;
        p1 = polygonPoints[0];
        for (i = 1; i <= pointCount; i++)
        {
            p2 = polygonPoints[i % pointCount];
            if (checkPoint.y > Math.Min(p1.y, p2.y)//校验点的Y大于线段端点的最小Y 
                && checkPoint.y <= Math.Max(p1.y, p2.y))//校验点的Y小于线段端点的最大Y 
            {
                if (checkPoint.x <= Math.Max(p1.x, p2.x))//校验点的X小于等线段端点的最大X(使用校验点的左射线判断). 
                {
                    if (p1.y != p2.y)//线段不平行于X轴 
                    {
                        xinters = (checkPoint.y - p1.y) * (p2.x - p1.x) / (p2.y - p1.y) + p1.x;
                        if (p1.x == p2.x || checkPoint.x <= xinters)
                        {
                            counter++;
                        }
                    }
                }
 
            }
            p1 = p2;
        }
        return counter % 2 == 1;
    }
    //获取多边形内所有的点
    public static List<Vector2> IntegerPointsInsidePolygon(List<Vector2> vertices)
    {
        List<Vector2> integerPoints = new List<Vector2>();
        if (vertices.Count < 3)
        {
            return integerPoints; // 空多边形
        }
        // 找到包围盒
        float minX = vertices[0].x;
        float maxX = vertices[0].x;
        float minY = vertices[0].y;
        float maxY = vertices[0].y;
        foreach (var vertex in vertices)
        {
            minX = Mathf.Min(minX, vertex.x);
            maxX = Mathf.Max(maxX, vertex.x);
            minY = Mathf.Min(minY, vertex.y);
            maxY = Mathf.Max(maxY, vertex.y);
        }

        // 遍历包围盒中的所有整数点
        for (int x = Mathf.CeilToInt(minX); x <= Mathf.FloorToInt(maxX); x++)
        {
            for (int y = Mathf.CeilToInt(minY); y <= Mathf.FloorToInt(maxY); y++)
            {
                Vector2 point = new Vector2(x, y);
                if (IsInPolygon(vertices,point))
                {
                    integerPoints.Add(point);
                }
            }
        }
        return integerPoints;
    }
    //经过边长线上的所有点
    public static List<Vector2> GetPointInLines(List<Vector2> vertices)
    {
        List<Vector2> integerPoints = new List<Vector2>();
        if (vertices.Count < 3)
        {
            return integerPoints; // 空多边形
        }

        Vector2 first = vertices[0];
        for (int i = 1; i < vertices.Count; i++)
        {
            var points = GetLinePoints((int)first.x, (int)first.y, (int)vertices[i].x, (int)vertices[i].y);
            foreach (var p in points)
            {
                if (!integerPoints.Contains(p))
                {
                    integerPoints.Add(p);
                }
            }
        }
        return integerPoints;
    }
    
    
    /// <summary>
    /// 踩着一个密闭多边形的边沿绕一圈，以这些点构造多边形,迷宫右手走法
    /// 采用右手摸墙法则
    /// </summary>
    /// <param name="data"></param>
    /// <param name="area">封闭区域的minX maxX minY maxY</param>
    /// <returns></returns>
    public static List<Vector2Int> GetPolygonPath(byte[,] data,int minX,int maxX,int minY,int maxY,bool getAllPoint = false)
    {
       //先取到第一个点 取值（minx,minY）
       Vector2Int frist = default;
       Vector2Int curPos = default;
       int xLength = data.GetLength(0);
       int yLength = data.GetLength(1);
       // 检查相邻的四个方向
       var directions = new Vector2Int[]
       {
           Vector2Int.left, Vector2Int.up,
           Vector2Int.right,Vector2Int.down, 
       };
       
       int forward = 2; 
       for (int i = minX; i < maxX; i++) //第一行
       {
           if (data[i, minY] != 0)
           {
               frist.x = i ;
               frist.y = minY;
           }
       }
       List<Vector2Int> ret = new List<Vector2Int>(32);
       bool isFrist = true;
       Vector2Int prePos = -Vector2Int.one;
       int lastMoveForward = forward;
       while (frist != curPos )
       {
           if (isFrist)
           {
               curPos = frist;
               isFrist = false;
           }
           // 注意当前如果为0则表示靠墙
           //1，检测右手边是否靠墙,
           // 2，右手如果不靠墙，则右转,
           var pos =  curPos +  directions[GetRightForward(forward)];
           if (!IsObstacle(data,pos.x,pos.y,xLength,yLength))
           {
               //右侧为空，则右转
               forward = GetRightForward(forward);
           }
           //3，判断前行,如果可以，则直接前行，不可则一直走转判断
           pos = curPos +  directions[forward];
           while (IsObstacle(data,pos.x,pos.y,xLength,yLength))
           {
               //前方障碍，就左转
               forward -= 1;
               if (forward < 0)
                   forward += 4;
               pos = curPos +  directions[forward];
           }
           curPos = pos;
           //判断一下，如果和上次的方向未变，则不添加，防止重复添加，
           if (!getAllPoint)
           {
               if (lastMoveForward != forward)
               {
                   if(prePos != -Vector2Int.one)
                    ret.Add(prePos);
               }
           }
           else
           {
               ret.Add(curPos);
           }
           prePos = curPos;
           lastMoveForward = forward;
       }
       if (!getAllPoint)
       {
           //添加起点，防止重复添加
           if(!ret.Contains(curPos))
               ret.Add(curPos);
       }
       return ret;
    }
    static int GetRightForward(int forward)
    {
        forward += 1;
        if (forward > 3)
            forward -= 4;
        return forward;
    }

    static bool IsObstacle(byte[,] data,int x,int y,int xLength,int yLength)
    {
        if (x < 0 || x >= xLength
                  || y < 0 || y >= yLength)
            return true;
        return data[x, y] == 0;
    }
}
