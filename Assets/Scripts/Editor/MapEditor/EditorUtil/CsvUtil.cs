/*******************************************************
 * Description:   地图编辑器源数据对象
 * 和改艳确认过,目前的表结构支持的基础数据类型只有 int 和 string ,以及他们的列表和数组,不支持数组&列表内嵌套数组&列表
 * 基于上面的逻辑修改一下
 * Author:        未知作者&袁楠
 * Created Date:  2024年8月6日
 ********************************************************/

using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Text;
using UnityEngine;

public enum CsvColumnType
{
    name,
    type,
    desc,
    filter,
    array,
    arraytype,
    index,
}

public class CsvHeader
{
    public string Name;
    public CsvFilter Filter;
    public int Index;
}

public static class CsvUtil
{
    #region 导出相关

    // ReSharper disable Unity.PerformanceAnalysis
    public static bool ExportCsv(string path, IEnumerable list)
    {
        if (string.IsNullOrEmpty(path) && list == null) return false;
        try
        {
            var stringBuilder = new StringBuilder();
            var elementType = list.GetType().GetGenericArguments()[0];
            var fieldInfos = elementType.GetFields();
            var csvInfos = new Dictionary<FieldInfo, ExportCsvAttribute>();
            foreach (var property in fieldInfos)
            {
                if (CheckCsvFieldType(property))
                {
                    var attr = (ExportCsvAttribute)property.GetCustomAttribute(typeof(ExportCsvAttribute));
                    if (attr == null) continue;
                    csvInfos.Add(property, attr);
                }
            }

            foreach (CsvColumnType item in Enum.GetValues(typeof(CsvColumnType)))
            {
                stringBuilder.Append($"{{{Enum.GetName(typeof(CsvColumnType), item)}}}");
                foreach (var info in csvInfos)
                {
                    switch (item)
                    {
                        case CsvColumnType.name:
                            HandleColumnName(info.Key, stringBuilder);
                            break;
                        case CsvColumnType.type:
                            HandleColumnType(info.Key, stringBuilder);
                            break;
                        case CsvColumnType.desc:
                            HandleColumnDesc(info.Value, stringBuilder);
                            break;
                        case CsvColumnType.filter:
                            HandleColumnFilter(info.Value, stringBuilder);
                            break;
                        case CsvColumnType.array:
                            HandleColumnArray(info.Key, info.Value, stringBuilder);
                            break;
                        case CsvColumnType.arraytype:
                            HandleColumnArrayType(info.Key, stringBuilder);
                            break;
                        case CsvColumnType.index:
                            HandleColumnIndex(info.Value, stringBuilder);
                            break;
                    }
                }

                stringBuilder.Remove(stringBuilder.Length - 1, 1);
                stringBuilder.AppendLine();
            }

            //data
            foreach (var item in list)
            {
                foreach (var fieldInfo in csvInfos.Keys)
                {
                    HandleData(item, fieldInfo, stringBuilder);
                }
                stringBuilder.Remove(stringBuilder.Length - 1, 1);
                stringBuilder.AppendLine();
            }
            var str = stringBuilder.ToString();
            File.WriteAllText(path, str);
            return true;
        }
        catch (Exception e)
        {
            Debug.LogError($"[ExportCsv] Error : {e.Message}");
            return false;
        }
    }

    private static bool CheckCsvFieldType(FieldInfo fieldInfo)
    {
        Type fieldType = fieldInfo.FieldType;
        if (fieldType.IsEnum || fieldType == typeof(int) || fieldType == typeof(string) || fieldType == typeof(int[]) ||
            fieldType == typeof(string[]))
        {
            return true;
        }
        else if (fieldType.IsGenericType && fieldType.GetGenericTypeDefinition() == typeof(List<>))
        {
            Type elementType = fieldType.GetGenericArguments()[0];
            return elementType == typeof(int) || elementType == typeof(string);
        }
        else
        {
            return false;
        }
    }

    private static bool CheckCsvFieldTypeIsArray(FieldInfo fieldInfo)
    {
        Type fieldType = fieldInfo.FieldType;
        return fieldInfo.FieldType.IsArray ||
               (typeof(IEnumerable).IsAssignableFrom(fieldType) && fieldType != typeof(string));
    }
    
    private static string GetCsvFieldTypeString(FieldInfo fieldInfo)
    {
        Type fieldType = fieldInfo.FieldType;
        if (fieldType.IsEnum || fieldType == typeof(int) || fieldType == typeof(int[]))
        {
            return "int32";
        }
        else if (fieldType == typeof(string) || fieldType == typeof(string[]))
        {
            return "string";
        }

        return string.Empty;
    }

    private static void HandleData(object item, FieldInfo fieldInfo, StringBuilder stringBuilder)
    {
        if (CheckCsvFieldTypeIsArray(fieldInfo) )
        {
            var fieldValue = fieldInfo.GetValue(item);
            if (fieldValue is IEnumerable enumerable)
            {
                foreach (var arrayItem in enumerable)
                {
                    stringBuilder.Append($"{arrayItem}#");
                }
            }
        }
        else if (fieldInfo.FieldType.IsEnum)
        {
            var value = (int)fieldInfo.GetValue(item);
            stringBuilder.Append($"{value},");
        }
        else
        {
            stringBuilder.Append($"{fieldInfo.GetValue(item)},");
        }
    }

    private static void HandleColumnIndex(ExportCsvAttribute attribute, StringBuilder stringBuilder)
    {
        stringBuilder.Append(attribute.IsIndex ? "1," : ",");
    }

    private static void HandleColumnArrayType(FieldInfo fieldInfo, StringBuilder stringBuilder)
    {
        if (CheckCsvFieldTypeIsArray(fieldInfo))
        {
            HandleColumnType(fieldInfo, stringBuilder);
        }
        else
        {
            stringBuilder.Append(",");
        }
    }

    private static void HandleColumnArray(FieldInfo fieldInfo, ExportCsvAttribute attribute,
        StringBuilder stringBuilder)
    {
        if (CheckCsvFieldTypeIsArray(fieldInfo))
        {
            HandleColumnFilter(attribute, stringBuilder);
        }
        else
        {
            stringBuilder.Append(",");
        }
    }

    private static void HandleColumnFilter(ExportCsvAttribute attribute, StringBuilder stringBuilder)
    {
        switch (attribute.Filter)
        {
            case CsvFilter.None:
                stringBuilder.Append(",");
                break;
            case CsvFilter.c:
                stringBuilder.Append("c,");
                break;
            case CsvFilter.s:
                stringBuilder.Append("s,");
                break;
            case CsvFilter.sc:
                stringBuilder.Append("sc,");
                break;
        }
    }

    private static void HandleColumnDesc(ExportCsvAttribute attribute, StringBuilder stringBuilder)
    {
        stringBuilder.Append($"{attribute.Desc},");
    }

    private static void HandleColumnType(FieldInfo fieldInfo, StringBuilder stringBuilder)
    {
        stringBuilder.Append($"{GetCsvFieldTypeString(fieldInfo)},");
    }

    private static void HandleColumnName(FieldInfo fieldInfo, StringBuilder stringBuilder)
    {
        stringBuilder.Append(fieldInfo.Name + ",");
    }

    #endregion

    #region  导入相关 todo 目前不大了解Map的配置格式,以后有需要再修改
    
    public static T ImportCsv<T>(string path) where T : IList, new()
    {
        var list = new T();
        try
        {
            var dataType = typeof(T).GetGenericArguments()[0];
            var dataFields = dataType.GetFields();

            var lines = File.ReadAllLines(path);

            var columns = lines[0].Split(',');
            var filter = lines[3].Split(',');

            var columnList = new Dictionary<string, CsvHeader>();
            for (var i = 0; i < columns.Length; i++)
            {
                var column = new CsvHeader();
                column.Name = columns[i].Replace("{name}", "");
                var filterStr = filter[i].Replace("{filter}", "");
                if (Enum.TryParse<CsvFilter>(filterStr, out var filterEnum))
                {
                    column.Filter = filterEnum;
                }

                column.Index = i;

                columnList.Add(column.Name, column);
            }

            //Data
            for (var i = (int)CsvColumnType.index + 1; i < lines.Length; i++)
            {
                var data = lines[i].Split(',');
                var item = Activator.CreateInstance(dataType);
                foreach (var field in dataFields)
                {
                    var fieldName = field.Name;
                    if (columnList.TryGetValue(fieldName, out var column))
                    {
                        // if (column.Filter == CsvFilter.None) continue;
                        FillData(field, column, data, item);
                    }
                }

                list.Add(item);
            }

            return list;
        }
        catch (Exception e)
        {
            Debug.LogError(e);
            return new T();
        }
    }

    private static void FillData(FieldInfo field, CsvHeader column, string[] data, object item)
    {
        if (field.FieldType.IsEnum)
        {
            field.SetValue(item, Enum.Parse(field.FieldType, data[column.Index]));
            return;
        }

        switch (field.FieldType.Name)
        {
            case "String":
                field.SetValue(item, data[column.Index]);
                break;
            case "Int32":
                field.SetValue(item, int.Parse(data[column.Index]));
                break;
            case "Single":
                field.SetValue(item, int.Parse(data[column.Index]) / 100f);
                break;
            case "Vector2":
                var v2 = new Vector2(int.Parse(data[column.Index]) / 100f, int.Parse(data[column.Index + 1]) / 100f);
                field.SetValue(item, v2);
                break;
        }
    }
    #endregion
}