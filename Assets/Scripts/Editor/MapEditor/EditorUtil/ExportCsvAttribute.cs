using System;

public enum CsvFilter
{
    None,
    c,
    s,
    sc,
}

/// <summary>
/// 用于标识需要导出的字段和其属性
/// </summary>
[AttributeUsage(AttributeTargets.Field)]
public class ExportCsvAttribute : Attribute
{
    //对应表格的规范
    
    public string Desc;

    public bool IsIndex = false;

    // public bool IsIgnore = false;
    public CsvFilter Filter;

    public ExportCsvAttribute(string desc = "", bool isIndex = false, CsvFilter filter = CsvFilter.None)
    {
        Desc = desc;
        IsIndex = isIndex;
        Filter = filter;
    }
}