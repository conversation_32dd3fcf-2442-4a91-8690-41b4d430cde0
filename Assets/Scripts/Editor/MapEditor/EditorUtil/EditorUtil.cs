using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using UnityEditor;
using UnityEngine;
using Object = UnityEngine.Object;

public static class EditorUtil
{
    /// <summary>
    /// 绘制对象所有属性
    /// </summary>
    /// <param name="obj"></param>
    public static void DrawSo(UnityEngine.Object obj)
    {
        var editor = Editor.CreateEditor(obj);

        if (editor.GetType().GetCustomAttribute(typeof(CustomEditor)) != null)
        {
            editor.OnInspectorGUI();
            editor.serializedObject.ApplyModifiedProperties();
            return;
        }

        using var selectSp = new SerializedObject(obj);

        EditorGUI.BeginChangeCheck();
        selectSp.Update();
        var property = selectSp.GetIterator();

        property.NextVisible(true);
        do
        {
            if (property.isExpanded || property.name == "m_Script") continue;
            EditorGUILayout.PropertyField(property, true);
        } while (property.NextVisible(false));

        if (EditorGUI.EndChangeCheck())
        {
            selectSp.ApplyModifiedProperties();
        }
    }


    public static string GetAssetPath(string path)
    {
        return path.StartsWith("Assets") ? path : path.Substring(path.IndexOf("Assets", StringComparison.Ordinal));
    }

    public static string GetAssetBundlePath(string path)
    {
        return path.Substring(path.IndexOf("/") + 1).ToLower();
    }

    public static bool ExportJson(string path, object obj)
    {
        if (string.IsNullOrEmpty(path)) return false;

        try
        {
            //需要忽略基类属性 Object会带有一个hideflag
            // var settings = new JsonSerializerSettings
            // {
            //     ContractResolver = new IgnoreBaseClassPropertiesResolver()
            // };
            // var json = JsonConvert.SerializeObject(obj, settings);
            var json = JsonConvert.SerializeObject(obj);
            File.WriteAllText(path, json);

            return true;
        }
        catch
        {
            return false;
        }
    }

    public static T ImportJson<T>(string exportPath) where T : class
    {
        if (string.IsNullOrEmpty(exportPath)) return null;
        try
        {
            var json = File.ReadAllText(exportPath);
            // var settings = new JsonSerializerSettings
            // {
            //     ContractResolver = new IgnoreBaseClassPropertiesResolver()
            // };
            // return JsonConvert.DeserializeObject<T>(json, settings);
            return JsonConvert.DeserializeObject<T>(json);
        }
        catch
        {
            return null;
        }
    }


    public static bool ExportCsv(string exportPath, IEnumerable buildingDatas)
    {
        return CsvUtil.ExportCsv(exportPath, buildingDatas);
    }
    public static T ImportCsv<T>(string path) where T : IList,new()
    {
        return CsvUtil.ImportCsv<T>(path);
    }
    
    public static bool IsPointInView(Vector3 worldPoint, Camera camera)
    {
        // 对于透视摄像机
        if (camera.orthographic == false)
        {
            // Vector3 direction = worldPoint - camera.transform.position;
            // direction.Normalize();
            //
            // // 计算点的方向向量与摄像机向前向量之间的夹角余弦值
            // float angle = Vector3.Dot(camera.transform.forward, direction);
            //
            // // 检查该点是否在视野锥内（这里简化处理，未考虑视野的上下边界）
            // float fovRadians = camera.fieldOfView * Mathf.Deg2Rad * 0.5f;
            // return angle > Mathf.Cos(fovRadians);
            
            Plane[] planes = GeometryUtility.CalculateFrustumPlanes(camera);
            
            // 使用点和一个足够小的边界盒（例如点周围的微小立方体）来检测点是否在视锥体内
            Bounds pointBounds = new Bounds(worldPoint, Vector3.one * 0.01f); // 假设点周围有一个很小的边界
            return GeometryUtility.TestPlanesAABB(planes, pointBounds);
        }
        // 对于正交摄像机
        else
        {
            // 直接检查点是否在摄像机的视口矩形内
            Plane[] planes = GeometryUtility.CalculateFrustumPlanes(camera);
            return GeometryUtility.TestPlanesAABB(planes, new Bounds(worldPoint, Vector3.one * 0.01f));
        }
    }
}


public class IgnoreBaseClassPropertiesResolver : DefaultContractResolver
{
    protected override JsonProperty CreateProperty(MemberInfo member, MemberSerialization memberSerialization)
    {
        JsonProperty property = base.CreateProperty(member, memberSerialization);

        // 检查属性是否属于基类，并决定是否忽略
        if (!property.DeclaringType.IsSubclassOf(typeof(Object))
            && !property.PropertyName.Equals("PropertyNameToKeep", StringComparison.OrdinalIgnoreCase))
        {
            Debug.Log(property.PropertyName);
            property.ShouldSerialize = instance => false;
        }

        return property;
    }
}