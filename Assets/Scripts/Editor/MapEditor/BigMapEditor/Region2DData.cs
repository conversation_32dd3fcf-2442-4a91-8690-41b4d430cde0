using System;
using System.Collections.Generic;
using System.Linq;
using UnityEditor;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Serialization;

[System.Serializable]
//区域的map格式的数据，可以理解存成X*Y的网格，未
public class Region2DData
{
    private const int MAX_SIZE = 1000;
    public int StartX { get; private set; } //坐标系的相对偏移x
    public int StartY { get; private set; }     //坐标系的相对偏移y
    public int CurXLength { get; private set; }
    public int CurYLength { get; private set; }
    
    public int MinX { get; private set; }
    public int MaxX { get; private set; }
    public int MinY { get; private set; }
    public int MaxY { get; private set; }

    public List<Vector2Int> PolygonPoints;

    private const int DEFAULT_2D_SIZE = 64;
    
    public byte[,] Data;    //注意，当前这个的区域是最大内接区域，外面还有多边形的边
    /// <summary>
    /// 检测是否需要扩容
    /// </summary>
    /// <param name="x">新增的点的x</param>
    /// <param name="y">新增的点的y</param>
    private void CheckAndAddCapacity(int x,int y)
    {
        if (CurXLength == 0 || CurYLength == 0)
        {
            //第一次扩容，以该坐标，绘制一个DEFAULT_2D_SIZE*DEFAULT_2D_SIZE的网格
            int offsetX = x - DEFAULT_2D_SIZE / 2; //已经隐式转化了
            offsetX = Mathf.Clamp(offsetX, 0, MAX_SIZE);
            int offsetY = y - DEFAULT_2D_SIZE / 2;
            offsetY = Mathf.Clamp(offsetY, 0, MAX_SIZE);
            GenMapData(offsetX,offsetY,DEFAULT_2D_SIZE,DEFAULT_2D_SIZE);
        }
        //不在区域内，则
        byte changeType = 0;
        if (x < StartX  || x >= StartX + CurXLength
            )
        {
            changeType ^= 0x1;
        }
        if (y < StartY || y >= StartY + CurYLength )
        {
            changeType ^= 0x2;
        }
        if (changeType != 0)
        {
            int targetXLength = 0;
            int targetYLength = 0;
            int offsetX = 0;
            int offsetY = 0;
            switch (changeType)
            {
                case 1:
                    //不在区域内，则*2扩容
                    targetXLength = CurXLength * 2;
                    offsetX = CurXLength / 2;
                    if (StartX - offsetX < 0)
                    {
                        offsetX = 0;
                    }
                    else
                    {
                        offsetX = StartX - offsetX;
                    }

                    offsetY = StartY;
                    targetYLength = CurYLength;
                    break;
                case 2:
                    targetYLength = CurYLength * 2;
                    offsetY = CurYLength / 2;
                    if (StartY - offsetY < 0)
                    {
                        offsetY = 0;
                    }
                    else
                    {
                        offsetY = StartY - offsetY;
                    }

                    offsetX = StartX;
                    targetXLength = CurXLength;
                    break;
                case 3:
                    targetXLength = CurXLength * 2;
                    offsetX = CurXLength / 2;
                    if (StartX - offsetX < 0)
                    {
                        offsetX = 0;
                    }
                    else
                    {
                        offsetX = StartX - offsetX;
                    }
                    
                    targetYLength = CurYLength * 2;
                    offsetY = CurYLength / 2;
                    if (StartY - offsetY < 0)
                    {
                        offsetY = 0;
                    }
                    else
                    {
                        offsetY = StartY - offsetY;
                    }
                    break;
            }
            GenMapData(offsetX,offsetY,targetXLength,targetYLength);
        }
    }
    /// <summary>
    /// 设置点的状态
    /// </summary>
    /// <param name="x"></param>
    /// <param name="y"></param>
    /// <param name="state"> 表示当前点被激活选中</param>
    public void SetPointState(int x,int y, byte state)
    {
        CheckAndAddCapacity(x, y);
        int lenX= Data.GetLength(0);
        int lenY = Data.GetLength(1);
        if (x - StartX >= 0 && x - StartX < lenX
                            && y - StartY >= 0 && y - StartY < lenY)
        {
            Data[x - StartX, y - StartY] = state;
        }
        else
        {
            Debug.LogError($"{x},{y}越界了 len:{lenX}*{lenY} Offset={StartX}*{StartY}");
        }

        if (state != 0)
        {
            //不用三元判断，增加设置消耗
            if (x < MinX)
                MinX = x;
            if (x > MaxX)
                MaxX = x;
            if (y < MinY)
                MinY = y;
            if (y > MaxY)
                MaxY = y;
        }
    }
    
    //刷新一下多边形
    public void RefreshPolygon()
    {
        //注意不要调用绕边框的，因为map的点可能在边界上 会越界了
        PolygonPoints = MathUtil.GetPolygonPath(Data, MinX-StartX, MaxX-StartX, MinY-StartY, MaxY-StartY,false);
        //注意要处理一下偏移
        for (int i = 0; i < PolygonPoints.Count; i++)
        {
            PolygonPoints[i] = new Vector2Int(PolygonPoints[i].x+ StartX,PolygonPoints[i].y+StartY);
        }
    }

    public void ResetData()
    {
        for (int i = 0; i < CurXLength; i++)
        {
            for (int j = 0; j < CurYLength; j++)
            {
                Data[i, j] = 0;
            }
        }
    }

    private void GenMapData(int offsetX,int offsetY,int xLength,int yLength)
    {
        if (xLength == 0 || yLength == 0)
        {
            Debug.LogError($"GenMapData Error CurXLength ={xLength}");
            return;
        }
        if (Data == null)
        {
            Data = new Byte[xLength, yLength];
        }
        else
        {
            //拷贝一份原来的
            var newData = new Byte[xLength, yLength];
            for (int i = 0; i < CurXLength; i++)
            {
                for (int j = 0; j < CurYLength; j++)
                {
                    newData[  i + StartX - offsetX, j + StartY - offsetY ] = Data[i, j];
                }
            }
            Data = newData;
        }
        StartX = offsetX;
        StartY = offsetY;
        CurXLength = xLength;
        CurYLength = yLength;
        //初始化最大，最小值
        MinX = int.MaxValue;
        MaxX = 0;
        MinY = int.MaxValue;
        MaxY = 0;
        Debug.Log($"生成了新的地图{StartX} {StartY} {CurXLength} {CurYLength}");
    }
}