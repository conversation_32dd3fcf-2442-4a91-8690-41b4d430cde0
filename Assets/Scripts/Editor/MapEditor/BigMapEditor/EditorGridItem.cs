using System.Linq;
using UnityEditor;
using UnityEngine;
using UnityEngine.Rendering;

[System.Serializable]
public class EditorGridItem
{
    public Vector2 Pos;
    public Vector2Int GridPos;

    public Vector3 WorldPos;
    public int Size;

    public const float AREA_HEIGHT = 0f;

    // private GameObject m_gameObject;
    // private TextMeshPro m_textPos;
    public int PosID;
    private GUIStyle m_guistyle;


    private GameObject m_building;
    public EditorBuildingData BuildData;
    public Vector3[] BlockGridPos { get; private set; }

    public EditorRegionData RegionData { get; private set; }

    private string posText;

    public static int GetPosID(Vector2Int pos)
    {
        return pos.x * 1000 + pos.y;
    }

    public static Vector2Int GetGridPos(Vector3 pos, int gridSize)
    {
        var gridX = Mathf.RoundToInt(pos.x / gridSize);
        var gridY = Mathf.RoundToInt(pos.z / gridSize);
        return new Vector2Int(gridX, gridY);
    }

    public EditorGridItem(Vector2 pos, int size, Transform parent)
    {
        Pos = pos;
        Size = size;
        BlockGridPos = new Vector3[4];
        var lbPos = new Vector3(Pos.x - Size / 2f, AREA_HEIGHT, Pos.y - Size / 2f);
        var ltPos = new Vector3(Pos.x - Size / 2f, AREA_HEIGHT, Pos.y + Size / 2f);
        var rtPos = new Vector3(Pos.x + Size / 2f, AREA_HEIGHT, Pos.y + Size / 2f);
        var rbPos = new Vector3(Pos.x + Size / 2f, AREA_HEIGHT, Pos.y - Size / 2f);
        BlockGridPos[0] = lbPos;
        BlockGridPos[1] = ltPos;
        BlockGridPos[2] = rtPos;
        BlockGridPos[3] = rbPos;
        // m_gameObject = new GameObject($"{pos.x / Size},{pos.y / Size}");
        // m_gameObject.transform.SetParent(parent);
        // m_gameObject.transform.localPosition = new Vector3(Pos.x, AREA_HEIGHT, Pos.y);

        WorldPos = new Vector3(Pos.x, AREA_HEIGHT, Pos.y);

        GridPos = GetGridPos(WorldPos, Size);

        PosID = (int)GridPos.x * 1000 + (int)GridPos.y;
        posText = $"{GridPos.x},{GridPos.y}";

        // var go = new GameObject("PosText", typeof(TextMeshPro));
        // go.transform.SetParent(m_gameObject.transform, false);
        // go.transform.position = new Vector3(Pos.x, AREA_HEIGHT, Pos.y);
        // go.transform.localScale = Vector3.one * 0.5f;
        // go.transform.localEulerAngles = new Vector3(90, 0, 0);
        // var tm = go.GetComponent<TextMeshPro>();
        // var mat = tm.GetComponent<Renderer>().sharedMaterial;
        // mat.renderQueue = 3300;
        // tm.richText = false;
        // tm.alignment = TextAlignmentOptions.Center;
        // tm.text = m_gameObject.name;
        // tm.color = Color.black;
        // if (Size < 2)
        // {
        //     tm.fontSize = 7f;
        // }
        //
        // m_textPos = go.GetComponent<TextMeshPro>();
    }

    public void DrawText()
    {
        if (m_guistyle == null)
        {
            m_guistyle = new GUIStyle();
            m_guistyle.normal.textColor = Color.black;
            m_guistyle.fontSize = 15;
            if (Size < 2)
            {
                m_guistyle.fontSize = 7;
            }

            m_guistyle.alignment = TextAnchor.MiddleCenter;
        }

        Handles.Label(WorldPos, posText, m_guistyle);
    }

    public void Destroy()
    {
        // Object.DestroyImmediate(m_gameObject);
        // m_gameObject = null;
        // m_textPos = null;

        Object.DestroyImmediate(m_building);
        Object.DestroyImmediate(RegionData);
        BuildData = null;
        m_building = null;
        RegionData = null;
    }

    public void SetBuilding(EditorBuildingData buildingData, GameObject gridRoot, string prefabPath)
    {
        if (!buildingData) return;

        //绑定城池模型 同时存储数据
        var buildingPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
        if (!buildingPrefab) return;
        m_building = Object.Instantiate(buildingPrefab, gridRoot.transform);
        m_building.transform.localPosition = WorldPos;

        var editorModelPath = AssetDatabase.GetAssetPathsFromAssetBundle(buildingData.ModelPath);

        if (editorModelPath == null || editorModelPath.Length <= 0)
        {
            return;
        }

        var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(editorModelPath[0]);
        if (!prefab)
        {
            return;
        }

        var model = m_building.transform.Find("Model");
        if (!model) return;

        Object.Instantiate(prefab, model.transform);
        BuildData = buildingData;
    }

    public void RemoveBuilding()
    {
        Destroy();
    }

    public void SetRegionData(EditorRegionData regionData)
    {
        RegionData = regionData;
    }

    public void DrawRegion(EditorMode bindMode,bool needDrawBorder = true)
    {
        if (!RegionData || BuildData) return;
        var color = RegionData.RegionColor;
        var zoneBuff = RegionData.SubRegionColor;
        var isSubRegion = RegionData.ZoneBuff.Contains(this) || RegionData.ZoneGrid.Contains(this);
        //编辑模式只需要绘制边框
        if ((bindMode == EditorMode.Bind || bindMode == EditorMode.Delete) && RegionData.RegionGrid.Contains(this))
        {
            return;
        }

        if ((bindMode == EditorMode.SubBind || bindMode == EditorMode.SubDelete) && RegionData.ZoneGrid.Contains(this))
        {
            return;
        }

        Handles.color = isSubRegion ? zoneBuff : color;
        if (needDrawBorder)
        {
            Handles.DrawSolidRectangleWithOutline(BlockGridPos, isSubRegion ? zoneBuff : color,
                Color.black);
        }
        else
        {
            Handles.DrawSolidRectangleWithOutline(BlockGridPos, isSubRegion ? zoneBuff : color,
                Handles.color);
        }
    }
}