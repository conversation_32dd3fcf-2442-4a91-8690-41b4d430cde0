using UnityEditor;
using UnityEngine;

[CustomEditor(typeof(EditorRegionData))]
public class EditorRegionDataCustomProperty : Editor
{
    public override void OnInspectorGUI()
    {
        EditorGUI.BeginChangeCheck();
        serializedObject.Update();

        var property = serializedObject.FindProperty("ID");
        EditorGUILayout.PropertyField(property, new GUIContent("区域ID"));
        property = serializedObject.FindProperty("ResourcesDeploy");
        EditorGUILayout.PropertyField(property, new GUIContent("资源点配置"));
        property = serializedObject.FindProperty("MonsterNumber");
        EditorGUILayout.PropertyField(property, new GUIContent("怪物数量"));
        property = serializedObject.FindProperty("LowNumber");
        EditorGUILayout.PropertyField(property, new GUIContent("怪物最低数量"));
        property = serializedObject.FindProperty("RenovateNumber");
        EditorGUILayout.PropertyField(property, new GUIContent("野怪补充刷新数量"));
        property = serializedObject.FindProperty("Ratio");
        EditorGUILayout.PropertyField(property, new GUIContent("野怪刷新比例"));
        property = serializedObject.FindProperty("RegionColor");
        EditorGUILayout.PropertyField(property, new GUIContent("区域颜色"));
        property = serializedObject.FindProperty("SubRegionColor");
        EditorGUILayout.PropertyField(property, new GUIContent("子区域颜色"));
        property = serializedObject.FindProperty("AdjacentRegion");
        EditorGUILayout.PropertyField(property, new GUIContent("相邻区域"));
        EditorGUILayout.HelpBox("填写相邻的区域id", MessageType.Info);

        if (EditorGUI.EndChangeCheck())
        {
            serializedObject.ApplyModifiedProperties();
        }
    }
}