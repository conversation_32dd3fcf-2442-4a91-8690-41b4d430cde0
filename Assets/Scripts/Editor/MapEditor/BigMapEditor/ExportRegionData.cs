public class ExportRegionData
{
    [ExportCsv("区域ID", true, CsvFilter.sc)]
    public int RegionID;
    [ExportCsv("城池ID", false, CsvFilter.sc)]
    public int CityID;
    [ExportCsv("城池等级", false, CsvFilter.sc)]
    public int CityLevel;
    [ExportCsv("城池位置", false, CsvFilter.sc)]
    public string CityPos;
    [ExportCsv("区域范围polygon", false, CsvFilter.c)]
    public string Scope;
    [ExportCsv("区域范围", false, CsvFilter.sc)]
    public string ScopeSc;
    [ExportCsv("区域大小", false, CsvFilter.sc)]
    public int RegionSize;
    [ExportCsv("特殊区域", false, CsvFilter.sc)]
    public string ZoneBuff;
    [ExportCsv("特殊区域大小", false, CsvFilter.sc)]
    public int ZoneBuffSize;
    [ExportCsv("区域颜色", false, CsvFilter.c)]
    public string Color;
    [ExportCsv("资源点配置", false, CsvFilter.sc)]
    public string ResourcesDeploy;
    [ExportCsv("怪物数量", false, CsvFilter.sc)]
    public int MonsterNumber;
    [ExportCsv("怪物最低数量", false, CsvFilter.sc)]
    public int LowNumber;
    [ExportCsv("野怪补充刷新数量", false, CsvFilter.sc)]
    public int RenovateNumber;
    [ExportCsv("野怪刷新比例", false, CsvFilter.sc)]
    public string Ratio;
    [ExportCsv("相邻区域", false, CsvFilter.sc)]
    public string AdjacentRegion;
    [ExportCsv("编辑器区域范围", false, CsvFilter.None)]
    public string EditorScope;
    [ExportCsv("子区域范围", false, CsvFilter.None)]
    public string EditorZoneBuff;
}