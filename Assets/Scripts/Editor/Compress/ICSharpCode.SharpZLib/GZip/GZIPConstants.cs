
		#if NOLOADDLL
		#endif
		#if UNITY_5
 
 
 
// GZipConstants.cs
//
// Copyright (C) 2001 <PERSON>
//
// This file was translated from java, it was part of the GNU Classpath
// Copyright (C) 2001 Free Software Foundation, Inc.
//
// This program is free software; you can redistribute it and/or
// modify it under the terms of the GNU General Public License
// as published by the Free Software Foundation; either version 2
// of the License, or (at your option) any later version.
//
// This program is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with this program; if not, write to the Free Software
// Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA  02111-1307, USA.
//
// Linking this library statically or dynamically with other modules is
// making a combined work based on this library.  Thus, the terms and
// conditions of the GNU General Public License cover the whole
// combination.
// 
// As a special exception, the copyright holders of this library give you
// permission to link this library with independent modules to produce an
// executable, regardless of the license terms of these independent
// modules, and to copy and distribute the resulting executable under
// terms of your choice, provided that you also meet, for each linked
// independent module, the terms and conditions of the license of that
// module.  An independent module is a module which is not derived from
// or based on this library.  If you modify this library, you may extend
// this exception to your version of the library, but you are not
// obligated to do so.  If you do not wish to do so, delete this
// exception statement from your version.

namespace ICSharpCode.SharpZipLib.GZip 
{
	
	/// <summary>
	/// This class contains constants used for gzip.
	/// </summary>
	sealed public class GZipConstants
	{
		/// <summary>
		/// Magic number found at start of GZIP header
		/// </summary>
		public const int GZIP_MAGIC = 0x1F8B;
		
		/*  The flag byte is divided into individual bits as follows:
			
			bit 0   FTEXT
			bit 1   FHCRC
			bit 2   FEXTRA
			bit 3   FNAME
			bit 4   FCOMMENT
			bit 5   reserved
			bit 6   reserved
			bit 7   reserved
		 */
		 
		/// <summary>
		/// Flag bit mask for text
		/// </summary>
		public const int FTEXT    = 0x1;
		
		/// <summary>
		/// Flag bitmask for Crc
		/// </summary>
		public const int FHCRC    = 0x2;
		
		/// <summary>
		/// Flag bit mask for extra
		/// </summary>
		public const int FEXTRA   = 0x4;
		
		/// <summary>
		/// flag bitmask for name
		/// </summary>
		public const int FNAME    = 0x8;
		
		/// <summary>
		/// flag bit mask indicating comment is present
		/// </summary>
		public const int FCOMMENT = 0x10;
		
		/// <summary>
		/// Initialise default instance.
		/// </summary>
		/// <remarks>Constructor is private to prevent instances being created.</remarks>
		GZipConstants()
		{
		}
	}
}
 
 
 
#endif