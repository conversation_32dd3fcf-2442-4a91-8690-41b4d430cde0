#if UNITY_EDITOR

using System.Collections.Generic;
using System.IO;
using System.Text.RegularExpressions;
using UnityEditor;
using UnityEngine;


/// <summary>
/// ab 包辅助类
/// </summary>
public static class BCABUtils
{
    private static Regex illegalPattern =
        new Regex("[`~!@#-$%^&*()+=|{}':;',\\[\\]<>/?~！@#￥%……&*（）——+|{}【】《》 ‘；：”“’。，、？]");

    /// <summary>
    /// 获取路径下所有被打了ab标签的文件(获取到.meta文件的信息)
    /// </summary>
    /// <param name="path"></param>
    /// <returns></returns>
    public static List<FileInfo> GetAllFilesTagedAbInfos(string path)
    {
        DirectoryInfo directoryInfo = new DirectoryInfo(path);
        FileInfo[] fileInfos = directoryInfo.GetFiles("*.meta", SearchOption.AllDirectories);
        List<FileInfo> list = new List<FileInfo>(fileInfos.Length);
        foreach (var fileInfo in fileInfos)
        {
            if (IsContainABName(fileInfo.FullName))
            {
                list.Add(fileInfo);
            }
        }

        return list;
    }

    private static bool IsContainABName(string FullName)
    {
        var info = File.ReadAllText(FullName);
        var mat = Regex.Match(info, @"assetBundleName: *.*");
        Debug.Log(mat.Value);
        return mat.Length > 18;
    }

    private static Regex AssetsPatten = new Regex("Assets*.*");

    public static AssetBundleBuild[] GetDirAssetBundleBuild(string dir)
    {
        DirectoryInfo directoryInfo = new DirectoryInfo(dir);
        FileInfo[] fileInfos = directoryInfo.GetFiles("*.meta", SearchOption.AllDirectories);
        List<AssetBundleBuild> list = new List<AssetBundleBuild>(200);
        foreach (var fileInfo in fileInfos)
        {
            string abName = GetAbName(fileInfo.FullName);
            if (!string.IsNullOrEmpty(abName))
            {
                string pathFormAssets = AssetsPatten.Match(fileInfo.FullName).Value.Replace(".meta", "").Trim();
                list.Add(new AssetBundleBuild()
                {
                    assetBundleName = Path.GetFileNameWithoutExtension(pathFormAssets),
                    assetNames = new[] {pathFormAssets}
                });
            }
        }

        return list.ToArray();
    }

    private static string GetAbName(string FullName)
    {
        var info = File.ReadAllText(FullName);
        var mat = Regex.Match(info, @"assetBundleName: *.*");

        return mat.Value.Replace("assetBundleName:", "").Trim();
    }


    public static string GetAssetName(string fullName)
    {
        return AssetsPatten.Match(fullName).Value.Replace(".meta", "").Trim();
    }
    
#if UNITY_2017_4_OR_NEWER
    const BuildTarget StandaloneOSXUniversal = BuildTarget.StandaloneOSX;
#else
        const BuildTarget StandaloneOSXUniversal = BuildTarget.StandaloneOSXUniversal;
#endif
    public static string GetPlatformFolderForAssetBundles(BuildTarget target)
    {
        switch (target)
        {
            case BuildTarget.Android:
                return "Android";
            case BuildTarget.iOS:
                return "iOS";
            case BuildTarget.StandaloneWindows:
            case BuildTarget.StandaloneWindows64:
                return "Windows";
            case BuildTarget.StandaloneOSXIntel:
            case BuildTarget.StandaloneOSXIntel64:
            case StandaloneOSXUniversal:
                return "OSX";
            default:
                return null;
        }
    }
}
#endif
