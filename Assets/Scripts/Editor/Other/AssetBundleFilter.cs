using UnityEngine;
using System.IO;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using UnityEditor;

namespace War.Base
{
    public class AssetBundleFilter : MonoBehaviour
    {
        private const string FilesFileName = "files.txt";

        public static void ProcessAssetBundles(BuildTarget buildTarget, string tmpPath, string outPath)
        {
            var tmpAbPath = Path.Combine(Application.dataPath + "/../", tmpPath);
            // 1. 读取manifest并提取AssetBundleInfos的Name到infoList
            var manifestFileName = BCABUtils.GetPlatformFolderForAssetBundles(buildTarget);
            var manifestPath = Path.Combine(tmpAbPath, manifestFileName + ".manifest");
            HashSet<string> infoList = GetMainifestList(manifestPath);

            // 2.设置目标目录
            var targetDir = Path.Combine(Application.dataPath + "/../", outPath);
            if (Directory.Exists(targetDir))
            {
                Directory.Delete(targetDir, true);
            }
            Directory.CreateDirectory(targetDir);

            // 3. 处理files.txt
            ProcessFile(FilesFileName, infoList, tmpAbPath, targetDir);
            Debug.Log("Asset Bundle processing completed.");
        }

        private static HashSet<string> GetMainifestList(string manifestPath)
        {
            HashSet<string> infoList = new HashSet<string>();
            if (File.Exists(manifestPath))
            {
                using (StreamReader reader = new StreamReader(manifestPath))
                {
                    string line;
                    while ((line = reader.ReadLine()) != null)
                    {
                        Match match = Regex.Match(line, @"Name:\s*(.+)");
                        if (match.Success)
                        {
                            infoList.Add(match.Groups[1].Value.Trim());
                        }
                    }
                }

                // 额外添加当前目标路径
                infoList.Add(Path.GetFileNameWithoutExtension(manifestPath));
            }
            else
            {
                Debug.LogError("Manifest file not found!");
            }

            return infoList;
        }

        private static void ProcessFile(string fileName, HashSet<string> infoList, string rootDir, string targetDir)
        {
            var filePath = Path.Combine(rootDir, fileName);
            if (!File.Exists(filePath))
            {
                Debug.LogError(filePath + " not found!");
                return;
            }

            string jsonContent = File.ReadAllText(filePath);
            var fileData = JsonConvert.DeserializeObject<FilesData>(jsonContent);
            var modifiedList = new Dictionary<string, object>();

            foreach (var entry in fileData.List)
            {
                // 仅保留在infoList中的条目
                if (infoList.Contains(entry.Key))
                {
                    modifiedList[entry.Key] = entry.Value;
                    // 复制文件到目标目录
                    CopyAssets(entry.Key, rootDir, targetDir);
                }
            }

            // 生成修改后的文件
            string modifiedJson = JsonConvert.SerializeObject(new { list = modifiedList }, Formatting.Indented);
            File.WriteAllText(Path.Combine(targetDir, Path.GetFileName(filePath)), modifiedJson);
        }

        private static void CopyAssets(string assetPath, string rootDir, string targetDir)
        {
            string fullPath = Path.Combine(rootDir, assetPath);
            if (File.Exists(fullPath))
            {
                string targetPath = Path.Combine(targetDir, assetPath);
                Directory.CreateDirectory(Path.GetDirectoryName(targetPath)); // 创建目录
                File.Copy(fullPath, targetPath, true); // 复制文件
            }
            else if (Directory.Exists(fullPath))
            {
                string targetPath = Path.Combine(targetDir, assetPath);
                Directory.CreateDirectory(targetPath); // 创建目录
                foreach (var file in Directory.GetFiles(fullPath, "*", SearchOption.AllDirectories))
                {
                    string destFilePath = Path.Combine(targetPath, Path.GetFileName(file));
                    File.Copy(file, destFilePath, true); // 复制文件
                }
            }
            else
            {
                Debug.LogWarning($"File or directory not found: {fullPath}");
            }
        }

        [System.Serializable]
        public class FilesData
        {
            public Dictionary<string, object> List;
        }
    }
}