using UnityEngine;
using UnityEditor;
using UnityEditorInternal;
using UnityEngine.Rendering;
using System;
using System.Reflection;

[CustomEditor(typeof(MeshRenderer)), CanEditMultipleObjects]
public class MeshRendererInspector : Editor
{
    protected SerializedObject m_SerializedObject;

    public void OnEnable()
    {
        m_SerializedObject = new SerializedObject(target as MeshRenderer);
    }

    public override void OnInspectorGUI()
    {
        m_SerializedObject.Update();

        EditorGUILayout.HelpBox("Custom Inspector", MessageType.Info);

        EditorGUI.BeginChangeCheck();

        EditorGUILayout.PropertyField(m_SerializedObject.FindProperty("m_CastShadows"));
        EditorGUILayout.PropertyField(m_SerializedObject.FindProperty("m_ReceiveShadows"));
        EditorGUILayout.PropertyField(m_SerializedObject.FindProperty("m_MotionVectors"));
        EditorGUILayout.PropertyField(m_SerializedObject.FindProperty("m_Materials"), true);
        EditorGUILayout.PropertyField(m_SerializedObject.FindProperty("m_DynamicOccludee"), true);

        var options = GetSortingLayerNames();
        var picks = new int[options.Length];

        var renderer = target as MeshRenderer;
        var name = renderer.sortingLayerName;
        var choice = -1;
        for (int i = 0; i < options.Length; i++)
        {
            picks[i] = i;
            if (name == options[i]) choice = i;
        }

        choice = EditorGUILayout.IntPopup("Sorting Layer", choice, options, picks);
        renderer.sortingLayerName = options[choice];
        renderer.sortingOrder = EditorGUILayout.IntField("Sorting Order", renderer.sortingOrder);

        renderer.lightProbeUsage = (LightProbeUsage)EditorGUILayout.EnumPopup("Ligth Probes", renderer.lightProbeUsage);
        renderer.reflectionProbeUsage = (ReflectionProbeUsage)EditorGUILayout.EnumPopup("Reflection Probes", renderer.reflectionProbeUsage);
        renderer.probeAnchor = EditorGUILayout.ObjectField("Anchor Override", renderer.probeAnchor, typeof(Transform), true) as Transform;

        if (EditorGUI.EndChangeCheck())
            SceneView.RepaintAll();

        m_SerializedObject.ApplyModifiedProperties();
    }

    public string[] GetSortingLayerNames()
    {
        Type internalEditorUtilityType = typeof(InternalEditorUtility);
        PropertyInfo sortingLayersProperty = internalEditorUtilityType.GetProperty("sortingLayerNames", BindingFlags.Static | BindingFlags.NonPublic);
        return (string[])sortingLayersProperty.GetValue(null, new object[0]);
    }
}

