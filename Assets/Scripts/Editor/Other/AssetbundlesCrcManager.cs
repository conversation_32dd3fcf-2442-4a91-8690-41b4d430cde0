using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEngine;

namespace War.Base
{
    public class AssetbundlesCrcManager
    {
        static List<string> sAssetEncrptSets = new List<string>();

        public static void setEncryptSets()
        {
            sAssetEncrptSets.Clear();
            sAssetEncrptSets.Add("luascript");

            //不再使用以下加密文件类型，为避免误加密正常文件，此处屏蔽
            //sAssetEncrptSets.Add("configs");
            //sAssetEncrptSets.Add("art/characters");
        }

        public static bool isEncryptType(string fileName)
        {
            foreach (var value in sAssetEncrptSets)
            {
                if (fileName.StartsWith(value))
                    return true;
            }
            return false;
        }
    }
}
