using System.Collections.Generic;
using System.IO;
using System.Text;
using UnityEditor;
using UnityEngine;

namespace War.Base
{
    public class FileCheck:Singleton<FileCheck>
    {
        public string pathRoot = "ui/";
        public object[] InitCheck(string path)
        {
            pathRoot = path;
            return null;
        }
        /// <summary>
        /// 计算出列表项的唯一码，返回唯一码字典
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public static Dictionary<string,string> CalFile(IEnumerable<string> list)
        {
            var dic = new Dictionary<string, string>();
            foreach (var path in  list)
            {
                var ai = AssetImporter.GetAtPath(path);
                if(ai)
                {
                    var origin = new StringBuilder();

                    if(ai is TextureImporter)
                    {
                        var setting = (ai as TextureImporter).GetPlatformTextureSettings(EditorUserBuildSettings.activeBuildTarget.ToString());

                        var json = JsonUtility.ToJson(setting);
                        origin.Append(json);
                        //"".Print("TextureImporter", json);

                    }
                    else{
                        if(File.Exists(path + ".meta"))
                        {
                            var json = File.ReadAllText(path+".meta");
                            origin.Append(json);
                            //"".Print("AssetImporter", json);
                        }

                    }
                    var filemd5 = ToolUti.File2MD5(path);
                    var md5 = ToolUti.String2MD5(origin.ToString());
                    dic[path] = string.Format("{0}|{1}", filemd5 , md5);


                }

            }
            return dic;
        }
        public static string GetABMD5(Dictionary<string,string> ctx,string ab)
        {
            var origin = new StringBuilder();
            //var abs = AssetDatabase.GetAssetBundleDependencies(ab,false);
            //foreach (var tab in abs)
            //{
            //    IterAB(ctx, origin, tab);
            //}
                IterAB(ctx, origin, ab);

            ctx[ab] = ToolUti.String2MD5(origin.ToString());
            return ctx[ab];

        }

        private static void IterAB(Dictionary<string, string> ctx, StringBuilder origin, string tab)
        {
            var tass = AssetDatabase.GetAssetPathsFromAssetBundle(tab);

            var l = new List<string>(tass);
            l.Sort();
            var tmd = "";

            foreach (var item in l)
            {
                if (ctx.TryGetValue(item, out tmd))
                {
                    origin.Append(tmd);
                    origin.Append("\n");
                }
            }
        }

        public List<string> CompareCtxWithPath(string old_path, string new_path, string pathRoot)
        {
            if(!File.Exists(old_path) || !File.Exists(new_path))
            {
                "".Print("CompareCtx path does not exist",old_path,new_path);
                List<string> lists = new List<string>();
                return lists;
            }
            var abnames = AssetDatabase.GetAllAssetBundleNames();
            var tabs = ArrayUtility.FindAll(abnames, (ab) => ab.StartsWith(pathRoot));
            var old_str = File.ReadAllText(old_path);
            var old_ctx = UIHelper.ToObj<Dictionary<string, string>>(old_str);
            var new_str = File.ReadAllText(new_path);
            var new_ctx = UIHelper.ToObj<Dictionary<string, string>>(new_str);


            var list = CompareCtx(old_ctx, new_ctx, tabs);

            return list;

        }
        public List<string> CompareCtx(Dictionary<string, string> old_ctx, Dictionary<string, string> new_ctx, IEnumerable<string> abs)
        {

            List<string> lists = new List<string>();
            var old_md5 = "";
            var new_md5 = "";

            foreach (var ab in abs)
            {
                old_md5 = GetABMD5(old_ctx, ab);
                new_md5 = GetABMD5(new_ctx, ab);
                if (old_md5 == new_md5) continue;
                lists.Add(ab);
                "".Print("CompareCtx diff", ab, old_md5, new_md5);
            }
            return lists;
        }
        public static Dictionary<string, string> CheckAndSaveCtx(string pathRoot,string savePath)
        {
            LogHelp.Instance.Log("FileCheck start");
            var abnames = AssetDatabase.GetAllAssetBundleNames();

            var tabs = ArrayUtility.FindAll(abnames, (ab) => ab.StartsWith(pathRoot));
            var dic = CalMd5Ctx(tabs);
            var dic_str = UIHelper.ToJson(dic);
            EditorHelp.CheckDir(savePath);
            File.WriteAllText(savePath, dic_str);
            LogHelp.Instance.Log("FileCheck end");
            return dic;
        }

        public void Exec(string outputPath = "AssetBundles")
        {
            var list2remove = new List<string>();
            var remove_ab_str = JenkinsEnv.Instance.Get("remove_ab_list");
            if(!string.IsNullOrEmpty(remove_ab_str))
            {
                var arr = remove_ab_str.Split(';');
                list2remove.AddRange(arr);
            }

            var oldpath = "AssetBundles/old.json";
            var newpath = "AssetBundles/new.json";

            CheckAndSaveCtx(pathRoot, newpath);
            if (File.Exists(oldpath))
            {
                var l = CompareCtxWithPath(oldpath, newpath, pathRoot);
                "".Print("FileCheck", UIHelper.ToJson(l));

                l.AddRange(list2remove);

                foreach (var ab in l)
                {
                    var manifestpath = string.Format("{0}/{1}.manifest", outputPath, ab);
                    if (File.Exists(manifestpath))
                    {
                        File.Delete(manifestpath);
                    }
                }
                 
            }
            File.Delete(oldpath);
            File.Move(newpath, oldpath);
        }

        private static Dictionary<string, string> CalMd5Ctx(List<string> tabs)
        {
            HashSet<string> set; Dictionary<string, string> dic;
            set = new HashSet<string>();
            foreach (var ab in tabs)
            {
                var abs = AssetDatabase.GetAssetBundleDependencies(ab, true);
                foreach (var tab in abs)
                {
                    var tass = AssetDatabase.GetAssetPathsFromAssetBundle(tab);

                    set.UnionWith(tass);
                }
            }
            dic = CalFile(set);
            return dic;

        }
    }
}
