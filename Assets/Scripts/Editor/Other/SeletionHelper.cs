#if UNITY_EDITOR
using System.Diagnostics;
using System.IO;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

namespace War.Base
{
    internal class SeletionHelper
    {
        private static string selectObjPath;
        private static GameObject selectPrefab;

        [InitializeOnLoadMethod]
        private static void Start()
        {
            EditorApplication.hierarchyWindowItemOnGUI += HierarchyWindowItemOnGUI;
            EditorApplication.projectWindowItemOnGUI += ProjectWindowItemOnGUI;
        }

        private static void HierarchyWindowItemOnGUI(int instanceID, Rect selectionRect)
        {
            Event e = Event.current;
            if (e.type == UnityEngine.EventType.KeyDown)
            {
                switch (e.keyCode)
                {
                    case UnityEngine.KeyCode.Space:
                        ToggleGameObjectActiveSelf();
                        e.Use();
                        break;
                }
            }
        }

        internal static void ToggleGameObjectActiveSelf()
        {
            Undo.RecordObjects(Selection.gameObjects, "Active");
            foreach (var go in Selection.gameObjects)
            {
                go.SetActive(!go.activeSelf);
            }
        }

        private static void ProjectWindowItemOnGUI(string guid, Rect selectionRect)
        {
            if (Event.current.type == EventType.KeyDown &&
                Event.current.keyCode == UnityEngine.KeyCode.Space &&
                selectionRect.Contains(Event.current.mousePosition))
            {
                //这个位置精准点
                string strPath = AssetDatabase.GUIDToAssetPath(Selection.assetGUIDs[0]);
                //这个位置不精准
                //string strPath = AssetDatabase.GUIDToAssetPath(guid);
                if (Event.current.alt)
                {
                    UnityEngine.Debug.Log(strPath);
                }

                if (Path.GetExtension(strPath) == string.Empty)
                {
                    Process.Start(Path.GetFullPath(strPath));
                }
                else
                {
                    Process.Start("explorer.exe", "/select," + Path.GetFullPath(strPath));
                }

                Event.current.Use();
            }
        }
    }
#endif
}