using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;
using War.Common;

namespace War.Base
{
    [System.Serializable]
    public class CaualFileInfo
    {
        public string Name;
        public int Version;
        public string MD5;
        public long CRC32;
        public long Size;
        public string[] Dependencies;
    }

    [System.Serializable]
    public class CaualFile
    {
        public CaualFileInfo[] Entries;
    }

    [System.Serializable]
    public class MarkInfo
    {
        public string GameName;
        public int Version;
    }

    [System.Serializable]
    public class CaualGameUpdate
    {
        public string update_json_url;
        public List<MarkInfo> key2_mark_files_url_map;
        public string resource_url;
    }

    /// <summary>
    /// 小游戏打整包
    /// </summary>
    public class CasualGameBuildNew
    {
        [MenuItem("AssetBundles/CasualGameBuild/CopyToStreamAsset")]
        public static void Build()
        {
            //             var game = EditorWindow.GetWindow(typeof(EditorWindow).Assembly.GetType("UnityEditor.GameView"));
            //             game?.ShowNotification(new GUIContent($"{"提示"}"));

            var serverUrlRoot = @"http://172.18.2.105:8001/CasualGame_Pure_Res_105_002/TinyRes/";
            //UpdateRes(serverUrlRoot);
        }
        public static void DelteRes()
        {
            string updatePath = GetPathUpdate_Assetbundle();
            if (Directory.Exists(updatePath))
                Directory.Delete(updatePath, true);

            string casualPath = GetCasualGamePath();
            if (Directory.Exists(casualPath))
                Directory.Delete(casualPath, true);
        }
        
        /// <summary>
        /// 更新小游戏资源
        /// </summary>
        public static void UpdateRes(string serverUrlRoot, string[] reskeys = null, bool isdelete = true)
        {
            string updatePath = GetPathUpdate_Assetbundle();
            if (isdelete)
            {
                DelteRes();
            }
            // 独立热更小游戏
            UpdateResByUpdateName(serverUrlRoot, updatePath, "update.txt", reskeys);
            // 华佗热更小游戏
            UpdateResByUpdateName(serverUrlRoot, updatePath, "update_tiny.txt", reskeys);
        }
        
        /// <summary>
        /// 更新小游戏资源
        /// </summary>
        public static void UpdateResByUpdateName(string serverUrlRoot, string updatePath, string updateName, string[] stand_alone_res_keys)
        {
            // 获取update.txt
            var updateTarFileUrl = Path.Combine(serverUrlRoot, GetManifestName(), updateName);
            var updateSrcFilePath = Path.Combine(updatePath, updateName);
            if (ToolUti.DownloadFileW(updateTarFileUrl, updateSrcFilePath))
            {
                string txtData = File.ReadAllText(updateSrcFilePath);
                var gameUpdate = JsonUtility.FromJson<CaualGameUpdate>(txtData);
                if (gameUpdate != null)
                {
                    if (stand_alone_res_keys == null)
                    {
                        foreach (var data in gameUpdate.key2_mark_files_url_map)
                        {
                            UpdataPatch(updatePath, data.GameName, gameUpdate.resource_url, data.Version);
                        }
                    }
                    else
                    {
                        foreach (var data in gameUpdate.key2_mark_files_url_map)
                        {
                            foreach (var item in stand_alone_res_keys)
                            {
                                if (data.GameName == item)
                                {
                                    UpdataPatch(updatePath, data.GameName, gameUpdate.resource_url, data.Version);
                                }
                            }
                        }
                    }
                }
                else
                {
                    Debug.LogError(" GameUpdate is Null ");
                    EditorApplication.Exit(1);
                    return;
                }

                UpdateJson(gameUpdate);
            }
        }

        public static void UpdataPatch(string updatePath, string gamekey, string resourceUrl, int version)
        {
            var filePath = Path.Combine(updatePath, gamekey);
            DirectoryInfo rDirInfo = new DirectoryInfo(filePath);
            if (!rDirInfo.Exists) rDirInfo.Create();

            // 更新文件列表
            var fileTarUrl = Path.Combine(resourceUrl, gamekey, version.ToString(), "file.txt");
            var fileDstPath = Path.Combine(updatePath, gamekey, "file.txt");

            /*var manifestTarUrl = Path.Combine(resourceUrl, gamekey, version.ToString(), gamekey);
            var manifestDstPath = Path.Combine(updatePath, gamekey, gamekey);


            // 下载manifest
            if (!ToolUti.DownloadFileW(manifestTarUrl, manifestDstPath))
            {
                Debug.LogError(" DownLoadUrlError " + manifestTarUrl);
                EditorApplication.Exit(1);
                return;
            }*/

            if (ToolUti.DownloadFileW(fileTarUrl, fileDstPath))
            {
                var txtData = File.ReadAllText(fileDstPath);
                var fileTxt = JsonUtility.FromJson<CaualFile>(txtData);

                foreach (var data in fileTxt.Entries)
                {
                    var url = Path.Combine(resourceUrl, gamekey, data.Version.ToString(), data.Name);
                    var file = Path.Combine(updatePath, gamekey, data.Name);

                    int i = 0;
                    for (i = 0; i < 10; i++)
                    {
                        if (ToolUti.DownloadFileW(url, file))
                        {
                            var crc = BuildScript.File2CRC32(file);
                            if (crc == data.CRC32)
                                break;
                        }
                    }
                    if (i == 10)
                    {
                        Debug.LogError("========== the file down error " + url);
                        EditorApplication.Exit(1);
                    }
                }
            }
        }

        public static void UpdateJson(CaualGameUpdate gameUpdate)
        {
            var key2markpath = Application.streamingAssetsPath + "/key2exclude.json";
            if (File.Exists(key2markpath) == false)
                return;
            var key2exclude = ToolUti.ToObj<Dictionary<string, string[]>>(File.ReadAllText(key2markpath));

            List<string> includeKeyList = new List<string>();
            foreach (var gameKey in key2exclude.Keys)
                includeKeyList.Add(gameKey);

            List<string> gameList = new List<string>();
            foreach (var data in gameUpdate.key2_mark_files_url_map)
                gameList.Add(data.GameName);

            foreach (var gameKey in includeKeyList)     // 旧配置下直接删除所有小程序的配置
            {
                var gameValue = new List<string>(key2exclude[gameKey]);
                foreach (var key in gameList)
                {
                    var path = Path.Combine("assets", "AssetBundles", GetManifestName(), key).Replace("\\", "/");
                    if (!gameValue.Contains(path))
                        gameValue.Add(path);
                }
                key2exclude[gameKey] = gameValue.ToArray();
            }
            File.WriteAllText(key2markpath, ToolUti.ToJson(key2exclude));

            // 生成新配置
            var casualgameclude = Application.streamingAssetsPath + "/casualgameclude.json";
            Dictionary<string, string[]> casualgameDict = new Dictionary<string, string[]>();
            foreach (var gameKey in includeKeyList)
            {
                var allclude = new List<string>(key2exclude["All"]);
                foreach (var key in gameList)
                {
                    var path = Path.Combine("assets", "AssetBundles", GetManifestName(), key).Replace("\\", "/");
                    if (key == gameKey)
                    {
                        allclude.Remove(path);
                    }
                }
                casualgameDict.Add(gameKey, allclude.ToArray());
            }
            File.WriteAllText(casualgameclude, ToolUti.ToJson(casualgameDict));
        }

        public static string GetPathUpdate_Assetbundle()
        {
            string AssetbundlePath = "AssetBundles";
            return Path.Combine(Application.streamingAssetsPath, AssetbundlePath, GetManifestName()).Replace("\\", "/");
        }

        public static string GetManifestName()
        {
            var platform = War.Base.BuildScript.GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget);
            return "T" + platform;
        }

        public static string GetCasualGamePath()
        {
            return Path.Combine(Application.streamingAssetsPath, "CasualGame").Replace("\\", "/");
        }
    }
}
