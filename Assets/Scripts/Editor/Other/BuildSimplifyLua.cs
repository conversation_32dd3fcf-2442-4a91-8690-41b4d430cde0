
using UnityEngine;
using UnityEditor;
using System.IO;
using System;
using System.Text;
using System.Text.RegularExpressions;
using System.Collections.Generic;
using War.Base;

public class BuildSimplifyLua
{
    [MenuItem("XLua/CreateLuaCommentllex")]
    public static void CreateLuaCommentllex()
    {
        string llexluaFolder = $"{Application.dataPath}/../../../Tools/luallex_tool/luallex";
        var luaCommentllexPath = $"{Application.dataPath}/../../../Tools/luallex_tool/LuaSyntax.exe";
        string luaFolder = $"{Application.dataPath}/Lua";

        llexluaFolder = Path.GetFullPath(llexluaFolder);
        luaCommentllexPath = Path.GetFullPath(luaCommentllexPath);
        luaFolder = Path.GetFullPath(luaFolder);
        var arg = $"/C {luaCommentllexPath} {luaFolder} {llexluaFolder}";

        using (System.Diagnostics.Process process = new System.Diagnostics.Process())
        {
            process.StartInfo.FileName = "cmd.exe";
            process.StartInfo.Arguments = arg;
            process.Start();
            process.WaitForExit();
        }

        Debug.LogWarning($"lua 注释信息生成完成，保存路径:{llexluaFolder}");
    }

    [MenuItem("XLua/RemoveComment")]
    public static void RemoveComment()
    {
        string llexluaFolder = $"{Application.dataPath}/../../../Tools/luallex_tool/luallex/";
        string stripptedLuaFolder = $"{Application.dataPath}/../../../Tools/luallex_tool/lua/";
        string luaFolder = $"{Application.dataPath}/Lua/";

        llexluaFolder = Path.GetFullPath(llexluaFolder) ;
        stripptedLuaFolder = Path.GetFullPath(stripptedLuaFolder) ;
        luaFolder = Path.GetFullPath(luaFolder);

        var fileSet = Directory.GetFiles(luaFolder, "*.txt", SearchOption.AllDirectories);
        try
        {
            int fileCount = fileSet.Length;
            int curFileIdx = 0;
            foreach (var file in fileSet)
            {
                curFileIdx++;

                var stripptedLuaPath = file.Replace(luaFolder, stripptedLuaFolder);
                string stripptedLuaParent = Path.GetDirectoryName(stripptedLuaPath);
                if (!Directory.Exists(stripptedLuaParent))
                {
                    Directory.CreateDirectory(stripptedLuaParent);
                }
                var llexPath = file.Replace(luaFolder, llexluaFolder);
                if (!File.Exists(llexPath))
                {
                    continue;
                }
                var lineSet = File.ReadAllLines(llexPath);
                int length = 1024 * 100; // 读取的长度
                FileInfo fileInfo = new FileInfo(file);
                bool bCancle = EditorUtility.DisplayCancelableProgressBar("剔除注释信息:", file, (float)curFileIdx / fileCount);
                if (bCancle)
                {
                    Debug.LogWarning($"手动取消执行");
                    break;
                }

                FileStream writeStream = null;
                FileStream fileStream = null;
                try
                {
                    writeStream = new FileStream(stripptedLuaPath, FileMode.Create, FileAccess.Write, FileShare.Read);
                    fileStream = new FileStream(file, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);

                    byte[] buffer = new byte[length];
                    int curIdx = 0;
                    for (int i = 0; i < lineSet.Length; i++)
                    {
                        var line = lineSet[i];
                        var commentInfo = line.Split(',');
                        if (commentInfo.Length < 3)
                        {
                            Debug.LogError($"行信息解析错误:{file}");
                            break;
                        }
                        int.TryParse(commentInfo[1], out int start);
                        int.TryParse(commentInfo[2], out int count);
                        var lineCount = 0;
                        if (commentInfo.Length > 3)
                        {
                            int.TryParse(commentInfo[3], out lineCount);
                        }
                        var readLen = start - curIdx;
                        if (readLen > 0)
                        {
                            int bytesRead = fileStream.Read(buffer, 0, readLen); // 从文件流中读取数据
                            if (bytesRead != readLen)
                            {
                                Debug.LogError($"未能成功读取指定长度的数据:{file}");
                                break;
                            }
                            curIdx = start + count;
                            fileStream.Seek(start + count, SeekOrigin.Begin); // 设置读取的起始位置
                            writeStream.Write(buffer, 0, bytesRead);

                            if (lineCount > 0)
                            {
                                //多行
                                for (int lineIdx = 0; lineIdx < lineCount; lineIdx++)
                                {
                                    writeStream.WriteByte((byte)'\n');
                                }
                            }
                            else
                            {
                                //单行
                                //writeStream.WriteByte((byte)'\n');
                            }
                        }
                        else
                        {
                            if (lineCount > 0)
                            {
                                for (int lineIdx = 0; lineIdx < lineCount; lineIdx++)
                                {
                                    writeStream.WriteByte((byte)'\n');
                                }
                            }
                            fileStream.Seek(start + count, SeekOrigin.Begin);
                            curIdx = start + count;
                        }
                    }
                    if (fileInfo.Length > curIdx)
                    {
                        int copyLen = (int)fileInfo.Length - curIdx;
                        while (length < copyLen)
                        {
                            length *= 2;
                        }
                        buffer = new byte[length];
                        int bytesRead = fileStream.Read(buffer, 0, copyLen);
                        writeStream.Write(buffer, 0, bytesRead);
                    }
                }
                catch (Exception ex)
                {
                    Debug.LogError(ex.Message);
                }
                finally
                {
                    if (writeStream != null)
                    {
                        writeStream.Close();
                        writeStream = null;
                    }
                    if (fileStream != null)
                    {
                        fileStream.Close();
                        fileStream = null;
                    }
                }

            }
        }
        catch (Exception ex)
        {
            Debug.LogError(ex.Message);
        }
        finally
        {
            EditorUtility.ClearProgressBar();
        }

        Debug.LogWarning($"{luaFolder} 目录下文件注释信息剔除完成,保存路径; {stripptedLuaFolder}");
    }

    [MenuItem("XLua/RemoveRedundancy")]
    public static void RemoveRedundancy()
    {
        string stripptedLuaFolder = $"{Application.dataPath}/../../../Tools/luallex_tool/lua/";

        stripptedLuaFolder = Path.GetFullPath(stripptedLuaFolder);

        try
        {
            var fileSet = Directory.GetFiles(stripptedLuaFolder, "*.txt", SearchOption.AllDirectories);
            int fileCount = fileSet.Length;
            int curFileIdx = 0;
            Regex operationRegex = new Regex(@"([\s])*([\+\-\=])([\s])*");
            StringBuilder stringBuilder = new StringBuilder();
            foreach (var file in fileSet)
            {
                curFileIdx++;

                bool bCancle = EditorUtility.DisplayCancelableProgressBar("剔除冗余信息:", file, (float)curFileIdx / fileCount);
                if (bCancle)
                {
                    Debug.LogWarning($"手动取消执行");
                    break;
                }

                stringBuilder.Clear();
                StreamReader streamReader = null;
                FileStream fileStream = null;
                try
                {
                    fileStream = new FileStream(file, FileMode.Open, FileAccess.ReadWrite, FileShare.Read);
                    streamReader = new StreamReader(fileStream);
                    string line;
                    while ((line =  streamReader.ReadLine()) != null)
                    {
                        // 剔除空格，制表符
                        line = line.Trim();
                        // 剔除部分空格
                        line = operationRegex.Replace(line, "$2");
                        // 换行符使用 \n,替换掉可能的 \r\n
                        stringBuilder.Append(line);
                        stringBuilder.Append("\n");
                    }
                    fileStream.Seek(0, SeekOrigin.Begin);
                    fileStream.SetLength(0);
                    byte[] bytesFromBuilder = Encoding.UTF8.GetBytes(stringBuilder.ToString());
                    fileStream.Write(bytesFromBuilder, 0, bytesFromBuilder.Length);

                    fileStream.Close();
                    fileStream = null;
                    streamReader.Close();
                    streamReader = null;

                }
                catch (Exception e)
                {
                    Debug.LogError(e);
                }
                finally
                {
                    if (fileStream != null)
                    {
                        fileStream.Close();
                        fileStream = null;
                    }
                    if (streamReader != null)
                    {
                        streamReader.Close();
                        streamReader = null;
                    }
                    stringBuilder.Clear();
                }
            }
        }
        catch (Exception ex)
        {
            Debug.LogError(ex.Message);
        }
        finally
        {
            EditorUtility.ClearProgressBar();
        }
        Debug.LogWarning($"{stripptedLuaFolder} 目录下文件冗余信息剔除完成,保存路径; {stripptedLuaFolder}");
    }

    /// <summary>
    /// 根据生成的注释信息移除单/多行注释 
    /// </summary>
    /// <param name="file"></param>
    /// <returns></returns>
    public static string RemoveLuaComment(string file)
    {
        string content = "";
        string llexluaFolder = $"{Application.dataPath}/../../../Tools/luallex_tool/luallex/";
        string stripptedLuaFolder = $"{Application.dataPath}/../../../Tools/luallex_tool/lua/";
        string luaFolder = $"{Application.dataPath}/Lua/";

        llexluaFolder = Path.GetFullPath(llexluaFolder);
        stripptedLuaFolder = Path.GetFullPath(stripptedLuaFolder);
        luaFolder = Path.GetFullPath(luaFolder);

        var stripptedLuaPath = file.Replace(luaFolder, stripptedLuaFolder);
        string stripptedLuaParent = Path.GetDirectoryName(stripptedLuaPath);
        if (!Directory.Exists(stripptedLuaParent))
        {
            Directory.CreateDirectory(stripptedLuaParent);
        }
        var llexPath = file.Replace(luaFolder, llexluaFolder);
        if (!File.Exists(llexPath))
        {
            content = RemoveLuaRedundancy(file);
            return content;
        }
        var lineSet = File.ReadAllLines(llexPath);
        int length = 1024 * 100; // 读取的长度
        FileInfo fileInfo = new FileInfo(file);

        FileStream writeStream = null;
        FileStream fileStream = null;
        try
        {
            writeStream = new FileStream(stripptedLuaPath, FileMode.Create, FileAccess.Write, FileShare.Read);
            fileStream = new FileStream(file, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);

            byte[] buffer = new byte[length];
            int curIdx = 0;
            for (int i = 0; i < lineSet.Length; i++)
            {
                var line = lineSet[i];
                var commentInfo = line.Split(',');
                if (commentInfo.Length < 3)
                {
                    Debug.LogError($"解析错误:{file}");
                    break;
                }
                int.TryParse(commentInfo[1], out int start);
                int.TryParse(commentInfo[2], out int count);
                var lineCount = 0;
                if (commentInfo.Length > 3)
                {
                    int.TryParse(commentInfo[3], out lineCount);
                }
                var readLen = start - curIdx;
                if (readLen > 0)
                {
                    int bytesRead = fileStream.Read(buffer, 0, readLen); // 从文件流中读取数据
                    if (bytesRead != readLen)
                    {
                        Debug.LogError($"未能成功读取指定长度的数据:{file}");
                        break;
                    }
                    curIdx = start + count;
                    fileStream.Seek(start + count, SeekOrigin.Begin); // 设置读取的起始位置
                    writeStream.Write(buffer, 0, bytesRead);

                    if (lineCount > 0)
                    {
                        //多行
                        for (int lineIdx = 0; lineIdx < lineCount; lineIdx++)
                        {
                            writeStream.WriteByte((byte)'\n');
                        }
                    }
                    else
                    {
                        //单行
                        //writeStream.WriteByte((byte)'\n');
                    }
                }
                else
                {
                    if (lineCount > 0)
                    {
                        for (int lineIdx = 0; lineIdx < lineCount; lineIdx++)
                        {
                            writeStream.WriteByte((byte)'\n');
                        }
                    }
                    fileStream.Seek(start + count, SeekOrigin.Begin);
                    curIdx = start + count;
                }
            }
            if (fileInfo.Length > curIdx)
            {
                int copyLen = (int)fileInfo.Length - curIdx;
                while (length < copyLen)
                {
                    length *= 2;
                }
                buffer = new byte[length];
                int bytesRead = fileStream.Read(buffer, 0, copyLen);
                writeStream.Write(buffer, 0, bytesRead);
            }
        }
        catch (Exception ex)
        {
            Debug.LogError(ex.Message);
        }
        finally
        {
            if (writeStream != null)
            {
                writeStream.Close();
                writeStream = null;
            }
            if (fileStream != null)
            {
                fileStream.Close();
                fileStream = null;
            }
            content = RemoveLuaRedundancy(stripptedLuaPath);
        }
        return content;
    }

    /// <summary>
    /// 处理文件中的空格，制表符，替换/r/n的换行符等，这里只进行文件流读取不写入
    /// 当文件有注释语义的内容时，使用luallex_tool下完成的lua文件，当没有是使用目录下的原文件
    /// </summary>
    /// <param name="file"></param>
    /// <returns></returns>
    public static string RemoveLuaRedundancy(string file)
    {
        string stripptedLuaFolder = $"{Application.dataPath}/../../../Tools/luallex_tool/lua/";
        stripptedLuaFolder = Path.GetFullPath(stripptedLuaFolder);
        var fileSet = Directory.GetFiles(stripptedLuaFolder, "*.txt", SearchOption.AllDirectories);
        int fileCount = fileSet.Length;
        Regex operationRegex = new Regex(@"([\s])*([\+\-\=])([\s])*");
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.Clear();
        StreamReader streamReader = null;
        FileStream fileStream = null;
        try
        {
            fileStream = new FileStream(file, FileMode.Open, FileAccess.Read, FileShare.Read);
            streamReader = new StreamReader(fileStream);
            string line;
            while ((line = streamReader.ReadLine()) != null)
            {
                // 剔除空格，制表符
                line = line.Trim();
                // 剔除部分空格
                line = operationRegex.Replace(line, "$2");
                // 换行符使用 \n,替换掉可能的 \r\n
                stringBuilder.Append(line);
                stringBuilder.Append("\n");
            }
            fileStream.Close();
            fileStream = null;
            streamReader.Close();
            streamReader = null;

        }
        catch (Exception e)
        {
            Debug.LogError(e);
        }
        finally
        {
            if (fileStream != null)
            {
                fileStream.Close();
                fileStream = null;
            }
            if (streamReader != null)
            {
                streamReader.Close();
                streamReader = null;
            }
        }
        return stringBuilder.ToString();
    }

    /// <summary>
    /// 指定路径的Lua脚本是否满足Recharge表的移除条件
    /// 移除Assets/Lua/Tables下的 Recharge、Recharge_n、Recharge_M_n 表
    /// </summary>
    /// <param name="pLua"></param>
    /// <returns>是否移除Lua脚本</returns>
    private static Boolean IsRechargeTableRemoveable(String pLua)
    {
        Boolean remove = Regex.IsMatch(pLua, ".+(/|\\\\?)Tables(/|\\\\?).+");
        if (!remove)
        {
            return remove;
        }

        remove = remove && Regex.IsMatch(pLua, ".+(/|\\\\?)Recharge(_M)?(_[0-9a-zA-Z]+)?\\.txt$");

        if (remove)
        {
            LogHelp.Instance.Log($"[BUILD][LUA][TABLES]BuildLuaScriptPatch.IsRechargeTableRemoveable() : remove lua table {pLua}.");
        }

        return remove;
    }

    public static bool ShouldRemoveLua(string luaPath, bool bRemoveOldChargeTable)
    {
        if (bRemoveOldChargeTable)
        {
            if (IsRechargeTableRemoveable(luaPath))
            {
                return true;
            }
        }

        // 其它剔除逻辑

        return false;
    }

    /// <summary>
    /// 是否删除注释和行首尾的空格
    /// 目前只用于 WebGL 平台，移动端暂没开启此配置
    /// </summary>
    /// <returns>是否开启了删除注释和行首尾的空格</returns>
    private static bool IsEnableDeleteNoteAndSpace()
    {
        #if !UNITY_WEBGL
        return false;
        #endif
        return JenkinsEnv.Instance.GetBool("enablue_compress_lua", true);
    }

    /// <summary>
    /// 是否移除Recharge表
    /// 目前只用于 WebGL 平台，移动端暂没开启此配置
    /// </summary>
    /// <returns>是否开启了Recharge表的移除</returns>
    private static bool IsEnableRemoveRechargeTables()
    {
        // return IsGetParamsValueByKey("ENABLE_RECHARGE_CHANNEL_TABLE");
        return true;
    }

    /// <summary>
    /// 从集合中剔除不需要的 lua 脚本，剔除注释
    /// </summary>
    /// <param name="DictLuaBs"></param>
    /// <returns></returns>
    public static Dictionary<string, byte[]> ProcessLuaDictWithLuallex(Dictionary<string, byte[]> DictLuaBs)
    {
        var bRemoveOldChargeTable = IsEnableRemoveRechargeTables();
        LogHelp.Instance.Log($"[BUILD][LUA] BuildLuaScriptPatch.BuildLuascriptVice: ENABLE_REMOVE_RECHARGE_TABLE = {bRemoveOldChargeTable}.");

        var bCompressLua = IsEnableDeleteNoteAndSpace();
        LogHelp.Instance.Log($"[BUILD][LUA] BuildLuaScriptPatch.BuildLuascriptVice: ENABLE_DELETE_NOTEANDSPACE = {bCompressLua}.");

        if (!bCompressLua && !bRemoveOldChargeTable)
        {
            return DictLuaBs;
        }
        System.Diagnostics.Stopwatch stopWatch = new System.Diagnostics.Stopwatch();
        stopWatch.Start();

        if (bCompressLua)
        {
            CreateLuaCommentllex();
        }

        Dictionary<string, byte[]> outDictLua = new Dictionary<string, byte[]>();
        foreach (var luaInfo in DictLuaBs)
        {
            var luaPath = luaInfo.Key;
            // 判断是否需要剔除
            var shouldRemove = ShouldRemoveLua(luaPath, bRemoveOldChargeTable);
            if (shouldRemove)
            {
                continue;
            }

            var contentBytes = luaInfo.Value;

            // 剔除/压缩 lua 脚本
            if (bCompressLua)
            {
                //LogHelp.Instance.Log($"[BUILD][LUA] compress file:{luaPath}.");
                var fstr = RemoveLuaComment(Path.GetFullPath(luaPath));
                var fbytes = Encoding.UTF8.GetBytes(fstr);
                contentBytes = fbytes;
            }

            outDictLua.Add(luaPath, contentBytes);
        }

        if (bCompressLua)
        {
            stopWatch.Stop();
            LogHelp.Instance.Log($" lua 脚本预处理完成，耗时:{stopWatch.ElapsedMilliseconds} ms");
        }
        return outDictLua;
    }

    public static byte[] RemoveLuaCommentWithLuaSrcDiet(string luaPath)
    {
        var luaOutPath = luaPath.Replace("/BinClient/Client/Assets/Lua/", "/BinClient/Client/Assets/LuaCompressed/");
        if(!File.Exists(luaOutPath))
        {
            return null;
        }
        var content = File.ReadAllText(luaOutPath);
        // 主动转一次 UTF8 避免可能的编码异常
        return Encoding.UTF8.GetBytes(content);
    }

    public static void ExecuteLuaSrcDiet(string luaPath, byte[] data)
    {
        var isWhitelist = false;
        if (isStripLog)
        {
            //属于Lua白名单的代码，日志不给它剔除。
            var fileName = Path.GetFileName(luaPath);
            isWhitelist = AssetbundlesMenuItems.ms_CommentLogFileNameDic.ContainsKey(fileName);
        }

        luaPath = Path.GetFullPath(luaPath).Replace("\\", "/");
        var luaOutPath = luaPath.Replace("/Lua/", "/LuaCompressed/");
        string luaParent = Path.GetDirectoryName(luaOutPath);
        if (!Directory.Exists(luaParent))
        {
            Directory.CreateDirectory(luaParent);
        }

        var luasrcdietPath = $"./Assets/../../../Tools/luarocks/3.9.2/systree/bin/luasrcdiet.bat";
        luasrcdietPath = Path.GetFullPath(luasrcdietPath).Replace("\\", "/");
        var arg = $"{luaPath} -o {luaOutPath} --basic --opt-comments --opt-whitespace --noopt-emptylines --noopt-numbers --noopt-locals --noopt-entropy";
        //剔除日志需要执行插件 --plugin logExclude
        if (isStripLog && !isWhitelist)
            arg = $"{arg} --noopt-binequiv --noopt-srcequiv --plugin logExclude";
        else
            arg = $"{arg} --opt-binequiv";
        using (System.Diagnostics.Process process = new System.Diagnostics.Process())
        {
            process.StartInfo.FileName = luasrcdietPath;
            process.StartInfo.Arguments = arg;
            process.StartInfo.UseShellExecute = false;
            process.StartInfo.RedirectStandardInput = true;
            process.StartInfo.RedirectStandardOutput = true;
            process.StartInfo.RedirectStandardError = true;
            process.StartInfo.CreateNoWindow = true;
            process.Start();

            // To avoid deadlocks, always read the output stream first and then wait.  
            //有的错误日志并不是Error级别，这里获取所有日志
            string errorOutput = process.StandardOutput.ReadToEnd();
            process.WaitForExit();

            // 获取进程的返回值
            int exitCode = process.ExitCode;
            if (exitCode != 0)
            {
                if (isSaveInScv) 
                {
                    //对公共的数据使用时lock
                    lock (saveCsvList)
                    {
                        saveCsvList.Add($"{luaPath},{exitCode}");
                    }
                }
                Debug.LogError($"Process {luaPath} failed with exit code: {exitCode}, message:{errorOutput}");
            }
        }
    }

    static List<string> saveCsvList = new List<string>();
    static bool isSaveInScv = false;
    static bool isStripLog = false;
    public static List<string> ExecuteLuaSrcDietAll(Dictionary<string, byte[]> DictLuaBs,bool isSaveCsv = false,bool stripLog = true)
    {
        isStripLog = stripLog;
        isSaveInScv = isSaveCsv;
        if (!isSaveInScv) { saveCsvList.Clear(); }
        var luaOutPath = (Application.dataPath + "/LuaCompressed/");
        luaOutPath = Path.GetFullPath(luaOutPath).Replace("\\", "/");
        if(Directory.Exists(luaOutPath))
        {
            // 避免当前生成失败，转而使用了旧的文件
            Directory.Delete(luaOutPath, true);
            Debug.Log($"清除文件夹:{luaOutPath}");
        }

        try
        {
            AssetDatabase.StartAssetEditing();
            ParallelExec.ResourcesParallel(DictLuaBs, ExecuteLuaSrcDiet, ParallelExec.UpdateDownload, "压缩 Lua");
        }
        catch(Exception e)
        {
            Debug.LogError(e);
        }
        finally
        {
            AssetDatabase.StopAssetEditing();
        }
        //恢复临时变量
        isStripLog = false;
        AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);
        return saveCsvList;
    }
    public static Dictionary<string, byte[]> ProcessLuaDictWithLuaSrcDiet(Dictionary<string, byte[]> DictLuaBs,bool bCompressLua,bool stripLog)
    {
        var bRemoveOldChargeTable = IsEnableRemoveRechargeTables();
        LogHelp.Instance.Log($"[BUILD][LUA] BuildLuaScriptPatch.BuildLuascriptVice: ENABLE_REMOVE_RECHARGE_TABLE = {bRemoveOldChargeTable}.");

        LogHelp.Instance.Log($"[BUILD][LUA] BuildLuaScriptPatch.BuildLuascriptVice: enablue_compress_lua = {bCompressLua}.");

        if (!bCompressLua && !bRemoveOldChargeTable)
        {
            return DictLuaBs;
        }
        System.Diagnostics.Stopwatch stopWatch = new System.Diagnostics.Stopwatch();
        stopWatch.Start();

        if(bCompressLua)
        {
            ExecuteLuaSrcDietAll(DictLuaBs, false, stripLog);
        }

        Dictionary<string, byte[]> outDictLua = new Dictionary<string, byte[]>();
        foreach (var luaInfo in DictLuaBs)
        {
            var luaPath = luaInfo.Key;
            // 判断是否需要剔除
            var shouldRemove = ShouldRemoveLua(luaPath, bRemoveOldChargeTable);
            if (shouldRemove)
            {
                continue;
            }

            var contentBytes = luaInfo.Value;

            // 剔除/压缩 lua 脚本
            if (bCompressLua)
            {
                //LogHelp.Instance.Log($"[BUILD][LUA] compress file:{luaPath}.");
                var filePath = Path.GetFullPath(luaPath).Replace("\\", "/");
                var _contentBytes = RemoveLuaCommentWithLuaSrcDiet(filePath);
                if(_contentBytes == null )
                {
                    Debug.LogError($"压缩 Lua {filePath} 失败");
                }
                else
                {
                    contentBytes = _contentBytes;
                }
            }

            outDictLua.Add(luaPath, contentBytes);
        }

        if (bCompressLua)
        {
            stopWatch.Stop();
            LogHelp.Instance.Log($" lua 脚本预处理完成，耗时:{stopWatch.ElapsedMilliseconds} ms");
        }
        return outDictLua;
    }

}
