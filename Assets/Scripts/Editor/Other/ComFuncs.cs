using UnityEngine;
using UnityEditor;
using System.IO;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using UnityEditor.Sprites;
using War.Base;
using System.Linq;
using FtpTest1;
using ICSharpCode.SharpZipLib.Zip;
using CLeopardZip;
using System.Text;
using System.Text.RegularExpressions;

using War.Common;
using XLua.LuaDLL;
using Sirenix.OdinInspector.Demos;
using Random = System.Random;
using System.Net;
using System.Runtime.Serialization.Formatters.Binary;

namespace War.Base
{
    public class ComFuncs
    {
#if UNITY_2017_4_OR_NEWER
        const BuildTarget StandaloneOSXUniversal = BuildTarget.StandaloneOSX;
#else
    const BuildTarget StandaloneOSXUniversal = BuildTarget.StandaloneOSXUniversal;
#endif

        public static int GetResouceVersion()
        {
            int resVersion = 0;
            var buildTarget = EditorUserBuildSettings.activeBuildTarget;
            var platform = GetPlatformFolderForAssetBundles(buildTarget);
            string outputPath = Path.Combine(ConstPaths.AssetBundlesOutputPath, platform);
            var update_info_path = outputPath + "/" + "update.json";
            string targetFileContent = "";
            if (File.Exists(update_info_path))
            {
                targetFileContent = File.ReadAllText(update_info_path);
            }

            if (string.IsNullOrEmpty(targetFileContent))
            {
                Debug.Log("update.json 读取失败，path:" + update_info_path);
                return resVersion;
            }

            Dictionary<string, object> oldUpdateJsion = UIHelper.ToObj<Dictionary<string, object>>(targetFileContent);
            if (oldUpdateJsion.ContainsKey("files_url"))
            {
                var files_url = oldUpdateJsion["files_url"];

                Regex driverRegex = new Regex(@".+/resource/(\d+)/files.txt");
                var matches = driverRegex.Matches(files_url.ToString());
                if (matches.Count > 0)
                {
                    var match = matches[0];
                    resVersion = int.Parse(match.Groups[1].Value);

                    Debug.Log("update.json 读取resversion:" + resVersion + " match:" + match);
                }
            }
            //因为读取的为上次打包的版本，本次打包版本需要+1

            return resVersion + 1;
        }

        public static string GetPlatformFolderForAssetBundles(BuildTarget target)
        {
            switch (target)
            {
                case BuildTarget.Android:
                    return "Android";
                case BuildTarget.iOS:
                    return "iOS";
                case BuildTarget.StandaloneWindows:
                case BuildTarget.StandaloneWindows64:
                    return "Windows";
                case BuildTarget.StandaloneOSXIntel:
                case BuildTarget.StandaloneOSXIntel64:
                case StandaloneOSXUniversal:
                    return "OSX";
                // Add more build targets for your own.
                // If you add more targets, don't forget to add the same platforms to GetPlatformFolderForAssetBundles(RuntimePlatform) function.
                default:
                    return null;
            }
        }


        public static string GetBuildConfig(Dictionary<string, object> configDic, string key)
        {
            object value;
            if (!configDic.TryGetValue(key, out value))
            {
                Debug.LogWarning("Do not find " + key);
                return string.Empty;
            }

            var strConfig = Convert.ToString(value);
            Debug.LogWarning("Editor Build Config, " + key + " is " + strConfig);
            return strConfig;
        }


        public static void PrintDirHash(string dir)
        {
            var files = Directory.GetFiles(dir);
            var dic = new Dictionary<string, object>();
            foreach (var f in files)
            {
                var hash = File2MD5(f);
                dic[f] = hash;
            }

            "".Print("PrintDirHash", UIHelper.ToJson(dic));
        }

        public static bool CompareBytes(byte[] b1, byte[] b2)
        {
            if (b1 == null || b2 == null)
            {
                return false;
            }

            if (b1.Length != b2.Length) return false;

            for (int i = 0; i < b1.Length; i++)
            {
                if (b1[i] != b2[i]) return false;
            }

            return true;
        }


        static public string File2MD5(string path)
        {
            string res = null;
            if (File.Exists(path))
            {
                byte[] data = File.ReadAllBytes(path);
                res = GetMD5HashFromFile(data);
            }

            return res;
        }
        public static string GetMD5HashFromFile(byte[] data)
        {
            var md5 = new System.Security.Cryptography.MD5CryptoServiceProvider();
            byte[] md5Data = md5.ComputeHash(data, 0, data.Length);
            md5.Clear();

            var destString = "";
            for (int i = 0; i < md5Data.Length; i++)
            {
                destString += string.Format("{0:x2}", md5Data[i]);
            }

            destString = destString.PadLeft(32, '0');
            return destString;
        }

        public static void EncryptFile(string filePathName, string assetBundleName, string newPathName)
        {
            var content = File.ReadAllBytes(filePathName);
            var encryptContent = Common.Aes.Encryptor(content, Script.MainLoop.AssetBundleEncryptKey,
                Common.AlgorithmUtils.HashUtf8MD5(assetBundleName).Substring(8, 16));
            File.WriteAllBytes(newPathName, encryptContent);
        }

    }
}