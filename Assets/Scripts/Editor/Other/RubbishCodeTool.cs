
#pragma warning disable 0219
using UnityEngine;
using System.Collections.Generic;
using UnityEditor;
using System.IO;
using System.Text;

/// <summary>
/// 生成垃圾代码和文件夹工具
/// 生成的文件夹之间的嵌套层级深度随机
/// 垃圾代码随机穿插在生成的目录和根目录中
/// 重建垃圾目录树，废弃原有目录结构和文件位置，将原有文件随机倒入垃圾目录树
/// 
/// 流程:生成垃圾目录树-->将路径下所有原始文件随机倒入垃圾目录树-->删除原始目录结构-->生成垃圾代码并随机倒入垃圾目录树
/// @author:zjw
/// </summary>
public class RubbishCodeTool
{

    //生成垃圾代码和文件夹的根目录
    public static string rootPath = Application.dataPath + "/RubbishCodes";

    //垃圾代码的保存目录
    public static string assetsPath = Application.dataPath + "/RubbishCodes";

    //当前使用过的随机名，使用工具前会将当前项目中已使用过的名字记录在表中
    public static Dictionary<string, int> exceptNameList = null;

    //生成目录数量最小值
    public static int minDirectoryCount = 50;
    //生成目录数量最大值
    public static int maxDirectoryCount = 500;

    //生成文件数量最小值
    public static int minFileCount = 100;//经过测试3000个文件可增加30m左右的包体  
    //生成文件数量最大值
    public static int maxFileCount = 100;//经过测试6000个文件可增加60m左右的包体

    //类中方法数量最小值
    public static int minMethodCount = 1;
    //类中方法数量最大值
    public static int maxMethodCount = 30;

    //方法体中代码块数量最小值,
    public static int minMethodRandomCodeCount = 1;
    //方法体中代码块数量最大值
    public static int maxMethodRandomCodeCount = 20;

    //类中属性数量的最大值，最小值为0
    public static int maxClassVarCount = 15;

    //随机文件夹最大深度
    public static int maxDirDeep = 8;

    //已使用的类名，避免新建的类名重复
    public static List<string> usedClassNames = new List<string>();


    private static List<string> usedMethodName = new List<string>();
    //当前使用过的变量名，避免同一类内随机出相同的变量名
    private static List<string> usedVarNameInCurClass = new List<string>();
    

    private static bool hasReturnValue;
    private static string returnType;
    private static string methodNamePrefix = "";

    //C#代码关键字，不可作为随机名
    private static List<string> systemVarName = new List<string> {
        "abstract","as","base","bool","break","byte","case","catch","char","checked","class","const",
    "continue","decimal","default","delegate","do","double","else","enum","event","explicit",
    "extern","finally", "static", "float","for","foreach","goto","if", "implicit", "in", "int",
    "interface","internal", "is", "lock", "long", "namespace", "new","null",
    "object","operator","out","override","params","private","protected","public",
    "readonly", "ref","return","sbyte","sealed","short","sizeof","stackalloc","static",
    "string","struct","switch","this","throw","True","try","typeof","uint","ulong","unchecked",
    "unsafe","ushort","using","virtual","void","volatile","while"};
    //继承自Component的unity组件类名
    private static List<string> unityComponentStrings = new List<string>{ "GameObject",
                "Collider", "Camera", "MeshCollider", "Rigidbody",
                "BoxCollider","BoxCollider2D", "ParticleSystem", "Light", "Animation",
                "Animator","AudioSource", "Renderer",
                "CharacterController"};
    /// <summary>
    /// 名字前缀
    /// </summary>
    private static List<string> namePrefixStrings = new List<string> { "Root", "Source", "Home", "Base", "Head", "Getter", "Finder", "Accessor", "Checker", "Matcher", "Validator", "Comparator", "Sorter", "Modifier", "Updater", "Adjuster", "Editor", "Emitter", "Sender", "Receiver", "Broadcaster", "Multicaster", "Visitor", "Iterator", "Loader", "Parser", "Constructor", "Maker", "Creator", "Generator", "Initializer", "Register", "Activator", "Starter", "Stopper", "Destroyer", "Listener", "Producer", "Consumer", "Observer", "Observable", "Timer", "Adapter", "Wrapper", "Container", "Server", "Filter", "Interceptor", "Signer", "Owner", "User", "Member", "Operator", "Authenticator", "Proxy", "Broker", "Delegate", "Gateway", "Detector", "Profiler", "Monitor", "Tracker", "Introspector", "Synchronizer", "Blocker", "Holder", "Worker", "Helper", "Supporter", "Util", "Utility", "Kit", "Tool", "Toolkit", "Maker", "Creator", "Generator", "Constructor", "Builder", "Supplier", "Provider", "Factory", "Chooser", "Selector", "Mediator", "Arbitrator", "Decider", "Allocator", "Scheduler", "Resolver", "Processor", "Handler", "Executor", "Performer", "Runner", "Ruler", "Controller", "Manager", "Ordinator", "Leader", "Boss", "Master", "business", "deal", "trade", "transaction", "user", "customer", "player", "Formatter", "Marshaller", "Unmarshaller", "Encoder", "Decoder", "Transformer", "Converter", "Packer", "Extractor", "Descriptor", "Counter", "Viewer", "Locator", "Accumulator", "Recognizer", "Scroller", "Printer", "Compiler", "Cleaner", "Reader", "Writer", "Buffer", "Collector", "Connector", "Scanner", "Linker", "Mapper", "Streamer", "Enhancer", "Renderer", "Painter", "Weaver", "adapter", "Classifier", "abstract", "abstraction", "abstract", "abstraction", "access", "account", "action", "activate", "active", "adapter", "address", "ADL", "ADO", "advanced", "aggregation", "algorithm", "alias", "align", "allocate", "allocator", "annotation", "API", "application", "appearance", "append", "architecture", "argument", "array", "ASP", "assembly", "assert", "assign", "assignment", "associated", "asynchronous", "atomic", "attribute", "authorization", "audio", "background", "backup", "bandwidth", "batch", "BCL", "binary", "binding", "bit", "bitmap", "bitwise", "block", "bookkeeping", "boolean", "border", "boxing", "brace", "bracket", "breakpoint", "build", "bus", "business", "buttons", "bug", "byte", "cache", "calendar", "call", "callback", "casting", "catalog", "chain", "character", "checkpoint", "CIL", "class", "classification", "clause", "cleanup", "CLI", "client", "clipboard", "clone", "CLS", "COFF", "collection", "COM", "comment", "commit", "communication", "compatible", "compiler", "component", "composition", "concept", "concrete", "concurrency", "constraint", "configuration", "connection", "console", "constant", "construct", "constructor", "container", "containment", "context", "control", "cookie", "copy", "CORBA", "cover", "CRTP", "CTS", "cube", "cursor", "custom", "data", "dataset", "database", "dataset", "datagram", "DBMS", "DCOM", "deallocate", "debug", "debugger", "decay", "declaration", "deduction", "DEFAULT", "default", "defer", "definition", "delegate", "delegation", "deploy", "dereference", "destroy", "destructor", "device", "DHTML", "dialog", "digest", "digital", "DIME", "directive", "directory", "dirty", "disassembler", "DISCO", "disk", "dispatch", "DISPID", "DNA", "document", "DOM", "driver", "DTD", "dump", "EAI", "EBCO", "EDI", "efficiency", "efficient", "engine", "entity", "encapsulation", "enum", "enumerators", "equal", "equality", "evaluate", "event", "evidence", "exception", "exit", "explicit", "export", "expression", "facility", "feature", "fetch", "field", "file", "filter", "finalization", "firewall", "finalizer", "firmware", "flag", "flush", "font", "form", "fragmentation", "framework", "function", "functionality", "functor", "GAC", "game", "generate", "generic", "genericity", "getter", "global", "grant", "granularity", "GUID", "handle", "hardware", "header", "heap", "hierarchy", "hook", "Host", "hyperlink", "HTML", "HTTP", "icon", "IDE", "IDL", "identifier", "image", "IME", "implicit", "index", "implement", "implementation", "implicit", "import", "information", "infrastructure", "inheritance", "inline", "initialization", "initialize", "instance", "instantiated", "instantiation", "integrate", "integrity", "interacts", "interface", "interoperability", "interpreter", "introspection", "invariants", "invoke", "iterate", "iterative", "iterator", "iteration", "item", "key", "laser", "level", "library", "lifetime", "link", "linkage", "linker", "list", "livelock", "load", "loader", "local", "lock", "log", "login", "loop", "lvalue", "macro", "maintain", "manifest", "manipulator", "marshal", "member", "memberwise", "memory", "menu", "message", "metadata", "metaprogramming", "method", "micro", "middleware", "modeling", "modifier", "modem", "module", "mouse", "mutable", "mutex", "multiuser", "namespace", "native", "network", "object", "ODR", "operand", "operation", "operator", "option", "optimizer", "overflow", "overhead", "overload", "override", "package", "packaging", "palette", "parallel", "parameter", "parameterize", "parentheses", "parse", "parser", "part", "pattern", "PDA", "performance", "persistence", "PInvoke", "pixel", "placeholder", "platform", "POD", "POI", "pointer", "poll", "pooling", "polymorphism", "port", "postfix", "precedence", "prefix", "preprocessor", "primary", "print", "printer", "procedure", "procedural", "process", "profile", "profiler", "program", "programmer", "programming", "project", "property", "protocol", "pseudo", "qualified", "qualifier", "quality", "queue", "radian", "raise", "range", "rank", "raw", "readOnly", "record", "recordset", "recursive", "refactoring", "refer", "reference", "reference", "register", "reflection", "remote", "represent", "resolve", "resolution", "return", "revoke", "robust", "robustness", "routine", "row", "rowset", "RPC", "runtime", "rvalue", "save", "savepoint", "SAX", "scalable", "schedule", "scheduler", "schema", "scroll", "scope", "screen", "SDK", "search", "semantics", "semaphore", "sequential", "server", "serial", "server", "session", "setter", "sibling", "signature", "slider", "slot", "SMTP", "snapshot", "specialization", "specification", "splitter", "SOAP", "software", "SQL", "stack", "stateless", "statement", "stream", "string", "stub", "subobject", "subquery", "subroutine", "subset", "subtype", "support", "suspend", "symbol", "syntax", "table", "target", "TCP", "template", "text", "thread", "throw", "token", "trace", "transaction", "traverse", "trigger", "tuple", "type", "UDDI", "UML", "unboxing", "underflow", "unmarshal", "unqualified", "URI", "URL", "user", "variable", "vector", "viable", "video", "view", "VEE", "vendor", "vowel", "window", "wizard", "word", "wrapper", "WSDL", "XML", "XSD", "XSL", "XSLT", "Dictionaries", "Sorting", "Searching", "Satisfiability", "Matching", "Clique", "Triangulation", "Cryptography", "recursion" };

    //随机名字组合库
    public static List<string> randomNameStrings = new List<string> { "Moslem", "melancholy", "clockwise", "headlong", "counter", "eastward", "trot", "ridicule", "chatter", "sneer", "boycott", "plunder", "endeavor", "scramble", "flap", "slaughter", "tow", "credit", "menace", "tramp", "trample", "suicide", "tighten", "jingle", "fret", "thrill", "deflect", "tilt", "revolve", "rotate", "compensate", "default", "infer", "repay", "scoff", "hoe", "retort", "broaden", "decompose", "chorus", "supervise", "beware", "grab", "entreat", "concentrate", "urge", "riot", "shrug", "revive", "terminate", "solidify", "dazzle", "transplant", "xerox", "peck", "rally", "squat", "fling", "scrub", "tread", "crack", "slander", "escort", "swell", "pedlar", "grope", "ranch", "versus", "sink", "fertile", "dean", "ear", "breed", "buzz", "shaft", "growl", "wisdom", "kernel", "insert", "bishop", "software", "situation", "gesture", "ticket", "refreshment", "hard", "elegant", "pyjamas", "chord", "realization", "measurement", "axle", "pope", "lounge", "pace", "clown", "resume", "flutter", "frock", "pier", "span", "perch", "quiver", "unanimous", "spill", "constitution", "eclipse", "shade", "clearing", "hoarse", "elapse", "expenditure", "pose", "deviate", "lump", "disperse", "immigrate", "board", "switch", "drain", "cane", "peak", "shrimp", "epoch", "climax", "rigorous", "panel", "mild", "lining", "pop", "heave", "gross", "market", "outbreak", "shuttle", "pendulum", "referee", "scripture", "ought", "should", "pathetic", "Egyptian", "alas", "stout", "dwarf", "patriotic", "patriot", "luxurious", "burial", "assassinate", "foul", "haughty", "ballet", "entitle", "except", "ascribe", "flatten", "straighten", "dock", "subdivide", "bestow", "tabulate", "bank", "idiot", "blond", "white", "millionaire", "lily", "shutter", "blind", "tar", "trigger", "liner", "scar", "radius", "hemisphere", "baseball", "embrace", "siege", "inclusive", "mint", "chip", "mist", "saturation", "preservation", "safeguard", "reservation", "fuse", "assurance", "fortress", "vengeance", "announce", "grumble", "leopard", "panther", "tyrant", "wrath", "tyranny", "storm", "firework", "grief", "woe", "tragic", "arctic", "deviation", "reverse", "captive", "essence", "breakdown", "bandage", "sullen", "indispensable", "diploma", "patron", "fireplace", "hearth", "rim", "verge", "edit", "program", "fall", "alteration", "loosen", "transform", "discrimination", "advocate", "reason", "heading", "norm", "criterion", "superficial", "seemingly", "manifest", "signify", "characterize", "villa", "objective", "icy", "strength", "ward", "invalid", "fluctuate", "fluctuation", "barge", "humanitarian", "compensation", "complement", "supplement", "mammal", "uneasy", "invariably", "inadequate", "impurity", "uncertain", "disregard", "irregularity", "absurd", "unfit", "undesirable", "unreasonable", "inaccurate", "irrespective", "inevitably", "watertight", "dissatisfaction", "improper", "opaque", "stuffy", "incomplete", "instability", "incompatible", "misfortune", "wretched", "immortal", "awkward", "disagreement", "absent", "sermon", "friction", "substance", "rule", "assumption", "recipe", "parameter", "spectator", "participant", "reference", "senator", "participate", "napkin", "cruelty", "warehouse", "hatch", "manipulate", "groove", "herb", "grassy", "tactics", "horizon", "ascertain" };

    /// <summary>
    /// 类中的被调用的公共变量
    /// </summary>
    private static string classForLoopCycleCount = "forLoopCount";
    private static string classWhileLoopCycleCount = "whileLoopCount";
    private static string classIfElse = "ifElse";

    /// <summary>
    /// 流程:生成垃圾目录树-->将路径下所有原始文件随机倒入垃圾目录树-->删除原始目录结构-->生成垃圾代码并随机倒入垃圾目录树
    /// </summary>
    public static void StartCreateRubiishCode()
    {
        Debug.LogWarning("StartCreateRubiishCode");

        if (!CheckRootPath())
        {
            CreateRubiish();
            CopyToTargetPath();
        }
    }

    /// <summary>
    /// 检查按标签保存的垃圾代码目录，如果已存在生成好的垃圾代码
    /// 则无需重新生成,直接拷贝到Assets目录
    /// </summary>
    private static bool CheckRootPath()
    {
        Debug.LogWarning("CheckRootPath");
        var rubbish_code_label = JenkinsEnv.Instance.Get("rubbish_code_label", "default"); // 默认为default
        rootPath = $"{Application.dataPath.Replace("Assets", "")}/RubbishCodes/{rubbish_code_label}";
        Debug.LogWarning($"rubbish_code_label:{rubbish_code_label} rootPath:{rootPath}");

        if (!Directory.Exists(rootPath)) // 文件夹不存在，则需要重新生成
        {
            Directory.CreateDirectory(rootPath);
            return false;
        }

        var files = Directory.GetFiles(rootPath, "*.*", SearchOption.AllDirectories);
        Debug.LogWarning($"rubbish files num:{files.Length}");
        if (files.Length == 0) // 文件夹里没有任何文件，也需要重新生成
        {
            return false;
        }

        CopyToTargetPath();

        return true;
    }

    // 清理Assets文件夹下的垃圾代码
    public static void ClearAssetsPath()
    {
        if(Directory.Exists(assetsPath))
        {
            Directory.Delete(assetsPath, true);
        }
    }

    // 将已有的垃圾代码拷贝至目标文件夹
    private static void CopyToTargetPath()
    {
        Debug.LogWarning("CopyToTargetPath");
        var rubbish_code_label = JenkinsEnv.Instance.Get("rubbish_code_label", "default");
        var targetPath = assetsPath + "/" + rubbish_code_label;
        CopyFolder(rootPath, targetPath);
    }

    /// <summary>
    /// 将配置文件夹复制到制定目录
    /// </summary>
    /// <param name="sourceFolder"></param>
    /// <param name="destFolder"></param>
    private static void CopyFolder(string sourceFolder, string destFolder)
    {
        //如果目标路径不存在,则创建目标路径
        if (!Directory.Exists(destFolder))
        {
            Directory.CreateDirectory(destFolder);
        }
        //得到原文件根目录下的所有文件
        string[] files = Directory.GetFiles(sourceFolder);
        foreach (string file in files)
        {
            string name = Path.GetFileName(file);
            string dest = Path.Combine(destFolder, name);
            File.Copy(file, dest, true);//复制文件
        }
        //得到原文件根目录下的所有文件夹
        string[] folders = Directory.GetDirectories(sourceFolder);
        foreach (string folder in folders)
        {
            string name = Path.GetFileName(folder);
            string dest = Path.Combine(destFolder, name);
            CopyFolder(folder, dest);//构建目标路径,递归复制文件
        }

    }

    //[MenuItem("Tools/Test/RubbishCode")]
    //static void TestRubbishCode()
    //{
    //    var rnum = 550;
    //    minFileCount = rnum;
    //    maxFileCount = rnum;
    //    StartCreateRubiishCode();
    //    AssetDatabase.Refresh();
    //}

    [MenuItem("Tools/将目录下所有文件移动到垃圾目录下")]
    static void MoveSourceFilesToRubbishDirectory()
    {
        if (!isUserSelectedRootPath())
        {
            return;
        }

        if (!Directory.Exists(rootPath))
        {
            Directory.CreateDirectory(rootPath);
        }

        //根目录下的原始子目录
        sourceTopDictorys = getSourceTopDictorys(rootPath);

        int randomDirectoryCount = Random.Range(minDirectoryCount, maxDirectoryCount);
        allDirectorys = createRandomDirectoryPath(randomDirectoryCount);

        MoveSourceFilesToRandomDirectory(rootPath, allDirectorys);
    }

    [MenuItem("Tools/开始生成垃圾代码")]
    static void CreateRubiishTool()
    {
        //for (int i = 0; i < 3000; i++)
        //{
        //    Debug.Log(getRandomName());

        //}
        //return;

        if (!isUserSelectedRootPath())
        {
            return;
        }

        CreateRubiish();
    }

    private static bool isUserSelectedRootPath()
    {
        UnityEngine.Object[] arr = Selection.GetFiltered(typeof(UnityEngine.Object), SelectionMode.TopLevel);
        if (arr.Length == 0)
        {
            rootPath = Application.dataPath + "/RubbishCodes";
            Debug.Log("未选中的目录,使用缺省目录：" + rootPath);
            return true;
        }
        rootPath = Application.dataPath.Substring(0, Application.dataPath.LastIndexOf('/')) + "/" + AssetDatabase.GetAssetPath(arr[0]);

        if (!Directory.Exists(rootPath))
        {
            Debug.Log(rootPath + "不是合法目录");
            return false;
        }
        Debug.Log("选中的目录:" + rootPath);
        return true;
    }


    /// <summary>
    /// 重建垃圾目录树，废弃原有目录结构和文件位置，将原有文件随机倒入垃圾目录树
    /// 将原根目录下所有文件随机移动到垃圾目录下，移动完成后删除原始子目录
    /// (.meta跟随其主文件一起移动到目标文件夹)
    /// </summary>
    private static void MoveSourceFilesToRandomDirectory(string rootPath, List<string> allDirectorys)
    {
        //根目录下的原始所有文件（包括所有子目录下的文件）
        List<string> sourceFilePaths = getAllFilestBy(rootPath);
        int fileCount = sourceFilePaths.Count;

        if (fileCount == 0)
        {
            Debug.Log("原目录下没有文件，不需要倒入垃圾目录树");
            deleteDictorys(sourceTopDictorys);
            return;
        }

        //存放文件的文件夹列表(垃圾目录)
        List<string> directorys = allDirectorys;

        string sourceFilePath = "";
        string targetFilePath = "";
        string fileName;

        for (int i = 0; i < fileCount; i++)
        {
            sourceFilePath = sourceFilePaths[i];
            fileName = Path.GetFileName(sourceFilePath);

            if (fileName == "Version.cs")// 这个文件不能挪位置，因为在根目录下，所有也不会被删除
                continue;

            if (directorys.Count > 0)
            {
                targetFilePath = directorys[Random.Range(0, directorys.Count)] + "/" + fileName;
            }

            File.Move(sourceFilePath, targetFilePath);
            //检测并移动.meta文件
            sourceFilePath = sourceFilePath + ".meta";
            if (File.Exists(sourceFilePath))
            {
                targetFilePath = targetFilePath + ".meta";
                File.Move(sourceFilePath, targetFilePath);
            }
            //Debug.Log(string.Format("{0}移动到{1}", sourceFilePath, targetFilePath));
            // System.Threading.Thread.Sleep(10);
        }

        Debug.Log(string.Format("根目录下原始文件数：{0};已随机移动到垃圾目录", fileCount));
        //删除原始子目录
        deleteDictorys(sourceTopDictorys);

    }

    /// <summary>
    /// 删除目录列表
    /// </summary>
    /// <param name="sourceTopDictorys"></param>
    private static void deleteDictorys(List<string> sourceTopDictorys)
    {
        int sourceDictoryCount = sourceTopDictorys.Count;
        string sourceDicPath;
        //DirectoryInfo dictoryInfo;
        for (int i = 0; i < sourceDictoryCount; i++)
        {
            sourceDicPath = sourceTopDictorys[i];
            //dictoryInfo = new DirectoryInfo(rootPath);
            //FileInfo[] fileInfos = dictoryInfo.GetFiles("*", SearchOption.AllDirectories);

            if (Directory.Exists(sourceDicPath))
            {
                Directory.Delete(sourceDicPath, true);
            }
            Debug.Log(string.Format("删除原始目录{0}", sourceDicPath));
            //检测并删除目录的.meta文件
            sourceDicPath = sourceDicPath + ".meta";
            if (File.Exists(sourceDicPath))
            {
                File.Delete(sourceDicPath);
            }
            //  System.Threading.Thread.Sleep(10);
        }
    }

    private static List<string> getSourceTopDictorys(string rootPath)
    {
        DirectoryInfo dictoryInfo = new DirectoryInfo(rootPath);
        //根目录下原始的子目录，目录下所有文件都移除原始目录后，会删掉原始的子目录
        DirectoryInfo[] sourceTopDictorys = dictoryInfo.GetDirectories("*", SearchOption.TopDirectoryOnly);

        List<string> allPath = new List<string>();
        foreach (DirectoryInfo info in sourceTopDictorys)
        {
            allPath.Add(info.FullName);
        }
        return allPath;
    }

    /// <summary>
    /// 获取目标目录下所有的文件路径（.meta文件不计入）
    /// </summary>
    /// <param name="rootPath"></param>
    /// <returns></returns>
    private static List<string> getAllFilestBy(string rootPath)
    {
        DirectoryInfo dictoryInfo = new DirectoryInfo(rootPath);
        List<string> allFilePath = new List<string>();
        FileInfo[] fileInfos = dictoryInfo.GetFiles("*", SearchOption.AllDirectories);
        string extension = "";
        foreach (FileInfo file in fileInfos)
        {
            extension = System.IO.Path.GetExtension(file.FullName);
            //.meta文件会随正式文件移动时移动过去
            if (extension == ".meta")
            {
                continue;
            }
            allFilePath.Add(file.FullName);
        }
        return allFilePath;
    }


    static void CreateRubiish()
    {
        Debug.LogWarning("CreateRubiish");

        if (!Directory.Exists(rootPath))
        {
            Directory.CreateDirectory(rootPath);
        }

        //根目录下的原始子目录
        sourceTopDictorys = getSourceTopDictorys(rootPath);

        int randomDirectoryCount = Random.Range(minDirectoryCount, maxDirectoryCount);
        allDirectorys = createRandomDirectoryPath(randomDirectoryCount);

        MoveSourceFilesToRandomDirectory(rootPath, allDirectorys);

        int randomFileCount = Random.Range(minFileCount, maxFileCount);
        List<string> files = getRandomFilePath(randomFileCount, allDirectorys);

        byte[] fileData;
        double totalFileSize = 0;
        for (int i = 0; i < files.Count; i++)
        {
            fileData = CreateCode(files[i]);
            if (fileData != null)
            {
                totalFileSize += fileData.Length;
            }
        }

        totalFileSize /= 1048576;//byte换算为mb
        Debug.Log(string.Format("输出目录：{0}；生成垃圾目录：{1}；生成垃圾代码：{2}; 总大小：{3}mb",
            rootPath, randomDirectoryCount, randomFileCount, totalFileSize));
    }

    /// <summary>
    /// 在根目录下随机创建目录
    /// </summary>
    /// <param name="directoryCount"></param>
    /// <returns></returns>
    private static List<string> createRandomDirectoryPath(int directoryCount)
    {
        string directoryName = "";
        List<string> directorys = new List<string>();
        string tempPath = "";
        
        for (int i = 0; i < directoryCount; i++)
        {
            directoryName = getRandomDirectoryName();
            //随机文件夹的父目录
            if (directorys.Count > 0)
            {
                tempPath = directorys[Random.Range(0, directorys.Count)];//随机抽取一个目录

                tempPath = tempPath.Replace(rootPath, "");//检查索引深度，超过设定极限就用新的目录
                string[] dirs = tempPath.Split('/');
                List<string> newDirs = new List<string>(dirs);
                if (newDirs.Count >= maxDirDeep)
                {
                    newDirs.RemoveRange(maxDirDeep - 1, newDirs.Count - maxDirDeep);
                    tempPath = string.Join("/", newDirs.ToArray());
                }
                tempPath = rootPath + "/" + tempPath;//加上根节点

                directoryName = tempPath + "/" + directoryName;
            }
            else
            {
                directorys.Add(rootPath);
                continue;
            }
            if (Directory.Exists(directoryName))
            {
                i--;
                continue;
            }
            directorys.Add(directoryName);

            Directory.CreateDirectory(directoryName);
        }
        Debug.Log(string.Format("生成垃圾目录：{0}个;", directoryCount));
        return directorys;
    }

    private static List<string> getRandomFilePath(int fileCount, List<string> directorys)
    {
        string fileName = "";
        List<string> files = new List<string>();
        for (int i = 0; i < fileCount; i++)
        {
            fileName = getRandomClassName() + ".cs"; ;
            if (directorys.Count > 0)
            {
                fileName = directorys[Random.Range(0, directorys.Count)] + "/" + fileName;
            }
            else
            {
                fileName = rootPath + "/" + fileName;
            }
            files.Add(fileName);
            //Debug.Log(fileName);
        }
        return files;
    }


    static byte[] CreateCode(string filePath)
    {
        if (File.Exists(filePath))
        {
            Debug.LogError("生成新文件的时候发现文件已经存在" + filePath);
            System.Console.WriteLine("生成新文件的时候发现文件已经存在" + filePath);
            return null;
        }

        FileStream fs = new FileStream(filePath, FileMode.OpenOrCreate);
        if (!fs.CanWrite)
        {
            System.Console.WriteLine("无法写入文件" + filePath);
            Debug.LogError("无法写入文件");
            return null;
        }
        string sss = fs.Name;
        int nameStartIndex = filePath.LastIndexOf("/") + 1;
        string fileName = filePath.Substring(nameStartIndex, filePath.Length - nameStartIndex - 3);

        string data = CreateClass(fileName, getRandomBool());


        byte[] bytes = Encoding.UTF8.GetBytes(data);
        //Debug.Log("class总长度：" + bytes.Length);

        fs.Write(bytes, 0, bytes.Length);

        fs.Flush();
        fs.Close();
        return bytes;
    }

    static string CreateClass(string className, bool implementMono)
    {
        usedMethodName.Clear();
        usedVarNameInCurClass.Clear();
        StringBuilder sb = new StringBuilder();
        sb.Append(CreatePragmaWarningDisable());
        
        sb.Append(CreateUsing());
        var str = CreateClassHead(className, implementMono);
        sb.Append(str);

        sb.Append(CreateControlVariables());

        int randomCount = Random.Range(minMethodCount, maxMethodCount);
        int randomCodeCount = 0;
        for (int i = 0; i < randomCount; i++)
        {
            int j = UnityEngine.Random.Range(20, 50);
            bool k = i % 2 == 0;
            string returnValue = GetReturnValue();
            randomCodeCount = Random.Range(minMethodRandomCodeCount, maxMethodCount);
            sb.Append(CreateMethod(k, returnValue, methodNamePrefix, randomCodeCount));
        }

        sb.Append("\n}");
        sb.Append(CreatePragmaWarningDisableEnd());

        return sb.ToString();
    }

    private static string GetReturnValue()
    {
        int i = UnityEngine.Random.Range(1, 9);

        switch (i)
        {
            case 1:
                return "int";
            case 2:
                return "string";
            case 3:
                return "long";
            case 4:
                return "object";
            case 5:
                return "Vector3";
            case 6:
                return "GameObject";
            case 7:
                return "Transform";
            case 8:
                return "float";
            default:
                return "int";
        }
    }



    //开始屏蔽警告打印
    static string CreatePragmaWarningDisable()
    {
        StringBuilder sb = new StringBuilder();
        sb.Append("#pragma warning disable 0414, 0219 \n");
        return sb.ToString();
    }
    //结束屏蔽警告打印
    static string CreatePragmaWarningDisableEnd()
    {
        StringBuilder sb = new StringBuilder();
        sb.Append("\n#pragma warning restore 0414, 0219");
        return sb.ToString();
    }
    static string CreateUsing()
    {
        StringBuilder sb = new StringBuilder();

        sb.Append("using UnityEngine;\nusing System;");

        return sb.ToString();
    }

    static string CreateClassHead(string className, bool implementMono)
    {
        string str = implementMono ? ":MonoBehaviour" : "";
        return "\npublic class " + "mm_"+ className + str + "\n{";
    }

    /// <summary>
    /// 创建类的变量（移除变量赋值）
    /// </summary>
    /// <returns></returns>
    static string CreateControlVariables()
    {
        string result = "";

        int randomClassVarCount = Random.Range(0, maxClassVarCount);
        int randomVarType;
        for (int i = 0; i < randomClassVarCount; i++)
        {
            randomVarType = Random.Range(0, 6);
            result += "\n\t" + getVarOrMethodSpecifier() + " " + createSingleRandomStatementNoEvaluation(randomVarType);
        }

        // if (getRandomBool())
        // {
        //     classForLoopCycleCount = getRandomVarName();
        //     result += "\n\t" + getVarOrMethodSpecifier() + " int " + classForLoopCycleCount + " = " + Random.Range(0, 1000000) + ";";
        // }
        // else
        // {
            classForLoopCycleCount = "";
        // }
        // if (getRandomBool())
        // {
        //     classIfElse = getRandomVarName();
        //     result += "\n\t" + getVarOrMethodSpecifier() + " bool " + classIfElse + " = true;";
        // }
        // else
        // {
            classIfElse = "";
        // }
        // if (getRandomBool())
        // {
        //     classWhileLoopCycleCount = getRandomVarName();
        //     result += "\n\t" + getVarOrMethodSpecifier() + " int " + classWhileLoopCycleCount + " = " + Random.Range(0, 1000000) + ";";
        // }
        // else
        // {
            classWhileLoopCycleCount = "";
        // }
        return result;
    }

    //访问修饰符
    private static List<string> accessSpecifiers = new List<string>() { "private", "public", "", "protected", "internal", "protected internal" };
    //属性的第二个修饰符
    private static List<string> varSecondSpecifiers = new List<string>() { "static", "" };
    //存放代码的文件夹(生成的垃圾目录)
    private static List<string> allDirectorys = new List<string>();
    //根目录下的原始子目录
    private static List<string> sourceTopDictorys;

    /// <summary>
    /// 获取随机的方法或属性修饰符
    /// </summary>
    /// <returns></returns>
    private static string getVarOrMethodSpecifier(bool isVar = true)
    {
        string randomSpe = accessSpecifiers[Random.Range(0, accessSpecifiers.Count)];
        List<string> secondSpecifiers;
        string secondSpe = "";
        // if (isVar)
        // {
        //     secondSpecifiers = varSecondSpecifiers;
        //     secondSpe = " " + secondSpecifiers[Random.Range(0, secondSpecifiers.Count)];
        // }
        // else
        // {
        //     secondSpe = "";
        // }

        return randomSpe + secondSpe;
    }

    private static bool getRandomBool()
    {
        int radomValue = Random.Range(0, 2);
        return radomValue == 0;
    }

    /// <summary>
    /// 创建一个随机函数
    /// </summary>
    /// <param name="hasReturnValue"></param>
    /// <param name="methodNamePrefix"></param>
    /// <returns></returns>
    static string CreateMethod(bool hasReturnValue, string returnValueType, string methodNamePrefix, int totalLine)
    {
        StringBuilder sb = new StringBuilder();

        sb.Append(CreateMethodHead(hasReturnValue, methodNamePrefix, returnValueType));

        sb.Append(CreateMethodBody(totalLine, hasReturnValue, returnValueType));

        sb.Append("\n\t}");

        return sb.ToString();
    }

    /// <summary>
    /// 创建函数头部，格式为 public 返回值 函数名(){},需要注意这些函数全部没有参数名，方便调用
    /// </summary>
    /// <param name="hasReturnValue"> 是否有返回值</param>
    /// <param name="methodNamePrefix">如果有返回值 返回值类型</param>
    /// <returns></returns>
    static string CreateMethodHead(bool hasReturnValue, string methodNamePrefix, string returnType)
    {
        var methodName = methodNamePrefix + getRandomMethodName();
        var returnStr = hasReturnValue ? returnType : "void";
        return "\n\n\t" + getVarOrMethodSpecifier(false) + " " + returnStr + " " + methodName + "()\n\t{";
    }

    /// <summary>
    /// 创建函数体，为包含在函数{}内部的代码，由几部分组拼而成
    /// </summary>
    /// <param name="totalCount">代码块数量</param>
    /// <param name="hasReturnValue">是否有返回值</param>
    /// <param name="ReturnValueType">如果有返回值，返回值的类型</param>
    /// <returns></returns>
    static string CreateMethodBody(int totalCount, bool hasReturnValue, string ReturnValueType)
    {
        string returnStatement = CreateReturnStatement(ReturnValueType, 2);//返回语句

        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < totalCount; i++)
        {
            sb.Append("\n\t\t");
            int j = UnityEngine.Random.Range(0, 9);
            int k = UnityEngine.Random.Range(0, 11);
            int randomSimpleCodeIndex = UnityEngine.Random.Range(0, 8);
            //添加一个随机代码块
            switch (j)
            {
                case 0:
                    sb.Append(CreateForLoop(createSingleRandomStatement(k)));
                    break;
                case 1:
                    int m = UnityEngine.Random.Range(0, 11);
                    sb.Append(CreateIfElse(createSingleRandomStatement(k), createSingleRandomStatement(m)));
                    break;
                case 2:
                    sb.Append(CreateWhile(createSingleRandomStatement(k)));
                    break;
                case 3:
                    sb.Append(createSingleRandomStatement(randomSimpleCodeIndex));
                    break;
                case 4:
                    sb.Append(createSingleRandomStatement(randomSimpleCodeIndex));
                    break;
                case 5:
                    sb.Append(createSingleRandomStatement(randomSimpleCodeIndex));
                    break;
                case 6:
                    sb.Append(createSingleRandomStatement(randomSimpleCodeIndex));
                    break;
                case 7:
                    sb.Append(createSingleRandomStatement(randomSimpleCodeIndex));
                    break;
                case 8:
                    sb.Append(createSingleRandomStatement(randomSimpleCodeIndex));
                    break;
                default:
                    break;
            }
        }
        if (hasReturnValue)
        {
            sb.Append(returnStatement);
        }

        return sb.ToString();
    }

    /// <summary>
    /// 创建For循环，其中循环次数为类的静态全局变量控制
    /// int forLoops：控制循环次数
    /// </summary>
    /// <param name="statementInForLoop">要放入for循环的具体语句</param>
    /// <returns></returns>
    static string CreateForLoop(string statementInForLoop)
    {
        string LoopCycleCount = classForLoopCycleCount;
        if (LoopCycleCount == "")
        {
            LoopCycleCount = ((int)Random.Range(0, 1500)).ToString();
        }
        string indexName = getRandomVarName();
        return "for(int " + indexName + " = 0; " + indexName + " < " + LoopCycleCount + "; " + indexName +
            "++)\n\t\t{\n\t\t\t" + statementInForLoop + "\n\t\t}";
    }

    /// <summary>
    /// 创建 if-else判断
    /// </summary>
    /// <param name="ifString">if语句里面要执行的东西</param>
    /// <param name="elseString">else语句里面要执行的东西</param>
    /// <returns></returns>
    static string CreateIfElse(string ifString, string elseString)
    {
        string returnText = "";
        if (classIfElse != "" && getRandomBool())
        {
            returnText += "if(" + classIfElse + ")\n\t\t{";
        }
        else
        {
            string boolName = getRandomVarName();
            returnText += "bool " + boolName + " = " + (getRandomBool() ? "true;" : "false;") + "\n\t\tif(" + boolName + ")\n\t\t{";
        }

        returnText += "\n\t\t\t" + ifString + "\n\t\t}\n\t\telse\n\t\t{\n\t\t\t" + elseString + "\n\t\t}";
        return returnText;
    }


    /// <summary>
    /// 创建while循环
    /// </summary>
    /// <param name="whileStr">while循环中要执行的东西</param>
    /// <returns></returns>
    static string CreateWhile(string whileStr)
    {
        string returnText = "";
        string LoopCycleCount = classWhileLoopCycleCount;
        if (LoopCycleCount == "")
        {
            LoopCycleCount = ((int)Random.Range(0, 1500)).ToString();
        }
        string indexName = getRandomVarName();
        returnText += "int " + indexName + " = 0;\n\t" + "\t" + "\twhile(" +
            indexName + " < " + LoopCycleCount + ")\n\t\t\t{\n\t\t\t" + whileStr + "\n\t\t\t}";
        return returnText;
    }

    /// <summary>
    /// 创建返回语句
    /// </summary>
    /// <param name="returnValueType"></param>
    /// <param name="indent"></param>
    /// <returns></returns>
    static string CreateReturnStatement(string returnValueType, int indent)
    {
        string returnText = "";
        switch (returnValueType)
        {
            case "int":
                returnText = ((int)Random.Range(0, 10000)).ToString();
                break;
            case "float":
                returnText = (Random.Range(0, 10000)).ToString();
                break;
            case "string":
                returnText = "\"" + getRandomString(0, 4) + "\"";
                break;
            case "long":
                returnText = (Random.Range(0, 1000000)).ToString();
                break;
            case "Vector3":
                returnText = "new Vector3(" + Random.Range(0, 10000) + ", " + Random.Range(0, 10000) + ", " + +Random.Range(0, 10000) + ")";
                break;
            case "object":
                returnText = "new " + unityComponentStrings[Random.Range(0, unityComponentStrings.Count)] + "()";
                break;
            case "GameObject":
            case "Transform":
                returnText = "new " + unityComponentStrings[Random.Range(0, unityComponentStrings.Count)] + "()";
                int loopCount = Random.Range(0, 4);
                for (int i = 0; i < loopCount; i++)
                {
                    int randomValue = Random.Range(0, 3);
                    switch (randomValue)
                    {
                        case 0:
                            returnText += ".transform";
                            break;
                        case 1:
                            returnText += ".gameObject";
                            break;
                        case 2:
                            returnText += "";
                            break;
                    }
                }
                if (returnValueType == "GameObject")
                {
                    returnText += ".gameObject";
                }
                else if (returnValueType == "Transform")
                {
                    returnText += ".transform";
                }
                break;
        }
        return "\n" + GetIndent(indent) + "return " + returnText + ";";
    }

    /// <summary>
    /// 获取缩进的字符串
    /// </summary>
    /// <param name="indent"></param>
    /// <returns></returns>
    static string GetIndent(int indent)
    {
        if (indent <= 0)
        {
            return "";
        }

        string suojinstr = string.Empty;

        for (int i = 0; i < indent; i++)
        {
            suojinstr += "\t";
        }

        return suojinstr;
    }

    static string getRandomDirectoryName()
    {
        return getRandomName(1,3);
    }

    static string getRandomVarName(int minCount = 0, int maxCount = 3)
    {
        string name = getRandomName(minCount, maxCount);

        if (systemVarName.Contains(name))
        {
            return getRandomVarName(minCount, maxCount);
        }

        if (usedVarNameInCurClass.Contains(name))
        {
            return getRandomVarName(minCount, maxCount);
        }
        else
        {
            usedVarNameInCurClass.Add(name);
            return name;
        }
    }

    static string getRandomString(int minCount = 5, int maxCount = 15)
    {
        //return getRandomNameBy(minCount, maxCount);

        int length = UnityEngine.Random.Range(minCount, maxCount);

        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < length; i++)
        {
            sb.Append(GetLetter(UnityEngine.Random.Range(1, 63)));
        }
        return sb.ToString();
    }

    static string getRandomName(int minCount = 1, int maxCount = 3)
    {
        string name = getRandomNameBy(minCount, maxCount);
        
        if (exceptNameList == null)
        {
            exceptNameList = new Dictionary<string, int>();
        }

        if (exceptNameList.ContainsKey(name))
        {
            return getRandomName(minCount, maxCount);
        }

        exceptNameList.Add(name, 1);
        return name;
    }


    static string getRandomNameBy(int minCount = 1, int maxCount = 5)
    {
        //return Cs.Mix.Public.str.RandNewName(exceptNameList, true);

        int length = UnityEngine.Random.Range(minCount, maxCount);

        StringBuilder sb = new StringBuilder();

        //if (hasRandomPrefix)
        //{
        //    string namePrefix = namePrefixStrings[Random.Range(0, namePrefixStrings.Count)];
        //    sb.Append(namePrefix);
        //}

        for (int i = 0; i < length; i++)
        {
            sb.Append(randomNameStrings[Random.Range(0, randomNameStrings.Count)]);
        }
        string nameLast = "";
        //如果长度为0则添加一个字符
        if (length == 0)
        {
            nameLast = GetLetter(UnityEngine.Random.Range(27, 53));
            sb.Append(nameLast);
        }
        //随机添加名字结尾的随机字符,或者 
        else if (UnityEngine.Random.Range(0, 3) == 0)
        {
            nameLast = GetLetter(UnityEngine.Random.Range(27, 63));
            sb.Append(nameLast);
        }
        
        return sb.ToString();
    }



    static string getRandomClassName()
    {
        string sb = getRandomName(1, 3);

        if (systemVarName.Contains(sb))
        {
            return getRandomClassName();
        }

        if (usedClassNames.Contains(sb))
        {
            return getRandomClassName();
        }
        else
        {
            usedClassNames.Add(sb);
            return sb;
        }
    }

    /// <summary>
    /// 随机函数名字
    /// </summary>
    /// <returns></returns>
    static string getRandomMethodName()
    {
        string sb = getRandomName(0, 3);
        if (systemVarName.Contains(sb))
        {
            return getRandomMethodName();
        }

        if (usedMethodName.Contains(sb))
        {
            return getRandomMethodName();
        }
        else
        {
            usedMethodName.Add(sb);
            return sb;
        }
    }

    //变量声明，不赋值
    static string createSingleRandomStatementNoEvaluation(int index)
    {
        switch (index)
        {
            case 0:
                //构建随机Random
                return "int " + getRandomVarName() + ";";
            case 1:
                //构建随机数字运算
                return getNumValueType(Random.Range(1, 6)) + " " + getRandomVarName() + ";";
            case 2:
                //构建随机bool
                return "bool " + getRandomVarName() + ";";
            case 3:
                //构建随机字符串
                return "string " + getRandomVarName() + ";";
            case 4:
                //构建随机数字
                return getNumValueType(Random.Range(1, 6)) + " " + getRandomVarName() + ";";
            case 5:
                //构建unity组件
                string componentType = unityComponentStrings[Random.Range(0, unityComponentStrings.Count)];
                return componentType + " " + getRandomVarName() + ";";
            case 6:
                //构建log
                return "UnityEngine.Debug.Log(\"" + getRandomString(0, 4) + "\");";
            default:
                return "";
        }
    }

    static string createSingleRandomStatement(int index)
    {
        switch (index)
        {
            case 0:
                //构建随机Random
                return "int " + getRandomVarName() +
                    " = UnityEngine.Random.Range(" + Random.Range(0, 50) + ", " + Random.Range(50, 1500) + ");";
            case 1:
                //构建随机数字运算
                System.Func<string> loopFun = () => { return GetSymbol(Random.Range(1, 6)) + Random.Range(1, 200); };
                return getNumValueType(Random.Range(1, 6)) + " " + getRandomVarName() + " = "
                    + Random.Range(0, 1600) + loopConnectString(loopFun, 0, 3) + ";";
            case 2:
                //构建随机bool
                return "bool " + getRandomVarName() + " = " + (getRandomBool() ? "true" : "false") + ";";
            case 3:
                //构建随机字符串
                return "string " + getRandomVarName() + " = \"" + getRandomString(0, 4) + "\";";
            case 4:
                //构建随机数字
                return getNumValueType(Random.Range(1, 6)) + " " + getRandomVarName() + " = "
                    + Random.Range(0, 100000000) + ";";
            case 5:
                //构建unity组件
                string componentType = unityComponentStrings[Random.Range(0, unityComponentStrings.Count)];
                return componentType + " " + getRandomVarName() + " = new " + componentType + "();";
            case 6:
                //构建log
                return "UnityEngine.Debug.Log(\"" + getRandomString(0, 4) + "\");";
            case 7:
                //随机调用一个类中的函数
                if (usedMethodName.Count == 0)
                {
                    return createSingleRandomStatement(Random.Range(0, 11));
                }
                return usedMethodName[Random.Range(0, usedMethodName.Count)] + "();";
            case 8:
                return CreateWhile(createSingleRandomStatement(Random.Range(0, 11)));
            case 9:
                return CreateForLoop(createSingleRandomStatement(Random.Range(0, 11)));
            case 10:
                return CreateIfElse(createSingleRandomStatement(Random.Range(0, 11)), createSingleRandomStatement(Random.Range(0, 11)));
            default:
                return "";
        }
    }

    static string loopConnectString(System.Func<string> function, int minLoop, int maxLoop)
    {
        int loopCount = Random.Range(minLoop, maxLoop);
        string result = "";
        for (int i = 0; i < loopCount; i++)
        {
            result += function();
        }

        return result;
    }

    static string getNumValueType(int index)
    {
        switch (index)
        {
            case 1:
                return "int";
            case 2:
                return "float";
            case 3:
                return "long";
            case 4:
                return "double";
            case 5:
                return "decimal";
        }
        return "int";
    }

    static string GetSymbol(int index)
    {
        switch (index)
        {
            case 1:
                return "+";
            case 2:
                return "-";
            case 3:
                return "*";
            case 4:
                return "/";
            case 5:
                return "%";
        }
        return "+";
    }

    static string GetLetter(int index)
    {
        switch (index)
        {
            case 1:
                return "A";
            case 2:
                return "B";
            case 3:
                return "C";
            case 4:
                return "D";
            case 5:
                return "E";
            case 6:
                return "F";
            case 7:
                return "G";
            case 8:
                return "H";
            case 9:
                return "I";
            case 10:
                return "J";
            case 11:
                return "K";
            case 12:
                return "L";
            case 13:
                return "M";
            case 14:
                return "N";
            case 15:
                return "O";
            case 16:
                return "P";
            case 17:
                return "Q";
            case 18:
                return "R";
            case 19:
                return "S";
            case 20:
                return "T";
            case 21:
                return "U";
            case 22:
                return "V";
            case 23:
                return "W";
            case 24:
                return "X";
            case 25:
                return "Y";
            case 26:
                return "Z";
            case 27:
                return "a";
            case 28:
                return "b";
            case 29:
                return "c";
            case 30:
                return "d";
            case 31:
                return "e";
            case 32:
                return "f";
            case 33:
                return "g";
            case 34:
                return "h";
            case 35:
                return "i";
            case 36:
                return "j";
            case 37:
                return "k";
            case 38:
                return "l";
            case 39:
                return "m";
            case 40:
                return "n";
            case 41:
                return "o";
            case 42:
                return "p";
            case 43:
                return "q";
            case 44:
                return "r";
            case 45:
                return "s";
            case 46:
                return "t";
            case 47:
                return "u";
            case 48:
                return "v";
            case 49:
                return "w";
            case 50:
                return "x";
            case 51:
                return "y";
            case 52:
                return "z";
            case 53:
                return "1";
            case 54:
                return "2";
            case 55:
                return "3";
            case 56:
                return "4";
            case 57:
                return "5";
            case 58:
                return "6";
            case 59:
                return "7";
            case 60:
                return "8";
            case 61:
                return "9";
            case 62:
                return "0";
            case 63:
                return "!";
            case 64:
                return "@";
            case 65:
                return "#";
            case 66:
                return "$";
            case 67:
                return "%";
            case 68:
                return "^";
            case 69:
                return "&";
            case 70:
                return "*";
            case 71:
                return "(";
            case 72:
                return ")";
            case 73:
                return "_";
            case 74:
                return "+";
            case 75:
                return "-";
            case 76:
                return "=";
            case 77:
                return "{";
            case 78:
                return "}";
            case 79:
                return "[";
            case 80:
                return "]";
            case 81:
                return "|";
            case 82:
                return ";";
            case 83:
                return ":";
            case 84:
                return "<";
            case 85:
                return ">";
            case 86:
                return "?";
            case 87:
                return ",";
            case 88:
                return ".";
            case 89:
                return "~";
            case 90:
                return "`";
            case 91:
                return "，";
            case 92:
                return "。";
            case 93:
                return "“";
            case 94:
                return "”";
            case 95:
                return "‘";
            case 97:
                return "’";
            case 98:
                return "、";
            case 99:
                return "；";
            default:
                return "";

        }
    }


}