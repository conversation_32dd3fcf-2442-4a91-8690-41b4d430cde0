using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;
namespace War.Base
{
    /// <summary>
    /// 提供渠道资源差异打包的操作
    /// </summary>
    public class CasualChannelBuild : Singleton<CasualChannelBuild>
    {
        public List<string> shareLuaFolder = new List<string>();
        public const string bundleDir = "casualchannel";
        public const string game_root = "Assets/CasualChannel";
        private const string PREFIX_CGAME_AB = "assets/AssetBundles/Android/casualchannel/"; // 只需要Android使用?

        private const string ABS_JSON = "/abs.json";
        private const string KEY_2_MARK_JSON = "/langkey2mark.json";
        private const string KEY_2_EXCLUDE_JSON = "/langkey2exclude.json";
        public const string CHANNEL_RES_KEY = "ch_res_key";


        // 生成分版本资源配置
        public void BuildABList()
        {
            if(!Directory.Exists(game_root))
            {
                Debug.Log($"CasualChannelBuild {game_root} not exist");
                return;
            }

            Dictionary<string, object> dic = new Dictionary<string, object>();

            var folders = Directory.GetDirectories(game_root);
            foreach (var f in folders)
            {
                "".Print(f);
                //if(File.Exists(configpath))
                //{
                //    File.Delete(configpath);
                //}
                var btfF = f.Replace("\\", "/");
                var assets = AssetDatabase.FindAssets("", new string[] { f });
                HashSet<string> set = new HashSet<string>();
                foreach (var guid in assets)
                {
                    var apath = AssetDatabase.GUIDToAssetPath(guid);

                    var ab = AssetDatabase.GetImplicitAssetBundleName(apath);
                    var paths = AssetDatabase.GetAssetPathsFromAssetBundle(ab);
                    foreach (var p in paths)
                    {
                        if (p.StartsWith(btfF, System.StringComparison.OrdinalIgnoreCase) == false)
                        {
                            "".Print("BuildABList:", f, ab, p);
                            ab = null;
                            break;
                        }
                    }
                    if (string.IsNullOrEmpty(ab)) continue;
                    set.Add(ab);
                }
                if (set.Count > 0)
                {
                    dic[Path.GetFileNameWithoutExtension(f)] = set;
                }
            }

            var key2markpath = game_root + KEY_2_MARK_JSON;
            string key2markJson = null;
            Dictionary<string, string[]> key2mark = new Dictionary<string, string[]>();
            if (File.Exists(key2markpath))
            {

                try
                {
                    key2markJson = File.ReadAllText(key2markpath);
                    key2mark = ToolUti.ToObj<Dictionary<string, string[]>>(key2markJson);

                    //foreach (var mark in key2mark)
                    //{
                    //    if (mark.Value.Length > 0)
                    //    {
                    //        dic[mark.Key] = mark.Value;
                    //    }
                    //}
                }
                catch (System.Exception e)
                {
                    "".Print("error:", e.ToString());
                }
            }

            var folderslist = new List<string>(folders);
            var allMarks = folderslist.ConvertAll((m) => Path.GetFileNameWithoutExtension(m).Replace("\\", "/"));
            key2mark["allmarks"] = allMarks.ToArray();
            File.WriteAllText(key2markpath, ToolUti.ToJson(key2mark));

            var configpath = game_root + ABS_JSON;
            File.WriteAllText(configpath, ToolUti.ToJson(new Dictionary<string, object>() {
                { "langmark2list",dic },
                { "langkey2mark", key2mark},

            }));

            GenExcludeConfigs(key2mark);
            //File.WriteAllText(configpath, ToolUti.ToJson(dic));
            AssetDatabase.Refresh();
        }

        // 生成分包剔除配置
        public void GenExcludeConfigs(Dictionary<string, string[]> key2mark)
        {
            string allmake_key = "allmarks";
            if (!key2mark.TryGetValue(allmake_key, out var allmarks)) return;
            Dictionary<string, string[]> key2exclude = new Dictionary<string, string[]>();
            var allmarkSet = new HashSet<string>();
            allmarkSet.UnionWith(allmarks);

            var prefix = PREFIX_CGAME_AB;
            foreach (var kv in key2mark)
            {
                string key = kv.Key;
                string[] value = kv.Value;
                if (key == allmake_key) continue;

                //if (!Directory.Exists(game_root + "/" + key)) continue;

                var set = new HashSet<string>(allmarkSet);
                set.ExceptWith(value);

                var exclude = new List<string>();
                foreach (var m in set)
                {
                    exclude.Add(prefix + m.ToLower());
                }

                key2exclude[key] = exclude.ToArray();
            }

            //allmarks内没有专门定标签的资源也需要添加到移除列表
            foreach (var k in allmarks)
            {
                string key = k;
                if (key2exclude.ContainsKey(key)) continue;
                //if (!Directory.Exists(game_root + "/" + key)) continue;

                string[] value = new string[] { k };
                var set = new HashSet<string>(allmarkSet);
                set.ExceptWith(value);

                var exclude = new List<string>();
                foreach (var m in set)
                {
                    exclude.Add(prefix + m.ToLower());
                }

                key2exclude[key] = exclude.ToArray();
            }
            var allexclude = new HashSet<string>();
            foreach (var kv in key2exclude)
            {
                allexclude.UnionWith(kv.Value);
            }
            key2exclude["All"] = new List<string>(allexclude).ToArray();

            var key2markpath = game_root + KEY_2_EXCLUDE_JSON;
            File.WriteAllText(key2markpath, ToolUti.ToJson(key2exclude));
            "".Print("key2exclude", ToolUti.ToJson(key2exclude));
        }
        // 1.分包剔除列表拷贝到streamingasset目录下
        // 2.出包前剔除非当前渠道资源ab
        public void Copy2Package()
        {
            if(Directory.Exists(game_root))
            {
                BuilDirectoryByJson();
                DeleteOtherMarkRes();
            }
        }

        public void BuilDirectoryByJson()
        {
            var key2excludepath = game_root + KEY_2_EXCLUDE_JSON;
            string key2excludeJson = null;
            Dictionary<string, string[]> key2exclude = new Dictionary<string, string[]>();
            if (File.Exists(key2excludepath))
            {
                try
                {
                    key2excludeJson = File.ReadAllText(key2excludepath);
                    key2exclude = ToolUti.ToObj<Dictionary<string, string[]>>(key2excludeJson);
                    Dictionary<string, string[]> dic = new Dictionary<string, string[]>();
                    string currPath = UnityEngine.Application.streamingAssetsPath + "/Pack";//获取当前文件夹路径
                    if (false == System.IO.Directory.Exists(currPath))//检查是否存在文件夹
                    {
                        System.IO.Directory.CreateDirectory(currPath);//创建指定文件夹
                    }
                    foreach (var mark in key2exclude)
                    {
                        if (mark.Value.Length > 0)
                        {
                            //Debug.LogError("mark.Key:" + mark.Key + ",mark.Value:" + mark.Value);
                            //TODO:将key2exclude.json各个key值生成文件夹，在对应的文件夹里再次生成json文件，json文件里仅保留对应key值内容，并且json文件中的key值改成TestDelete
                            string subPath = currPath + "/" + mark.Key;
                            if (false == System.IO.Directory.Exists(subPath))//检查是否存在文件夹
                            {
                                System.IO.Directory.CreateDirectory(subPath);//创建指定文件夹
                            }
                            string subJsonPath = subPath + "/" + mark.Key + ".json";
                            if (key2exclude.ContainsKey(mark.Key))
                                dic["TestDelete"] = new List<string>(mark.Value).ToArray();
                            File.WriteAllText(subJsonPath, ToolUti.ToJson(dic));
                        }
                    } 
                    "".Print("dic", ToolUti.ToJson(dic));
                    
                }
                catch (System.Exception e)
                {
                    "".Print("error:", e.ToString());
                }
            }
        }

        //根据当前mark删除abres
        public static void DeleteOtherMarkRes()
        {
            LogHelp.Instance.Log("DeleteOtherMarkRes-start");

            var key2markpath = game_root + KEY_2_EXCLUDE_JSON;
            if (File.Exists(key2markpath) == false)
            {
                "".Print("key2markpath no exist!");
                LogHelp.Instance.Log("DeleteOtherMarkRes-end");

                return;
            }

            var key2exclude = ToolUti.ToObj<Dictionary<string, string[]>>(File.ReadAllText(key2markpath));

            var key2markStreamings = key2markpath.Replace(game_root, UnityEngine.Application.streamingAssetsPath);
            if (!File.Exists(key2markStreamings)) // 目标文件不存在，则直接拷贝
            {
                File.Copy(key2markpath, key2markStreamings, true);
            }
            else
            {
                /// 不能直接覆盖目标文件，需要将渠道的key_2_exclude添加进去
                var key2excludeStreamings = ToolUti.ToObj<Dictionary<string, string[]>>(File.ReadAllText(key2markStreamings));
                foreach (var exclude in key2exclude)
                {
                    if (key2excludeStreamings.ContainsKey(exclude.Key))
                    {
                        string[] existedArray = key2excludeStreamings[exclude.Key];
                        string[] array = new string[existedArray.Length + exclude.Value.Length];
                        System.Array.Copy(existedArray, 0, array, 0, existedArray.Length);
                        System.Array.Copy(exclude.Value, 0, array, existedArray.Length, exclude.Value.Length);
                        key2excludeStreamings[exclude.Key] = array;
                    }
                    else
                    {
                        key2excludeStreamings[exclude.Key] = exclude.Value;
                    }
                }

                File.WriteAllText(key2markStreamings, ToolUti.ToJson(key2excludeStreamings));
            }

            //根据当前mark删除abres

            var mark = JenkinsEnv.Instance.Get(CHANNEL_RES_KEY, "");
            Debug.Log("ch_res_key=" + mark);
            var pRoot = $"{Application.streamingAssetsPath}/{BuildScript.AssetBundlesOutputPath}/{BuildScript.GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget)}";
            var hc = ModifyAB.Instance.GetHashCheckForFilesTxtInPath(pRoot);

            "".Print("GetHashCheckFromFolder", pRoot, hc?.name, hc?.ToJson());

            var configpath = game_root + ABS_JSON;
            if (File.Exists(configpath) == false)
            {
                "".Print("Error file not exist", configpath);
                return;
            }
            var absJson = File.ReadAllText(configpath);
            var absDic = ToolUti.ToObj<Dictionary<string, object>>(absJson);
            var mark2list = ToolUti.ToObj<Dictionary<string, List<string>>>(absDic["langmark2list"]?.ToString());

            var lowerMark2list = new Dictionary<string, List<string>>();

            foreach (var kv in mark2list)
            {
                lowerMark2list[kv.Key.ToLower()] = kv.Value;
            }

            // var keyList = new List<string>(hc?.list.Keys);
            List<string> keyList = null;
            if (hc)
            {
                // new List<string>(param) , param 不能为 NULL
                keyList = new List<string>(hc.list.Keys);
            }
            if (key2exclude.TryGetValue(mark, out var removelist))
            {
                var prefix = PREFIX_CGAME_AB;
                Debug.LogWarning(mark + "remove start ====" + hc.list.Count);
                foreach (var path in removelist)
                {
                    var rpath = path.Replace(prefix, $"{Application.streamingAssetsPath}/{BuildScript.AssetBundlesOutputPath}/{BuildScript.GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget)}/{bundleDir}/");
                    "".Print("Copy2Package delete", mark, rpath, Directory.Exists(rpath));
                    if (Directory.Exists(rpath))
                    {
                        Directory.Delete(rpath, true);
                    }

                    // 删除files对应文件
                    if (hc && mark2list != null)
                    {
                        //var mmark = mark.Replace(PREFIX_CGAME_AB, "casualgame/");
                        var mmark = PREFIX_CGAME_AB + mark;
                        var remove_mark = path.Replace(PREFIX_CGAME_AB, "");

                        "".Print("remove filesx ", mark, remove_mark, keyList?.Count);
                        /*if (remove_mark != "allmarks" && lowerMark2list.ContainsKey(remove_mark))
                        {
                            var remove_ablist = lowerMark2list[remove_mark];

                            foreach (var k in remove_ablist)
                            {
                                hc.list.Remove(k);
                                "".Print("remove in files ", mark, k);
                            }
                        }*/
                        var list = new List<string>(hc.list.Keys);
                        string path_mark = bundleDir + "/" + remove_mark + "/";
                        Debug.LogWarning("remove==" + path_mark);
                        foreach (var item in list)
                        {
                            //Debug.LogError(item + "   " + item.StartsWith(path_mark, System.StringComparison.OrdinalIgnoreCase));
                            if (item.StartsWith(path_mark, System.StringComparison.OrdinalIgnoreCase))
                            {
                                hc.list.Remove(item);
                            }
                        }
                    }
                }
                if (hc)
                {
                    File.WriteAllText(hc.name, hc.ToJson());
                }
            }
            else
            {
                "".Print("Copy2Package ch_res_key==null");
                //默认包含所有渠道资源，为兼容到时分包方式，可以在Jenkins上直接移除ch_res_key配置
            }
            Debug.LogWarning(mark + "remove end ====" + hc.list.Count);
            LogHelp.Instance.Log("DeleteOtherMarkRes-end");
        }

        /// <summary>
        /// 向files里面添加首包多语言资源配置
        /// </summary>
        /// <param name="f"></param>
        /// <returns></returns>
        public List<string> InsertCasualChannelRes2files(string destination)
        {
            var abRootPath = Path.GetDirectoryName(destination);
            var f = ModifyAB.Instance.GetHashCheckForFilesTxtInPath(abRootPath);
            var listInsert = InsertCasualChannelRes2files(f);
            if (listInsert.Count == 0)
            {
                "".PrintError("InsertCasualChannelRes2files error,", abRootPath);
            }
            else
            {
                var filePath = f.name;
                "".Print("InsertCasualChannelRes2files finish,", listInsert.Count);
                if (string.IsNullOrEmpty(filePath) == false)
                {
                    "".Print("InsertCasualChannelRes2files write file,", filePath);
                    File.WriteAllText(filePath, f.ToJson());
                }
            }
            return listInsert;
        }
        /// <summary>
        /// 向files里面添加首包多语言资源配置
        /// </summary>
        /// <param name="f"></param>
        /// <returns></returns>
        public List<string> InsertCasualChannelRes2files(hashCheck f)
        {
            var listInsert = new List<string>();
            if (f == null) return listInsert;

            var patch_mark = JenkinsEnv.Instance.Get("patch_mark");
            hashCheck h = null;
            var path = "";

            if (!string.IsNullOrEmpty(patch_mark))
            {

                path = BuildScript.GetNewestFilePath(patch_mark);
            }
            else
            {

                //在生成的 ab 资源路径下的files.txt 文件
                path = string.Format("{0}/files.txt",
                    Path.Combine(BuildScript.AssetBundlesOutputPath,
                       BuildScript.GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget)));

            }

            if (File.Exists(path))
            {
                try
                {
                    var fFolder = Path.GetDirectoryName(path);
                    h = ModifyAB.Instance.GetHashCheckForFilesTxtInPath(fFolder);
                }
                catch (System.Exception e)
                {
                    "".PrintError("InsertCasualChannelRes2files path error:", path, e.ToString());
                }
            }
            if (h == null)
            {
                return listInsert;
            }
            var list = new List<string>(h.list.Keys);
            string path_mark = bundleDir + "/";
            foreach (var item in list)
            {
                if (item.StartsWith(path_mark, System.StringComparison.OrdinalIgnoreCase))
                {
                    listInsert.Add(item);
                }
            }

            foreach (var item in listInsert)
            {
                f.Update(item, h.list[item]);
            }
            "".Print("InsertCasualChannelRes2files", listInsert.Count);
            return listInsert;
        }
        // =========================以下为测试代码=====================================
        /*[MenuItem("Test/CasualChannel/Build")]
        public static void TestCasualChannelBuild()
        {
            bool useChannel = true;
            LogHelp.Instance.Log("CasualGameBuild+");
            //计算当前打包分版本资源各版本资源列表
            CasualGameBuild.Instance.AddCommonResKey();
            CasualGameBuild.Instance.BuildABList();
            LogHelp.Instance.Log("CasualGameBuild-");

            if (useChannel)
            {
                LogHelp.Instance.Log("CasualChannelBuild+");
                //计算当前打包分版本资源各版本资源列表
                CasualChannelBuild.Instance.BuildABList();
                LogHelp.Instance.Log("CasualChannelBuild-");
            }

            LogHelp.Instance.Log("Copy2Package-start");
            CasualGameBuild.Instance.Copy2Package();
            LogHelp.Instance.Log("Copy2Package-end");

            if (useChannel)
            {
                LogHelp.Instance.Log("Channel Copy2Package-start");
                CasualChannelBuild.Instance.Copy2Package();
                LogHelp.Instance.Log("Channel Copy2Package-end");
            }
            //PlayerPrefs.DeleteAll();
            //PlayerPrefs.Save();
        }

        [MenuItem("Test/CasualChannel/Copy")]
        public static void TestCasualChannelCopy()
        {
            var pRoot = $"{Application.streamingAssetsPath}/{BuildScript.AssetBundlesOutputPath}/{BuildScript.GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget)}";
            var hc = ModifyAB.Instance.GetHashCheckFromFolder(pRoot);

            var configpath = game_root + ABS_JSON;
            if (File.Exists(configpath) == false)
            {
                "".Print("Error file not exist", configpath);
                return;
            }
            var absJson = File.ReadAllText(configpath);
            var absDic = ToolUti.ToObj<Dictionary<string, object>>(absJson);
            var mark2list = ToolUti.ToObj<Dictionary<string, List<string>>>(absDic["mark2list"]?.ToString());
            foreach (var v in mark2list)
            {
                Debug.Log($"===={v.Key}");
                if (!hc.mark2list.ContainsKey(v.Key))
                    hc.mark2list.Add(v.Key, v.Value.ToArray());
                else
                    hc.mark2list[v.Key] = v.Value.ToArray();
            }

            File.WriteAllText(hc.name, hc.ToJson());
        }

        [MenuItem("Test/CasualChannel/ChooseMark")]
        public static void TestCasualChannelChooseMark()
        {
            var pRoot = $"{Application.streamingAssetsPath}/{BuildScript.AssetBundlesOutputPath}/{BuildScript.GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget)}";
            var hc = ModifyAB.Instance.GetHashCheckFromFolder(pRoot);
            hashCheck.ChooseMark(hc, "ModeControl", "cn");
        }*/
        // =================================
    }
}
