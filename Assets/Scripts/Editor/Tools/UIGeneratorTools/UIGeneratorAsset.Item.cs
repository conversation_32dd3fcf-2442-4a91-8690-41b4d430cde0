/******************************************************
 * Description:   UI 绑定资源对象
 * Author:        袁楠
 * Created Date:  2024年9月9日
 ********************************************************/

using Sirenix.OdinInspector;
using System;
using UnityEditor;
using UnityEngine;
using System.IO;
using System.Text;

// ReSharper disable once CheckNamespace
namespace GameTools.UIGeneratorTools
{
    public partial class UIGeneratorAsset : ScriptableObject
    {
        [Serializable]
        public class UIItem
        {
            [AssetsOnly, OnValueChanged("BindModuleName")]
            public GameObject prefab;

            public string viewName;

            public void BindModuleName()
            {
                viewName = ConvertCamelCaseToUnderscore(prefab);
            }

            [<PERSON><PERSON>, LabelText("Apply")]
            public void Generate()
            {
                SetAssetBundlePath(prefab);
                UIGeneratorTool.GenerateItemModule(this, true);
            }

            [But<PERSON>, LabelText("Apply Binding")]
            public void GenerateBinding()
            {
                SetAssetBundlePath(prefab);
                UIGeneratorTool.GenerateItemModule(this, false);
            }
        }

        [Serializable]
        public class UIViewController
        {
            [AssetsOnly, OnValueChanged("BindModuleName")]
            public GameObject prefab;

            public string viewName;

            public bool isBlurBg;
            public bool needAni;
            public bool needBtnScale = true;
            //是否需要设置偏移 支持刘海
            public bool needSetOffset  = false;
            public void BindModuleName()
            {
                viewName = ConvertCamelCaseToUnderscore(prefab);
            }

            [Button, LabelText("Apply")]
            public void Generate()
            {
                SetAssetBundlePath(prefab);
                UIGeneratorTool.GenerateViewControllerModule(this, true);
            }

            [Button, LabelText("Apply Binding")]
            public void GenerateBinding()
            {
                SetAssetBundlePath(prefab);
                UIGeneratorTool.GenerateViewControllerModule(this, false);
            }
        }

        // 将驼峰命名法转换为下划线命名法
        private static string ConvertCamelCaseToUnderscore(GameObject prefab)
        {
            if (prefab == null) return "";

            var camelCaseName = prefab.name;
            // 处理特殊前缀
            camelCaseName = camelCaseName.Replace("GW", "Gw");
            camelCaseName = camelCaseName.Replace("UI", "Ui");

            var result = new StringBuilder();
            for (int i = 0; i < camelCaseName.Length; i++)
            {
                var currentChar = camelCaseName[i];
                // 如果是大写字母，并且不是第一个字母，则加下划线
                if (char.IsUpper(currentChar) && i > 0)
                {
                    if (result.Length > 0 && result[result.Length - 1] != '_')
                    {
                        result.Append('_');
                    }
                }
                result.Append(char.ToLower(currentChar));
            }

            return result.ToString();
        }

        // 自动设置AB包名
        private static void SetAssetBundlePath(GameObject prefab)
        {
            if (prefab == null) return;
            var assetPath = AssetDatabase.GetAssetPath(prefab);
            if (!Path.HasExtension(assetPath))
            {
                Debug.Log("选择到了文件夹" + assetPath);
            }
            else
            {
                var assetImporter = AssetImporter.GetAtPath(assetPath);
                var assetBundleName = assetPath.Substring(assetPath.IndexOf("/", StringComparison.Ordinal) + 1);
                assetImporter.assetBundleName = assetBundleName;
                LogHelp.clipboard = assetBundleName.ToLower();
            }
        }
    }
}