/*******************************************************
 * Description:   UI绑定模板
 * Author:        袁楠
 * Created Date:  2024年9月10日
 ********************************************************/


using System.Collections.Generic;
using System.IO;
using UnityEngine;

// ReSharper disable once CheckNamespace
namespace GameTools.UIGeneratorTools
{
    public static partial class UITemplate
    {
        public static readonly string BindingTemplate = $@"local require = require
local typeof = typeof

#requireStr

module(""#fileName"")

UIPath = ""#uiPath""

WidgetTable ={{
#bindingStr
}}
";
    }
}