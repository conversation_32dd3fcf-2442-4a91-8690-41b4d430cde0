/*******************************************************
 * Description:   UIView 模板
 * Author:        袁楠
 * Created Date:  2024年9月10日
 ********************************************************/

// ReSharper disable once CheckNamespace

namespace GameTools.UIGeneratorTools
{
    public static partial class UITemplate
    {
        public static readonly string ViewTemplate = @"local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local UIUtil = CS.Common_Util.UIUtil

local enum_define = require ""enum_define""
local ui_base = require ""ui_base""
local lang = require ""lang""
local util = require ""util""
local class = require ""class""
local binding = require ""#fileName_binding""

--region View Life
module(""#fileName"")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)

    self.VData = {}
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == ""table"" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.VData = nil    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic

--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
#windowExtend
        if data and type(data) == ""table"" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
#windowStr1
        else
#windowStr2
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
";
    }
}