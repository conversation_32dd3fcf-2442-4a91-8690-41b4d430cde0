/*******************************************************
 * Description:   UIView 模板
 * Author:        袁楠
 * Created Date:  2024年9月10日
 ********************************************************/

// ReSharper disable once CheckNamespace

namespace GameTools.UIGeneratorTools
{
    public static partial class UITemplate
    {
        public static readonly string ItemTemplate = @"local require = require
local type = type
local pairs = pairs

local card_sprite_asset = require ""card_sprite_asset""
local log = require ""log""
local util = require ""util""
local util_widget_table = require ""util_widget_table""
local binding = require ""#fileName_binding""

module(""#fileName"")
local itemView = { }

itemView.widget_table = binding.WidgetTable

function itemView:ctor()
    self.isLoaded = false
    self.gameObject = nil
    self.transform = nil
    self.IData = {
        spriteAsset = card_sprite_asset.CreateSpriteAsset(),
    }
end

function itemView:Init(prefab)
    if prefab then
        util_widget_table.BindWidgetTableItem(self, prefab, self.widget_table)
        self.isLoaded = true
    else
        log.Error(""ui_battlePass_info Init gameObject is nil"")
    end
end

function itemView:UpdateData(data)
    
end

function itemView:LoadSpriteAsset(image, pathName)
    if not image or not pathName then
        return
    end

    self.IData.spriteAsset:GetSprite(pathName, function(sp)
        if self.isLoaded and not util.IsObjNull(sp) then
            image.sprite = sp
        end
    end)
end

function itemView:Dispose()
    util_widget_table.DisposeWidgetTableManageEvent(self, self.widget_table)
    self.isLoaded = false
    self.gameObject = nil
    self.transform = nil
    if self.IData then
        for i, v in pairs(self.IData) do
        if type(v) == ""table"" and v.Dispose then
            v:Dispose()
        end
    end
    self.IData = nil
end

--region Item Logic
#eventStr
--endregion

return itemView
";
    }
}