/*******************************************************
 * Description:   UI 生成规则配置文件
 * Author:        袁楠
 * Created Date:  2024年9月9日
 ********************************************************/

using System;
using System.Collections.Generic;
using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.Serialization;

// ReSharper disable once CheckNamespace
namespace GameTools.UIGeneratorTools
{
    public class UIGeneratorRules : ScriptableObject
    {
        [Serializable]
        public class Rule
        {
            [LabelText("前缀(xx_)")] public string prefix;

            [LabelText("类型")] public string type;
        }

        [TableList, LabelText("规则列表"), InfoBox(" 特殊规则: \n" +
                                               "1.item 当检测到item_的命名的时候,会默认将其下的子对象当成一个独立的Item模块,将停止向下生成binging的绑定; \n" +
                                               "2.btnb 当检测到back_的命名的时候,会为其按钮绑定backEvent事件;")]
        public List<Rule> rules = new List<Rule>()
        {
            new Rule() { prefix = "item", type = "UnityEngine.GameObject" },
            new Rule() { prefix = "back", type = "UnityEngine.UI.Button" },
            new Rule() { prefix = "obj", type = "UnityEngine.GameObject" },
            new Rule() { prefix = "tf", type = "UnityEngine.Transform" },
            new Rule() { prefix = "rtf", type = "UnityEngine.RectTransform" },
            new Rule() { prefix = "img", type = "UnityEngine.UI.Image" },
            new Rule() { prefix = "rImg", type = "UnityEngine.UI.RawImage" },
            new Rule() { prefix = "txt", type = "UnityEngine.UI.Text" },
            new Rule() { prefix = "tmp", type = "TMPro.TextMeshProUGUI" },
            new Rule() { prefix = "inp", type = "UnityEngine.UI.InputField" },
            new Rule() { prefix = "sld", type = "UnityEngine.UI.Slider" },
            new Rule() { prefix = "sr", type = "UnityEngine.UI.ScrollRect" },
            new Rule() { prefix = "srt", type = "UI.UGUIExtend.ScrollRectTable" },
            new Rule() { prefix = "sri", type = "UI.UGUIExtend.ScrollRectItem" },
            new Rule() { prefix = "tog", type = "UnityEngine.UI.Toggle" },
            new Rule() { prefix = "btn", type = "UnityEngine.UI.Button" },
            new Rule() { prefix = "ator", type = "UnityEngine.Animator" },
            new Rule() { prefix = "ss", type = "War.UI.SpriteSwitcher" },
            new Rule() { prefix = "grad", type = "War.UI.Gradient" },
        };
    }
}