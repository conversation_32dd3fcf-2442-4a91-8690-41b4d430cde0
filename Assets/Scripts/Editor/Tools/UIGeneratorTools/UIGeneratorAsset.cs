/*******************************************************
 * Description:   UI 自动化资源持久化对象
 * Author:        袁楠
 * Created Date:  2024年9月9日
 ********************************************************/

using System.Collections.Generic;
using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.Serialization;

// ReSharper disable once CheckNamespace
namespace GameTools.UIGeneratorTools
{
    public partial class UIGeneratorAsset : ScriptableObject
    {
        [LabelText("UI Binding Folder"), ReadOnly] public string uiGeneratorPath = "Assets/Lua/ui_module/";

        [LabelText("UI Folder"), ReadOnly] public string uiPath = "Assets/Lua/";

        [LabelText("UI Module Folder"), FolderPath(ParentFolder = "Assets/Lua/"), InfoBox("请设置输出的文件夹"),
         GUIColor("GetModuleFolderColor")]
        public string modulePath = "";

        [LabelText("UI Auto Create Folder")] public bool autoCreateUIFolder = true;

        [TableList, LabelText("VC 模块"), InfoBox("1. prefab:对应的UI预制体\n" +
                                                "2. moduleName:自动生成UI模块名字\n" +
                                                "3. isBlurBg:设置背景,需要给UI上加上一个命名为closeBtn的按钮，否则不生效\n" +
                                                "4. needAni:设置弹出动画,一般用于弹窗\n" +
                                                "5. needBtnScale:给预制体下的所有按钮添加缓动动画\n" +
                                                "6. needSetOffset:设置UI是否支持刘海\n" +
                                                "7. Apply:生成 {moduleName}_binding.txt , {moduleName}.txt , {moduleName}_controller.txt 三个文件")]
        public List<UIViewController> uiViewControllers = new List<UIViewController>();

        [TableList, LabelText("Item 模块"), InfoBox("1. prefab:对应的UI预制体\n" +
                                                  "2. moduleName:自动生成UI模块名字\n" +
                                                  "3. Apply:生成{moduleName}_binding.txt, {moduleName}.txt 两个文件")]
        public List<UIItem> uiItems = new List<UIItem>();

        private Color GetModuleFolderColor()
        {
            if (string.IsNullOrEmpty(modulePath))
                return Color.HSVToRGB(0, 1f, 1f);
            return Color.HSVToRGB(0.33f, 1f, 1f);
        }

        public UIViewController AddUIViewController(GameObject prefab)
        {
            foreach (var vcItem in uiViewControllers)
            {
                if (vcItem.prefab == prefab)
                    return vcItem;
            }

            var uiViewController = new UIViewController
                { prefab = prefab, viewName = UIGeneratorAsset.ConvertCamelCaseToUnderscore(prefab) };
            uiViewControllers.Add(uiViewController);
            return uiViewController;
        }

        public UIItem AddUIItem(GameObject prefab)
        {
            foreach (var item in uiItems)
            {
                if (item.prefab == prefab)
                    return item;
            }

            var uiItem = new UIItem
                { prefab = prefab, viewName = UIGeneratorAsset.ConvertCamelCaseToUnderscore(prefab) };
            uiItems.Add(uiItem);
            return uiItem;
        }
    }
}