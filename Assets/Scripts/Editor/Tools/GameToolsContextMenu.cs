/*******************************************************
 * Description:   右键菜单功能
 * Author:        袁楠
 * Created Date:  2024年11月19日
 ********************************************************/

using GameTools.UIGeneratorTools;
using SuperTools;
using UnityEditor;
using UnityEngine;

// ReSharper disable once CheckNamespace
namespace GameTools
{
    public class GameToolsContextMenu : Editor
    {
        [SuperTMenuItem(EMenuType.Main, "Assets/UI Generator Tool/Create View Controller All Files", false)]
        private static void CreateUIGeneratorViewControllerFiles()
        {
            if (Selection.objects.Length > 0)
            {
                var uiGeneratorAsset = UIGeneratorTool.GetUIGeneratorAsset();
                foreach (var obj in Selection.objects)
                {
                    string path = AssetDatabase.GetAssetPath(obj);
                    var prefabType = PrefabUtility.GetPrefabAssetType(obj);
                    if (prefabType == PrefabAssetType.Regular || prefabType == PrefabAssetType.Variant)
                    {
                        GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                        if (prefab != null)
                        {
                            var item = uiGeneratorAsset.AddUIViewController(prefab);
                            item.Generate();
                        }
                    }
                }
            }

            AssetDatabase.Refresh();
        }

        [SuperTMenuItem(EMenuType.Main, "Assets/UI Generator Tool/Create View Controller Binding File", false)]
        private static void CreateUIGeneratorViewControllerBindingFiles()
        {
            if (Selection.objects.Length > 0)
            {
                var uiGeneratorAsset = UIGeneratorTool.GetUIGeneratorAsset();
                foreach (var obj in Selection.objects)
                {
                    string path = AssetDatabase.GetAssetPath(obj);
                    var prefabType = PrefabUtility.GetPrefabAssetType(obj);
                    if (prefabType == PrefabAssetType.Regular || prefabType == PrefabAssetType.Variant)
                    {
                        GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                        if (prefab != null)
                        {
                            var item = uiGeneratorAsset.AddUIViewController(prefab);
                            item.GenerateBinding();
                        }
                    }
                }
            }

            AssetDatabase.Refresh();
        }
        
        [SuperTMenuItem(EMenuType.Main, "Assets/UI Generator Tool/Create Item All Files", false)]
        private static void CreateUIGeneratorItemFiles()
        {
            if (Selection.objects.Length > 0)
            {
                var uiGeneratorAsset = UIGeneratorTool.GetUIGeneratorAsset();
                foreach (var obj in Selection.objects)
                {
                    string path = AssetDatabase.GetAssetPath(obj);
                    var prefabType = PrefabUtility.GetPrefabAssetType(obj);
                    if (prefabType == PrefabAssetType.Regular || prefabType == PrefabAssetType.Variant)
                    {
                        GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                        if (prefab != null)
                        {
                            var item = uiGeneratorAsset.AddUIItem(prefab);
                            item.Generate();
                        }
                    }
                }
            }
            AssetDatabase.Refresh();
        }
        
        [SuperTMenuItem(EMenuType.Main, "Assets/UI Generator Tool/Create Item Binding Files", false)]
        private static void CreateUIGeneratorBindingFiles()
        {
            if (Selection.objects.Length > 0)
            {
                var uiGeneratorAsset = UIGeneratorTool.GetUIGeneratorAsset();
                foreach (var obj in Selection.objects)
                {
                    string path = AssetDatabase.GetAssetPath(obj);
                    var prefabType = PrefabUtility.GetPrefabAssetType(obj);
                    if (prefabType == PrefabAssetType.Regular || prefabType == PrefabAssetType.Variant)
                    {
                        GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                        if (prefab != null)
                        {
                            var item = uiGeneratorAsset.AddUIItem(prefab);
                            item.GenerateBinding();
                        }
                    }
                }
            }
            AssetDatabase.Refresh();
        }
    }
}