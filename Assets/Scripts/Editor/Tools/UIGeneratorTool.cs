using System;
using Sirenix.OdinInspector;
using Sirenix.OdinInspector.Editor;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using UnityEditor;
using UnityEngine;

// ReSharper disable once CheckNamespace
namespace GameTools.UIGeneratorTools
{
    public class UIGeneratorTool : OdinEditorWindow
    {
        private const string AssetFilePath = "Assets/Scripts/Editor/Tools/UIGeneratorTools/UIGeneratorAsset.asset";
        private const string RulesFilePath = "Assets/Scripts/Editor/Tools/UIGeneratorTools/UIGeneratorRule.asset";

        [InlineEditor(ObjectFieldMode = InlineEditorObjectFieldModes.Boxed), LabelText("UI Generator Asset")]
        public UIGeneratorAsset uiGeneratorAsset;

        [InlineEditor(ObjectFieldMode = InlineEditorObjectFieldModes.Boxed), LabelText("UI Generator Rules")]
        public UIGeneratorRules uiGeneratorRule;

        [OnInspectorInit]
        private void Init()
        {
            uiGeneratorAsset = AssetDatabase.LoadAssetAtPath<UIGeneratorAsset>(AssetFilePath);
            if (uiGeneratorAsset == null)
            {
                uiGeneratorAsset = ScriptableObject.CreateInstance<UIGeneratorAsset>();
                AssetDatabase.CreateAsset(uiGeneratorAsset, AssetFilePath);
            }

            uiGeneratorRule = AssetDatabase.LoadAssetAtPath<UIGeneratorRules>(RulesFilePath);
            if (uiGeneratorRule == null)
            {
                uiGeneratorRule = ScriptableObject.CreateInstance<UIGeneratorRules>();
                AssetDatabase.CreateAsset(uiGeneratorRule, RulesFilePath);
            }
        }

        /// <summary>
        /// 自动化生成UI模块
        /// </summary>
        public static void GenerateItemModule(UIGeneratorAsset.UIItem item, bool createLogic = true)
        {
            var requireKeys = new List<string>();
            var bindingKeys = new List<string>();
            var eventKeys = new List<string>();
            // 生成对应的数据集合
            GeneratePrefabInfo(item.prefab, ref requireKeys, ref bindingKeys, ref eventKeys);
            // 生成文件
            CreateUIBindingFile(item.prefab, item.viewName, requireKeys, bindingKeys);
            if (createLogic)
            {
                CreateUIItemFile(item, eventKeys);
            }
            Debug.Log("Generate Item Module Finish");
        }

        /// <summary>
        /// 自动化生成UI模块
        /// </summary>
        public static void GenerateViewControllerModule(UIGeneratorAsset.UIViewController item, bool createLogic = true)
        {
            var requireKeys = new List<string>();
            var bindingKeys = new List<string>();
            var eventKeys = new List<string>();
            // 生成对应的数据集合
            GeneratePrefabInfo(item.prefab, ref requireKeys, ref bindingKeys, ref eventKeys);
            // 生成文件
            CreateUIBindingFile(item.prefab, item.viewName, requireKeys, bindingKeys);
            if (createLogic)
            {
                CreateUIViewFile(item);
                CreateUIControllerFile(item, eventKeys);
            }
            Debug.Log("Generate UI Module Finish");
        }

        /// <summary>
        /// 自动化生成UI绑定文件
        /// </summary>
        private static void CreateUIBindingFile(GameObject prefab, string moduleName, List<string> requireKeys,
            List<string> bindingKeys)
        {
            var bindingFileName = $"{moduleName}_binding";
            var bindingTemplate = UITemplate.BindingTemplate.Replace("#fileName", bindingFileName);
            var requireStr = new StringBuilder();
            foreach (var t in requireKeys)
            {
                requireStr.AppendLine(t);
            }

            bindingTemplate = bindingTemplate.Replace("#requireStr", requireStr.ToString());

            var bindingStr = new StringBuilder();
            foreach (var t in bindingKeys)
            {
                bindingStr.AppendLine(t);
            }

            bindingTemplate = bindingTemplate.Replace("#bindingStr", bindingStr.ToString());

            var uiPathStr = GetAssetBundlePath(AssetDatabase.GetAssetPath(prefab));
            bindingTemplate = bindingTemplate.Replace("#uiPath", uiPathStr);

            var folderPath = GetUIGeneratorAssetBindingFolderPath();
            var filePath = Path.Combine(folderPath, bindingFileName + ".txt");
            File.WriteAllText(filePath, bindingTemplate);
            Debug.Log($"Generate UI Binding {filePath} save to {filePath}");
        }

        private static void CreateUIViewFile(UIGeneratorAsset.UIViewController item)
        {
            var uiPath = GetUIGeneratorAssetUIFolderPath();
            var folderPath = GetUIGeneratorAssetModuleFolderPath(item.viewName);
            var fileName = $"{item.viewName}.txt";
            var filePath = Path.Combine(folderPath, fileName);
            if (!IsFileExists(uiPath, fileName))
            {
                // 创建文件夹
                EnsureDirectoryExists(folderPath);

                var viewTemplate = UITemplate.ViewTemplate.Replace("#fileName", item.viewName);
                var windowExtend = new StringBuilder();
                var windowStr1 = new StringBuilder();
                var windowStr2 = new StringBuilder();
                string setOffsetStr = item.needSetOffset ? "true" : "false";
                if (item.isBlurBg)
                {
                    windowExtend.AppendLine("\t\twindow.isBlurBg = true");
                }
                if (item.needAni && item.needBtnScale)
                {
                    windowStr1.Append($"\t\t\twindow:LoadUIResource(tempPath, nil, tempParent, nil, true, {setOffsetStr})");
                    windowStr2.Append($"\t\t\twindow:LoadUIResource(ui_path, nil, nil, nil, true, {setOffsetStr})");
                }
                else if (item.needAni)
                {
                    windowStr1.Append(
                        $"\t\t\twindow:LoadUIResource(tempPath, nil, tempParent, nil, true, {setOffsetStr}, nil, false)");
                    windowStr2.Append($"\t\t\twindow:LoadUIResource(ui_path, nil, nil, nil, true, {setOffsetStr}, nil, false)");
                }
                else
                {
                    windowStr1.Append($"\t\t\twindow:LoadUIResource(tempPath, nil, tempParent, nil, nil, {setOffsetStr})");
                    windowStr2.Append($"\t\t\twindow:LoadUIResource(ui_path, nil, nil, nil,nil, {setOffsetStr})");
                }

                viewTemplate = viewTemplate.Replace("#windowExtend", windowExtend.ToString());
                viewTemplate = viewTemplate.Replace("#windowStr1", windowStr1.ToString());
                viewTemplate = viewTemplate.Replace("#windowStr2", windowStr2.ToString());
                File.WriteAllText(filePath, viewTemplate);
                Debug.Log($"Generate UI View {item.viewName} save to {filePath}");
            }
            else
            {
                Debug.Log($"Generate UI View {item.viewName} file already exists");
            }
        }

        private static void CreateUIControllerFile(UIGeneratorAsset.UIViewController item, List<string> eventKeys)
        {
            var uiPath = GetUIGeneratorAssetUIFolderPath();
            var folderPath = GetUIGeneratorAssetModuleFolderPath(item.viewName);
            var fileName = $"{item.viewName}_controller.txt";
            var filePath = Path.Combine(folderPath, fileName);
            if (!IsFileExists(uiPath, fileName))
            {
                // 创建文件夹
                EnsureDirectoryExists(folderPath);

                var controllerTemplate =
                    UITemplate.ControllerTemplate.Replace("#fileName", $"{item.viewName}_controller");
                var eventStr = new StringBuilder();
                foreach (var t in eventKeys)
                {
                    eventStr.AppendLine(t);
                }

                controllerTemplate = controllerTemplate.Replace("#eventStr", eventStr.ToString());

                File.WriteAllText(filePath, controllerTemplate);
                Debug.Log($"Generate UI Controller {item.viewName} save to {filePath}");
            }
            else
            {
                Debug.Log($"Generate UI Controller {item.viewName} file already exists");
            }
        }

        private static void CreateUIItemFile(UIGeneratorAsset.UIItem item, List<string> eventKeys)
        {
            var uiPath = GetUIGeneratorAssetUIFolderPath();
            var folderPath = GetUIGeneratorAssetModuleFolderPath(null);
            var fileName = $"{item.viewName}.txt";
            var filePath = Path.Combine(folderPath, fileName);
            if (!IsFileExists(uiPath, fileName))
            {
                // 创建文件夹
                EnsureDirectoryExists(folderPath);
                var itemTemplate = UITemplate.ItemTemplate.Replace("#fileName", item.viewName);
                var eventStr = new StringBuilder();
                foreach (var t in eventKeys)
                {
                    eventStr.AppendLine(t);
                }

                eventStr = eventStr.Replace("UIController", "itemView");
                itemTemplate = itemTemplate.Replace("#eventStr", eventStr.ToString());

                File.WriteAllText(filePath, itemTemplate);
                Debug.Log($"Generate UI Item {item.viewName} save to {filePath}");
            }
            else
            {
                Debug.Log($"Generate UI Item {item.viewName} file already exists");
            }
        }

        #region 生成绑定的数据

        /// <summary>
        /// 获得UIGeneratorAsset文件,如果没有就创建
        /// </summary>
        public static UIGeneratorAsset GetUIGeneratorAsset()
        {
            var uiAssets = AssetDatabase.LoadAssetAtPath<UIGeneratorAsset>(AssetFilePath);
            if (uiAssets != null) return uiAssets;
            uiAssets = ScriptableObject.CreateInstance<UIGeneratorAsset>();
            AssetDatabase.CreateAsset(uiAssets, AssetFilePath);
            return uiAssets;
        }

        /// <summary>
        /// 获得UIGeneratorRules文件,如果没有就创建
        /// </summary>
        private static UIGeneratorRules GetUIGeneratorRules()
        {
            var rulesAssets = AssetDatabase.LoadAssetAtPath<UIGeneratorRules>(RulesFilePath);
            if (rulesAssets != null) return rulesAssets;
            rulesAssets = ScriptableObject.CreateInstance<UIGeneratorRules>();
            AssetDatabase.CreateAsset(rulesAssets, RulesFilePath);
            return rulesAssets;
        }

        /// <summary>
        /// 获得UIGeneratorAsset下的UIPath路径
        /// </summary>
        private static string GetUIGeneratorAssetUIFolderPath()
        {
            var uiAssets = GetUIGeneratorAsset();
            return uiAssets.uiPath;
        }

        /// <summary>
        /// 获得UIGeneratorAsset下的BindingPath路径
        /// </summary>
        private static string GetUIGeneratorAssetBindingFolderPath()
        {
            var uiAssets = GetUIGeneratorAsset();
            var bindingPath = uiAssets.uiGeneratorPath + "auto_generate/";
            EnsureDirectoryExists(bindingPath);
            return bindingPath;
        }

        /// <summary>
        /// 获得UIGeneratorAsset下的ModulePath路径
        /// </summary>
        private static string GetUIGeneratorAssetModuleFolderPath(string moduleName)
        {
            var uiAssets = GetUIGeneratorAsset();
            var modulePath = uiAssets.uiPath;
            if (!string.IsNullOrEmpty(uiAssets.modulePath))
            {
                modulePath += uiAssets.modulePath + "/";
            }

            if (uiAssets.autoCreateUIFolder && !string.IsNullOrEmpty(moduleName))
            {
                modulePath += moduleName + "/";
            }

            return modulePath;
        }

        /// <summary>
        /// 确保指定的目录存在，如果不存在则创建它。
        /// </summary>
        private static void EnsureDirectoryExists(string path)
        {
            string fullPath = Path.GetFullPath(path);
            if (!Directory.Exists(fullPath))
            {
                Directory.CreateDirectory(fullPath);
            }
        }

        /// <summary>
        /// 检查指定目录及其所有子目录中是否存在具有指定名称的文件。
        /// </summary>
        private static bool IsFileExists(string directory, string fileName)
        {
            try
            {
                if (!Directory.Exists(directory))
                {
                    Console.WriteLine("指定的目录不存在。");
                    return false;
                }

                string[] files = Directory.GetFiles(directory, "*", SearchOption.AllDirectories);
                foreach (var file in files)
                {
                    if (string.Equals(Path.GetFileName(file), fileName, StringComparison.OrdinalIgnoreCase))
                    {
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("发生了错误: " + ex.Message);
            }

            return false;
        }

        private static IReadOnlyDictionary<string, string> GetUIRulesIReadOnlyDictionary()
        {
            var rulesAssets = GetUIGeneratorRules();
            return rulesAssets.rules.ToDictionary(t => t.prefix, t => t.type);
        }

        private static void GeneratePrefabInfo(GameObject prefab, ref List<string> requireKeys,
            ref List<string> bindingKeys, ref List<string> eventKeys)
        {
            // 转化成字符串，方便读取
            var ruleDic = GetUIRulesIReadOnlyDictionary();
            RecursiveBindPrefab(prefab.transform, ruleDic, "", true, ref requireKeys, ref bindingKeys, ref eventKeys);
        }

        private static void RecursiveBindPrefab(Transform item, IReadOnlyDictionary<string, string> ruleDic,
            string rootPath, bool isRoot,
            ref List<string> requireKeys, ref List<string> bindingKeys, ref List<string> eventKeys)
        {
            if (item.name.Contains("_") || item.name.Contains("closeBtn"))
            {
                ParseTransformNameRule(item.name, out var name, out var ruleArray);
                foreach (var rule in ruleArray)
                {
                    if (!ruleDic.TryGetValue(rule, out var typeName)) continue;

                    ParseTypeNameRule(typeName, out var compName, out var requireStr);
                    if (!requireKeys.Contains(requireStr))
                        requireKeys.Add(requireStr);
                    ParseTypeNameEventRule(name, compName, rule, out var eventName, out var eventStr);
                    if (!string.IsNullOrEmpty(eventStr))
                        eventKeys.Add(eventStr);

                    var bindingStr = $"\t{rule}_{name} = {{ path = \"{rootPath}\", type = {compName}, {eventName}}},";
                    bindingKeys.Add(bindingStr);
                }
            }

            if (item.childCount <= 0 || (!isRoot && item.name.Contains("item_"))) return;
            foreach (Transform child in item.transform)
            {
                var newRootPath = rootPath == "" ? child.name : $"{rootPath}/{child.name}";
                RecursiveBindPrefab(child, ruleDic, newRootPath, false, ref requireKeys, ref bindingKeys,
                    ref eventKeys);
            }
        }

        private static void ParseTransformNameRule(string transformName, out string name, out string[] rules)
        {
            var index = transformName.IndexOf('_');
            if (index == -1) // "closeBtn"
            {
                name = transformName;
                rules = new[] { "btn" };
            }
            else
            {
                name = transformName.Substring(index + 1);

                var ruleStr = transformName.Substring(0, index);
                rules = ruleStr.Split('&');
            }
        }

        private static void ParseTypeNameRule(string typeName, out string compName, out string requireStr)
        {
            var lastDotIndex = typeName.LastIndexOf('.');
            compName = lastDotIndex != -1 ? typeName.Substring(lastDotIndex + 1) : typeName;

            requireStr = $"local {compName} = CS.{typeName}";
        }

        private static void ParseTypeNameEventRule(string name, string compName, string rule, out string eventName,
            out string eventStr)
        {
            switch (compName)
            {
                // 目前只处理几个比较通用的 Button,Toggle,Slider,InputField
                case "Button":
                    var btnStr = $"OnBtn{CapitalizeFirstLetter(name)}ClickedProxy";
                    if (name.Contains("closeBtn") || rule.Equals("back"))
                    {
                        eventName = $"event_name = \"{btnStr}\", backEvent = true";
                        eventStr =
                            $"function  UIController:{btnStr}()\n\tui_window_mgr:UnloadModule(self.view_name)\nend";
                    }
                    else
                    {
                        eventName = $"event_name = \"{btnStr}\"";
                        eventStr = $"function  UIController:{btnStr}()\nend";
                    }

                    break;
                case "InputField":
                    var changeStr = $"OnInput{CapitalizeFirstLetter(name)}ValueChange";
                    var endStr = $"OnInput{CapitalizeFirstLetter(name)}EndEdit";
                    eventName = $"value_changed_event = \"{changeStr}\" , end_edit_event = \"{endStr}\"";
                    eventStr = $"function  UIController:{changeStr}(text)\nend\n" +
                               $"function  UIController:{endStr}(text)\nend";
                    break;
                case "Toggle":
                    var changeStr2 = $"OnTog{CapitalizeFirstLetter(name)}ValueChange";
                    eventName = $"value_changed_event = \"{changeStr2}\"";
                    eventStr = $"function  UIController:{changeStr2}(state)\nend";
                    break;
                case "Slider":
                    var changeStr3 = $"OnSlider{CapitalizeFirstLetter(name)}ValueChange";
                    eventName = $"value_changed_event = \"{changeStr3}\"";
                    eventStr = $"function  UIController:{changeStr3}(value)\nend";
                    break;
                default:
                    eventName = "";
                    eventStr = "";
                    break;
            }
        }

        private static string CapitalizeFirstLetter(string input)
        {
            if (string.IsNullOrEmpty(input))
            {
                return input;
            }

            if (input.Length == 1)
            {
                return input.ToUpper();
            }

            return char.ToUpper(input[0]) + input.Substring(1);
        }

        private static string GetAssetBundlePath(string path)
        {
            return path.Substring(path.IndexOf('/') + 1).ToLower();
        }

        #endregion
    }
}