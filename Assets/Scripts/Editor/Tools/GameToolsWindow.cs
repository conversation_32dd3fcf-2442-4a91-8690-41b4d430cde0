using GameTools.UIGeneratorTools;
using Sirenix.OdinInspector.Editor;
using Sirenix.Utilities;
using Sirenix.Utilities.Editor;
using SuperTools;
using UnityEngine;

// ReSharper disable once CheckNamespace
namespace GameTools
{
    internal class GameToolsWindow : OdinMenuEditorWindow
    {
        [SuperTMenuItem(EMenuType.Main, "SToolCollection/工具集")]
        private static void OpenWindow()
        {
            var window = GetWindow<GameToolsWindow>();
            window.position = GUIHelper.GetEditorWindowRect().AlignCenter(1280, 720);
        }

        protected override OdinMenuTree BuildMenuTree()
        {
            var tree = new OdinMenuTree(supportsMultiSelect: true)
            {
                { "UI Generator Tool", ScriptableObject.CreateInstance<UIGeneratorTool>() }
            };
            return tree;
        }
    }
}