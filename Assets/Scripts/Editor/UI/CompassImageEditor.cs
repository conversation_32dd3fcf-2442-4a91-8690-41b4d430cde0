using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

namespace War.UI
{
    [CustomEditor(typeof(CompassImage), true)]
    [CanEditMultipleObjects]
    public class CompassImageEditor : UnityEditor.UI.ImageEditor
    {
        public override void OnInspectorGUI()
        {
            base.OnInspectorGUI();
            CompassImage compassImage = (CompassImage)(target);
            compassImage.range = (float)EditorGUILayout.DoubleField("Range", compassImage.range);
            compassImage.offset = (float)EditorGUILayout.DoubleField("Offset", compassImage.offset);
        }
    }
}