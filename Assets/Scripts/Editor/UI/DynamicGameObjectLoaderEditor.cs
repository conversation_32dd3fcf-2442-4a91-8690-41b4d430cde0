using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using War.UI;

[CustomEditor(typeof(DynamicGameObjectLoader))]
public class DynamicGameObjectLoaderEditor : Editor
{
    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();

        if (GUILayout.Button("load"))
        {
            DynamicGameObjectLoader dynamicLoader = target as DynamicGameObjectLoader;
            dynamicLoader.Unload();
            int length = dynamicLoader.abnames.Count;
            string abname;
            string[] assetPaths;
            for (int i = 0; i < length; i++)
            {
                abname = dynamicLoader.abnames[i];
                assetPaths = AssetDatabase.GetAssetPathsFromAssetBundle(abname);
                if (assetPaths.Length == 0)
                {
                    "".Print("There is no asset with name \"" + abname);
                    continue;
                }
                // @TODO: Now we only get the main object from the first asset. Should consider type also.
                UnityEngine.Object target = AssetDatabase.LoadMainAssetAtPath(assetPaths[0]);
                dynamicLoader.OnAbGoLoaded(abname, target as GameObject);
            }
        }

        if (GUILayout.Button("clearLoaded"))
        {
            DynamicGameObjectLoader dynamicLoader = target as DynamicGameObjectLoader;
            dynamicLoader.Unload();
        }
    }

    public void EditorUpdate()
    {
    }
}


