
using UnityEditor;
using UnityEditor.UI;
using War.UI;

[CustomEditor(typeof(UIText), true)]
public class UITextEditor : TextEditor
{
    SerializedProperty m_ellipsis;

    protected override void OnEnable()
    {
        base.OnEnable();

        m_ellipsis = serializedObject.FindProperty("ellipsis");
    }

    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();
        EditorGUILayout.Space();

        serializedObject.Update();
        EditorGUILayout.PropertyField(m_ellipsis);
        serializedObject.ApplyModifiedProperties();
    }
}
