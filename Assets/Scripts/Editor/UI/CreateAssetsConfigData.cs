using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System.Linq;
using Sirenix.OdinInspector.Demos;
using War.UI;
using System.IO;

public class CreateAssetsConfigData
{
    public static CreateAssetsConfigData instance = new CreateAssetsConfigData();
    public static CreateAssetsConfigData Instance
    {
        get
        {
            return instance;
        }
    }
    private SpriteAssets spriteAssets;
    private Dictionary<string, List<Sprite>> spriteDict = new Dictionary<string, List<Sprite>>();
    private ExportCSV exportCSV = new ExportCSV();
    private List<ExportCSV.PatientStatisticsOutputDto> ConnectEffectList = new List<ExportCSV.PatientStatisticsOutputDto>();
    private Dictionary<string, string> subSpriteAssetsData = new Dictionary<string, string>();
    
    public void InitAssetCsvData(SpriteAssets target, Dictionary<string, List<Sprite>> sd, bool onlyCsv = false)
    {
        spriteAssets = target;
        spriteDict = sd;
        ConnectEffectList.Clear();
        CreateCsvTitle();
        string path = Application.dataPath.Replace("BinClient/Client/Assets", "Tools/csv_script");
        if (Directory.Exists(path) == false)
        {
            Directory.CreateDirectory(path);
        }
        SetAssetCSVData(onlyCsv);
        string[] directories = Directory.GetDirectories(path);
        foreach (string directory in directories)
        {
            string folderName = Path.GetFileName(directory);
            string childPath = path + "/" + folderName;
            Debug.LogError($"FestivalAssetsConfig.csv  输出到  {childPath} ");
            string filePath = $"{childPath}/{spriteAssets.name}AssetsConfig.csv";
            if (System.IO.Directory.Exists(childPath) == false)
            {
                EditorUtility.DisplayDialog("提示", $"路径错误,请添加目录  {childPath}", "确定");
                return;
            }

            if (System.IO.File.Exists(filePath))
            {
                System.IO.File.Delete(filePath);
                if (System.IO.File.Exists(filePath))
                {
                    EditorUtility.DisplayDialog("提示", $"删除失败，请手动删除 FestivalAssetsConfig", "确定");
                    ExportCSV.OpenDirectory(childPath);
                    return;
                }
            }

            exportCSV.ExportPatientStatisticsDetails(ConnectEffectList, filePath,
                "{name}assetName,assetAB,signalTexture");
        }

        ExportCSV.OpenDirectory(path);
    }

    public void InitSpriteAssetsData(SpriteAssets target, Dictionary<string, List<Sprite>> sd, bool onlyChangeScriptableObject = false)
    {
        spriteAssets = target;
        spriteDict = sd;
        subSpriteAssetsData.Clear();

        SetAssetSpriteData(onlyChangeScriptableObject);
    }
    public void CreateCsvTitle()
    {
        List<string> col = new List<string>()
        {
            "{type}string,string,int32",
            "{filter}c,c,c",
            "{array}",
            "{arraytype}",
            "{index}1"
        };
        for (int i = 0; i < col.Count; i++)
        {
            ExportCSV.PatientStatisticsOutputDto dto = new ExportCSV.PatientStatisticsOutputDto();
            dto.datas.Add(col[i]);
            ConnectEffectList.Add(dto);
        }
    }

    public void CreateSpriteAssets(string path, string assetPath, List<Sprite> sprites)
    {
        //todo    2、没有裁剪ui功能
        if (!string.IsNullOrEmpty(assetPath))
        {
            if (!System.IO.Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }

            SpriteAssets spriteAssets;
            if (File.Exists(assetPath))
            {
                spriteAssets = AssetDatabase.LoadAssetAtPath<SpriteAssets>(assetPath);
            }
            else
            {
                spriteAssets = ScriptableObject.CreateInstance<SpriteAssets>();
                spriteAssets.m_SpriteList = new List<Sprite>();
            }

            for (int i = spriteAssets.m_SpriteList.Count - 1; i >= 0; i--)
            {
                if (sprites.Contains(spriteAssets.m_SpriteList[i]) == false)
                {
                    spriteAssets.m_SpriteList.RemoveAt(i);
                }
            }
            List<Sprite> sps = new List<Sprite>();
            for (int i = 0; i < sprites.Count; i++)
            {
                var sprite = sprites[i];
                if (spriteAssets.m_SpriteList.Contains(sprite) == false)
                {
                    sps.Add(sprite);
                }
            }

            sps.Sort((a, b) => string.Compare(a.name, b.name));
            spriteAssets.AddSpriteList(sps);
            if (File.Exists(assetPath))
            {
                EditorUtility.SetDirty(spriteAssets);
            }
            else
            {
                AssetDatabase.CreateAsset(spriteAssets, assetPath);
            }

        }
    }

    public void CheckAssetExit()
    {
        string spriteAssetsPath = AssetDatabase.GetAssetPath(spriteAssets);
        string p = spriteAssetsPath.Replace(spriteAssets.name + ".asset", "AssetsConfig");
        //如果不存在AssetsConfig文件夹就创建一个
        if (Directory.Exists(p) == false)
        {
            Directory.CreateDirectory(p);
        }
        string[] ps = Directory.GetFiles(p, "*.asset", SearchOption.AllDirectories);
        List<string> assetLs = new List<string>();
        foreach (var item in spriteDict)
        {
            string spriteAssetsAB;
            if (item.Key.Contains(".spritepack"))
            {
                string name = item.Key.Replace("ui/", "").Replace(".spritepack", "");
                string spriteAssetsNewPath = p + "/" + spriteAssets.name + "_" + name + ".asset";
                assetLs.Add(spriteAssetsNewPath);
            }
        }

        for (int i = 0; i < ps.Length; i++)
        {
            if (assetLs.Contains(ps[i].Replace("\\", "/")) == false)
            {
                File.Delete(ps[i]);
                File.Delete(ps[i] + ".meta");
            }
        }
    }

    public void SetAssetCSVData(bool onlyCsv)
    {
        CheckAssetExit();
        string spriteAssetsPath = AssetDatabase.GetAssetPath(spriteAssets);
        string p = spriteAssetsPath.Replace(spriteAssets.name + ".asset", "AssetsConfig");
        int tempSpritePack;
        foreach (var item in spriteDict)
        {
            string spriteAssetsAB;
            if (item.Key.Contains(".spritepack"))
            {
                string name = item.Key.Replace("ui/", "").Replace(".spritepack", "");
                string spriteAssetsNewPath = p + "/" + spriteAssets.name + "_" + name + ".asset";
                tempSpritePack = 0;
                if (onlyCsv == false)
                    CreateSpriteAssets(p, spriteAssetsNewPath, item.Value);

                spriteAssetsAB = spriteAssetsNewPath.Substring(spriteAssetsNewPath.IndexOf("/") + 1).ToLower();

                var assetImporter = AssetImporter.GetAtPath(spriteAssetsNewPath) as TextureImporter;
                if (assetImporter)
                    assetImporter.assetBundleName = spriteAssetsAB;

            }
            else
            {
                spriteAssetsAB = item.Key;
                tempSpritePack = 1;
            }
            
            for (int i = 0; i < item.Value.Count; i++)
            {
                ExportCSV.PatientStatisticsOutputDto dto = new ExportCSV.PatientStatisticsOutputDto();
                Sprite sprite = item.Value[i];

                dto.datas.Add(sprite.name);
                dto.datas.Add(spriteAssetsAB);
                dto.datas.Add(tempSpritePack.ToString());

                ConnectEffectList.Add(dto);
            }
        }
        
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
    }
    
    public void SetAssetSpriteData(bool onlyChangeScriptableObject)
    {
        CheckAssetExit();
        spriteAssets.ClearSubSpriteAssets();
        string spriteAssetsPath = AssetDatabase.GetAssetPath(spriteAssets);
        string p = spriteAssetsPath.Replace(spriteAssets.name + ".asset", "AssetsConfig");
        foreach (var item in spriteDict)
        {
            string spriteAssetsAB;
            if (item.Key.Contains(".spritepack"))
            {
                string name = item.Key.Replace("ui/", "").Replace(".spritepack", "");
                string spriteAssetsNewPath = p + "/" + spriteAssets.name + "_" + name + ".asset";
                if (onlyChangeScriptableObject == false)
                    CreateSpriteAssets(p, spriteAssetsNewPath, item.Value);

                spriteAssetsAB = spriteAssetsNewPath.Substring(spriteAssetsNewPath.IndexOf("/") + 1).ToLower();
                var assetImporter = AssetImporter.GetAtPath(spriteAssetsNewPath) as TextureImporter;
                if (assetImporter)
                    assetImporter.assetBundleName = spriteAssetsAB;
            }
            else
            {
                spriteAssetsAB = item.Key;
            }
            
            for (int i = 0; i < item.Value.Count; i++)
            {
                Sprite sprite = item.Value[i];
                if (subSpriteAssetsData.ContainsKey(sprite.name) == false)
                {
                    subSpriteAssetsData.Add(sprite.name, spriteAssetsAB);
                    spriteAssets.AddSubSpriteAssets(sprite.name, spriteAssetsAB);
                }
            }
        }
        EditorUtility.SetDirty(spriteAssets);
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
    }
}
