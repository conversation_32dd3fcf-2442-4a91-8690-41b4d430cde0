using UnityEngine;
using UnityEditor;
using System.IO;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using War.Battle;

namespace War.UI
{
    public static class SpriteAssetMenu
    {
        public static string GetSelectedPathOrFallback()
        {
            string path = "Assets";

            foreach (UnityEngine.Object obj in Selection.GetFiltered(typeof(UnityEngine.Object), SelectionMode.Assets))
            {
                path = AssetDatabase.GetAssetPath(obj);
                if (!string.IsNullOrEmpty(path) && File.Exists(path))
                {
                    path = Path.GetDirectoryName(path);
                    break;
                }
            }
            return path;
        }

        [MenuItem("Assets/Create/CreateAssets/UGUI Sprite Assets", false, 10)]
        static void CreateSpriteAsset()
        {
            var assetPath = EditorUtility.SaveFilePanelInProject("选择文件路径", "", "asset", "", 
                                                                    GetSelectedPathOrFallback());
            if (!string.IsNullOrEmpty(assetPath))
            {
                SpriteAssets spriteAssets = ScriptableObject.CreateInstance<SpriteAssets>();
                List<Sprite> sprites = new List<Sprite>();
                foreach (Object tex in Selection.GetFiltered(typeof(Object), SelectionMode.Assets))
                {
                    string filePath = AssetDatabase.GetAssetPath(tex);
                    Object[] objects = AssetDatabase.LoadAllAssetsAtPath(filePath);
                    foreach (Object obj in objects)
                    {
                        Sprite sprite = obj as Sprite;
                        if (sprite != null)
                        {
                            sprites.Add(sprite);
                        }
                    }
                }

                sprites.Sort((a, b) => string.Compare(a.name, b.name));
                spriteAssets.AddSpriteList(sprites);
                AssetDatabase.CreateAsset(spriteAssets, assetPath);
            }
        }

        [MenuItem("Assets/Create/CreateAssets/Prefab Assets", false, 10)]
        static void CreatePrefabAsset()
        {
            var assetPath = EditorUtility.SaveFilePanelInProject("选择文件路径", "", "asset", "",
                                                                    GetSelectedPathOrFallback());
            if (!string.IsNullOrEmpty(assetPath))
            {
                PrefabAssets prefabAssets = ScriptableObject.CreateInstance<PrefabAssets>();
                List<GameObject> prefabs = new List<GameObject>();
                foreach (Object tex in Selection.GetFiltered(typeof(Object), SelectionMode.Assets))
                {
                    string filePath = AssetDatabase.GetAssetPath(tex);
                    Object[] objects = AssetDatabase.LoadAllAssetsAtPath(filePath);
                    foreach (Object obj in objects)
                    {
                        GameObject prefab = obj as GameObject;
                        if (prefab != null)
                        {
                            prefabs.Add(prefab);
                        }
                    }
                }

                prefabs.Sort((a, b) => string.Compare(a.name, b.name));
                prefabAssets.AddPrefabList(prefabs);
                AssetDatabase.CreateAsset(prefabAssets, assetPath);
            }
        }

        [MenuItem("Assets/Create/CreateAssets/BattleIdentifying Assets", false, 10)]
        static void CreatBattleIdentifyingAsset()
        {
            var assetPath = EditorUtility.SaveFilePanelInProject("选择文件路径", "", "asset", "",
                                                                    GetSelectedPathOrFallback());
            if (!string.IsNullOrEmpty(assetPath))
            {
                Battle.BattleAssetIdentifyCollection prefabAssets = ScriptableObject.CreateInstance<Battle.BattleAssetIdentifyCollection>();
                AssetDatabase.CreateAsset(prefabAssets, assetPath);
            }
        }

        //[MenuItem("Assets/Create/CreateAssets/Adjust Assets", false, 10)]
        //static void CreaAdjustAsset()
        //{
        //    var assetPath = EditorUtility.SaveFilePanelInProject("选择文件路径", "", "asset", "",
        //                                                            GetSelectedPathOrFallback());
        //    if (!string.IsNullOrEmpty(assetPath))
        //    {
        //        com.adjust.sdk.AdjustChannelAsset prefabAssets = ScriptableObject.CreateInstance<com.adjust.sdk.AdjustChannelAsset>();
        //        AssetDatabase.CreateAsset(prefabAssets, assetPath);
        //    }
        //}

        [MenuItem("Assets/Create/CreateAssets/ShakeScreenCfg Assets", false, 10)]
        static void CreaShakeScreenCfgAssets()
        {
            var assetPath = EditorUtility.SaveFilePanelInProject("选择文件路径", "", "asset", "",
                                                                    GetSelectedPathOrFallback());
            if (!string.IsNullOrEmpty(assetPath))
            {
                ShakeScreenCfgAsset prefabAssets = ScriptableObject.CreateInstance<ShakeScreenCfgAsset>();
                AssetDatabase.CreateAsset(prefabAssets, assetPath);
            }
        }

        [MenuItem("Assets/Create/CreateAssets/Material Assets", false, 11)]
        static void CreateMaterialAsset()
        {
            var assetPath = EditorUtility.SaveFilePanelInProject("选择文件路径", "", "asset", "",
                                                                    GetSelectedPathOrFallback());
            if (!string.IsNullOrEmpty(assetPath))
            {
                MaterialAsset materialAssets = ScriptableObject.CreateInstance<MaterialAsset>();
                List<Material> sprites = new List<Material>();
                foreach (Object tex in Selection.GetFiltered(typeof(Object), SelectionMode.Assets))
                {
                    string filePath = AssetDatabase.GetAssetPath(tex);
                    Object[] objects = AssetDatabase.LoadAllAssetsAtPath(filePath);
                    foreach (Object obj in objects)
                    {
                        Material sprite = obj as Material;
                        if (sprite != null)
                        {
                            sprites.Add(sprite);
                        }
                    }
                }

                sprites.Sort((a, b) => string.Compare(a.name, b.name));
                materialAssets.AddMatetialList(sprites);
                AssetDatabase.CreateAsset(materialAssets, assetPath);
            }
        }

        [MenuItem("Assets/Create/CreateAssets/ActorNodeConfig Assets", false, 12)]
        static void CreaUIActorNodeCfgAssets()
        {
            var assetPath = EditorUtility.SaveFilePanelInProject("选择文件路径", "", "asset", "",GetSelectedPathOrFallback());
            if (!string.IsNullOrEmpty(assetPath))
            {
                UIActorNodeConfig prefabAssets = ScriptableObject.CreateInstance<UIActorNodeConfig>();
                AssetDatabase.CreateAsset(prefabAssets, assetPath);
            }
        }

        [MenuItem("Assets/Create/CreateAssets/CardConfig", false, 13)]
        static void CreaCardConfigAssets()
        {
            var assetPath = EditorUtility.SaveFilePanelInProject("选择文件路径", "", "asset", "", GetSelectedPathOrFallback());
            if (!string.IsNullOrEmpty(assetPath))
            {
                CardConfigAssets prefabAssets = ScriptableObject.CreateInstance<CardConfigAssets>();
                AssetDatabase.CreateAsset(prefabAssets, assetPath);
            }
        }
    }
}