using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEditor;
using UnityEngine;
using Sirenix.OdinInspector.Editor;
using Sirenix.Utilities;
using Debug = UnityEngine.Debug;
using Object = UnityEngine.Object;

namespace War.UI
{
    /// <summary>
    /// SpriteAssets 辅助工具
    /// </summary>
    /// <author>陈皮皮</author>
    /// <version>20221018</version>
    [CustomEditor(typeof(SpriteAssets))]
    public class SpriteAssetsUtility : OdinEditor
    {
        #region GUI

        public override void OnInspectorGUI()
        {
            // 获取当前ScriptableObject的资产路径
            string assetPath = AssetDatabase.GetAssetPath(target);
            // 判断路径是否包含"AssetsConfig"（不区分大小写）
            if (ContainsPathSegment(assetPath, "AssetsConfig"))
            {
                // 路径符合条件时，返回以隐藏Inspector
                
                GUILayout.Label("贴图资源");
                for (int j = 0; j < spriteDict.Keys.Count; j++)
                {
                    var key = spriteDict.Keys.ToArray()[j];
                    var ls = spriteDict[key];
                    if (!string.IsNullOrEmpty(filterName)) //过滤筛选
                    {
                        bool hasName = false;
                        for (int i = 0; i < ls.Count; i++)
                        {
                            if (ls[i].name.Contains(filterName))
                            {
                                hasName = true;
                                break;
                            }
                        }

                        if (!hasName)
                        {
                            continue;
                        }
                    }

                    foldOutDict[key] = EditorGUILayout.Foldout(foldOutDict[key], $"AB:{key} , ({ls.Count})");
                    if (foldOutDict[key])
                    {
                        for (int i = 0; i < ls.Count; i++)
                        {
                            if (!ls[i].name.Contains(filterName))
                            {
                                continue;
                            }

                            EditorGUILayout.BeginHorizontal();
                            string path = AssetDatabase.GetAssetPath(ls[i]);
                            GUILayout.Box(AssetDatabase.LoadAssetAtPath<Texture>(path), GUILayout.Width(25),
                                GUILayout.Height(25));
                            EditorGUILayout.ObjectField(ls[i], typeof(Sprite));

                            EditorGUILayout.EndHorizontal();
                        }
                    }
                }
                
                return;
            }
            base.OnInspectorGUI(); 
            EditorGUILayout.BeginVertical();
            
             #region new 2023/1/18 fanchenhui copy from yHero220726

                if (mTarget == null)
                {
                    mTarget = target as War.UI.SpriteAssets;
                }

#if UNITY_EDITOR
            
                SerializedProperty editorSpriteListProp = serializedObject.FindProperty("m_editorSpriteList");
                SerializedProperty spriteListProp = serializedObject.FindProperty("m_SpriteList");
                serializedObject.Update();
                
                bool isMainSpriteAsset = serializedObject.FindProperty("isMainSpriteAsset").boolValue;
                //判断是否勾选了isMainSpriteAsset
                //如果没有勾选，就显示m_SpriteList（老的那套数据），并且把m_editorSpriteList的值传进去保存
                //如果勾选了，不显示m_SpriteList，把m_SpriteList的值传进去给m_editorSpriteList，清空m_SpriteList
                if (!isMainSpriteAsset)
                {
                    EditorGUILayout.PropertyField(spriteListProp, true);
                    if (spriteListProp.arraySize == 0 && editorSpriteListProp.arraySize > 0)
                    {
                        mTarget.ClearSubSpriteAssets();
                        SwapTwoList(spriteListProp,mTarget.GetEditorSpriteList(), mTarget.m_SpriteList);
                        foldOutDict.Clear();
                        spriteDict.Clear();
                        for (int i = 0; i < mTarget.m_SpriteList.Count; i++)
                        {
                            AddSpriteToDict(mTarget.m_SpriteList[i]);
                        }
                    }
                }
                else
                {
                    if (editorSpriteListProp == null)
                    {
                        Debug.LogError("editorSpriteListProp is null");
                    }
                    // SerializedProperty prop = serializedObject.GetIterator();
                    // while (prop.NextVisible(true)) {
                    //     Debug.Log(prop.name);
                    // }
                    if (spriteListProp == null)
                    {
                        Debug.LogError("spriteListProp is null");
                    }

                     if ((editorSpriteListProp == null || editorSpriteListProp.arraySize == 0) &&  (spriteListProp != null && spriteListProp.arraySize > 0))
                     {
                         SwapTwoList(editorSpriteListProp,mTarget.m_SpriteList, mTarget.GetEditorSpriteList());
                     }
                     UpdateEditorSpriteDict();
                 
                }
                
                serializedObject.ApplyModifiedProperties();
                
#endif
                EditorGUILayout.Space();
#if UNITY_EDITOR
                if (isMainSpriteAsset)
                {
                    EditorGUILayout.LabelField(new GUIContent("导出Assets数据表"));
                    EditorGUILayout.BeginVertical();
                    {

                        if (GUILayout.Button(new GUIContent("导出子图集")))
                        {
                            UpdateEditorSpriteDict();
                            CreateAssetsConfigData.Instance.InitSpriteAssetsData(mTarget, editorSpriteDict);
                            EditorUtility.SetDirty(target);
                            AssetDatabase.SaveAssets();
                        }
                        

                    }
                    EditorGUILayout.EndVertical();
                }
#endif
            EditorGUILayout.Space();
            GUILayout.Button(new GUIContent("---分割线---"));
            EditorGUILayout.Space();
#if UNITY_EDITOR
                if (GUILayout.Button("添加选中项(支持多项，↗先锁定再操作)"))
                {
                    Object[] selected = Selection.objects;
                    int counter = 0;
                    int failCount = 0;
                    if (selected != null && selected.Length > 0)
                    {
                        for (int i = 0; i < selected.Length; i++)
                        {
                            string path = AssetDatabase.GetAssetPath(selected[i]);
                            var sp = AssetDatabase.LoadAssetAtPath<Sprite>(path);
                            if (sp != null)
                            {
                                bool result = TryAddToSpriteAssets(sp);
                                counter += result ? 1 : 0;
                                failCount += !result ? 1 : 0;
                            }
                        }
                    }

                    EditorUtility.DisplayDialog("添加结果:", $"成功添加数:{counter} , 添加失败数:{failCount}", "确认");
                }
#endif
                EditorGUILayout.Space();

                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("搜索：", GUILayout.Width(150));
                filterName = EditorGUILayout.TextField(filterName);
                if (GUILayout.Button("展开/折叠", GUILayout.Width(150)))
                {
                    int firstState = -1;
                    bool foldOutState = false;

                    for (int i = 0; i < foldOutDict.Keys.Count; i++)
                    {
                        var key = foldOutDict.Keys.ToArray()[i];
                        if (firstState == -1)
                        {
                            firstState = 0;
                            foldOutState = !foldOutDict[key];
                        }

                        foldOutDict[key] = foldOutState;
                    }
                }

                EditorGUILayout.EndHorizontal();


                EditorGUILayout.BeginHorizontal();
                GUILayout.Label("添加贴图资源");
                Sprite addSprite = null;
                addSprite = EditorGUILayout.ObjectField(addSprite, typeof(Sprite)) as Sprite;

                if (addSprite != null)
                {
                    TryAddToSpriteAssets(addSprite);
                }

                GUILayout.Label($"图片数量：{mTarget.m_SpriteList.Count}");
                EditorGUILayout.EndHorizontal();

                ///添加tips
                if (!string.IsNullOrEmpty(tips))
                {
                    GUI.color = Color.red;
                    EditorGUILayout.LabelField(tips);
                    GUI.color = Color.white;
                }

                Dictionary<string, List<Sprite>> localSpriteDict;
                if (mTarget.isMainSpriteAsset)
                {
                    localSpriteDict = editorSpriteDict; 
                }
                else
                {
                    localSpriteDict = spriteDict;
                }
                for (int j = 0; j < localSpriteDict.Keys.Count; j++)
                {
                    var key = localSpriteDict.Keys.ToArray()[j];
                    var ls = localSpriteDict[key];
                    if (!string.IsNullOrEmpty(filterName)) //过滤筛选
                    {
                        bool hasName = false;
                        for (int i = 0; i < ls.Count; i++)
                        {
                            if (ls[i].name.Contains(filterName))
                            {
                                hasName = true;
                                break;
                            }
                        }

                        if (!hasName)
                        {
                            continue;
                        }
                    }

                    foldOutDict[key] = EditorGUILayout.Foldout(foldOutDict[key], $"AB:{key} , ({ls.Count})");
                    if (foldOutDict[key])
                    {
                        for (int i = 0; i < ls.Count; i++)
                        {
                            if (!ls[i].name.Contains(filterName))
                            {
                                continue;
                            }

                            EditorGUILayout.BeginHorizontal();
                            string path = AssetDatabase.GetAssetPath(ls[i]);
                            GUILayout.Box(AssetDatabase.LoadAssetAtPath<Texture>(path), GUILayout.Width(25),
                                GUILayout.Height(25));
                            EditorGUILayout.ObjectField(ls[i], typeof(Sprite));
                            if (GUILayout.Button("删除"))
                            {
                                TryRemoveFromSpriteAssets(ls[i]);
                            }

                            EditorGUILayout.EndHorizontal();
                        }
                    }
                }


                EditorGUILayout.Space();
                GUILayout.Button(new GUIContent("---分割线---"));
                EditorGUILayout.Space();

                #endregion
            
            
            
            
            {
                EditorGUILayout.LabelField(new GUIContent("快捷操作"));
                EditorGUILayout.BeginVertical();
                {
                    if (GUILayout.Button(new GUIContent("应用变更", EditorGUIUtility.IconContent("P4_CheckOutLocal").image)))
                    {
                        Internal_SaveChanged();
                    }
                    if (GUILayout.Button(new GUIContent("重新导入该资源", EditorGUIUtility.IconContent("TreeEditor.Refresh").image)))
                    {
                        Internal_Reimport();
                    }
                    EditorGUILayout.LabelField(
                        new GUIContent(
                            "资源文件更新（如SVN_Update或Revert）后，编辑器内数据可能会不同步！点击上方“重新导入该资源”按钮可以强制刷新数据。",
                            EditorGUIUtility.IconContent("console.infoicon.sml").image
                        ),
                        new GUIStyle("HelpBox")
                        {
                            fontSize = 12,
                        }
                    );
                    if (GUILayout.Button(new GUIContent("在资源管理器中展示该资源", EditorGUIUtility.IconContent("d_CanvasRenderer Icon").image)))
                    {
                        Internal_ShowInExplorer();
                    }
                }
                EditorGUILayout.EndVertical();

                // EditorGUILayout.LabelField(new GUIContent("快速操作"));
                // EditorGUILayout.BeginHorizontal();
                // {
                //     if (GUILayout.Button(new GUIContent("通过路径批量添加图片", EditorGUIUtility.IconContent("CreateAddNew").image)))
                //     {
                //         Internal_AddByPath();
                //     }
                // }
                // EditorGUILayout.EndHorizontal();

                EditorGUILayout.LabelField(new GUIContent("差异"));
                EditorGUILayout.BeginHorizontal();
                {
                    if (GUILayout.Button(new GUIContent("与记录对比", EditorGUIUtility.IconContent("Profiler.UIDetails").image)))
                    {
                        Internal_CompareWithRecord();
                    }
                }
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.LabelField(new GUIContent("记录"));
                EditorGUILayout.BeginVertical();
                {
                    EditorGUILayout.BeginHorizontal();
                    {
                        if (GUILayout.Button(new GUIContent("保存到记录文件", EditorGUIUtility.IconContent("SaveActive").image)))
                        {
                            Internal_SaveToRecord();
                        }
                        if (GUILayout.Button(new GUIContent("从记录文件中恢复资源", EditorGUIUtility.IconContent("SaveFromPlay").image)))
                        {
                            Internal_RecoverFromRecord();
                        }
                    }
                    EditorGUILayout.EndHorizontal();
                    // if (GUILayout.Button(new GUIContent("同步记录文件到其他项目", EditorGUIUtility.IconContent("SaveFromPlay").image)))
                    // {
                    //     Internal_SyncRecordToProject();
                    // }
                }
                EditorGUILayout.EndVertical();

                EditorGUILayout.LabelField(new GUIContent("SVN"));
                EditorGUILayout.BeginHorizontal();
                {
                    if (GUILayout.Button(new GUIContent("更新（Update）", EditorGUIUtility.IconContent("sv_icon_dot3_sml").image)))
                    {
                        Internal_SVNUpdate();
                    }
                    if (GUILayout.Button(new GUIContent("提交（Commit）", EditorGUIUtility.IconContent("sv_icon_dot6_sml").image)))
                    {
                        Internal_SVNCommit();
                    }
                    // if (GUILayout.Button(new GUIContent("回退（Revert）", EditorGUIUtility.IconContent("sv_icon_dot4_sml").image)))
                    // {
                    //     Internal_SVNRevert();
                    // }
                    // if (GUILayout.Button(new GUIContent("差异（Diff）", EditorGUIUtility.IconContent("sv_icon_dot1_sml").image)))
                    // {
                    //     Internal_SVNDiff();
                    // }
                }
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.LabelField(new GUIContent("其他"));
                EditorGUILayout.BeginHorizontal();
                {
                    if (GUILayout.Button(new GUIContent("工具说明文档", EditorGUIUtility.IconContent("UnityEditor.ConsoleWindow").image)))
                    {
                        Internal_Docs();
                    }
                }
                EditorGUILayout.EndHorizontal();
            }
            EditorGUILayout.EndVertical();
        }

        private void SwapTwoList(SerializedProperty prop,List<Sprite> sourceSpriteList, List<Sprite> targetSpriteList)
        {
            if (sourceSpriteList != null)
            {
                if (targetSpriteList == null) // 如果 targetSpriteList 为 null，需要初始化一个新的 List
                {
                    targetSpriteList = new List<Sprite>();
                    prop.managedReferenceValue = targetSpriteList; // 将新的 List 赋值回去
                }

                // 复制 m_SpriteList 的数据到 m_editorSpriteList
                targetSpriteList.Clear(); // 先清空目标 List，避免重复添加
                foreach (Sprite sprite in sourceSpriteList)
                {
                    targetSpriteList.Add(sprite);
                }
                sourceSpriteList.Clear(); // 清空源 List

                // 通知 Unity 对象已被修改，需要保存
                EditorUtility.SetDirty(target);
                AssetDatabase.SaveAssets(); // 如果是 ScriptableObject，可能需要保存 Asset
            }
        }
        
        
          #region new 2023/1/18 fanchenhui copy from yHero220726

        private War.UI.SpriteAssets mTarget;
        private string filterName = "";
        private bool hasEdit = false;
        private Dictionary<string, List<Sprite>> spriteDict = new Dictionary<string, List<Sprite>>();
        private Dictionary<string, bool> foldOutDict = new Dictionary<string, bool>();

        private Dictionary<string, List<Sprite>> editorSpriteDict = new Dictionary<string, List<Sprite>>();
        
        public Dictionary<string, List<Sprite>> GetEditorSpriteDict()
        {
            return editorSpriteDict; 
        }
        
        private void OnEnable()
        {
            hasEdit = false;
            if (target != null)
            {
                mTarget = target as War.UI.SpriteAssets;
            }
            foldOutDict.Clear();
            spriteDict.Clear();
            for (int i = 0; i < mTarget.m_SpriteList.Count; i++)
            {
                AddSpriteToDict(mTarget.m_SpriteList[i]);
            }
            UpdateEditorSpriteDict();
        }

        private void OnDisable()
        {
            if (hasEdit)
            {
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
            }
        }
#if UNITY_EDITOR
        public void UpdateEditorSpriteDict()
        {
            editorSpriteDict.Clear();
            List<Sprite> editorSpriteList = mTarget.GetEditorSpriteList();
            if (editorSpriteList != null)
            {
                for (int i = 0; i < editorSpriteList.Count; i++)
                {
                    if (editorSpriteList[i] == null)
                    {
                        Debug.LogError("有资源为空");
                        return;
                    }

                    AssetImporter ap = AssetImporter.GetAtPath(AssetDatabase.GetAssetPath(editorSpriteList[i]));
                    string abName = ap.assetBundleName;
                    if (string.IsNullOrEmpty(abName))
                    {
                        abName = "noneABName";
                    }

                    if (!editorSpriteDict.ContainsKey(abName))
                    {
                        editorSpriteDict.Add(abName, new List<Sprite>());
                        

                    }

                    if (!foldOutDict.ContainsKey(abName))
                    {
                        foldOutDict.Add(abName, false);
                    }


                    if (!editorSpriteDict[abName].Contains(editorSpriteList[i]))
                    {
                        editorSpriteDict[abName].Add(editorSpriteList[i]);
                    }
                }
            }
        }
#endif

        private void AddSpriteToDict(Sprite sprite)
        {
            if (sprite == null)
            {
                Debug.LogError("有资源为空");
                return;
            }

            AssetImporter ap = AssetImporter.GetAtPath(AssetDatabase.GetAssetPath(sprite));
            string abName = ap.assetBundleName;
            if (string.IsNullOrEmpty(abName))
            {
                abName = "noneABName";
            }

            if (!spriteDict.ContainsKey(abName))
            {
                spriteDict.Add(abName, new List<Sprite>());
                foldOutDict.Add(abName, false);
            }

            if (!spriteDict[abName].Contains(sprite))
            {
                spriteDict[abName].Add(sprite);
            }
        }

        private void RemoveSpriteFromDict(Sprite sprite)
        {
            List<string> removeKeys = new List<string>();
            for (int j = 0; j < spriteDict.Keys.Count; j++)
            {
                var key = spriteDict.Keys.ToArray()[j];
                for (int i = 0; i < spriteDict[key].Count; i++)
                {
                    if (spriteDict[key].Contains(sprite))
                    {
                        spriteDict[key].Remove(sprite);
                        if (spriteDict[key].Count <= 0)
                        {
                            removeKeys.Add(key);
                        }

                        break;
                    }
                }
            }

            for (int i = 0; i < removeKeys.Count; i++)
            {
                if (spriteDict.ContainsKey(removeKeys[i]))
                {
                    spriteDict.Remove(removeKeys[i]);
                }
            }
        }

        private bool TryAddToSpriteAssets(Sprite sp)
        {
            List<Sprite> spriteList;
            if (mTarget.isMainSpriteAsset)
                spriteList = mTarget.GetEditorSpriteList();
            else
                spriteList = mTarget.m_SpriteList;
            
            if (spriteList.Contains(sp))
            {
                tips = $"贴图已存在无需重复添加：{sp.name}";
                return false;
            }
            else
            {
                for (int i = 0; i < spriteList.Count; i++)
                {
                    if (sp.name.Equals(spriteList[i].name))
                    {
                        tips = $"贴图名字重复添加：{sp.name} , 请修改名字后重新添加";
                        return false;
                    }
                }

                tips = $"添加成功：{sp.name}";
                spriteList.Add(sp);
                EditorUtility.SetDirty(mTarget);
                hasEdit = true;
                if (!mTarget.isMainSpriteAsset)
                {
                    AddSpriteToDict(sp);
                }
                return true;
            }
        }

        private void TryRemoveFromSpriteAssets(Sprite sp)
        {
            List<Sprite> spriteList;
            if (mTarget.isMainSpriteAsset)
                spriteList = mTarget.GetEditorSpriteList();
            else
                spriteList = mTarget.m_SpriteList;
            
            if (spriteList.Contains(sp))
            {
                spriteList.Remove(sp);
                EditorUtility.SetDirty(mTarget);
                hasEdit = true;
            }
            if (!mTarget.isMainSpriteAsset)
                RemoveSpriteFromDict(sp);
        }

        string tips = "";

        #endregion

        /// <summary>
        /// 保存该资源
        /// </summary>
        private void Internal_SaveChanged()
        {
            ApplyAssetChanges(target);
        }

        /// <summary>
        /// 重新导入该资源
        /// </summary>
        private void Internal_Reimport()
        {
            ReimportAsset(target as SpriteAssets);
        }

        /// <summary>
        /// 在资源管理器中展示该资源
        /// </summary>
        private void Internal_ShowInExplorer()
        {
            EditorUtility.RevealInFinder(AssetDatabase.GetAssetPath(target));
        }

        /// <summary>
        /// 通过路径添加
        /// </summary>
        private void Internal_AddByPath()
        {
            InputDialogWindow inputDialog = InputDialogWindow.Create($"通过路径添加图片到 {{{target.name}.asset}} 中");
            inputDialog.description = "可以输入多条路径，路径间用英文逗号分隔：";
            inputDialog.confirmBtnLabel = "确认";
            inputDialog.SetSize(450, 600);
            inputDialog.SetCenter();
            async void InputDialogConfirmCallback(string s)
            {
                // 处理输入
                s = s.Trim().Replace("\"", "");
                string[] paths = s.Split(',');
                paths = paths.Where(path => !string.IsNullOrWhiteSpace(path)).ToArray();
                paths = paths.Select(path => path.Trim()).ToArray();
                paths = paths.Where(path => (AssetDatabase.LoadAssetAtPath<Sprite>(path) != null)).ToArray();
                if (paths.Length == 0)
                {
                    Debug.LogWarning($"[SpriteAssetsUtility] 无效输入！");
                    return;
                }
                // 加载图像
                List<Sprite> sprites = paths.Select(AssetDatabase.LoadAssetAtPath<Sprite>).ToList();
                if (sprites.Count == 0)
                {
                    Debug.LogWarning($"[SpriteAssetsUtility] 无效路径！");
                    return;
                }
                // 记录资源路径
                string assetPath = AssetDatabase.GetAssetPath(target);
                // 重导资源
                await ReimportAsset(target);
                // 加载资源
                SpriteAssets asset = AssetDatabase.LoadAssetAtPath<SpriteAssets>(assetPath);
                asset.AddSpriteList(sprites);
                // 保存
                ApplyAssetChanges(asset);
                // 打印
                Debug.LogWarning($"[SpriteAssetsUtility] {{{asset.name}.asset}} <color=#00ff00>+ 添加图片数量：{paths.Length}</color>\n\n\n");
                Debug.LogWarning($"[SpriteAssetsUtility] {{{asset.name}.asset}} <color=#00ff00>+ 添加图片列表：</color>\n<color=#00ff00>{Join(paths, "\n")}</color>\n\n\n");
            };
            inputDialog.confirmCallback = InputDialogConfirmCallback;
        }

        /// <summary>
        /// 同步到记录
        /// </summary>
        private void Internal_SaveToRecord()
        {
            SaveToRecord(target as SpriteAssets);
        }

        /// <summary>
        /// 从记录文件中恢复资源
        /// </summary>
        private void Internal_RecoverFromRecord()
        {
            bool isOk = EditorUtility.DisplayDialog("从记录文件中恢复资源", "确定要从记录文件中恢复数据吗？此操作将覆盖当前资源！", "确认", "取消");
            if (isOk)
            {
                RecoverFromRecord(target as SpriteAssets);
            }
        }

        /// <summary>
        /// 从记录中恢复资源
        /// </summary>
        private void Internal_SyncRecordToProject()
        {
            SyncRecordToProject(target as SpriteAssets);
        }

        /// <summary>
        /// 与记录对比
        /// </summary>
        private void Internal_CompareWithRecord()
        {
            CompareWithRecord(target as SpriteAssets);
        }

        /// <summary>
        /// 说明文档
        /// </summary>
        private void Internal_Docs()
        {
            Application.OpenURL("https://q1doc.yuque.com/kiob3t/gynxh4/awuwks");
        }

        /// <summary>
        /// SVN 更新
        /// </summary>
        private void Internal_SVNUpdate()
        {
            string path = Join(GetRelativeFilePaths(), "*");
            ExecSVNCmd("update", path);
        }

        /// <summary>
        /// SVN 提交
        /// </summary>
        private async void Internal_SVNCommit()
        {
            // DiffInfo diffInfo = await GetDiffInfoFromRecord(target as SpriteAssets);
            // if (diffInfo != null && (diffInfo.addedCount != 0 || diffInfo.removedCount != 0))
            // {
            //     bool isOk = EditorUtility.DisplayDialog("SVN 提交", "检测到资源与记录间存在差异，资源修改确认完毕后需要同步到记录！\n> 点击“确定”自动进行同步", "确定", "取消");
            //     if (!isOk)
            //     {
            //         return;
            //     }
            //     SaveToRecord(target as SpriteAssets);
            // }
            string path = Join(GetRelativeFilePaths(), "*");
            ExecSVNCmd("commit", path);
        }

        // /// <summary>
        // /// SVN 回退
        // /// </summary>
        // private void Internal_SVNRevert()
        // {
        //     bool isOk = EditorUtility.DisplayDialog("SVN 回退", "确认要回退吗！", "确定", "取消");
        //     if (isOk)
        //     {
        //         string path = AssetDatabase.GetAssetPath(target);
        //         ExecSVNCmd("revert", path);
        //     }
        // }
        //
        // /// <summary>
        // /// SVN 差异
        // /// </summary>
        // private void Internal_SVNDiff()
        // {
        //     string path = AssetDatabase.GetAssetPath(target);
        //     ExecSVNCmd("diff", path);
        // }

        #endregion

        #region SVN

        /// <summary>
        /// 获取相关文件路径
        /// </summary>
        /// <returns></returns>
        private string[] GetRelativeFilePaths()
        {
            List<string> list = new List<string>();
            // 资源文件
            string assetPath = ToAbsolutePath(AssetDatabase.GetAssetPath(target));
            string assetMetaPath = assetPath + ".meta";
            list.Add(assetPath);
            list.Add(assetMetaPath);
            // 记录文件
            string recordPath = GetRecordFilePath(target as SpriteAssets);
            if (File.Exists(recordPath))
            {
                string recordMetaPath = recordPath + ".meta";
                list.Add(recordPath);
                list.Add(recordMetaPath);
            }
            return list.ToArray();
        }

        /// <summary>
        /// 执行 SVN 命令
        /// </summary>
        /// <param name="operation"></param>
        /// <param name="path"></param>
        /// <param name="closeOnEnd"></param>
        public static void ExecSVNCmd(string operation, string path, bool closeOnEnd = false)
        {
            string command = $"/c tortoiseproc.exe /command:{operation} /path:\"{path}\"";
            command += closeOnEnd ? " /closeonend:1" : " /closeonend:0";
            ProcessStartInfo proc = new ProcessStartInfo("cmd.exe", command)
            {
                WindowStyle = ProcessWindowStyle.Hidden
            };
            Process.Start(proc);
        }

        #endregion

        #region Record (Json)

        /// <summary>
        /// 同步到记录
        /// </summary>
        /// <param name="asset"></param>
        public static async void SaveToRecord(SpriteAssets asset)
        {
            // 先保存资源变更
            ApplyAssetChanges(asset);
            // 生成记录
            EditorUtility.DisplayProgressBar("正在同步到记录...", "请稍候...", 1);
            {
                Debug.LogWarning($"[SpriteAssetsUtility] {{{asset.name}.asset}} 即将同步到记录，共 {asset.m_SpriteList.Count} 份 Sprite 资源！", asset);
                // 生成记录文件
                await GenerateRecordFile(asset);
                // 记录文件
                RecordInfo recordInfo = await GetRecordInfo(asset);
                if (recordInfo == null)
                {
                    Debug.LogError($"[SpriteAssetsUtility] 生成记录失败！");
                    EditorUtility.ClearProgressBar();
                    return;
                }
                string recordAssetPath = "Assets" + GetRecordFilePath(asset).Substring(Application.dataPath.Length);
                Object recordAsset = AssetDatabase.LoadAssetAtPath<Object>(recordAssetPath);
                Debug.LogWarning($"[SpriteAssetsUtility] {{{asset.name}.asset}} 记录已生成，共 {recordInfo.assetCount} 条记录！", recordAsset);
            }
            EditorUtility.ClearProgressBar();
        }

        /// <summary>
        /// 从记录中恢复资源
        /// </summary>
        /// <param name="asset"></param>
        public static async void RecoverFromRecord(SpriteAssets asset)
        {
            EditorUtility.DisplayProgressBar("正在从记录中恢复...", "请稍候...", 1);
            {
                RecordInfo recordInfo = await GetRecordInfo(asset);
                if (recordInfo == null)
                {
                    Debug.LogWarning($"[SpriteAssetsUtility] {{{asset.name}.asset}} 没有记录！", asset);
                    EditorUtility.ClearProgressBar();
                    return;
                }

                // 对比差异
                DiffInfo diffInfo = await GetDiffInfoFromRecord(asset);
                if (diffInfo.addedCount == 0 && diffInfo.removedCount == 0)
                {
                    Debug.LogWarning($"[SpriteAssetsUtility] {{{asset.name}.asset}} 资源与记录无差异，已跳过恢复操作。", asset);
                    EditorUtility.ClearProgressBar();
                    return;
                }

                Debug.LogWarning($"[SpriteAssetsUtility] {{{asset.name}.asset}} 即将从记录中恢复，共 {recordInfo.assetCount} 条记录！", asset);
                List<Sprite> spriteList = new List<Sprite>(recordInfo.assetCount);
                foreach (string path in recordInfo.assetList)
                {
                    Sprite sprite = AssetDatabase.LoadAssetAtPath<Sprite>(path);
                    if (sprite)
                    {
                        spriteList.Add(sprite);
                    }
                    else
                    {
                        Debug.LogWarning($"[SpriteAssetsUtility] 无效资源路径：{path}", asset);
                    }
                }

                asset.m_SpriteList = spriteList;
                // 刷新资源
                EditorUtility.SetDirty(asset);
                AssetDatabase.SaveAssets();
                AssetDatabase.ImportAsset(AssetDatabase.GetAssetPath(asset));
                Debug.LogWarning($"[SpriteAssetsUtility] {{{asset.name}.asset}} 已从记录中恢复，共 {spriteList.Count} 份有效 Sprite 资源！", asset);
            }
            EditorUtility.ClearProgressBar();
        }

        private static async void SyncRecordToProject(SpriteAssets asset)
        {
            string recordFilePath = GetRecordFilePath(asset);
            FileInfo recordFileInfo = new FileInfo(recordFilePath);
            if (!recordFileInfo.Exists)
            {
                Debug.LogWarning($"[SpriteAssetsUtility] {{{asset.name}.asset}} 没有记录！", asset);
                return;
            }
            string targetDirPath = EditorUtility.OpenFolderPanel($"选择要同步到的文件夹（源文件路径：{recordFilePath}）", recordFileInfo.DirectoryName, null);
            if (string.IsNullOrEmpty(targetDirPath) || targetDirPath.Equals(recordFileInfo.DirectoryName))
            {
                Debug.LogWarning($"[SpriteAssetsUtility] {{{asset.name}.asset}} 无效路径！");
                return;
            }
        }

        /// <summary>
        /// 生成记录文件
        /// </summary>
        /// <param name="asset"></param>
        /// <returns></returns>
        public static async Task<string> GenerateRecordFile(SpriteAssets asset)
        {
            // 读取资源
            List<string> assetList = GetAssetListFromAsset(asset);
            assetList.Sort(); // 排序
            RecordInfo recordInfo = new RecordInfo
            {
                asset = AssetDatabase.GetAssetPath(asset),
                assetCount = assetList.Count,
                assetList = assetList.ToArray()
            };
            string jsonString = JsonUtility.ToJson(recordInfo, true);
            // 创建记录文件
            string recordFilePath = GetRecordFilePath(asset);
            FileInfo recordFileInfo = new FileInfo(recordFilePath);
            if (recordFileInfo.Exists)
            {
                recordFileInfo.Delete();
            }

            // 写入数据
            FileStream fileStream = recordFileInfo.Create();
            StreamWriter streamWriter = new StreamWriter(fileStream);
            await streamWriter.WriteAsync(jsonString);
            // 完成写入
            streamWriter.Close();
            fileStream.Close();
            // 刷新资源
            string recordAssetPath = ToAssetRelativePath(recordFilePath);
            TextAsset recordAsset = AssetDatabase.LoadAssetAtPath<TextAsset>(recordAssetPath);
            if (recordAsset)
            {
                EditorUtility.SetDirty(recordAsset);
                AssetDatabase.SaveAssets();
            }
            else
            {
                AssetDatabase.ImportAsset(recordAssetPath);
            }

            AssetDatabase.Refresh();
            // 返回文件路径
            return recordFilePath;
        }

        /// <summary>
        /// 获取记录
        /// </summary>
        /// <param name="asset"></param>
        /// <returns></returns>
        public static async Task<RecordInfo> GetRecordInfo(SpriteAssets asset)
        {
            string recordFilePath = GetRecordFilePath(asset);
            FileInfo recordFileInfo = new FileInfo(recordFilePath);
            if (!recordFileInfo.Exists)
            {
                return null;
            }

            // 读取记录
            StreamReader streamReader = recordFileInfo.OpenText();
            string jsonString = await streamReader.ReadToEndAsync();
            RecordInfo recordInfo = JsonUtility.FromJson<RecordInfo>(jsonString);
            streamReader.Close();
            return recordInfo;
        }

        /// <summary>
        /// 获取 SpriteAssets 的资源列表
        /// </summary>
        /// <param name="asset"></param>
        /// <returns></returns>
        public static List<string> GetAssetListFromAsset(SpriteAssets asset)
        {
            List<string> list = new List<string>();
            foreach (Sprite sprite in asset.m_SpriteList)
            {
                if (sprite == null)
                {
                    continue;
                }

                list.Add(AssetDatabase.GetAssetPath(sprite));
            }

            return list;
        }

        /// <summary>
        /// 获取记录文件路径
        /// </summary>
        /// <param name="asset"></param>
        /// <returns></returns>
        public static string GetRecordFilePath(SpriteAssets asset)
        {
            // 资源路径
            FileInfo fileInfo = new FileInfo(AssetDatabase.GetAssetPath(asset));
            // 记录文件
            string recordFileName = fileInfo.Name.Replace(fileInfo.Extension, ".record.json");
            return Path.Combine(fileInfo.DirectoryName!, recordFileName).Replace("\\", "/");
        }

        /// <summary>
        /// 记录信息
        /// </summary>
        [Serializable]
        public class RecordInfo
        {
            /// <summary>
            /// 源资源
            /// </summary>
            public string asset;

            /// <summary>
            /// 资源数量
            /// </summary>
            public int assetCount;

            /// <summary>
            /// 资源列表
            /// </summary>
            public string[] assetList;
        }

        #endregion

        #region Diff

        /// <summary>
        /// 与记录对比
        /// </summary>
        /// <param name="asset"></param>
        /// <returns></returns>
        public static async void CompareWithRecord(SpriteAssets asset)
        {
            DiffInfo diffInfo = await GetDiffInfoFromRecord(asset);
            if (diffInfo == null)
            {
                Debug.Log($"[SpriteAssetsUtility] {{{asset.name}.asset}} 没有记录！", asset);
                return;
            }

            Debug.LogWarning($"[SpriteAssetsUtility] {{{asset.name}.asset}} <color=#ffff00>> 差异信息：</color>{JsonUtility.ToJson(diffInfo, true)}\n\n\n");
            Debug.LogWarning($"[SpriteAssetsUtility] {{{asset.name}.asset}} <color=#00ff00>+ 新增资源数量：{diffInfo.addedCount}</color>\n\n\n");
            Debug.LogWarning($"[SpriteAssetsUtility] {{{asset.name}.asset}} <color=#00ff00>+ 新增资源列表：</color>\n<color=#00ff00>{Join(diffInfo.addedList, "\n")}</color>\n\n\n");
            Debug.LogWarning($"[SpriteAssetsUtility] {{{asset.name}.asset}} <color=#ff0000>- 移除资源数量：{diffInfo.removedCount}</color>\n\n\n");
            Debug.LogWarning($"[SpriteAssetsUtility] {{{asset.name}.asset}} <color=#ff0000>- 移除资源列表：</color>\n<color=#ff0000>{Join(diffInfo.removedList, "\n")}</color>\n\n\n");
        }

        /// <summary>
        /// 获取与记录的差异
        /// </summary>
        /// <param name="asset"></param>
        /// <returns></returns>
        public static async Task<DiffInfo> GetDiffInfoFromRecord(SpriteAssets asset)
        {
            RecordInfo recordInfo = await GetRecordInfo(asset);
            if (recordInfo == null)
            {
                return null;
            }

            List<string> assetList = GetAssetListFromAsset(asset);
            // 获取差异
            DiffInfo diffInfo = new DiffInfo
            {
                addedList = assetList.Except(recordInfo.assetList).ToArray(),
                removedList = recordInfo.assetList.Except(assetList).ToArray(),
            };
            diffInfo.addedCount = diffInfo.addedList.Length;
            diffInfo.removedCount = diffInfo.removedList.Length;
            diffInfo.addedList.Sort();
            diffInfo.removedList.Sort();
            return diffInfo;
        }

        /// <summary>
        /// 差异信息
        /// </summary>
        [Serializable]
        public class DiffInfo
        {
            /// <summary>
            /// 增加数量
            /// </summary>
            public int addedCount;

            /// <summary>
            /// 增加
            /// </summary>
            public string[] addedList;

            /// <summary>
            /// 移除数量
            /// </summary>
            public int removedCount;

            /// <summary>
            /// 移除
            /// </summary>
            public string[] removedList;
        }

        #endregion

        #region Record (Plain Text)

        // /// <summary>
        // /// 生成记录文件
        // /// </summary>
        // /// <param name="asset"></param>
        // /// <returns></returns>
        // public static async Task<string> GenerateRecordFile(SpriteAssets asset)
        // {
        //     // 创建文件
        //     string recordFilePath = GetRecordFilePath(asset);
        //     FileInfo recordFileInfo = new FileInfo(recordFilePath);
        //     if (recordFileInfo.Exists)
        //     {
        //         recordFileInfo.Delete();
        //     }
        //     FileStream fileStream = recordFileInfo.Create();
        //     // 写入数据
        //     List<Sprite> spriteList = asset.m_SpriteList;
        //     StreamWriter streamWriter = new StreamWriter(fileStream);
        //     // 信息头
        //     string header = $">>> Asset Count: {spriteList.Count}";
        //     header += "\n- - - - - - - - - - - - - - - - - - - -";
        //     await streamWriter.WriteLineAsync(header);
        //     // 资源路径
        //     for (int i = spriteList.Count - 1; i >= 0; i--)
        //     {
        //         if (spriteList[i] == null) continue;
        //         string assetPath = AssetDatabase.GetAssetPath(spriteList[i]);
        //         await streamWriter.WriteLineAsync(assetPath);
        //     }
        //     // 完成写入
        //     streamWriter.Close();
        //     fileStream.Close();
        //     // 刷新资源
        //     string recordAssetPath = GetAssetRelativePath(recordFilePath);
        //     TextAsset recordAsset = AssetDatabase.LoadAssetAtPath<TextAsset>(recordAssetPath);
        //     if (recordAsset)
        //     {
        //         EditorUtility.SetDirty(recordAsset);
        //         AssetDatabase.SaveAssets();
        //     }
        //     else
        //     {
        //         AssetDatabase.ImportAsset(recordAssetPath);
        //     }
        //     AssetDatabase.Refresh();
        //     // 返回文件路径
        //     return recordFilePath;
        // }
        //
        // /// <summary>
        // /// 获取记录
        // /// </summary>
        // /// <param name="asset"></param>
        // /// <returns></returns>
        // public static async Task<List<string>> GetRecord(SpriteAssets asset)
        // {
        //     string recordFilePath = GetRecordFilePath(asset);
        //     FileInfo recordFileInfo = new FileInfo(recordFilePath);
        //     // 读取记录
        //     List<string> list = new List<string>();
        //     if (!recordFileInfo.Exists)
        //     {
        //         return list;
        //     }
        //     StreamReader streamReader = recordFileInfo.OpenText();
        //     string line;
        //     while ((line = await streamReader.ReadLineAsync()) != null)
        //     {
        //         // 忽略信息行
        //         if (!line.StartsWith("Assets"))
        //         {
        //             continue;
        //         }
        //         list.Add(line);
        //     }
        //     streamReader.Close();
        //     // 反转（使最后添加的记录的排在最后面）
        //     list.Reverse();
        //     return list;
        // }
        //
        // /// <summary>
        // /// 获取记录文件路径
        // /// </summary>
        // /// <param name="asset"></param>
        // /// <returns></returns>
        // public static string GetRecordFilePath(SpriteAssets asset)
        // {
        //     // 资源路径
        //     FileInfo assetFileInfo = new FileInfo(AssetDatabase.GetAssetPath(asset));
        //     // 记录文件
        //     string recordFileName = assetFileInfo.Name.Replace(assetFileInfo.Extension, ".record.txt");
        //     return Path.Combine(assetFileInfo.DirectoryName!, recordFileName);
        // }

        #endregion

        #region 工具函数

        private static bool ContainsPathSegment(string path, string segment)
        {
            // 统一转换为小写以忽略大小写
            return path.ToLower().Contains(segment.ToLower());
        }
        
        /// <summary>
        /// 绝对路径转相对路径
        /// </summary>
        /// <param name="absolutePath">绝对路径</param>
        /// <returns></returns>
        private static string ToAssetRelativePath(string absolutePath)
        {
            return ("Assets" + absolutePath.Substring(Application.dataPath.Length)).Replace("\\", "/");
        }

        /// <summary>
        /// 相对路径转绝对路径
        /// </summary>
        /// <param name="relativePath">相对路径</param>
        /// <returns></returns>
        private static string ToAbsolutePath(string relativePath)
        {
            string projectPath = Application.dataPath;
            projectPath = projectPath.Substring(0, projectPath.LastIndexOf("Assets", StringComparison.Ordinal));
            return Path.Combine(projectPath, relativePath).Replace("\\", "/");
        }

        /// <summary>
        /// 拼接字符串
        /// </summary>
        /// <param name="list"></param>
        /// <param name="separator"></param>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        private static string Join<T>(IList<T> list, string separator = ",")
        {
            StringBuilder builder = new StringBuilder();
            for (int i = 0; i < list.Count; ++i)
            {
                builder.Append(list[i]);
                if (i < list.Count - 1)
                {
                    builder.Append(separator);
                }
            }

            return builder.ToString();
        }

        /// <summary>
        /// 应用资源变更
        /// </summary>
        /// <param name="asset">目标资源</param>
        private static void ApplyAssetChanges(Object asset)
        {
            EditorUtility.DisplayProgressBar("正在应用资源变更...", "请稍候...", 1);
            {
                EditorUtility.SetDirty(asset);
                AssetDatabase.SaveAssets();
            }
            EditorUtility.ClearProgressBar();
        }

        /// <summary>
        /// 重新导入资源
        /// </summary>
        /// <param name="asset">目标资源</param>
        private static async Task ReimportAsset(Object asset)
        {
            // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - 
            // 重导也不管用，Unity 的八哥
            // https://forum.unity.com/threads/solved-issue-with-asset-files-updating-through-collab.457722/
            // AssetDatabase.ImportAsset(AssetDatabase.GetAssetPath(target), ImportAssetOptions.ForceUpdate);
            // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - 
            EditorUtility.DisplayProgressBar("正在重新导入资源...", "请稍候...", 1);
            {
                // 文件路径
                string assetPath = AssetDatabase.GetAssetPath(asset);
                string filePath = Application.dataPath + assetPath.Substring(6);
                string metaPath = filePath + ".meta";
                FileInfo fileInfo = new FileInfo(filePath);
                string dirPath = fileInfo.DirectoryName!.Replace("\\", "/");
                // 临时目录路径（项目根目录）
                string tempDirPath = Application.dataPath.Replace("/Assets", "");
                string tempFilePath = filePath.Replace(dirPath, tempDirPath);
                string tempMetaPath = tempFilePath + ".meta";
                // 移动到临时目录
                File.Move(filePath, tempFilePath);
                File.Move(metaPath, tempMetaPath);
                AssetDatabase.Refresh();
                // 延迟
                await Task.Delay(100);
                // 移动回项目内原位置
                File.Move(tempFilePath, filePath);
                File.Move(tempMetaPath, metaPath);
                AssetDatabase.Refresh();
                // 选中
                Selection.activeObject = AssetDatabase.LoadAssetAtPath<Object>(assetPath);
            }
            EditorUtility.ClearProgressBar();
        }

        #endregion
    }
}