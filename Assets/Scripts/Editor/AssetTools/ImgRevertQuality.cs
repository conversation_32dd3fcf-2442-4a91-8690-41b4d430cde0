using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using UnityEngine.UI; 
using System;
using War.UI;
using System.IO;

public class ImgRevertQuality : Editor
{
    
    static List<string> revertImages = new List<string>();
    [MenuItem("Assets/��ԭͼƬ������ _F12")]
    public static void RevertToHightQuality()
    { 
        var imgType = GameObject.FindObjectsOfType<Image>(true);
        var rawImgType = GameObject.FindObjectsOfType<RawImage>(true);
        var spriteSwitcher = GameObject.FindObjectsOfType<SpriteSwitcher>(true);
        int totatlLength = imgType.Length + rawImgType.Length + spriteSwitcher.Length;
        revertImages = new List<string>();
        AssetDatabase.StartAssetEditing();
        try {
            int accIndex = 0;
            for (int i = 0; i < imgType.Length; i++)
            {
                var img = imgType[i];
                if (img.sprite != null)
                {
                    RevertSprite(img.sprite);
                    
                }
                EditorUtility.DisplayProgressBar("У����ͼ��������", $"{accIndex / totatlLength}", accIndex * 1.0f / totatlLength);
                accIndex++;
            }

            for (int i = 0; i < rawImgType.Length; i++)
            {
                var img = rawImgType[i];
                if (img.texture != null)
                {
                    RevertTexture(img.texture);

                }
                EditorUtility.DisplayProgressBar("У����ͼ��������", $"{accIndex / totatlLength}", accIndex * 1.0f / totatlLength);
                accIndex++;
            }

            for (int i = 0; i < spriteSwitcher.Length; i++)
            {
                var sws = spriteSwitcher[i];
                if (sws.SpriteList != null)
                {
                    for (int j = 0; j < sws.SpriteList.Length; j++)
                    {
                        var sp = sws.SpriteList[j];
                        if (sp != null)
                        {
                            RevertSprite(sp);
                        }
                    }
                }
                EditorUtility.DisplayProgressBar("У����ͼ��������", $"{accIndex / totatlLength}", accIndex * 1.0f / totatlLength);
                accIndex++;
            }

            EditorUtility.ClearProgressBar();

            if (revertImages.Count > 0)
            {
                for (int i = 0; i < revertImages.Count; i++)
                {
                    Debug.LogError("revert img:" + revertImages[i]);
                }
            }
        }catch(Exception e)
        {
            Debug.LogError("faild :"+e.ToString());
        }
        finally
        { 
            AssetDatabase.StopAssetEditing();
        }
        
        if (revertImages.Count > 0)
        {
            for (int i = 0; i < revertImages.Count; i++)
            {
                revertImages[i] = revertImages[i] + ".meta";
            }
            string commitPath = string.Join("*", revertImages.ToArray());
            if (!string.IsNullOrEmpty(commitPath))
            { 
                SVNTool.ProcessCommand("TortoiseProc.exe", "/command:commit /path:" + commitPath);
            } 
        }
     
    }
    public static void RevertSprite(Sprite sprite)
    {
        var assetPath = AssetDatabase.GetAssetPath(sprite);
        if (string.IsNullOrEmpty(assetPath))
        {
            return;
        }
        TextureImporter ip = AssetImporter.GetAtPath(assetPath) as TextureImporter;
        if (ip == null)
        {
            return;
        }
        var textureSetting = ip.GetPlatformTextureSettings("Android");
        if (ip.maxTextureSize == 32 || textureSetting.maxTextureSize == 32) //��Ҫ��ԭ������
        {
            ip.maxTextureSize = 2048;
            textureSetting.maxTextureSize = 2048;
            ip.SetPlatformTextureSettings(textureSetting);
            ip.SaveAndReimport();
            if (!revertImages.Contains(assetPath))
            {
                revertImages.Add(assetPath);
            }
        }
    }
    /// <summary>
    /// ��ԭͼƬ�����ȣ������ԭ�ɹ������ص�ַ�����򷵻ؿ� 
    /// </summary>
    /// <param name="texture"></param>
    /// <returns></returns>
    public static void RevertTexture(Texture texture)
    {
       var assetPath = AssetDatabase.GetAssetPath(texture);
        if (string.IsNullOrEmpty(assetPath))
        {
            return;
        }
        TextureImporter ip = AssetImporter.GetAtPath(assetPath) as TextureImporter;
        if(ip == null)
        {
            return;
        }
        var textureSetting = ip.GetPlatformTextureSettings("Android");
        if (ip.maxTextureSize == 32 || textureSetting.maxTextureSize == 32) //��Ҫ��ԭ������
        {
            ip.maxTextureSize = 2048; 
            textureSetting.maxTextureSize = 2048;
            if (!revertImages.Contains(assetPath))
            {
                revertImages.Add(assetPath);
            }
            ip.SetPlatformTextureSettings(textureSetting);
            ip.SaveAndReimport();
        } 
    }
}
