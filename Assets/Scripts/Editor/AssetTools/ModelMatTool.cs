using UnityEngine;
using UnityEditor;
using System.Collections;

namespace War
{
    /// ���ߣ�_Walker__
    /// ���ӣ�https://www.jianshu.com/p/632869a87848
    public class ModelMatTool : AssetPostprocessor
    {
        private static bool m_NeedWaiting = false;

        /// <summary>
        /// �Ƿ�����ɾ������
        /// OnPostprocessModel�ص���ģ�͵����ʱ��ͻ������
        /// ͨ��������λ��ֻ֤�ڵ��ýű�������ʱ��ִ�С�
        /// </summary>
        private static bool m_EnableDelete = false;

        /// <summary>
        /// ����ɾ��ģ���ϵĲ���
        /// </summary>
        public static IEnumerator DelModelMats(Object[] models)
        {
            foreach (Object obj in models)
            {
                while (m_NeedWaiting) yield return null;
                DelModelMat(obj as GameObject);
            }
        }

        /// <summary>
        /// ɾ��ģ���ϰ󶨵Ĳ���
        /// </summary>
        /// <param name="model">ģ�Ͷ���</param>
        public static void DelModelMat(GameObject model)
        {
            if (null == model) return;
            string assetPath = AssetDatabase.GetAssetPath(model);
            ModelImporter importer = AssetImporter.GetAtPath(assetPath) as ModelImporter;
            if (null == importer) return;
            m_EnableDelete = true;
            m_NeedWaiting = true;

#if UNITY_2020_1_OR_NEWER
            importer.materialImportMode = ModelImporterMaterialImportMode.None;
#else
            importer.importMaterials = true;
            importer.importMaterials = false;
#endif
            AssetDatabase.ImportAsset(assetPath);
        }

        private void OnPostprocessModel(GameObject model)
        {
            if (null == model)
            {
                return;
            }

            if (!m_EnableDelete)
            {
                return;
            }
            m_EnableDelete = false;

            Renderer[] renders = model.GetComponentsInChildren<Renderer>();
            if (null == renders) return;
            foreach (Renderer render in renders)
            {
                render.sharedMaterial = new Material(Shader.Find("Mobile/Diffuse"));
                render.sharedMaterials = new Material[render.sharedMaterials.Length];
            }
            m_NeedWaiting = false;
        }

        protected virtual Material OnAssignMaterialModel(Material previousMaterial, Renderer renderer)
        {
            var materialPath = "Assets/Art/Common/Materials/Default_Material.mat";

            if (AssetDatabase.LoadAssetAtPath(materialPath, typeof(Material)))
            {
                return AssetDatabase.LoadAssetAtPath(materialPath, typeof(Material)) as Material;
            }

            Debug.LogErrorFormat("renderer[{0}] assign default material failed, assign to {1}", renderer.name, previousMaterial.name);

            return previousMaterial;
        }

        [MenuItem("Assets/Remove Selected Models Default Material")]
        static void DelSelectedModelMat()
        {
            Object[] objs = Selection.GetFiltered(typeof(Object), SelectionMode.DeepAssets);
            if (null == objs) return;
            EditorCoroutine.Start(DelModelMats(objs));
        }
    }
}