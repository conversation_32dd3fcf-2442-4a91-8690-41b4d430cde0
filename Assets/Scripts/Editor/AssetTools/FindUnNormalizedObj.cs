using UnityEngine;
using UnityEditor;
using UnityEngine.UI;
using System.Collections.Generic;
using System.IO;
using System.Linq;
public class FindUnNormalizedObj
{
     [MenuItem("GameObject/排查工具/设置所有UI物体Z轴归零")]
    static public void SetAllGameObjectZToZero()
    {
        foreach (var obj in Selection.gameObjects)
        {
           SetGameObjectZbyDepth(obj);
        }
    }

    [MenuItem("GameObject/排查工具/查找所有mask")]
    static public void SetAllGameObjectmask()
    {
        foreach (var obj in Selection.gameObjects)
        {
           SetGameObjectmask(obj);
        }
    }

     static private void SetGameObjectmask(GameObject obj)
    {
        if(obj == null)
            return;
        Mask rectTrans = obj.GetComponent<Mask>();
        if(rectTrans == null)
            return;
        Debug.Log(string.Format("{0} setting z to zero",obj.name));
    }
    static private void SetGameObjectZbyDepth(GameObject obj)
    {
        if(obj == null)
            return;
        RectTransform rectTrans = obj.GetComponent<RectTransform>();
        if(rectTrans == null)
            return;
        Vector3 rectPos = rectTrans.anchoredPosition3D;
        if(rectPos.z != 0)
        {
            rectTrans.anchoredPosition3D = new Vector3(rectPos.x,rectPos.y,0);
            Debug.Log(string.Format("{0} setting z to zero",obj.name));
        }
        for (int i = 0; i < obj.transform.childCount; i++)
        {
            Transform childObj = obj.transform.GetChild(i);
            SetGameObjectZbyDepth(childObj.gameObject);
        }
    }
}
