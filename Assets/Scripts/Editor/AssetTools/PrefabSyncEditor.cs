using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System.IO;
using UnityEngine.UI;
using UnityEditor.Experimental.SceneManagement;
using System;
using UnityEditorInternal;
public class PrefabSyncEditor : Editor
{

    [MenuItem("CONTEXT/Text/ͬ����Ԥ����")]
    [MenuItem("CONTEXT/Image/ͬ����Ԥ����")]
    [MenuItem("CONTEXT/RawImage/ͬ����Ԥ����")]
    [MenuItem("CONTEXT/Button/ͬ����Ԥ����")]
    [MenuItem("CONTEXT/RectTransform/ͬ����Ԥ����")]
    [MenuItem("CONTEXT/Outline/ͬ����Ԥ����")]
    //[MenuItem("CONTEXT/ScrollRectTable/ͬ����Ԥ����")]
    [MenuItem("CONTEXT/VerticalLayoutGroup/ͬ����Ԥ����")]
    [MenuItem("CONTEXT/HorizontalLayoutGroup/ͬ����Ԥ����")]
    [MenuItem("CONTEXT/Canvas/ͬ����Ԥ����")]
    [MenuItem("CONTEXT/ContentSizeFitter/ͬ����Ԥ����")]
    [MenuItem("CONTEXT/TextMeshProUGUI/ͬ����Ԥ����")]
    [MenuItem("CONTEXT/Transform/ͬ����Ԥ����")]
    public static void PrefabSync(MenuCommand menu)
    {
        string prefabResPath = "Assets/UI/Prefabs/";
        var go = Selection.activeGameObject;
        var compType = (Component)menu.context;
        ComponentUtility.CopyComponent(compType);

        if (go != null)
        {
            string compPath = go.name; //���·��
            string prefabName = "";
            Transform parent = go.transform;

            if (compPath.Contains("(Clone)"))
            {
                compPath = compPath.Replace("(Clone)", "");
                prefabName = compPath;
            }
            else
            {
                while (parent.parent != null)
                {

                    parent = parent.parent;
                    string mName = parent.name;
                    if (mName.Contains("(Clone)"))
                    {
                        prefabName = mName.Replace("(Clone)", "");
                        break;
                    }
                    compPath = parent.name + "/" + compPath;

                }

            }

            if (!string.IsNullOrEmpty(prefabName))
            {

                string[] files = Directory.GetFiles(prefabResPath, "*.*", SearchOption.AllDirectories);
                string searchFile = prefabName + ".prefab";
                bool isFind = false; 
                foreach (string file in files)
                {
                    if (file.Contains(searchFile))
                    {
                        string path = file;
                        path = path.Replace("\\", "/");
                        Debug.Log("���ҳɹ�");
                        Debug.Log(path);
                        var prefabIns = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                        if(prefabIns != null)
                        {
                            Transform t = prefabIns.transform.Find(compPath);
                            if (t != null)
                            {
                                var comp = t.GetComponent(compType.GetType().Name);
                                if (comp != null)
                                {
                                    ComponentUtility.PasteComponentValues(comp);
                                }
                                else
                                {
                                    ComponentUtility.PasteComponentAsNew(t.gameObject);
                                }
                                //if (compType.GetType() == typeof(ScrollRectTable))
                                //{
                                //    var sc = comp as ScrollRectTable;
                                //    sc.Clear();

                                //}
                                EditorUtility.SetDirty(prefabIns);
                                EditorUtility.UnloadUnusedAssetsImmediate();
                                Debug.Log("ͬ���ɹ�");
                                isFind = true;
                                break;
                            }
                            else
                            {
                                Debug.LogError("Ԥ������·�����������飺" + compPath);
                            }
                        }

                    }
                }
                if(!isFind)
                {
                    Debug.LogError("û���ҵ����Ԥ����");
                }
            }
        }




    }
}
