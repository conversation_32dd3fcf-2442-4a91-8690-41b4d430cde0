using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using UnityEditor;
using UnityEngine;
using System.Linq;

public class CheckLuaCodeType
{
    

    [MenuItem("Tools/CheckLuaCodeType")]
    static public void CheckAllLuaCodeType()
    {
        string folderName = EditorUtility.OpenFolderPanel("Add Lua Folder", null, null);
        if(folderName != null)
        {
            List<FileInfo> files = new List<FileInfo>();
            List<FileInfo> filesList = GetFile(folderName,".txt",files);
            for (int i = 0; i < filesList.Count; i++)
            {
                FileInfo info = filesList[i];
                (Encoding,bool) result = GetType(info.FullName);
                if(result.Item1 != Encoding.UTF8 || result.Item2 == true)
                {
                    string content = File.ReadAllText(info.FullName);
                    File.WriteAllText(info.FullName, content, new UTF8Encoding(false));
                    Debug.Log("修改文件:" + info.FullName);
                }
            }
        }
    }
    static public void CheckAssetLuaCodeType()
    {
        string folderName = Application.dataPath + "/Lua";
        string loginFolderName = Application.dataPath + "/Driver";
        if (folderName != null)
        {
            List<FileInfo> files = new List<FileInfo>();
            List<FileInfo> filesList = GetFile(folderName, ".txt", files);
            List<FileInfo> loginFilesList = GetFile(loginFolderName, ".txt", files);
            filesList.AddRange(loginFilesList);
            for (int i = 0; i < filesList.Count; i++)
            {
                FileInfo info = filesList[i];
                (Encoding, bool) result = GetType(info.FullName);
                if (result.Item1 != Encoding.UTF8 || result.Item2 == true)
                {
                    string content = File.ReadAllText(info.FullName);
                    File.WriteAllText(info.FullName, content, new UTF8Encoding(false));
                    Debug.Log("修改文件:" + info.FullName);
                }
            }
        }
    }
    /// <summary>
    /// 获得目录下所有文件或指定文件类型文件(包含所有子文件夹)
    /// </summary>
    /// <param name="path">文件夹路径</param>
    /// <param name="extName">扩展名可以多个 例如 .mp3.wma.rm</param>
    /// <returns>List<FileInfo></returns>
    public static List<FileInfo> GetFile(string path, string extName, List<FileInfo> lst)
    {
        try
        {
            string[] dir = Directory.GetDirectories(path); //文件夹列表  
            DirectoryInfo fdir = new DirectoryInfo(path);
            FileInfo[] file = fdir.GetFiles();
           //FileInfo[] file = Directory.GetFiles(path); //文件列表  
           if (file.Length != 0 || dir.Length != 0) //当前目录文件或文件夹不为空          
            {
                foreach (FileInfo f in file) //显示当前目录所有文件  
                {
                    if (extName.ToLower().IndexOf(f.Extension.ToLower()) >= 0)
                    {
                        lst.Add(f);
                    }
                }
                foreach (string d in dir)
                {
                    GetFile(d, extName,lst);//递归  
                }
            }
            return lst;
        }
        catch (Exception ex)
        {
            throw ex;
        }
    }
    /// <summary>
    /// 给定文件的路径，读取文件的二进制数据，判断文件的编码类型
    /// </summary>
    /// <param name=“FILE_NAME“>文件路径</param>
    /// <returns>文件的编码类型</returns>
    public static (System.Text.Encoding,bool) GetType(string FILE_NAME)
    {
        FileStream fs = new FileStream(FILE_NAME, FileMode.Open, FileAccess.Read);
        (Encoding,bool) r = GetType(fs);
        fs.Close();
        return r;
     }
 
    /// <summary>
    /// 通过给定的文件流，判断文件的编码类型
    /// </summary>
    /// <param name=“fs“>文件流</param>
    /// <returns>文件的编码类型</returns>
    public static (System.Text.Encoding,bool) GetType(FileStream fs)
    {
        byte[] Unicode = new byte[] { 0xFF, 0xFE, 0x41 };
        byte[] UnicodeBIG = new byte[] { 0xFE, 0xFF, 0x00 };
        byte[] UTF8 = new byte[] { 0xEF, 0xBB, 0xBF }; //带BOM
        Encoding reVal = Encoding.Default;

        BinaryReader r = new BinaryReader(fs, System.Text.Encoding.Default);
        int i;
        int.TryParse(fs.Length.ToString(), out i);
        byte[] ss = r.ReadBytes(i);
        bool isUTFBOM = false;
        if (IsUTF8Bytes(ss) || (ss[0] == 0xEF && ss[1] == 0xBB && ss[2] == 0xBF))
        {
            reVal = Encoding.UTF8;
            if (ss.Length >= 3 && ss[0] == 0xEF && ss[1] == 0xBB && ss[2] == 0xBF)
                isUTFBOM = true;
        }
        else if (ss[0] == 0xFE && ss[1] == 0xFF && ss[2] == 0x00)
        {
            reVal = Encoding.BigEndianUnicode;
        }
        else if (ss[0] == 0xFF && ss[1] == 0xFE && ss[2] == 0x41)
        {
            reVal = Encoding.Unicode;
        }
        r.Close();
        return (reVal, isUTFBOM);
 
    }
 
    /// <summary>
    /// 判断是否是不带 BOM 的 UTF8 格式
    /// </summary>
    /// <param name=“data“></param>
    /// <returns></returns>
    private static bool IsUTF8Bytes(byte[] data)
    {
        int charByteCounter = 1;//计算当前正分析的字符应还有的字节数
        byte curByte;//当前分析的字节
        for (int i = 0; i < data.Length; i++)
        {
            curByte = data[i];
            if (charByteCounter == 1)
            {
                if (curByte >= 0x80)
                {
                    //判断当前
                    while (((curByte <<= 1) & 0x80) != 0)
                    {
                        charByteCounter++;
                    }
                    //标记位首位若为非0 则至少以2个1开始，如：110XXXXX.....1111110X
                    if (charByteCounter == 1 || charByteCounter > 6)
                    {
                        return false;
                    }
                }
            }
            else
            {
                //若是UTF-8 此时第一位必须为1
                if ((curByte & 0xC0) != 0x80)
                {
                    return false;
                }
                charByteCounter--;
            }
        }
        if (charByteCounter > 1)
        {
            throw new Exception("非预期的byte格式");
        }
        return true;
     }

    [MenuItem("Tools/LuaScriptJson/PrintSize")]
    public static void PrintLuaScriptJsonSize()
    {
        string output = EditorUtility.OpenFolderPanel("导出统计表格_luascript.json", "", "");
        var path = Application.dataPath + "/Scripts/GameUpdate/Plugins/_luascript.json";
        if (!File.Exists(path))
        {
            return;
        }

        LogHelp.Instance.Log("PrintLuaScriptJsonSize-start");

        var LuaPath = "Assets/Lua";
        string[] luaFiles = Directory.GetFiles(LuaPath + "/", "*.txt", SearchOption.AllDirectories);

        //LogHelp:[BUILD][LUA][1] File: Assets / Lua / net\net_route.txt.== now:21:16 lastItvl: 0.0009967 since_start: 0.1312486
        Dictionary<string, string> filesPathDic = new Dictionary<string, string>(); 
        foreach (var luaFile in luaFiles)
        {
            var fileName = Path.GetFileNameWithoutExtension(luaFile);
            filesPathDic.Add(fileName, luaFile);
        }

        var json = File.ReadAllText(path);
        var lusConfig = UIHelper.ToObj<BuildLuaScriptPatch.LuaScriptConfig>(json);
        var dicLua = lusConfig.luascr;
        BuildLuaScriptPatch.LuaScriptConfig luaScriptConfig = new BuildLuaScriptPatch.LuaScriptConfig();
        Dictionary<string, long> filesSizeDic = new Dictionary<string, long>();
        Dictionary<string, object> filesPathDic1 = new Dictionary<string, object>();
        foreach (var luaFile in dicLua)
        {
            if (filesPathDic.TryGetValue(luaFile, out string luaFilePath)) 
            {
                var fileInfo = new FileInfo(luaFilePath);
                long fileSizeInBytes = fileInfo.Length;
                filesSizeDic[luaFile] = fileSizeInBytes;
            }
        }
        var sortedDict = filesSizeDic.OrderByDescending(x => x.Value).ToDictionary(x => x.Key, x => x.Value);
        filesPathDic.Clear();
        long totalsize = 0;
        foreach (var item in sortedDict)
        {
            totalsize += item.Value;
            filesPathDic[item.Key] = SizeSuffix(item.Value, 2);
        }
        
        filesPathDic1["totalsize"] = SizeSuffix(totalsize, 2);
        filesPathDic1["_luascript.json"] = filesPathDic;

        string s = UIHelper.ToJson(filesPathDic1);
        File.WriteAllText($"{output}/LuaScriptJsonSize.csv", s, Encoding.UTF8);
        LogHelp.Instance.Log("PrintLuaScriptJsonSize-end");
    }


    [MenuItem("Tools/LuaScriptJson/RemoveListByLuaScript")]
    public static void RemoveListByLuaScript()
    {
        if (!JenkinsEnv.Instance.GetBool("p_removeListByLuaScript"))
        {
            Debug.Log("p_removeListByLuaScript false");
            return;
        }

#if UNITY_WEBGL && WX_WECHAT
        var path = Application.dataPath + "/Scripts/GameUpdate/Plugins/_luascript.json";
        if (!File.Exists(path))
        {
            return;
        }
        var path_move = Application.dataPath + "/Scripts/GameUpdate/Plugins/_luascript_remove_list.json";
        List<string> removelist = null;
        if (!File.Exists(path))
        {
            return;
        }
        var json_move = File.ReadAllText(path_move);
        removelist = UIHelper.ToObj<List<string>>(json_move);
        if (removelist != null) 
        {
            var json = File.ReadAllText(path);
            var lusConfig = UIHelper.ToObj<LuaScriptConfig>(json);
            var dicLua = lusConfig.luascr;

            List<string> list = dicLua.ToList();
            for (int i = 0; i < removelist.Count; i++)
            {
                if (list.Contains(removelist[i]))
                {
                    list.Remove(removelist[i]);
                }
            }
            lusConfig.luascr = list.ToArray();

            string s = UIHelper.ToJson(lusConfig);
            File.WriteAllText($"{path}", s, Encoding.UTF8);

            AssetDatabase.Refresh();
        }
#endif

    }

    static readonly string[] SizeSuffixes =
    {"bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"};

    public static string SizeSuffix(long value, int decimalPlaces = 1)
    {
        if (decimalPlaces < 0)
        {
            throw new ArgumentOutOfRangeException("decimalPlaces");
        }

        if (value < 0)
        {
            return "-" + SizeSuffix(-value, decimalPlaces);
        }

        if (value == 0)
        {
            return string.Format("{0:n" + decimalPlaces + "} bytes", 0);
        }

        int mag = (int)Math.Log(value, 1024);
        decimal adjustedSize = (decimal)value / (1L << (mag * 10));
        if (Math.Round(adjustedSize, decimalPlaces) >= 1000)
        {
            mag += 1;
            adjustedSize /= 1024;
        }

        return string.Format("{0:n" + decimalPlaces + "} {1}",
            adjustedSize,
            SizeSuffixes[mag]);
    }

}
