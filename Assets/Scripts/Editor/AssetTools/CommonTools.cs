using System.IO;
using UnityEngine;

public class CommonTools
{
    public static System.Type StringToType(string typeAsString)
    {
        System.Type typeAsType = System.Type.GetType(typeAsString);
        return typeAsType;
    }

    // heres the more advanced version
    public static System.Type StringToTypeByName(string aClassName)
    {
        var assemblies = System.AppDomain.CurrentDomain.GetAssemblies();
        for (int i = 0; i < assemblies.Length; i++)
        {
            var types = assemblies[i].GetTypes();
            for (int j = 0; j < types.Length; j++)
            {
                var type = types[j];
                if (typeof(UnityEngine.Object).IsAssignableFrom(type) && aClassName == type.Name)
                {
                    return type;
                }
            }
        }
        return null;
    }

    public static string GetComponentFullPath(Component component)
    {
        string fullPath = component.name;
        Transform parent = component.transform.parent;
        while (parent != null)
        {
            fullPath = parent.name + "/" + fullPath;
            parent = parent.parent;
        }
        return fullPath;
    }

    public static string GetComponentFullPath(GameObject obj)
    {
        string fullPath = obj.name;
        Transform parent = obj.transform.parent;
        while (parent != null)
        {
            fullPath = parent.name + "/" + fullPath;
            parent = parent.parent;
        }
        return fullPath;
    }

    public static Texture2D LoadImage(string filePath)
    {
        if(!File.Exists(filePath))
        {
            return null;
        }

        byte[] fileData = File.ReadAllBytes(filePath);
        if(fileData == null || fileData.Length == 0)
        {
            return null;
        }
        Texture2D tex = new Texture2D(1, 1);
        tex.LoadImage(fileData);
        return tex;
    }
}
