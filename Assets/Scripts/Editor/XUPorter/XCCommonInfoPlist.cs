#if UNITY_IPHONE || UNITY_IOS
using System;
using System.Collections.Generic;
using System.IO;

using UnityEditor;
using UnityEditor.Callbacks;
using UnityEditor.iOS.Xcode;
using UnityEditor.iOS.Xcode.Extensions;
using UnityEngine;

public static class XCCommonInfoPlist
{
	[PostProcessBuild]
    public static void OnPostProcessBuild(BuildTarget buildTarget, string path)
    {
		//将Common_Info.plist数据合并到Info.plist中
		string fileName = "Common_Info.plist";
		string commonPlistPath = "./Assets/Plugins/iOS/" + fileName;
		Debug.LogWarning("XCCommonInfoPlist commonPlistPath:" + commonPlistPath);
		if (!File.Exists(commonPlistPath))
		{
			Debug.LogWarning(commonPlistPath + " can not find");
			return;
		}

		string plistPath = Path.Combine(path, "Info.plist");
		PlistDocument plist = new PlistDocument();
		plist.ReadFromFile(plistPath);
		PlistElementDict rootDic = plist.root;
		try
		{
			PlistDocument commonPlist = new PlistDocument();
			commonPlist.ReadFromFile(commonPlistPath);
			PlistElementDict newRootDic = commonPlist.root;
			foreach (var node in newRootDic.values)
			{
				//Debug.LogWarning("node:" + node+ ",node.Key:" + node.Key+ ",node.Value:" + node.Value);
				if (node.Value is PlistElementString)
				{
					AddPlistElementString(rootDic, node);
				}
				else if (node.Value is PlistElementArray)
				{
					AddPlistElementArray(rootDic, node);
				}
				else if(node.Value is PlistElementDict)
                {
					AddPlistElementDict(rootDic, node);
				}
			}
		}
		catch (Exception ex)
		{
			Debug.LogError("Failed to read Common_Info.plist: " + ex.Message);
		}

        File.WriteAllText(plistPath, plist.WriteToString());
	}

	private static void AddPlistElementString(PlistElementDict rootDic, KeyValuePair<string, PlistElement> node)
	{
		if (!string.IsNullOrEmpty(node.Key))
			rootDic.SetString(node.Key, node.Value.AsString());
	}

	private static void AddPlistElementArray(PlistElementDict rootDic, KeyValuePair<string, PlistElement> node)
	{
		var array = rootDic[node.Key] as PlistElementArray;
		if (array == null)
		{
			array = rootDic.CreateArray(node.Key);
		}

		var arr = node.Value.AsArray();
		foreach (var item in arr.values)
		{
			if (item is PlistElementString)
			{
				//Debug.LogWarning("item:" + item + ",item.ToString():" + item.ToString()+ ",item.AsString():"+ item.AsString());
				array.AddString(item.AsString());
			}
		}
	}

	private static void AddPlistElementDict(PlistElementDict rootDic, KeyValuePair<string, PlistElement> node)
	{
		var dic = rootDic[node.Key] as PlistElementDict;
		if (dic == null)
		{
			dic = rootDic.CreateDict(node.Key);
		}
		var dictArr = node.Value.AsDict();
		foreach (var item in dictArr.values)
		{
			//Debug.LogWarning(item.GetType() + " item:" + item + ",item.ToString():" + item.ToString() + ",item.AsString():" + item.Key + ":" + item.Value);
			if (item.Value.GetType() == typeof(PlistElementString))
			{
				dic.SetString(item.Key, item.Value.AsString());
			}
			else if (item.Value.GetType() == typeof(PlistElementBoolean))
			{
				dic.SetBoolean(item.Key, item.Value.AsBoolean());
			}
			else if (item.Value.GetType() == typeof(PlistElementInteger))
			{
				dic.SetInteger(item.Key, item.Value.AsInteger());
			}
			else if (item.Value.GetType() == typeof(PlistElementReal))
			{
				dic.SetReal(item.Key, item.Value.AsReal());
			}
			else if (item.Value.GetType() == typeof(PlistElementDate))
			{
				dic.SetDate(item.Key, item.Value.AsDate());
			}
			else
			{
				Debug.LogError("check PlistElemen Type!!!");
			}
		}
	}

}
#endif
