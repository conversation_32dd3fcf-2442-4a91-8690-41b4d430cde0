using UnityEngine;
using UnityEditor;
using UnityEngine.UI;
using System.Collections.Generic;
using System.IO;
using System.Linq;

public class ReplaceMaterial : EditorWindow
{
    class MaterialInfo
    {
        public int id = 0;
        public Material srcMaterial;
        public Material desMaterial;
    }
    struct StencilInfo
    {
        public float stencilRef;
        public float stencilReadMask;
        public float stencilWriteMask;
    }
    GameObject prefab = null;
    List<MaterialInfo> materialSet = new List<MaterialInfo>();

    int materialCount = 0;
    int preMaterialCount = 0;
    bool removeNumber = true;

    string indexSeparator = "_";

    [MenuItem("Tools/Replace Material")]
	static void Open()
	{
		GetWindow<ReplaceMaterial>();
	}

    void OnDestroy()
    {
        materialCount = 0;
        preMaterialCount = 0;
        materialSet.Clear();
    }

	void OnGUI()
	{
        EditorGUILayout.BeginHorizontal();
        GUILayout.Label("需要被替换的Prefab:");
        prefab = EditorGUILayout.ObjectField(prefab, typeof(GameObject), false) as GameObject;
        EditorGUILayout.EndHorizontal();

        OnGUIReplaceMaterial();
        OnGUIModifyStencilValue();
    }

    /// <summary>
    /// 去掉字符串中的数字
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    public static string RemoveNumber(string key)
    {
        return System.Text.RegularExpressions.Regex.Replace(key, @"\d", "");
    }

    void OnGUIReplaceMaterial()
    {
        EditorGUILayout.BeginHorizontal();
        GUILayout.Label("需要被替换的材质数量:");
        materialCount = EditorGUILayout.IntField(materialCount);
        EditorGUILayout.EndHorizontal();
        if (preMaterialCount != materialCount || materialSet.Count != materialCount)
        {
            preMaterialCount = materialSet.Count;
            int offsetCount = preMaterialCount - materialCount;
            if (offsetCount > 0)
            {
                materialSet.RemoveRange(preMaterialCount, offsetCount);
            }
            else if (offsetCount < 0)
            {
                for (int i = 0; i < -offsetCount; i++)
                {
                    materialSet.Add(new MaterialInfo());
                }
            }
            preMaterialCount = materialCount;
        }
        for (int i = 0; i < materialSet.Count; i++)
        {
            EditorGUILayout.BeginHorizontal();
            GUILayout.Label("需要被替换的材质:");
            MaterialInfo materialInfo = materialSet[i];
            materialInfo.srcMaterial = EditorGUILayout.ObjectField(materialInfo.srcMaterial, typeof(Material), false) as Material;
            materialInfo.desMaterial = EditorGUILayout.ObjectField(materialInfo.desMaterial, typeof(Material), false) as Material;
            EditorGUILayout.EndHorizontal();
        }

        EditorGUILayout.BeginHorizontal();
        removeNumber = EditorGUILayout.Toggle(removeNumber);
        //if (GUILayout.Button("替换文件夹下的所有Material"))
        //{
        //    ReplaceMaterials(false);
        //    AssetDatabase.SaveAssets();
        //}
        //if (GUILayout.Button("替换所有Material并拷贝Stencil ID"))
        //{
        //    ReplaceMaterials(true);
        //    AssetDatabase.SaveAssets();
        //}
        if (GUILayout.Button("替换所有Material并拷贝Stencil属性"))
        {
            ReplaceMaterialProperty();
            AssetDatabase.SaveAssets();
        }
        EditorGUILayout.EndHorizontal();
    }

    private bool ReplaceMaterialProperty()
    {
        if (materialSet == null || materialSet.Count == 0)
        {
            Debug.LogError("请先设置相应材质");
            return false;
        }

        string prefabPath = AssetDatabase.GetAssetPath(prefab);
        string prefabFolder = Path.GetDirectoryName(prefabPath);
        string[] allPath = AssetDatabase.FindAssets("t:Prefab", new string[] { prefabFolder });
        Dictionary<string, Material> cachedMaterialSet = new Dictionary<string, Material>();
        for (int i = 0; i < allPath.Length; i++)
        {
            string path = AssetDatabase.GUIDToAssetPath(allPath[i]);
            var obj = AssetDatabase.LoadAssetAtPath(path, typeof(GameObject)) as GameObject;
            if (obj != null)
            {
                var rendererSet = obj.GetComponentsInChildren<Renderer>();
                for (int j = 0; j < rendererSet.Length; j++)
                {
                    var renderer = rendererSet[j];
                    for (int mt = 0; mt < materialSet.Count; mt++)
                    {
                        var material = materialSet[mt];
                        bool needModify = renderer.sharedMaterial == material.srcMaterial;
                        if (!needModify && removeNumber && null != renderer.sharedMaterial)
                        {
                            string modifyName = RemoveNumber(material.srcMaterial.name);
                            needModify = renderer.sharedMaterial.name.Contains(modifyName);
                        }
                        if (needModify)
                        {
                            Material cachedMaterial;
                            if (cachedMaterialSet.TryGetValue(renderer.sharedMaterial.name, out cachedMaterial))
                            {
                                renderer.sharedMaterial = cachedMaterial;
                            }
                            else
                            {
                                Material copyMat = GameObject.Instantiate(material.desMaterial);

                                float stencilRef = renderer.sharedMaterial.GetFloat("_Stencil");
                                copyMat.SetFloat("_Stencil", stencilRef);

                                float stencilReadMask = renderer.sharedMaterial.GetFloat("_StencilReadMask");
                                copyMat.SetFloat("_StencilReadMask", stencilReadMask);

                                float stencilWriteMask = renderer.sharedMaterial.GetFloat("_StencilWriteMask");
                                copyMat.SetFloat("_StencilWriteMask", stencilWriteMask);

                                string idx = System.Text.RegularExpressions.Regex.Replace(renderer.sharedMaterial.name, @"[^0-9]+", "");
                                string materialSavePath = AssetDatabase.GetAssetPath(material.srcMaterial);
                                string materialSaveFolder = Path.GetDirectoryName(materialSavePath);
                                string modifyName = RemoveNumber(material.desMaterial.name);
                                AssetDatabase.CreateAsset(copyMat, materialSaveFolder + "/" + modifyName + "_" + idx + ".mat");

                                cachedMaterialSet[renderer.sharedMaterial.name] = copyMat;

                                renderer.sharedMaterial = copyMat;
                            }
                        }
                    }
                }
                EditorUtility.SetDirty(obj);
                Debug.LogError(obj.name);
            }
        }

        return true;
    }

    private bool ReplaceMaterials(bool copyStencil)
    {
        if (materialSet == null || materialSet.Count == 0)
        {
            Debug.LogError("请先设置相应材质");
            return false;
        }

        string prefabPath = AssetDatabase.GetAssetPath(prefab);
        string prefabFolder = Path.GetDirectoryName(prefabPath);
        string[] allPath = AssetDatabase.FindAssets("t:Prefab", new string[] { prefabFolder });
        for (int i = 0; i < allPath.Length; i++)
        {
            string path = AssetDatabase.GUIDToAssetPath(allPath[i]);
            var obj = AssetDatabase.LoadAssetAtPath(path, typeof(GameObject)) as GameObject;
            if (obj != null)
            {
                var rendererSet = obj.GetComponentsInChildren<Renderer>();
                for (int j = 0; j < rendererSet.Length; j++)
                {
                    var renderer = rendererSet[j];
                    for(int mt = 0; mt < materialSet.Count; mt++)
                    {
                        var material = materialSet[mt];
                        bool needModify = renderer.sharedMaterial == material.srcMaterial;
                        if(!needModify && removeNumber)
                        {
                            string modifyName = RemoveNumber(material.srcMaterial.name);
                            needModify = renderer.sharedMaterial.name.Contains(modifyName);
                        }
                        if (needModify)
                        {
                            if (copyStencil)
                            {
                                var materialPropertyHelper = renderer.GetComponent<MaterialPropertyHelper>();
                                if (null == materialPropertyHelper)
                                {
                                    materialPropertyHelper = renderer.gameObject.AddComponent<MaterialPropertyHelper>();
                                }
                                if (materialPropertyHelper)
                                {
                                    float stencilRef = renderer.sharedMaterial.GetFloat("_Stencil");
                                    materialPropertyHelper.SetFloatValue("_Stencil", stencilRef);

                                    float stencilReadMask = renderer.sharedMaterial.GetFloat("_StencilReadMask"); 
                                    materialPropertyHelper.SetFloatValue("_StencilReadMask", stencilReadMask);

                                    float stencilWriteMask = renderer.sharedMaterial.GetFloat("_StencilWriteMask");
                                    materialPropertyHelper.SetFloatValue("_StencilWriteMask", stencilWriteMask);
                                }
                            }
                            renderer.sharedMaterial = material.desMaterial;
                        }
                    }
                }
                EditorUtility.SetDirty(obj);
                Debug.LogError(obj.name);
            }
        }

        return true;
    }

    private bool ModifyStencilValue()
    {
        string prefabPath = AssetDatabase.GetAssetPath(prefab);
        string prefabFolder = Path.GetDirectoryName(prefabPath);
        string[] allPath = AssetDatabase.FindAssets("t:Prefab", new string[] { prefabFolder });
        for (int i = 0; i < allPath.Length; i++)
        {
            string path = AssetDatabase.GUIDToAssetPath(allPath[i]);
            var obj = AssetDatabase.LoadAssetAtPath(path, typeof(GameObject)) as GameObject;
            if (obj == null)
            {
                continue;
            }
            var rendererSet = obj.GetComponentsInChildren<Renderer>();
            for (int j = 0; j < rendererSet.Length; j++)
            {
                var renderer = rendererSet[j];
                for (int mt = 0; mt < materialSet.Count; mt++)
                {
                    var material = materialSet[mt];
                    if (renderer.sharedMaterial == material.srcMaterial)
                    {
                        var materialPropertyHelper = renderer.GetComponent<MaterialPropertyHelper>();
                        if (null == materialPropertyHelper)
                        {
                            materialPropertyHelper = renderer.gameObject.AddComponent<MaterialPropertyHelper>();
                        }
                        if (materialPropertyHelper)
                        {
                            int index;
                            string[] indexSet = renderer.name.Split(indexSeparator[0]);
                            int length = indexSet.Length;
                            if (length > 0 && int.TryParse(indexSet[length - 1], out index))
                            {
                                materialPropertyHelper.SetFloatValue("_Stencil", index + 1);
                            }
                        }
                    }
                }
            }
            EditorUtility.SetDirty(obj);
            Debug.LogError(obj.name);
        }
        return true;
    }

    void OnGUIModifyStencilValue()
    {
        EditorGUILayout.BeginHorizontal();
        //e.g. Mask_0,输入分割符_,得到的顺序值为 index = 0,最终修改的stencil ref 值为 index + 1
        GUILayout.Label("顺序数字分割符:");
        indexSeparator = GUILayout.TextField(indexSeparator);

        if (GUILayout.Button("递增文件夹下的所有Stencil"))
        {
            ModifyStencilValue();
            AssetDatabase.SaveAssets();
        }

        EditorGUILayout.EndHorizontal();
    }
}
