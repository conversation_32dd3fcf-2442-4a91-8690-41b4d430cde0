using System;
using System.Collections;
using UnityEngine;
using UnityEditor;
using War.Battle;

[CustomEditor(typeof(War.Battle.ParticleConfig))]
public class ParticleConfigEditor : Editor
{
    private bool m_IsEditing = false;

    public override void OnInspectorGUI()
    {
        EditorGUI.BeginChangeCheck();
        serializedObject.Update();
        SerializedProperty iterator = serializedObject.GetIterator();
        for (bool flag = true; iterator.NextVisible(flag); flag = false)
        {
            using (new EditorGUI.DisabledScope("m_Script" == iterator.propertyPath))
            {
                if (iterator.propertyPath == "cloneFlag")
                {
                    ParticleConfig config = target as ParticleConfig;
                    config.cloneFlag = (int)(ParticleConfig.EffectTypeFlag)EditorGUILayout.EnumMaskField("ÿ��Ŀ���¡", (ParticleConfig.EffectTypeFlag)config.cloneFlag);
                }
                else if(iterator.propertyPath == "visibleFlag")
                {
                    ParticleConfig config = target as ParticleConfig;
                    config.visibleFlag = (int)(ParticleConfig.EffectTypeFlag)EditorGUILayout.EnumMaskField("Visible flag������ѡ��", (ParticleConfig.EffectTypeFlag)config.visibleFlag);
                }
                else
                {
                    EditorGUILayout.PropertyField(iterator, true, new GUILayoutOption[0]);
                }
            }
        }
        serializedObject.ApplyModifiedProperties();
        EditorGUI.EndChangeCheck();

        GUIStyle style = new GUIStyle("Button");

        EditorGUI.BeginChangeCheck();
        m_IsEditing = ToggleButton(m_IsEditing, "�༭FlyEffect��/�յ�", style);
        if (EditorGUI.EndChangeCheck())
        {
            SceneView.RepaintAll();
        }
    }

    private bool ToggleButton(bool toggle, string label, GUIStyle style)
    {
        Rect rect = EditorGUILayout.GetControlRect(true, 23f, style);
        Rect position = new Rect(rect.xMin + EditorGUIUtility.labelWidth, rect.yMin, 33f, 23f);
        GUIContent content = new GUIContent(label);
        Vector2 vector = GUI.skin.label.CalcSize(content);
        Rect labelRect = new Rect(position.xMax + 5f, rect.yMin + ((rect.height - vector.y) * 0.5f), vector.x, rect.height);

        bool t = GUI.Toggle(position, toggle, EditorGUIUtility.IconContent("EditCollider"), style);
        GUI.Label(labelRect, label);
        return t;
    }

    protected void OnSceneGUI()
    {
        if (m_IsEditing)
        {
            ParticleConfig target = (ParticleConfig)base.target;
            if (!Mathf.Approximately(target.transform.lossyScale.sqrMagnitude, 0f))
            {
                using (new Handles.DrawingScope(Matrix4x4.TRS(target.transform.position, target.transform.rotation, Vector3.one)))
                {
                    Color c = Handles.color;
                    Handles.color = Color.green;
                    Vector3 pos = target.sourcePoint;
                    EditorGUI.BeginChangeCheck();
                    pos = Handles.FreeMoveHandle(pos, Quaternion.identity, HandleUtility.GetHandleSize(pos) * 0.2f, Vector3.zero, Handles.CubeHandleCap);
                    if (EditorGUI.EndChangeCheck())
                    {
                        Undo.RecordObject(target, string.Format("Modify {0}", ObjectNames.NicifyVariableName(base.target.GetType().Name)));
                        target.sourcePoint = pos;
                    }

                    Handles.color = Color.red;
                    pos = target.destPoint;
                    EditorGUI.BeginChangeCheck();
                    pos = Handles.FreeMoveHandle(pos, Quaternion.identity, HandleUtility.GetHandleSize(pos) * 0.2f, Vector3.zero, Handles.CubeHandleCap);
                    if (EditorGUI.EndChangeCheck())
                    {
                        Undo.RecordObject(target, string.Format("Modify {0}", ObjectNames.NicifyVariableName(base.target.GetType().Name)));
                        target.destPoint = pos;
                    }

                    Handles.color = c;
                }
            }
        }
    }
}