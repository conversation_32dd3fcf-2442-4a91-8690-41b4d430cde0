using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using War.Battle;

[CustomEditor(typeof(BattleLayoutPreview))]
public class BattleComponentHubEditor : Editor
{
    Object TargetObject;
    Object TargetMap;

    //GameObject uiActorObj;
    GameObject testMap = null;
    List<GameObject> testObjectList = new List<GameObject>();
    BattleLayoutPreview preview;

    Material tempMat;
    public override void OnInspectorGUI()
    {
        if (preview == null)
        {
            preview = target as BattleLayoutPreview;
        }
        //GUILayout.BeginHorizontal();
        //if (GUILayout.Button("�л�Ϊս������"))
        //{
        //    preview.isUIMode = false;
        //    preview.SwitchToBattle();
        //}
        //if (GUILayout.Button("�л�ΪUIģ��"))
        //{
        //    preview.isUIMode = true;
        //    preview.SwitchToUI();
        //}
        //GUILayout.EndHorizontal();
        DrawDefaultInspector();


        if (!Application.isPlaying)
        {
            TargetObject = EditorGUILayout.ObjectField("����ģ��", TargetObject, typeof(GameObject), false);
            //if (preview.isUIMode)
            //{
            //    GUILayout.BeginHorizontal();
            //    if (GUILayout.Button("��ʼ�����Զ���"))
            //    {
            //        uiActorObj = preview.ActorRoot.childCount > 0 ? preview.ActorRoot.GetChild(0).gameObject : PrefabUtility.InstantiatePrefab(TargetObject, preview.ActorRoot) as GameObject;
            //        uiActorObj.transform.localRotation = Quaternion.Euler(0,180,0);
            //        uiActorObj.GetComponent<Card>().Initialize();
            //        uiActorObj.GetComponent<Card>().SetupShadow();
            //    }

            //    if (GUILayout.Button("������Զ���"))
            //    {
            //        uiActorObj = uiActorObj != null? uiActorObj: (preview.ActorRoot.childCount > 0 ? preview.ActorRoot.GetChild(0).gameObject : null);

            //        if (uiActorObj)
            //        {
            //            DestroyImmediate(uiActorObj);
            //        }
            //    }

            //    GUILayout.EndHorizontal();
            //}
            //else
            //{

                TargetMap = EditorGUILayout.ObjectField("���Ե�ͼ", TargetMap, typeof(GameObject), false);
                if (TargetObject == null)
                {
                    TargetObject = AssetDatabase.LoadAssetAtPath<GameObject>("assets/art/characters/luojier/prefabs/luojier.prefab");
                }

                if (TargetMap == null)
                {
                    TargetMap = AssetDatabase.LoadAssetAtPath<GameObject>("assets/art/maps/battle/background/212.prefab");
                }

                GUILayout.BeginHorizontal();
                if (GUILayout.Button("��ʼ�����Զ���"))
                {
                    //DestroyTestObject();
                    CreateOrFindObject();
                    ViewScale();
                }

                if (GUILayout.Button("������Զ���"))
                {
                    CreateOrFindObject(true);
                    DestroyTestObject();
                }

                GUILayout.EndHorizontal();
            //if (GUILayout.Button("���沼��"))
            //{
            //    DestroyTestObject();
            //    EditorUtility.SetDirty(preview.virtualCamera.transform);
            //    EditorUtility.SetDirty(preview.hub.BackGroundRoot.transform);
            //    EditorUtility.SetDirty(preview.player);
            //    AssetDatabase.Refresh();
            //}
            //}

            //}
            //else
            //{
            //    if (preview.isUIMode)
            //    {
            //        if (GUILayout.Button("����Show����"))
            //        {
            //            uiActorObj = uiActorObj != null ? uiActorObj : (preview.ActorRoot.childCount > 0 ? preview.ActorRoot.GetChild(0).gameObject : null);

            //            if (uiActorObj)
            //            {
            //                uiActorObj.GetComponent<Animator>().SetBool("Show",false);
            //                uiActorObj.GetComponent<Animator>().SetTrigger("ShowTrigger");

            //            }
            //        }

            //        GUILayout.BeginHorizontal();
            //        if (GUILayout.Button("�滻���Բ���"))
            //        {
            //            uiActorObj = uiActorObj != null ? uiActorObj : (preview.ActorRoot.childCount > 0 ? preview.ActorRoot.GetChild(0).gameObject : null);

            //            if (uiActorObj)
            //            {
            //                preview.ChangeMaterial(uiActorObj.GetComponent<Card>(), true);
            //            }
            //        }
            //        if (GUILayout.Button("��ԭ����"))
            //        {
            //            uiActorObj = uiActorObj != null ? uiActorObj : (preview.ActorRoot.childCount > 0 ? preview.ActorRoot.GetChild(0).gameObject : null);

            //            if (uiActorObj)
            //            {
            //                preview.ChangeMaterial(uiActorObj.GetComponent<Card>(), false);
            //            }
            //        }
            //        GUILayout.EndHorizontal();
            //    }

        }

        preview.SetCameraTrans();
    }

    void DestroyTestObject()
    {
        if (testObjectList.Count > 0)
        {
            foreach(GameObject obj in testObjectList)
            {
                GameObject.DestroyImmediate(obj);
            }
            testObjectList.Clear();
        }
        if (testMap)
        {
            GameObject.DestroyImmediate(testMap);
        }
        //if (uiActorObj)
        //{
        //    GameObject.DestroyImmediate(uiActorObj);
        //}
        preview.CMController.Stop();
    }

    void ViewScale()
    {
        preview.UpdateScale();
        BattleComponentHub hub = preview.hub;
        foreach (BattleActorNode node in hub.actorManager.nodeList)
        {
            if(node)
            {
                Transform obj = node.transform.childCount > 0 ? node.transform.GetChild(0) : null;
                if (obj != null)
                {
                    float _scale = 1;
                    if (node.order >= 9)
                    {
                        _scale = hub.layerScale[0];
                    }
                    else if (node.order < 9 && node.order >= 6)
                    {
                        _scale = hub.layerScale[1];
                    }
                    else if (node.order < 6 && node.order >= 3)
                    {
                        _scale = hub.layerScale[2];
                    }
                    else if (node.order < 3)
                    {
                        _scale = hub.layerScale[3];
                    }
                    CardConfig cconfig = obj.GetComponent<CardConfig>();
                    obj.transform.localScale = Vector3.one * cconfig.size * _scale;
                }
            }

        }
        SetupHudTrans();
    }

    //void ViewRevert()
    //{
    //    if (ScaleRecord == 0) return;
    //    BattleComponentHub hub = preview.hub;
    //    foreach (BattleActorNode node in hub.actorManager.nodeList)
    //    {
    //        Transform obj = node.transform.childCount > 0 ? node.transform.GetChild(0) : null;
    //        if (obj != null)
    //        {
    //            CardConfig cconfig = obj.GetComponent<CardConfig>();
    //            obj.transform.localScale = Vector3.one * cconfig.size;
    //        }
    //    }
    //}

    void CreateOrFindObject(bool FindOnly = false)
    {
        BattleComponentHub hub = preview.hub;

        SetUpCameraGroup();

        foreach (BattleActorNode node in hub.actorManager.nodeList)
        {
            if (node)
            {
                Transform obj = node.transform.childCount > 0 ? node.transform.GetChild(0) : null;
                if (obj != null)
                {
                    testObjectList.Add(obj.gameObject);
                }
                else if (!FindOnly)
                {
                    GameObject item = PrefabUtility.InstantiatePrefab(TargetObject, node.transform) as GameObject;
                    obj = item.transform;
                    testObjectList.Add(item);
                }
                if (!FindOnly)
                {
                    obj.GetComponent<Card>().Initialize();
                    obj.GetComponent<Card>().SetupShadow();
                }
            }

        }
        preview.maptRoot = preview.hub.BackGroundRoot;
       testMap = preview.maptRoot.childCount > 0 ? preview.maptRoot.GetChild(0).gameObject : PrefabUtility.InstantiatePrefab(TargetMap, preview.maptRoot) as GameObject;

    }

    void SetUpCameraGroup()
    {
        preview.CMController.Stop();
        foreach (BattleActorNode node in preview.hub.actorManager.nodeList)
        {
            if (node)
            {
                preview.CMController.Add(node.transform, preview.CMController.defualt.radius);
            }

        }

        
    }

    void SetupHudTrans()
    {
        Transform hudRoot = preview.hub.hudManager.gameObject.transform;
        for (int i = 0; i < hudRoot.childCount; i++)
        {
            BattleHud c_hud = hudRoot.GetChild(i).GetComponent<BattleHud>();
            Card c_card = testObjectList[i].GetComponent<Card>();
            if (c_card)
            {
                InitHudTransByCard(c_hud.transform, preview.hub, c_card);
            }
        }
    }

    //Layout�༭ר��
    Vector3 actorPosition;
    Quaternion actorRotation = Quaternion.identity;
    void InitHudTransByCard(Transform _hud, BattleComponentHub _hub, Card _card)
    {
        //actorRotation.x = (-90 / 360) + preview.hub.battleCamera.transform.rotation.x;
        //actorRotation.y = _hud.rotation.y;
        //actorRotation.z = _hud.rotation.z;
        //actorRotation.w = _hud.rotation.w;
        //_hud.rotation = actorRotation;

        actorPosition = _card.GetAnchorPoint();
        actorPosition.y += _hub.hudManager.headOffset;
        _hud.position = actorPosition;

        //Debug.LogError("_card.GetAnchorPoint() >"+ _card.config.anchor + "/"+_hud.position);
    }
}
