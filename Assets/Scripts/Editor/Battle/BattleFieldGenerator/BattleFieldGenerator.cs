using System;
using System.Collections.Generic;
using UnityEngine;

using UnityEditor;
using War.Battle;
using UnityEngine.UI;
using War.UI;


public class BattleFieldCustomGenerator
{
    public BattleComponentHub hub;
    public Camera camera;
    public List<GameObject> masks;
    public BattleActorManager actorManager;
    public BattleHudManager hudManager;
    public BattleTalkingBubbleManager bubbleManager;
    public BattleFloatTextManager textManager;
    public BattleJumpoutManager jumpoutManager;
    public BattleFxManager fxManager;

    public void GenerateRootAndCamera(int down, int up)
    {
        hub = BattleFieldBuilder.BuildManager(string.Format("{0}v{1}", down, up));
    }


}

