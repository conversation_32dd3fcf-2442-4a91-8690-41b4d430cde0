using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEditor;
using War.Battle;


public class BattleFieldEditor : EditorWindow
{
    float xScale = 5;
    BattleLayoutHelpInfo battleLayoutHelpInfo;
    BattleLayoutDirectory battleLayoutDirectory;

    // Add menu named "My Window" to the Window menu
    [<PERSON>uI<PERSON>("Tool/Battle Field Editor")]
    static void Init()
    {
        // Get existing open window or if none, make a new one:
        BattleFieldEditor window = (BattleFieldEditor)EditorWindow.GetWindow(typeof(BattleFieldEditor));
        window.Show();
    }
    
    private void OnGUI()
    {
        EditorGUI.BeginChangeCheck();
        xScale = EditorGUILayout.Slider("scale", xScale, 1, 5);
        if (EditorGUI.EndChangeCheck())
        {
            GameObject mask = Selection.objects[0] as GameObject;
            if (mask.name.StartsWith("Mask_"))
            {
                MeshRenderer renderer = mask.GetComponent<MeshRenderer>();
                //float aspect = renderer.bounds.size.x / renderer.bounds.size.y;
                //Vector3 scale = new Vector3(xScale, (xScale - 1) * aspect + 1, 1);
                //mask.transform.GetChild(0).localScale = scale;

                float x = xScale / (renderer.bounds.size.x * 100) + 1;
                float y = xScale / (renderer.bounds.size.y * 100) + 1;
                float z = 1;
                mask.transform.GetChild(0).localScale = new Vector3(x, y, z);
            }
        }

        if (GUILayout.Button("create style"))
        {
            BattleHudStyle asset = ScriptableObject.CreateInstance<BattleHudStyle>();
            AssetDatabase.CreateAsset(asset, "Assets/hudStyle.asset");
            AssetDatabase.SaveAssets();
        }

        if (GUILayout.Button("create template"))
        {
            UnityEngine.Object obj = Selection.objects[0];
            BattleFieldBuilder.BuildTemplate(obj as GameObject);
        }

        GUILayout.BeginHorizontal();
        for (int i = 0; i < 6; ++i)
        {
            if (GUILayout.Button("Mask_" + i.ToString()))
            {
                GameObject go = Selection.objects[0] as GameObject;
                go.name = "Mask_" + i.ToString();

                MeshRenderer renderer = go.GetComponent<MeshRenderer>();
                renderer.sharedMaterial = AssetDatabase.LoadAssetAtPath<Material>("Assets/Art/Maps/Battle/common/ComicMask_0.mat");

                float x = xScale / renderer.bounds.size.x / 100 + 1;
                float y = xScale / renderer.bounds.size.y / 100 + 1;
                float z = 1;

                GameObject frame = GameObject.Instantiate(go);
                frame.name = "Frame_" + i.ToString();
                frame.transform.SetParent(go.transform, false);
                frame.transform.localPosition = Vector3.zero;
                frame.transform.localRotation = Quaternion.identity;
                frame.transform.localScale = new Vector3(x, y, z);
                frame.GetComponent<MeshRenderer>().sharedMaterial
                    = AssetDatabase.LoadAssetAtPath<Material>("Assets/Art/Maps/Battle/common/BlackFrame.mat");
            }
        }
        GUILayout.EndHorizontal();

        GUILayout.Space(10);
        for (int r = 0; r < 4; r++)
        {
            GUILayout.BeginHorizontal();
            for (int c = 0; c < 3; c++)
            {
                if (GUILayout.Button("Rename_" + (r * 3 + c).ToString()))
                {
                    GameObject go = Selection.objects[0] as GameObject;
                    string[] token = go.name.Split('_');
                    int i = int.Parse(token[1]);
                    GameObject root = go;
                    while (root.transform.parent != null)
                        root = root.transform.parent.gameObject;

                    GameObject mask = root.transform.Find("Mask/Mask_" + i.ToString()).gameObject;
                    GameObject hud = root.transform.Find("CanvasHud/Hud_" + i.ToString()).gameObject;

                    mask.name = "Mask_" + (r * 3 + c).ToString();
                    hud.name = "Hud_" + (r * 3 + c).ToString();
                }
            }
            GUILayout.EndHorizontal();
        }
        GUILayout.Space(10);

        if (GUILayout.Button("bind hud"))
        {
            GameObject root = Selection.objects[0] as GameObject;
            if (root)
            {
                BattleHudManager manager = root.GetComponentInChildren<BattleHudManager>();
                Transform mask = manager.transform.parent.Find("Mask");
                Transform HudManager = manager.transform.parent.Find("CanvasHud");

                for (int i = 0; i < HudManager.childCount; ++i)
                {
                    Transform hud = HudManager.GetChild(i);
                    string[] words = hud.name.Split('_');
                    string name = string.Format("Mask_{0}", words[1]);
                    Transform mask_pos = mask.Find(name);
                    hud.position = mask_pos.position;
                }

            }
        }

        if (GUILayout.Button("attach up bar"))
        {
            AttachBar(Selection.objects[0] as GameObject, 0);
        }

        if (GUILayout.Button("attach down bar"))
        {
            AttachBar(Selection.objects[0] as GameObject, 1);
        }

        if (GUILayout.Button("generate layout"))
        {
            GenerateLayout();
        }

        if (GUILayout.Button("generate boss layout"))
        {
            GenerateBossLayout();
        }

        if (GUILayout.Button("generate gj layout"))
        {
            GenerateGJLayout();
        }

        if (GUILayout.Button("Set frame"))
        {
            //Sprite sp = BattleFieldBuilder.LoadFirstSprite("Assets/UI/BattleInUI/xuetiaoBG_aa_R.png");
            foreach (UnityEngine.Object obj in Selection.objects)
            {
                GameObject go = obj as GameObject;
                if (go)
                {
                    Transform hud = go.transform.Find("CanvasHud");
                    for (int i = 0; i < hud.childCount; ++i)
                    {
                        Transform child = hud.GetChild(i);
                        Image image = child.Find("root/level_bg").gameObject.GetComponent<Image>();
                        //image.sprite = sp;
                        image.type = Image.Type.Sliced;
                    }

                    GameObject prefab = PrefabUtility.GetPrefabParent(go) as GameObject;
                    PrefabUtility.ReplacePrefab(go, prefab);
                }
            }
        }

        if (GUILayout.Button("Set frame"))
        {
            int id = 3;
            foreach (BattleTalkingBubble bubble in GameObject.FindObjectsOfType<BattleTalkingBubble>())
            {
                GameObject go = bubble.gameObject;
                DestroyImmediate(bubble);
                BattleStaticBubble sb = go.AddComponent<BattleStaticBubble>();
                sb.bubbleId = id++;
            }
        }

        if(battleLayoutHelpInfo == null)
        {
            battleLayoutHelpInfo = GetLayoutHelpInfo();
        }
        battleLayoutHelpInfo = EditorGUILayout.ObjectField("排除不使用的布局:", battleLayoutHelpInfo, typeof(BattleLayoutHelpInfo), false) as BattleLayoutHelpInfo;

        if(battleLayoutDirectory == null)
        {
            battleLayoutDirectory = GetLayoutDirectory();
        }
        battleLayoutDirectory = EditorGUILayout.ObjectField("战斗布局配置:", battleLayoutDirectory, typeof(BattleLayoutDirectory), false) as BattleLayoutDirectory;
    }

    BattleLayoutHelpInfo GetLayoutHelpInfo()
    {
        string directoryHelpPath = "Assets/Scripts/Editor/Battle/BattleFieldGenerator/directoryHelp.asset";
        BattleLayoutHelpInfo directorHelpAsset = AssetDatabase.LoadAssetAtPath<BattleLayoutHelpInfo>(directoryHelpPath);
        if (directorHelpAsset)
        {
            return directorHelpAsset;
        }
        directorHelpAsset = ScriptableObject.CreateInstance<BattleLayoutHelpInfo>();
        directorHelpAsset.excludeLayout = new List<string>();
        directorHelpAsset.excludeBossLayout = new List<string>();

        AssetDatabase.CreateAsset(directorHelpAsset, directoryHelpPath);

        return directorHelpAsset;
    }

    private void AttachBar(GameObject root, int direction)
    {
        BattleHudManager manager = root.GetComponentInChildren<BattleHudManager>();
        for (int i = 0; i < manager.transform.childCount; ++i)
        {
            GameObject child = manager.transform.GetChild(i).gameObject;
            BattleHud hud = child.GetComponent<BattleHud>();
            if (hud == null)
                hud = child.AddComponent<BattleHud>();

            for (int j = hud.transform.childCount - 1; j >= 0; --j)
                GameObject.Destroy(hud.transform.GetChild(j).gameObject);

            BattleFieldBuilder.CreateHudRoot(hud);
            if (direction != 0)
            {
                Transform buff = hud.transform.Find("root/buff");
                RectTransform rect_buff = buff.gameObject.GetComponent<RectTransform>();
                rect_buff.anchoredPosition = new Vector3(0, 64, 0);

                GridLayoutGroup layout = buff.gameObject.GetComponent<GridLayoutGroup>();
                layout.startCorner = GridLayoutGroup.Corner.LowerLeft;
                layout.childAlignment = TextAnchor.LowerLeft;
            }
        }
    }

   

    private void Modify(GameObject root)
    {
        GameObject hudmgr = root.transform.Find("Canvas/HudManager").gameObject;
        hudmgr.transform.position = Vector3.zero;

        if (hudmgr.GetComponent<RectTransform>() == null)
            hudmgr.AddComponent<RectTransform>();

        for (int i = 0; i < hudmgr.transform.childCount; ++i)
        {
            GameObject obj = hudmgr.transform.GetChild(i).gameObject;
            if (obj.GetComponent<RectTransform>() == null)
                obj.AddComponent<RectTransform>();
        }
    }

    BattleLayoutDirectory GetLayoutDirectory()
    {
        BattleLayoutDirectory dir = AssetDatabase.LoadAssetAtPath<BattleLayoutDirectory>("Assets/Art/Maps/Battle/layout/directoryLayout.asset");
        if (dir)
        {

        }
        else
        {
            dir = ScriptableObject.CreateInstance<BattleLayoutDirectory>();
            dir.directory = "art/maps/battle/layout";
            dir.names = new List<string>();
            dir.bossNames = new List<string>();

            AssetDatabase.CreateAsset(dir, "Assets/Art/Maps/Battle/layout/directoryLayout.asset");
        }
        return dir;
    }

    private void GenerateLayout()
    {
        List<GameObject> downTemplate = new List<GameObject>();
        List<GameObject> upTemplate = new List<GameObject>();

        EditorUtility.DisplayProgressBar("Generating...", "", 0);

        //读取template中的文件
        LoadTemplate(ref downTemplate, ref upTemplate);

        Dictionary<string, int> nameCount = new Dictionary<string, int>();

        BattleLayoutDirectory dir = GetLayoutDirectory();
        dir.names.Clear();

        int count = downTemplate.Count * upTemplate.Count;

        for (int i = 0; i < downTemplate.Count; ++i)
        {
            for (int j = 0; j < upTemplate.Count; ++j)
            {
                GameObject go = MergeTemplate(downTemplate[i], upTemplate[j], nameCount, true);
                EditorUtility.DisplayProgressBar("Generating...", go.name, (float)(i * upTemplate.Count + j) / count);

                if(battleLayoutHelpInfo.excludeLayout.Contains(go.name))
                {
                    //不生成此Prefab,也不记录到 directoryLayout 布局文件中。可以优化为直接不生成 go,即 MergeTemplate 中处理排除的布局，此处牵扯到 nameCount 对物体Prefab
                    //的命名规则，会导致生成的名字发生变化，暂不处理 MergeTemplate 以保持命名不变
                    continue;
                }
                dir.names.Add(go.name);
                string path = "Assets/Art/Maps/Battle/layout/" + go.name + ".prefab";

                GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                if (prefab)
                {
                    prefab = PrefabUtility.ReplacePrefab(go, prefab, ReplacePrefabOptions.ConnectToPrefab);
                }
                else
                {
                    prefab = PrefabUtility.CreatePrefab(path, go);
                }

                var assetPath = AssetDatabase.GetAssetPath(prefab);
                var assetImporter = AssetImporter.GetAtPath(assetPath);
                string assetBundleName = assetPath.Substring(assetPath.IndexOf("/") + 1);
                assetImporter.assetBundleName = assetBundleName;
            }
        }
        dir.defaultName = dir.names[dir.names.Count - 1];
        if(battleLayoutHelpInfo)
        {
            battleLayoutHelpInfo.excludeLayout.Sort();
            battleLayoutHelpInfo.excludeBossLayout.Sort();
            EditorUtility.SetDirty(battleLayoutHelpInfo);
        }
        dir.names.Sort();
        dir.bossNames.Sort();

        EditorUtility.SetDirty(dir);
        AssetDatabase.SaveAssets();
        EditorUtility.ClearProgressBar();
    }

    private void GenerateBossLayout()
    {
        List<GameObject> downTemplate = new List<GameObject>();
        List<GameObject> upTemplate = new List<GameObject>();

        EditorUtility.DisplayProgressBar("Generating Boss Layout...", "", 0);
        LoadBossTemplate(ref downTemplate, ref upTemplate);
        Dictionary<string, int> nameCount = new Dictionary<string, int>();
        BattleLayoutDirectory dir = GetLayoutDirectory();
        dir.bossNames.Clear();

        int count = downTemplate.Count * upTemplate.Count;

        for (int i = 0; i < downTemplate.Count; ++i)
        {
            for (int j = 0; j < upTemplate.Count; ++j)
            {
                GameObject go = MergeTemplate(downTemplate[i], upTemplate[j], nameCount, true);
                EditorUtility.DisplayProgressBar("Generating...", go.name, (float)(i * upTemplate.Count + j) / count);

                if (battleLayoutHelpInfo.excludeBossLayout.Contains(go.name))
                {
                    //排除不使用的 Boss战 战斗布局
                    continue;
                }

                go.transform.Find("Node/6").GetComponent<BattleActorNode>().scaleAttenuation = 0.3f;
                string path = "Assets/Art/Maps/Battle/layout/" + go.name + ".prefab";
                dir.bossNames.Add(go.name);

                GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                if (prefab)
                {
                    prefab = PrefabUtility.ReplacePrefab(go, prefab, ReplacePrefabOptions.ConnectToPrefab);
                }
                else
                {
                    prefab = PrefabUtility.CreatePrefab(path, go);
                }

                var assetPath = AssetDatabase.GetAssetPath(prefab);
                var assetImporter = AssetImporter.GetAtPath(assetPath);
                string assetBundleName = assetPath.Substring(assetPath.IndexOf("/") + 1);
                assetImporter.assetBundleName = assetBundleName;
            }
        }

        if (battleLayoutHelpInfo)
        {
            battleLayoutHelpInfo.excludeLayout.Sort();
            battleLayoutHelpInfo.excludeBossLayout.Sort();
            EditorUtility.SetDirty(battleLayoutHelpInfo);
        }
        dir.names.Sort();
        dir.bossNames.Sort();

        EditorUtility.SetDirty(dir);
        AssetDatabase.SaveAssets();
        EditorUtility.ClearProgressBar();
    }

    private void GenerateGJLayout()
    {
        List<GameObject> downTemplate = new List<GameObject>();
        List<GameObject> upTemplate = new List<GameObject>();

        EditorUtility.DisplayProgressBar("Generating...", "", 0);
        LoadGJTemplate(ref downTemplate, ref upTemplate);
        Dictionary<string, int> nameCount = new Dictionary<string, int>();


        BattleLayoutDirectory dir = AssetDatabase.LoadAssetAtPath<BattleLayoutDirectory>("Assets/Art/Maps/Battle/layoutGJ/directoryLayout.asset");
        if (dir)
        {
        }
        else
        {
            dir = ScriptableObject.CreateInstance<BattleLayoutDirectory>();
            dir.directory = "art/maps/battle/layoutgj";
            dir.names = new List<string>();
            dir.bossNames = new List<string>();

            AssetDatabase.CreateAsset(dir, "Assets/Art/Maps/Battle/layoutgj/directoryLayout.asset");
        }
        dir.names.Clear();

        int count = downTemplate.Count * upTemplate.Count;

        for (int i = 0; i < downTemplate.Count; ++i)
        {
            for (int j = 0; j < upTemplate.Count; ++j)
            {
                GameObject go = MergeTemplate(downTemplate[i], upTemplate[j], nameCount, false);
                BattleComponentHub hub = go.GetComponent<BattleComponentHub>();
                hub.battleCamera.targetTexture = AssetDatabase.LoadAssetAtPath<RenderTexture>("Assets/UI/Battle/battleInWin.renderTexture");

                string path = "Assets/Art/Maps/Battle/layoutGJ/" + go.name + ".prefab";
                dir.names.Add(go.name);
                EditorUtility.DisplayProgressBar("Generating...", go.name, (float)(i * upTemplate.Count + j) / count);

                GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                if (prefab)
                {
                    prefab = PrefabUtility.ReplacePrefab(go, prefab, ReplacePrefabOptions.ConnectToPrefab);
                }
                else
                {
                    prefab = PrefabUtility.CreatePrefab(path, go);
                }

                var assetPath = AssetDatabase.GetAssetPath(prefab);
                var assetImporter = AssetImporter.GetAtPath(assetPath);
                string assetBundleName = assetPath.Substring(assetPath.IndexOf("/") + 1);
                assetImporter.assetBundleName = assetBundleName;
            }
        }
        dir.defaultName = dir.names[dir.names.Count - 1];

        EditorUtility.SetDirty(dir);
        AssetDatabase.SaveAssets();
        EditorUtility.ClearProgressBar();
    }

    /// <summary>
    /// 读取Template的文件
    /// </summary>
    /// <param name="downTemplate"></param>
    /// <param name="upTemplate"></param>
    private void LoadTemplate(ref List<GameObject> downTemplate, ref List<GameObject> upTemplate)
    {
        string path = "Art/Maps/Battle/template";

        string[] files = System.IO.Directory.GetFiles(Application.dataPath + "/" + path, "*.prefab");
        foreach (string file in files)
        {
            string res = "Assets" + file.Replace(Application.dataPath, "").Replace('\\', '/');
            GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(res);
            if (prefab.activeSelf == false)
                continue;

            string name = System.IO.Path.GetFileNameWithoutExtension(file);
            if (name[name.Length-1] == 's')
            {
                upTemplate.Add(prefab);
            }
            else if (name[name.Length - 1] == 'x')
            {
                downTemplate.Add(prefab);
            }
        }
    }

    private void LoadBossTemplate(ref List<GameObject> downTemplate, ref List<GameObject> upTemplate)
    {
        string path_boss = "Art/Maps/Battle/templateBoss";
        string path = "Art/Maps/Battle/template";

        string[] files = System.IO.Directory.GetFiles(Application.dataPath + "/" + path, "*.prefab");
        foreach (string file in files)
        {
            string res = "Assets" + file.Replace(Application.dataPath, "").Replace('\\', '/');
            GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(res);
            if (prefab.activeSelf == false)
                continue;

            string name = System.IO.Path.GetFileNameWithoutExtension(file);
            if (name[name.Length - 1] == 'x')
            {
                downTemplate.Add(prefab);
            }
        }

        files = System.IO.Directory.GetFiles(Application.dataPath + "/" + path_boss, "*.prefab");
        foreach (string file in files)
        {
            string res = "Assets" + file.Replace(Application.dataPath, "").Replace('\\', '/');
            GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(res);
            if (prefab.activeSelf == false)
                continue;
            //string name = System.IO.Path.GetFileNameWithoutExtension(file);
            upTemplate.Add(prefab);
        }
    }

    private void LoadGJTemplate(ref List<GameObject> downTemplate, ref List<GameObject> upTemplate)
    {
        string path = "Art/Maps/Battle/templateGJ";

        string[] files = System.IO.Directory.GetFiles(Application.dataPath + "/" + path, "*.prefab");
        foreach (string file in files)
        {
            string res = "Assets" + file.Replace(Application.dataPath, "").Replace('\\', '/');
            GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(res);
            if (prefab.activeSelf == false)
                continue;

            string name = System.IO.Path.GetFileNameWithoutExtension(file);
            if (name[name.Length - 1] == 's')
            {
                upTemplate.Add(prefab);
            }
            else if (name[name.Length - 1] == 'x')
            {
                downTemplate.Add(prefab);
            }
        }
    }

    /// <summary>
    /// 构建战斗布局
    /// </summary>
    /// <param name="down"></param>
    /// <param name="up"></param>
    /// <param name="nameCount"></param>
    /// <param name="hasInput"></param>
    /// <returns></returns>
    private GameObject MergeTemplate(GameObject down, GameObject up, Dictionary<string, int> nameCount, bool hasInput)
    {
        GameObject downMask = down.transform.Find("Mask").gameObject;
        GameObject upMask = up.transform.Find("Mask").gameObject;

        GameObject downNode = down.transform.Find("Node").gameObject;
        GameObject upNode = up.transform.Find("Node").gameObject;

        Transform downHud = down.transform.Find("CanvasHud");
        Transform upHud = up.transform.Find("CanvasHud");

        BattleComponentHub hub = BattleFieldBuilder.BuildManager("root");
        Camera camera = BattleFieldBuilder.BuildCamera(hub.gameObject);
        List<GameObject> masks = BattleFieldBuilder.BuildMask(hub.gameObject, downMask, upMask);
        BattleFieldBuilder.BuildBackgroundNoneMask(hub.gameObject);
        NodeSnapshotManager nodeSnapshotManager = BattleFieldBuilder.BuildNodeSnapshotManager(hub, camera);
        BattleActorManager actorManager = BattleFieldBuilder.BuildNode(hub.gameObject, masks, downNode, upNode);
        //BattleFieldBuilder.AssignMask(masks, actorManager.nodeList);
        BattleHudManager hudManager = null;
        if (downHud && upHud)
            hudManager = BattleFieldBuilder.BuildHud(hub.gameObject, camera, actorManager.nodeList, downHud.gameObject, upHud.gameObject);
        BattleTalkingBubbleManager bubbleManager = BattleFieldBuilder.BuildTalkingBubble(hub.gameObject, camera, down, up);
        BattleFloatTextManager textManager = BattleFieldBuilder.BuildFloatText(hub.gameObject, camera);
        BattleOutsideSkillManager battleOutsideSkillManager = BattleFieldBuilder.BuildOutsideSkillManager(hub.gameObject, camera);
        BattleFieldBuilder.BuildAnchor(hub.gameObject, masks, actorManager.nodeList, actorManager);
        BattleJumpoutManager jumpoutManager = BattleFieldBuilder.BuildJumpout(hub.gameObject, actorManager.nodeList);
        BattleFxManager fxManager = BattleFieldBuilder.BuildFxManager(hub.gameObject);
        BattleFieldBuilder.BuildLayout(hub.gameObject, actorManager, down.GetComponent<BattleLayoutDescriptor>(), up.GetComponent<BattleLayoutDescriptor>(), nameCount);
        BattleWeaponManager weaponManager = BattleFieldBuilder.BuildWeaponManager(hub.gameObject);
        BattleFieldBuilder.AssignMesh(actorManager, down, up);
        BattleFieldBuilder.AssigBubble(actorManager, down, up, bubbleManager);

        //Light light = BattleFieldBuilder.BuildLight(hub.gameObject);
        if (hasInput)
        {
            BattleInput input = BattleFieldBuilder.BuildInput(hub.gameObject);
            hub.input = input;
            input.hub = hub;
        }
        hub.battleCamera = camera;
        hub.actorManager = actorManager;
        hub.textManager = textManager;
        hub.hudManager = hudManager;
        hub.bubbleManager = bubbleManager;
        hub.jumpoutManager = jumpoutManager;
        hub.fxManager = fxManager;
        hub.weaponManager = weaponManager;
        hub.outsideManager = battleOutsideSkillManager;
        //hub.battleLight = light;

        return hub.gameObject;
    }
}

