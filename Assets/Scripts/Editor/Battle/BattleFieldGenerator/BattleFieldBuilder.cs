using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using UnityEngine.UI;
using UnityEngine.PostProcessing;
using UnityEngine.Playables;
using UnityEngine.Audio;
using War.Battle;
using War.UI;
using System.Linq;
using Cinemachine;

public class BattleFieldBuilder
{

    #region mask
    public static List<GameObject> BuildMask(GameObject root, GameObject downMask, GameObject upMask)
    {
        GameObject go = new GameObject("Mask");
        go.transform.SetParent(root.transform);
        go.transform.localPosition = Vector3.zero;
        go.transform.localRotation = Quaternion.Euler(0, 180, 0);
        go.transform.localScale = Vector3.one;

        List<GameObject> maskList = new List<GameObject>() { null, null, null, null, null, null, null, null, null, null, null, null, null };
        for (int i = 0; i < downMask.transform.childCount; ++i)
        {
            GameObject maskGoPrefab = downMask.transform.GetChild(i).gameObject;
            GameObject maskGo = GameObject.Instantiate(maskGoPrefab, go.transform);
            maskGo.name = maskGoPrefab.name;
            //Debug.Log($"maskGoPrefab:{maskGoPrefab.transform.position},maskGo:{maskGo.transform.position}");
            string[] token = maskGo.name.Split('_');
            maskList[int.Parse(token[1])] = maskGo;
        }

        for (int i = 0; i < upMask.transform.childCount; ++i)
        {
            GameObject maskGoPrefab = upMask.transform.GetChild(i).gameObject;
            GameObject maskGo = GameObject.Instantiate(maskGoPrefab, go.transform);
            maskGo.name = maskGoPrefab.name;

            string[] token = maskGo.name.Split('_');
            maskList[int.Parse(token[1])] = maskGo;
        }

        return maskList;
    }

    //public static void AssignMask(List<GameObject> masks, List<BattleActorNode> nodes)
    //{
    //    int maskID = 0;
    //    for (int i = 0; i < 12; ++i)
    //    {
    //        if (nodes[i] != null)
    //        {
    //            int id = (maskID++ % 6);
    //            nodes[i].MaskID = id;
    //            Renderer render = nodes[i].Mask.GetComponent<MeshRenderer>();
    //            string name = string.Format("Assets/Art/Maps/Battle/common/BlackFogMask_{0}.mat", id);
    //            render.sharedMaterial = AssetDatabase.LoadAssetAtPath<Material>(name);

    //            Renderer frame = nodes[i].Mask.transform.GetChild(0).GetComponent<MeshRenderer>();
    //            string frameMat = string.Format("Assets/Art/Maps/Battle/common/BlackFrame_{0}.mat", id);
    //            frame.sharedMaterial = AssetDatabase.LoadAssetAtPath<Material>(frameMat);
    //        }
    //    }
    //}

    public static void AssignMesh(BattleActorManager manager, GameObject down, GameObject up)
    {
        List<BattleActorNode> nodes = manager.nodeList;
        //for (int i = 0; i < 6; ++i)
        //{
        //    BattleActorNode node = nodes[i];
        //    if (node != null)
        //    {
                //Transform mask = down.transform.Find(string.Format("Mask/Mask_{0}", node.name));
                //if (mask)
                //    node.gridMesh = mask.GetComponent<MeshFilter>().sharedMesh;

                //Transform edge = down.transform.Find(string.Format("Edge/Edge_{0}", node.name));
                //if (edge)
                //    node.edgeMesh = edge.GetComponent<MeshFilter>().sharedMesh;
           
        //    }
        //}

        for (int i = 6; i < 12; ++i)
        {
            BattleActorNode node = nodes[i];
            if (node != null)
            {
                Transform mask = up.transform.Find(string.Format("Mask/Mask_{0}", node.name));
                //if (mask)
                //    node.gridMesh = mask.GetComponent<MeshFilter>().sharedMesh;

                //Transform edge = up.transform.Find(string.Format("Edge/Edge_{0}", node.name));
                //if (edge)
                //    node.edgeMesh = edge.GetComponent<MeshFilter>().sharedMesh;
            }
        }
    }

    public static void AssigBubble(BattleActorManager manager, GameObject down, GameObject up, BattleTalkingBubbleManager bubbleManager)
    {
        List<BattleActorNode> nodes = manager.nodeList;
        for (int i = 0; i < 6; ++i)
        {
            BattleActorNode node = nodes[i];
            if (node != null)
            {
                Transform mask = down.transform.Find(string.Format("Node/{0}", node.name));
                if (mask)
                    node.bubbleStyle = mask.GetComponent<BattleActorNode>().bubbleStyle;
            }
        }

        for (int i = 6; i < 12; ++i)
        {
            BattleActorNode node = nodes[i];
            if (node != null)
            {
                Transform mask = up.transform.Find(string.Format("Node/{0}", node.name));
                if (mask)
                    node.bubbleStyle = mask.GetComponent<BattleActorNode>().bubbleStyle;
            }
        }

        string path = "Assets/Art/Maps/Battle/talkingBubble/BubbleCollection.asset";
        bubbleManager.collection = AssetDatabase.LoadAssetAtPath<BattleTalkingBubbleCollection>(path);
    }

    #endregion

    #region background
    public static void BuildBackgroundNoneMask(GameObject root)
    {
        GameObject go = new GameObject("Background");
        go.transform.SetParent(root.transform, false);
        go.transform.localPosition = new Vector3(0,-4, 5.2f);
        go.transform.localRotation = Quaternion.Euler(30, 0, 0);
        go.transform.localScale = Vector3.one;
    }
    #endregion

    public static Sprite LoadFirstSprite(string path)
    {
        var objects = UnityEditor.AssetDatabase.LoadAllAssetsAtPath(path);
        var sprites = objects.Where(q => q is Sprite).Cast<Sprite>();
        return sprites.First();
    }

    #region node
    public static BattleActorManager BuildNode(GameObject root, List<GameObject> masks, GameObject downNode, GameObject upNode)
    {
        GameObject go = new GameObject("Node");
        go.transform.SetParent(root.transform);
        go.transform.localPosition = Vector3.zero;
        go.transform.localRotation = Quaternion.identity;
        go.transform.localScale = Vector3.one;

        BattleActorManager manager = go.AddComponent<BattleActorManager>();
        manager.backgrounds = AssetDatabase.LoadAssetAtPath<PrefabAssets>("Assets/Art/Maps/Battle/rayBg/background.asset");

        List<BattleActorNode> nodes = new List<BattleActorNode>();
        for (int i = 0; i < 12; i++)
        {
            nodes.Add(null);
        }

        Vector3 toward = new Vector3(0, 1, 0);
        for (int i = 0; i < 6; ++i)
        {
            nodes[i] = BuildSingleNode(go, i, manager, masks, toward, downNode);
        }

        toward = new Vector3(0, -1, 0);
        for (int i = 6; i < 12; ++i)
        {
            nodes[i] = BuildSingleNode(go, i, manager, masks, toward, upNode);
        }

        manager.nodeList = nodes;
        return manager;
    }

    public static NodeSnapshotManager BuildNodeSnapshotManager(BattleComponentHub hub, Camera battleCamera)
    {
        Transform parentTrans = hub.gameObject.transform;

        Transform rootTrans;
        rootTrans = parentTrans.Find("NodeSnapshotRoot");
        if (rootTrans)
        {
            return rootTrans.GetComponent<NodeSnapshotManager>();
        }
        GameObject go = new GameObject("NodeSnapshotRoot");
        go.transform.SetParent(parentTrans);
        go.transform.localPosition = Vector3.zero;
        go.transform.localScale = Vector3.one;

        rootTrans = go.transform;

        Camera snapshotCamera;
        Transform cameraTrans = rootTrans.Find("SnapshotCamera");
        if(cameraTrans)
        {
            snapshotCamera = cameraTrans.GetComponent<Camera>();
        }
        else
        {
            GameObject cameraObj = new GameObject("SnapshotCamera");
            cameraObj.transform.SetParent(rootTrans, false);
            cameraObj.transform.position += new Vector3(0, 0, -10);

            snapshotCamera = cameraObj.AddComponent<Camera>();
            snapshotCamera.enabled = false; //默认不渲染，需主动调用
            snapshotCamera.cullingMask = LayerMask.GetMask("Snapshot");
            snapshotCamera.orthographic = true;
            snapshotCamera.orthographicSize = battleCamera.orthographicSize;
            snapshotCamera.nearClipPlane = battleCamera.nearClipPlane;
            snapshotCamera.farClipPlane = battleCamera.farClipPlane;
            snapshotCamera.renderingPath = battleCamera.renderingPath;
            snapshotCamera.useOcclusionCulling = battleCamera.useOcclusionCulling;
            //snapshotCamera.allowHDR = battleCamera.allowHDR; //渲染RT alpha数据异常
            snapshotCamera.allowHDR = false;
            snapshotCamera.allowMSAA = battleCamera.allowMSAA;
            snapshotCamera.allowDynamicResolution = battleCamera.allowDynamicResolution;
            snapshotCamera.clearFlags = CameraClearFlags.SolidColor;
            snapshotCamera.backgroundColor = new Color(0, 0, 0, 0);
        }

        NodeSnapshotManager snapshotManager = go.AddComponent<NodeSnapshotManager>();
        snapshotManager.snapshotCamera = snapshotCamera;
        snapshotManager.MaxTextureSize = 512;
        snapshotManager.unit2pixelScale = 100;
        snapshotManager.cameraPosOffset = new Vector3(0, 0, -10);
        snapshotManager.displayPosOffset = new Vector3(0, 0, -5);

        string materialName = string.Format("Assets/Art/Maps/Battle/common/UISpriteLightFrame.mat");
        snapshotManager.displayMaterail = AssetDatabase.LoadAssetAtPath<Material>(materialName);

        go.gameObject.SetActive(false);
        return snapshotManager;
    }

    public static BattleActorNode BuildSingleNode(GameObject go, int index, BattleActorManager manager, List<GameObject> masks, Vector3 toward, GameObject templateNode)
    {
        if (masks[index] != null)
        {
            GameObject node = new GameObject(index.ToString());
            node.transform.SetParent(go.transform);
            node.transform.position = masks[index].transform.position;

            if (index > 5)
            {
                node.transform.localRotation = Quaternion.Euler(0, 180, 0);
            }
            else
            {
                node.transform.localRotation = Quaternion.Euler(0, 0, 0);
            }

            node.AddComponent<PlayableDirector>();

            AudioSource audio = node.AddComponent<AudioSource>();
            SetAudioSource(audio);

            BattleActorNode battleNode = node.AddComponent<BattleActorNode>();
            battleNode.manager = manager;
            //battleNode.Mask = masks[index];

            var templateActorNode = templateNode.transform.Find(index.ToString()).GetComponent<BattleActorNode>();
            //battleNode.defaultActorMoveAttackId = templateActorNode.defaultActorMoveAttackId;
            //battleNode.actorMoveAttackCfg = templateActorNode.actorMoveAttackCfg;

            //var maskRendererSet = battleNode.Mask.transform.GetComponentsInChildren<MeshRenderer>();
            //foreach(var renderer in maskRendererSet)
            //{
            //    if(renderer.gameObject != battleNode.Mask && renderer.gameObject.name.Contains("Frame_"))
            //    {
            //        battleNode.Frame = renderer.gameObject;
            //        break;
            //    }
            //}

            //battleNode.showLayer = LayerMask.GetMask("BattleActorModel", "BattleActorModelBg");
            //battleNode.maskedLayer = -1;

            //Renderer frameRenderer = battleNode.Frame.GetComponent<MeshRenderer>();
            //battleNode.nodeCenter = frameRenderer.bounds.center;
            //battleNode.nodeSize = frameRenderer.bounds.size;

            BattleComponentHub hub = node.GetComponentInParent<BattleComponentHub>();
            //battleNode.snapshotManager = hub.GetComponentInChildren<NodeSnapshotManager>();
            battleNode.torward = toward;

            return battleNode;
        }
        else
            return null;
    }

    public static void SetAudioSource(AudioSource audio)
    {
        //AudioMixer mixer = AssetDatabase.LoadAssetAtPath<AudioMixer>("Assets/SoundMixer/MainMixer.mixer");
        //audio.outputAudioMixerGroup = mixer.FindMatchingGroups("Master/SFX")[0];
    }

    #endregion

    #region manager
    public static BattleComponentHub BuildManager(string name)
    {
        GameObject root = new GameObject(name);
        Animator animator = root.AddComponent<Animator>();
        animator.runtimeAnimatorController = AssetDatabase.LoadAssetAtPath<
            RuntimeAnimatorController>("Assets/Art/Maps/Battle/animation/battle.controller");

        return root.AddComponent<BattleComponentHub>();
    }


    #endregion




    #region floatText
    public static BattleFloatTextManager BuildFloatText(GameObject root, Camera camera)
    {
        GameObject go = new GameObject("CanvasFloatText");
        go.transform.SetParent(root.transform, false);
        RectTransform rect = go.AddComponent<RectTransform>();
        rect.sizeDelta = new Vector2(720, 1280);
        rect.localScale = new Vector3(0.01f, 0.01f, 0.01f);
        go.transform.localPosition = Vector3.zero;
        go.transform.localRotation = Quaternion.identity;
        go.transform.localScale = Vector3.one;

        Canvas canvas = go.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.ScreenSpaceCamera;
        canvas.worldCamera = camera;
        canvas.planeDistance = 5f;
        canvas.sortingLayerName = "FloatText";

        CanvasScaler scaler = go.AddComponent<CanvasScaler>();
        scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
        scaler.referenceResolution = new Vector2(720, 1280);
        scaler.screenMatchMode = CanvasScaler.ScreenMatchMode.Expand;

        BattleFloatTextManager ft = go.AddComponent<BattleFloatTextManager>();
        ft.styles = AssetDatabase.LoadAssetAtPath<BattleFloatTextStyleCollection>("Assets/UI/Battle/style_collection.asset");
        ft.identifySet = AssetDatabase.LoadAssetAtPath<BattleAssetIdentifyCollection>("Assets/UI/Battle/battle_identifying_collection.asset");

        BuildFloatTextTemplate(go);

        return ft;
    }

    public static void BuildFloatTextTemplate(GameObject go)
    {
        GameObject floatText = new GameObject("FloatText");
        floatText.transform.SetParent(go.transform, false);
        floatText.AddComponent<RectTransform>();
        floatText.transform.localScale = Vector3.one;

        GameObject tween = new GameObject("Tween");
        tween.transform.SetParent(floatText.transform, false);
        tween.transform.localScale = Vector3.one;
        tween.AddComponent<RectTransform>();
        Animator animator = tween.AddComponent<Animator>();
        animator.enabled = false;

        HorizontalLayoutGroup horizontalLayoutGroup = tween.AddComponent<HorizontalLayoutGroup>();
        horizontalLayoutGroup.spacing = 5;
        horizontalLayoutGroup.childAlignment = TextAnchor.MiddleCenter;
        horizontalLayoutGroup.childControlWidth = true;
        horizontalLayoutGroup.childControlHeight = true;
        horizontalLayoutGroup.childForceExpandWidth = false;
        horizontalLayoutGroup.childForceExpandHeight = false;

        ContentSizeFitter contentSizeFitter = tween.AddComponent<ContentSizeFitter>();
        contentSizeFitter.horizontalFit = ContentSizeFitter.FitMode.PreferredSize;
        contentSizeFitter.verticalFit = ContentSizeFitter.FitMode.PreferredSize;

        GameObject imgTypeObj = new GameObject("imgType");
        imgTypeObj.transform.SetParent(tween.transform, false);
        imgTypeObj.transform.localScale = Vector3.one;
        RectTransform rect = imgTypeObj.AddComponent<RectTransform>();
        rect.sizeDelta = new Vector2(46, 46);

        Image imgType = imgTypeObj.AddComponent<Image>();
        imgType.enabled = false;

        GameObject text = new GameObject("Text");
        text.transform.SetParent(tween.transform, false);
        text.transform.localScale = Vector3.one;

        rect = text.AddComponent<RectTransform>();
        rect.sizeDelta = new Vector2(320, 100);

        Text t = text.AddComponent<Text>();
        t.font = AssetDatabase.LoadAssetAtPath<Font>("Assets/UI/Battle/white.fontsettings");
        t.alignment = TextAnchor.MiddleCenter;
        t.raycastTarget = false;

        //Outline outline = text.AddComponent<Outline>();
        //outline.enabled = false;

        //War.UI.Gradient gradient = text.AddComponent<War.UI.Gradient>();
        //gradient.TopColor = Color.white;
        //gradient.BottomColor = Color.white;
        //gradient.enabled = false;

        floatText.SetActive(false);
    }
    #endregion

    #region hud

    public static BattleHudManager BuildHud(GameObject root, Camera camera, List<BattleActorNode> nodes, GameObject downHud, GameObject upHud)
    {
        BattleHudManager hudManager = BuildHudManager(root, camera);

        for (int i = 0; i < downHud.transform.childCount; ++i)
        {
            GameObject maskGoPrefab = downHud.transform.GetChild(i).gameObject;
            GameObject maskGo = GameObject.Instantiate(maskGoPrefab, hudManager.transform);
            maskGo.name = maskGoPrefab.name;

            string[] token = maskGo.name.Split('_');
            nodes[int.Parse(token[1])].Hud = maskGo.GetComponent<BattleHud>();

        }

        for (int i = 0; i < upHud.transform.childCount; ++i)
        {
            GameObject maskGoPrefab = upHud.transform.GetChild(i).gameObject;
            GameObject maskGo = GameObject.Instantiate(maskGoPrefab, hudManager.transform);
            maskGo.name = maskGoPrefab.name;

            string[] token = maskGo.name.Split('_');
            nodes[int.Parse(token[1])].Hud = maskGo.GetComponent<BattleHud>();
        }

        return hudManager;
    }

    public static BattleHudManager BuildHud(GameObject root, Camera camera, int count)
    {
        BattleHudManager hudManager = BuildHudManager(root, camera);

        for (int i = 0; i < count; i++)
        {
            GameObject hudGo = new GameObject("Hud_" + i.ToString());
            hudGo.transform.SetParent(hudManager.transform, false);
            Vector3 pos = hudGo.transform.localPosition;
            pos.z = 0;
            hudGo.transform.localPosition = pos;
            hudGo.AddComponent<RectTransform>();
            hudGo.transform.localScale = Vector3.one;
        }

        return hudManager;
    }

    public static BattleHudManager BuildHudManager(GameObject root, Camera camera)
    {
        GameObject go = new GameObject("CanvasHud");
        go.transform.SetParent(root.transform, false);
        RectTransform rect = go.AddComponent<RectTransform>();
        go.transform.localPosition = Vector3.zero;
        go.transform.localRotation = Quaternion.identity;
        go.transform.localScale = Vector3.one;

        Canvas canvas = go.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.WorldSpace;
        canvas.worldCamera = camera;
        canvas.planeDistance = 10;
        canvas.sortingLayerName = "Hud";


        rect.sizeDelta = new Vector2(720, 1280);
        rect.localScale = new Vector3(0.01f, 0.01f, 0.01f);
        rect.localRotation = Quaternion.Euler(90, 0, 0);
        rect.localPosition = new Vector3(0, 1.7f, -2);

        CanvasScaler scaler = go.AddComponent<CanvasScaler>();
        scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
        scaler.referenceResolution = new Vector2(720, 1280);
        scaler.screenMatchMode = CanvasScaler.ScreenMatchMode.Expand;

        BattleHudManager hudManager = go.AddComponent<BattleHudManager>();
        hudManager.style = AssetDatabase.LoadAssetAtPath<BattleHudStyle>("Assets/Art/Maps/Battle/config/hudStyle.asset");
        return hudManager;
    }

    public static void CreateHudRoot(BattleHud hud)
    {
        GameObject root = new GameObject("root");
        hud.root = AppendRectTransform(root, hud.transform, Vector3.zero, new Vector2(107, 23));

        GameObject hp_bg = new GameObject("hp_bg");
        AppendRectTransform(hp_bg, root.transform, Vector3.zero, new Vector2(101, 16));
        hud.mp_bg = hud.hp_bg = AppendImage(hp_bg, "Assets/Art/Maps/Battle/ui/xuetiao_di_2.png", Image.Type.Simple);

        GameObject hp_di = new GameObject("hp_di");
        AppendRectTransform(hp_di, hp_bg.transform, new Vector3(0, 3, 0), new Vector2(101, 10));
        hud.hp_di = AppendImage(hp_di, "Assets/Art/Maps/Battle/ui/xuetiao_di.png", Image.Type.Filled);

        GameObject hp = new GameObject("hp");
        AppendRectTransform(hp, hp_bg.transform, new Vector3(0, 3, 0), new Vector2(101, 10));
        hud.hp_bar = AppendImage(hp, "Assets/Art/Maps/Battle/ui/xuetiao.png", Image.Type.Filled);

        GameObject mp = new GameObject("mp");
        AppendRectTransform(mp, hp_bg.transform, new Vector3(0, -6, 0), new Vector2(101, 4));
        hud.mp_bar = AppendImage(mp, "Assets/Art/Maps/Battle/ui/nengliangtiao.png", Image.Type.Filled);

        GameObject level_bg = new GameObject("level_bg");
        AppendRectTransform(level_bg, root.transform, new Vector3(300, 0, 0), new Vector2(35, 18));
        AppendImage(level_bg, Color.black);

        GameObject level = new GameObject("level");
        AppendRectTransform(level, level_bg.transform, Vector3.zero, new Vector2(40, 25));
        hud.level = AppendText(level, 18);

        GameObject buff = new GameObject("buff");
        hud.buffList = AppendRectTransform(buff, root.transform, new Vector3(0, -62, 0), new Vector2(101, 101));
        AppendGridLayoutGroup(buff, GridLayoutGroup.Corner.UpperLeft, TextAnchor.UpperLeft);
    }

    private static RectTransform AppendRectTransform(GameObject go, Transform parent, Vector3 pos, Vector2 size)
    {
        RectTransform rt = go.AddComponent<RectTransform>();
        rt.SetParent(parent, false);
        rt.anchoredPosition = pos;
        rt.sizeDelta = size;
        return rt;
    }

    private static Image AppendImage(GameObject go, string res, Image.Type  type)
    {
        Image image = go.AddComponent<Image>();
        image.sprite = LoadFirstSprite(res);
        image.raycastTarget = false;
        image.type = type;

        if (type == Image.Type.Filled)
            image.fillMethod = Image.FillMethod.Horizontal;
        return image;
    }

    private static Image AppendImage(GameObject go, Color color)
    {
        Image image = go.AddComponent<Image>();
        image.color = color;
        image.raycastTarget = false;
        return image;
    }

    private static Text AppendText(GameObject go, int size)
    {
        Text text = go.AddComponent<Text>();
        text.text = "555";
        text.font = AssetDatabase.LoadAssetAtPath<Font>("Assets/UI/Fonts/FangZhengZongYi_GBK.TTF");
        text.fontSize = size;
        text.alignment = TextAnchor.MiddleCenter;
        text.raycastTarget = false;
        return text;
    }

    private static GridLayoutGroup AppendGridLayoutGroup(GameObject go, GridLayoutGroup.Corner corner, TextAnchor anchor)
    {
        GridLayoutGroup layout = go.AddComponent<GridLayoutGroup>();
        layout.cellSize = new Vector2(22, 22);
        layout.spacing = Vector2.zero;
        layout.startCorner = corner;
        layout.childAlignment = anchor;
        layout.constraint = GridLayoutGroup.Constraint.Flexible;
        layout.startAxis = GridLayoutGroup.Axis.Vertical;
        return layout;
    }

    #endregion

    #region anchor
    public static void BuildAnchor(GameObject root, List<GameObject> masks, List<BattleActorNode> nodes, BattleActorManager actorManager)
    {
        GameObject go = new GameObject("Anchor");
        go.transform.SetParent(root.transform);
        go.transform.localPosition = Vector3.zero;
        go.transform.localRotation = Quaternion.identity;
        go.transform.localScale = Vector3.one;

        Transform center = BuildSingleAnchor(go, "center", masks, null);
        Transform areaDown = BuildSingleAnchor(go, "areaDown", masks.GetRange(0, 6), null);
        Transform areaUp = BuildSingleAnchor(go, "areaUp", masks.GetRange(6, 6), null);
        Transform rowDownBack = BuildSingleAnchor(go, "rowDownBack", masks.GetRange(0, 3), areaDown);
        Transform rowDownFront = BuildSingleAnchor(go, "rowDownFront", masks.GetRange(3, 3), areaDown);
        Transform rowUpFront = BuildSingleAnchor(go, "rowUpFront", masks.GetRange(6, 3), areaUp);
        Transform rowUpBack = BuildSingleAnchor(go, "rowUpBack", masks.GetRange(9, 3), areaUp);

        SetNodeAnchor(nodes[0], rowDownBack, areaDown);
        SetNodeAnchor(nodes[1], rowDownBack, areaDown);
        SetNodeAnchor(nodes[2], rowDownBack, areaDown);
        SetNodeAnchor(nodes[3], rowDownFront, areaDown);
        SetNodeAnchor(nodes[4], rowDownFront, areaDown);
        SetNodeAnchor(nodes[5], rowDownFront, areaDown);
        SetNodeAnchor(nodes[6], rowUpFront, areaUp);
        SetNodeAnchor(nodes[7], rowUpFront, areaUp);
        SetNodeAnchor(nodes[8], rowUpFront, areaUp);
        SetNodeAnchor(nodes[9], rowUpBack, areaUp);
        SetNodeAnchor(nodes[10], rowUpBack, areaUp);
        SetNodeAnchor(nodes[11], rowUpBack, areaUp);

        actorManager.center = center;
    }

    static void SetNodeAnchor(BattleActorNode node, Transform row, Transform area)
    {
        if (node)
        {
            node.row = row;
            node.area = area;
        }
    }


    static Transform BuildSingleAnchor(GameObject go, string name, List<GameObject> masks, Transform revert)
    {
        if (masks[0] == null)
            return revert;

        Bounds b = new Bounds();
        {
            MeshRenderer renderer = masks[0].GetComponent<MeshRenderer>();
            b = renderer.bounds;
        }

        for (int i = 1; i < masks.Count; ++i)
        {
            if (masks[i] != null)
            {
                MeshRenderer renderer = masks[i].GetComponent<MeshRenderer>();
                b.Encapsulate(renderer.bounds);
            }
        }

        GameObject anchor = new GameObject(name);
        anchor.transform.SetParent(go.transform);
        anchor.transform.position = b.center;

        return anchor.transform;
    }
    #endregion

    #region layout
    public static void BuildLayout(GameObject root, BattleActorManager manager, BattleLayoutDescriptor down, BattleLayoutDescriptor up, Dictionary<string, int> nameCount)
    {
        BattleLayoutDescriptor descriptor = manager.GetComponent<BattleLayoutDescriptor>();
        if (descriptor == null)
            descriptor = manager.gameObject.AddComponent<BattleLayoutDescriptor>();

        descriptor.nodeMap = new int[12];
        descriptor.descriptor = down.descriptor + "_" +  up.descriptor;

        for (int i = 0; i < 6; ++i)
            descriptor.nodeMap[i] = down.nodeMap[i];

        for (int i = 6; i < 12; ++i)
            descriptor.nodeMap[i] = up.nodeMap[i];

        int downCount = down.GetMaxSlot();
        int upCount = up.GetMaxSlot();

        string name = string.Format("{0}v{1}_{2}_{3}", downCount, upCount, down.descriptor, up.descriptor);
        int count = 0;
        if (nameCount.TryGetValue(name, out count))
        {
            nameCount[name] = count + 1;
            name += "_" + count.ToString();
        }
        else
        {
            nameCount.Add(name, 1);
            name += "_" + count.ToString();
        }
        root.name = name;

        for (int i = 0; i <  manager.nodeList.Count; ++i)
        {
            BattleActorNode node = manager.nodeList[i];
            if (node)
                node.order = 11 - i;
        }
    }

    #endregion

    #region jumpout
    public static BattleJumpoutManager BuildJumpout(GameObject root, List<BattleActorNode> nodes)
    {
        GameObject go = new GameObject("Jumpout");
        go.transform.SetParent(root.transform);
        go.transform.localPosition = Vector3.zero;
        go.transform.localRotation = Quaternion.identity;
        go.transform.localScale = Vector3.one;

        BattleJumpoutManager manager = go.AddComponent<BattleJumpoutManager>();
        manager.upper = new Bounds(new Vector3(0.38f, 4.58f, 0), new Vector3(3.98f, 2.75f, 2));
        manager.lower = new Bounds(new Vector3(-0.38f, -4.58f, 0), new Vector3(3.98f, 2.75f, 2));
        return manager;
    }
    #endregion

    #region camera
    public static Camera BuildCamera(GameObject root)
    {
        GameObject go = new GameObject("Camera");
        go.transform.SetParent(root.transform);
        go.transform.localPosition = new Vector3(0, 11, -20);
        go.transform.localRotation = Quaternion.Euler(30,0,0);
        go.transform.localScale = Vector3.one;

        Camera camera = go.AddComponent<Camera>();
        camera.clearFlags = CameraClearFlags.SolidColor;
        camera.backgroundColor = new Color(0, 0, 0, 0);
        camera.cullingMask = LayerMask.GetMask("Default", "TransparentFX", "Ignore Raycast", "Water", "BattleActorEffectStatic",
            "BattleActorEffectBg", "BattleActorEffect", "BattleActorModelBg", "BattleActorModel");
        camera.orthographic = false;
        camera.fieldOfView = 40;
        //camera.orthographicSize = 6.4f;
        camera.nearClipPlane = 1;
        camera.farClipPlane = 50;
        camera.depth = 10;
        camera.useOcclusionCulling = false;
        camera.allowMSAA = false;

        PostProcessingBehaviour post = go.AddComponent<PostProcessingBehaviour>();
        post.profile = AssetDatabase.LoadAssetAtPath<PostProcessingProfile>("Assets/Art/Effects/DefaultPostProcessingProfile.asset");


        CinemachineBrain beain = go.AddComponent<CinemachineBrain>();
        return camera;
    }
    #endregion

    #region light
    public static Light BuildLight(GameObject root)
    {
        GameObject go = new GameObject("DirectLight");
        go.transform.SetParent(root.transform);
        go.transform.localPosition = Vector3.zero;
        go.transform.localRotation = Quaternion.Euler(30, 0, 0);
        go.transform.localScale = Vector3.one;

        Light light = go.AddComponent<Light>();
        light.type = LightType.Directional;
        return light;
    }
    #endregion

    #region talking bubble
    public static BattleTalkingBubbleManager BuildTalkingBubble(GameObject root, Camera camera, GameObject down, GameObject up)
    {
        GameObject go = new GameObject("CanvasTalkingBubble");
        go.transform.SetParent(root.transform, false);
        RectTransform rect = go.AddComponent<RectTransform>();
        rect.sizeDelta = new Vector2(720, 1280);
        rect.localScale = new Vector3(0.01f, 0.01f, 0.01f);
        go.transform.localPosition = Vector3.zero;
        go.transform.localRotation = Quaternion.identity;
        go.transform.localScale = Vector3.one;

        Canvas canvas = go.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.ScreenSpaceCamera;
        canvas.worldCamera = camera;
        canvas.planeDistance = 4f;
        canvas.sortingLayerName = "Bubble";

        CanvasScaler scaler = go.AddComponent<CanvasScaler>();
        scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
        scaler.referenceResolution = new Vector2(720, 1280);
        scaler.screenMatchMode = CanvasScaler.ScreenMatchMode.Expand;

        BattleTalkingBubbleManager manager = go.AddComponent<BattleTalkingBubbleManager>();
        manager.style = AssetDatabase.LoadAssetAtPath<BattleTalkingBubbleStyle>("Assets/Art/Maps/Battle/config/bubbleStyle.asset");
        //manager.staticBubbles = new List<BattleStaticBubble>();

        //BattleStaticBubble[] downBubbles = down.GetComponentsInChildren<BattleStaticBubble>();
        //foreach (BattleStaticBubble bubble in downBubbles)
        //{
        //    GameObject x = GameObject.Instantiate(bubble.gameObject, go.transform);
        //    //x.name = "bubble";
        //    x.name = x.name.Replace("(Clone)", "");
        //    manager.staticBubbles.Add(x.GetComponent<BattleStaticBubble>());
        //}
        //BattleStaticBubble[] upBubbles = up.GetComponentsInChildren<BattleStaticBubble>();
        //foreach (BattleStaticBubble bubble in upBubbles)
        //{
        //    GameObject x = GameObject.Instantiate(bubble.gameObject, go.transform);
        //    //x.name = "bubble";
        //    x.name = x.name.Replace("(Clone)", "");
        //    manager.staticBubbles.Add(x.GetComponent<BattleStaticBubble>());
        //}

        return manager;
    }
    #endregion

    #region fxManger
    public static BattleFxManager BuildFxManager(GameObject root)
    {
        GameObject go = new GameObject("FxManager");
        go.transform.SetParent(root.transform, false);

        return go.AddComponent<BattleFxManager>();
    }

    #endregion

    #region weaponManager
    public static BattleWeaponManager BuildWeaponManager(GameObject root)
    {
        GameObject go = new GameObject("WeaponDirector");
        go.transform.SetParent(root.transform);
        go.transform.localPosition = Vector3.zero;
        go.transform.localScale = Vector3.one;

        BattleWeaponManager manager = go.AddComponent<BattleWeaponManager>();

        manager.leftWeapon = BuildSingleWeapon(go, "leftWeaon");
        manager.rightWeapon = BuildSingleWeapon(go, "rightWeapon");
        return manager;
    }
    public static BattleActorNode BuildSingleWeapon(GameObject go,string name)
    {
        GameObject node = new GameObject(name);
        node.transform.SetParent(go.transform);

        node.AddComponent<PlayableDirector>();

        AudioSource audio = node.AddComponent<AudioSource>();
        SetAudioSource(audio);

        BattleActorNode battleNode = node.AddComponent<BattleActorNode>();
        return battleNode;
    }
    #endregion

    #region template
    public static void BuildTemplate(GameObject maskPrefab)
    {
        GameObject root = new GameObject(maskPrefab.name);
        root.transform.localPosition = Vector3.zero;
        root.transform.localRotation = Quaternion.identity;
        root.transform.localScale = Vector3.one;

        GameObject maskGo = GameObject.Instantiate(maskPrefab);
        maskGo.transform.SetParent(root.transform, false);
        maskGo.transform.localPosition = Vector3.zero;
        maskGo.transform.localRotation = Quaternion.Euler(0, 0, 0);
        maskGo.transform.localScale = Vector3.one;
        maskGo.name = "Mask";

        Camera camera = BuildCamera(root);
        BuildBackgroundNoneMask(root);
        BuildHud(root, camera, maskGo.transform.childCount);
    }
    #endregion

    #region input
    public static BattleInput BuildInput(GameObject root)
    {
        GameObject go = new GameObject("Input");
        go.transform.SetParent(root.transform);
        go.transform.localPosition = Vector3.zero;
        go.transform.localScale = Vector3.one;
        return go.AddComponent<BattleInput>();
    }
    #endregion

    #region OutsideBattle
    public static BattleOutsideSkillManager BuildOutsideSkillManager(GameObject root, Camera camera)
    {
        GameObject go = new GameObject("CanvasOutsideSkill");
        go.transform.SetParent(root.transform, false);
        RectTransform rect = go.AddComponent<RectTransform>();
        rect.sizeDelta = new Vector2(720, 1280);
        rect.localScale = new Vector3(0.01f, 0.01f, 0.01f);
        go.transform.localPosition = Vector3.zero;
        go.transform.localRotation = Quaternion.identity;
        go.transform.localScale = Vector3.one;

        Canvas canvas = go.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.ScreenSpaceCamera;
        canvas.worldCamera = camera;
        canvas.planeDistance = 3f;
        canvas.sortingLayerName = "FloatText";
        canvas.sortingOrder = 10;

        CanvasScaler scaler = go.AddComponent<CanvasScaler>();
        scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
        scaler.referenceResolution = new Vector2(720, 1280);
        scaler.screenMatchMode = CanvasScaler.ScreenMatchMode.Expand;

        BattleOutsideSkillManager manager = go.AddComponent<BattleOutsideSkillManager>();

        //RawImage部分
        GameObject imageObj = new GameObject("TargetImage");
        RectTransform imageRect = AppendRectTransform(imageObj, go.transform, Vector3.zero, new Vector2(1280, 1280));
        RawImage rawImage = imageObj.AddComponent<RawImage>();
        Color color = Color.white; color.a = 0;
        rawImage.color = color;
        manager.image = rawImage;
        return manager;
    }
    #endregion
}
