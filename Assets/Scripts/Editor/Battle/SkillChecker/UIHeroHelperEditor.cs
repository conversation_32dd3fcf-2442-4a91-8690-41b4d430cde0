using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

[CustomEditor(typeof(UIHeroHelper))]
public class UIHeroHelperEditor : Editor
{
    UIHeroHelper preview;
    public override void OnInspectorGUI()
    {
        if (preview == null)
        {
            preview = target as UIHeroHelper;
        }
        DrawDefaultInspector();

        GUILayout.BeginHorizontal();
        //if (GUILayout.Button("��ʼ�����Զ���"))
        //{
        //    preview.InstanceObject();
        //}

        if (GUILayout.Button("ѡ��ƹ�"))
        {
            Selection.activeGameObject = preview.controller.lightGroup[preview.lightGroupIndex].mainLight.gameObject;
        }

        if (GUILayout.Button("��������"))
        {
            //float jiaodu = preview.controller.lightGroup[2].mainLight.transform.localRotation.y * 180 / Mathf.PI;
            //preview.ShadowAngle = jiaodu;
            preview.SaveData();
        }

        preview.ChangeShadow();

        GUILayout.EndHorizontal();

        if (Application.isPlaying)
        {
            if (GUILayout.Button("����Show����"))
            {
                preview.PlayAnimator();
            }
        }

        GUILayout.BeginHorizontal();
        if (GUILayout.Button("�ı����"))
        {
            preview.ChangeMat();
        }
        if (GUILayout.Button("��ԭ����"))
        {
            preview.ResumeOriginMaterial();
        }
        GUILayout.EndHorizontal();
    }
}
