/// <summary>
/// 检测英雄技能配置是否正确,如：
/// 配置表中配置的多段伤害数量与 Timeline 中是否一致，若不一致将导致死亡时血量不归0
/// 若缺少最后一段伤害，可能不触发死亡表现
/// OnNext 是否在 OnHit之前，将导致多段伤害未能表现
/// OnHit 与 多段伤害是否一致，若 OnHit 指定 segment 重复，可能影响受击表现，血量，死亡表现等
/// </summary>

using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using UnityEditor;
using UnityEngine;
using UnityEngine.Timeline;

public class SkillHelperEditor : EditorWindow
{
	// 配置表根路径
	string csvPath = "../../Tools/csv_script/";
	string heroCSVName = "Hero.csv";
	string heroSkillCSVName = "HeroSkill.csv";
	string skillCSVName = "Skill.csv";
	string heroID;
	int skillMaxCount = 100;

	HashSet<string> ignoreSkill = new HashSet<string>
	{
		"art/skill/common/common_wubiaoxian.playable",
	};

	string encodingName = "GB2312";

	[MenuItem("Tools/Skill Helper")]
	public static void Open()
	{
		GetWindow<SkillHelperEditor>();
	}

	//void OnDestroy()
	//{
	//}

	void OnGUI()
	{
		OnGUICheckSkillTimeline();
	}

	void OnGUICheckSkillTimeline()
    {
		csvPath = EditorGUILayout.TextField("配置表根路径", csvPath);

		EditorGUILayout.BeginHorizontal();
		heroCSVName = EditorGUILayout.TextField("英雄表名:", heroCSVName);
		heroSkillCSVName = EditorGUILayout.TextField("英雄技能表名:", heroSkillCSVName);
		skillCSVName = EditorGUILayout.TextField("技能表名:", skillCSVName);
		EditorGUILayout.EndHorizontal();

		heroID = EditorGUILayout.TextField("英雄ID", heroID);
		skillMaxCount = EditorGUILayout.IntField("英雄技能最大数量:", skillMaxCount);
		encodingName = EditorGUILayout.TextField(encodingName);

		if (GUILayout.Button("检测技能资源"))
		{
			if (string.IsNullOrEmpty(heroID))
            {
				Debug.LogError("英雄 ID 不能为空");
				return;
            }
			var heroCSVPath = Path.GetFullPath(csvPath + "/" + heroCSVName);
			var recordFile = CSVParser.Load(heroCSVPath, encodingName);
			if (recordFile == null)
            {
				return;
            }
			var recordList = recordFile.GetRecord("heroID", heroID);
			if(recordList.Count == 0)
            {
				Debug.LogError("未能找到英雄ID:" + heroID);
				return;
            }
			Dictionary<string, string> parsedSkill = new Dictionary<string, string>();

			foreach (var record in recordList)
            {
				var heroSkillId = record["heroSkillId"];
				if(string.IsNullOrEmpty(heroSkillId))
                {
					Debug.LogError(heroID + " 未正确配置技能 ID");
					continue;
                }
				var heroSkillList = heroSkillId.Split('#');
				foreach(var heroSkillID in heroSkillList)
                {
					CheckHeroSkillValid(heroSkillID, parsedSkill);
				}
			}
			Debug.LogWarning("英雄ID:" + heroID + ",检测技能资源完成");
			Resources.UnloadUnusedAssets();
		}
	}

	bool CheckHeroSkillValid(string heroSkillID, Dictionary<string, string> parsedSkill)
    {
		//加载英雄技能表
		var heroSkillCSVPath = Path.GetFullPath(csvPath + "/" + heroSkillCSVName);
		var recordFile = CSVParser.Load(heroSkillCSVPath, encodingName);
		if (recordFile == null)
		{
			return false;
		}

		//加载技能表
		var skillCSVPath = Path.GetFullPath(csvPath + "/" + skillCSVName);
		var skillRecordFile = CSVParser.Load(skillCSVPath, encodingName);
		if (skillRecordFile == null)
		{
			return false;
		}

		//解析 HeroSkill 表格
		var heroSkillList = recordFile.GetRecord("heroSkillID", heroSkillID);
		if (heroSkillList.Count == 0)
		{
			Debug.LogError("未能找到英雄技能ID:" + heroSkillID);
			return false;
		}

		foreach (var heroSkillRecord in heroSkillList)
		{
			ParseHeroSkillAndCheck(heroSkillID, heroSkillRecord, skillRecordFile, parsedSkill);
		}

		// 为避开 HeroSkill + Buff + Effect 三个表格之间的复杂跳转，真实查找英雄技能的逻辑（Buff + Effect 之间会互相多次跳转，而 Effect 又有 > 50 种以上类型）
		// 采用直接根据英雄 ID + 技能 ID 组合的命令规则，直接遍历查询表格
		var numberLength = (skillMaxCount - 1).ToString().Length;
		var numberFormant = "D" + numberLength;
		for(var i = 0; i < skillMaxCount; i++)
        {
			var skillID = heroID + i.ToString(numberFormant);
			var recordDataList = skillRecordFile.GetRecord("nSkillID", skillID);
			if (recordDataList.Count == 0)
			{
				continue;
			}
			CheckSkillValid(skillRecordFile, skillID, parsedSkill);
		}

		return true;
	}

	void ParseHeroSkillAndCheck(string heroSkillID, CSVParser.RecordData heroSkillRecord, CSVParser.RecordFile skillRecordFile, Dictionary<string, string> parsedSkill)
    {
		var skillLevel = heroSkillRecord["Lv"];
		// 决定技能类型 （ 1: 技能 2: buff 3:属性 ）
		var effectGroup = heroSkillRecord["effectGroup"];
		var paramGroup = heroSkillRecord["paramGroup"];
		var effectGroupList = effectGroup.Split('#');
		var paramGroupList = paramGroup.Split(';');
		if (effectGroupList.Length != paramGroupList.Length)
		{
			Debug.LogError("英雄技能 ID:" + heroSkillID + ", Lv: " + skillLevel + ",effeGroup 段数:" + effectGroupList.Length + ", paramGroup 段数:" + paramGroupList.Length);
			return;
		}
		for (var i = 0; i < effectGroupList.Length; i++)
		{
			var effectID = effectGroupList[i];
			if (effectID != "1")
			{
				continue;
			}
			var skillIDStr = paramGroupList[i];
			var skillIDList = skillIDStr.Split('#');
			if (skillIDList.Length < 1)
			{
				Debug.LogError("英雄技能 ID:" + heroSkillID + ", Lv: " + skillLevel + ",paramGroup 配置错误:" + paramGroup);
				continue;
			}
			var skillID = skillIDList[0];
			CheckSkillValid(skillRecordFile, skillID, parsedSkill);
		}
	}

	bool CheckSkillValid(CSVParser.RecordFile skillRecordFile, string skillID, Dictionary<string,string> parsedSkill)
    {
		var recordDataList = skillRecordFile.GetRecord("nSkillID", skillID);
		if(recordDataList.Count == 0)
        {
			Debug.LogError(skillCSVName + "表格中未找到技能:" + skillID);
			return false;
        }

		// <段数,每段触发次数>
		Dictionary<string, int> segmentHit = new Dictionary<string, int>();

		foreach(var recordData in recordDataList)
        {
			//多段伤害表现
			var multistage = recordData["Multistage"];
			if(string.IsNullOrEmpty(multistage))
            {
				//不填，默认为一次伤害
				multistage = "1";
            }
			int nMultistage;
			if (!int.TryParse(multistage, out nMultistage))
			{
				Debug.LogError("多段受击表现配置错误，技能ID:" + skillID + "，Multistage:" + multistage);
				continue;
			}

			var skillResPath = recordData["strPath"];
			if(string.IsNullOrEmpty(skillResPath))
            {
				Debug.LogError("技能ID:" + skillID + "未配置技能表现资源");
				continue;
            }
			if(ignoreSkill.Contains(skillResPath))
            {
				continue;
            }

			var parsedSkillName = multistage + "_" + skillResPath;
			if (parsedSkill.ContainsKey(parsedSkillName))
            {
				// 已检测过此技能
				continue;
            }
			parsedSkill[parsedSkillName] = "1";

			// Windows 系统对路径大小写不敏感，此处先直接使用 asetbundle name 来作为文件路径
			var skillTimeline = AssetDatabase.LoadAssetAtPath<TimelineAsset>("Assets/" + skillResPath);
			if(skillTimeline == null)
            {
				Debug.LogError("加载技能失败,资源可能不存在:" + skillResPath);
				continue;
            }
			double endTime = skillTimeline.duration;
			double maxOnHitTime = 0;
			foreach (TrackAsset track in skillTimeline.GetRootTracks())
			{
				foreach (TimelineClip clip in track.GetClips())
				{
					var eventClip = clip.asset;
					if(eventClip == null)
                    {
						continue;
                    }
                    var assetType = eventClip.GetType();
                    if (assetType.FullName != "EventClip")
                    {
                        continue;
                    }
                    FieldInfo templateField = assetType.GetField("template");
                    var template = templateField.GetValue(eventClip);
                    if (template == null)
                    {
                        continue;
                    }
					var templateType = template.GetType();
					FieldInfo eventNameField = templateType.GetField("eventName");
                    string eventName = eventNameField.GetValue(template) as string;
					if (eventName == null)
                    {
                        continue;
                    }

					if(eventName == "OnNext" && clip.end < endTime)
                    {
						endTime = clip.end;
					}

					if (eventName != "OnHit")
                    {
						continue;
                    }

					// 记录 OnHit 最后触发时间
					if(clip.start > maxOnHitTime)
                    {
						maxOnHitTime = clip.start;
                    }

					// 记录 Timeline 中配置 OnHit 段数
					FieldInfo paramField = templateType.GetField("param");
					string param = paramField.GetValue(template) as string;
					if (param == null)
					{
						continue;
					}

					int onHitCount = 0;
					segmentHit.TryGetValue(param, out onHitCount);
					segmentHit[param] = ++onHitCount;
				}
			}

			string strError = "";
			for(var i = 0; i < nMultistage; i++)
            {
				int onHitCount = 0;
				if(!segmentHit.TryGetValue(i.ToString(), out onHitCount) || onHitCount != 1)
				{
					strError += "segment:" + (i + 1) + ",OnHit 次数:" + onHitCount + "\n";

				}
            }
			if(!string.IsNullOrEmpty(strError))
            {
				Debug.LogError("技能ID:" + skillID + "," + skillResPath + ",以下段数攻击次数不为1:\n" + strError);
				//Debug.LogError(parsedSkillName);
            }

			if(endTime <= maxOnHitTime)
            {
				Debug.LogError("技能ID:" + skillID + "," + skillResPath + ", OnNext 在 OnHit 之前触发，多段伤害不能完整触发");
            }
		}

		return true;
	}
}
