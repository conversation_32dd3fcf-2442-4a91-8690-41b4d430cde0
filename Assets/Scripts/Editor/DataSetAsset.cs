
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;

namespace editor.tools.asset
{

    [System.Serializable]
    public class DataSetAsset : ScriptableObject
    {
        [System.Serializable]
        public struct KeyValuePair
        {
            public string Key;
            public string Value;
        }

        // �� KeyValuePair ֮ǰ���� DataCfg �࣬������չ�������ݣ�����ʵ����ʱ�����Ĭ��ֵ
        [System.Serializable]
        public class DataCfg
        {
            public List<KeyValuePair> keyValueSet = new List<KeyValuePair>
            {
                { new KeyValuePair { Key = "channelTag", Value = "com.q1.xhero" } },
                { new KeyValuePair { Key = "keyExample", Value = "" } },
                { new KeyValuePair { Key = "deubg_keyExample", Value = "" } },
            };
            public RuntimePlatform platform = RuntimePlatform.IPhonePlayer;
        }

        public List<DataCfg> dataSet = new List<DataCfg>();

        public string Get(KeyValuePair condition, string key, RuntimePlatform platform)
        {
            string value;
            bool validCondition;
            foreach(var cfg in dataSet)
            {
                if(cfg.platform != platform)
                {
                    continue;
                }
                value = null;
                validCondition = false;
                foreach (var property in cfg.keyValueSet)
                {
                    if (property.Key == key)
                    {
                        value = property.Value;
                    }
                    if(property.Key == condition.Key && property.Value == condition.Value)
                    {
                        validCondition = true;
                    }
                    if(validCondition && value != null)
                    {
                        return value;
                    }
                }
            }
            return null;
        }
    }
}