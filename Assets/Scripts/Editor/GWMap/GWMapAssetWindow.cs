/*******************************************************
 * Description:   地图编辑器的窗口,主要功能绘制网格,左键选择框
 * 选,右键取消选择,序列化等
 * Author:        袁楠
 * Created Date:  2024年8月8日
 ********************************************************/

using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

namespace EditorTools.GWMap
{
    public class GWMapAssetWindow : EditorWindow
    {
        private GWMapAssetSerializedProperty _gwMapAssetProperty;

        private Vector2 _mapScrollPosition;
        private float _mapScale = 1.0f;
        private Texture2D _tileTexture;

        private string[] _tileConfigOptions;
        private int _tileConfigIndex;
        private Tuple<int, Color> _tileConfigTuple;

        // 鼠标事件
        private Vector2Int _startDragGrid;
        private Vector2Int _endDragGrid;
        private Vector2Int _hoverGridPos;
        private bool _mouseDown = false;
        private Color _hoverColor = Color.white;

        public static void OpenWindow(SerializedProperty mapData)
        {
            var window = GetWindow<GWMapAssetWindow>("Map Data Editor");
            // 设置窗口的最小和最大大小，以防止用户修改
            window.minSize = new Vector2(680, 480);
            window.maxSize = new Vector2(1280, 960);
            window._gwMapAssetProperty = new GWMapAssetSerializedProperty(mapData);
            window.InitAllData();
            window.Show();
        }

        private void OnEnable()
        {
            EditorApplication.update += Update;
        }

        private void OnDisable()
        {
            EditorApplication.update -= Update;
        }

        private void Update()
        {
            // 设置一个标志位，控制重绘
            Repaint();
        }

        private void OnGUI()
        {
            if (_gwMapAssetProperty != null)
            {
                DrawEventButton();
                DrawMapSize();
                DrawMapSetting();
                if (_gwMapAssetProperty.mapTexture.objectReferenceValue != null)
                {
                    _mapScrollPosition = EditorGUILayout.BeginScrollView(_mapScrollPosition,
                        GUILayout.Width(position.width),
                        GUILayout.Height(position.height - 165f));
                    Texture2D mapTexture = _gwMapAssetProperty.mapTexture.objectReferenceValue as Texture2D;
                    if (mapTexture != null)
                    {
                        Rect controlRect = EditorGUILayout.GetControlRect(
                            GUILayout.Width(mapTexture.width * _mapScale),
                            GUILayout.Height(mapTexture.height * _mapScale));
                        float gridWidth = controlRect.width / _gwMapAssetProperty.width.intValue;
                        float gridHeight = controlRect.height / _gwMapAssetProperty.height.intValue;
                        DrawMapTextureAndGridLine(controlRect, mapTexture, _tileTexture, gridWidth, gridHeight);

                        // 鼠标事件
                        Event currentEvent = Event.current;
                        Vector2 mousePosition = currentEvent.mousePosition;
                        if (_startDragGrid != _endDragGrid)
                        {
                            DrawMapSelectedBox(controlRect, _startDragGrid, _endDragGrid, gridWidth, gridHeight);
                        }

                        if (!_mouseDown)
                        {
                            DrawMapSelectedBox(controlRect, _hoverGridPos, _hoverGridPos, gridWidth, gridHeight);
                        }

                        if (controlRect.Contains(mousePosition))
                        {
                            Vector2Int gridPos = GetWindowGridPosition(controlRect, mousePosition, gridWidth,
                                gridHeight, _mapScale);
                            _hoverGridPos = gridPos; // 更新悬停的网格位置
                            HandleMouse0(currentEvent, gridPos);
                            HandleMouse1(currentEvent, gridPos);
                            // 绘制坐标
                            DrawHoverGridPos(mousePosition, _hoverGridPos);
                            if (currentEvent.type != EventType.Repaint)
                                currentEvent.Use();
                        }

                        EditorGUILayout.EndScrollView();
                    }
                }
            }
        }

        private void DrawHoverGridPos(Vector2 mousePosition, Vector2Int gridPos)
        {
            // 保存原始字体大小和颜色
            var tempSize = GUI.skin.label.fontSize;
            var tempColor = GUI.color;

            // 设置字体大小和颜色
            GUI.skin.label.fontSize = 20;
            GUI.color = _hoverColor;

            Vector3 worldPos = new Vector3(mousePosition.x + 10f, mousePosition.y, 0);
            Vector3 worldPos2 = new Vector3(mousePosition.x + 10f, mousePosition.y + 20f, 0);
            Handles.Label(worldPos, $"{gridPos.x + 1},{gridPos.y + 1}");

            int type = GetTileGrid(gridPos);
            Handles.Label(worldPos2, $"type: {type}");

            // 恢复原始字体大小和颜色
            GUI.skin.label.fontSize = tempSize;
            GUI.color = tempColor;
        }

        private void OnDestroy()
        {
            _gwMapAssetProperty?.Dispose();
            _gwMapAssetProperty = null;
            _mouseDown = false;
            _mapScrollPosition = Vector2.zero;
            _mapScale = 1F;
            _tileTexture = null;
            _tileConfigOptions = null;
            _tileConfigIndex = 0;
            _tileConfigTuple = null;
        }

        #region Data

        private void InitAllData()
        {
            _startDragGrid = Vector2Int.zero;
            _endDragGrid = Vector2Int.zero;
            _hoverGridPos = Vector2Int.one;
            _mouseDown = false;
            _tileConfigOptions = new string[_gwMapAssetProperty.tileConfigs.arraySize];
            for (int i = 0; i < _gwMapAssetProperty.tileConfigs.arraySize; i++)
            {
                _tileConfigOptions[i] =
                    $"type: {_gwMapAssetProperty.tileConfigs.GetArrayElementAtIndex(i).FindPropertyRelative("type").intValue}";
            }

            UpdateTileTexture(_gwMapAssetProperty.width.intValue, _gwMapAssetProperty.height.intValue);
        }

        private void UpdateTileTexture(int width, int height)
        {
            if (_tileTexture == null || _tileTexture.width != width || _tileTexture.height != height)
            {
                _tileTexture = _gwMapAssetProperty.GetTileTexture();
            }
        }

        #endregion

        #region GUI

        private void DrawEventButton()
        {
            GUILayout.BeginHorizontal();
            if (GUILayout.Button("Convert to csv"))
            {
                HandleConvertToCsv();
            }
            else if (GUILayout.Button("Convert to json"))
            {
                HandleConvertToJson();
            }

            GUILayout.EndHorizontal();
        }

        private void DrawMapSize()
        {
            EditorGUILayout.LabelField("Map Size", EditorStyles.boldLabel);
            int width = Mathf.Max(1, EditorGUILayout.IntField("Width", _gwMapAssetProperty.width.intValue));
            int height = Mathf.Max(1, EditorGUILayout.IntField("Height", _gwMapAssetProperty.height.intValue));
            _gwMapAssetProperty.SetWidthHeight(width, height);
            UpdateTileTexture(width, height);
        }

        private void DrawMapSetting()
        {
            EditorGUILayout.LabelField("Map Setting", EditorStyles.boldLabel);
            _gwMapAssetProperty.mapTexture.objectReferenceValue = (Texture2D)EditorGUILayout.ObjectField("Map Texture",
                _gwMapAssetProperty.mapTexture.objectReferenceValue, typeof(object), false);

            _mapScale = EditorGUILayout.Slider("Map Scale", _mapScale, 0.5f, 100.0f);

            GUILayout.BeginHorizontal();
            int index = EditorGUILayout.Popup("Map Config", _tileConfigIndex, _tileConfigOptions);
            if (_tileConfigTuple == null || index != _tileConfigIndex)
            {
                _tileConfigIndex = index;
                _tileConfigTuple = _gwMapAssetProperty.GetTileConfig(_tileConfigIndex);
            }

            Color color = EditorGUILayout.ColorField("", _tileConfigTuple.Item2);
            if (color != _tileConfigTuple.Item2)
            {
                _gwMapAssetProperty.SetTileConfigData(_tileConfigIndex, _tileConfigTuple.Item1, color);
                _tileConfigTuple = _gwMapAssetProperty.GetTileConfig(_tileConfigIndex);
            }

            if (GUILayout.Button("Confirm Set Map"))
            {
                SetTileGridList(_startDragGrid, _endDragGrid, _tileConfigTuple.Item1, _tileConfigTuple.Item2);
            }

            GUILayout.EndHorizontal();

            // 悬停颜色
            _hoverColor = EditorGUILayout.ColorField("悬停颜色", _hoverColor);
        }

        private void DrawMapTextureAndGridLine(Rect rect, Texture2D mapTexture, Texture2D tileTexture, float gridWidth,
            float gridHeight)
        {
            GUI.DrawTexture(rect, mapTexture);
            GUI.DrawTexture(rect, tileTexture);
            // 绘制边框
            float x = rect.x;
            float y = rect.y;
            while (true)
            {
                if (x < rect.x + rect.width)
                {
                    Handles.DrawLine(new Vector3(x, rect.y, 0), new Vector3(x, rect.y + rect.height, 0));
                    x += gridWidth;
                }

                if (y < rect.y + rect.height)
                {
                    Handles.DrawLine(new Vector3(rect.x, y, 0), new Vector3(rect.x + rect.width, y, 0));
                    y += gridHeight;
                }

                // 绘制最后的边
                if (x >= rect.x + rect.width && y >= rect.y + rect.height)
                {
                    x = rect.x + rect.width;
                    y = rect.y + rect.height;
                    Handles.DrawLine(new Vector3(x, rect.y, 0), new Vector3(x, rect.y + rect.height, 0));
                    Handles.DrawLine(new Vector3(rect.x, y, 0), new Vector3(rect.x + rect.width, y, 0));
                    break;
                }
            }
        }

        private void DrawMapSelectedBox(Rect controlRect, Vector2Int startPos, Vector2Int endPos, float gridWidth,
            float gridHeight)
        {
            float startX, startY, endX, endY;
            if (endPos.x < startPos.x)
            {
                startX = controlRect.x + endPos.x * gridWidth;
                endX = Mathf.Clamp(startX + gridWidth * (startPos.x - endPos.x + 1),
                    controlRect.x, controlRect.x + controlRect.width);
            }
            else
            {
                startX = controlRect.x + startPos.x * gridWidth;
                endX = Mathf.Clamp(startX + gridWidth * (endPos.x - startPos.x + 1),
                    controlRect.x, controlRect.x + controlRect.width);
            }

            if (endPos.y < startPos.y)
            {
                startY = controlRect.y + (controlRect.height - (startPos.y + 1) * gridHeight);
                endY = startY + gridHeight * (startPos.y - endPos.y + 1);
            }
            else
            {
                startY = controlRect.y + (controlRect.height - (endPos.y + 1) * gridHeight);
                endY = startY + gridHeight * (endPos.y - startPos.y + 1);
            }

            DrawRectangle(new Vector3(startX, startY, 0), new Vector3(endX, startY, 0), new Vector3(endX, endY, 0),
                new Vector3(startX, endY, 0), _tileConfigTuple.Item2);
        }

        private void DrawRectangle(Vector3 leftTop, Vector3 rightTop, Vector3 rightBottom, Vector3 leftBottom,
            Color color, int count = 3)
        {
            Handles.color = color;
            for (int i = 0; i < count; i++)
            {
                Handles.DrawLine(leftTop + Vector3.up * i, rightTop + Vector3.up * i);
                Handles.DrawLine(rightTop + Vector3.right * i, rightBottom + Vector3.right * i);
                Handles.DrawLine(rightBottom + Vector3.down * i, leftBottom + Vector3.down * i);
                Handles.DrawLine(leftBottom + Vector3.left * i, leftTop + Vector3.left * i);
            }
        }

        #endregion

        #region Event Handler

        private void HandleConvertToCsv()
        {
            List<MatrixLayout> cacheDataList = new List<MatrixLayout>()
            {
                GetMatrixLayout()
            };
            var exportPath = EditorUtility.SaveFilePanel("选择路径", "",
                $"{_gwMapAssetProperty.mapData.serializedObject.targetObject.name}.csv", "csv");
            EditorUtility.DisplayDialog("提示",
                EditorUtil.ExportCsv(exportPath, cacheDataList) ? "导出成功" : "导出失败", "确定");
        }

        private void HandleConvertToJson()
        {
            MatrixLayout cacheData = GetMatrixLayout();
            var exportPath = EditorUtility.SaveFilePanel("选择路径", "",
                $"{_gwMapAssetProperty.mapData.serializedObject.targetObject.name}.json", "json");
            EditorUtility.DisplayDialog("提示",
                EditorUtil.ExportJson(exportPath, cacheData) ? "导出成功" : "导出失败", "确定");
        }

        private void HandleMouse0(Event cEvent, Vector2Int gridPos)
        {
            if (cEvent.button != 0) return;
            switch (cEvent.type)
            {
                case EventType.MouseDown:
                    _mouseDown = true;
                    _startDragGrid = gridPos;
                    _endDragGrid = gridPos;
                    break;
                case EventType.MouseDrag:
                    _endDragGrid = gridPos;
                    break;
                case EventType.MouseUp:
                    _mouseDown = false;
                    _endDragGrid = gridPos;
                    int x = _startDragGrid.x - _endDragGrid.x;
                    int y = _startDragGrid.y - _endDragGrid.y;
                    if (x * x < 1 && y * y < 1)
                    {
                        SetTileGrid(_startDragGrid, _tileConfigTuple.Item1, _tileConfigTuple.Item2);
                        _startDragGrid = Vector2Int.zero;
                        _endDragGrid = Vector2Int.zero;
                    }

                    break;
            }
        }

        private void HandleMouse1(Event cEvent, Vector2Int gridPos)
        {
            if (cEvent.button != 1) return;
            switch (cEvent.type)
            {
                case EventType.MouseDown:
                    SetTileGrid(gridPos, 0, Color.clear);
                    _startDragGrid = Vector2Int.zero;
                    _endDragGrid = Vector2Int.zero;
                    break;
                case EventType.MouseDrag:
                    SetTileGrid(gridPos, 0, Color.clear);
                    break;
            }
        }

        private MatrixLayout GetMatrixLayout()
        {
            MatrixLayout data = new MatrixLayout()
            {
                mapID = _gwMapAssetProperty.mapData.FindPropertyRelative("mapID").intValue,
                mapWidth = _gwMapAssetProperty.width.intValue,
                mapHeight = _gwMapAssetProperty.height.intValue,
                tiles = new int[_gwMapAssetProperty.width.intValue * _gwMapAssetProperty.height.intValue]
            };
            for (int i = 0; i < _gwMapAssetProperty.tiles.arraySize; i++)
            {
                data.tiles[i] = _gwMapAssetProperty.tiles.GetArrayElementAtIndex(i).intValue;
            }

            return data;
        }

        private Vector2Int GetWindowGridPosition(Rect controlRect, Vector2 mousePosition, float gridWidth,
            float gridHeight, float scale = 1f)
        {
            Vector2 relativePosition = (mousePosition - new Vector2(controlRect.x, controlRect.y)) * scale;
            Vector2 texturePosition = relativePosition / _mapScale;

            return new Vector2Int(
                Mathf.FloorToInt(texturePosition.x / gridWidth),
                _gwMapAssetProperty.height.intValue - 1 -
                Mathf.FloorToInt(texturePosition.y / gridHeight) // Y coordinate inversion
            );
        }

        private int GetTileGrid(Vector2Int gridPos)
        {
            return _gwMapAssetProperty.GetTileData(gridPos.x, gridPos.y);
        }

        private void SetTileGrid(Vector2Int gridPos, int value, Color color)
        {
            _gwMapAssetProperty.SetTileData(gridPos.x, gridPos.y, value);
            _tileTexture.SetPixel(gridPos.x, gridPos.y, color);
            _tileTexture.Apply();
        }

        private void SetTileGridList(Vector2Int startPos, Vector2Int endPos, int value, Color color)
        {
            if (startPos != endPos)
            {
                int startX, startY, endX, endY;
                if (endPos.x < startPos.x)
                {
                    startX = endPos.x;
                    endX = startPos.x + 1;
                }
                else
                {
                    startX = startPos.x;
                    endX = endPos.x + 1;
                }

                if (endPos.y < startPos.y)
                {
                    startY = endPos.y;
                    endY = startPos.y + 1;
                }
                else
                {
                    startY = startPos.y;
                    endY = endPos.y + 1;
                }

                for (int x = startX; x < endX; x++)
                {
                    for (int y = startY; y < endY; y++)
                    {
                        _gwMapAssetProperty.SetTileData(x, y, value);
                        _tileTexture.SetPixel(x, y, color);
                    }
                }

                _tileTexture.Apply();
            }
        }

        #endregion
    }
}