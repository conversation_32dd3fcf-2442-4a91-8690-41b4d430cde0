/*******************************************************
 * Description:   地图编辑器源数据对象
 * Author:        袁楠
 * Created Date:  2024年8月6日
 ********************************************************/

using UnityEngine;
using Newtonsoft.Json;

namespace EditorTools.GWMap
{
    [CreateAssetMenu(fileName = "New MapData", menuName = "GWMapData/Create Map Data", order = 1)]
    public class GWMapAsset : ScriptableObject
    {
        public MatrixLayout map;
    }

    [System.Serializable]
    public class MatrixLayout
    {
        [ExportCsv("地图ID", true, CsvFilter.sc)]
        public int mapID;
        [ExportCsv("地图宽", false, CsvFilter.sc)]
        public int mapWidth = 50;
        [ExportCsv("地图高", false, CsvFilter.sc)]
        public int mapHeight = 50;
        [ExportCsv("地图网格数组,左下角坐标原点", false, CsvFilter.sc)]
        public int[] tiles;

        [JsonIgnore] public Texture2D mapTexture;
        [JsonIgnore] public TileConfig[] tileConfigs = { new TileConfig() { type = 1, color = Color.yellow } };
    }
    
    [System.Serializable]
    public class TileConfig
    {
        public int type;
        public Color color;
    }
}