/*******************************************************
 * Description:   地图编辑器源数据对象inspector面板布局
 * Author:        袁楠
 * Created Date:  2024年8月6日
 ********************************************************/

using UnityEditor;
using UnityEngine;

namespace EditorTools.GWMap
{
    [CustomPropertyDrawer(typeof(MatrixLayout))]
    public class GWMapAssetLayout : PropertyDrawer
    {
        private const float RectHeight = 20f;

        private Rect _position;
        private bool _showTiles;
        private bool _showEditorTileTypes;

        private readonly Color _black = new Color(0.43f, 0.25f, 0.15f, 0.7f);
        private readonly Color _white = new Color(0.55f, 0.63f, 0.65f, 0.7f);
        
        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            EditorGUI.BeginProperty(position, label, property);
            _position = new Rect(position.x, position.y, position.width, RectHeight);
            if (GUI.Button(_position, "Editor Map Data"))
            {
                GWMapAssetWindow.OpenWindow(property);
            }

            SerializedProperty idProp = property.FindPropertyRelative("mapID");
            SerializedProperty widthProp = property.FindPropertyRelative("mapWidth");
            SerializedProperty heightProp = property.FindPropertyRelative("mapHeight");
            SerializedProperty tilesProp = property.FindPropertyRelative("tiles");
            SerializedProperty baseTextureProp = property.FindPropertyRelative("mapTexture");
            SerializedProperty tileTypeProp = property.FindPropertyRelative("tileConfigs");

            EditorGUIIntField("Map ID", idProp);
            idProp.intValue = Mathf.Max(0, idProp.intValue);
            EditorGUIIntField("Map Width", widthProp);
            widthProp.intValue = Mathf.Max(1, widthProp.intValue);
            EditorGUIIntField("Map Height", heightProp);
            heightProp.intValue = Mathf.Max(1, heightProp.intValue);
            EditorGUITexture2D("Base Texture", baseTextureProp);

            ResizeArray(tilesProp, widthProp.intValue, heightProp.intValue); // 重置数组大小
            EditorGUITileArray("Tiles", tilesProp);
            EditorGUITileTypeArray("Tile Types", tileTypeProp);

            EditorGUI.EndProperty();
        }

        public override float GetPropertyHeight(SerializedProperty property, GUIContent label)
        {
            return _position.y;
        }

        private void EditorGUIIntField(string label, SerializedProperty property)
        {
            _position.y += RectHeight;
            property.intValue = EditorGUI.IntField(_position, label,
                property.intValue);
        }

        private void EditorGUITileArray(string label, SerializedProperty property)
        {
            _position.y += RectHeight;
            _showTiles = EditorGUI.Foldout(_position, _showTiles, label);
            Rect sizeRect = new Rect(_position.x + _position.width - 40f, _position.y, 40f,
                RectHeight);
            EditorGUI.IntField(sizeRect, "", property.arraySize);
            if (_showTiles)
            {
                for (int i = 0; i < property.arraySize; i++)
                {
                    SerializedProperty type = property.GetArrayElementAtIndex(i);
                    EditorGUIIntField($"type_{i}", type);
                }
            }
        }

        private void EditorGUITexture2D(string label, SerializedProperty property)
        {
            _position.y += RectHeight;
            Rect textureRect = new Rect(_position.x, _position.y, _position.width, EditorGUIUtility.singleLineHeight);
            property.objectReferenceValue = (Texture2D)EditorGUI.ObjectField(textureRect, label,
                property.objectReferenceValue, typeof(Texture2D), false);
        }

        private void EditorGUITileTypeArray(string label, SerializedProperty property)
        {
            _position.y += RectHeight;
            _showEditorTileTypes = EditorGUI.Foldout(_position, _showEditorTileTypes, label);
            Rect sizeRect = new Rect(_position.x + _position.width - 40f, _position.y, 40f,
                RectHeight);
            property.arraySize = EditorGUI.IntField(sizeRect, "", property.arraySize);
            if (_showEditorTileTypes)
            {
                for (int i = 0; i < property.arraySize; i++)
                {
                    SerializedProperty item = property.GetArrayElementAtIndex(i);
                    SerializedProperty type = item.FindPropertyRelative("type");
                    SerializedProperty color = item.FindPropertyRelative("color");

                    Color rectColor = i % 2 == 0 ? _white : _black;
                    EditorGUI.DrawRect(new Rect(_position.x, _position.y + 20f, _position.width, 40), rectColor);
                    _position.y += RectHeight;
                    type.intValue = EditorGUI.IntField(_position, "type", type.intValue);
                    _position.y += RectHeight;
                    color.colorValue = EditorGUI.ColorField(_position, "color", color.colorValue);
                }
            }
        }

        private bool ResizeArray(SerializedProperty property, int width, int height)
        {
            int newTileCount = width * height;
            if (property.arraySize != newTileCount)
            {
                property.arraySize = newTileCount;
                return true;
            }
            return false;
        }
    }
}