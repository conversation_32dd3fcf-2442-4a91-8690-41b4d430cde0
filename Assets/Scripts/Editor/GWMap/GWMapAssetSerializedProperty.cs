/*******************************************************
 * Description:   对GWMapAsset的属性做功能封装
 * Author:        袁楠
 * Created Date:  2024年8月8日
 ********************************************************/

using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using UnityEngine.Assertions;

namespace EditorTools.GWMap
{
    public class GWMapAssetSerializedProperty : IDisposable
    {
        public SerializedProperty mapData;

        public SerializedProperty width;
        public SerializedProperty height;
        public SerializedProperty mapTexture;
        public SerializedProperty tiles;
        public SerializedProperty tileConfigs;

        public GWMapAssetSerializedProperty(SerializedProperty mapData)
        {
            Assert.IsNotNull(mapData);
            this.mapData = mapData;
            this.width = mapData.FindPropertyRelative("mapWidth");
            this.height = mapData.FindPropertyRelative("mapHeight");
            this.mapTexture = mapData.FindPropertyRelative("mapTexture");
            this.tiles = mapData.FindPropertyRelative("tiles");
            this.tileConfigs = mapData.FindPropertyRelative("tileConfigs");
        }

        public Tuple<int, Color> GetTileConfig(int i)
        {
            if (i < 0 || i >= tileConfigs.arraySize) return null;
            var tileConfig = this.tileConfigs.GetArrayElementAtIndex(i);
            return new Tuple<int, Color>(
                tileConfig.FindPropertyRelative("type").intValue,
                tileConfig.FindPropertyRelative("color").colorValue);
        }

        public Texture2D GetTileTexture()
        {
            Texture2D tileTexture =
                new Texture2D(this.width.intValue, this.height.intValue, TextureFormat.RGBA32, false);
            tileTexture.wrapMode = TextureWrapMode.Clamp;
            tileTexture.filterMode = FilterMode.Point;
            Dictionary<int, Color> tempColors = new Dictionary<int, Color>();
            for (int i = 0; i < tileConfigs.arraySize; i++)
            {
                Tuple<int, Color> tuple = GetTileConfig(i);
                if (tuple == null) continue;
                if (!tempColors.ContainsKey(tuple.Item1))
                {
                    tempColors.Add(tuple.Item1, tuple.Item2);
                }
            }

            for (int i = 0; i < tiles.arraySize; i++)
            {
                int type = tiles.GetArrayElementAtIndex(i).intValue;
                if (type != 0 && tempColors.TryGetValue(type, out var color))
                {
                    tileTexture.SetPixel(i % width.intValue, i / width.intValue, color);
                }
                else
                {
                    if (type == 0) // 当存在不在配置中的数据时,清楚内容
                        SetTileData(i % width.intValue, i / width.intValue, 0);
                    tileTexture.SetPixel(i % width.intValue, i / width.intValue, Color.clear);
                }
            }

            tileTexture.Apply();
            return tileTexture;
        }

        public void SetWidthHeight(int w, int h)
        {
            if (w == this.width.intValue && h == this.height.intValue) return;
            this.width.intValue = w;
            this.height.intValue = h;
            this.tiles.arraySize = w * h;
            mapData.serializedObject.ApplyModifiedProperties();
        }

        public int GetTileData(int x, int y)
        {
            var typeProp = tiles.GetArrayElementAtIndex(y * width.intValue + x);
            return typeProp.intValue;
        }
        
        public void SetTileData(int x, int y, int type)
        {
            var typeProp = tiles.GetArrayElementAtIndex(y * width.intValue + x);
            if (typeProp.intValue == type) return;
            typeProp.intValue = type;
            mapData.serializedObject.ApplyModifiedProperties();
        }

        public void SetTileConfigData(int i, int type, Color color)
        {
            var tileConfigProp = this.tileConfigs.GetArrayElementAtIndex(i);
            var typeProp = tileConfigProp.FindPropertyRelative("type");
            var colorProp = tileConfigProp.FindPropertyRelative("color");
            if (typeProp.intValue == type && colorProp.colorValue == color) return;
            typeProp.intValue = type;
            colorProp.colorValue = color;
            mapData.serializedObject.ApplyModifiedProperties();
        }

        public void Dispose()
        {
            mapData?.Dispose();
            width?.Dispose();
            height?.Dispose();
            mapTexture?.Dispose();
            tiles?.Dispose();
            tileConfigs?.Dispose();
        }
    }
}