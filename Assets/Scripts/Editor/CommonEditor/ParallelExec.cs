using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using UnityEditor;
using UnityEngine;

public class ParallelExec
{
    public class ParallelInfo
    {
        public System.Threading.Tasks.TaskCompletionSource<bool> taskCompletionSource;
        public int currentIdx = 0;
        public string displayTxt = string.Empty;
        public int totalCount = 1;
        public bool cancel = false;
    }

    public static bool ResourcesParallel<T>(Dictionary<string, T> resourcesSet, Action<string,T> taskFunc, Action<ParallelInfo, string, string> updateFunc, string completeDes)
    {
        var bSuccess = true;
        ParallelInfo parallelDownloadInfo = new ParallelInfo
        {
            taskCompletionSource = new TaskCompletionSource<bool>(),
            currentIdx = 0,
            totalCount = resourcesSet.Count,
            cancel = false
        };

        try
        {
            var stopWatch = System.Diagnostics.Stopwatch.StartNew();

            ParallelOptions po = new ParallelOptions
            {
                MaxDegreeOfParallelism = Environment.ProcessorCount
            };

            System.Threading.Tasks.Task.Run(() =>
            {
                Parallel.ForEach(resourcesSet, po, (keyPair, loopState) =>
                {
                    var key = keyPair.Key;
                    try
                    {
                        if (parallelDownloadInfo.cancel)
                        {
                            loopState.Break();
                        }

                        taskFunc.Invoke(key, keyPair.Value);
                    }
                    catch (Exception ex)
                    {
                        bSuccess = false;
                        Debug.LogException(ex);
                    }
                    finally
                    {
                        parallelDownloadInfo.displayTxt = key;
                        parallelDownloadInfo.currentIdx++;
                    }
                });

                parallelDownloadInfo.taskCompletionSource.SetResult(true);
            });

            while (true)
            {
                if(parallelDownloadInfo.cancel)
                {
                    Debug.LogWarning($"手动取消执行");
                    break;
                }
                if (parallelDownloadInfo.taskCompletionSource != null && parallelDownloadInfo.taskCompletionSource.Task.IsCompleted)
                {
                    stopWatch.Stop();
                    Debug.LogWarning($"{completeDes} 耗时:{stopWatch.ElapsedMilliseconds} ms");
                    break;
                }
                else if(parallelDownloadInfo.taskCompletionSource == null)
                {
                    stopWatch.Stop();
                    Debug.LogWarning($"{completeDes} 耗时:{stopWatch.ElapsedMilliseconds} ms");
                    break;
                }
                else
                {
                    updateFunc(parallelDownloadInfo, completeDes, completeDes);
                }
            }
        }
        catch (Exception e)
        {
            Debug.LogException(e);
            bSuccess = false;
        }
        finally
        {
            EditorUtility.ClearProgressBar();
            if (parallelDownloadInfo.taskCompletionSource != null && !parallelDownloadInfo.taskCompletionSource.Task.IsCompleted)
            {
                parallelDownloadInfo.taskCompletionSource.SetResult(true);
            }
        }
        return bSuccess;
    }

    public static void UpdateDownload(ParallelInfo parallelDownloadInfo, string titleDes, string completeDes)
    {
        if (parallelDownloadInfo.taskCompletionSource != null && parallelDownloadInfo.taskCompletionSource.Task.IsCompleted)
        {
            parallelDownloadInfo.taskCompletionSource = null;
            Debug.Log($"{completeDes} 完成");
            return;
        }

        if (parallelDownloadInfo.taskCompletionSource != null)
        {
            var bCancle = EditorUtility.DisplayCancelableProgressBar($"{titleDes}", parallelDownloadInfo.displayTxt, parallelDownloadInfo.currentIdx / (float)parallelDownloadInfo.totalCount);
            if (bCancle && !parallelDownloadInfo.cancel)
            {
                Debug.LogWarning($"手动取消执行");
                parallelDownloadInfo.cancel = true;
            }
        }
    }
}
