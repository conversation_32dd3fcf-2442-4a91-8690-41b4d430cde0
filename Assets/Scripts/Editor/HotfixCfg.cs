using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using UnityEngine;

public static class HotfixCfg{

    // Use this for initialization
    /*[XLua.Hotfix]
    public static List<Type> by_field = new List<Type>()
    {
        typeof(War.Battle.EpisodeAction),
        typeof(War.Battle.Episode),
        typeof(War.Battle.BattlePlayer),
        //typeof(GenericClass<>),
    };*/

    [XLua.Makeup]
    public static List<Type> by_property
    {
        get
        {
            var typeList = (from type in Assembly.Load("Battle").GetTypes()
                            where type.IsPublic
                            select type).ToList();
            typeList.AddRange((from type in Assembly.Load("Base").GetTypes()
                               where type.IsPublic && !type.Name.EndsWith("MiniLZO")
                               select type).ToList());
            typeList.AddRange((from type in Assembly.Load("Common").GetTypes()
                               where type.IsPublic
                               select type).ToList());
            typeList.AddRange((from type in Assembly.Load("Controller").GetTypes()
                               where type.IsPublic
                               select type).ToList());
            typeList.AddRange((from type in Assembly.Load("Game").GetTypes()
                               where type.IsPublic
                               select type).ToList());
            typeList.AddRange((from type in Assembly.Load("Render").GetTypes()
                               where type.IsPublic
                               select type).ToList());
            typeList.AddRange((from type in Assembly.Load("Scene").GetTypes()
                               where type.IsPublic
                               select type).ToList());
            typeList.AddRange((from type in Assembly.Load("UI").GetTypes()
                               where type.IsPublic
                               select type).ToList());
            typeList.AddRange((from type in Assembly.Load("Script").GetTypes()
                               where type.IsPublic
                               select type).ToList());
            /*typeList.AddRange((from type in Assembly.Load("Assembly-CSharp").GetTypes()
                               where type.IsPublic
                               select type).ToList());*/
            return typeList;
        }
    }
}
