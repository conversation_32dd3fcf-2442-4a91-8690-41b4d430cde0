using System.IO;
using UnityEngine;
using UnityEditor;

namespace Assets.Scripts.Editor
{
    public class AssetInfoUtility
    {
        [MenuItem("Assets/AssetInfo/Relative Path（相对路径）", false, 3)]
        public static void PrintAssetRelativePath()
        {
            string path = AssetDatabase.GetAssetPath(Selection.activeObject);
            Debug.Log($"{path}");
        }

        [MenuItem("Assets/AssetInfo/Absolute Path（绝对路径）", false, 3)]
        public static void PrintAssetAbsolutePath()
        {
            string assetsPath = Application.dataPath;
            assetsPath = assetsPath.Substring(0, assetsPath.LastIndexOf("Assets"));
            string path = Path.Combine(assetsPath, AssetDatabase.GetAssetPath(Selection.activeObject));
            Debug.Log($"{path}");
        }

        [MenuItem("Assets/AssetInfo/GUID and FileID", false, 3)]
        public static void PrintAssetGUIDAndFileID()
        {
            string guid;
            long localId;
            AssetDatabase.TryGetGUIDAndLocalFileIdentifier(Selection.activeObject, out guid, out localId);
            Debug.Log($"GUID: {guid} | FileId: {localId}");
        }
    }
}