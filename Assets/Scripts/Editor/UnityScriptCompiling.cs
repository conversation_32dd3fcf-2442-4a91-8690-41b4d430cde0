using UnityEditor;

[InitializeOnLoad]
public class UnityScriptCompiling : AssetPostprocessor
{
    /// <summary>
    /// Unity内脚本修改编译完成之后回调该函数
    /// </summary>
    //[UnityEditor.Callbacks.DidReloadScripts]
    //static void AllScriptsReloaded()
    //{
    //    // 消息监听 + 编译状态机来控制执行步骤：编译 dll,生成 xlua wrap 再编译，hotfix 注入
    //    // 主要问题，依赖消息监听机制，依赖维护的状态机正常，相对于通过脚本分三次调用三个功能，逻辑上更复杂，可靠性低
    //    // 在升级到 unity 2018, unity 2020 时均出现问题，现暂停此机制，直接使用脚本调用
    //    AutoBuildDLL.ScriptCompilingCB();
    //}
}