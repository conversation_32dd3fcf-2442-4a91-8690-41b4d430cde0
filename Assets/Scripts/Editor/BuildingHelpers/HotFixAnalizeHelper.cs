/****************************************************
 *  Copyright © 2018-2022 冰川网络  All rights reserved.
 *  文件：HotFixAnalizeHelper
 *  作者：wjy
 *  日期：22-8-13
 *  功能： 分析热更包体依赖
*****************************************************/

#if UNITY_EDITOR

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEditor.UIElements;
using UnityEngine;
using War.Base;
using Object = System.Object;
using WebRequest = System.Net.WebRequest;

namespace BuildingHelpers
{
    public static class HotFixAnalizeHelper
    {
        // [MenuItem("Assets/CalculateReferenceInfos", false, 1)]
        // public static void AnalizeHotFixFiles()
        // {
        //     string hotUrlOrPath = @"C:\Users\<USER>\Desktop\DiffDetails(3).json";
        //     string svnChangeList = @"C:\Users\<USER>\Desktop\svnFiles.json";
        //     string outPath = Path.Combine(Application.dataPath, "HotFixeReferences11.json");
        //     CalculateReferenceInfos(svnChangeList, hotUrlOrPath, outPath);
        // }
        
        /// <summary>
        /// 溯源热更文件改变的出处
        /// </summary>
        /// <param name="svnFileUrl">svn差异文件是地路径</param>
        /// <param name="diffUrl">热更详情本地路径</param>
        /// <param name="outFilePath">输出分析结果的文件路径</param>
        public static void CalculateReferenceInfos(string svnFilePath, string diffFilePath, string outFilePath)
        {
            var hotABNameList = GetHotABNameList(diffFilePath);
            //要分析的svn修改列表
            var svnChangeList = GetSvnChangeList(svnFilePath);

            Dictionary<string, ReferenceInfo> mainAnalizeData = new Dictionary<string, ReferenceInfo>();
            Dictionary<string, ReferenceInfo> mgameAnalizeData = new Dictionary<string, ReferenceInfo>();

            foreach (var hotABInfo in hotABNameList)
            {
                var abFiles = AssetDatabase.GetAssetPathsFromAssetBundle(hotABInfo.Key);
                var selfDic = hotABInfo.Key.StartsWith("casualgame") ? mgameAnalizeData : mainAnalizeData;
                foreach (var abFile in abFiles)
                {
                    var deps = AssetDatabase.GetDependencies(abFile);
                    foreach (var dep in deps)
                    {
                        if (svnChangeList.Contains(dep))
                        {
                            //dep 是修改的资源，影响的ab包为hotABInfo.Key  该包影响的预设体是：abFile
                            if (selfDic.TryGetValue(dep, out var tmpTree))
                            {
                                tmpTree.referenceBy[hotABInfo.Key] = hotABNameList[hotABInfo.Key];
                            }
                            else
                            {
                                var referenceInfo = new ReferenceInfo(dep);
                                referenceInfo.referenceBy[hotABInfo.Key] = hotABNameList[hotABInfo.Key];
                                selfDic[dep] = referenceInfo;
                            }
                        }
                    }
                }
            }

            List<ReferenceInfo> mainList = mainAnalizeData.Values.ToList();
            foreach (var node in mainList)
            {
                node.CalculateInfos(SizeSuffix);
            }

            mainList.Sort();


            List<ReferenceInfo> mgameList = mgameAnalizeData.Values.ToList();
            mgameList.OrderBy(_ => _.bytes);
            foreach (var node in mgameList)
            {
                node.CalculateInfos(SizeSuffix);
            }


            // string outPath = Path.Combine(Application.dataPath, "HotFixeReferences.json");
            File.WriteAllText(outFilePath, UIHelper.ToJson(new Dictionary<string, List<ReferenceInfo>>()
            {
                {"assets", mainList}, {"mgame", mgameList}
            }));
            // Debug.LogWarning($"输出文件路径为：{outPath}");
        }


        public static Dictionary<string, long> GetHotABNameList(string url)
        {
            var urlString = ReadUrlText(url);
            Dictionary<string, Object> oldFile = new Dictionary<string, Object>();
            if (!string.IsNullOrEmpty(urlString))
            {
                oldFile = UIHelper.ToObj<Dictionary<string, Object>>(urlString);
            }

            var assetInfos = oldFile["diffList"].ToString();
            Dictionary<string, Object> assetInfosContainer = UIHelper.ToObj<Dictionary<string, Object>>(assetInfos);
            Debug.Log(assetInfosContainer["assetDiff"].ToString());
            var casualsContainer =
                UIHelper.ToObj<Dictionary<string, Dictionary<string, string>>>(assetInfosContainer["casualDiff"]
                    .ToString());
            var mainABString = UIHelper.ToObj<Dictionary<string, string>>(assetInfosContainer["assetDiff"].ToString());

            Dictionary<string, string> allInfos = new Dictionary<string, string>();

            //小游戏的ab包信息
            foreach (var casualsData in casualsContainer.Values)
            {
                foreach (var casualAbs in casualsData)
                {
                    allInfos.Add(casualAbs.Key, casualAbs.Value);
                }
            }

            //主游戏的ab包信息
            foreach (var keyValuePair in mainABString)
            {
                allInfos.Add(keyValuePair.Key, keyValuePair.Value);
            }

            // var allCasuleGameAssets = assetInfos["diffList"] as Dictionary<string, Dictionary<string, string>>;
            // var allMainAssets = assetInfos["assetDiff"] as string;
            Dictionary<string, long> allInfosLong = new Dictionary<string, long>(allInfos.Count);
            foreach (var abI in allInfos)
            {
                allInfosLong.Add(abI.Key, SizeSuffixToByteLength(abI.Value));
            }

            return allInfosLong;
        }

        public static Dictionary<string, long> GetABInfosFormString(string absString)
        {
            Dictionary<string, string> aa = UIHelper.ToObj<Dictionary<string, string>>(absString);
            foreach (var keyValuePair in aa)
            {
                Debug.LogWarning(keyValuePair.Value);
            }

            throw new NotImplementedException();
        }

        static readonly string[] SizeSuffixes =
            {"bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"};

        public static string SizeSuffix(long value, int decimalPlaces = 1)
        {
            if (decimalPlaces < 0)
            {
                throw new ArgumentOutOfRangeException("decimalPlaces");
            }

            if (value < 0)
            {
                return "-" + SizeSuffix(-value, decimalPlaces);
            }

            if (value == 0)
            {
                return string.Format("{0:n" + decimalPlaces + "} bytes", 0);
            }

            int mag = (int) Math.Log(value, 1024);
            decimal adjustedSize = (decimal) value / (1L << (mag * 10));
            if (Math.Round(adjustedSize, decimalPlaces) >= 1000)
            {
                mag += 1;
                adjustedSize /= 1024;
            }

            return string.Format("{0:n" + decimalPlaces + "} {1}",
                adjustedSize,
                SizeSuffixes[mag]);
        }

        public static long SizeSuffixToByteLength(string sizeInfo)
        {
            int mag = 0;
            string per = SizeSuffixes[mag];
            for (var i = 0; i < SizeSuffixes.Length; i++)
            {
                if (sizeInfo.Contains(SizeSuffixes[i]))
                {
                    mag = i;
                    per = SizeSuffixes[mag];
                    break;
                }
            }

            sizeInfo = sizeInfo.Replace(per, "").Trim();
            double targetSize = double.Parse(sizeInfo);
            double bytes = targetSize * Math.Pow(1024, mag);
            return (long) bytes;
        }

        public static List<string> GetSvnChangeList(string url)
        {
            var svnString = ReadUrlText(url);
            string[] splitStrs = new[] {"\r\n"};
            var allLines = svnString.Split(splitStrs, StringSplitOptions.None);
            var targetList = new List<string>(allLines.Length);

            splitStrs = new[] {"BinClient/Client/"};

            Debug.LogWarning(allLines.Length);
            foreach (var line in allLines)
            {
                if (line.Contains("BinClient/Client/Assets"))
                {
                    var target = line.Split(splitStrs, StringSplitOptions.None);
                    targetList.Add(target[1]);
                }
            }

            return targetList;
        }


        /// <summary>
        /// 读取url的file文件
        /// </summary>
        /// <param name="url"></param>
        /// <returns>text</returns>
        public static string ReadUrlText(string url)
        {
            string target = string.Empty;
            if (url.StartsWith("http"))
            {
                using Stream stream = WebRequest.Create(new Uri(url)).GetResponse().GetResponseStream();
                using (StreamReader file_content = new StreamReader(stream))
                {
                    target = file_content.ReadToEnd();
                    file_content.Close();
                }

                stream.Close();
            }
            else
            {
                string path = url.Replace("\\", "/");
                if (File.Exists(path))
                {
                    target = File.ReadAllText(path); // 添加对本地文件支持
                }
            }

            return target;
        }
    }
}

#endif