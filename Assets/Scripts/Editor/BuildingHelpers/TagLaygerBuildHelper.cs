/****************************************************
 *  Copyright © 2018-2022 冰川网络  All rights reserved.
 *  文件：TagLaygerBuildHelper.cs
 *  作者：wjy
 *  日期：2022-08-18
 *  功能：监测tag跟layer被错误设置的报警
*****************************************************/

#if UNITY_EDITOR

using System;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;
using War.Base;

namespace BuildingHelpers
{
    public static class TagLaygerBuildHelper
    {
        public static string GetFilePath()
        {
            string fileDir = Path.Combine(Application.dataPath, "BuildLogs");
            if (!Directory.Exists(fileDir))
            {
                Directory.CreateDirectory(fileDir);
            }

            string filePath = Path.Combine(fileDir, "layertag.json");
            return filePath;
        }

        // public static List<string> GetLogSVNFiles()
        // {
        //     return new List<string>()
        //     {
        //         SVNTool.SVNProjectPath + "/Assets/BuildLogs",
        //         SVNTool.SVNProjectPath + "/Assets/BuildLogs/layertag.json",
        //         SVNTool.SVNProjectPath + "/Assets/BuildLogs/layertag.json.meta",
        //     };
        // }


        public static LayerAndTagData GetLastData()
        {
            string path = GetFilePath();
            if (File.Exists(path))
            {
                var dataStr = File.ReadAllText(path);
                var dataObject = UIHelper.ToObj<LayerAndTagData>(dataStr);
                return dataObject;
            }

            return null;
        }

        public static LayerAndTagData CreateDefaultData()
        {
            return new LayerAndTagData
            {
                tags = StringArray2PackList(UnityEditorInternal.InternalEditorUtility.tags, false),
                layers = StringArray2PackList(UnityEditorInternal.InternalEditorUtility.layers, true)
            };
        }

        [MenuItem("Assets/DataAnalize", false, 1)]
        public static bool DataAnalize()
        {
            var filePath = GetFilePath();
            var lastData = GetLastData();

            if (lastData == null)
            {
                //工程为第一次启动检测，生成一份默认的存档
                lastData = CreateDefaultData();

                File.WriteAllText(filePath, UIHelper.ToJson(lastData));
            }
            else
            {
                //工程已经有存档，启动对比

                //是否层级检验通过
                if (!IsPass(lastData.layers, UnityEditorInternal.InternalEditorUtility.layers, true))
                {
                    Debug.LogError("层级被错误修改");
                    return false;
                }

                //是否标签检测通过
                if (!IsPass(lastData.tags, UnityEditorInternal.InternalEditorUtility.tags, false))
                {
                    Debug.LogError("标签被错误修改");

                    return false;
                }

                Debug.LogWarning("配置被正常,或者被正常修改了");

                //检测都通过，将现用存档覆盖为最新存档
                File.WriteAllText(filePath, UIHelper.ToJson(CreateDefaultData()));
            }

            AssetDatabase.Refresh();

            return true;
        }

        public static List<DataPack> StringArray2PackList(string[] origin, bool isLayer)
        {
            List<DataPack> data = new List<DataPack>(origin.Length);
            for (var i = 0; i < origin.Length; i++)
            {
                int tmpIndex = isLayer ? LayerMask.NameToLayer(origin[i]) : i;
                data.Add(new DataPack {name = origin[i], index = tmpIndex});
            }

            return data;
        }


        public static bool IsPass(List<DataPack> lastData, string[] currentStrs, bool isLayer)
        {
            if (lastData.Count > currentStrs.Length)
            {
                Debug.LogWarning("出现删除的情况了");
                return false;
            }

            List<DataPack> currentData = StringArray2PackList(currentStrs, isLayer);

            for (int i = 0; i < lastData.Count; i++)
            {
                if (isLayer)
                {
                    foreach (var dataPack in lastData)
                    {
                        if (!IsLayerPass(dataPack, currentData))
                        {
                            return false;
                        }
                    }
                }
                else
                {
                    bool isPass = lastData[i].name == currentData[i].name && lastData[i].index == currentData[i].index;
                    if (!isPass)
                    {
                        return false;
                    }
                }
            }

            return true;
        }


        public static void testBuildPipline()
        {
            DataAnalize();
        }
        
        
        
        
        public static void Commit2SvnServer(string[] files)
        {
            Debug.LogWarning($"svn={UIHelper.ToJson(files)}");
        }

        public static bool IsLayerPass(DataPack pack, List<DataPack> pool)
        {
            foreach (var dataPack in pool)
            {
                if (pack.name == dataPack.name)
                {
                    return pack.index == dataPack.index;
                }
            }

            return false;
        }
    }

    [System.Serializable]
    public class LayerAndTagData
    {
        public List<DataPack> tags;
        public List<DataPack> layers;
    }

    [System.Serializable]
    public class DataPack
    {
        public string name;
        public int index;
    }
}
#endif