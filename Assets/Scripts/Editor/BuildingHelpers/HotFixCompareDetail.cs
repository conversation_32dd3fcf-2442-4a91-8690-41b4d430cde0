/****************************************************
 *  Copyright © 2018-2022 冰川网络  All rights reserved.
 *  文件：HotFixCompareDetail.cs
 *  作者：wjy
 *  日期：2022-08-13
 *  功能：
*****************************************************/

#if UNITY_EDITOR

using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

namespace BuildingHelpers
{
    public class ReferenceInfo : IComparable<ReferenceInfo>
    {
        public string name;
        public long bytes;

        public string sumInfo;

        public Dictionary<string, long> referenceBy;

        public ReferenceInfo(string fileName)
        {
            this.name = fileName;
            bytes = 0;
            sumInfo = string.Empty;
            referenceBy = new Dictionary<string, long>();
        }

        public void CalculateInfos(Func<long, int, string> toString)
        {
            if (referenceBy != null && referenceBy.Count > 0)
            {
                foreach (var abFile in referenceBy)
                {
                    bytes += abFile.Value;
                }

                sumInfo = toString?.Invoke(this.bytes, 1);
            }
        }

        public int CompareTo(ReferenceInfo other)
        {
            if (this.bytes > other.bytes)
            {
                return -1;
            }

            if (this.bytes < other.bytes)
            {
                return 1;
            }

            return 0;
        }
    }
}
#endif