using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace War.Render
{
    [CustomEditor(typeof(ShaderCollectionWarmUp))]
    public class ShaderWarmupCollectionEditor : Editor
    {
        protected SerializedObject targetObject;
        protected SerializedProperty m_ShadersProperty;

        public void OnEnable()
        {
            targetObject = new SerializedObject(target as ShaderCollectionWarmUp);
            m_ShadersProperty = targetObject.FindProperty("m_Shaders");
        }

        public override void OnInspectorGUI()
        {
            if (GUILayout.Button("Refresh Shaders", new GUILayoutOption[] { GUILayout.MinWidth(100f)}))
            {
                m_ShadersProperty.ClearArray();

                var svcCollections = targetObject.FindProperty("m_ShaderVariantCollections");
                var extraShaderNames = GetStringsFromArrayProperty(targetObject.FindProperty("m_ExtraShaderNames"));

                var svcSize = svcCollections.arraySize;
                for (int i = 0; i < svcSize; ++i)
                {
                    var shaderVariantCollection = svcCollections.GetArrayElementAtIndex(i).objectReferenceValue;
                    SerializedProperty shadersProperty = new SerializedObject(shaderVariantCollection as ShaderVariantCollection).FindProperty("m_Shaders");
                    var shaderNames = GetShaderNames(shadersProperty);

                    var assetPath = AssetDatabase.GetAssetPath(shaderVariantCollection);
                    AssetImporter assetImporter = AssetImporter.GetAtPath(assetPath);
                    var assetBundleName = assetImporter.assetBundleName;

                    var shaderObjs = LoadAllAssetsByBundleName(assetBundleName);
                    if (shaderObjs != null)
                    {
                        foreach (var shaderObj in shaderObjs)
                        {
                            var shader = shaderObj as Shader;
                            if (shader != null)
                            {
                                var shaderName = shader.name;
                                if (shaderNames.Contains(shaderName) || extraShaderNames.Contains(shaderName))
                                {
                                    m_ShadersProperty.InsertArrayElementAtIndex(m_ShadersProperty.arraySize);
                                    var property = m_ShadersProperty.GetArrayElementAtIndex(m_ShadersProperty.arraySize - 1);
                                    property.objectReferenceValue = shader;
                                }
                            }
                        }
                    }
                }

                targetObject.ApplyModifiedProperties();
            }

            DrawDefaultInspector();
        }

        private List<string> GetStringsFromArrayProperty(SerializedProperty arrayProperty)
        {
            var result = new List<string>();
            for (int i = 0; i < arrayProperty.arraySize; ++i)
            {
                result.Add(arrayProperty.GetArrayElementAtIndex(i).stringValue);
            }
            return result;
        }

        private List<string> GetShaderNames(SerializedProperty shadersProperty)
        {
            int shadersSize = shadersProperty.arraySize;
            List<string> result = new List<string>();

            for (int i = 0; i < shadersSize; ++i)
            {
                var shaderProperty = shadersProperty.GetArrayElementAtIndex(i);
                Shader objectReferenceValue = (Shader)shaderProperty.FindPropertyRelative("first").objectReferenceValue;
                if (objectReferenceValue)
                {
                result.Add(objectReferenceValue.name);
                }
            }

            return result;
        }

        private Object[] LoadAllAssetsByBundleName(string assetBundleName)
        {
            string[] assetBundlePaths = AssetDatabase.GetAssetPathsFromAssetBundle(assetBundleName);
            if (assetBundlePaths.Length == 0)
            {
                ///@TODO: The error needs to differentiate that an asset bundle name doesn't exist
                //        from that there right scene does not exist in the asset bundle...

                Debug.LogError("There is no asset bundle with name \"" + assetBundleName + "\"");
                return null;
            }

            Object[] assets = new Object[assetBundlePaths.Length];

            for (int i = 0, count = assetBundlePaths.Length; i < count; ++i)
            {
                string assetPath = assetBundlePaths[i];
                assets[i] = AssetDatabase.LoadAssetAtPath<Object>(assetPath);
            }

            return assets;
        }
    }
}