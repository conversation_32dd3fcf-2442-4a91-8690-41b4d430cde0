using System.Collections.Generic;
using System.IO;
using System.Linq;
using PhotoshopFile;
using UnityEngine;
using UnityEditor;
using Object = UnityEngine.Object;

namespace subjectnerdagreement.psdexport
{
    public class PSDExporter
    {
        public enum ScaleDown
        {
            Default,
            Half,
            Quarter
        }

        public delegate Color GetPixelColor(int row, int col);

        //遍历UI文件夹所有子文件夹和文件,算出文件的md5添加到字典;
        private static Dictionary<System.Guid, string> m_TextureGuids = new Dictionary<System.Guid, string>();

        private static void CalcUITextureGuids(DirectoryInfo dir)
        {
            System.Security.Cryptography.MD5 md5 = new System.Security.Cryptography.MD5CryptoServiceProvider();
            DirectoryInfo[] dii = dir.GetDirectories();
            FileInfo[] fil = dir.GetFiles();
            foreach (DirectoryInfo d in dii)
            {
                CalcUITextureGuids(d);
            }

            foreach (FileInfo f in fil)
            {
                if (Path.GetExtension(f.Name).ToLower() == ".png")
                {
                    FileStream file = new FileStream(f.FullName, FileMode.Open);
                    byte[] retVal = md5.ComputeHash(file);
                    file.Close();
                    System.Guid guid = new System.Guid(retVal);
                    string filepath;
                    if (m_TextureGuids.TryGetValue(guid, out filepath))
                    {
                        Debug.Log("图" + f.FullName + "图" + filepath + "是同一张图");
                    }
                    else
                    {
                        m_TextureGuids.Add(guid, f.FullName);
                    }
                }
            }
        }
           
        public static List<int> GetExportLayers(PsdExportSettings settings, PsdFileInfo fileInfo)
        {
            List<int> exportLayers = new List<int>();
            foreach (var keypair in settings.layerSettings)
            {
                PsdExportSettings.LayerSetting layerSetting = keypair.Value;
                // Don't export if not set to export,如果没设置导出,则不导出
                if (!layerSetting.doExport)
                    continue;

                // Don't export if group is off, 如果组关闭,不导出
                var groupInfo = fileInfo.GetGroupByLayerIndex(layerSetting.layerIndex);
                if (groupInfo != null && !groupInfo.visible)
                    continue;

                exportLayers.Add(layerSetting.layerIndex);
            }
            return exportLayers;
        }

        public static int GetExportCount(PsdExportSettings settings, PsdFileInfo fileInfo)
        {
            var exportLayers = GetExportLayers(settings, fileInfo);
            return exportLayers.Count;
        }

        public static void Export(PsdExportSettings settings, PsdFileInfo fileInfo, bool exportExisting = true)
        {
            m_TextureGuids.Clear();
            CalcUITextureGuids(new DirectoryInfo(Application.dataPath + "/UI"));
            List<int> layerIndices = GetExportLayers(settings, fileInfo);

            // If not going to export existing, filter out layers with existing files 如果不导出现有的，用现有文件过滤层
            if (exportExisting == false)
            {
                layerIndices = layerIndices.Where(delegate (int layerIndex)
                {
                    string filePath = GetLayerFilename(settings, layerIndex);
                    // If file exists, don't export
                    return !File.Exists(filePath);
                }).ToList();
            }

            int exportCount = 0;
            foreach (int layerIndex in layerIndices)
            {
                string infoString = string.Format("Importing {0} / {1} Layers", exportCount, layerIndices.Count);
                string fileString = string.Format("Importing PSD Layers: {0}", settings.Filename);

                float progress = exportCount / (float)layerIndices.Count;

                EditorUtility.DisplayProgressBar(fileString, infoString, progress);

                CreateSprite(settings, layerIndex);
                exportCount++;
            }

            EditorUtility.ClearProgressBar();
            settings.SaveMetaData();
            settings.SaveLayerMetaData();
        }

        public static Sprite CreateSprite(PsdExportSettings settings, int layerIndex)
        {
            var layer = settings.Psd.Layers[layerIndex];
            if (layer.IsText)
            {
                return null;
            }

            Vector4 spriteBorder;
            Texture2D tex = CreateTexture(layer, out spriteBorder);
            if (tex == null)
            {
                return null;
            }

            Sprite sprite = SaveAsset(settings, tex, spriteBorder, layerIndex);
            Object.DestroyImmediate(tex);
            return sprite;
        }

        private static Texture2D CreateTexture(Layer layer, out Vector4 spriteBorder)
        {
            spriteBorder = Vector4.zero;
            int layerWidth = (int)layer.Rect.width;
            int layerHeight = (int)layer.Rect.height;

            if (layerWidth == 0 || layerWidth == 0)
            {
                return null;
            }

            Channel red = (from l in layer.Channels where l.ID == 0 select l).First();
            Channel green = (from l in layer.Channels where l.ID == 1 select l).First();
            Channel blue = (from l in layer.Channels where l.ID == 2 select l).First();
            Channel alpha = layer.AlphaChannel;

            byte r0 = red.ImageData[0];
            byte g0 = green.ImageData[0];
            byte b0 = blue.ImageData[0];
            byte a0 = 255;
            if (alpha != null)
                a0 = alpha.ImageData[0];
            bool pureColor = true;

            for (int i = 0; i < layer.Rect.width * layer.Rect.height; i++)
            {
                byte r = red.ImageData[i];
                byte g = green.ImageData[i];
                byte b = blue.ImageData[i];
                byte a = 255;

                if (alpha != null)
                    a = alpha.ImageData[i];

                if (r != r0 || g != g0 || b != b0 || a != a0)
                {
                    pureColor = false;
                    break;
                }
            }

            Texture2D tex = null;
            Color32[] pixels = null;
            if (pureColor)
            {
                //长*宽的值 = 像素个数
                tex = new Texture2D(1, 1, TextureFormat.RGBA32, true);
                pixels = new Color32[1];
                pixels[0] = new Color32(r0, g0, b0, a0);
            }
            else
            {
                // 检测九宫格形式图片
                spriteBorder = CheckLayerBorder(layer);
                if (spriteBorder == Vector4.zero)
                {
                    tex = new Texture2D(layerWidth, layerHeight, TextureFormat.RGBA32, true);
                    pixels = new Color32[tex.width * tex.height];
                    for (int i = 0; i < pixels.Length; i++)
                    {
                        byte r = red.ImageData[i];
                        byte g = green.ImageData[i];
                        byte b = blue.ImageData[i];
                        byte a = 255;

                        if (alpha != null)
                            a = alpha.ImageData[i];

                        int mod = i % tex.width;
                        int n = ((tex.width - mod - 1) + i) - mod;
                        pixels[pixels.Length - n - 1] = new Color32(r, g, b, a);
                    }
                }
                else
                {
                    GetPixelColor GetPixelColor = (row, col) =>
                    {
                        int pixelIndex = (layerHeight - col - 1) * layerWidth + row;
                        byte r = red.ImageData[pixelIndex];
                        byte g = green.ImageData[pixelIndex];
                        byte b = blue.ImageData[pixelIndex];
                        byte a = 255;

                        if (alpha != null)
                            a = alpha.ImageData[pixelIndex];
                        return new Color32(r, g, b, a);
                    };

                    int texWidth = (int)spriteBorder.x + 1 + (layerWidth - 1 - (int)spriteBorder.z);
                    int texHeight = (int)spriteBorder.y + 1 + (layerHeight - 1 - (int)spriteBorder.w);
                    tex = new Texture2D(texWidth, texHeight, TextureFormat.RGBA32, true);
                    pixels = new Color32[tex.width * tex.height];
                    int pixelsIndex = 0;
                    for (int i = 0; i < layerHeight; ++i)
                    {
                        if (spriteBorder.y <= i && i <= spriteBorder.w)
                        {
                            i += (int)(spriteBorder.w - spriteBorder.y);
                        }

                        for (int j = 0; j < layerWidth; ++j)
                        {
                            if (spriteBorder.x <= j && j <= spriteBorder.z)
                            {
                                j += (int)(spriteBorder.z - spriteBorder.x);
                            }

                            pixels[pixelsIndex++] = GetPixelColor(j, i);
                        }
                    }

                    // 转换border为Unity的Sprite格式的border
                    // 值有效才做处理，0为无效值
                    if (spriteBorder.z != 0) 
                    {
                        spriteBorder.z = layerWidth - spriteBorder.z - 1;
                    }
                    if (spriteBorder.w != 0)
                    {
                        spriteBorder.w = layerHeight - spriteBorder.w - 1;
                    }
                }
            }

            tex.SetPixels32(pixels);
            tex.Apply();
            return tex;
        }

        static protected Vector4 CheckLayerBorder(Layer layer)
        {
            int layerWidth = (int)layer.Rect.width;
            int layerHeight = (int)layer.Rect.height;

            Vector4 spriteBorder = Vector4.zero;

            Channel red = (from l in layer.Channels where l.ID == 0 select l).First();
            Channel green = (from l in layer.Channels where l.ID == 1 select l).First();
            Channel blue = (from l in layer.Channels where l.ID == 2 select l).First();
            Channel alpha = layer.AlphaChannel;

            // 检测九宫格形式图片
            Vector2Int centerPoint = new Vector2Int(layerWidth / 2, layerHeight / 2);

            GetPixelColor GetPixelColor = (row, col) =>
            {
                try
                {
                    int pixelIndex = (layerHeight - col - 1) * layerWidth + row;
                    byte r = red.ImageData[pixelIndex];
                    byte g = green.ImageData[pixelIndex];
                    byte b = blue.ImageData[pixelIndex];
                    byte a = 255;

                    if (alpha != null)
                        a = alpha.ImageData[pixelIndex];
                    return new Color32(r, g, b, a);
                }
                catch (System.IndexOutOfRangeException)
                {
                    return Color.black;
                }
            };

            int left = 0;
            int right = 0;
            int top = 0;
            int bottom = 0;

            bool leftPass = true;
            bool rightPass = true;

            for (int widthBias = 1; widthBias < centerPoint.x; ++widthBias)
            {
                int l = centerPoint.x - widthBias;
                int r = centerPoint.x + widthBias;
                
                for (int j = 0; j < layerHeight; ++j)
                {
                    Color colorMiddle = GetPixelColor(centerPoint.x, j);
                    if (leftPass)
                    {
                        Color colorLeft = GetPixelColor(l, j);
                        if (colorMiddle != colorLeft)
                        {
                            leftPass = false;
                        }
                    }

                    if (rightPass)
                    {
                        Color colorRight = GetPixelColor(r, j);
                        if (colorMiddle != colorRight)
                        {
                            rightPass = false;
                        }
                    }

                    if (!leftPass && !rightPass)
                    {
                        break;
                    }
                }

                if (leftPass)
                {
                    left = l;
                    // 如果左边检测通过，右边的值至少是中间值
                    if (right == 0)
                    {
                        right = centerPoint.x;
                    }
                }

                if (rightPass)
                {
                    right = r;
                    // 如果右边检测通过，左边的值至少是中间值
                    if (left == 0)
                    {
                        left = centerPoint.x;
                    }
                }

                if (!leftPass && !rightPass)
                {
                    break;
                }
            }

            bool topPass = true;
            bool bottomPass = true;

            for (int heightBias = 1; heightBias < centerPoint.y; ++heightBias)
            {
                int t = centerPoint.y - heightBias;
                int b = centerPoint.y + heightBias;

                for (int j = 0; j < layerWidth; ++j)
                {
                    Color colorMiddle = GetPixelColor(j, centerPoint.y);

                    if (topPass)
                    {
                        Color colorTop = GetPixelColor(j, t);
                        if (colorMiddle != colorTop)
                        {
                            topPass = false;
                        }
                    }

                    if (bottomPass)
                    {
                        Color colorBottom = GetPixelColor(j, b);
                        if (colorMiddle != colorBottom)
                        {
                            bottomPass = false;
                        }
                    }

                    if (!topPass && !bottomPass)
                    {
                        break;
                    }
                }

                if (topPass)
                {
                    top = t;
                    // 如果上边检测通过，下边的值至少是中间值
                    if (bottom == 0)
                    {
                        bottom = centerPoint.y;
                    }
                }

                if (bottomPass)
                {
                    bottom = b;
                    // 如果下边检测通过，上边的值至少是中间值
                    if (top == 0)
                    {
                        top = centerPoint.y;
                    }
                }

                if (!topPass && !bottomPass)
                {
                    break;
                }
            }

            // left border
            spriteBorder.x = left;
            // right border
            spriteBorder.z = right;
            // top border
            spriteBorder.y = top;
            // bottom border
            spriteBorder.w = bottom;

            return spriteBorder;
        }

        public static string GetLayerFilename(PsdExportSettings settings, int layerIndex)
        {
            // Strip out invalid characters from the file name从文件名中删除无效字符
            string layerName = settings.Psd.Layers[layerIndex].Name;
            layerName = settings.GetLayerPath(layerName);
            return layerName;
        }

        private static Sprite SaveAsset(PsdExportSettings settings, Texture2D tex, Vector4 spriteBorder, int layer)
        {
            string assetPath = GetLayerFilename(settings, layer);
            string assetDirectory = Path.GetDirectoryName(assetPath);
            if (!Directory.Exists(assetDirectory))
            {
                Directory.CreateDirectory(assetDirectory);
            }

            // 设置缩放变量
            float pixelsToUnits = settings.PixelsToUnitSize;

            //应用全局缩放，如果有的话
            if (settings.ScaleBy > 0)
            {
                tex = ScaleTextureByMipmap(tex, settings.ScaleBy);
            }

            PsdExportSettings.LayerSetting layerSetting = settings.layerSettings[layer];

            // Then scale by layer scale
            if (layerSetting.scaleBy != ScaleDown.Default)
            {
                // By default, scale by half
                int scaleLevel = 1;
                pixelsToUnits = Mathf.RoundToInt(settings.PixelsToUnitSize / 2f);
                spriteBorder = spriteBorder / 2f;

                // Setting is actually scale by quarter
                if (layerSetting.scaleBy == ScaleDown.Quarter)
                {
                    scaleLevel = 2;
                    pixelsToUnits = Mathf.RoundToInt(settings.PixelsToUnitSize / 4f);
                    spriteBorder = spriteBorder / 2f;
                }

                // Apply scaling
                tex = ScaleTextureByMipmap(tex, scaleLevel);
            }
            //把所有图片的MD5码和Guid与 正在导入的层（生成在内存中的png进行比较;
          
            byte[] buf = tex.EncodeToPNG();
            System.Security.Cryptography.MD5 _md5 = new System.Security.Cryptography.MD5CryptoServiceProvider();
            byte[] retVal = _md5.ComputeHash(buf);
            System.Guid LoadingTextureGuid = new System.Guid(retVal);
            string filePath;
            if (m_TextureGuids.TryGetValue(LoadingTextureGuid, out filePath))
            {
               Debug.LogWarning("和正在导入的 " + assetPath + " 相同的图 " + filePath + " 已经存在 ");
            }
            File.WriteAllBytes(assetPath, buf);
            AssetDatabase.Refresh();

            // 加载纹理，以便我们可以更改类型
            var textureObj = AssetDatabase.LoadAssetAtPath(assetPath, typeof(Texture2D));

            // 获取资产的纹理导入器
            TextureImporter textureImporter = (TextureImporter)AssetImporter.GetAtPath(assetPath);

            // 读出纹理导入设置，以便更改导入轴点
            TextureImporterSettings importSetting = new TextureImporterSettings();
            textureImporter.ReadTextureSettings(importSetting);

            // 设置枢轴导入设置
            importSetting.spriteAlignment = (int)settings.Pivot;

            // 但是，如果图层设置具有不同的数据透视，请将其设置为新透视
            if (settings.Pivot != layerSetting.pivot)
                importSetting.spriteAlignment = (int)layerSetting.pivot;

            // 枢轴设置是相同的，但自定义，设置向量
            //  else if (settings.Pivot == SpriteAlignment.Custom)
            //	importSetting.spritePivot = settings.PivotVector;
            importSetting.mipmapEnabled = false;
            importSetting.spritePixelsPerUnit = pixelsToUnits;

            // 写入纹理导入设置
            textureImporter.SetTextureSettings(importSetting);

            // 设置纹理设置的其余部分
            textureImporter.textureType = TextureImporterType.Sprite;
            textureImporter.spriteImportMode = SpriteImportMode.Single;
            textureImporter.spritePackingTag = settings.PackingTag;
            textureImporter.spriteBorder = spriteBorder;

            EditorUtility.SetDirty(textureObj);
            AssetDatabase.WriteImportSettingsIfDirty(assetPath);
            AssetDatabase.ImportAsset(assetPath, ImportAssetOptions.ForceUpdate);

            return (Sprite)AssetDatabase.LoadAssetAtPath(assetPath, typeof(Sprite));
        }

        private static Texture2D ScaleTextureByMipmap(Texture2D tex, int mipLevel)
        {
            if (mipLevel < 0 || mipLevel > 2)
                return null;
            int width = Mathf.RoundToInt(tex.width / (mipLevel * 2));
            int height = Mathf.RoundToInt(tex.height / (mipLevel * 2));

            // Scaling down by abusing mip maps
            Texture2D resized = new Texture2D(width, height);
            resized.SetPixels32(tex.GetPixels32(mipLevel));
            resized.Apply();
            return resized;
        }
    }
}
