/*=================================================================================
* 创建者:刘军
* 功能描述:文件编码格式设置检测
* 包含功能:1.文件编码格式设置检测
*=================================================================================*/
using System;
using System.Collections.Generic;
using System.Text;
using UnityEngine;
using EditorUtilitys;

/// <summary>
/// 文件编码格式设置检测
/// </summary>
public partial class ResSettingChecker
{
    /// <summary>
    /// 文件编码格式设置检测
    /// 参数说明:包含四个参数 eg:Assets/Lua/ads,.txt,0,utf-8
    /// 参数1为string类型,指定目录
    /// 参数2为string类型,指定后缀扩展名的文件,多个扩展名用||分割
    /// 参数3为bool类型,等于或者不等于指定编码
    /// 参数4为string类型,指定编码,如果带bom在后面加入eg:utf-8-bom
    /// </summary>
    class FileEncodeChecker : ResSetingsCheckerBase
    {
        /// <summary>
        /// 目录
        /// </summary>
        public string dir;
        /// <summary>
        /// 文件扩展名列表
        /// </summary>
        public string fileExtensionList;
        /// <summary>
        /// 是,否等于编码格式
        /// </summary>
        public bool matchEncodingName;
        /// <summary>
        /// 编码格式
        /// </summary>
        public string encodingName;
        /// <summary>
        /// 键
        /// </summary>
        public override string KeyDesc { get { return fileKeyString + resSettingCheckKeySplit + encodingString; } }
        /// <summary>
        /// 检测类型
        /// </summary>
        public override ResSetingsCheckType CheckType { get { return ResSetingsCheckType.FileEncoding; } }
        /// <summary>
        /// 文件扩展名
        /// </summary>
        string fileExtensionArr = string.Empty;

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="strParam"></param>
        /// <param name="lineNum"></param>
        public override void Init(string strParam, int lineNum)
        {
            base.Init(strParam, lineNum);

            if (!string.IsNullOrEmpty(strParam))
            {
                var paramsArr = strParam.Split(',');
                if (paramsArr != null && paramsArr.Length >= 4)
                {
                    try
                    {
                        dir = paramsArr[0];
                        fileExtensionList = paramsArr[1];
                        matchEncodingName = int.Parse(paramsArr[2]) > 0;
                        encodingName = paramsArr[3];
                        bParamValid = true;
                        ProcessFileExtension();
                    }
                    catch (Exception e)
                    {
                        UnityEngine.Debug.Log($"【FileEncodeChecker.Init,捕获到异常:{e.ToString()}】");
                    }
                }
            }

            if (!bParamValid)
            {
                Debug.LogError($"FileEncodeChecker类型参数配置错误:{strParam}");
            }
        }

        /// <summary>
        /// 处理文件扩展名
        /// </summary>
        void ProcessFileExtension()
        {
            if(string.IsNullOrEmpty(fileExtensionList))
            {
                bParamValid = false;
            }
            else
            {
                var strTarAssetTypes = fileExtensionList.Split(new string[] { oneParamSplit }, StringSplitOptions.RemoveEmptyEntries);

                if (strTarAssetTypes == null || strTarAssetTypes.Length <= 0)
                {
                    bParamValid = false;
                }
                else
                {
                    int addedIndex = 0;
                    StringBuilder sb = new StringBuilder();
                    foreach (var t in strTarAssetTypes)
                    {
                        if (addedIndex > 0)
                        {
                            sb.Append(",");
                        }

                        sb.Append(t);
                        addedIndex++;
                    }

                    fileExtensionArr = sb.ToString();
                }
            }
        }

        /// <summary>
        /// 执行检测
        /// </summary>
        /// <param name="tipsFiles"></param>
        public override void ExecuteCheck(List<FileTipsDetail> tipsFiles)
        {
            base.ExecuteCheck(tipsFiles);

            if (string.IsNullOrEmpty(dir))
            {
                return;
            }

            List<string> filesList = new List<string>();
            FileUtilitys.GetFilesOnDir(dir, filesList, fileExtensionArr);
            List<FileTipsDetail> checkList = new List<FileTipsDetail>();
            var setEncodingNameLower = encodingName.ToLower();
            var encodingBomNameLower = encodingBomName.ToLower();
            bool setHasBom = false;
            if (setEncodingNameLower.Contains(encodingBomNameLower))
            {
                setHasBom = true;
            }

            string strFileEncodingTip = "文件编码格式设置";
            if(matchEncodingName)
            {
                strFileEncodingTip += "为";
            }
            else
            {
                strFileEncodingTip += "不为";
            }

            foreach (var t in filesList)
            {
                bool withBom = true;
                var curEncoding = FileUtilitys.DetectEncode(t, ref withBom);
                var curEncodingNameLower = curEncoding.EncodingName.ToLower();
                var assetPath = t.Replace(Application.dataPath, "Assets");

                var satisfy = false;

                if (curEncodingNameLower.Contains(setEncodingNameLower))
                {
                    if(withBom == setHasBom)
                    {
                        satisfy = true;
                    }
                }

                if(matchEncodingName == satisfy)
                {
                    var ftd = new FileTipsDetail() { strTips = $"{strFileEncodingTip}{encodingName}", strFilePath = assetPath, checkType = CheckType, strGroupJsonValue = KeyDesc };
                    SetFileTipsDetailLastCommitInfo(assetPath, ftd);
                    SetFileTipsDetailJsonInfo(ftd);
                    ftd.sortSeq = ftd.svnDateTicks;

                    checkList.Add(ftd);
                }
            }

            if (checkList != null && checkList.Count > 0)
            {
                checkList.Sort((l, r) => 
                {
                    var sortSeqCom = l.sortSeq - r.sortSeq;
                    if (sortSeqCom > 0)
                    {
                        return -1;
                    }
                    else if (sortSeqCom == 0)
                    {
                        return 0;
                    }
                    else
                    {
                        return 1;
                    }
                });

                tipsFiles.AddRange(checkList);
            }
        }
    }
}

