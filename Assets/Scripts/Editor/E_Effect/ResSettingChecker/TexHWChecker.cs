/*=================================================================================
* 创建者:刘军
* 功能描述:图片宽高限制检测(排除UI图集里的图,对特效的贴图检测有说明)
* 包含功能:1.Texuter宽高限制检测
*=================================================================================*/
using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

/// <summary>
/// 图片宽高限制检测(排除UI图集里的图,对特效的贴图检测有说明)
/// </summary>
public partial class ResSettingChecker
{
    ///粒子资源命名规范
    ///常规图<=256*256
    ///序列帧尺寸<=512*512 命名带xulie_
	///噪声图<=64*64  命名带noise_
    ///光晕图<=128*128  命名带glow_  blur没有用到
    ///遮罩图<=256*256 命名带mask_
    /// <summary>
    /// 图片宽高限制检测(排除UI图集里的图,对特效的贴图检测有说明)
    /// 参数说明:包含五个参数 eg:Texture:HW,Assets/Art/Effects,1,"normal_",256,256
    /// 参数1为string类型,目录
    /// 参数2为检测类型,0为常规1为特效类型
    /// 参数3为检测参数,根据参数2来决定,如果参数2为特效类型,该参数表示以粒子特效命名规范检测
    /// 参数4为int类型,宽
    /// 参数5为int类型,高
    /// </summary>
    class TexHWChecker : ResSetingsCheckerBase
    {
        /// <summary>
        /// 目录
        /// </summary>
        public string dir;
        /// <summary>
        /// 检测类型
        /// </summary>
        public int checkType;
        /// <summary>
        /// 检测参数
        /// </summary>
        public string checkParam;
        /// <summary>
        /// 宽
        /// </summary>
        public int width = 1024;
        /// <summary>
        /// 高
        /// </summary>
        public int height = 1024;
        /// <summary>
        /// 键
        /// </summary>
        public override string KeyDesc { get { return texKeyString + resSettingCheckKeySplit + texHWKeyString; } }
        /// <summary>
        /// 检测类型
        /// </summary>
        public override ResSetingsCheckType CheckType { get { return ResSetingsCheckType.TexHWCheck; } }
        /// <summary>
        /// 图片检测类型
        /// </summary>
        TexHWCheckType texHWCheckType;

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="strParam"></param>
        /// <param name="lineNum"></param>
        public override void Init(string strParam, int lineNum)
        {
            base.Init(strParam, lineNum);

            if (!string.IsNullOrEmpty(strParam))
            {
                var paramsArr = strParam.Split(',');
                if (paramsArr != null && paramsArr.Length >= 5)
                {
                    try
                    {
                        dir = paramsArr[0];
                        checkType = int.Parse(paramsArr[1]);
                        bool isDefined = Enum.IsDefined(typeof(TexHWCheckType), checkType);
                        if (!isDefined)
                        {
                            bParamValid = false;
                            return;
                        }
                        else
                        {
                            texHWCheckType = (TexHWCheckType)checkType;
                        }

                        checkParam = paramsArr[2];
                        if (!CheckParamValid())
                        {
                            bParamValid = false;
                            return;
                        }

                        width = int.Parse(paramsArr[3]);
                        height = int.Parse(paramsArr[4]);
                        bParamValid = true;
                    }
                    catch (Exception e)
                    {
                        UnityEngine.Debug.Log($"【TexHWChecker.Init,捕获到异常:{e.ToString()}】");
                    }
                }
            }

            if (!bParamValid)
            {
                Debug.LogError($"TexHWChecker类型参数配置错误:{strParam}");
            }
        }

        /// <summary>
        /// 检测参数是否有效
        /// </summary>
        /// <returns></returns>
        bool CheckParamValid()
        {
            if(texHWCheckType == TexHWCheckType.Effect)
            {
                if (string.IsNullOrEmpty(checkParam))
                {
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 检测类型
        /// </summary>
        /// <param name="assetPath"></param>
        /// <param name="textureImporter"></param>
        /// <returns></returns>
        bool CheckTexHWCheckType(string assetPath, TextureImporter textureImporter)
        {
            if (texHWCheckType == TexHWCheckType.Effect)
            {
                var effectTexType = GetEffectTexType(assetPath);
                return CheckEffectTexHWCheckType(assetPath, effectTexType);
            }

            return true;
        }

        /// <summary>
        /// 检测特效类型
        /// </summary>
        /// <param name="assetPath"></param>
        /// <param name="effectTexType"></param>
        /// <returns></returns>
        bool CheckEffectTexHWCheckType(string assetPath, EffectTexType effectTexType)
        {
            var lCheckParam = checkParam.ToLower();
            var chkEffectTexType = GetEffectTexType(lCheckParam);

            return chkEffectTexType == effectTexType;
        }

        /// <summary>
        /// 是否需要检测
        /// </summary>
        /// <param name="assetPath"></param>
        /// <param name="textureImporter"></param>
        /// <param name="texture"></param>
        /// <returns></returns>
        bool NeedCheck(string assetPath, TextureImporter textureImporter, Texture2D texture, ref string strReasonTips)
        {
            strReasonTips = string.Empty;
            if (IsSprite(textureImporter))
            {//如果是图集里的图
                return false;
            }

            if(!CheckTexHWCheckType(assetPath, textureImporter))
            {
                return false;
            }

            var texWidth = texture.width;
            var texHeight = texture.height;
            strReasonTips = $"当前{texWidth}x{texHeight}超过指定的{width}x{height}";

            return CheckHW(texture);
        }

        /// <summary>
        /// 是否是图集里的图
        /// </summary>
        /// <param name="textureImporter"></param>
        /// <returns></returns>
        bool IsSprite(TextureImporter textureImporter)
        {
            var textureType = textureImporter.textureType;
            var spritePackingTag = textureImporter.spritePackingTag;
            if (textureType == TextureImporterType.Sprite && !string.IsNullOrEmpty(spritePackingTag))
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// 检测宽高
        /// </summary>
        /// <param name="texture"></param>
        /// <returns></returns>
        bool CheckHW(Texture2D texture)
        {
            var texWidth = texture.width;
            var texHeight = texture.height;

            return texWidth > width || texHeight > height;
        }

        /// <summary>
        /// 执行检测
        /// </summary>
        /// <param name="tipsFiles"></param>
        public override void ExecuteCheck(List<FileTipsDetail> tipsFiles)
        {
            base.ExecuteCheck(tipsFiles);

            List<string> filesList = new List<string>();
            List<FileTipsDetail> checkList = new List<FileTipsDetail>();
            string[] textureGuids = AssetDatabase.FindAssets("t:texture2D", new string[] { dir });

            if (textureGuids != null)
            {
                foreach (var t in textureGuids)
                {
                    var path = AssetDatabase.GUIDToAssetPath(t);
                    var texture = AssetDatabase.LoadAssetAtPath<Texture2D>(path);
                    var textureImporter = TextureImporter.GetAtPath(AssetDatabase.GetAssetPath(texture)) as TextureImporter;
                    string strReasonTips = "";
                    if (textureImporter != null && texture != null)
                    {
                        if (NeedCheck(path, textureImporter, texture, ref strReasonTips))
                        {
                            var ftd = new FileTipsDetail() { strTips = $"TexHWChecker检测({strReasonTips})", strFilePath = path, checkType = CheckType, strGroupJsonValue = KeyDesc };
                            SetFileTipsDetailLastCommitInfo(path, ftd);
                            SetFileTipsDetailJsonInfo(ftd);
                            ftd.sortSeq = ftd.svnDateTicks;

                            checkList.Add(ftd);
                        }
                    }
                }
            }

            if (checkList != null && checkList.Count > 0)
            {
                checkList.Sort((l, r) => 
                {
                    var sortSeqCom = l.sortSeq - r.sortSeq;
                    if (sortSeqCom > 0)
                    {
                        return -1;
                    }
                    else if (sortSeqCom == 0)
                    {
                        return 0;
                    }
                    else
                    {
                        return 1;
                    }
                });

                tipsFiles.AddRange(checkList);
            }
        }
    }
}

