/*=================================================================================
* 创建者:刘军
* 功能描述:战斗技能配置检查
* 包含功能:1.战斗技能配置检查
*=================================================================================*/
using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using UnityEditor;
using UnityEngine.Timeline;
using System.Text;
using UnityEngine;

/// <summary>
/// 战斗技能配置检查
/// </summary>
public partial class ResSettingChecker
{
    /// <summary>
    /// 战斗技能配置检查
    /// 参数说明:包含两个参数 eg:../../Tools/csv_script/,2000
    /// 参数1为string类型,指定目录
    /// 参数2为int类型,检测条数,每次检测最大条数最大为2000条
    /// </summary>
    class ConfigHeroSkillChecker : ResSetingsCheckerBase
    {
        /// <summary>
        /// csv目录
        /// </summary>
        public string csvDir = "../../Tools/csv_script/";
        /// <summary>
        /// 检测条数
        /// </summary>
        public int checkNum = 2000;
        /// <summary>
        /// 键
        /// </summary>
        public override string KeyDesc { get { return configKeyString + resSettingCheckKeySplit + heroSkillKeyString; } }
        /// <summary>
        /// 检测类型
        /// </summary>
        public override ResSetingsCheckType CheckType { get { return ResSetingsCheckType.ConfigHeroSkill ; } }
        /// <summary>
        /// 英雄csv文件名
        /// </summary>
        string heroCSVName = "Hero.csv";
        /// <summary>
        /// 英雄技能csv文件名
        /// </summary>
        string heroSkillCSVName = "HeroSkill.csv";
        /// <summary>
        /// 技能csv文件名
        /// </summary>
        string skillCSVName = "Skill.csv";
        /// <summary>
        /// 技能最大个数
        /// </summary>
        int skillMaxCount = 100;
        /// <summary>
        /// 忽略检测的技能配置集合
        /// </summary>
        HashSet<string> ignoreSkillSet = new HashSet<string>
        {
            "art/skill/common/common_wubiaoxian.playable",
        };
        /// <summary>
        /// 检测数据
        /// </summary>
        List<FileTipsDetail> checkList = new List<FileTipsDetail>();
        /// <summary>
        /// 已检测字典记录
        /// </summary>
        Dictionary<string, string> parsedSkill = new Dictionary<string, string>();
        /// <summary>
        /// 英雄表数据
        /// </summary>
        CSVParser.RecordFile heroRecordFile;
        /// <summary>
        /// 英雄技能表数据
        /// </summary>
        CSVParser.RecordFile heroSkillRecordFile;
        /// <summary>
        /// 技能表数据
        /// </summary>
        CSVParser.RecordFile skillRecordFile;
        /// <summary>
        /// 配置错误
        /// </summary>
        Dictionary<string, List<string>> configErrDic;

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="strParam"></param>
        /// <param name="lineNum"></param>
        public override void Init(string strParam, int lineNum)
        {
            base.Init(strParam, lineNum);

            if (!string.IsNullOrEmpty(strParam))
            {
                var paramsArr = strParam.Split(',');
                if(paramsArr != null && paramsArr.Length >= 2)
                {
                    try
                    {
                        csvDir = paramsArr[0];
                        checkNum = int.Parse(paramsArr[1]);
                        checkNum = Mathf.Abs(checkNum);
                        if(checkNum >= maxCfgHeroPerCheckNum)
                        {
                            checkNum = maxCfgHeroPerCheckNum;
                        }

                        bParamValid = true;
                    }
                    catch (Exception e)
                    {
                        UnityEngine.Debug.Log($"【ConfigHeroSkillChecker.Init,捕获到异常:{e.ToString()}】");
                    }
                }
            }

            if (!bParamValid)
            {
                Debug.LogError($"ConfigHeroSkillChecker类型参数配置错误:{strParam}");
            }
        }

        /// <summary>
        /// 检测配置
        /// </summary>
        /// <param name="record"></param>
        void CheckConfig(CSVParser.RecordData record)
        {
            var heroID = record["heroID"];
            var heroSkillId = record["heroSkillId"];

            if (string.IsNullOrEmpty(heroID) || string.IsNullOrEmpty(heroSkillId))
            {
                return;
            }

            var heroSkillList = heroSkillId.Split('#');  //某个英雄的技能ID列表
            foreach (var heroSkillID in heroSkillList)
            {
                CheckHeroSkillValid(heroID, heroSkillID);
            }
        }

        /// <summary>
        /// 检测英雄的技能配置是否有效
        /// </summary>
        /// <param name="heroID"></param>
        /// <param name="heroSkillID"></param>
        /// <returns></returns>
        bool CheckHeroSkillValid(string heroID, string heroSkillID)
        {
            //解析 HeroSkill 表格
            var heroSkillList = heroSkillRecordFile.GetRecord("heroSkillID", heroSkillID);
            if (heroSkillList.Count == 0)
            {
                return false;
            }

            foreach (var heroSkillRecord in heroSkillList)
            {//技能的配置在HeroSkill可能存在多个 技能ID+等级
                ParseHeroSkillAndCheck(heroID, heroSkillID, heroSkillRecord);
            }

            // 为避开 HeroSkill + Buff + Effect 三个表格之间的复杂跳转，真实查找英雄技能的逻辑（Buff + Effect 之间会互相多次跳转，而 Effect 又有 > 50 种以上类型）
            // 采用直接根据英雄 ID + 技能 ID 组合的命令规则，直接遍历查询表格
            var numberLength = (skillMaxCount - 1).ToString().Length;
            var numberFormant = "D" + numberLength;
            for (var i = 0; i < skillMaxCount; i++)
            {
                var skillID = heroID + i.ToString(numberFormant);
                var recordDataList = skillRecordFile.GetRecord("nSkillID", skillID);
                if (recordDataList.Count == 0)
                {
                    continue;
                }

                AddConfigErr(heroID, skillID, "1", CheckSkillValid(skillID));
            }

            return true;
        }

        /// <summary>
        /// 解析英雄的CSV配置
        /// </summary>
        /// <param name="heroID"></param>
        /// <param name="heroSkillID"></param>
        /// <param name="heroSkillRecord"></param>
        void ParseHeroSkillAndCheck(string heroID, string heroSkillID, CSVParser.RecordData heroSkillRecord)
        {
            var skillLevel = heroSkillRecord["Lv"];
            //决定技能类型 (1:技能 2:Buff 3:属性)
            var effectGroup = heroSkillRecord["effectGroup"];
            var paramGroup = heroSkillRecord["paramGroup"];
            var effectGroupList = effectGroup.Split('#');
            var paramGroupList = paramGroup.Split(';');
            if (effectGroupList == null || paramGroupList == null || effectGroupList.Length != paramGroupList.Length)
            {//effectGroupList段数和paramGroupList段数不一致
                return;
            }

            for (var i = 0; i < effectGroupList.Length; i++)
            {
                var effectID = effectGroupList[i];
                if (effectID != "1")
                {//不是技能类型就返回
                    continue;
                }

                var skillIDStr = paramGroupList[i];
                var skillIDList = skillIDStr.Split('#');
                if (skillIDList == null || skillIDList.Length < 1)
                {
                    continue;
                }

                var skillID = skillIDList[0];
                AddConfigErr(heroID, heroSkillID, skillLevel, CheckSkillValid(skillID));
            }
        }

        /// <summary>
        /// 检测配置和TimeLine的配置是否一致
        /// </summary>
        /// <param name="skillID"></param>
        /// <returns></returns>
        string CheckSkillValid(string skillID)
        {
            var recordDataList = skillRecordFile.GetRecord("nSkillID", skillID);
            if (recordDataList.Count == 0)
            {
                return "";
            }

            StringBuilder sb = new StringBuilder();

            //<段数,每段触发次数>
            Dictionary<string, int> segmentHit = new Dictionary<string, int>();
            foreach (var recordData in recordDataList)
            {
                //多段伤害表现
                var multistage = recordData["Multistage"];
                if (string.IsNullOrEmpty(multistage) || multistage == "0")
                {
                    //0或者不填默认为1次受击伤害
                    multistage = "1";
                }

                int nMultistage;
                if (!int.TryParse(multistage, out nMultistage))
                {
                    continue;
                }

                var skillResPath = recordData["strPath"];
                if (string.IsNullOrEmpty(skillResPath))
                {
                    continue;
                }

                if (ignoreSkillSet.Contains(skillResPath))
                {//忽略集合
                    continue;
                }

                var parsedSkillName = multistage + "_" + skillResPath;
                if (parsedSkill.ContainsKey(parsedSkillName))
                {//已检测过此技能
                    continue;
                }

                parsedSkill[parsedSkillName] = "1";

                //Windows 系统对路径大小写不敏感，此处先直接使用 asetbundle name 来作为文件路径
                var skillTimeline = AssetDatabase.LoadAssetAtPath<TimelineAsset>("Assets/" + skillResPath);
                if (skillTimeline == null)
                {
                    continue;
                }

                double endTime = skillTimeline.duration;
                double maxOnHitTime = 0;
                foreach (TrackAsset track in skillTimeline.GetRootTracks())
                {
                    foreach (TimelineClip clip in track.GetClips())
                    {
                        var eventClip = clip.asset;
                        if (eventClip == null)
                        {
                            continue;
                        }

                        var assetType = eventClip.GetType();
                        if (assetType.FullName != "EventClip")
                        {
                            continue;
                        }

                        FieldInfo templateField = assetType.GetField("template");
                        var template = templateField.GetValue(eventClip);
                        if (template == null)
                        {
                            continue;
                        }

                        var templateType = template.GetType();
                        FieldInfo eventNameField = templateType.GetField("eventName");
                        string eventName = eventNameField.GetValue(template) as string;
                        if (eventName == null)
                        {
                            continue;
                        }

                        if (eventName == "OnNext" && clip.end < endTime)
                        {
                            endTime = clip.end;
                        }

                        if (eventName != "OnHit")
                        {
                            continue;
                        }

                        //记录 OnHit 最后触发时间
                        if (clip.start > maxOnHitTime)
                        {
                            maxOnHitTime = clip.start;
                        }

                        //记录 Timeline 中配置 OnHit 段数
                        FieldInfo paramField = templateType.GetField("param");
                        string param = paramField.GetValue(template) as string;
                        if (string.IsNullOrEmpty(param))
                        {
                            param = "0";
                        }

                        int onHitCount = 0;
                        segmentHit.TryGetValue(param, out onHitCount);
                        segmentHit[param] = ++onHitCount;
                    }
                }

                for (var i = 0; i < nMultistage; i++)
                {
                    int onHitCount = 0;
                    if (!segmentHit.TryGetValue(i.ToString(), out onHitCount) || onHitCount != 1)
                    {
                        bool bFirst = string.IsNullOrEmpty(sb.ToString());

                        if (bFirst)
                        {
                            sb.Append("技能ID:" + skillID + "," + skillResPath + ",以下段数攻击次数不为1,");
                        }
                        else
                        {
                            sb.Append("||");
                        }

                        sb.Append("segment:" + (i + 1) + ",OnHit 次数:" + onHitCount);
                    }
                }

                if (endTime <= maxOnHitTime)
                {
                    if(string.IsNullOrEmpty(sb.ToString()))
                    {
                        sb.Append("技能ID:" + skillID + "," + skillResPath + ",");
                    }
                    else
                    {
                        sb.Append("||");
                    }

                    sb.Append("OnNext 在 OnHit 之前触发，多段伤害不能完整触发");
                }
            }

            return sb.ToString();
        }

        /// <summary>
        /// 添加配置错误信息
        /// </summary>
        /// <param name="heroID"></param>
        /// <param name="skillID"></param>
        /// <param name="skillLev"></param>
        /// <param name="strError"></param>
        void AddConfigErr(string heroID, string skillID, string skillLev, string strError)
        {
            if(string.IsNullOrEmpty(strError))
            {
                return;
            }

            if (configErrDic == null)
            {
                configErrDic = new Dictionary<string, List<string>>();
            }

            var key = string.Format("HeroID:{0},SkillID:{1},SkillLev:{2}", heroID, skillID, skillLev);
            List<string> errorList = null;
            if (!configErrDic.TryGetValue(key, out errorList))
            {
                errorList = new List<string>();
            }

            if (!errorList.Contains(strError))
            {
                errorList.Add(strError);
            }

            configErrDic[key] = errorList;
        }

        /// <summary>
        /// 处理配置错误信息
        /// </summary>
        void ProcessConfigErr()
        {
            if (configErrDic != null && configErrDic.Count > 0)
            {
                foreach (var t in configErrDic)
                {
                    var k = t.Key;
                    var tList = t.Value;

                    int addedIndex = 0;
                    bool bShowIndex = tList.Count > 1;
                    foreach (var g in tList)
                    {
                        var reasonTips = $"{g}";
                        string path = k;
                        if (bShowIndex)
                        {
                            path = k + "(" + addedIndex + ")";
                        }

                        var ftd = new FileTipsDetail() { strTips = $"ConfigHeroSkillChecker:{reasonTips})", strFilePath = path, checkType = CheckType, strGroupJsonValue = KeyDesc };
                        SetFileTipsDetailJsonInfo(ftd);
                        ftd.strJsonValue = reasonTips;
                        addedIndex++;

                        checkList.Add(ftd);
                    }
                }
            }
        }

        /// <summary>
        /// 执行检测
        /// </summary>
        /// <param name="tipsFiles"></param>
        public override void ExecuteCheck(List<FileTipsDetail> tipsFiles)
        {
            base.ExecuteCheck(tipsFiles);

            try
            {
                //英雄表
                var heroCsvPath = Path.GetFullPath(csvDir + "/" + heroCSVName).Replace("\\", "/");
                heroRecordFile = CSVParser.Load(heroCsvPath);
                if (heroRecordFile == null)
                {
                    return;
                }

                //加载英雄技能表
                var heroSkillCSVPath = Path.GetFullPath(csvDir + "/" + heroSkillCSVName).Replace("\\", "/");
                heroSkillRecordFile = CSVParser.Load(heroSkillCSVPath);
                if (heroSkillRecordFile == null)
                {
                    return;
                }

                //加载技能表
                var skillCSVPath = Path.GetFullPath(csvDir + "/" + skillCSVName);
                skillRecordFile = CSVParser.Load(skillCSVPath);
                if (skillRecordFile == null)
                {
                    return;
                }

                var recordMax = heroRecordFile.MaxRecord();
                if(recordMax <= 0)
                {//csv数据为空
                    return;
                }

                //每次检测条数索引保存供下次使用
                var lastCheckRowIndex = PlayerPrefs.GetInt(heroRowIndexPP, 0);
                var nowMaxRowIndex = lastCheckRowIndex + checkNum;
                bool bExceed = false;
                if(nowMaxRowIndex >= recordMax - 1)
                {
                    bExceed = true;
                    nowMaxRowIndex = recordMax;
                }

                PlayerPrefs.SetInt(heroRowIndexPP, bExceed ? 0 : nowMaxRowIndex);
                PlayerPrefs.Save();

                for (int i = lastCheckRowIndex; i < nowMaxRowIndex; i++)
                {
                    var tRecord = heroRecordFile.GetRecord(i);
                    if (tRecord != null)
                    {
                        CheckConfig(tRecord);
                    }
                }

                ProcessConfigErr();

                if (checkList != null && checkList.Count > 0)
                {
                    checkList.Sort((l, r) => 
                    {
                        var sortSeqCom = l.sortSeq - r.sortSeq;
                        if (sortSeqCom > 0)
                        {
                            return -1;
                        }
                        else if (sortSeqCom == 0)
                        {
                            return 0;
                        }
                        else
                        {
                            return 1;
                        }
                    });

                    tipsFiles.AddRange(checkList);
                }
            }
            catch (Exception e)
            {
                UnityEngine.Debug.Log($"【ResSettingChecker.ConfigHeroSkillChecker.ExecuteCheck,捕获到异常:{e.ToString()}】");
            }
        }
    }
}

