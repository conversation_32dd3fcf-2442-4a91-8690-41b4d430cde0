/*=================================================================================
* 创建者:刘军
* 功能描述:检查prefab上是否有Card Script脚本miss
* 包含功能:1.检查prefab上是否有Card Script脚本miss
*=================================================================================*/
using System.Collections.Generic;
using UnityEngine;
using War.Battle;

/// <summary>
/// 检查prefab上是否有Card Script脚本miss
/// </summary>
public partial class ResSettingChecker
{
    /// <summary>
    /// 检查prefab上是否有Card Script脚本miss
    /// 参数说明:包含一个参数 eg:Assets/Animations/Characters
    /// 参数1为string类型,目录,参数1可以不填,不填就默认指定为Assets/目录
    /// </summary>
    class PrefabCardScriptMissingChecker : PrefabMissingChecker
    {
        /// <summary>
        /// 键
        /// </summary>
        public override string KeyDesc { get { return prefabKeyString + resSettingCheckKeySplit + cardScriptMissingKeyString; } }
        /// <summary>
        /// 检测类型
        /// </summary>
        public override ResSetingsCheckType CheckType { get { return ResSetingsCheckType.PrefabCardScriptMissing; } }

        /// <summary>
        /// 查找丢失
        /// </summary>
        /// <param name="path"></param>
        /// <param name="tGameObj"></param>
        protected override void FindPrefabMissing(string path, GameObject tGameObj)
        {
            if (tGameObj != null)
            {
                var tPath = path.Replace("\\", "/").ToLower();
                if (!tPath.Contains("/edit_"))
                {
                    return;
                }

                if (tGameObj.GetComponent<CardConfig>() == null || tGameObj.GetComponent<Card>() == null)
                {
                    AddMissingTransform(path, tGameObj, tGameObj);
                }
            }
        }

        /// <summary>
        /// 处理Prefab丢失脚本
        /// </summary>
        /// <param name="checkList"></param>
        /// <param name="strReasonTips"></param>
        /// <param name="strTips"></param>
        protected override void ProcessPrefabMiss(List<FileTipsDetail> checkList, string strReasonTips, string strTips)
        {
            strReasonTips = "CardConfig或Card脚本缺失:";
            strTips = "PrefabCardScriptMissingChecker";

            base.ProcessPrefabMiss(checkList, strReasonTips, strTips);
        }
    }
}

