/*=================================================================================
* 创建者:刘军
* 功能描述:图片Readable(Read/Write Enable)资源设置检测
* 包含功能:1.图片Readable(Read/Write Enable)资源设置检测
*=================================================================================*/
using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using EditorUtilitys;

/// <summary>
/// TextureReadable开启设置检测
/// </summary>
public partial class ResSettingChecker
{
    /// <summary>
    /// TextureReadable开启设置检测
    /// 参数说明:两个参数eg:TRUE,Assets/UI
    /// 参数1为bool类型Read/Write Enable的设置开启或关闭检测
    /// 参数2为string类型,目录
    /// </summary>
    class TexReadableChecker : ResSetingsCheckerBase
    {
        /// <summary>
        /// 目录
        /// </summary>
        public string dir;
        /// <summary>
        /// 开启
        /// </summary>
        public bool enable;
        /// <summary>
        /// 键
        /// </summary>
        public override string KeyDesc { get { return textureKeyString + resSettingCheckKeySplit + textureReadableKeyString; } }
        /// <summary>
        /// 检测类型
        /// </summary>
        public override ResSetingsCheckType CheckType { get { return ResSetingsCheckType.TextureReadableCheck; } }

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="strParam"></param>
        /// <param name="lineNum"></param>
        public override void Init(string strParam, int lineNum)
        {
            base.Init(strParam, lineNum);

            if (!string.IsNullOrEmpty(strParam))
            {
                var paramsArr = strParam.Split(',');
                if (paramsArr != null && paramsArr.Length >= 2)
                {
                    try
                    {
                        enable = bool.Parse(paramsArr[0]);
                        dir = paramsArr[1];
                        bParamValid = true;
                    }
                    catch (Exception e)
                    {
                        UnityEngine.Debug.Log($"【TexReadableChecker.Init,捕获到异常:{e.ToString()}】");
                    }
                }
            }

            if (!bParamValid)
            {
                Debug.LogError($"TextureReadableSettingChecker类型参数配置错误:{strParam}");
            }
        }

        /// <summary>
        /// 执行检测
        /// </summary>
        /// <param name="tipsFiles"></param>
        public override void ExecuteCheck(List<FileTipsDetail> tipsFiles)
        {
            base.ExecuteCheck(tipsFiles);

            if (string.IsNullOrEmpty(dir))
            {
                return;
            }

            List<string> filesList = new List<string>();
            FileUtilitys.GetFilesOnDir(dir, filesList, picCheckTypeFilter);
            List<FileTipsDetail> checkList = new List<FileTipsDetail>();

            foreach (var t in filesList)
            {
                var assetPath = t.Replace(Application.dataPath, "Assets");
                TextureImporter textureImporter = AssetImporter.GetAtPath(assetPath) as TextureImporter;
                if (textureImporter != null)
                {
                    var isReadable = textureImporter.isReadable;
                    if (isReadable == enable)
                    {
                        var ftd = new FileTipsDetail() { strTips = $"图片isReadable设置为{enable}", strFilePath = assetPath, checkType = CheckType, strGroupJsonValue = KeyDesc };
                        SetFileTipsDetailLastCommitInfo(assetPath, ftd);
                        SetFileTipsDetailJsonInfo(ftd);
                        ftd.sortSeq = ftd.svnDateTicks;

                        checkList.Add(ftd);
                    }
                }
            }

            if (checkList != null && checkList.Count > 0)
            {
                checkList.Sort((l, r) => 
                {
                    var sortSeqCom = l.sortSeq - r.sortSeq;
                    if (sortSeqCom > 0)
                    {
                        return -1;
                    }
                    else if (sortSeqCom == 0)
                    {
                        return 0;
                    }
                    else
                    {
                        return 1;
                    }
                });

                tipsFiles.AddRange(checkList);
            }
        }
    }
}

