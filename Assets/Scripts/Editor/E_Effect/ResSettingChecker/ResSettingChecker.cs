/*=================================================================================
* 创建者:刘军
* 功能描述:资源设置检测
* 包含功能:1.图片Mipmap资源设置检测
*=================================================================================*/
using System;
using System.Collections.Generic; 
using System.IO;
using System.Text;
using UnityEditor;
using UnityEngine;
using EditorUtilitys;

/// <summary>
/// 资源设置检测
/// </summary>
public partial class ResSettingChecker
{
    /// <summary>
    /// 文件提示详情
    /// </summary>
    class FileTipsDetail
    {
        /// <summary>
        /// 提示
        /// </summary>
        public string strTips;
        /// <summary>
        /// 文件路径
        /// </summary>
        public string strFilePath;
        /// <summary>
        /// 最后一次提交人
        /// </summary>
        public string strSvnAuthor;
        /// <summary>
        /// 排序序号(eg.按名字分组用)
        /// </summary>
        [System.NonSerialized]
        public long sortSeq;
        /// <summary>
        /// 最后一次提交时间
        /// </summary>
        public string strSvnDate;
        /// <summary>
        /// 最后一次提交时间Tick
        /// </summary>
        public long svnDateTicks;
        /// <summary>
        /// 输出json时value
        /// </summary>
        public string strJsonValue;
        /// <summary>
        /// 检测类型
        /// </summary>
        public ResSetingsCheckType checkType;
        /// <summary>
        /// 输出json时Group key
        /// </summary>
        public string strGroupJsonValue;
        /// <summary>
        /// 同一类型总数
        /// </summary>
        public int theSameCount;
        /// <summary>
        /// 组内索引
        /// </summary>
        public int groupIndex;
    }

    /// <summary>
    /// 钉钉群通知token
    /// </summary>
    static string dingTalkToken = "59fa1f9400a22f231324ed0fd436dd83fd548a98014b019d15ff9faecf503212";
    /// <summary>
    /// 项目名称
    /// </summary>
    private static string projectName = "苍穹国内";
    /// <summary>
    /// 上传文件目录
    /// </summary>
    static string upFileLogDir = "E:/Roguelike_Projects/HServer/ResSettingCheck/";
    /// <summary>
    /// 上传文件目录url(钉钉链接用)
    /// </summary>
    static string upFileLogDirUrl = "http://172.18.0.138:8001/ResSettingCheck/";
    /// <summary>
    /// svn 安装路径
    /// </summary>
    private static string svnExePath = "C:/Program Files/TortoiseSVN/bin/svn.exe";
    /// <summary>
    /// 最近一段时间内提交
    /// </summary>
    static int submitRecentPeriod = 7;
    /// <summary>
    /// 最近一段时间内提交tick
    /// </summary>
    static long submitRecentPeriodTicks = 0;
    /// <summary>
    /// 唯一标识
    /// </summary>
    private static string uniqueIdentity = "122";
    /// <summary>
    /// 项目svn url
    /// </summary>
    private static string svnRepositoryUrl = "http://************/res_svn7/MGame/yHero220726/BinClient/Client/";
    /// <summary>
    /// 资源配置设置模板
    /// </summary>
    private static string resSettingPath = "Assets/Scripts/Editor/E_Effect/ResSettingChecker/ResSettingTemplate.txt";
    /// <summary>
    /// 资源配置设置检测映射表
    /// </summary>
    private static string resSettingCheckerMapPath = "Assets/Scripts/Editor/E_Effect/ResSettingChecker/ResSettingCheckerMap.txt";
    /// <summary>
    /// 资源配置设置检测对应描述映射表
    /// </summary>
    private static string checkerDescMapPath = "Assets/Scripts/Editor/E_Effect/ResSettingChecker/CheckTypeDescMap.txt";
    /// <summary>
    /// 设置检测类型字典
    /// </summary>
    private static Dictionary<string, Type> settingCheckTypesDic = null;
    /// <summary>
    /// 当前检测类型
    /// </summary>
    private static ResSetingsCheckType nowResSetingsCheckType = ResSetingsCheckType.None;
    /// <summary>
    /// 参数字典
    /// </summary>
    static Dictionary<string, System.Object> paramsBlackBoard;
    /// <summary>
    /// 检测类型对应描述字典
    /// </summary>
    private static Dictionary<int, string> checkTypeDescDic = null;

    //[MenuItem("Tool/ResSettingChecker/资源设置检测")]
    //public static void CheckPicMipSettingsDefault()
    //{
    //    //bool withBom = true;
    //    //var encoding = FileUtilitys.DetectEncode("E:/yHero220726/BinClient/Client/Assets/Lua/ads/ad_config.txt", ref withBom);
    //    //Debug.LogError(encoding.EncodingName + ":" + withBom);
    //    //return;

    //    submitRecentPeriod = 180;
    //    SetSubmitRecentPeriodTicks();
    //    settingCheckTypesDic = null;
    //    InitSettingCheckTypesDic();
    //    ClearParamsBlackBoard();
    //    nowResSetingsCheckType = ResSetingsCheckType.TextureMipmapCheck;
    //    var templateStr = FormatResSettingsString();
    //    if (string.IsNullOrEmpty(templateStr))
    //    {
    //        return;
    //    }
    //    var cache = new Dictionary<int, string>();
    //    int count = 0;
    //    var lines = templateStr.Split('&');
    //    for (int i = 0; i < lines.Length; i++)
    //    {
    //        cache[count++] = lines[i];
    //    }

    //    AnalyzeSettingCheckerDesc();
    //    List<ResSetingsCheckerBase> resSetingsBaseList = AnalyzeSettingCheckerOnParam(cache);
    //    if (resSetingsBaseList.Count <= 0)
    //    {
    //        Debug.LogError("获取参数配置类型对应的处理类为空!!!!!!");
    //        return;
    //    }

    //    ExecuteCheckProcess(resSetingsBaseList);
    //}

    [MenuItem("Tool/ResSettingChecker/资源设置格式化输出字符串(查看debug输出)")]
    public static string FormatResSettingsString()
    {
        var path = FileUtilitys.GetCurFullPathOnAssets(resSettingPath);
        if(string.IsNullOrEmpty(path) || !File.Exists(path))
        {//兼容使用csv
            path = FileUtilitys.GetCurFullPathOnAssets(resSettingPath.Replace(".txt", ".csv"));
        }

        if(!string.IsNullOrEmpty(path) && File.Exists(path))
        {
            using (StreamReader ReaderObject = new StreamReader(path))
            {
                string line;
                StringBuilder sb = new StringBuilder();
                int fileCount = 0;
                while ((line = ReaderObject.ReadLine()) != null)
                {
                    if(line.StartsWith("#"))
                    {//注释行
                        continue;
                    }

                    if(fileCount != 0)
                    { 
                        sb.Append("&");
                    }

                    fileCount++;
                    sb.Append(line);
                }

                string valueColor = "yellow";
                Debug.LogError($"资源设置格式化输出字符串: <color={ valueColor }>{ sb.ToString()}</color>");

                return sb.ToString();
            }
        }

        return string.Empty;
    }

    [MenuItem("Tool/ResSettingChecker/Jenkins检测参数查看(剪贴板查看debug输出)")]
    public static string DebugResSettingsJenkinsCheckParams()
    {
        var str = LogHelp.clipboard;
        if (!string.IsNullOrEmpty(str))
        {
            var lines = str.Split('&');
            if (lines == null || lines.Length <= 0)
            {
                return string.Empty;
            }

            var lineLen = lines.Length;
            StringBuilder sb = new StringBuilder();
            sb.Append("Jenkins检测参数输出:\n");
            for (int i = 0; i < lines.Length; i++)
            {
                var line = lines[i];
                var vals = line.Split(',');
                if (vals != null && vals.Length >= 1)
                {
                    sb.Append(line);
                    if(i != lineLen - 1)
                    {
                        sb.Append("\n");
                    }
                }
            }

            Debug.LogError(sb.ToString());

        }

        return string.Empty;
    }

    /// <summary>
    /// 初始化设置检测类型字典
    /// </summary>
    static void InitSettingCheckTypesDic()
    {
        if (settingCheckTypesDic == null)
        {
            settingCheckTypesDic = new Dictionary<string, Type>();
            var path = FileUtilitys.GetCurFullPathOnAssets(resSettingCheckerMapPath);

            if (!string.IsNullOrEmpty(path) && File.Exists(path))
            {
                using (StreamReader ReaderObject = new StreamReader(path))
                {
                    string line;
                    StringBuilder sb = new StringBuilder();
                    while ((line = ReaderObject.ReadLine()) != null)
                    {
                        if (line.StartsWith("#"))
                        {//注释行
                            continue;
                        }

                        var contents = line.Split('=');
                        if(contents != null && contents.Length >= 2)
                        {
                            var key = contents[0];
                            var checkType = contents[1];
                            try
                            {
                                settingCheckTypesDic[key.ToLower()] = Type.GetType(checkType);
                            }
                            catch(Exception e)
                            {
                                UnityEngine.Debug.LogError($"【ResSettingChecker.InitSettingCheckTypesDic】解析解析检测类型映射出错{e.ToString()}!!!!!!");
                            }
                        }
                    }
                }
            }
        }
    }

    /// <summary>
    /// 获取指定设置类型
    /// </summary>
    /// <param name="str"></param>
    /// <returns></returns>
    static Type GetTypeInCheckSettingsDic(string str)
    {
        if (string.IsNullOrEmpty((str)) || settingCheckTypesDic == null)
        {
            return null;
        }

        settingCheckTypesDic.TryGetValue(str.ToLower(), out var retType);
        return retType;
    }

    /// <summary>
    /// 清空字典数据
    /// </summary>
    static void ClearParamsBlackBoard()
    {
        paramsBlackBoard?.Clear();
        paramsBlackBoard = null;
    }

    /// <summary>
    /// 设置最近一段时间内提交Ticks
    /// </summary>
    static void SetSubmitRecentPeriodTicks()
    {
        submitRecentPeriodTicks = DateTime.Now.AddDays(submitRecentPeriod * -1).Ticks;
    }

    /// <summary>
    /// 资源设置检测入口
    /// </summary>
    public static void ResSettingCheck()
    {
        ///解析参数
        var paramsDic = GetCheckParams();
        if(paramsDic == null || paramsDic.Count <= 0)
        {
            Debug.Log($"【ResSettingChecker.ResSettingCheck】解析resSettingCheckParams参数错误!!!!!!");
            return;
        }

        projectName = GetProjectNameParam();
        upFileLogDir = GetUpFileLogDirParam();
        upFileLogDirUrl = GetUpFileLogDirUrlParam();
        svnExePath = GetSvnExePathParam();
        svnRepositoryUrl = GetSvnRepositoryUrlParam();
        dingTalkToken = GetDingTalkTokenParam();
        nowResSetingsCheckType = GetResSetingsCheckTypeParam();
        submitRecentPeriod = GetSubmitRecentPeriodParam();
        uniqueIdentity = GetUniqueIdentityParam();
        SetSubmitRecentPeriodTicks();

        StringBuilder sb = new StringBuilder();
        sb.Append("ProjectName:").Append(projectName).Append("\n");
        sb.Append("UpFileLogDir:").Append(upFileLogDir).Append("\n");
        sb.Append("UpFileLogDirUrl:").Append(upFileLogDirUrl).Append("\n");
        sb.Append("SvnExePath:").Append(svnExePath).Append("\n");
        sb.Append("SvnRepositoryUrl:").Append(svnRepositoryUrl).Append("\n");
        sb.Append("DingTalkToken:").Append(dingTalkToken);

        Debug.Log($"【ResSettingChecker.ResSettingCheck】解析Svn参数:\n{sb.ToString()}");
        sb.Clear();

        if (string.IsNullOrEmpty(upFileLogDir) || string.IsNullOrEmpty(upFileLogDirUrl) ||
            string.IsNullOrEmpty(svnExePath) || string.IsNullOrEmpty(svnRepositoryUrl) ||
            string.IsNullOrEmpty(dingTalkkeyWord))
        {
            Debug.Log("【ResSettingChecker.ResSettingCheck(有变量的值为空)】错误返回");
            return;
        }

        InitSettingCheckTypesDic();
        ClearParamsBlackBoard();

        AnalyzeSettingCheckerDesc();
        List<ResSetingsCheckerBase> resSetingsBaseList = AnalyzeSettingCheckerOnParam(paramsDic);

        if (resSetingsBaseList.Count <= 0)
        {
            Debug.LogError("获取参数配置类型对应的处理类为空!!!!!!");
            return;
        }

        ExecuteCheckProcess(resSetingsBaseList);

        ClearParamsBlackBoard();
        Debug.Log("【资源设置检测完成!!!!!!】");
    }

    /// <summary>
    /// 解析资源设置检测描述信息
    /// </summary>
    /// <returns></returns>
    static void AnalyzeSettingCheckerDesc()
    {
        if (checkTypeDescDic == null)
        {
            checkTypeDescDic = new Dictionary<int, string>();
        }

        checkTypeDescDic.Clear();

        var path = FileUtilitys.GetCurFullPathOnAssets(checkerDescMapPath);
        if (!string.IsNullOrEmpty(path) && File.Exists(path))
        {
            using (StreamReader ReaderObject = new StreamReader(path))
            {
                string line;
                StringBuilder sb = new StringBuilder();
                while ((line = ReaderObject.ReadLine()) != null)
                {
                    if (line.StartsWith("#"))
                    {//注释行
                        continue;
                    }

                    var contents = line.Split('=');
                    if (contents != null && contents.Length >= 2)
                    {
                        var checkType = contents[0];
                        var desc = contents[1];
                        try
                        {
                            if(!string.IsNullOrEmpty(checkType))
                            {
                                bool isDefined = Enum.IsDefined(typeof(ResSetingsCheckType), checkType);
                                if (isDefined)
                                {
                                    var enuCheckType = (ResSetingsCheckType)Enum.Parse(typeof(ResSetingsCheckType), checkType);
                                    checkTypeDescDic[(int)enuCheckType] = desc;
                                }
                            }
                        }
                        catch (Exception e)
                        {
                            UnityEngine.Debug.LogError($"【ResSettingChecker.AnalyzeSettingCheckerDesc】解析解析检测类型对应描述映射出错{e.ToString()}!!!!!!");
                        }
                    }
                }
            }
        }
    }

    /// <summary>
    /// 获取检测类型描述信息
    /// </summary>
    /// <param name="checkType"></param>
    /// <returns></returns>
    static string GetCheckTypeDesc(int checkType)
    {
        if(checkTypeDescDic == null)
        {
            return string.Empty;
        }

        if(checkTypeDescDic.TryGetValue(checkType, out var result))
        {
            return result;
        }

        return string.Empty;
    }

    /// <summary>
    /// 解析资源设置检测
    /// </summary>
    /// <param name="paramsDic"></param>
    /// <returns></returns>
    static List<ResSetingsCheckerBase> AnalyzeSettingCheckerOnParam(Dictionary<int, string> paramsDic)
    {
        List<ResSetingsCheckerBase> resSetingsBaseList = new List<ResSetingsCheckerBase>();
        foreach (var t in paramsDic)
        {
            var lineNum = t.Key;
            var line = t.Value;
            var vals = line.Split(',');
            var index = line.IndexOf(',');
            if (vals != null && vals.Length >= 1)
            {
                var key = vals[0];
                var retType = GetTypeInCheckSettingsDic(key);
                if (retType != null)
                {
                    var cc = System.Activator.CreateInstance(retType) as ResSetingsCheckerBase;
                    if (cc != null)
                    {
                        cc.Init(index >= 0 ? line.Substring(index + 1) : "", lineNum);
                        if(cc.bParamValid)
                        {
                            resSetingsBaseList.Add(cc);
                        }
                    }
                }
            }
        }

        return resSetingsBaseList;
    }

    /// <summary>
    /// 执行检测过程
    /// </summary>
    /// <param name="resSetingsBaseList"></param>
    static void ExecuteCheckProcess(List<ResSetingsCheckerBase> resSetingsBaseList)
    {
        List<FileTipsDetail> tipsFiles = new List<FileTipsDetail>();
        foreach (var t in resSetingsBaseList)
        {
            t.ExecuteCheck(tipsFiles);
        }
        
        OutputReports(tipsFiles);
    }

    #region 获取jenkins参数

    /// <summary>
    /// 获取项目名称
    /// </summary>
    /// <returns></returns>
    static string GetProjectNameParam()
    {
        return JenkinsUtilitys.GetParams(ResSettingChecker.JPProjectName);
    }

    /// <summary>
    /// 项目工程svn url
    /// </summary>
    /// <returns></returns>
    static string GetSvnRepositoryUrlParam()
    {
        return JenkinsUtilitys.GetParams(ResSettingChecker.JPSvnRepositoryUrl);
    }

    /// <summary>
    /// svn安装路径
    /// </summary>
    /// <returns></returns>
    static string GetSvnExePathParam()
    {
        var strParam = JenkinsUtilitys.GetParams(ResSettingChecker.JPSvnExePath);
        if (!string.IsNullOrEmpty(strParam))
        {
            return strParam.Replace("@", " ");
        }

        return strParam;
    }

    /// <summary>
    /// 获取上传文件目录
    /// </summary>
    /// <returns></returns>
    static string GetUpFileLogDirParam()
    {
        return JenkinsUtilitys.GetParams(ResSettingChecker.JPUpFileLogDir);
    }

    /// <summary>
    /// 获取上传文件目录url(钉钉链接用)
    /// </summary>
    /// <returns></returns>
    static string GetUpFileLogDirUrlParam()
    {
        return JenkinsUtilitys.GetParams(ResSettingChecker.JPUpFileLogDirUrl);
    }

    /// <summary>
    /// 获取资源设置CheckParams参数
    /// </summary>
    /// <returns></returns>
    static Dictionary<int, string> GetCheckParams()
    {
        string jeParam = JenkinsEnv.Instance.Get(ResSettingChecker.JPCheckParams);
        if (string.IsNullOrEmpty(jeParam))
        {
            Debug.LogError("svn参数resSettingCheckParams获取出错!!!!!!");
            return null;
        }

        var cache = new Dictionary<int, string>();
        int count = 0;
        var lines = jeParam.Split('&');
        for (int i = 0; i < lines.Length; i++)
        {
            cache[count++] = lines[i];
        }

        return cache;
    }

    /// <summary>
    /// 获取钉钉群配置参数
    /// </summary>
    /// <returns></returns>
    static string GetDingTalkTokenParam()
    {
        return JenkinsUtilitys.GetParams(ResSettingChecker.JPDingTalkToken);
    }

    /// <summary>
    /// 获取检测设置类型枚举
    /// </summary>
    /// <returns></returns>
    static ResSetingsCheckType GetResSetingsCheckTypeParam()
    {
        ResSetingsCheckType retType = ResSetingsCheckType.None;
        var retString = JenkinsUtilitys.GetParams(ResSettingChecker.JPCheckType);
        if (!string.IsNullOrEmpty(retString))
        {
            if (retString == resSetingsCheckMipmapEnable)
            {
                retType = ResSetingsCheckType.TextureMipmapCheck;
            }
        }

        return retType;
    }

    /// <summary>
    /// 获取最近一段时间内提交
    /// </summary>
    /// <returns></returns>
    static int GetSubmitRecentPeriodParam()
    {
        int retValue = 7;
        var retString = JenkinsUtilitys.GetParams(ResSettingChecker.JPSubmitRecentPeriod);
        if (!string.IsNullOrEmpty(retString))
        {
            retValue = int.Parse(retString);
        }

        return retValue;
    }

    /// <summary>
    /// 获取唯一标识
    /// </summary>
    /// <returns></returns>
    static string GetUniqueIdentityParam()
    {
        return JenkinsUtilitys.GetParams(ResSettingChecker.JPUniqueIdentity);
    }

    #endregion 获取jenkins参数

    /// <summary>
    /// 设置param参数
    /// </summary>
    /// <param name="strKey"></param>
    /// <param name="value"></param>
    static void SetParam(string strKey, System.Object value)
    {
        if(string.IsNullOrEmpty(strKey))
        {
            return;
        }

        if(paramsBlackBoard == null)
        {
            paramsBlackBoard = new Dictionary<string, object>();
        }

        paramsBlackBoard[strKey] = value;
    }

    /// <summary>
    /// 获取param参数
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="strKey"></param>
    /// <returns></returns>
    static T GetParam<T>(string strKey)
    {
        T ret = default(T);

        if (paramsBlackBoard != null)
        {
            if(paramsBlackBoard.TryGetValue(strKey, out var result))
            {
                if(result != null && result is T)
                {
                    return (T)result;
                }
            }
        }

        return ret;
    }

    /// <summary>
    /// 获取保存文件路径
    /// </summary>
    /// <param name="upFileLogDir"></param>
    /// <param name="upFileName"></param>
    /// <returns></returns>
    static string GetSaveFilePath(string upFileLogDir, string upFileName)
    {
        string saveFileName = upFileLogDir + upFileName;
        saveFileName = saveFileName.Replace("\\", "/");

        return saveFileName;
    }

    /// <summary>
    /// 获取最近一段时间提交文件列表
    /// </summary>
    /// <param name="tipsFiles"></param>
    /// <returns></returns>
    static List<FileTipsDetail> GetSubmitRecentPeriod(List<FileTipsDetail> tipsFiles)
    {
        List<FileTipsDetail> retList = new List<FileTipsDetail>();
        if(tipsFiles != null)
        {
            foreach(var t in tipsFiles)
            {
                if(t.svnDateTicks >= submitRecentPeriodTicks)
                {
                    retList.Add(t);
                }
            }
        }

        return retList;
    }

    /// <summary>
    /// 输出报告
    /// </summary>
    /// <param name="tipsFiles"></param>
    static void OutputReports(List<FileTipsDetail> tipsFiles)
    {
        string saveFileDir = upFileLogDir;
        FileUtilitys.CheckDirectory(saveFileDir);
        var upFileNameTmp = string.Format(upFileName, uniqueIdentity);
        var upRecentFileNameTmp = string.Format(upRecentFileName, uniqueIdentity);

        string saveNowFileName = FileUtilitys.CombineFileNameWithCurTime(upFileNameTmp);
        string saveFileName = GetSaveFilePath(upFileLogDir, saveNowFileName);
        string saveFileNameUrl = GetSaveFilePath(upFileLogDirUrl, saveNowFileName);

        string upRecentNowFileName = FileUtilitys.CombineFileNameWithCurTime(upRecentFileNameTmp);
        string saveRecentFileName = GetSaveFilePath(upFileLogDir, upRecentNowFileName);
        string saveRecentFileNameUrl = GetSaveFilePath(upFileLogDirUrl, upRecentNowFileName);

        //删除目录下的所有.json文件
        FileUtilitys.DelFilesOnDir(upFileLogDir, ".json");

        //写入文件
        //string output = Newtonsoft.Json.JsonConvert.SerializeObject(tipsFiles, Newtonsoft.Json.Formatting.Indented);
        //File.WriteAllText(saveFileName, output);
        var result = ExportSimplyClear(tipsFiles, saveFileName);
        ExportSimplyClear(GetSubmitRecentPeriod(tipsFiles), saveRecentFileName);
        if (result == null || result.results.Count <= 0)
        {
            return;
        }

        //上传钉钉
        System.Text.StringBuilder sb = new StringBuilder();
        bool atAll = false;
        bool sendDingTalkMsg = true;
        sb.Append(dingTalkkeyWord).Append("\n");
        sb.Append("对应项目:").Append(projectName).Append("\n");
        sb.Append("SVN地址:").Append(svnRepositoryUrl).Append("\n");
        sb.Append("结果日志:").Append(saveFileNameUrl).Append("\n");
        sb.Append($"{submitRecentPeriod}天内结果日志:").Append(saveRecentFileNameUrl).Append("\n");
        PostDoCommonOperationExtra(sb, result, tipsFiles, ref atAll, ref sendDingTalkMsg);

        if(sendDingTalkMsg)
        {
            DingDingUtilitys.SendToDingDingByTextType(dingTalkToken, "检测输出结果", sb.ToString(), atAll);
        }
    }

    /// <summary>
    /// 通用操作后的额外处理
    /// </summary>
    /// <param name="sb"></param>
    /// <param name="vo"></param>
    /// <param name="tipsFiles"></param>
    /// <param name="atAll"></param>
    /// <param name="sendDingTalkMsg"></param>
    static void PostDoCommonOperationExtra(StringBuilder sb, ResultExportSimplyClear vo, List<FileTipsDetail> tipsFiles, ref bool atAll, ref bool sendDingTalkMsg)
    {
        atAll = false;
        sendDingTalkMsg = true;
        if (nowResSetingsCheckType == ResSetingsCheckType.TextureMipmapCheck)
        {
            DoMipmapEnableCheckExtra(sb, vo, tipsFiles, ref atAll, ref sendDingTalkMsg);
        }
    }
}
 
