/*=================================================================================
* 创建者:刘军
* 功能描述:图片Mipmap资源设置检测
* 包含功能:1.图片Mipmap资源设置检测
*=================================================================================*/
using System;
using System.Collections.Generic;
using System.Text;
using UnityEditor;
using UnityEngine;
using EditorUtilitys;

/// <summary>
/// 图片Mipmap资源设置检测
/// </summary>
public partial class ResSettingChecker
{
    /// <summary>
    /// 图片检测格式
    /// </summary>
    private static string picCheckTypeFilter = ".png,.jpg,jpeg,.tga";

    /// <summary>
    /// ResSetingsCheckerBase基类
    /// </summary>
    class ResSetingsCheckerBase
    {
        /// <summary>
        /// 键
        /// </summary>
        public virtual string KeyDesc { get { return ""; } }
        /// <summary>
        /// 检测类型
        /// </summary>
        public virtual ResSetingsCheckType CheckType { get { return ResSetingsCheckType.None; } }
        /// <summary>
        /// 参数是否有效正确
        /// </summary>
        public bool bParamValid = false;
        /// <summary>
        /// 排序(用于归类)
        /// </summary>
        Dictionary<string, int> sortSeqDic = new Dictionary<string, int>();

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="strParam"></param>
        /// <param name="lineNum"></param>
        public virtual void Init(string strParam, int lineNum)
        {
            
        }
        
        /// <summary>
        /// 执行检测
        /// </summary>
        /// <param name="tipsFiles"></param>
        public virtual void ExecuteCheck(List<FileTipsDetail> tipsFiles)
        {
            
        }

        /// <summary>
        /// 设置最后一次提交信息
        /// </summary>
        /// <param name="findedPath"></param>
        /// <param name="ftd"></param>
        protected void SetFileTipsDetailLastCommitInfo(string findedPath, FileTipsDetail ftd)
        {
            if(ftd != null)
            {
                var info = SvnUtilitys.GetLastCommitInfo(SvnUtilitys.GetLastCommitCmdType.SvnInfo, svnExePath, svnRepositoryUrl, findedPath);
                if(info != null)
                {
                    var strAuthor = info.author;
                    if(!string.IsNullOrEmpty(strAuthor))
                    {
                        strAuthor = strAuthor.Trim();
                    }

                    ftd.strSvnAuthor = strAuthor;
                    ftd.strSvnDate = info.date;

                    var strSvnDate = ftd.strSvnDate;
                    if(!string.IsNullOrEmpty(strSvnDate))
                    {
                        strSvnDate = SvnUtilitys.FormatSvnInfoCmdDate(strSvnDate);
                        if(!string.IsNullOrEmpty(strSvnDate) && DateTime.TryParse(strSvnDate, out var result))
                        {
                            ftd.svnDateTicks = result.Ticks;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 设置输出Json信息
        /// </summary>
        /// <param name="ftd"></param>
        protected void SetFileTipsDetailJsonInfo(FileTipsDetail ftd)
        {
            if (ftd != null)
            {
                var date = SvnUtilitys.FormatSvnInfoCmdDate(ftd.strSvnDate);
                sbForSimplyClear.Clear();
                sbForSimplyClear.Append(resCheckResultKey).Append(",");
                sbForSimplyClear.Append((int)CheckType).Append(",");
                sbForSimplyClear.Append(ftd.strTips).Append(",");
                sbForSimplyClear.Append(ftd.strSvnAuthor).Append(",");
                var strDate = date;
                if (!string.IsNullOrEmpty(strDate))
                {
                    strDate = strDate.TrimEnd();
                }

                sbForSimplyClear.Append(strDate);
                ftd.strJsonValue = sbForSimplyClear.ToString();

                sbForSimplyClear.Clear();
            }
        }

        /// <summary>
        /// 获取序号用于归类排序
        /// </summary>
        /// <param name="strAuthor"></param>
        /// <returns></returns>
        protected int GetSortSeq(string strSort)
        {
            if (!string.IsNullOrEmpty(strSort))
            {
                if (sortSeqDic.TryGetValue(strSort, out var result))
                {
                    return result;
                }
                else
                {
                    return sortSeqDic[strSort] = sortSeqDic.Count + 1;
                }
            }

            return 0;
        }
    }

    /// <summary>
    /// Texture Mipmap设置检测
    /// 参数说明:默认含三个参数 eg:TRUE,Assets/Art/Characters/3d,""
    /// 参数1为bool类型mipmapEnabled的设置开启或关闭检测
    /// 参数2为string类型,目录
    /// 参数3为DateTime类型,指定检测开始的时间,填空就表示不指定时间,否则按照C#的DateTime类型字符串填入
    /// </summary>
    class TexMipmapChecker : ResSetingsCheckerBase
    {
        /// <summary>
        /// 目录
        /// </summary>
        public string dir;
        /// <summary>
        /// 开启
        /// </summary>
        public bool enable;
        /// <summary>
        /// 指定时间后
        /// </summary>
        public long afterTheTimeTicks = -1;
        /// <summary>
        /// 键
        /// </summary>
        public override string KeyDesc { get { return texKeyString + resSettingCheckKeySplit + mipmapKeyString; } }
        /// <summary>
        /// 检测类型
        /// </summary>
        public override ResSetingsCheckType CheckType { get { return ResSetingsCheckType.TextureMipmapCheck; } }
        /// <summary>
        /// 检测日期
        /// </summary>
        DateTime dtTimeCheck = DateTime.Now;

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="strParam"></param>
        /// <param name="lineNum"></param>
        public override void Init(string strParam, int lineNum)
        {
            base.Init(strParam, lineNum);

            if (!string.IsNullOrEmpty(strParam))
            {
                var paramsArr = strParam.Split(',');
                if (paramsArr != null && paramsArr.Length >= 3)
                {
                    try
                    {
                        enable = bool.Parse(paramsArr[0]);
                        dir = paramsArr[1];
                        afterTheTimeTicks = GetAfterTheTimeParams(paramsArr, out dtTimeCheck);
                        bParamValid = true;
                    }
                    catch (Exception e)
                    {
                        UnityEngine.Debug.Log($"【MipmapEnableSetingsChecker.Init,捕获到异常:{e.ToString()}】");
                    }
                }
            }

            if (!bParamValid)
            {
                Debug.LogError($"MipmapEnableSetingsChecker类型参数配置错误:{strParam}");
            }
        }

        /// <summary>
        /// 获取时间描述
        /// </summary>
        /// <returns></returns>
        string GetTheTimeTickDesc()
        {
            string strTimeTickDesc = string.Empty;
            if (afterTheTimeTicks <= -1)
            {
                strTimeTickDesc = "未指定日期";
            }
            else
            {
                strTimeTickDesc = dtTimeCheck.ToString("yyyy-MM-dd HH:mm:ss");
            }

            return strTimeTickDesc;
        }

        /// <summary>
        /// 设置日期描述
        /// </summary>
        void SetTheTimeTickDesc()
        {
            SetParam(mipmapEnableTimeTickParamDesc, GetTheTimeTickDesc());
        }

        /// <summary>
        /// 获取AfterTheTime配置参数
        /// 填空就表示不指定时间,否则就取指定日期ticks
        /// </summary>
        /// <param name="paramsArr"></param>
        /// <param name="dtTimeResult"></param>
        /// <returns></returns>
        long GetAfterTheTimeParams(string[] paramsArr, out DateTime dtTimeResult)
        {
            dtTimeResult = DateTime.Today;

            var afterTimeParam = paramsArr[2];
            if (!string.IsNullOrEmpty(afterTimeParam))
            {
                if (long.TryParse(afterTimeParam, out var timeResult))
                {
                    if (timeResult <= 0)
                    {
                        return -1;
                    }
                }

                if (DateTime.TryParse(afterTimeParam, out var result))
                {
                    dtTimeResult = result;
                    return result.Ticks;
                }
            }
            else
            {
                return -1;
            }

            return -1;
            //DateTime today = DateTime.Today;
            //DateTime todayStart = today.Date + DateTime.MinValue.TimeOfDay;
            //dtTimeResult = todayStart;

            ////return -1;
            //return todayStart.Ticks;
        }

        /// <summary>
        /// 执行检测
        /// </summary>
        /// <param name="tipsFiles"></param>
        public override void ExecuteCheck(List<FileTipsDetail> tipsFiles)
        {
            base.ExecuteCheck(tipsFiles);

            if (string.IsNullOrEmpty(dir))
            {
                return;
            }
            
            List<string> filesList = new List<string>();
            FileUtilitys.GetFilesOnDir(dir, filesList, picCheckTypeFilter);
            List<FileTipsDetail> checkList = new List<FileTipsDetail>();

            foreach (var t in filesList)
            {
                if (!CheckLocalMofifyTimeSatisfy(t))
                {//时间不满足
                    continue;
                }

                var assetPath = t.Replace(Application.dataPath, "Assets");
                TextureImporter textureImporter = AssetImporter.GetAtPath(assetPath) as TextureImporter;
                if(textureImporter != null)
                {
                    var mipmapEnabled = textureImporter.mipmapEnabled;
                    if (mipmapEnabled == enable)
                    {
                        var ftd = new FileTipsDetail() { strTips = $"图片mipmapEnabled设置为{enable}", strFilePath = assetPath, checkType = CheckType , strGroupJsonValue = KeyDesc};
                        SetFileTipsDetailLastCommitInfo(assetPath, ftd);
                        SetFileTipsDetailJsonInfo(ftd);
                        SetTheTimeTickDesc();
                        ftd.sortSeq = ftd.svnDateTicks;

                        checkList.Add(ftd);
                    }
                }
            }

            if (checkList != null && checkList.Count > 0)
            {
                checkList.Sort((l, r) => 
                {
                    var sortSeqCom = l.sortSeq - r.sortSeq;
                    if(sortSeqCom > 0)
                    {
                        return -1;
                    }
                    else if(sortSeqCom == 0)
                    {
                        return 0;
                    }
                    else
                    {
                        return 1;
                    }
                });

                tipsFiles.AddRange(checkList);
            }
        }

        /// <summary>
        /// 检测文件路径本地修改是否在指定时间后
        /// </summary>
        /// <param name="strFilePath"></param>
        /// <returns></returns>
        bool CheckLocalMofifyTimeSatisfy(string strFilePath)
        {
            return FileUtilitys.GetFileLocalLastModifyTimeTicks(strFilePath) >= afterTheTimeTicks;
        }
    }

    /// <summary>
    /// MipmapEnableCheck额外设置操作
    /// </summary>
    /// <param name="sb"></param>
    /// <param name="vo"></param>
    /// <param name="tipsFiles"></param>
    /// <param name="atAll"></param>
    /// <param name="sendDingTalkMsg"></param>
    static void DoMipmapEnableCheckExtra(StringBuilder sb, ResultExportSimplyClear vo, List<FileTipsDetail> tipsFiles, ref bool atAll, ref bool sendDingTalkMsg)
    {
        atAll = false;
        sendDingTalkMsg = true;
        var checkTypeResults = vo.checkTypeResults;
        if (checkTypeResults.TryGetValue((int)nowResSetingsCheckType, out var result))
        {
            if (result > 0)
            {
                atAll = true;
                string strTheTimeDesc = "未指定时间";
                strTheTimeDesc = GetParam<string>(mipmapEnableTimeTickParamDesc);

                string strAuthorsInfo = "";
                if (vo.checkTypeAuthorsResults.TryGetValue((int)nowResSetingsCheckType, out var authorsList))
                {
                    if (authorsList != null)
                    {
                        StringBuilder sbAuthorsInfo = new StringBuilder();
                        foreach (var t in authorsList)
                        {
                            if(string.IsNullOrEmpty(t))
                            {
                                continue;
                            }

                            var pathList = GetReleatedResPathOnAuthors(tipsFiles, t);
                            sbAuthorsInfo.Append("\n");
                            sbAuthorsInfo.Append("提交人" + t + ":");
                            foreach (var path in pathList)
                            {
                                sbAuthorsInfo.Append("\n");
                                sbAuthorsInfo.Append(path);
                            }
                        }

                        strAuthorsInfo = sbAuthorsInfo.ToString();
                        sbAuthorsInfo.Clear();
                    }
                }

                sb.Append("检测指定时间:").Append(strTheTimeDesc).Append("\n");
                sb.Append("日志最后提交人列表:").Append(strAuthorsInfo).Append("\n");
            }
            else
            {//如果这种类型的数据的数量小于等于0
                sendDingTalkMsg = false;
            }
        }
        else
        {//如果没这种类型的数据
            sendDingTalkMsg = false;
        }
    }

    /// <summary>
    /// 获取作者关联的资源路径
    /// </summary>
    /// <param name="tipsFiles"></param>
    /// <param name="author"></param>
    /// <returns></returns>
    static List<string> GetReleatedResPathOnAuthors(List<FileTipsDetail> tipsFiles, string author)
    {
        List<string> releatedResPath = new List<string>();

        foreach(var t in tipsFiles)
        {
            if(t.strSvnAuthor == author)
            {
                releatedResPath.Add(t.strFilePath);
            }
        }

        return releatedResPath;
    }
}
 
