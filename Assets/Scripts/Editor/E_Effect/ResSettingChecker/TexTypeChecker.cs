/*=================================================================================
* 创建者:刘军
* 功能描述:图片类型设置检测
* 包含功能:1.图片类型设置检测
*=================================================================================*/
using System;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;

/// <summary>
/// 图片类型设置检测
/// </summary>
public partial class ResSettingChecker
{
    /// <summary>
    /// 图片类型设置检测
    /// 参数说明:包含三个参数 eg:Assets/Art/Characters,0,_N
    /// 参数1为string类型,指定目录
    /// 参数2为int类型,贴图类型 0法线贴图
    /// 参数3为string类型,参数字符串,视不同情况扩展
    /// 当参数2为0时,"_N(法线贴图命名)"表示检测文件名以"_N"结尾的文件
    /// </summary>
    class TexTypeChecker : ResSetingsCheckerBase
    {
        /// <summary>
        /// 目录
        /// </summary>
        public string dir;
        /// <summary>
        /// 检测类型
        /// </summary>
        public int checkType;
        /// <summary>
        /// 检测参数
        /// </summary>
        public string checkParam;
        /// <summary>
        /// 键
        /// </summary>
        public override string KeyDesc { get { return texKeyString + resSettingCheckKeySplit + texTypeKeyString; } }
        /// <summary>
        /// 检测类型
        /// </summary>
        public override ResSetingsCheckType CheckType { get { return ResSetingsCheckType.TexType; } }
        /// <summary>
        /// 图片类型检测枚举
        /// </summary>
        TexTypeCheckEnum texTypeCheckEnum;

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="strParam"></param>
        /// <param name="lineNum"></param>
        public override void Init(string strParam, int lineNum)
        {
            base.Init(strParam, lineNum);

            if (!string.IsNullOrEmpty(strParam))
            {
                var paramsArr = strParam.Split(',');
                if (paramsArr != null && paramsArr.Length >= 3)
                {
                    try
                    {
                        dir = paramsArr[0];
                        checkType = int.Parse(paramsArr[1]);
                        bool isDefined = Enum.IsDefined(typeof(TexTypeCheckEnum), checkType);
                        if (!isDefined)
                        {
                            bParamValid = false;
                            return;
                        }
                        else
                        {
                            texTypeCheckEnum = (TexTypeCheckEnum)checkType;
                        }

                        checkParam = paramsArr[2];
                        bParamValid = true;
                        CheckParamValid();
                    }
                    catch (Exception e)
                    {
                        UnityEngine.Debug.Log($"【TexTypeChecker.Init,捕获到异常:{e.ToString()}】");
                    }
                }
            }

            if (!bParamValid)
            {
                Debug.LogError($"TexTypeChecker类型参数配置错误:{strParam}");
            }
        }

        /// <summary>
        /// 检测参数是否有效
        /// </summary>
        /// <returns></returns>
        bool CheckParamValid()
        {
            if (texTypeCheckEnum == TexTypeCheckEnum.NormalMap)
            {
                if (string.IsNullOrEmpty(checkParam))
                {
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 检测类型
        /// </summary>
        /// <param name="assetPath"></param>
        /// <param name="textureImporter"></param>
        /// <returns></returns>
        bool CheckTexType(string assetPath, TextureImporter textureImporter)
        {
            var bTexPathSatisfy = CheckTexPath(assetPath, textureImporter);
            if(bTexPathSatisfy)
            {
                if (texTypeCheckEnum == TexTypeCheckEnum.NormalMap)
                {
                    return textureImporter.textureType != TextureImporterType.NormalMap;
                }
            }

            return false;
        }

        /// <summary>
        /// 检测贴图路径
        /// </summary>
        /// <param name="assetPath"></param>
        /// <param name="textureImporter"></param>
        /// <returns></returns>
        bool CheckTexPath(string assetPath, TextureImporter textureImporter)
        {
            string fileNameWithoutExtension = Path.GetFileNameWithoutExtension(assetPath);

            if (texTypeCheckEnum == TexTypeCheckEnum.NormalMap)
            {
                return fileNameWithoutExtension.ToLower().EndsWith(checkParam.ToLower());
            }

            return false;
        }

        /// <summary>
        /// 获取贴图检测提示
        /// </summary>
        /// <param name="assetPath"></param>
        /// <param name="textureImporter"></param>
        /// <returns></returns>
        string GetTexCheckTips(string assetPath, TextureImporter textureImporter)
        {
            string texTips = string.Empty;

            if (texTypeCheckEnum == TexTypeCheckEnum.NormalMap)
            {
                texTips = "法线贴图未设置贴图类型为NormalMap";
            }

            return texTips;
        }

        /// <summary>
        /// 是否需要检测
        /// </summary>
        /// <param name="assetPath"></param>
        /// <param name="textureImporter"></param>
        /// <param name="texture"></param>
        /// <returns></returns>
        bool NeedCheck(string assetPath, TextureImporter textureImporter, Texture2D texture, ref string strReasonTips)
        {
            strReasonTips = string.Empty;
            if (IsSprite(textureImporter))
            {//如果是图集里的图
                return false;
            }

            if(!CheckTexType(assetPath, textureImporter))
            {
                return false;
            }

            strReasonTips = GetTexCheckTips(assetPath, textureImporter);

            return true;
        }

        /// <summary>
        /// 是否是图集里的图
        /// </summary>
        /// <param name="textureImporter"></param>
        /// <returns></returns>
        bool IsSprite(TextureImporter textureImporter)
        {
            var textureType = textureImporter.textureType;
            var spritePackingTag = textureImporter.spritePackingTag;
            if (textureType == TextureImporterType.Sprite && !string.IsNullOrEmpty(spritePackingTag))
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// 执行检测
        /// </summary>
        /// <param name="tipsFiles"></param>
        public override void ExecuteCheck(List<FileTipsDetail> tipsFiles)
        {
            base.ExecuteCheck(tipsFiles);

            List<string> filesList = new List<string>();
            List<FileTipsDetail> checkList = new List<FileTipsDetail>();
            string[] textureGuids = AssetDatabase.FindAssets("t:texture2D", new string[] { dir });

            if (textureGuids != null)
            {
                foreach (var t in textureGuids)
                {
                    var path = AssetDatabase.GUIDToAssetPath(t);
                    var texture = AssetDatabase.LoadAssetAtPath<Texture2D>(path);
                    var textureImporter = TextureImporter.GetAtPath(AssetDatabase.GetAssetPath(texture)) as TextureImporter;
                    string strReasonTips = "";
                    if (textureImporter != null && texture != null)
                    {
                        if (NeedCheck(path, textureImporter, texture, ref strReasonTips))
                        {
                            var ftd = new FileTipsDetail() { strTips = $"{strReasonTips}", strFilePath = path, checkType = CheckType, strGroupJsonValue = KeyDesc };
                            SetFileTipsDetailLastCommitInfo(path, ftd);
                            SetFileTipsDetailJsonInfo(ftd);
                            ftd.sortSeq = ftd.svnDateTicks;

                            checkList.Add(ftd);
                        }
                    }
                }
            }

            if (checkList != null && checkList.Count > 0)
            {
                checkList.Sort((l, r) => 
                {
                    var sortSeqCom = l.sortSeq - r.sortSeq;
                    if (sortSeqCom > 0)
                    {
                        return -1;
                    }
                    else if (sortSeqCom == 0)
                    {
                        return 0;
                    }
                    else
                    {
                        return 1;
                    }
                });

                tipsFiles.AddRange(checkList);
            }
        }
    }
}

