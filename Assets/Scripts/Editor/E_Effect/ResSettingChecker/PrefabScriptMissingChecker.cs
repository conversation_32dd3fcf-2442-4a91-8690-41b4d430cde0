/*=================================================================================
* 创建者:刘军
* 功能描述:检查prefab上是否有脚本miss
* 包含功能:1.检查prefab上是否有脚本miss
*=================================================================================*/
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 检查prefab上是否有脚本miss
/// </summary>
public partial class ResSettingChecker
{
    /// <summary>
    /// 检查prefab上是否有脚本miss
    /// 参数说明:包含一个参数 eg:Assets/UI
    /// 参数1为string类型,目录
    /// </summary>
    class PrefabScriptMissingChecker : PrefabMissingChecker
    {
        /// <summary>
        /// 键
        /// </summary>
        public override string KeyDesc { get { return prefabKeyString + resSettingCheckKeySplit + scriptMissingKeyString; } }
        /// <summary>
        /// 检测类型
        /// </summary>
        public override ResSetingsCheckType CheckType { get { return ResSetingsCheckType.PrefabScriptMissing; } }

        /// <summary>
        /// 查找丢失
        /// </summary>
        /// <param name="path"></param>
        /// <param name="tGameObj"></param>
        protected override void FindPrefabMissing(string path, GameObject tGameObj)
        {
            var tfa = tGameObj.GetComponentsInChildren<Transform>(true);
            if (tfa != null)
            {
                foreach (var tf in tfa)
                {
                    if (tf)
                    {
                        var coms = tf.GetComponents<Component>();
                        foreach (var c in coms)
                        {
                            if (c == null)
                            {
                                AddMissingTransform(path, tGameObj, tf.gameObject);
                                break;
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 处理Prefab丢失脚本
        /// </summary>
        /// <param name="checkList"></param>
        /// <param name="strReasonTips"></param>
        /// <param name="strTips"></param>
        protected override void ProcessPrefabMiss(List<FileTipsDetail> checkList, string strReasonTips, string strTips)
        {
            strReasonTips = "Prefab脚本丢失:";
            strTips = "PrefabScriptMissingChecker";

            base.ProcessPrefabMiss(checkList, strReasonTips, strTips);
        }
    }
}

