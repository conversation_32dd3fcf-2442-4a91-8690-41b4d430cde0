/*=================================================================================
* 创建者:刘军
* 功能描述:资源设置检测导出
* 包含功能:1.资源设置检测导出Json文件
*=================================================================================*/
using System;
using System.Collections.Generic; 
using System.IO;
using System.Text;

/// <summary>
/// 资源设置检测
/// </summary>
public partial class ResSettingChecker
{
    /// <summary>
    /// StringBuilder
    /// </summary>
    static StringBuilder sbForSimplyClear = new StringBuilder();
    /// <summary>
    /// 结果导出简化
    /// </summary>
    public class ResultExportSimplyClear
    {
        /// <summary>
        /// 统计描述
        /// </summary>
        public Dictionary<string, string> statisticssDesc = new Dictionary<string, string>();
        /// <summary>
        /// 结果列表
        /// </summary>
        public Dictionary<string, string> results = new Dictionary<string, string>();
        /// <summary>
        /// 检测类型对应数量结果列表
        /// </summary>
        [System.NonSerialized]
        public Dictionary<int, int> checkTypeResults = new Dictionary<int, int>();
        /// <summary>
        /// 检测类型对应描述
        /// </summary>
        [System.NonSerialized]
        public Dictionary<int, string> checkTypeGroupDescResults = new Dictionary<int, string>();
        /// <summary>
        /// 检测类型对应作者结果列表
        /// </summary>
        [System.NonSerialized]
        public Dictionary<int, List<string>> checkTypeAuthorsResults = new Dictionary<int, List<string>>();
    }

    /// <summary>
    /// 导出信息简化可读性
    /// </summary>
    /// <param name="fileList"></param>
    /// <param name="saveFilePath"></param>
    /// <returns></returns>
    static ResultExportSimplyClear ExportSimplyClear(List<FileTipsDetail> fileList, string saveFilePath)
    {
        if (fileList != null)
        {
            ResultExportSimplyClear vo = new ResultExportSimplyClear();
            fileList.Sort((l, r) => 
            {
                var lCheckType = (int)l.checkType;
                var rCheckType = (int)r.checkType;

                if (lCheckType != rCheckType)
                {
                    return lCheckType - rCheckType;
                }
                else
                {
                    var sortSeqCom = l.sortSeq - r.sortSeq;
                    if (sortSeqCom > 0)
                    {
                        return -1;
                    }
                    else if (sortSeqCom == 0)
                    {
                        return 0;
                    }
                    else
                    {
                        return 1;
                    }
                }
            });
            CalCheckTypeResults(fileList, vo);
            AssignCheckTypeResultsToList(fileList, vo);
            List<int> checkTypeList = new List<int>();

            foreach (var t in fileList)
            {
                SimplyClearFileTipsDetail(t, vo, checkTypeList); 
            }

            StatisticsResSettingCheckerDesc(vo, checkTypeList);
            int allCheckTypeCnt = checkTypeList.Count;
            int nowCheckTypeCnt = 0;

            //写入文件
            string output = Newtonsoft.Json.JsonConvert.SerializeObject(vo, Newtonsoft.Json.Formatting.Indented);

            try
            {
                MemoryStream stream = new MemoryStream(Encoding.UTF8.GetBytes(output));
                StreamReader reader = new StreamReader(stream);
                string line;
                StringBuilder sb = new StringBuilder();
                StringBuilder sbLine = new StringBuilder();
                while ((line = reader.ReadLine()) != null)
                {
                    var lineArr = line.Split(',');
                    bool lastItemList = IsLastItem(line, lineArr);
                    bool lineCheckContent = IsLineCheckContent(lineArr);

                    if (lineCheckContent)
                    {
                        var keyContent = lineArr[0];
                        var resSetingsCheckType = int.Parse(lineArr[1]);
                        var groupIndex = int.Parse(lineArr[2]);
                        var theSameCount = int.Parse(lineArr[3]);
                        var groupJsonKey = lineArr[4];
                        lastItemList = lastItemList || groupIndex == theSameCount - 1;

                        ProcessGroupContentBegin(sb, groupJsonKey, groupIndex, theSameCount, vo, resSetingsCheckType);
                        AddLineConentJsonKey(sb, keyContent);

                        for (int i = 5, j = lineArr.Length; i < j; i++)
                        {
                            if(i == 5)
                            {
                                sb.Append("\"");
                            }

                            string c = lineArr[i];
                            bool bLastItem = false;
                            if (c.Contains("\""))
                            {
                                bLastItem = true;
                            }

                            if (lastItemList)
                            {
                                sbLine.Append(lineArr[i]).Append(",");
                            }
                            else
                            {
                                sb.Append(lineArr[i]).Append(",");
                            }

                            if(bLastItem)
                            {
                                break;
                            }
                        }

                        if (lastItemList)
                        {
                            var lastLine = sbLine.ToString();
                            sbLine.Clear();

                            if (!string.IsNullOrEmpty(lastLine))
                            {
                                var index = lastLine.LastIndexOf(',');
                                if (index >= 0)
                                {
                                    lastLine = lastLine.Substring(0, index);
                                }
                            }

                            sb.Append(lastLine);
                        }

                        sb.Append("\n");

                        if(lastItemList)
                        {
                            nowCheckTypeCnt++;
                            ProcessGroupContentEnd(sb, nowCheckTypeCnt != allCheckTypeCnt);
                        }
                    }
                    else
                    {
                        sb.Append(line).Append("\n");
                    }
                }

                File.WriteAllText(saveFilePath, sb.ToString());
                reader.Close();
            }
            catch (Exception e)
            {
                UnityEngine.Debug.Log($"【ResSettingChecker.ExportSimplyClear,捕获到异常:{e.ToString()}】");
            }

            return vo;

        }

        return null;
    }

    /// <summary>
    /// 是否最后一项
    /// </summary>
    /// <param name="line"></param>
    /// <param name="lineArr"></param>
    /// <returns></returns>
    static bool IsLastItem(string line, string[] lineArr)
    {
        if (lineArr != null && lineArr.Length >= 5)
        {
            if (!line.EndsWith(","))
            {
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// 是否是要解析的内容行
    /// </summary>
    /// <param name="lineArr"></param>
    /// <returns></returns>
    static bool IsLineCheckContent(string[] lineArr)
    {
        if (lineArr != null && lineArr.Length >= 5)
        {
            var keyContent = lineArr[0];
            if (!string.IsNullOrEmpty(keyContent) && keyContent.Contains(resCheckResultKey))
            {
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// 处理分组开始内容
    /// </summary>
    /// <param name="sb"></param>
    /// <param name="groupJsonKey"></param>
    /// <param name="groupIndex"></param>
    /// <param name="theSameCount"></param>
    /// <param name="checkType"></param>
    static void ProcessGroupContentBegin(StringBuilder sb, string groupJsonKey, int groupIndex, int theSameCount, ResultExportSimplyClear vo, int checkType)
    {
        if(groupIndex == 0)
        {
            var checkTypeDesc = GetCheckTypeDesc(checkType);
            sb.Append("    ");
            sb.Append("\"");
            sb.Append($"{groupJsonKey}({checkTypeDesc},");
            sb.Append($"检测到{ theSameCount}条设置问题");
            sb.Append(")");
            sb.Append("\"");
            sb.Append(": {");

            sb.Append("\n");

            ProcessLastCommitAuthors(sb, groupJsonKey, vo, checkType);
        }
    }

    /// <summary>
    /// 处理最后一次提交信息作者列表
    /// </summary>
    /// <param name="sb"></param>
    /// <param name="groupJsonKey"></param>
    /// <param name="checkType"></param>
    static void ProcessLastCommitAuthors(StringBuilder sb, string groupJsonKey, ResultExportSimplyClear vo, int checkType)
    {
        sb.Append("    ");
        sb.Append("\"");
        sb.Append($"----【{groupJsonKey}】Svn最后提交人列表");
        sb.Append("\":");
        sb.Append("\"");

        if(vo.checkTypeAuthorsResults.TryGetValue(checkType, out var results))
        {//最后提交人列表信息
            if(results != null)
            {
                int addCount = 0;
                foreach(var t in results)
                {
                    if(addCount != 0)
                    {
                        sb.Append(",");
                    }

                    sb.Append(t);
                    addCount++;
                }
            }
        }

        sb.Append("\"");
        sb.Append(",");
        sb.Append("\n");
    }

    /// <summary>
    /// 处理分组结束内容
    /// </summary>
    /// <param name="sb"></param>
    /// <param name="bJsonGroupEnds"></param>
    static void ProcessGroupContentEnd(StringBuilder sb, bool bJsonGroupEnds)
    {
        string strC = bJsonGroupEnds ? "," : "";
        sb.Append("   }").Append(strC).Append("\n");
    }

    /// <summary>
    /// 添加Json行内容的key
    /// </summary>
    /// <param name="sb"></param>
    /// <param name="keyContent"></param>
    static void AddLineConentJsonKey(StringBuilder sb, string keyContent)
    {
        var index = keyContent.LastIndexOf(":");
        if(index >= 0)
        {
            sb.Append(keyContent.Substring(0, index + 1));
        }
    }

    /// <summary>
    /// 计算检查类型数量
    /// </summary>
    /// <param name="fileList"></param>
    /// <param name="vo"></param>
    static void CalCheckTypeResults(List<FileTipsDetail> fileList, ResultExportSimplyClear vo)
    {
        var enu = fileList.GetEnumerator();
        while (enu.MoveNext())
        {
            var checkType = (int)enu.Current.checkType;
            var strSvnAuthor = enu.Current.strSvnAuthor;
            var t = vo.checkTypeResults;

            int result = 0;
            t.TryGetValue(checkType, out result);
            enu.Current.groupIndex = result;
            vo.checkTypeResults[checkType] = ++result;
            vo.checkTypeGroupDescResults[checkType] = enu.Current.strGroupJsonValue;

            //最后提交人列表
            if(!string.IsNullOrEmpty(strSvnAuthor))
            {
                strSvnAuthor = strSvnAuthor.Trim();
                List<string> authorList = null;
                if (!vo.checkTypeAuthorsResults.TryGetValue(checkType, out authorList))
                {
                    authorList = new List<string>();
                }

                if(!authorList.Contains(strSvnAuthor))
                {
                    authorList.Add(strSvnAuthor);
                }

                vo.checkTypeAuthorsResults[checkType] = authorList;
            }
        }
    }

    /// <summary>
    /// 赋值检查类型数量
    /// </summary>
    /// <param name="fileList"></param>
    /// <param name="vo"></param>
    static void AssignCheckTypeResultsToList(List<FileTipsDetail> fileList, ResultExportSimplyClear vo)
    {
        var enu = fileList.GetEnumerator();
        while (enu.MoveNext())
        {
            var checkType = (int)enu.Current.checkType;
            if(vo.checkTypeResults.TryGetValue(checkType, out var result))
            {
                enu.Current.theSameCount = result;
            }
        }
    }

    /// <summary>
    /// 文件提示信息简化信息
    /// </summary>
    /// <param name="ftd"></param>
    /// <param name="resc"></param>
    /// <param name="checkTypeList"></param>
    static void SimplyClearFileTipsDetail(FileTipsDetail ftd, ResultExportSimplyClear resc, List<int> checkTypeList)
    {
        if (resc != null && ftd != null)
        {
            var checkType = (int)ftd.checkType;
            var strFilePath = $"--({checkType})--" + ftd.strFilePath;
            Dictionary<string, string> procList = resc.results;

            var strJsonValue = ftd.strJsonValue;
            if(!string.IsNullOrEmpty(strJsonValue))
            {
                var strArr = strJsonValue.Split(',');
                if(strArr != null && strArr.Length >= 3)
                {
                    StringBuilder sb = new StringBuilder();
                    for (int i = 0, j = strArr.Length; i < j; i++)
                    {
                        if(i == 2)
                        {
                            sb.Append(ftd.groupIndex).Append(",");
                            sb.Append(ftd.theSameCount).Append(",");
                            sb.Append(ftd.strGroupJsonValue).Append(",");
                        }
                      
                        sb.Append(strArr[i]).Append(",");
                    }

                    strJsonValue = sb.ToString();
                    sb.Clear();

                    strJsonValue = strJsonValue.Substring(0, strJsonValue.LastIndexOf(","));
                }
            }

            if(!checkTypeList.Contains(checkType))
            {
                checkTypeList.Add(checkType);
            }

            procList[strFilePath] = strJsonValue;
            sbForSimplyClear.Clear();
        }
    }

    /// <summary>
    /// 统计检测设置信息
    /// </summary>
    /// <param name="resc"></param>
    /// <param name="checkTypeList"></param>
    static void StatisticsResSettingCheckerDesc(ResultExportSimplyClear resc, List<int> checkTypeList)
    {
        int num = resc.results.Count;

        resc.statisticssDesc[descKey] = $"共检测到{num}条资源设置问题";
        StringBuilder sb = new StringBuilder();
        if(num > 0)
        {
            int addNum = 0;
            foreach(var t in checkTypeList)
            {
                if(resc.checkTypeGroupDescResults.TryGetValue(t, out var desc) && resc.checkTypeResults.TryGetValue(t, out var count))
                {
                    sb.Clear();
                    var checkTypeDesc = GetCheckTypeDesc(t);
                    sb.Append("【").Append(desc).Append("】").Append("(").Append(checkTypeDesc).Append(")").Append(count).Append("条");

                    resc.statisticssDesc[descDetailKey + addNum.ToString("D2")] = sb.ToString();

                    addNum++;
                }
            }
        }

        sb.Clear();
    }
}
 
