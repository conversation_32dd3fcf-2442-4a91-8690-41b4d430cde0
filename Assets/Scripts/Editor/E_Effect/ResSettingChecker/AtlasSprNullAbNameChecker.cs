/*=================================================================================
* 创建者:刘军
* 功能描述:检测sprite的abname是否为空
* 包含功能:1.检测sprite的abname是否为空
*=================================================================================*/
using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

/// <summary>
/// 检测sprite的abname是否为空
/// </summary>
public partial class ResSettingChecker
{
    /// <summary>
    /// 检测sprite的abname是否为空
    /// 参数说明:包含一个参数 eg:Assets/UI
    /// 参数1为string类型,目录
    /// </summary>
    class AtlasSprNullAbNameChecker : ResSetingsCheckerBase
    {
        /// <summary>
        /// 目录
        /// </summary>
        public string dir;
        /// <summary>
        /// 键
        /// </summary>
        public override string KeyDesc { get { return atlasKeyString + resSettingCheckKeySplit + atlasSpriteNullABNameKeyString; } }
        /// <summary>
        /// 检测类型
        /// </summary>
        public override ResSetingsCheckType CheckType { get { return ResSetingsCheckType.AtlasSpriteNullABName; } }

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="strParam"></param>
        /// <param name="lineNum"></param>
        public override void Init(string strParam, int lineNum)
        {
            base.Init(strParam, lineNum);

            if (!string.IsNullOrEmpty(strParam))
            {
                var paramsArr = strParam.Split(',');
                if (paramsArr != null && paramsArr.Length >= 1)
                {
                    try
                    {
                        dir = paramsArr[0];
                        bParamValid = true;
                    }
                    catch (Exception e)
                    {
                        UnityEngine.Debug.Log($"【AtlasSprNullAbNameChecker.Init,捕获到异常:{e.ToString()}】");
                    }
                }
            }

            if (!bParamValid)
            {
                Debug.LogError($"AtlasSprNullAbNameChecker类型参数配置错误:{strParam}");
            }
        }

        /// <summary>
        /// 执行检测
        /// </summary>
        /// <param name="tipsFiles"></param>
        public override void ExecuteCheck(List<FileTipsDetail> tipsFiles)
        {
            base.ExecuteCheck(tipsFiles);

            if (string.IsNullOrEmpty(dir))
            {
                return;
            }

            List<string> filesList = new List<string>();
            List<FileTipsDetail> checkList = new List<FileTipsDetail>();
            var textureGuids = AssetDatabase.FindAssets("t:texture2D", new string[] { dir });

            if (textureGuids != null)
            {
                foreach (var t in textureGuids)
                {
                    var path = AssetDatabase.GUIDToAssetPath(t);
                    var texture = AssetDatabase.LoadAssetAtPath<Texture2D>(path);
                    var textureImporter = TextureImporter.GetAtPath(AssetDatabase.GetAssetPath(texture)) as TextureImporter;
                    if (textureImporter != null && texture != null)
                    {
                        var abName = textureImporter.assetBundleName;
                        var atlasName = textureImporter.spritePackingTag;
                        if (textureImporter.textureType == TextureImporterType.Sprite)
                        {//图集里的图
                            if (string.IsNullOrEmpty(abName))
                            {
                                var strReasonTips = $"SpriteABName为空:spritePackingTag:{atlasName}";
                                var ftd = new FileTipsDetail() { strTips = $"AtlasSprNullAbNameChecker检测({strReasonTips})", strFilePath = path, checkType = CheckType, strGroupJsonValue = KeyDesc };
                                SetFileTipsDetailLastCommitInfo(path, ftd);
                                SetFileTipsDetailJsonInfo(ftd);
                                ftd.sortSeq = GetSortSeq(atlasName);

                                checkList.Add(ftd);
                            }
                        }
                    }
                }
            }

            if (checkList != null && checkList.Count > 0)
            {
                checkList.Sort((l, r) => 
                {
                    var sortSeqCom = l.sortSeq - r.sortSeq;
                    if (sortSeqCom > 0)
                    {
                        return -1;
                    }
                    else if (sortSeqCom == 0)
                    {
                        return 0;
                    }
                    else
                    {
                        return 1;
                    }
                });

                tipsFiles.AddRange(checkList);
            }
        }
    }
}

