/*=================================================================================
* 创建者:刘军
* 功能描述:图片占用大小检测(可用作大图检测,排除UI图集里的图)
* 包含功能:1.图片占用大小检测(可用作大图检测,排除UI图集里的图)
*=================================================================================*/
using Sirenix.OdinInspector.Demos;
using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using EditorUtilitys;

/// <summary>
/// 图片占用大小检测(可用作大图检测,排除UI图集里的图)
/// </summary>
public partial class ResSettingChecker
{
    /// <summary>
    /// 图片占用大小检测(可用作大图检测,排除UI图集里的图)
    /// </summary>
    class TexSizeChecker : ResSetingsCheckerBase
    {
        /// <summary>
        /// 目录
        /// </summary>
        public string dir;
        /// <summary>
        /// 大小(字节)
        /// </summary>
        public long texSize = 400 * 1024;  //400kb
        /// <summary>
        /// 键
        /// </summary>
        public override string KeyDesc { get { return texKeyString + resSettingCheckKeySplit + sizeKeyString; } }
        /// <summary>
        /// 检测类型
        /// </summary>
        public override ResSetingsCheckType CheckType { get { return ResSetingsCheckType.TexSizeCheck; } }

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="strParam"></param>
        /// <param name="lineNum"></param>
        public override void Init(string strParam, int lineNum)
        {
            base.Init(strParam, lineNum);

            if (!string.IsNullOrEmpty(strParam))
            {
                var paramsArr = strParam.Split(',');
                if (paramsArr != null && paramsArr.Length >= 2)
                {
                    try
                    {
                        dir = paramsArr[0];
                        texSize = long.Parse(paramsArr[1]);
                        bParamValid = true;
                    }
                    catch (Exception e)
                    {
                        UnityEngine.Debug.Log($"【TexSizeChecker.Init,捕获到异常:{e.ToString()}】");
                    }
                }
            }

            if (!bParamValid)
            {
                Debug.LogError($"TexSizeChecker类型参数配置错误:{strParam}");
            }
        }

        /// <summary>
        /// 是否需要检测
        /// </summary>
        /// <param name="assetPath"></param>
        /// <param name="textureImporter"></param>
        /// <param name="texture"></param>
        /// <param name="retTexSize"></param>
        /// <returns></returns>
        bool NeedCheck(string assetPath, TextureImporter textureImporter, Texture2D texture, ref string strReasonTips, ref long retTexSize)
        {
            retTexSize = 0;
            strReasonTips = string.Empty;
            if (IsSprite(textureImporter))
            {//如果是图集里的图
                return false;
            }

            retTexSize = FileUtilitys.GetTextureStorageMemorySize(texture);
            var fileSizeStr = HotFixFilesDiffDetails.SizeSuffix(texSize, 2);
            var nowfileSizeStr = HotFixFilesDiffDetails.SizeSuffix(retTexSize, 2);
            strReasonTips = $"当前{nowfileSizeStr}超过指定的{fileSizeStr}".Replace(" ", "");

            return retTexSize > texSize;
        }

        /// <summary>
        /// 是否是图集里的图
        /// </summary>
        /// <param name="textureImporter"></param>
        /// <returns></returns>
        bool IsSprite(TextureImporter textureImporter)
        {
            var textureType = textureImporter.textureType;
            var spritePackingTag = textureImporter.spritePackingTag;
            if (textureType == TextureImporterType.Sprite && !string.IsNullOrEmpty(spritePackingTag))
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// 执行检测
        /// </summary>
        /// <param name="tipsFiles"></param>
        public override void ExecuteCheck(List<FileTipsDetail> tipsFiles)
        {
            base.ExecuteCheck(tipsFiles);

            List<string> filesList = new List<string>();
            List<FileTipsDetail> checkList = new List<FileTipsDetail>();
            string[] textureGuids = AssetDatabase.FindAssets("t:texture2D", new string[] { dir });

            if (textureGuids != null)
            {
                foreach (var t in textureGuids)
                {
                    var path = AssetDatabase.GUIDToAssetPath(t);
                    var texture = AssetDatabase.LoadAssetAtPath<Texture2D>(path);
                    var textureImporter = TextureImporter.GetAtPath(AssetDatabase.GetAssetPath(texture)) as TextureImporter;
                    string strReasonTips = "";
                    long retTexSize = 0;
                    if (textureImporter != null && texture != null)
                    {
                        if (NeedCheck(path, textureImporter, texture, ref strReasonTips, ref retTexSize))
                        {
                            var ftd = new FileTipsDetail() { strTips = $"TexHWChecker检测({strReasonTips})", strFilePath = path, checkType = CheckType, strGroupJsonValue = KeyDesc };
                            SetFileTipsDetailLastCommitInfo(path, ftd);
                            SetFileTipsDetailJsonInfo(ftd);
                            ftd.sortSeq = retTexSize;

                            checkList.Add(ftd);
                        }
                    }
                }
            }

            if (checkList != null && checkList.Count > 0)
            {
                checkList.Sort((l, r) => 
                {
                    var sortSeqCom = l.sortSeq - r.sortSeq;
                    if (sortSeqCom > 0)
                    {
                        return -1;
                    }
                    else if (sortSeqCom == 0)
                    {
                        return 0;
                    }
                    else
                    {
                        return 1;
                    }
                });

                tipsFiles.AddRange(checkList);
            }
        }
    }
}

