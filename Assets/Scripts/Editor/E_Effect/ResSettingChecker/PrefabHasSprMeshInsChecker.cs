/*=================================================================================
* 创建者:刘军
* 功能描述:检查prefab是否存在SpriteMeshInstance组件
* 包含功能:1.检查prefab是否存在SpriteMeshInstance组件
*=================================================================================*/

using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 检查prefab是否存在SpriteMeshInstance组件
/// </summary>
public partial class ResSettingChecker
{
    /// <summary>
    /// 检查prefab是否存在SpriteMeshInstance组件
    /// 参数说明:包含一个参数 eg:Assets/Animations/Characters
    /// 参数1为string类型,目录,参数1可以不填,不填就默认指定为Assets/目录
    /// </summary>
    class PrefabHasSprMeshInsChecker : PrefabMissingChecker
    {
        /// <summary>
        /// 键
        /// </summary>
        public override string KeyDesc { get { return prefabKeyString + resSettingCheckKeySplit + hasSpriteMeshInstanceKeyString; } }
        /// <summary>
        /// 检测类型
        /// </summary>
        public override ResSetingsCheckType CheckType { get { return ResSetingsCheckType.PrefabHasSpriteMeshInstance; } }

        /// <summary>
        /// 查找丢失
        /// 这里是查找多出的组件
        /// </summary>
        /// <param name="path"></param>
        /// <param name="tGameObj"></param>
        protected override void FindPrefabMissing(string path, GameObject tGameObj)
        {
            if (tGameObj != null)
            {
                var tPath = path.Replace("\\", "/").ToLower();
                if (!tPath.EndsWith(".prefab"))
                {
                    return;
                }

                var anisCharaterDir = anisCharater.ToLower();
                if (tPath.Contains(anisCharaterDir))
                {//Assets/Animations/Characters目录
                    if (!tPath.Contains("/edit_"))
                    {
                        return;
                    }
                }

                //var smis = tGameObj.GetComponentsInChildren<SpriteMeshInstance>(true);
                //if (smis != null && smis.Length > 0)
                //{
                //    foreach(var t in smis)
                //    {
                //        AddMissingTransform(path, tGameObj, t.gameObject);
                //    }
                //}
            }
        }

        /// <summary>
        /// 处理Prefab丢失脚本
        /// </summary>
        /// <param name="checkList"></param>
        /// <param name="strReasonTips"></param>
        /// <param name="strTips"></param>
        protected override void ProcessPrefabMiss(List<FileTipsDetail> checkList, string strReasonTips, string strTips)
        {
            strReasonTips = "存在SpriteMeshInstance组件:";
            strTips = "PrefabHasSprMeshInsChecker";

            base.ProcessPrefabMiss(checkList, strReasonTips, strTips);
        }
    }
}

