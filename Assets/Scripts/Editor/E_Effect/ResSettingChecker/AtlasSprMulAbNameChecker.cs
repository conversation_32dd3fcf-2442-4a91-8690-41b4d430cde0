/*=================================================================================
* 创建者:刘军
* 功能描述:检测同一个图集里Sprite是否存在多个ABName
* 包含功能:1.检测同一个图集里Sprite是否存在多个ABName
*=================================================================================*/
using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

/// <summary>
/// 检测同一个图集里Sprite是否存在多个ABName
/// </summary>
public partial class ResSettingChecker
{
    /// <summary>
    /// 检测同一个图集里Sprite是否存在多个ABName
    /// 参数说明:包含一个参数 eg:Assets/UI
    /// 参数1为string类型,目录
    /// </summary>
    class AtlasSprMulAbNameChecker : ResSetingsCheckerBase
    {
        /// <summary>
        /// AtlasSprite集合
        /// </summary>
        struct AtlasSpriteList
        {
            /// <summary>
            /// atlasName
            /// </summary>
            public string atlasName;
            /// <summary>
            /// abName对应AtlasSpriteInfo
            /// </summary>
            public Dictionary<string, List<AtlasSpriteInfo>> spriteInfoDic;

            /// <summary>
            /// 添加一个sprite
            /// </summary>
            /// <param name="path"></param>
            /// <param name="abName"></param>
            public void Add(string path, string abName)
            {
                if(string.IsNullOrEmpty(atlasName) || string.IsNullOrEmpty(path) || string.IsNullOrEmpty(abName))
                {
                    return;
                }

                if (spriteInfoDic == null)
                {
                    spriteInfoDic = new Dictionary<string, List<AtlasSpriteInfo>>();
                }

                List<AtlasSpriteInfo> spriteInfoList = null;
                if (!spriteInfoDic.TryGetValue(abName, out spriteInfoList))
                {
                    spriteInfoList = new List<AtlasSpriteInfo>();
                }

                spriteInfoList.Add(new AtlasSpriteInfo() { atlasName = atlasName, path = path, abName = abName});
                spriteInfoDic[abName] = spriteInfoList;
            }
        }

        /// <summary>
        /// Atlas里一个Sprite信息封装
        /// </summary>
        struct AtlasSpriteInfo
        {
            /// <summary>
            /// atlasName
            /// </summary>
            public string atlasName;
            /// <summary>
            /// 路径
            /// </summary>
            public string path;
            /// <summary>
            /// abName
            /// </summary>
            public string abName;
        }

        /// <summary>
        /// 目录
        /// </summary>
        public string dir;
        /// <summary>
        /// 键
        /// </summary>
        public override string KeyDesc { get { return atlasKeyString + resSettingCheckKeySplit + atlasSpriteMulABNameKeyString; } }
        /// <summary>
        /// 检测类型
        /// </summary>
        public override ResSetingsCheckType CheckType { get { return ResSetingsCheckType.AtlasSpriteMulABNameInPkg; } }
        /// <summary>
        /// spritePackingTag对应AtlasSpriteList结构
        /// </summary>
        Dictionary<string, AtlasSpriteList> atlasSpritesDic = new Dictionary<string, AtlasSpriteList>();

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="strParam"></param>
        /// <param name="lineNum"></param>
        public override void Init(string strParam, int lineNum)
        {
            base.Init(strParam, lineNum);

            if (!string.IsNullOrEmpty(strParam))
            {
                var paramsArr = strParam.Split(',');
                if (paramsArr != null && paramsArr.Length >= 1)
                {
                    try
                    {
                        dir = paramsArr[0];
                        bParamValid = true;
                    }
                    catch (Exception e)
                    {
                        UnityEngine.Debug.Log($"【AtlasSprMulAbNameChecker.Init,捕获到异常:{e.ToString()}】");
                    }
                }
            }

            if (!bParamValid)
            {
                Debug.LogError($"AtlasSprMulAbNameChecker类型参数配置错误:{strParam}");
            }
        }

        /// <summary>
        /// CheckSprite
        /// </summary>
        /// <param name="atlasName"></param>
        /// <param name="assetPath"></param>
        /// <param name="abName"></param>
        void CheckSprite(string atlasName, string assetPath, string abName)
        {
            AtlasSpriteList atlasSpriteList;

            if (!atlasSpritesDic.TryGetValue(atlasName, out atlasSpriteList))
            {
                atlasSpriteList = new AtlasSpriteList() { atlasName = atlasName };
            }

            atlasSpriteList.Add(assetPath, abName);
            atlasSpritesDic[atlasName] = atlasSpriteList;
        }

        /// <summary>
        /// 检测同一个图集里Sprite是否存在多个ABName
        /// </summary>
        /// <param name="checkList"></param>
        void CheckSpriteMulABName(List<FileTipsDetail> checkList)
        {
            if (atlasSpritesDic != null && atlasSpritesDic.Count > 0)
            {
                var enu = atlasSpritesDic.GetEnumerator();
                while (enu.MoveNext())
                {
                    var v = enu.Current.Value;
                    var spriteInfoDic = v.spriteInfoDic;
                    if (spriteInfoDic != null && spriteInfoDic.Count > 1)
                    {
                        foreach (var t in spriteInfoDic)
                        {//abName对应的sprite列表
                            foreach(var asi in t.Value)
                            {//每个列表里取一个
                                var abName = asi.abName;
                                var atlasName = asi.atlasName;
                                var path = asi.path;
                                var strReasonTips = $"同一个图集里Sprite存在多个ABName,spritePackingTag:{atlasName},ABName:{abName}";
                                var ftd = new FileTipsDetail() { strTips = $"AtlasSprMulAbNameChecker检测({strReasonTips})", strFilePath = path, checkType = CheckType, strGroupJsonValue = KeyDesc };
                                SetFileTipsDetailLastCommitInfo(path, ftd);
                                SetFileTipsDetailJsonInfo(ftd);
                                ftd.sortSeq = GetSortSeq(atlasName);

                                checkList.Add(ftd);
                                break;
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 执行检测
        /// </summary>
        /// <param name="tipsFiles"></param>
        public override void ExecuteCheck(List<FileTipsDetail> tipsFiles)
        {
            base.ExecuteCheck(tipsFiles);

            if (string.IsNullOrEmpty(dir))
            {
                return;
            }

            List<string> filesList = new List<string>();
            List<FileTipsDetail> checkList = new List<FileTipsDetail>();
            var textureGuids = AssetDatabase.FindAssets("t:texture2D", new string[] { dir });

            if (textureGuids != null)
            {
                foreach (var t in textureGuids)
                {
                    var path = AssetDatabase.GUIDToAssetPath(t);
                    var texture = AssetDatabase.LoadAssetAtPath<Texture2D>(path);
                    var textureImporter = TextureImporter.GetAtPath(AssetDatabase.GetAssetPath(texture)) as TextureImporter;
                    if (textureImporter != null && texture != null)
                    {
                        var abName = textureImporter.assetBundleName;
                        var atlasName = textureImporter.spritePackingTag;
                        if (textureImporter.textureType == TextureImporterType.Sprite && !atlasName.Equals(""))
                        {//图集里的图
                            if(!string.IsNullOrEmpty(abName))
                            {
                                CheckSprite(atlasName, path, abName);
                            }
                        }
                    }
                }
            }

            CheckSpriteMulABName(checkList);

            if (checkList != null && checkList.Count > 0)
            {
                checkList.Sort((l, r) => 
                {
                    var sortSeqCom = l.sortSeq - r.sortSeq;
                    if (sortSeqCom > 0)
                    {
                        return -1;
                    }
                    else if (sortSeqCom == 0)
                    {
                        return 0;
                    }
                    else
                    {
                        return 1;
                    }
                });

                tipsFiles.AddRange(checkList);
            }
        }
    }
}

