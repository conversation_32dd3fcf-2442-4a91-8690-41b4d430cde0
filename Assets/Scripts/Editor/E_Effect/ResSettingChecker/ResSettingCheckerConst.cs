/*=================================================================================
* 创建者:刘军
* 功能描述:资源设置检测常量定义
* 包含功能:1.资源设置检测常量定义,枚举
*=================================================================================*/

/// <summary>
/// 资源设置检测
/// </summary>
public partial class ResSettingChecker
{
    /// <summary>
    /// 资源检测类型
    /// </summary>
    enum ResSetingsCheckType
    {
        None,
        /// <summary>
        /// TextureMipmap检测
        /// </summary>
        TextureMipmapCheck,
        /// <summary>
        /// TextureReadable检测
        /// </summary>
        TextureReadableCheck,
        /// <summary>
        /// Texture 单独打包检测
        /// </summary>
        TexPkgSeparateCheck,
        /// <summary>
        /// Texture ABName冗余
        /// </summary>
        TexABNameRepeatCheck,
        /// <summary>
        /// Texture 宽高倍数检测
        /// </summary>
        TexHWMulCheck,
        /// <summary>
        /// Texture 宽高检测
        /// </summary>
        TexHWCheck,
        /// <summary>
        /// Texture 占用大小检测
        /// </summary>
        TexSizeCheck,
        /// <summary>
        /// Texture 图集中的sprite是否有多个abname
        /// </summary>
        AtlasSpriteMulABNameInPkg,
        /// <summary>
        /// sprite的abname为空
        /// </summary>
        AtlasSpriteNullABName,
        /// <summary>
        /// sprite冗余
        /// </summary>
        AtlasSpriteRepeat,
        /// <summary>
        /// 换皮UI
        /// </summary>
        ReSkinUI,
        /// <summary>
        /// prefab 丢失
        /// </summary>
        PrefabMissing,
        /// <summary>
        /// prefab脚本丢失
        /// </summary>
        PrefabScriptMissing,
        /// <summary>
        /// prefab controller丢失
        /// </summary>
        PrefabControllerMissing,
        /// <summary>
        /// prefab card script丢失
        /// </summary>
        PrefabCardScriptMissing,
        /// <summary>
        /// prefab是否有SpriteMeshInstance
        /// </summary>
        PrefabHasSpriteMeshInstance,
        /// <summary>
        /// AudioSetting
        /// </summary>
        AudioSetting,
        /// <summary>
        /// 英雄技能配置
        /// </summary>
        ConfigHeroSkill,
        /// <summary>
        /// spriteMesh uv
        /// </summary>
        SpriteMeshUV,
        /// <summary>
        /// 文件编码格式
        /// </summary>
        FileEncoding,
        /// <summary>
        /// 文件命名规范
        /// </summary>
        FileNamingNorm,
        /// <summary>
        /// 贴图类型
        /// </summary>
        TexType,
        /// <summary>
        /// 动画压缩
        /// </summary>
        AnimCompression,
        /// <summary>
        /// 动画曲线浮点数精度
        /// </summary>
        AnimCurveFloatAccu,
    }

    /// <summary>
    /// Texture目录类型
    /// </summary>
    enum TexDirType
    {
        None,
        /// <summary>
        /// UI
        /// </summary>
        UI,
        /// <summary>
        /// 角色
        /// </summary>
        Character,
        /// <summary>
        /// 特效
        /// </summary>
        Effect,
        /// <summary>
        /// 场景
        /// </summary>
        Scene,
    }

    /// <summary>
    /// 特效Texture类型
    /// </summary>
    enum EffectTexType
    {
        /// <summary>
        /// 常规
        /// </summary>
        Normal,
        /// <summary>
        /// 序列帧
        /// </summary>
        Sequence,
        /// <summary>
        /// 噪声
        /// </summary>
        Noise,
        /// <summary>
        /// 光晕
        /// </summary>
        Glow,
        /// <summary>
        /// 遮罩
        /// </summary>
        Mask,
    }

    /// <summary>
    /// Texture宽高检测类型
    /// </summary>
    enum TexHWCheckType
    {
        /// <summary>
        /// 常规
        /// </summary>
        Normal,
        /// <summary>
        /// 特效
        /// </summary>
        Effect,
    }

    /// <summary>
    /// TextureType检测枚举
    /// </summary>
    enum TexTypeCheckEnum
    {
        /// <summary>
        /// 法线贴图类型
        /// </summary>
        NormalMap,
    }

    /// <summary>
    /// 动画压缩类型
    /// </summary>
    enum AnimCompressionType
    {
        /// <summary>
        /// 关闭
        /// </summary>
        Off = 0,
        /// <summary>
        /// 减少冗余帧
        /// </summary>
        KeyFrameReduction,
        /// <summary>
        /// 减少冗余帧，同时压缩
        /// </summary>
        KeyframeReductionAndCompression,
        /// <summary>
        /// 最优的,最佳的,Unity决定如何压缩
        /// </summary>
        Optimal,
    }

    /// <summary>
    /// 逻辑操作符类型
    /// </summary>
    enum LogicOperatorType
    {
        /// <summary>
        /// 大于
        /// </summary>
        Greater = 0,
        /// <summary>
        /// 大于等于
        /// </summary>
        GreaterEqual,
        /// <summary>
        /// 小于
        /// </summary>
        Less,
        /// <summary>
        /// 小于等于
        /// </summary>
        LessEqual,
        /// <summary>
        /// 等于
        /// </summary>
        Equal,
        /// <summary>
        /// 不等于
        /// </summary>
        NotEqual,
    }

    /// <summary>
    /// 描述字符串
    /// </summary>
    const string descKey = "desc";
    /// <summary>
    /// 详情描述字符串
    /// </summary>
    const string descDetailKey = "descDetail";
    /// <summary>
    /// 键的参数分隔符
    /// </summary>
    const string resSettingCheckKeySplit = ":";
    /// <summary>
    /// 资源检查结果键
    /// </summary>
    const string resCheckResultKey = "#ResSettingCheck#";
    /// <summary>
    /// 钉钉群通知关键字
    /// </summary>
    const string dingTalkkeyWord = "CheckRes";
    /// <summary>
    /// 上传文件名字
    /// </summary>
    const string upFileName = "output1({0}).json";
    /// <summary>
    /// 最近一段时间内上传文件名字
    /// </summary>
    const string upRecentFileName = "output2Recent({0}).json";
    /// <summary>
    /// Assets目录
    /// </summary>
    const string assetsDir = "Assets/";
    /// <summary>
    /// ui目录
    /// </summary>
    const string uiDir = "Assets/UI/";
    /// <summary>
    /// 角色目录
    /// </summary>
    const string characterDir = "Assets/Art/Characters/";
    /// <summary>
    /// 特效目录
    /// </summary>
    const string effectDir = "Assets/Art/Effects/";
    /// <summary>
    /// 特效美术源文件目录
    /// </summary>
    const string effectSourceDir = "Assets/Art/Effects_Source/";
    /// <summary>
    /// 场景目录
    /// </summary>
    const string sceneDir = "Assets/Art/Maps";
    /// <summary>
    /// animation character 目录
    /// </summary>
    const string anisCharater = "Assets/Animations/Characters";
    /// <summary>
    /// 英雄保存所在行键PlayerPrefs
    /// </summary>
    const string heroRowIndexPP = "ConfigHeroSkill_HeroSaveRowIndex";

    #region mipmap检测检测相关

    /// <summary>
    /// mipmap检测
    /// </summary>
    const string mipmapKeyString = "Mipmap";

    #endregion mipmap检测检测相关

    #region Texture readable检测检测相关

    /// <summary>
    /// texture检测
    /// </summary>
    const string textureKeyString = "Texture";
    /// <summary>
    /// texture Readable检测
    /// </summary>
    const string textureReadableKeyString = "Readable";

    #endregion Texture readable检测检测相关

    #region Texture PackingTag检测检测相关

    /// <summary>
    /// 图片检测
    /// </summary>
    const string texKeyString = "Texture";
    /// <summary>
    /// 图片单独打包
    /// </summary>
    const string texPkgSeparateKeyString = "PkgSeperate";
    /// <summary>
    /// ABName冗余
    /// </summary>
    const string abNameRepeatKeyString = "ABNameRepeat";

    #endregion Texture PackingTag检测检测相关

    /// <summary>
    /// 动画
    /// </summary>
    const string animKeyString = "Animation";
    /// <summary>
    /// 文件
    /// </summary>
    const string fileKeyString = "File";
    /// <summary>
    /// 编码
    /// </summary>
    const string encodingString = "Encoding";
    /// <summary>
    /// 命名规范
    /// </summary>
    const string namingNormString = "NamingNorm";
    /// <summary>
    /// 大小检测
    /// </summary>
    const string sizeKeyString = "Size";
    /// <summary>
    /// 图片宽高检测
    /// </summary>
    const string texHWKeyString = "HW";
    /// <summary>
    /// 图片类型设置检测
    /// </summary>
    const string texTypeKeyString = "Type";
    /// <summary>
    /// 图片宽高倍数检测
    /// </summary>
    const string texHWMulKeyString = "HWMultiple";
    /// <summary>
    /// 压缩
    /// </summary>
    const string compressionKeyString = "Compression";
    /// <summary>
    /// 动画曲线浮点数精度
    /// </summary>
    const string curveFloatAccuKeyString = "CurveFloatAccu";

    /// <summary>
    /// MipmapEnableCheck
    /// </summary>
    const string resSetingsCheckMipmapEnable = "MipmapEnableCheck";
    /// <summary>
    /// MipmapEnableTimeTick
    /// </summary>
    const string mipmapEnableTimeTickParamDesc = "MipmapEnableTimeTick";

    /// <summary>
    /// 图集
    /// </summary>
    const string atlasKeyString = "Atlas";
    /// <summary>
    /// atlasSpriteMulABNameKey
    /// </summary>
    const string atlasSpriteMulABNameKeyString = "SpriteMulABName";
    /// <summary>
    /// sprite空的abName
    /// </summary>
    const string atlasSpriteNullABNameKeyString = "SpriteNullABName";
    /// <summary>
    /// sprite冗余
    /// </summary>
    const string atlasSpriteRepeatKeyString = "SpriteRepeat";
    /// <summary>
    /// 换皮
    /// </summary>
    const string reSkinKeyString = "ReSkin";
    /// <summary>
    /// UI
    /// </summary>
    const string uiKeyString = "UI";
    /// <summary>
    /// 老资源图片大小
    /// </summary>
    const int maxOldTextureSize = 32;
    /// <summary>
    /// 英雄配置每次最大检测条数
    /// </summary>
    const int maxCfgHeroPerCheckNum = 2000;
    /// <summary>
    /// prefab
    /// </summary>
    const string prefabKeyString = "Prefab";
    /// <summary>
    /// MissingKey
    /// </summary>
    const string missingKeyString = "Missing";
    /// <summary>
    /// ScriptMissingKey
    /// </summary>
    const string scriptMissingKeyString = "ScriptMissing";
    /// <summary>
    ///ControllerMissingKey
    /// </summary>
    const string controllerMissingKeyString = "ControllerMissing";
    /// <summary>
    ///CardScriptMissingString
    /// </summary>
    const string cardScriptMissingKeyString = "CardScriptMissing";
    /// <summary>
    /// HasSpriteMeshInstance
    /// </summary>
    const string hasSpriteMeshInstanceKeyString = "HasSpriteMeshInstance";
    /// <summary>
    /// Audio
    /// </summary>
    const string audioKeyString = "Audio";
    /// <summary>
    /// SettingKeyString
    /// </summary>
    const string settingKeyString = "Setting";
    /// <summary>
    /// SettingKeyString
    /// </summary>
    const string configKeyString = "Config";
    /// <summary>
    /// HeroSkill
    /// </summary>
    const string heroSkillKeyString = "HeroSkill";
    /// <summary>
    /// SpriteMesh
    /// </summary>
    const string spriteMeshKeyString = "SpriteMesh";
    /// <summary>
    /// UV
    /// </summary>
    const string uvKeyString = "UV";

    #region Jenkins参数

    /// <summary>
    /// 项目名称
    /// </summary>
    const string JPProjectName = "ProjectName";
    /// <summary>
    /// 获取上传文件目录
    /// </summary>
    const string JPUpFileLogDir = "UpFileLogDir";
    /// <summary>
    ///  获取上传文件目录url(钉钉链接用)
    /// </summary>
    const string JPUpFileLogDirUrl = "UpFileLogDirUrl";
    /// <summary>
    /// 项目工程svn url
    /// </summary>
    const string JPSvnRepositoryUrl = "SvnRepositoryUrl";
    /// <summary>
    /// svn安装路径
    /// </summary>
    const string JPSvnExePath = "SvnExePath";
    /// <summary>
    /// 获取资源设置CheckParams参数
    /// </summary>
    const string JPCheckParams = "CheckParams";
    /// <summary>
    /// 获取钉钉群配置参数
    /// </summary>
    const string JPDingTalkToken = "DingTalkToken";
    /// <summary>
    /// 获取检测设置类型枚举
    /// </summary>
    const string JPCheckType = "CheckType";
    /// <summary>
    /// 最近一段时间内提交
    /// </summary>
    const string JPSubmitRecentPeriod = "SubmitRecentPeriod";
    /// <summary>
    /// 唯一标识
    /// </summary>
    const string JPUniqueIdentity = "UniqueIdentity";

    #endregion Jenkins参数

    #region 特效类型命名

    const string effectXuLie_ = "xulie_";
    const string effectNoise = "noise_";
    const string effectGlow = "glow_";
    const string effectMask = "mask_";

    #endregion 特效类型命名

    /// <summary>
    /// 参数内分割符
    /// </summary>
    const string oneParamSplit = "||";
    /// <summary>
    /// bom
    /// </summary>
    const string encodingBomName = "bom";

    /// <summary>
    /// 是否贴图检测目录
    /// </summary>
    /// <returns></returns>
    static TexDirType IsTheTextureCheckDir(string strDir)
    {
        TexDirType texDirType = TexDirType.None;
        if (string.IsNullOrEmpty(strDir))
        {
            return texDirType;
        }

        if(strDir.Contains(uiDir))
        {
            texDirType = TexDirType.UI;
            return texDirType;
        }

        if (strDir.Contains(characterDir))
        {
            texDirType = TexDirType.Character;
            return texDirType;
        }

        if (strDir.Contains(effectDir) || strDir.Contains(effectSourceDir) )
        {
            texDirType = TexDirType.Effect;
            return texDirType;
        }

        if (strDir.Contains(sceneDir))
        {
            texDirType = TexDirType.Scene;
            return texDirType;
        }

        return texDirType;
    }

    /// <summary>
    /// 返回默认的资源检测目录
    /// </summary>
    /// <returns></returns>
    static string[] GetDefaultAssetNormDirs()
    {
        return new string[] { GetAssetNormDir(uiDir), GetAssetNormDir(effectDir), GetAssetNormDir(effectSourceDir), GetAssetNormDir(characterDir), GetAssetNormDir(sceneDir) };
    }

    /// <summary>
    /// 获取Asset规范路径
    /// </summary>
    /// <param name="strDir"></param>
    /// <returns></returns>
    static string GetAssetNormDir(string strDir)
    {
        if(string.IsNullOrEmpty(strDir))
        {
            return strDir;
        }

        strDir = strDir.Replace("\\", "/");
        if(strDir.EndsWith("/"))
        {
            strDir = strDir.Substring(0, strDir.Length - 1);
        }

        return strDir;
    }

    /// <summary>
    /// 返回默认的资源检测目录
    /// </summary>
    /// <returns></returns>
    static EffectTexType GetEffectTexType(string strPath)
    {
        if(!string.IsNullOrEmpty(strPath))
        {
            strPath = strPath.ToLower();
            
            if(strPath.Contains(effectXuLie_))
            {
                return EffectTexType.Sequence;
            }
            else if(strPath.Contains(effectNoise))
            {
                return EffectTexType.Noise;
            }
            else if (strPath.Contains(effectGlow))
            {
                return EffectTexType.Glow;
            }
            else if (strPath.Contains(effectMask))
            {
                return EffectTexType.Mask;
            }
        }

        return EffectTexType.Normal;
    }
}

