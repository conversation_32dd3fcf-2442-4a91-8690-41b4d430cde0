/*=================================================================================
* 创建者:刘军
* 功能描述:动画曲线浮点数精度设置检测
* 包含功能:1.动画曲线浮点数精度设置检测
*=================================================================================*/
using System;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;
using EditorUtilitys;

/// <summary>
/// 动画曲线浮点数精度设置检测
/// </summary>
public partial class ResSettingChecker
{
    /// <summary>
    /// 动画曲线浮点数精度设置检测
    /// 参数说明:包含五个参数 eg:Assets/Art/Characters,/Animations,Stand||Show,3,3
    /// 参数1为string类型,指定目录
    /// 参数2为string类型,动画目录路径
    /// 参数3为string类型,过滤动画名列表
    /// 参数4为int类型,逻辑操作符类型 0大于 1大于等于 2小于 3小于等于 4等于 5不等于
    /// 参数5为int类型,浮点数小数点位数
    /// </summary>
    class AnimCurveFloatAccuChecker : ResSetingsCheckerBase
    {
        /// <summary>
        /// 目录
        /// </summary>
        public string dir;
        /// <summary>
        /// 动画目录路径
        /// </summary>
        public string animDirPath;
        /// <summary>
        /// 过滤动画名列表
        /// </summary>
        public string filterAniNameList;
        /// <summary>
        /// 操作符
        /// </summary>
        public int operation;
        /// <summary>
        /// 小数位数
        /// </summary>
        public int digitNum;
        /// <summary>
        /// 键
        /// </summary>
        public override string KeyDesc { get { return animKeyString + resSettingCheckKeySplit + curveFloatAccuKeyString; } }
        /// <summary>
        /// 检测类型
        /// </summary>
        public override ResSetingsCheckType CheckType { get { return ResSetingsCheckType.AnimCurveFloatAccu; } }
        /// <summary>
        /// 逻辑操作符类型
        /// </summary>
        LogicOperatorType logicOperatorType;
        /// <summary>
        /// 文件扩展名
        /// </summary>
        string fileExtensionArr = ".anim";
        /// <summary>
        /// 动画路径是否为空
        /// </summary>
        bool bAnimDirPathNull;
        /// <summary>
        /// 动画路径
        /// </summary>
        string animDirPathLower;
        /// <summary>
        /// 过滤动画名数组
        /// </summary>
        string[] filterAniNameLowerArr;

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="strParam"></param>
        /// <param name="lineNum"></param>
        public override void Init(string strParam, int lineNum)
        {
            base.Init(strParam, lineNum);

            if (!string.IsNullOrEmpty(strParam))
            {
                var paramsArr = strParam.Split(',');
                if (paramsArr != null && paramsArr.Length >= 5)
                {
                    try
                    {
                        dir = paramsArr[0];
                        animDirPath = paramsArr[1];
                        filterAniNameList = paramsArr[2];
                        operation = int.Parse(paramsArr[3]);
                        digitNum = int.Parse(paramsArr[4]);
                        bool isDefined = Enum.IsDefined(typeof(LogicOperatorType), operation);
                        if (!isDefined)
                        {
                            bParamValid = false;
                            return;
                        }
                        else
                        {
                            logicOperatorType = (LogicOperatorType)operation;
                        }

                        bParamValid = true;
                        ProcessAnimDirPath();
                        ProcessFilterAnimNameList();
                    }
                    catch (Exception e)
                    {
                        UnityEngine.Debug.Log($"【AnimCurveFloatAccuChecker.Init,捕获到异常:{e.ToString()}】");
                    }
                }
            }

            if (!bParamValid)
            {
                Debug.LogError($"AnimCurveFloatAccuChecker类型参数配置错误:{strParam}");
            }
        }

        /// <summary>
        /// 处理动画目录路径
        /// </summary>
        void ProcessAnimDirPath()
        {
            bAnimDirPathNull = string.IsNullOrEmpty(animDirPath);

            if (!bAnimDirPathNull)
            {
                animDirPathLower = animDirPath.ToLower();
            }
        }

        /// <summary>
        /// 处理过滤动画名列表
        /// </summary>
        void ProcessFilterAnimNameList()
        {
            if (!string.IsNullOrEmpty(filterAniNameList))
            {
                filterAniNameLowerArr = filterAniNameList.Split(new string[] { oneParamSplit }, StringSplitOptions.RemoveEmptyEntries);
                if (filterAniNameLowerArr != null)
                {
                    for (int i = 0, j = filterAniNameLowerArr.Length; i < j; i++)
                    {
                        filterAniNameLowerArr[i] = filterAniNameLowerArr[i].ToLower();
                    }
                }
            }
        }

        /// <summary>
        /// 是否是动画目录
        /// </summary>
        /// <param name="strPath"></param>
        /// <returns></returns>
        bool IsAnimDir(string strPath)
        {
            if (bAnimDirPathNull)
            {
                return true;
            }

            return strPath.ToLower().Contains(animDirPathLower);
        }

        /// <summary>
        /// 是否是过滤的动画名
        /// </summary>
        /// <param name="strPath"></param>
        /// <returns></returns>
        bool IsFilterAnimNameList(string strPath)
        {
            string strPathLower = Path.GetFileNameWithoutExtension(strPath).ToLower();
            if (filterAniNameLowerArr != null)
            {
                foreach (var t in filterAniNameLowerArr)
                {
                    if (strPathLower.Contains(t))
                    {
                        return true;
                    }
                }
            }
            else
            {
                return false;
            }

            return false;
        }

        /// <summary>
        /// 是否需要继续处理
        /// </summary>
        /// <param name="strPath"></param>
        /// <returns></returns>
        bool IsNeedContinueProcess(string strPath)
        {
            var bAnimDir = IsAnimDir(strPath);
            var bFilterAnimName = IsFilterAnimNameList(strPath);

            return bAnimDir && !bFilterAnimName;
        }

        /// <summary>
        /// 获取浮点数小数位数
        /// </summary>
        /// <param name="number"></param>
        /// <returns></returns>
        int GetFloatDigitNum(float number)
        {
            int digitNum = 0;
            try
            {
                digitNum = BitConverter.GetBytes(decimal.GetBits((decimal)number)[3])[2];
            }
            catch (Exception)
            {

            }

            return digitNum;
        }

        /// <summary>
        /// 检测动画曲线浮点数精度是否满足
        /// </summary>
        /// <param name="assetPath"></param>
        /// <returns></returns>
        bool CheckAnimCurveFloatAccuSatisfy(string assetPath)
        {
            AnimationClip clip = AssetDatabase.LoadAssetAtPath<AnimationClip>(assetPath);
            if (clip != null)
            {
                AnimationClipCurveData[] curves = null;
                curves = AnimationUtility.GetAllCurves(clip);
                Keyframe key;
                Keyframe[] keyFrames;

                if (curves != null && curves.Length > 0)
                {
                    for (int ii = 0; ii < curves.Length; ++ii)
                    {
                        AnimationClipCurveData curveDate = curves[ii];
                        if (curveDate.curve == null || curveDate.curve.keys == null)
                        {
                            continue;
                        }

                        keyFrames = curveDate.curve.keys;
                        for (int i = 0; i < keyFrames.Length; i++)
                        {
                            key = keyFrames[i];
                            if(!CompareDightNumerSatisfy(GetFloatDigitNum(key.value)))
                            {
                                return false;
                            }

                            if (!CompareDightNumerSatisfy(GetFloatDigitNum(key.inTangent)))
                            {
                                return false;
                            }

                            if (!CompareDightNumerSatisfy(GetFloatDigitNum(key.outTangent)))
                            {
                                return false;
                            }

                            if (!CompareDightNumerSatisfy(GetFloatDigitNum(key.inWeight)))
                            {
                                return false;
                            }

                            if (!CompareDightNumerSatisfy(GetFloatDigitNum(key.outWeight)))
                            {
                                return false;
                            }
                        }
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// 比较小数点位数
        /// </summary>
        /// <param name="sDigitNum"></param>
        /// <returns></returns>
        bool CompareDightNumerSatisfy(int sDigitNum)
        {
            if(logicOperatorType == LogicOperatorType.Greater)
            {
                return sDigitNum > digitNum;
            }
            else if (logicOperatorType == LogicOperatorType.GreaterEqual)
            {
                return sDigitNum >= digitNum;
            }
            else if (logicOperatorType == LogicOperatorType.Less)
            {
                return sDigitNum < digitNum;
            }
            else if (logicOperatorType == LogicOperatorType.LessEqual)
            {
                return sDigitNum <= digitNum;
            }
            else if (logicOperatorType == LogicOperatorType.Equal)
            {
                return sDigitNum == digitNum;
            }
            else if (logicOperatorType == LogicOperatorType.NotEqual)
            {
                return sDigitNum != digitNum;
            }

            return false;
        }

        /// <summary>
        /// 获取操作符描述
        /// </summary>
        /// <returns></returns>
        string GetOperatorDesc()
        {
            if (logicOperatorType == LogicOperatorType.Greater)
            {
                return "大于";
            }
            else if (logicOperatorType == LogicOperatorType.GreaterEqual)
            {
                return "大于等于";
            }
            else if (logicOperatorType == LogicOperatorType.Less)
            {
                return "小于";
            }
            else if (logicOperatorType == LogicOperatorType.LessEqual)
            {
                return "小于等于";
            }
            else if (logicOperatorType == LogicOperatorType.Equal)
            {
                return "等于";
            }
            else if (logicOperatorType == LogicOperatorType.NotEqual)
            {
                return "不等于";
            }

            return "";
        }

        /// <summary>
        /// 执行检测
        /// </summary>
        /// <param name="tipsFiles"></param>
        public override void ExecuteCheck(List<FileTipsDetail> tipsFiles)
        {
            base.ExecuteCheck(tipsFiles);

            List<string> filesList = new List<string>();
            FileUtilitys.GetFilesOnDir(dir, filesList, fileExtensionArr);
            List<FileTipsDetail> checkList = new List<FileTipsDetail>();
            string strOperatorDesc = GetOperatorDesc();
            string strTip = "动画曲线浮点数精度";
            strTip = $"{strTip}{strOperatorDesc}{digitNum}位,不满足";

            foreach (var t in filesList)
            {
                if(!IsNeedContinueProcess(t))
                {
                    continue;
                }

                var assetPath = t.Replace(Application.dataPath, "Assets");
                if (!CheckAnimCurveFloatAccuSatisfy(assetPath))
                {
                    var ftd = new FileTipsDetail() { strTips = strTip, strFilePath = assetPath, checkType = CheckType, strGroupJsonValue = KeyDesc };
                    SetFileTipsDetailLastCommitInfo(assetPath, ftd);
                    SetFileTipsDetailJsonInfo(ftd);
                    ftd.sortSeq = GetSortSeq(assetPath);

                    checkList.Add(ftd);
                }
            }

            if (checkList != null && checkList.Count > 0)
            {
                checkList.Sort((l, r) => 
                {
                    var sortSeqCom = l.sortSeq - r.sortSeq;
                    if (sortSeqCom > 0)
                    {
                        return -1;
                    }
                    else if (sortSeqCom == 0)
                    {
                        return 0;
                    }
                    else
                    {
                        return 1;
                    }
                });

                tipsFiles.AddRange(checkList);
            }
        }
    }
}

