/*=================================================================================
* 创建者:刘军
* 功能描述:检查换皮ui旧图(判断maxTextureSize==32)
* 包含功能:1.检查换皮ui旧图(判断maxTextureSize==32)
*=================================================================================*/
using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using System.Text;
using EditorUtilitys;

/// <summary>
/// 检查换皮ui旧图(判断maxTextureSize==32)
/// </summary>
public partial class ResSettingChecker
{
    /// <summary>
    /// 检查换皮ui旧图(判断maxTextureSize==32)
    /// 参数说明:包含三个参数 eg:Assets/UI,Assets/UI/Prefabs,.prefab||.asset
    /// 参数1为string类型,给定旧图片资源目录
    /// 参数2为string类型,指定目录
    /// 参数3为string类型,指定检测资源类型(.prefab.asset),参数填""就指定类型为.prefab||.asset,多个用||分割
    /// </summary>
    class ReSkinUIChecker : ResSetingsCheckerBase
    {
        /// <summary>
        /// 旧图片资源目录
        /// </summary>
        public string oldTexDir;
        /// <summary>
        /// 目标目录
        /// </summary>
        public string targetDir;
        /// <summary>
        /// 目标资源类型(.prefab,.asset)
        /// </summary>
        public string targetAssetTypes;
        /// <summary>
        /// 键
        /// </summary>
        public override string KeyDesc { get { return reSkinKeyString + resSettingCheckKeySplit + uiKeyString; } }
        /// <summary>
        /// 检测类型
        /// </summary>
        public override ResSetingsCheckType CheckType { get { return ResSetingsCheckType.ReSkinUI; } }
        /// <summary>
        /// 目标资源类型
        /// </summary>
        string tarAssetTypes;

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="strParam"></param>
        /// <param name="lineNum"></param>
        public override void Init(string strParam, int lineNum)
        {
            base.Init(strParam, lineNum);

            if (!string.IsNullOrEmpty(strParam))
            {
                var paramsArr = strParam.Split(',');
                if (paramsArr != null && paramsArr.Length >= 3)
                {
                    try
                    {
                        oldTexDir = paramsArr[0];
                        targetDir = paramsArr[1];
                        targetAssetTypes = paramsArr[2];
                        bParamValid = true;
                    }
                    catch (Exception e)
                    {
                        UnityEngine.Debug.Log($"【ReSkinUIChecker.Init,捕获到异常:{e.ToString()}】");
                    }
                }
            }

            if (!bParamValid)
            {
                Debug.LogError($"ReSkinUIChecker类型参数配置错误:{strParam}");
            }
            else
            {
                if(string.IsNullOrEmpty(targetAssetTypes))
                {
                    targetAssetTypes = ".prefab||.asset";
                }

                var strTarAssetTypes = targetAssetTypes.Split(new string[] { oneParamSplit }, StringSplitOptions.RemoveEmptyEntries);

                if(strTarAssetTypes == null)
                {
                    bParamValid = false;
                }
                else
                {
                    int addedIndex = 0;
                    StringBuilder sb = new StringBuilder();
                    foreach(var t in strTarAssetTypes)
                    {
                        if(addedIndex > 0)
                        {
                            sb.Append(",");
                        }

                        sb.Append(t);
                        addedIndex++;
                    }

                    tarAssetTypes = sb.ToString();
                }
            }
        }

        /// <summary>
        /// 处理旧图片资源
        /// </summary>
        /// <returns></returns>
        List<string> ProcessOldTex()
        {
            List<string> fileList = new List<string>();
            FileUtilitys.GetFilesOnDir(oldTexDir, fileList, picCheckTypeFilter);
            List<string> oldTexList = new List<string>();
            if(fileList.Count > 0)
            {
                foreach(var t in fileList)
                {
                    var tPath = FileUtilitys.GetAssetPathOnFullPath(t);
                    TextureImporter textureImporter = AssetImporter.GetAtPath(tPath) as TextureImporter;
                    if(textureImporter != null)
                    {
                        TextureImporterPlatformSettings androidSettings = textureImporter.GetPlatformTextureSettings(BuildTarget.Android.ToString());
                        TextureImporterPlatformSettings iosSettings = textureImporter.GetPlatformTextureSettings(BuildTarget.iOS.ToString());
                        if(androidSettings != null)
                        {
                            if (androidSettings.maxTextureSize == maxOldTextureSize)
                            {
                                oldTexList.Add(tPath);
                                continue;
                            }
                        }

                        if (iosSettings != null)
                        {
                            if (iosSettings.maxTextureSize == maxOldTextureSize)
                            {
                                oldTexList.Add(tPath);
                                continue;
                            }
                        }
                    }
                }
            }

            return oldTexList;
        }

        /// <summary>
        /// 处理目标目录资源
        /// </summary>
        /// <returns></returns>
        List<string> ProcessTargetAssetsDir()
        {
            List<string> fileList = new List<string>();
            FileUtilitys.GetFilesOnDir(targetDir, fileList, tarAssetTypes);

            return fileList;
        }

        /// <summary>
        /// 处理目标目录资源依赖
        /// </summary>
        /// <param name="assetList"></param>
        /// <returns></returns>
        Dictionary<string, List<string>> ProcessTargetAssetsDepends(List<string> assetList)
        {
            Dictionary<string, List<string>> dependsDic = new Dictionary<string, List<string>>();
            foreach (var asset in assetList)
            {
                var assetPath = FileUtilitys.GetAssetPathOnFullPath(asset);
                string[] depends = AssetDatabase.GetDependencies(assetPath);
                if(depends != null)
                {
                    foreach(var t in depends)
                    {
                        var tPath = t.Replace("\\", "/");

                        List<string> assets = null;
                        if (!dependsDic.TryGetValue(tPath, out assets))
                        {
                            assets = new List<string>();
                        }

                        if(!assets.Contains(assetPath))
                        {
                            assets.Add(assetPath);
                        }

                        dependsDic[tPath] = assets;
                    }
                }
            }

            return dependsDic;
        }

        /// <summary>
        /// 处理老资源引用
        /// </summary>
        /// <param name="oldTexList"></param>
        /// <param name="assetDependsDic"></param>
        /// <returns></returns>
        Dictionary<string, List<string>> ProcessOldTexRefrence(List<string> oldTexList, Dictionary<string, List<string>> assetDependsDic)
        {
            Dictionary<string, List<string>> dependsDic = new Dictionary<string, List<string>>();
            foreach (var tex in oldTexList)
            {
                if(assetDependsDic.TryGetValue(tex, out var assetlist))
                {
                    if(assetlist != null && assetlist.Count > 0)
                    {
                        dependsDic[tex] = assetlist;
                    }
                }
            }

            return dependsDic;
        }

        /// <summary>
        /// 执行检测
        /// </summary>
        /// <param name="tipsFiles"></param>
        public override void ExecuteCheck(List<FileTipsDetail> tipsFiles)
        {
            base.ExecuteCheck(tipsFiles);

            if(!bParamValid)
            {
                return;
            }

            var texList = ProcessOldTex();
            var assetList = ProcessTargetAssetsDir();
            if(texList == null || texList.Count <= 0 || assetList == null || assetList.Count <= 0)
            {//没有找到图片资源或者目标目录的资源类型不存在
                return;
            }

            var dependsDic = ProcessTargetAssetsDepends(assetList);
            if (dependsDic == null || dependsDic.Count <= 0)
            {//资源依赖列表字典数量为空
                return;
            }

            var refDic = ProcessOldTexRefrence(texList, dependsDic);
            if (refDic == null || refDic.Count <= 0)
            {//引用列表字典数量为空
                return;
            }

            List<FileTipsDetail> checkList = new List<FileTipsDetail>();
            foreach (var t in refDic)
            {
                var k = t.Key;
                var tList = t.Value;

                int addedIndex = 0;
                bool bShowIndex = tList.Count > 1;
                foreach (var g in tList)
                {
                    string strReasonTips = $"换皮UI:{g}";
                    string path = k;
                    if(bShowIndex)
                    {
                        path = k + "(" + addedIndex + ")";
                    }

                    var ftd = new FileTipsDetail() { strTips = $"ReSkinUIChecker检测({strReasonTips})", strFilePath = path, checkType = CheckType, strGroupJsonValue = KeyDesc };
                    SetFileTipsDetailLastCommitInfo(path, ftd);
                    SetFileTipsDetailJsonInfo(ftd);
                    ftd.sortSeq = GetSortSeq(g);
                    addedIndex++;

                    checkList.Add(ftd);
                }
            }

            if (checkList != null && checkList.Count > 0)
            {
                checkList.Sort((l, r) => 
                {
                    var sortSeqCom = l.sortSeq - r.sortSeq;
                    if (sortSeqCom > 0)
                    {
                        return -1;
                    }
                    else if (sortSeqCom == 0)
                    {
                        return 0;
                    }
                    else
                    {
                        return 1;
                    }
                });

                tipsFiles.AddRange(checkList);
            }
        }
    }
}

