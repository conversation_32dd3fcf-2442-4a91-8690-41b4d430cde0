/*=================================================================================
* 创建者:刘军
* 功能描述:图集里的图片冗余检测(给定目录下给定图集的图片在指定目录下的冗余检测)
* 包含功能:1.检测图集里的图片是否存在冗余
*=================================================================================*/
using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using EditorUtilitys;

/// <summary>
/// 图集里的图片冗余检测(给定目录下给定图集的图片在指定目录下的冗余检测)
/// </summary>
public partial class ResSettingChecker
{
    /// <summary>
    /// 图集里的图片冗余检测(给定目录下给定图集的图片在指定目录下的冗余检测)
    /// 参数说明:包含三个参数 eg:Assets/UI,ui.common,Assets/UI
    /// 参数1为string类型,给定目录
    /// 参数2为string类型,给定图集,如果填入为""则表示所有图集
    /// 参数3为string类型,指定目录
    /// </summary>
    class AtlasSprRepeatChecker : ResSetingsCheckerBase
    {
        /// <summary>
        /// 给定目录
        /// </summary>
        public string specDir;
        /// <summary>
        /// 给定图集名称
        /// </summary>
        public string specPkgTagName;
        /// <summary>
        /// 指定目录
        /// </summary>
        public string targetDir;
        /// <summary>
        /// 键
        /// </summary>
        public override string KeyDesc { get { return atlasKeyString + resSettingCheckKeySplit + atlasSpriteRepeatKeyString; } }
        /// <summary>
        /// 检测类型
        /// </summary>
        public override ResSetingsCheckType CheckType { get { return ResSetingsCheckType.AtlasSpriteRepeat; } }
        /// <summary>
        /// 检测所有图集
        /// </summary>
        bool bCheckAllAtlas = false;
        /// <summary>
        /// 指定目录图集md5
        /// </summary>
        Dictionary<string, List<string>> specImgMD5Dic = null;
        /// <summary>
        /// 目标目录md5
        /// </summary>
        Dictionary<string, List<string>> targetImgMD5Dic = null;
        /// <summary>
        /// 检测出来的重复的md5
        /// </summary>
        Dictionary<string, List<string>> repeatMD5Dic = null;

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="strParam"></param>
        /// <param name="lineNum"></param>
        public override void Init(string strParam, int lineNum)
        {
            base.Init(strParam, lineNum);

            if (!string.IsNullOrEmpty(strParam))
            {
                var paramsArr = strParam.Split(',');
                if (paramsArr != null && paramsArr.Length >= 3)
                {
                    try
                    {
                        specDir = paramsArr[0];
                        specPkgTagName = paramsArr[1];
                        targetDir = paramsArr[2];
                        bParamValid = true;
                       
                    }
                    catch (Exception e)
                    {
                        UnityEngine.Debug.Log($"【AtlasSprRepeatChecker.Init,捕获到异常:{e.ToString()}】");
                    }
                }
            }

            bCheckAllAtlas = string.IsNullOrEmpty(specPkgTagName);

            if (!bParamValid)
            {
                Debug.LogError($"AtlasSprRepeatChecker类型参数配置错误:{strParam}");
            }
        }

        /// <summary>
        /// 处理指定目录下指定图集的图片MD5
        /// </summary>
        int ProcessTextureMD5InSpecDir()
        {
            return ProcessTextureMD5InDir(specDir, false, ref specImgMD5Dic);
        }

        /// <summary>
        /// 检测冗余的图片MD5
        /// </summary>
        /// <param name="md5Dic"></param>
        void CheckTextureRepeatInDir(Dictionary<string, List<string>> md5Dic)
        {
            if (md5Dic != null && md5Dic.Count > 0)
            {
                var enu = md5Dic.GetEnumerator();
                while (enu.MoveNext())
                {
                    var k = enu.Current.Key;
                    var v = enu.Current.Value;
                    if(v != null && v.Count > 1)
                    {
                        if(repeatMD5Dic == null)
                        {
                            repeatMD5Dic = new Dictionary<string, List<string>>();
                        }

                        List<string> filePathList;
                        if(!repeatMD5Dic.TryGetValue(k, out filePathList))
                        {
                            filePathList = new List<string>();
                        }

                        foreach(var t in v)
                        {
                            if(!filePathList.Contains(t))
                            {
                                filePathList.Add(t);
                            }
                        }

                        repeatMD5Dic[k] = filePathList;
                    }
                }
            }
        }

        /// <summary>
        /// 检测集合里冗余的图片MD5
        /// </summary>
        /// <param name="specDic"></param>
        /// <param name="targetDic"></param>
        void CheckTextureRepeatBetweenSet(Dictionary<string, List<string>> specDic, Dictionary<string, List<string>> targetDic)
        {
            if (specDic != null && specDic.Count > 0 && targetDic != null && targetDic.Count > 0)
            {
                var enu = targetDic.GetEnumerator();
                while (enu.MoveNext())
                {
                    var kTarget = enu.Current.Key;
                    var vTargetList = enu.Current.Value;
                    if (vTargetList != null && vTargetList.Count > 0)
                    {
                        if(specDic.TryGetValue(kTarget, out var specList))
                        {
                            if(specList != null && specList.Count > 0)
                            {
                                if (repeatMD5Dic == null)
                                {
                                    repeatMD5Dic = new Dictionary<string, List<string>>();
                                }

                                List<string> filePathList;
                                if (!repeatMD5Dic.TryGetValue(kTarget, out filePathList))
                                {
                                    filePathList = new List<string>();
                                }

                                foreach (var t in specList)
                                {
                                    if (!filePathList.Contains(t))
                                    {
                                        filePathList.Add(t);
                                    }
                                }

                                foreach (var t in vTargetList)
                                {
                                    if (!filePathList.Contains(t))
                                    {
                                        filePathList.Add(t);
                                    }
                                }

                                repeatMD5Dic[kTarget] = filePathList;
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 处理指定目录下的图片MD5
        /// </summary>
        /// <param name="dir"></param>
        /// <param name="excludeSpecPkgTagName">是否排除指定图集的图片</param>
        /// <param name="md5Dic"></param>
        /// <returns></returns>
        int ProcessTextureMD5InDir(string dir, bool excludeSpecPkgTagName, ref Dictionary<string, List<string>> md5Dic)
        {
            int texNum = 0;
            var textureGuids = AssetDatabase.FindAssets("t:texture2D", new string[] { dir });
            if (textureGuids != null)
            {
                foreach (var t in textureGuids)
                {
                    var path = AssetDatabase.GUIDToAssetPath(t);
                    var texture = AssetDatabase.LoadAssetAtPath<Texture2D>(path);
                    var textureImporter = TextureImporter.GetAtPath(AssetDatabase.GetAssetPath(texture)) as TextureImporter;
                    if (textureImporter != null && texture != null)
                    {
                        var atlasName = textureImporter.spritePackingTag;
                        if (textureImporter.textureType == TextureImporterType.Sprite && !atlasName.Equals(""))
                        {//图集里的图
                            bool bValid = true;

                            if (!bCheckAllAtlas)
                            {
                                bValid = atlasName == specPkgTagName;  //查找指定图集名称的图片
                                if(excludeSpecPkgTagName)
                                {//如果是目标文件夹 就要排除掉指定图集名称的图片
                                    bValid = atlasName != specPkgTagName;
                                }
                            }

                            if (bValid)
                            {
                                var fullPath = FileUtilitys.GetCurFullPathOnAssets(path);
                                string fileMd5 = FileUtilitys.GetMD5HashFromFile(fullPath);
                                if (string.IsNullOrEmpty(fileMd5))
                                {
                                    continue;
                                }

                                if (md5Dic == null)
                                {
                                    md5Dic = new Dictionary<string, List<string>>();
                                }

                                List<string> filePathList;
                                if (!md5Dic.TryGetValue(fileMd5, out filePathList))
                                {
                                    filePathList = new List<string>();
                                }

                                filePathList.Add(path);
                                md5Dic[fileMd5] = filePathList;

                                texNum++;
                            }
                        }
                    }
                }
            }

            return texNum;
        }

        /// <summary>
        /// 处理目标目录下图片MD5
        /// </summary>
        int ProcessTextureMD5InTargetDir()
        {
            return ProcessTextureMD5InDir(targetDir, true, ref targetImgMD5Dic);
        }

        /// <summary>
        /// 执行检测
        /// </summary>
        /// <param name="tipsFiles"></param>
        public override void ExecuteCheck(List<FileTipsDetail> tipsFiles)
        {
            base.ExecuteCheck(tipsFiles);

            if(ProcessTextureMD5InSpecDir() <= 0 || ProcessTextureMD5InTargetDir() <= 0 )
            {//指定目录或者目标目录图片数量小于等于0
                return;
            }

            CheckTextureRepeatInDir(specImgMD5Dic);
            CheckTextureRepeatInDir(targetImgMD5Dic);
            CheckTextureRepeatBetweenSet(specImgMD5Dic, targetImgMD5Dic);

            List<FileTipsDetail> checkList = new List<FileTipsDetail>();
            if(repeatMD5Dic != null)
            {
                foreach (var t in repeatMD5Dic)
                {
                    var kMD5 = t.Key;
                    var tList = t.Value;
                    if(tList != null && tList.Count > 1)
                    {
                        foreach(var g in tList)
                        {
                            string strReasonTips = $"图片冗余Md5:{kMD5}";
                            string path = g;
                            var ftd = new FileTipsDetail() { strTips = $"AtlasSprRepeatChecker检测({strReasonTips})", strFilePath = path, checkType = CheckType, strGroupJsonValue = KeyDesc };
                            SetFileTipsDetailLastCommitInfo(path, ftd);
                            SetFileTipsDetailJsonInfo(ftd);
                            ftd.sortSeq = GetSortSeq(kMD5);

                            checkList.Add(ftd);
                        }
                    }
                }
            }

            if (checkList != null && checkList.Count > 0)
            {
                checkList.Sort((l, r) => 
                {
                    var sortSeqCom = l.sortSeq - r.sortSeq;
                    if (sortSeqCom > 0)
                    {
                        return -1;
                    }
                    else if (sortSeqCom == 0)
                    {
                        return 0;
                    }
                    else
                    {
                        return 1;
                    }
                });

                tipsFiles.AddRange(checkList);
            }
        }
    }
}

