/*=================================================================================
* 创建者:刘军
* 功能描述:检查prefab上是否有Controller miss
* 包含功能:1.检查prefab上是否有Controller miss
*=================================================================================*/
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 检查prefab上是否有Controller miss
/// </summary>
public partial class ResSettingChecker
{
    /// <summary>
    /// 检查prefab上是否有Controller miss
    /// 参数说明:包含一个参数 eg:Assets/Art/Characters
    /// 参数1为string类型,目录
    /// </summary>
    class PrefabControllerMissingChecker : PrefabMissingChecker
    {
        /// <summary>
        /// 键
        /// </summary>
        public override string KeyDesc { get { return prefabKeyString + resSettingCheckKeySplit + controllerMissingKeyString; } }
        /// <summary>
        /// 检测类型
        /// </summary>
        public override ResSetingsCheckType CheckType { get { return ResSetingsCheckType.PrefabControllerMissing; } }

        /// <summary>
        /// 查找丢失
        /// </summary>
        /// <param name="path"></param>
        /// <param name="tGameObj"></param>
        protected override void FindPrefabMissing(string path, GameObject tGameObj)
        {
            if (tGameObj != null)
            {
                var tPath = path.ToLower();
                if(tPath.EndsWith(".fbx"))
                {
                    return;
                }

                var aniArr = tGameObj.GetComponentsInChildren<Animator>(true);
                if (aniArr != null)
                {
                    foreach (var ani in aniArr)
                    {
                        if (ani.runtimeAnimatorController == null)
                        {
                            AddMissingTransform(path, tGameObj, ani.gameObject);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 处理Prefab丢失脚本
        /// </summary>
        /// <param name="checkList"></param>
        /// <param name="strReasonTips"></param>
        /// <param name="strTips"></param>
        protected override void ProcessPrefabMiss(List<FileTipsDetail> checkList, string strReasonTips, string strTips)
        {
            strReasonTips = "Prefab Controller丢失:";
            strTips = "PrefabControllerMissingChecker";

            base.ProcessPrefabMiss(checkList, strReasonTips, strTips);
        }
    }
}

