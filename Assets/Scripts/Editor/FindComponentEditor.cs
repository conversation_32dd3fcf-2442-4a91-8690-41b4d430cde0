using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;
using System.Text;

public class FindComponentEditor : EditorWindow
{
    string componentName;
    string fileGuid;

    string prefabComponentName;
    int prefabComponentCount = 2;
    Rect pathRect;
    string prefabPath;
    readonly Dictionary<GameObject, GameObjectComponent> objWithComponentSet = new Dictionary<GameObject, GameObjectComponent>();
    readonly Dictionary<GameObject, GameObjectComponent> objDisplayComponentSet = new Dictionary<GameObject, GameObjectComponent>();

    Vector2 scrollPos;

    class GameObjectComponent
    {
        public GameObject gameObject;
        public int count;
        public GameObject rootPrefab;
        public string fullPath;
    }

    [MenuItem("Tools/Find Component")]
	static void Open()
	{
		GetWindow<FindComponentEditor>();
	}

    void OnDestroy()
    {
        objWithComponentSet.Clear();
        objDisplayComponentSet.Clear();
        Resources.UnloadUnusedAssets();
    }

    void OnGUI()
	{
        OnGUIFindComponent();

        GUILayout.Space(10);
        OnGUIFindFileByGuid();

        GUILayout.Space(10);
        OnGUIFindComponentInPrefab();
    }

    private System.Type StringToType(string typeAsString)
    {
        System.Type typeAsType = System.Type.GetType(typeAsString);
        return typeAsType;
    }

    // heres the more advanced version
    public static System.Type StringToTypeByName(string aClassName)
    {
        var assemblies = System.AppDomain.CurrentDomain.GetAssemblies();
        for (int i = 0; i < assemblies.Length; i++)
        {
            var types = assemblies[i].GetTypes();
            for (int j = 0; j < types.Length; j++)
            {
                var type = types[j];
                if (typeof(UnityEngine.Object).IsAssignableFrom(type) && aClassName == type.Name)
                {
                    return type;
                }
            }
        }
        return null;
    }

    string GetComponentFullPath(Component component)
    {
        string fullPath = component.name;
        Transform parent = component.transform.parent;
        while(parent != null)
        {
            fullPath = parent.name + "/" + fullPath;
            parent = parent.parent;
        }
        return fullPath;
    }

    string GetComponentFullPath(GameObject obj)
    {
        string fullPath = obj.name;
        Transform parent = obj.transform.parent;
        while (parent != null)
        {
            fullPath = parent.name + "/" + fullPath;
            parent = parent.parent;
        }
        return fullPath;
    }

    void OnGUIFindComponent()
    {
        EditorGUILayout.BeginHorizontal();
        GUILayout.Label("需要查找的组件名:");
        componentName = EditorGUILayout.TextField(componentName);
        EditorGUILayout.EndHorizontal();

        if (GUILayout.Button("查找当前场景组件"))
        {
            FindComponent(false);
        }
        if (GUILayout.Button("查找当前场景可见组件"))
        {
            FindComponent(true);
        }
    }

    void OnGUIFindFileByGuid()
    {
        EditorGUILayout.BeginHorizontal();
        GUILayout.Label("需要查找的 GUID:");
        fileGuid = EditorGUILayout.TextField(fileGuid);
        EditorGUILayout.EndHorizontal();

        if (GUILayout.Button("查找文件"))
        {
            if(string.IsNullOrEmpty(fileGuid))
            {
                Debug.LogError("GUID不能为空");
                return;
            }
            var assetPath = AssetDatabase.GUIDToAssetPath(fileGuid);
            if(string.IsNullOrEmpty(assetPath))
            {
                Debug.LogError("未找到指定文件");
            }
            else
            {
                var assetObj = AssetDatabase.LoadAssetAtPath<Object>(assetPath);
                Debug.LogErrorFormat(assetObj, "GUID文件路径:" + assetPath);
            }
        }
    }

    void FindComponent(bool bCheckVisible)
    {
        if (string.IsNullOrEmpty(componentName))
        {
            Debug.LogError("组件名不能为空");
            return;
        }
        var type = StringToTypeByName(componentName);
        if (type == null)
        {
            Debug.LogError("组件名不存在");
            return;
        }
        Object[] components = GameObject.FindObjectsOfType(type);
        if (components.Length == 0)
        {
            Debug.LogError("未找到指定组件");
        }
        int suitableCount = 0;
        for (int i = 0; i < components.Length; i++)
        {
            var component = components[i] as Component;
            if (component == null)
            {
                continue;
            }
            if (!bCheckVisible || bCheckVisible && component.gameObject.activeInHierarchy)
            {
                suitableCount++;
                Debug.LogError(GetComponentFullPath(component));
            }
        }
        Debug.LogError("Find " + componentName + " count:" + suitableCount);
    }

    void OnGUIFindComponentInPrefab()
    {
        EditorGUILayout.BeginHorizontal();
        GUILayout.Label("需要查找的组件名:");
        prefabComponentName = EditorGUILayout.TextField(prefabComponentName);
        EditorGUILayout.EndHorizontal();
        EditorGUILayout.BeginHorizontal();
        GUILayout.Label("组件在节点上的最小数量");
        prefabComponentCount = EditorGUILayout.IntField(prefabComponentCount);
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.BeginHorizontal();
        //获得一个长300的框
        pathRect = EditorGUILayout.GetControlRect(GUILayout.Width(300));
        //将上面的框作为文本输入框
        prefabPath = EditorGUI.TextField(pathRect, prefabPath);
        EditorGUILayout.EndHorizontal();

        //如果鼠标正在拖拽中或拖拽结束时，并且鼠标所在位置在文本输入框内
        if ((Event.current.type == EventType.DragUpdated
          || Event.current.type == EventType.DragExited)
          && pathRect.Contains(Event.current.mousePosition))
        {
            //改变鼠标的外表
            DragAndDrop.visualMode = DragAndDropVisualMode.Generic;
            if (DragAndDrop.paths != null && DragAndDrop.paths.Length > 0)
            {
                prefabPath = DragAndDrop.paths[0];
            }
        }

        if (GUILayout.Button("查找路径下所有组件"))
        {
            var type = StringToTypeByName(prefabComponentName);
            if (type == null)
            {
                Debug.LogError("组件名不存在");
                return;
            }

            if (string.IsNullOrEmpty(prefabPath) || string.IsNullOrEmpty(prefabComponentName) || prefabComponentCount < 1)
            {
                Debug.LogError("输入路径，名称，数量有误");
                return;
            }

            objWithComponentSet.Clear();
            objDisplayComponentSet.Clear();
            Resources.UnloadUnusedAssets();

            GameObjectComponent objCom;

            string prefabFolder = Path.GetDirectoryName(prefabPath);
            string[] allPath = AssetDatabase.FindAssets("t:Prefab", new string[] { prefabFolder });
            for (int i = 0; i < allPath.Length; i++)
            {
                string path = AssetDatabase.GUIDToAssetPath(allPath[i]);
                var obj = AssetDatabase.LoadAssetAtPath(path, typeof(GameObject)) as GameObject;
                if (obj == null)
                {
                    continue;
                }
                var componentSet = obj.GetComponentsInChildren(type);
                for (int j = 0; j < componentSet.Length; j++)
                {
                    var component = componentSet[j];
                    if (!objWithComponentSet.TryGetValue(component.gameObject, out objCom))
                    {
                        objWithComponentSet[component.gameObject] = new GameObjectComponent()
                        {
                            gameObject = component.gameObject,
                            count = 1,
                            rootPrefab = obj,
                        };
                    }
                    else
                    {
                        objCom.count++;
                    }
                }
            }
            StringBuilder result = new StringBuilder();
            var objIt = objWithComponentSet.GetEnumerator();
            int printCount = 0;
            string objFullPath;
            while (objIt.MoveNext())
            {
                if (objIt.Current.Value.count < prefabComponentCount)
                {
                    continue;
                }
                objCom = objIt.Current.Value;
                objFullPath = GetComponentFullPath(objCom.gameObject);
                objCom.fullPath = objFullPath;
                objDisplayComponentSet[objCom.gameObject] = objCom;

                result.Append(objFullPath + ", count:" + objIt.Current.Value.count);
                result.Append('\n');

                //消息太长会被unity截断
                if (++printCount % 100 == 0 && result.Length > 0)
                {
                    Debug.LogError(result.ToString());
                    result.Remove(0, result.Length);
                }
            }
            objIt.Dispose();
            objWithComponentSet.Clear();
            if(result.Length > 0)
            {
                Debug.LogError(result.ToString());
            }
            result = null;
            Debug.LogError("Total find: " + objDisplayComponentSet.Count + " gameObjects");
        }

        GUILayout.Space(5);
        scrollPos = EditorGUILayout.BeginScrollView(scrollPos, GUIStyle.none, GUI.skin.verticalScrollbar);
        var objWithComponentIt = objDisplayComponentSet.GetEnumerator();
        while (objWithComponentIt.MoveNext())
        {
            if (GUILayout.Button(objWithComponentIt.Current.Value.fullPath))
            {
                Selection.activeGameObject = objWithComponentIt.Current.Value.rootPrefab;
            }
        }
        objWithComponentIt.Dispose();
        EditorGUILayout.EndScrollView();
    }
}
