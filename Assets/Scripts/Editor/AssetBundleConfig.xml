<?xml version="1.0" encoding="utf-8"?>
<!-- 
AssetBundle一个配置选项 name只是这里的标�?
AssetPath	为资源路径?
PostFix		为资源后缀,允许识别多个后缀，用","分隔
Strategy	为打AssetBundle的策略?
	path	路径下的单个资源打包AssetBundle，按路径名作为AB的名称?
	dir		路径下的所有资源打包为AssetBundle，按路径名作为AB的名称?
	tag		按照预先设置的packingtag打包AssetBundle
	customer打包的AssetBundle用这里配置的name属性值作为名称?
  remain fix 名字有歧义?
-->
<AssetBundleConfig>
<!-- 程序脚本 -->
<!-- <AssetBundle name="luascript">
	<AssetPath>Assets/Lua</AssetPath>
	<PostFix>.txt</PostFix>
	<Strategy>customer</Strategy>
</AssetBundle> -->

<!-- <AssetBundle name="exec.asset">
	<AssetPath>Assets/LuaVice/Lua</AssetPath>
	<PostFix>.asset</PostFix>
	<Strategy>customer</Strategy>
</AssetBundle>  -->

<!-- 策划数据文件 -->
<AssetBundle name="configs">
	<AssetPath>Assets/Configs</AssetPath>
	<PostFix>.bytes</PostFix>
	<Strategy>customer</Strategy>
</AssetBundle>

<!-- 策划数据文件 -->
<!-- <AssetBundle name="gamescp">
	<AssetPath>Assets/GameScp</AssetPath>
	<PostFix>.bytes</PostFix>
	<Strategy>customer</Strategy>
</AssetBundle> -->

<!-- 策划数据文件 -->
<AssetBundle name="battlescp">
	<AssetPath>Assets/BattleScp</AssetPath>
	<PostFix>.bytes</PostFix>
	<Strategy>customer</Strategy>
</AssetBundle>

<AssetBundle name="aiconfig">
    <AssetPath>Assets/Data/AI</AssetPath>
	<PostFix>.bytes</PostFix>
    <Strategy>customer</Strategy>
</AssetBundle>
<AssetBundle name="restag">
    <AssetPath>Assets/EditorConfig/ResTag</AssetPath>
	<PostFix>.bytes</PostFix>
    <Strategy>path</Strategy>
</AssetBundle>


<!-- 场景 -->
<!-- <AssetBundle name="scene">
	<AssetPath>Assets/Scene</AssetPath>
	<PostFix>.unity</PostFix>
	<Strategy>path</Strategy>
</AssetBundle> -->
 
<AssetBundle name="mapPrefabs">
	<AssetPath>Assets/Art/Maps/Prefabs</AssetPath>
	<PostFix>.prefab</PostFix>
	<Strategy>path</Strategy>
</AssetBundle>
<AssetBundle name="mapPrefabs">
	<AssetPath>Assets/Art/Maps/ToT/map</AssetPath>
	<PostFix>.prefab</PostFix>
	<Strategy>path</Strategy>
</AssetBundle>
<AssetBundle name="mapTerrainTex">
	<AssetPath>Assets/Art/Maps/Terrains/Textures</AssetPath>
	<PostFix>.png,.tga</PostFix>
	<Strategy>path</Strategy>
</AssetBundle>
<AssetBundle name="mapModels">
	<AssetPath>Assets/Art/Maps/Models</AssetPath>
	<PostFix>.fbx</PostFix>
	<Strategy>path</Strategy>
</AssetBundle>
<AssetBundle name="mapMaterials">
	<AssetPath>Assets/Art/Maps/Materials</AssetPath>
	<PostFix>.mat</PostFix>
	<Strategy>path</Strategy>
</AssetBundle> 
<AssetBundle name="mapTex">
	<AssetPath>Assets/Art/Maps/TexturesSingle</AssetPath>
	<PostFix>.png,.tga</PostFix>
	<Strategy>path</Strategy>
</AssetBundle>

<!-- 场景图集纹理 -->
<AssetBundle name="mapSprites" subdir="True">
	<AssetPath>Assets/Art/Maps/Textures</AssetPath>
	<PostFix>.png,.tga</PostFix>
	<Strategy>settagbypath</Strategy>
</AssetBundle> 
<!-- 场景纹理 -->
<AssetBundle name="mapSprites" subdir="True">
	<AssetPath>Assets/Art/Maps/Sprites</AssetPath>
	<PostFix>.png,.tga</PostFix>
	<Strategy>settagbypath</Strategy>
</AssetBundle>



<!-- 纹理 -->
<AssetBundle name="mapSprites" subdir="True" priority="-1">
	<AssetPath>Assets/Art/Sprites</AssetPath>
	<PostFix>.png,.tga</PostFix>
	<Strategy>settagbypath</Strategy>
</AssetBundle>
<!-- 建筑纹理 -->
<AssetBundle name="mapSprites" subdir="True">
	<AssetPath>Assets/Art/Sprites/Buildings</AssetPath>
	<PostFix>.png,.tga</PostFix>
	<Strategy>settagbypath</Strategy>
</AssetBundle>

<!-- 怪物纹理 -->
<AssetBundle name="mapSprites" subdir="True">
	<AssetPath>Assets/Art/Sprites/Monsters</AssetPath>
	<PostFix>.png,.tga</PostFix>
	<Strategy>settagbypath</Strategy>
</AssetBundle>

<!-- 跳伞 -->
<AssetBundle name="Parachute" subdir="True">
	<AssetPath>Assets/Art/Sprites/tiaosan</AssetPath>
	<PostFix>.png,.tga</PostFix>
	<Strategy>settagbypath</Strategy>
</AssetBundle>   
 
<AssetBundle name="mapSprites" subdir="True">
	<AssetPath>Assets/Art/Effects/Textures</AssetPath>
	<PostFix>.png,.tga</PostFix>
	<Strategy>settagbypath</Strategy>
</AssetBundle>


<!-- 人物
<AssetBundle name="CharacterTextures">
	<AssetPath>Assets/Art/Characters</AssetPath>
	<PostFix>.tga,.png</PostFix>
	<Strategy>path</Strategy>
</AssetBundle>
<AssetBundle name="Character">
	<AssetPath>Assets/Art/Characters/002</AssetPath>
	<PostFix>.prefab</PostFix>
	<Strategy>path</Strategy>
</AssetBundle> -->
<!-- 技能?-->
<AssetBundle name="BulletTracks">
	<AssetPath>Assets/Art/Skills/BulletTracks</AssetPath>
	<PostFix>.asset</PostFix>
	<Strategy>path</Strategy>
</AssetBundle>
<!-- 特效 -->
<AssetBundle name="EffectTextures">
	<AssetPath>Assets/Art/Effects/Single</AssetPath>
	<PostFix>.tga,.png</PostFix>
	<Strategy>path</Strategy>
</AssetBundle>
<!-- 物品 -->
<AssetBundle name="Accessories">
	<AssetPath>Assets/Art/DropItems/Accessories/Prefabs</AssetPath>
	<PostFix>.prefab</PostFix>
	<Strategy>path</Strategy>
</AssetBundle>
<AssetBundle name="Clothes">
	<AssetPath>Assets/Art/DropItems/Clothes/Prefabs</AssetPath>
	<PostFix>.prefab</PostFix>
	<Strategy>path</Strategy>
</AssetBundle>
<AssetBundle name="Others">
	<AssetPath>Assets/Art/DropItems/Others/Prefabs</AssetPath>
	<PostFix>.prefab</PostFix>
	<Strategy>path</Strategy>
</AssetBundle>
<AssetBundle name="Weapons">
	<AssetPath>Assets/Art/DropItems/Weapons/Prefabs</AssetPath>
	<PostFix>.prefab</PostFix>
	<Strategy>path</Strategy>
</AssetBundle>
<!-- 载具 -->
<AssetBundle name="Vehicles">
	<AssetPath>Assets/Art/Vehicles/Prefabs</AssetPath>
	<PostFix>.prefab</PostFix>
	<Strategy>path</Strategy>
</AssetBundle>

<!-- 特效 -->
<AssetBundle name="Effects">
	<AssetPath>Assets/Art/Effects/Prefabs</AssetPath>
	<PostFix>.prefab</PostFix>
	<Strategy>path</Strategy>
</AssetBundle>

<!-- Water -->
<AssetBundle name="Effects">
	<AssetPath>Assets/Art/Maps/Terrain/Water</AssetPath>
	<PostFix>.png,.tga</PostFix>
	<Strategy>path</Strategy>
</AssetBundle>
<!-- anima2d -->
<!-- <AssetBundle name="anima2d"  subdir="True" forceUpdateTag="False">
	<AssetPath>Assets/Animations/Characters</AssetPath>
	<PostFix>.png,.tga</PostFix>
	<Strategy>settagbypath</Strategy>
</AssetBundle> -->

<!-- spriteatlas -->
<!-- <AssetBundle name="spriteatlas">
	<AssetPath>Assets/UI/0SpriteAtlas</AssetPath>
	<PostFix>.spriteatlas</PostFix>
	<Strategy>path</Strategy>
</AssetBundle> -->
<!-- UI -->
<!-- UI -->
<AssetBundle name="UI" subdir="True"   forceUpdateTag="False">
	<AssetPath>Assets/UI</AssetPath>
	<PostFix>.png,.tga</PostFix>
	<Strategy>settagbypath</Strategy>
</AssetBundle>
<AssetBundle name="UI" subdir="True"  forceUpdateTag="False">
	<AssetPath>Assets/UI</AssetPath>
	<PostFix>.asset</PostFix>
	<Strategy>path</Strategy>
</AssetBundle>
</AssetBundleConfig>
