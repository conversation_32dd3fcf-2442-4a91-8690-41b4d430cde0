#if UNITY_EDITOR
using Sirenix.OdinInspector.Demos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using UnityEngine;

namespace Assets.Scripts.Editor
{
    public class AssetsChecker
    {
        public static void Check()
        {
            Debug.Log("Execute ExludeFile.Instance.Check");
            ExludeFile.Instance.Check();
            Debug.Log("Execute ExludeFile.Instance.Check Completely");
        }
    }
}
#endif
