local print = print
local GameObject = CS.UnityEngine.GameObject
local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local class = require "class"
local ui_base = require "ui_base"
local typeof = typeof
local Transform= CS.UnityEngine.Transform
local Image = CS.UnityEngine.UI.Image
local string = string

--列表相关
local ScrollRect = CS.UnityEngine.UI.ScrollRect
local table = table
local com_class = require "com_class"
local com_event = require "com_event"
local module_scroll_list = require "scroll_list"
local CScrollList = module_scroll_list.CScrollList
local CScrollListItemBase = module_scroll_list.CScrollListItemBase

--通信相关
local net = require "net"
local require = require
local pairs = pairs
local virtual_server = require "virtual_server"
local require = require
local msg_pb = require "msg_pb"
local prop_pb = require "prop_pb"
local zone_pb = require "zone_pb"
local demo_pb = require "demo_pb"

module("demo")

local DemoPanel = {}
local window = nil

--组件表
DemoPanel.widget_table = {
    ButtonClose = { path = "ButtonClose", type = "Button", },
    ImformationText = { path = "MainPanel/InfromationText/Text", type = "Text", },
    ItemList = { path = "MainPanel/ScrollView", type = ScrollRect, },
    BtnStartServer = { path = "BtnStartServer", type = "Button", },
    BtnSendMessage = { path = "BtnSendMessage", type = "Button", },
    --StartNumber = { path = "InputFieldStartNumber/Text", type = "Text", },
    --Count = { path = "InputFieldCount/Text", type = "Text", },
}

--创建继承于CScrollListItemBase的对象
local DemoItem = com_class.CreateClass(CScrollListItemBase)

--获取子组件
function GetChildComponent(tr,path,type)
	return tr:FindChild(path):GetComponent(type) 
end

--创建一个Item
function DemoItem:Create(goSkin)
	DemoItem.__super.Create(self, goSkin)
  
  self.ui = {}
  self.ui["text"] = GetChildComponent(self,"Text",typeof(Text))
  self.ui["selectedImg"] = GetChildComponent(self,"Selected",typeof(Image))
	self.ui["selectedImg"]:SetActive(false)
end

--销毁，清空ui列表
function DemoItem:Destroy()
	for k,_ in pairs(self.ui) do
		self.ui[k]=nil
	end
	self.ui=nil
  DemoItem.__super.Destroy(self)
end

--绘制一个Item
function DemoItem:Draw(data, i)
	self.data=data
	self.ui["text"].text = data.text
  
end

--Item被选中时，显示选中图片，
function DemoItem:OnSelected()
  self.ui["selectedImg"]:SetActive(true)
end

--Item失去选中时，隐藏选中图片
function DemoItem:OnLostSelected()
  self.ui["selectedImg"]:SetActive(false)
end


--初始化列表数据
function DemoPanel:InitData()
	self.listInstance = CScrollList:CreateInstance({})
	self.listInstance:Create(self.ItemList, DemoItem)
  
  --更新列表Item
	--self:UpdateList()
	
  --注册事件
  --self.listInstance:SubscribeEvent(module_scroll_list.EVENT_LIST_CHANGE, self.UpdateList, self)
end

--更新列表，在服务器发来消息时
function DemoPanel:UpdateList(msg)
  if msg == nil then
    print("!! DemoPanel:UpdateList(msg) msg is nil")
    return;
  end
  print("Update Message")
  
  datas = {}
  for i = 1, #msg.arrItem do
    local cData = {}
    cData.text = msg.arrItem[i].number
    table.insert(datas, cData)
  end
  self.listInstance:SetListData(datas)
  
  --对选中Item信息放到信息栏
  local data = self.listInstance:GetSelectItemData()
	if data == nil then
    self.ImformationText.text = "empty"
  else
    self.ImformationText.text = "selected item by index : " .. data.text
	end
  
end

--面板初始化，关闭按钮事件，开启服务器按钮事件，发送信息按钮事件
function DemoPanel:Init()
  print("Demo Init")
  if self.ButtonClose then
    self.ButtonClose.onClick:AddListener(Close)
  end
  
  if self.BtnStartServer then
    self.BtnStartServer.onClick:AddListener(StartServer)
  end
  
  if self.BtnSendMessage then
    self.BtnSendMessage.onClick:AddListener(self.SendMessage)
  end
  
  self:InitData() -- 初始化列表数据
end

--向服务器发送信息：列表长度，起始数据
function DemoPanel:SendMessage()
  print("Send Message")
  local msg = demo_pb.TMSG_DEMO_REQ()
  --[[
  if self.StartNumber.text and self.Count then
  
  end
  ]]
  
  msg.listCount  = 35;
  msg.startNumber = 123;
  net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_DEMO_REQ, msg)	
end

--面板关闭释放资源
function DemoPanel:Close()
  if self.ButtonClose then
    self.ButtonClose.onClick:RemoveListener(Close)
  end
  if self.listInstance then
    --self.listInstance:UnsubscribeEvent(module_scroll_list.EVENT_LIST_CHANGE, self.UpdateList, self)
    self.listInstance = nil
  end
end

local CDemoPanel = class(ui_base, nil, DemoPanel)

--显示面板
function Show()
  print("Demo Show")
  if window == nil then
    window = CDemoPanel()
    window._NAME = _NAME;window:LoadUIResource("demo/demoui.prefab",nil,
      GameObject.Find("UIRoot/DemoCanvas").transform,nil)
      
    RegisterMessage()   --注册服务器响应事件
  end
  window:Show()
  return window
end

--接收到服务器时响应
function OnResponseMesssage(msg)
  if window then
    window:UpdateList(msg)
  end
end

--注册消息，接收到服务器返回的消息，更新列表
function RegisterMessage()
    local MessageTable = 
    {
      { msg_pb.MSG_DEMO_RSP, OnResponseMesssage ,demo_pb.TMSG_DEMO_RSP, },
    }

    local net_route = require "net_route"
    net_route.RegisterMsgHandlers(MessageTable)
end

--隐藏面板
function Hide()
  print("Demo Hide")
  if window ~= nil then
    window:Hide()
  end
end

--关闭面板（先隐藏后删除）
function Close()
  print("Demo Close")
  if window ~= nil then
		Hide()
    window:Close()
		window = nil
	end
end

-- /// 虚拟服务器相关

--标记服务器是否启动
local IsStarted = false
local HANDLER = {
    "demo_msg_handler",
}

--[[启动]]
function StartServer()
    if IsStarted then
        return
    end
    print("StartServer")
    virtual_server.Start()
    for _,m in pairs(HANDLER) do
        virtual_server.RegModule(m)
    end
    
    IsStarted = true
end

--[[停止]]
function StopServer()
    for _,m in pairs(HANDLER) do
        virtual_server.UnregModule(m)
    end
    virtual_server.Stop()
    IsStarted = false
end






