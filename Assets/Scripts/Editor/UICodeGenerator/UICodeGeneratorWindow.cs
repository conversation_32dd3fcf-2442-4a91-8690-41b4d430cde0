using UnityEditor;
using UnityEngine;
using System.IO;
using System.Collections.Generic;
using System.Text.RegularExpressions;

namespace UICodeGenerator
{
    public class UICodeGeneratorWindow : EditorWindow
    {
        public string folderPath = "Assets/Lua";
        public string fileName = "";
        //public RegisterMessage register = new RegisterMessage();
        public string[] registerArr;
        public bool newRegister = false;

        private string FilePath { get { return folderPath + "/" + fileName + ".txt"; } }
        private string IFilePath { get { return folderPath + "/i" + fileName + ".txt"; } }

        private Vector2 scrollPos;                  // 滑动面板位置

        [MenuItem("Tool/UI Code Generator")]
        static void ShowWindow()
        {
            
            EditorWindow window = GetWindow<UICodeGeneratorWindow>();
            window.Show();
        }

        private void OnGUI()
        {
            PrefabInfo.prefab = EditorGUILayout.ObjectField("Prefab : ", PrefabInfo.prefab, typeof(GameObject), true) as GameObject;
            #region 获取名字
            if (string.IsNullOrEmpty(fileName) && PrefabInfo.prefab != null)
            {
                // UI开头，大写连小写
                Regex reg = new Regex(@"(UI)?([A-Z]+[a-z]+)");
                var matchs = reg.Matches(PrefabInfo.Name);
                foreach (Match match in matchs)
                {
                    fileName = fileName + string.Format("{0}_{1}_", match.Groups[1].Value.ToLower(), match.Groups[2].Value.ToLower());
                }
                fileName = Regex.Replace(fileName, "__", "_");
                fileName = Regex.Replace(fileName, "_$", "");
                //fileName = PrefabInfo.Name.ToLower();
            }
            #endregion

            GUILayout.BeginHorizontal();
            #region 自动找到路径
            var paths = Directory.GetFiles(Application.dataPath + "/Lua", fileName + ".txt", SearchOption.AllDirectories);
            if (paths.Length > 0)
            {
                var path = paths[0];
                folderPath = Regex.Match(path.Replace("\\", "/"), @"(Assets/Lua.*)/[^/]+$").Groups[1].Value;
            }
            #endregion

            folderPath = EditorGUILayout.TextField("Folder Path :", folderPath);
            if (GUILayout.Button("选择生成文件的目录"))
                folderPath = EditorUtility.OpenFolderPanel("生成文件的目录", folderPath, "");
            GUILayout.EndHorizontal();

            EditorGUILayout.Space();

            GUILayout.BeginHorizontal();
            
            fileName = EditorGUILayout.TextField("File Name：", fileName/*.ToLower()*/);
            Config.FileName = fileName;
            
            Config.updateMask = (Config.EUpdateMask)EditorGUILayout.EnumMaskField("选择需要更新的标签：", Config.updateMask);

            //EditorGUILayout.LabelField("接口文件名：i" + fileName);
            GUILayout.EndHorizontal();
            EditorGUILayout.Space();

            GUILayout.BeginHorizontal();
            if (GUILayout.Button("创建实现文件："+fileName + ".txt"))
            {
                if (PrefabInfo.prefab == null)
                    EditorUtility.DisplayDialog("请选择预设！", "请选择预设！", "确认");
                else if (File.Exists(FilePath))
                    EditorUtility.DisplayDialog("实现文件已存在！", FilePath, "确认");
                else
                {
                    RegisterMessage.instance = new RegisterMessage(registerArr);
                    Config.LoadConfigFile();
                    InitPrefabInfo();
                    if (Parser.Create(folderPath, fileName, false))
                        Debug.Log("Create : " + FilePath);
                }
            }

            if (GUILayout.Button("更新实现文件：" + fileName + ".txt"))
            {
                if (PrefabInfo.prefab == null)
                    EditorUtility.DisplayDialog("请选择预设！", "请选择预设！", "确认");
                else if (!File.Exists(FilePath))
                    EditorUtility.DisplayDialog("实现文件不存在！", FilePath, "确认");
                else
                {
                    RegisterMessage.instance = new RegisterMessage(registerArr);
                    Config.LoadConfigFile();
                    InitPrefabInfo();
                    if (!Parser.Update(FilePath))
                        Debug.LogError("更新实现文件失败！");
                }
            }

            if (GUILayout.Button("创建接口文件：i" + fileName + ".txt"))
            {
                if (PrefabInfo.prefab == null)
                    EditorUtility.DisplayDialog("请选择预设！", "请选择预设！", "确认");
                else if (File.Exists(IFilePath))
                    EditorUtility.DisplayDialog("接口文件已存在！", IFilePath, "确认");
                else
                {
                    RegisterMessage.instance = new RegisterMessage(registerArr);
                    Config.LoadConfigFile();
                    Parser.CreateInterface(folderPath, fileName);
                    Debug.Log("Create : " + IFilePath);
                }
            }
            if (GUILayout.Button("创建实现ViewCtrl文件："+fileName + ".txt"))
            {
                if (PrefabInfo.prefab == null)
                    EditorUtility.DisplayDialog("请选择预设！", "请选择预设！", "确认");
                else if (File.Exists(FilePath))
                    EditorUtility.DisplayDialog("实现文件已存在！", FilePath, "确认");
                else
                {
                    RegisterMessage.instance = new RegisterMessage(registerArr);
                    Config.LoadConfigFile();
                    InitPrefabInfo();
                    ViewAndCtrlParser.CreateViewAndCtrl(folderPath, fileName, false);
                }
            }
            GUILayout.EndHorizontal();
            EditorGUILayout.Space();

            if (Config.ContainMask(Config.EUpdateMask.RegisterMsg))
                RegisterMsgBox();

            if (Config.ContainMask(Config.EUpdateMask.RegisterEvent))
                ;
        }

        /// <summary>
        /// 初始化预设信息
        /// </summary>
        private void InitPrefabInfo()
        {
            GameObject obj = Instantiate(PrefabInfo.prefab);

            PrefabInfo.Init(obj, array => { foreach (var item in array) DestroyImmediate(item); });

            DestroyImmediate(obj);
        }

        private void RegisterMsgBox()
        {
            scrollPos = EditorGUILayout.BeginScrollView(scrollPos);
            GUILayout.BeginVertical("Box");

            EditorGUILayout.LabelField("消息注册： 输入例：\"abc_pb.TMSG_AAA\", 生成注册消息：\"{msg_pb.MSG_AAA, OnMSG_AAA, abc_pb.TMSG_AAA}\"");
            //序列化myData对象，让它可以显示在编辑窗口。
            SerializedObject serializedObject = new SerializedObject(this);
            SerializedProperty serializedProperty = serializedObject.FindProperty("registerArr");
            EditorGUILayout.PropertyField(serializedProperty, true);
            serializedObject.ApplyModifiedProperties();                     //应用修改的属性

            GUILayout.EndVertical();
            EditorGUILayout.EndScrollView();
        }
    }
}