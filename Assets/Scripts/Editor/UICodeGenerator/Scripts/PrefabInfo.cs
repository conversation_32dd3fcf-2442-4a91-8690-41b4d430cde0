using System;
using System.Collections.Generic;
using System.Text;
using UnityEngine;
using UnityEditor;
using UnityEngine.UI;
using UI.UGUIExtend;

namespace UICodeGenerator
{
    static public class PrefabInfo
    {
        static public GameObject prefab;

        /// <summary>
        /// 预设名称
        /// </summary>
        static public string Name { get { return prefab == null ? "" : prefab.name; } }

        /// <summary>
        /// 资源的AB路径
        /// </summary>
        static public string ABPath
        {
            get
            {
                string path = AssetDatabase.GetAssetPath(prefab);
                return path.Substring(path.IndexOf('/') + 1).ToLower();
            }
        }

        /// <summary>
        /// 组件列表
        /// </summary>
        static public List<WidgetInfo> widgetList = new List<WidgetInfo>();

        static private Button[] btnArr;
        static private Text[] textArr;
        static private ScrollRectTable[] scrollArr;
        static private Image[] imgArr;

        static private void Clean()
        {
            if (btnArr != null)
                Array.Clear(btnArr, 0, btnArr.Length);
            if (textArr != null)
                Array.Clear(textArr, 0, textArr.Length);
            if (scrollArr != null)
                Array.Clear(scrollArr, 0, scrollArr.Length);
            if (imgArr != null)
                Array.Clear(imgArr, 0, imgArr.Length);
            widgetList.Clear();


			WidgetInfo.nameDic.Clear ();
			nameDic.Clear ();
        }

        /// <summary>
        /// 预设信息的初始化
        /// </summary>
        static public void Init(GameObject obj, System.Action<ScrollRectTable[]> action)
        {
            Clean();

            List<string> listPath = new List<string>();
            
            scrollArr = obj.GetComponentsInChildren<ScrollRectTable>();
            int scrollArrLength = scrollArr.Length;
            for (int i = 0; i < scrollArrLength; i++)
                if (scrollArr[i].name.StartsWith(Config.WidgetPrefix))
                    widgetList.Add(new WidgetInfo(EWidgetType.ScrollRectTable, GetWidgetPath(scrollArr[i].gameObject, obj)));

            if (scrollArrLength > 0)
                action.Invoke(scrollArr);

            btnArr = obj.GetComponentsInChildren<Button>();
            int btnArrLength = btnArr.Length;
            for (int i = 0; i < btnArrLength; i++)
                if (btnArr[i].name.StartsWith(Config.WidgetPrefix))
					widgetList.Add(new WidgetInfo(EWidgetType.Button, GetWidgetPath(btnArr[i].gameObject, obj,EWidgetType.Button)));

            textArr = obj.GetComponentsInChildren<Text>();
            int textArrLength = textArr.Length;
            for (int i = 0; i < textArrLength; i++)
                if (textArr[i].name.StartsWith(Config.WidgetPrefix))
					widgetList.Add(new WidgetInfo(EWidgetType.Text, GetWidgetPath(textArr[i].gameObject, obj,EWidgetType.Text)));

            scrollArr = obj.GetComponentsInChildren<ScrollRectTable>();
            scrollArrLength = scrollArr.Length;
            for (int i = 0; i < scrollArrLength; i++)
                if (scrollArr[i].name.StartsWith(Config.WidgetPrefix))
					widgetList.Add(new WidgetInfo(EWidgetType.ScrollRectTable, GetWidgetPath(scrollArr[i].gameObject, obj,EWidgetType.ScrollRectTable)));

            imgArr = obj.GetComponentsInChildren<Image>();
            int imgArrLength = imgArr.Length;
            for (int i = 0; i < imgArrLength; i++)
                if(imgArr[i].transform.GetComponent<Button>() == null && imgArr[i].transform.GetComponent<ScrollRect>() == null)
                    if (imgArr[i].name.StartsWith(Config.WidgetPrefix))
					widgetList.Add(new WidgetInfo(EWidgetType.Image, GetWidgetPath(imgArr[i].gameObject, obj,EWidgetType.Image)));


			var rstfArr = obj.GetComponentsInChildren<RectTransform>();
			var rstfArrLength = rstfArr.Length;
			for (int i = 0; i < rstfArrLength; i++)
				if (rstfArr[i].name.StartsWith(Config.WidgetPrefix)){
					var path = GetWidgetPath (rstfArr [i].gameObject, obj,EWidgetType.RectTransform);
					if(widgetList.Find (w=>w.Path == path) !=null)continue;
					widgetList.Add(new WidgetInfo (EWidgetType.RectTransform, path));
				}

        }
		readonly public static Dictionary<string, bool> nameDic = new Dictionary<string, bool>();

        /// <summary>
        /// 获取组件相对层级字符串
        /// </summary>
		static private string GetWidgetPath(GameObject obj, GameObject top,EWidgetType type=EWidgetType.None)
        {
            StringBuilder builder = new StringBuilder(obj.name);
            Transform parent = obj.transform.parent;
            while (parent != null && parent != top.transform)
            {
                builder.Insert(0, parent.name + "/");
                parent = parent.parent;
            }
			var path = builder.ToString ();

			var namekey = path + "|" + (int)type;
			if (nameDic.ContainsKey (namekey)){
				var uCode = (uint)obj.GetHashCode ();
				obj.name = obj.name + uCode;
				path = path + uCode;
			}
			namekey = path + "|" + (int)type; 
			nameDic [namekey] = true;
			return path;
        }


    }
}
