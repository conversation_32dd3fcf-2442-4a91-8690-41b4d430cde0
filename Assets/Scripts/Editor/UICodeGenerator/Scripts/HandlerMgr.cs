using System;
using System.Collections.Generic;
using System.Text;

namespace UICodeGenerator
{
    static class HandlerMgr
    {
        readonly static public Dictionary<ENodeType, INodeHandler> Handler = new Dictionary<ENodeType, INodeHandler>
        {
            { ENodeType.FileHead, new FileHeadNodeHandler() },
            { ENodeType.Require, new RequireNodeHandler() },
            { ENodeType.ModuleDeclare, new ModuleDeclareNodeHandler() },
            { ENodeType.WidgetTable, new WidgetTableNodeHandler() },
            { ENodeType.WinodwFunction, new WindowFunctionNodeHandler() },
            { ENodeType.WindowInherited, new WindowInheritedNodeHandler() },
            { ENodeType.ModuleFunction, new ModuleFunctionNodeHandler() },
            { ENodeType.ScrollItem, new ScrollItemNodeHandler() },
            { ENodeType.RegisterMsg, new RegisterMsgNodeHandler() },
            { ENodeType.WindowCtor, new FixedFuncNodeHandler(Config.WindowCtor) },
            { ENodeType.WindowOnShow, new FixedFuncNodeHandler(Config.WindowOnShow) },
            { ENodeType.WindowOnHide, new FixedFuncNodeHandler(Config.WindowOnHide) },
            { ENodeType.WindowSetInputParam, new FixedFuncNodeHandler(Config.WindowSetInputParam) },
            { ENodeType.WindowBuildUpdateData, new FixedFuncNodeHandler(Config.WindowBuildUpdateData) },
            { ENodeType.WindowUpdateUI, new FixedFuncNodeHandler(Config.WindowUpdateUI) },
            { ENodeType.WindowInit, new WindowInitNodeHandler() },
            { ENodeType.WindowClose, new WindowCloseNodeHandler() },
            { ENodeType.WindowSubscribeEvents, new WindowSubscribeEventsNodeHandler() },
            { ENodeType.WindowUnsubscribeEvents, new WindowUnsubscribeEventsNodeHandler() },
            { ENodeType.WindowBtnFunctions, new WindowButtonFunctionsNodeHandler() },

        };
    }
}
