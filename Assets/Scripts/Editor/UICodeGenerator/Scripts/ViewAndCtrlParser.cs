using System.IO;
using System.Linq;
using System.Text;
using UnityEditor;
using UnityEngine;

namespace UICodeGenerator
{
    public class ViewAndCtrlParser
    {
        static public bool CreateViewAndCtrl(string folderPath, string fileName, bool createInterface = true)
        {
            string srcViewFile = folderPath + '/' + fileName + ".txt";
            string srcCtrlFile = folderPath + '/' + fileName + "_controller" + ".txt";
            if (File.Exists(srcViewFile) || File.Exists(srcCtrlFile))
                return false;
            EditorHelp.CheckDir(srcViewFile);
            StringBuilder viewStrBuild = new StringBuilder();
            StringBuilder CtrlStrBuild = new StringBuilder();
            int widgetCount = PrefabInfo.widgetList.Count; // 获取组件表
            for (int i = 0; i < widgetCount; ++i)
            {
                string viewWidgetStr = PrefabInfo.widgetList[i].WidgetDefine; 
                viewStrBuild.Append(viewWidgetStr);
                viewStrBuild.Append('\n');
                if (PrefabInfo.widgetList[i].Type == EWidgetType.Button)
                {
                    string ctrlwidgetStr = "On" + PrefabInfo.widgetList[i].Name + "ClickedProxy";
                    CtrlStrBuild.Append($@"function  UIController:{ctrlwidgetStr}()
end");
                    CtrlStrBuild.Append('\n');
                }
            }
            string viewStr = viewCreate(viewStrBuild.ToString(),fileName);
            string ctrlStr = CtrlCreate(CtrlStrBuild.ToString(),fileName);
            SaveFile(srcViewFile, viewStr); // 写入文件
            SaveFile(srcCtrlFile, ctrlStr); // 写入文件
            return true;
        }
        /// <summary>
        /// 保存文件
        /// </summary>
        /// <param name="path">保存路径</param>
        /// <param name="content">文件内容</param>
        /// <param name="iscover">存在是否进行覆盖,默认true</param>
        public static void SaveFile(string path, string content,bool isDelet = true)
        {
            FileInfo info = new FileInfo(path);
            if (!isDelet && info.Exists)
            {
                return;
            }
            FileStream fs = new FileStream(path, FileMode.Create);
            StreamWriter sWriter = new StreamWriter(fs, Encoding.GetEncoding("UTF-8"));
            sWriter.WriteLine(content);
            sWriter.Flush();
            sWriter.Close();
            fs.Close();
            UnityEngine.Debug.Log($"成功生成文件 {path}");
            AssetDatabase.Refresh();
        }
        public static string viewCreate(string widgeStr,string fileName)
        {
            string viewstr = $@"--region FileHead
--- {fileName}.txt
-- author:  author
-- ver:     1.0
-- desc:    
-------------------------------------------------
--endregion 

--region Require
local print = print
local require = require
local type = type
local pairs = pairs
local ipairs = ipairs
local typeof = typeof
local string = string
local table = table
local handle = handle
local dump = dump
local tostring = tostring
local log = log

local GameObject = CS.UnityEngine.GameObject
local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local Image = CS.UnityEngine.UI.Image
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable
local const = require ""const""
local enum_define = require ""enum_define""
local event = require ""event""
local class = require ""class""
local lang = require ""lang""
local ui_base = require ""ui_base""
local com_class = require ""com_class""
local com_event = require ""com_event""
local module_scroll_list = require ""scroll_list""
local CScrollList = module_scroll_list.CScrollList
local CScrollListItemBase = module_scroll_list.CScrollListItemBase
local Common_Util = CS.Common_Util.UIUtil
local net_route = require ""net_route""
local e_handler_mgr = require ""e_handler_mgr""
local game_scheme = require ""game_scheme""
local card_assets = require ""card_sprite_asset""

local RectTransform = CS.UnityEngine.RectTransform
local windowMgr = require ""ui_window_mgr""
--endregion 

--region ModuleDeclare
module(""{fileName}"")
local ui_path = ""{PrefabInfo.ABPath}""
local window = nil
local UIView = {{}}
--endregion 

--region WidgetTable
UIView.widget_table = {{
  {widgeStr} 
}}
--endregion 

--region function 设置View-Controller模式的UI
-- return type  ---- 未定义/VC/纯V   
-- 注意，View-Controller模式的ui必须要重写这个接口
function UIView:SetVCTypeUI()
    return self.__base:SetVCTypeUI(enum_define.enum_ui_vc_type.vc)
end
--endregion 

--region WindowInit
--[[窗口初始化]]
function UIView:Init()
    self:SetVCTypeUI()
    self.__base:Init(self)
    self:SubscribeEvents()    
    --region User
    --endregion 
end --///<<< function

--endregion 


--region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function UIView:OnShow()
    self.__base:OnShow()
end --///<<< function

--endregion 

--region WindowOnHide
--[[界面隐藏时调用]]
function UIView:OnHide()
    self.__base:OnHide()    
end --///<<< function

--endregion 


--region WindowClose
function UIView:Close()
    self.__base:Close()
    self:UnsubscribeEvents() 
    window = nil   
    --region User
    --endregion 
end --///<<< function
--endregion 
--region 事件注册
function UIView:SubscribeEvents()    
    -- 注意 事件建议使用self:RegisterEvent(event_name,func)来注册，不需要手动维护注销了    
end
function UIView:UnsubscribeEvents()    
    
end

--endregion

--region 功能函数区
---********************功能函数区**********---


---********************end功能函数区**********---
--endregion
--region WindowInherited
local CUIView = class(ui_base, nil, UIView)
--endregion 

--region static ModuleFunction 
-- 特别注意，当前并不是由controller层来驱动ui的生命流程的 
-- 当前因为需要view层 也就是ui_base来驱动ui的init  加载完成，show等流程，所以流程仍然保留，而controller层的流程逻辑受view流程影响，
-- view对应的Init/Show 加载完后，当前会通过事件同步调用controller层的Init/Show流程，controller层的流程逻辑才会执行。
--当前仍然保留了静态的Show，Close接口流程
function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
        if data and type(data) == ""table"" then
            local uiPath = data.uiPath or ui_path
            local uiParent = data.uiParent or nil
            window:LoadUIResource(uiPath, nil, uiParent, nil)
        else
            window:LoadUIResource(ui_path, nil, nil, nil)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close()
        window = nil
    end
end
--endregion";
            return viewstr;
        }

        public static string CtrlCreate(string widgeStr,string fileName)
        {
            string ctrlStr = $@"
--- {fileName}_controller.txt
--- Generated by EmmyLua(https://github.com/EmmyLua)
--- Created by .
--- DateTime: 
--- desc:    
---
local print = print
local require = require
local pairs = pairs
local ipairs = ipairs
local typeof = typeof
local string = string
local table = table
local handle = handle
local dump = dump
local newclass = newclass

local util                  = require ""util""
local event = require ""event""
local class = require ""class""
local ui_base = require ""ui_base""
local com_class = require ""com_class""
local com_event = require ""com_event""
local module_scroll_list = require ""scroll_list""
local CScrollList = module_scroll_list.CScrollList
local CScrollListItemBase = module_scroll_list.CScrollListItemBase
local net_route = require ""net_route""
local log = require ""log""
local controller_base = require ""controller_base""
local e_handler_mgr = require ""e_handler_mgr""
local tostring = tostring
local player_mgr = require ""player_mgr""
local flow_text = require ""flow_text""
local game_scheme = require ""game_scheme""
local windowMgr = require ""ui_window_mgr""
module(""{fileName}_controller"")
local controller = nil
local UIController = newclass(""{fileName}_controller"", controller_base)

--[[窗口初始化]]
function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self,view_name,controller_name)    
end

--[[界面被显示的时候调用]]
function UIController:OnShow()
    self.__base.OnShow(self)
end
{widgeStr}
function UIController:Close()
    self.__base.Close(self)
    controller = nil
end

--会基类自动调用
function UIController:AutoSubscribeEvents()
    -- 注意 事件建议使用self:RegisterEvent(event_name,func)来注册，不需要手动维护注销了    
end
--会基类自动调用
function UIController:AutoUnsubscribeEvents()   

end
---********************功能函数区**********---


---********************end功能函数区**********---
--region ModuleFunction 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion";
            return ctrlStr;
        }
    }
}