
namespace UICodeGenerator
{
    sealed public class ScrollRectInfo
    {
        public string Name { get; private set; }
        public ScrollRectInfo(string name) { Name = name; }
        public string ScrollInitListLabel { get { return ScrollInitListFunc.Split('\r')[0]; } }
        public string ScrollInitListFunc { get { return Config.ScrollInitListFunc.Replace("#WidgetName#", Name); } }
        public string ScrollItemRenderLabel { get { return ScrollItemRenderFunc.Split('\r')[0]; } }
        public string ScrollItemRenderFunc { get { return Config.ScrollItemRenderFunc; } }
        public string ScrollItemDisposeLabel { get { return ScrollItemDisposeFunc.Split('\r')[0]; } }
        public string ScrollItemDisposeFunc { get { return Config.ScrollItemDisposeFunc; } }
    }
}
