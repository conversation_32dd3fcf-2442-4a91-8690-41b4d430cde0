
namespace UICodeGenerator
{
    static public class InterfaceFile
    {
        public static string IName { get { return "i" + Config.FileName; } }

        /// <summary>
        /// 获取默认的接口文件字符串
        /// </summary>
        public static string GetDefualt()
        {
            return Config.InterfaceText;
        }

        /// <summary>
        /// 更新接口文件的信息
        /// </summary>
        public static string Update(string fileStr)
        {
            return fileStr.Replace("#IFileNameWithSuffix#", IName)
                .Replace("#Author#", Config.Author)
                .Replace("#Date#", Config.Date)
                .Replace("#Version#", "1.0")
                .Replace("#Description#", "Description")
                .Replace("#IFileName#", IName)
                .Replace("#FileName#", Config.FileName)
                .Replace("#MessageItems#", RegisterMessage.instance.GetAllRegisterItemStr())
                .Replace("#MessageFunc#", RegisterMessage.instance.GetAllHandlerFuncStr());

        }

    }
}
