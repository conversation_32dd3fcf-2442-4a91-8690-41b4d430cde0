using System.Collections.Generic;
using System.Text;

namespace UICodeGenerator
{
    public class NodeContext : INodeContext
    {
        public ENodeType NodeType { get; private set; }
        public string NodeName { get { return NodeType.ToString(); } }
        public string NodeBeiginStr { get { return Config.BeginString(NodeName); } }
        public string NodeEndStr { get { return Config.EndString(NodeName); } }

        public string content = string.Empty;
        public List<INodeContext> childrenNodeList = new List<INodeContext>();

        /// <summary>
        /// ��㹹�캯��
        /// </summary>
        /// <param name="type">Ĭ������Ϊ�����ݽ��</param>
        /// <param name="addContentNode">�Ƿ���������ӽڵ㣨Ҫ���ܱ�����������ӽڵ㣩</param>
        public NodeContext(ENodeType type = ENodeType.Content, bool addContentNode = true)
        {
            NodeType = type;
            if (addContentNode && type != ENodeType.Content)      // �Ǻ��Ա�ǩ�Ľڵ㣬��������ӽڵ�
            {
                NodeContext node = new NodeContext(ENodeType.Content);
                childrenNodeList.Add(node);
            }
        }

        /// <summary>
        /// ֻ��һ�����еĽڵ�
        /// </summary>
        public static NodeContext OneLineNodeContext
        {
            get
            {
                NodeContext node = new NodeContext(ENodeType.Content);
                node.content = "\r\n";
                return node;
            }
        }

        /// <summary>
        /// Ĭ�Ϲ���Ľڵ��������ظ��ڵ�
        /// </summary>
        public static NodeContext GetDefualtNodeContext()
        {
            NodeContext root = new NodeContext(ENodeType.Root);
            root.AddChildNode(new NodeContext(ENodeType.FileHead));
            root.AddChildNode(OneLineNodeContext);
            root.AddChildNode(new NodeContext(ENodeType.Require));
            root.AddChildNode(OneLineNodeContext);
            root.AddChildNode(new NodeContext(ENodeType.ModuleDeclare));
            root.AddChildNode(OneLineNodeContext);
            root.AddChildNode(new NodeContext(ENodeType.WidgetTable));
            root.AddChildNode(OneLineNodeContext);
            //root.AddChildNode(new NodeContext(ENodeType.WinodwFunction));
            root.AddChildNode(new NodeContext(ENodeType.WindowCtor));
            root.AddChildNode(OneLineNodeContext);
            root.AddChildNode(new NodeContext(ENodeType.WindowInit));
            root.AddChildNode(OneLineNodeContext);
            root.AddChildNode(new NodeContext(ENodeType.WindowOnShow));
            root.AddChildNode(OneLineNodeContext);
            root.AddChildNode(new NodeContext(ENodeType.WindowOnHide));
            root.AddChildNode(OneLineNodeContext);
            root.AddChildNode(new NodeContext(ENodeType.WindowSetInputParam));
            root.AddChildNode(OneLineNodeContext);
            root.AddChildNode(new NodeContext(ENodeType.WindowBuildUpdateData));
            root.AddChildNode(OneLineNodeContext);
            root.AddChildNode(new NodeContext(ENodeType.WindowUpdateUI));
            root.AddChildNode(OneLineNodeContext);
            root.AddChildNode(new NodeContext(ENodeType.WindowClose));
            root.AddChildNode(OneLineNodeContext);
            root.AddChildNode(new NodeContext(ENodeType.WindowSubscribeEvents));
            root.AddChildNode(OneLineNodeContext);
            root.AddChildNode(new NodeContext(ENodeType.WindowUnsubscribeEvents));
            root.AddChildNode(OneLineNodeContext);
            root.AddChildNode(new NodeContext(ENodeType.WindowBtnFunctions));
            root.AddChildNode(OneLineNodeContext);
            root.AddChildNode(new NodeContext(ENodeType.ScrollItem));
            root.AddChildNode(OneLineNodeContext);
            root.AddChildNode(new NodeContext(ENodeType.WindowInherited));
            root.AddChildNode(OneLineNodeContext);
            root.AddChildNode(new NodeContext(ENodeType.ModuleFunction));
            root.AddChildNode(OneLineNodeContext);
            root.AddChildNode(new NodeContext(ENodeType.RegisterMsg));
            root.AddChildNode(OneLineNodeContext);
            return root;
        }

        /// <summary>
        /// ׷�����ݲ����У����"\r\n"��
        /// </summary>
        public void AddContentLine(string str) { content += str + "\r\n"; }

        public void DoHandler()
        {
            if (HandlerMgr.Handler.ContainsKey(NodeType))
                HandlerMgr.Handler[NodeType].Handle(this);
            int count = childrenNodeList.Count;
            for (int i = 0; i < count; ++i)
                childrenNodeList[i].DoHandler();
        }

        public bool IsRootNode() { return NodeType == ENodeType.Root; }

        public bool IsContentNode() { return NodeType == ENodeType.Content; }

        public ENodeType GetENodeType() { return NodeType; }

        public void AddChildNode(INodeContext node) { childrenNodeList.Add(node); }

        public string GetNodeBeiginStr() { return NodeBeiginStr; }

        public string GetNodeEndStr() { return NodeEndStr; }

        public string GetContent() { return content; }

        public void SetContent(string content) { this.content = content; }

        public List<string> GetContentList()
        {
            List<string> strList = new List<string>();
            string separator = "\r\n";
            int index = content.IndexOf(separator);
            int last = 0;
            while (index != -1)
            {
                strList.Add(content.Substring(last, index - last));
                last = index + separator.Length;
                index = content.IndexOf(separator, last);
            }
            return strList;
        }

        public List<INodeContext> GetChildren() { return childrenNodeList; }

        public List<INodeContext> GetContentChildren()
        {
            List<INodeContext> children = new List<INodeContext>();
            int srcCount = childrenNodeList.Count;
            for (int i = 0; i < srcCount; i++)
                if (childrenNodeList[i].IsContentNode())
                    children.Add(childrenNodeList[i]);
            return children;
        }

        public List<string> GetChildrenContentList()
        {
            List<INodeContext> children = GetContentChildren();
            int childrenCount = children.Count;
            List<string> lines = new List<string>();
            for (int i = 0; i < childrenCount; i++)
                lines.AddRange(children[i].GetContentList());
            return lines;
        }

        public INodeContext GetFristContentChild()
        {
            List<INodeContext> children = GetContentChildren();
            int childrenCount = children.Count;
            return childrenCount == 0 ? null : children[0];
        }

        public INodeContext GetLastContentChild()
        {
            List<INodeContext> children = GetContentChildren();
            int childrenCount = children.Count;
            return childrenCount == 0 ? null : children[childrenCount - 1];
        }

        public void SetContentToFristChild(string content)
        {
            List<INodeContext> children = GetContentChildren();
            int childrenCount = children.Count;
            if (childrenCount != 0)
                children[0].SetContent(content);
        }

        public void SetContentToLastChild(string content)
        {
            List<INodeContext> children = GetContentChildren();
            int childrenCount = children.Count;
            if (childrenCount != 0)
                children[childrenCount - 1].SetContent(content);
        }

        public void CleanContent() { content = string.Empty; }

        public void CleanAllContentChildren()
        {
            List<INodeContext> children = GetContentChildren();
            int childrenCount = children.Count;
            for (int i = 0; i < childrenCount; i++)
                children[i].CleanContent();
        }

        public string GetAllContent()
        {
            List<string> contents =  GetChildrenContentList();
            int childrenCount = contents.Count;
            StringBuilder builder = new StringBuilder();
            for (int i = 0; i < childrenCount; i++)
                builder.AppendLine(contents[i]);
            return builder.ToString();
        }

        public void AddContentNodeIfNotExist()
        {
            if (GetFristContentChild() == null)
                AddChildNode(OneLineNodeContext);
        }
    }

}
