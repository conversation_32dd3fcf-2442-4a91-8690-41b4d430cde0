using System.Collections.Generic;

namespace UICodeGenerator
{
    public interface INodeContext
    {
        /// <summary>
        /// 执行Handler
        /// </summary>
        void DoHandler();

        /// <summary>
        /// 获取结点类型
        /// </summary>
        /// <returns></returns>
        ENodeType GetENodeType();

        /// <summary>
        /// 当前结点是否为根节点
        /// </summary>
        bool IsRootNode();

        /// <summary>
        /// 当前结点是否为内容结点
        /// </summary>
        bool IsContentNode();

        /// <summary>
        /// 添加子节点
        /// </summary>
        void AddChildNode(INodeContext node);

        /// <summary>
        /// 获取节点的头标签
        /// </summary>
        string GetNodeBeiginStr();

        /// <summary>
        /// 获取节点的尾标签
        /// </summary>
        /// <returns></returns>
        string GetNodeEndStr();

        /// <summary>
        /// 获取文本内容
        /// </summary>
        string GetContent();

        /// <summary>
        /// 设置结点内容
        /// </summary>
        void SetContent(string content);

        /// <summary>
        /// 获取文本内容（每一行）
        /// </summary>
        List<string> GetContentList();

        /// <summary>
        /// 获取子结点列表
        /// </summary>
        List<INodeContext> GetChildren();

        /// <summary>
        /// 获取内容子节点
        /// </summary>
        List<INodeContext> GetContentChildren();

        /// <summary>
        /// 获取所有内容子节点的内容列表
        /// </summary>
        List<string> GetChildrenContentList();

        /// <summary>
        /// 获取第一个内容子节点
        /// </summary>
        INodeContext GetFristContentChild();

        /// <summary>
        /// 获取最后一个内容子节点
        /// </summary>
        INodeContext GetLastContentChild();

        /// <summary>
        /// 设置第一个内容子节点的内容
        /// </summary>
        void SetContentToFristChild(string content);

        /// <summary>
        /// 设置最后一个内容子节点的内容
        /// </summary>
        void SetContentToLastChild(string content);

        /// <summary>
        /// 清除当前结点内容
        /// </summary>
        void CleanContent();

        /// <summary>
        /// 清除所有内容子节点的内容
        /// </summary>
        void CleanAllContentChildren();

        /// <summary>
        /// 获取所有子内容节点的内容
        /// </summary>
        string GetAllContent();

        /// <summary>
        /// 如果不存在内容子节点，添加进去
        /// </summary>
        void AddContentNodeIfNotExist();
    }
}
