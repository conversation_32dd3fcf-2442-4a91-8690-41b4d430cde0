using System;
using System.Collections.Generic;
using UnityEngine;

namespace UICodeGenerator
{
    public enum NodeType
    {
        WindowShow,
    }


    public class NodeInfo
    {
        public string name = "ModuleShow";
        public string configText = "";
    }


    static public class Config
    {

        public enum EUpdateMask
        {
            //None,
            FileHead = 1,
            Require = 1 << 1,
            ModuleDeclare = 1 << 2,
            WidgetTable = 1 << 3,
            //WinodwFunction = 1 << 4,
            WindowInit = 1 << 4,
            WindowClose = 1 << 5,
            WindowSubscribeEvents = 1 << 6,
            WindowUnsubscribeEvents = 1 << 7,
            WindowBtnFunctions = 1 << 8,
            ScrollItem = 1 << 9,
            WindowInherited = 1 << 10,
            ModuleFunction = 1 << 11,
            RegisterMsg = 1 << 12,
            RegisterEvent = 1 << 13,
            //Other = 1 << 14,
        }
        static public EUpdateMask updateMask = (EUpdateMask)(~0 ^ 1 << 9);

        static public string ConfigText { get { return Resources.Load<TextAsset>("ui_code_generator_config").text; } }
        static public string InterfaceText { get { return Resources.Load<TextAsset>("ui_code_generator_interface").text; } }
        static public string RequireText { get { return Resources.Load<TextAsset>("ui_code_generator_require").text; } }

        #region Properties

        static public string FileName { get; set; }

        static public string PrefabName { get { return PrefabInfo.Name; } }
        static public string PrefabABPath { get { return PrefabInfo.ABPath; } }

        static public string Date { get { return DateTime.Now.ToString(); } }

        static public string Author { get; private set; }

        //static public bool AddNewRegisterMsg { get; set; }

        static public string WidgetPrefix { get; private set; }

        public static string Prefix { get { return LabelPrefix; } }
        public static string Begin { get { return LabelBegin; } }
        public static string End { get { return LabelEnd; } }

        public static string PrefixBegin { get { return Prefix + Begin; } }
        public static string PrefixEnd { get { return Prefix + End; } }

        public static string BeginString(string name) { return Prefix + Begin + name; }
        public static string EndString(string name) { return Prefix + End/* + name*/; }

        static public string LabelPrefix { get; private set; }
        static public string LabelBegin { get; private set; }
        static public string LabelEnd { get; private set; }

        static public string FileHead { get; private set; }
        static public string ModuleDeclare { get; private set; }
        static public string WidgetTable { get; private set; }

        static public string FunctionEnd { get; private set; }
        static public string WindowCtor { get; private set; }
        static public string WindowInit { get; private set; }
        static public string WindowOnShow { get; private set; }
        static public string WindowOnHide { get; private set; }
        static public string WindowBuildUpdateData { get; private set; }
        static public string WindowUpdateUI { get; private set; }
        static public string WindowSetInputParam { get; private set; }
        static public string WindowClose { get; private set; }
        static public string WindowSubscribe { get; private set; }
        static public string WindowUnsubscribe { get; private set; }

        static public string WindowInheritedName { get; private set; }
        static public string WindowInheritedDefine { get; private set; }
        static public string ModuleShow { get; private set; }
        static public string ModuleHide { get; private set; }
        static public string ModuleClose { get; private set; }

        static public string PrefixText { get; private set; }
        static public string PrefixButton { get; private set; }
        static public string PrefixScroll { get; private set; }
		static public string PrefixImage { get; private set; }
        static public string PrefixRectTransform { get; private set; }
        static public string TypeStrText { get; private set; }
        static public string TypeStrButton { get; private set; }
        static public string TypeStrScroll { get; private set; }
		static public string TypeStrImage { get; private set; }
		static public string TypeStrRectTransform { get; private set; }

        static public string WidgetDefine { get; private set; }

        static public string BtnFunctionName { get; private set; }
        static public string BtnFunction { get; private set; }
        static public string BtnFuncProxyName { get; private set; }
        static public string BtnFuncProxy { get; private set; }
        static public string BtnAddListenerNoCheck { get; private set; }
        static public string BtnAddListener { get; private set; }
        static public string BtnRemoveListenerNoCheck { get; private set; }
        static public string BtnRemoveListener { get; private set; }
        static public string BtnWidgetEventName { get; private set; }
        static public string ScrollInitListFunc { get; private set; }
        static public string ScrollItemRenderFunc { get; private set; }
        static public string ScrollItemDisposeFunc { get; private set; }

        static public string ButtonProxyMark { get; private set; }

        static public string MsgTableName { get; private set; }
        static public string MsgTable { get; private set; }
        static public string MsgTableStart { get; private set; }
        static public string MsgTableEnd { get; private set; }
        static public string RegisterMsg { get; private set; }
        static public string UnregisterMsg { get; private set; }

        #endregion

        static private string config;
        static private Dictionary<string, string> nameValueDic;

        /// <summary>
        /// 更新标签对应实际值
        /// </summary>
        /// <returns></returns>
        static private Dictionary<string, string> UpdateNameValueDic()
        {
            Dictionary<string, string> nameValueDic = new Dictionary<string, string>
            {
                { "PrefabName", PrefabName },
                { "PrefabABPath", PrefabABPath },
                { "Author", Author },
                { "PrefixText", PrefixText },
                { "PrefixButton", PrefixButton },
				{ "PrefixScroll", PrefixScroll },
				{ "PrefixImage", PrefixImage },
				{ "PrefixRectTransform", PrefixRectTransform },
                { "TypeStrText", TypeStrText },
                { "TypeStrButton", TypeStrButton },
                { "TypeStrScroll", TypeStrScroll },
                { "TypeStrImage", TypeStrImage },
                { "MsgTableName", MsgTableName },
                { "MsgTableStart", MsgTableStart },
                { "MsgTableEnd", MsgTableEnd },
                { "FunctionEnd", FunctionEnd },
            };
            return nameValueDic;
        }

        /// <summary>
        /// 加载配置文件
        /// </summary>
        static public void LoadConfigFile()
        {
            config = ConfigText;

            #region Base Config

            Author = GetValue("Author");
            WidgetPrefix = GetValue("WidgetPrefix");
            LabelPrefix = GetValue("LabelPrefix");
            LabelBegin = GetValue("LabelBegin");
            LabelEnd = GetValue("LabelEnd");
            PrefixText = GetValue("PrefixText");
            PrefixButton = GetValue("PrefixButton");
			PrefixScroll = GetValue("PrefixScroll");
			PrefixImage = GetValue("PrefixImage");
			PrefixRectTransform = GetValue("PrefixRectTransform");
            TypeStrText = GetValue("TypeStrText");
            TypeStrButton = GetValue("TypeStrButton");
            TypeStrScroll = GetValue("TypeStrScroll");
			TypeStrImage = GetValue("TypeStrImage");
			TypeStrRectTransform = GetValue("TypeStrRectTransform");
            ButtonProxyMark = GetValue("ButtonProxyMark");
            FunctionEnd = GetValue("FunctionEnd");
            MsgTableName = GetValue("MsgTableName");
            MsgTableStart = GetValue("MsgTableStart");
            MsgTableEnd = GetValue("MsgTableEnd");

            #endregion

            nameValueDic = UpdateNameValueDic();    // 更新变量对应值的字典

            #region Complex Config

            WidgetTable = GetAndParseValue("WidgetTable");

            WindowInheritedName = GetAndParseValue("WindowInheritedName");
            nameValueDic.Add("WindowInheritedName", WindowInheritedName);
            WindowInheritedDefine = GetAndParseValue("WindowInheritedDefine");

            FileHead = GetAndParseValue("FileHead");
            ModuleDeclare = GetAndParseValue("ModuleDeclare");
            WindowCtor = GetAndParseValue("WindowCtor");
            WindowInit = GetAndParseValue("WindowInit");

            WindowOnShow = GetAndParseValue("WindowOnShow");
            WindowOnHide = GetAndParseValue("WindowOnHide");
            WindowBuildUpdateData = GetAndParseValue("WindowBuildUpdateData");
            WindowUpdateUI = GetAndParseValue("WindowUpdateUI");
            WindowSetInputParam = GetAndParseValue("WindowSetInputParam");

            WindowClose = GetAndParseValue("WindowClose");
            WindowSubscribe = GetAndParseValue("WindowSubscribe");
            WindowUnsubscribe = GetAndParseValue("WindowUnsubscribe");

            ModuleShow = GetAndParseValue("ModuleShow");
            ModuleHide = GetAndParseValue("ModuleHide");
            ModuleClose = GetAndParseValue("ModuleClose");

            WidgetDefine = GetAndParseValue("WidgetDefine");
            //TextName = GetAndParseValue("TextName");
            //BtnName = GetAndParseValue("BtnName");
            //ScrollName = GetAndParseValue("ScrollName");

            BtnFunctionName = GetAndParseValue("BtnFunctionName");
            BtnFunction = GetAndParseValue("BtnFunction");
            BtnFuncProxyName = GetAndParseValue("BtnFuncProxyName");
            BtnFuncProxy = GetAndParseValue("BtnFuncProxy");
            BtnAddListenerNoCheck = GetAndParseValue("BtnAddListenerNoCheck");
            BtnAddListener = GetAndParseValue("BtnAddListener");
            BtnRemoveListenerNoCheck = GetAndParseValue("BtnRemoveListenerNoCheck");
            BtnRemoveListener = GetAndParseValue("BtnRemoveListener");
            BtnWidgetEventName = GetAndParseValue("BtnWidgetEventName");

            ScrollInitListFunc = GetAndParseValue("ScrollInitListFunc");
            ScrollItemRenderFunc = GetAndParseValue("ScrollItemRenderFunc");
            ScrollItemDisposeFunc = GetAndParseValue("ScrollItemDisposeFunc");

            MsgTable = GetAndParseValue("MsgTable");
            RegisterMsg = GetAndParseValue("RegisterMsg");
            UnregisterMsg = GetAndParseValue("UnregisterMsg");
            #endregion
        }

        /// <summary>
        /// 获取配置文件中，变量的值
        /// </summary>
        static private string GetValue(string name)
        {
            int index = config.IndexOf(name);
            while (index != -1)
            {
                index += name.Length;
                if (config.Substring(index).TrimStart().StartsWith("="))
                    return GetContentInSeparator(config, "$", index);
                index = config.IndexOf(config, index);
            }

            return GetContentInSeparator(config, "$", config.IndexOf(name + " ="));
        }

        /// <summary>
        /// 解析变量值，将'#'符号内容替换
        /// </summary>
        static private string ParseValue(string value)
        {
            string result = value;
            string str = GetContentInSeparator(result, "#");
            int index = 0;
            string strWithSeparator;
            while (!string.IsNullOrEmpty(str))
            {
                index = result.IndexOf(str, index) + str.Length + 1;
                strWithSeparator = '#' + str + '#';
                // 包含转换对应替换，且转换后和原来不一样时替换
                if (nameValueDic.ContainsKey(str) && nameValueDic[str] != strWithSeparator)
                    result = result.Replace(strWithSeparator, nameValueDic[str]);
                str = GetContentInSeparator(result, "#", index);
            }
            return result;
        }

        /// <summary>
        /// 获取解析后的字符串
        /// </summary>
        static private string GetAndParseValue(string name)
        {
            return ParseValue(GetValue(name));
        }


        /// <summary>
        /// 获取source开始，第一对separator之间的内容
        /// </summary>
        public static string GetContentInSeparator(string source, string separator, int startIndex = 0, bool withSpearetor = false)
        {
            if (startIndex < 0 || startIndex > source.Length)
                return string.Empty;

            int begin = -1, end = -1;
            begin = source.IndexOf(separator, startIndex);
            if (begin == -1 || begin + 1 >= source.Length)
                return string.Empty;
            end = source.IndexOf(separator, begin + 1);
            if (withSpearetor)
                end += separator.Length;
            else
                begin += separator.Length;

            return end == -1 ? string.Empty : source.Substring(begin, end - begin);
        }

        /// <summary>
        /// 是否包含标记掩码
        /// </summary>
        public static bool ContainMask(EUpdateMask mask)
        {
            return (updateMask & mask) != 0;
        }

        //public static NodeType Get(string strCode)
        //{
        //    //return NodeType.WindowShow
        //}


    }
}
