
namespace UICodeGenerator
{
    class FileHeadNodeHandler : INodeHandler
    {
        public void Handle(INodeContext node)
        {
            node.AddContentNodeIfNotExist();
            if (!Config.ContainMask(Config.EUpdateMask.FileHead))
                return;
            node.SetContentToFristChild(Config.FileHead
                    .Replace("#Author#", Config.Author)
                .Replace("#Date#", Config.Date)
                .Replace("#Version#", "1.0")
                .Replace("#Description#", "Description")
                .Replace("#FileName#", Config.FileName));
        }

    }
}
