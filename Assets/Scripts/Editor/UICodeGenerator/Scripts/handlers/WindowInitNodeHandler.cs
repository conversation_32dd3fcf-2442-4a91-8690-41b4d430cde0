
using System.Collections.Generic;
using System.Text;

namespace UICodeGenerator
{
    class WindowInitNodeHandler : INodeHandler
    {
        public void Handle(INodeContext node)
        {
            if (!Config.ContainMask(Config.EUpdateMask.WindowInit))
                return;

            FixedFuncNodeHandler fixedFunc = new FixedFuncNodeHandler(Config.WindowInit);
            FunctionCheck check = new FunctionCheck(Config.WindowInit);

            string allContent = node.GetAllContent();

            StringBuilder builder = new StringBuilder();
            if (Config.ContainMask(Config.EUpdateMask.RegisterMsg))
            {
                 builder.AppendLine("    " + Config.RegisterMsg);
            }
            
            int widgetCount = PrefabInfo.widgetList.Count;
            ScrollListCheck tem;
            for (int i = 0; i < widgetCount; ++i)
            {
                if (PrefabInfo.widgetList[i].Type == EWidgetType.ScrollRectTable)
                {
                    //tem = new ScrollListCheck(new ScrollRectInfo(PrefabInfo.widgetList[i].Name));
                    //builder.AppendLine("    " + tem.scr.ScrollListInitCall);
                }
            }
            fixedFunc.Handle(node, builder.ToString());
        }

    }
}
