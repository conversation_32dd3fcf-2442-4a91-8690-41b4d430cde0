using System.Collections.Generic;
using System.Text;

namespace UICodeGenerator
{
    public class RegisterMsgNodeHandler : INodeHandler
    {
        static public string MessageTable { get { return Config.MsgTable; } }
        static public string MsgTableStart { get { return Config.MsgTableStart; } }
        static public string MsgTableEnd { get { return Config.MsgTableEnd; } }
        //static public string RegisterMsg { get { return Config.RegisterMsg; } }

        public class LabelCheck
        {
            public bool isExist;        // �Ƿ���ڸñ�ǩ
            public string label;        // ��ǩ��Ϣ������ͷ��
            public string detail;       // ����ϸ�ڣ������壩

            public LabelCheck(string detail, bool isExist = false)
            {
                this.isExist = isExist;
                this.detail = detail;

                label = detail.Split('\r')[0];
            }
        }

        public class MsgItemCheck
        {
            public RegisterMessage.RegisterItem item;
            public bool exist;
            public string detail;
            public string func;

            public MsgItemCheck(RegisterMessage.RegisterItem item)
            {
                this.item = item;
                detail = RegisterMessage.GetRegisterItemStr(item);
                func = RegisterMessage.GetHandlerFunc(item);
            }
        }

        public void Handle(INodeContext node)
        {
            if (!Config.ContainMask(Config.EUpdateMask.RegisterMsg))
                return;
            node.AddContentNodeIfNotExist();

            List<LabelCheck> checkList = new List<LabelCheck>
            { new LabelCheck(MessageTable),/* new LabelCheck(RegisterMsg),*/
             new LabelCheck(MsgTableStart), new LabelCheck(MsgTableEnd)};

            List<string> contentList = node.GetChildrenContentList();
            int lineCount = contentList.Count;
            int checkCount = checkList.Count;
            int tableStartIndex = -1;
            int tableEndIndex = -1;
            for (int j = 0; j < checkCount; ++j)
                for (int i = 0; i < lineCount; ++i)
                    if (contentList[i].Contains(checkList[j].label))
                    {
                        checkList[j].isExist = true;
                        if (checkList[j].label == MsgTableStart)
                            tableStartIndex = i;
                        else if (checkList[j].label == MsgTableEnd)
                            tableEndIndex = i;
                        break;
                    }

            List<MsgItemCheck> msgItemList = new List<MsgItemCheck>();
            List<RegisterMessage.RegisterItem> registerItemList = RegisterMessage.instance.registerItemList;
            int msgItemCount = registerItemList.Count;
            for (int i = 0; i < msgItemCount; i++)
                msgItemList.Add(new MsgItemCheck(registerItemList[i]));

            if (tableStartIndex >= 0 && tableEndIndex >= tableStartIndex)
            {
                string content = node.GetAllContent();
                int startIndex = content.IndexOf(MsgTableStart) + MsgTableStart.Length;
                int endIndex = content.IndexOf(MsgTableEnd);
                content = content.Substring(startIndex, endIndex - startIndex);
                for (int i = 0; i < msgItemCount; i++)
                {
                    if (content.Contains(msgItemList[i].detail))
                        msgItemList[i].exist = true;
                }
            }
            string updateString = UpdateString(node.GetFristContentChild().GetContentList(), checkList, msgItemList, tableStartIndex,tableEndIndex);
            if (updateString == string.Empty)
                updateString = "\r\n";
            node.SetContentToFristChild(updateString);
        }

        /// <summary>
        /// ��������Ϊ�ַ���
        /// </summary>
        private string UpdateString(List<string> contentList, List<LabelCheck> checkList, List<MsgItemCheck> msgItemList, int startIndex,int endIndex)
        {
            int lineCount = contentList.Count;
            StringBuilder builder = new StringBuilder();
            int itemCount = msgItemList.Count;

            // ���ȱ�ٵ���Ϣ����
            for (int i = 0; i < itemCount; i++)
                if (!msgItemList[i].exist)
                    builder.AppendLine(msgItemList[i].func);

            int checkCount = checkList.Count;           // ���������
            for (int i = 0; i < checkCount; i++)
            {
                if (checkList[i].label == MessageTable.Split('\r')[0])
                {
                    if (checkList[i].isExist == false)
                    {
                        builder.AppendLine(checkList[i].label);
                        builder.AppendLine("{ " + MsgTableStart);
                        for (int j = 0; j < itemCount; j++)
                            builder.AppendLine(msgItemList[j].detail);
                        builder.AppendLine("} " + MsgTableEnd);
                    }
                    else if (startIndex >= 0)
                    {
                        for (int j = 0; j <= startIndex; j++)
                            builder.AppendLine(contentList[j]);
                        for (int j = 0; j < itemCount; j++)
                            if (!msgItemList[j].exist)
                                builder.AppendLine(msgItemList[j].detail);
                    }
                }
            }

            if (endIndex < 0)
                endIndex = lineCount - 1;

            // ԭʼ������
            for (int i = startIndex + 1; i < lineCount; i++)
                builder.AppendLine(contentList[i]);

            return builder.ToString();

        }

    }
}
