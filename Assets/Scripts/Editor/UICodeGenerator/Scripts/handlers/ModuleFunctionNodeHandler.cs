using System.Collections.Generic;
using System.Text;

namespace UICodeGenerator
{
    public class ModuleFunctionNodeHandler : INodeHandler
    {
        static public string FuncShow { get { return Config.ModuleShow; } }
        static public string FuncHide { get { return Config.ModuleHide; } }
        static public string FuncClose { get { return Config.ModuleClose; } }

        public class FuncCheck
        {
            public bool isExist;        // �Ƿ���ڸñ�ǩ
            public string label;        // ��ǩ��Ϣ������ͷ��
            public string detail;       // ����ϸ�ڣ������壩

            public FuncCheck(string detail, bool isExist = false)
            {
                this.isExist = isExist;
                this.detail = detail;
                label = detail.Split('\r')[0];
            }
        }

        public void Handle(INodeContext node)
        {
            if (!Config.ContainMask(Config.EUpdateMask.ModuleFunction))
                return;
            node.AddContentNodeIfNotExist();

            List<FuncCheck> funcList = new List<FuncCheck>
            { new FuncCheck(FuncShow), new FuncCheck(FuncHide), new FuncCheck(FuncClose), };

            List<string> contentList = node.GetChildrenContentList();
            int lineCount = contentList.Count;
            int funcCount = funcList.Count;
            for (int j = 0; j < funcCount; ++j)
                for (int i = 0; i < lineCount; ++i)     // ����Ƿ����Ĭ����Ҫ�ĺ���
                    if (contentList[i].Contains(funcList[j].label))
                    {
                        funcList[j].isExist = true;
                        break;
                    }

            node.SetContentToFristChild(UpdateString(node.GetFristContentChild().GetContentList(), funcList));
        }

        /// <summary>
        /// ��������Ϊ�ַ���
        /// </summary>
        private string UpdateString(List<string> contentList, List<FuncCheck> checkList)
        {
            int lineCount = contentList.Count;
            StringBuilder builder = new StringBuilder();

            int checkCount = checkList.Count;           // ���������
            for (int i = 0; i < checkCount; i++)
                if (checkList[i].isExist == false)
                    builder.AppendLine(checkList[i].detail);

            // ԭʼ���������
            for (int i = 0; i < lineCount; i++)
                builder.AppendLine(contentList[i]);
            return builder.ToString();
        }

    }

}
