
using System.Collections.Generic;
using System.Text;

namespace UICodeGenerator
{
    class WindowCloseNodeHandler : INodeHandler
    {
        public void Handle(INodeContext node)
        {
            if (!Config.ContainMask(Config.EUpdateMask.WindowClose))
                return;
            FixedFuncNodeHandler fixedFunc = new FixedFuncNodeHandler(Config.WindowClose);
            FunctionCheck check = new FunctionCheck(Config.WindowClose);

            string allContent = node.GetAllContent();

            StringBuilder builder = new StringBuilder();
            if (Config.ContainMask(Config.EUpdateMask.RegisterMsg))
            {
                builder.AppendLine("    " + Config.UnregisterMsg);
            }

            int widgetCount = PrefabInfo.widgetList.Count;
            ScrollListCheck tem;
            for (int i = 0; i < widgetCount; ++i)
            {
                if (PrefabInfo.widgetList[i].Type == EWidgetType.ScrollRectTable)
                {
                    //tem = new ScrollListCheck(new ScrollRectInfo(PrefabInfo.widgetList[i].Name));
                    //builder.AppendLine("    " + tem.scr.ScrollListCloseCall);
                }

            }
            fixedFunc.Handle(node, builder.ToString(), false);
        }

    }
}
