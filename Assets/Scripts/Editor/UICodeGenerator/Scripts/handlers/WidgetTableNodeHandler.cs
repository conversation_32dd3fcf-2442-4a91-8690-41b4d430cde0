using System;
using System.Collections.Generic;
using System.Text;

namespace UICodeGenerator
{
    public class WidgetTableNodeHandler : INodeHandler
    {
        public string WidgetTableName { get { return Config.WidgetTable; } }

        public void Handle(INodeContext node)
        {
            if (!Config.ContainMask(Config.EUpdateMask.WidgetTable))
                return;
            node.AddContentNodeIfNotExist();
            List<string> contentList = node.GetChildrenContentList();       // �жϵ�һ���ǲ��������ͷ
            bool haveTable = contentList.Count != 0 && contentList[0].Contains(WidgetTableName);

            node.GetFristContentChild().CleanContent();
            StringBuilder builder = new StringBuilder();
            builder.AppendLine(WidgetTableName + " = {");

            int widgetCount = PrefabInfo.widgetList.Count;      // ��ȡ�����

            for (int i = 0; i < widgetCount; ++i)
                builder.AppendLine(PrefabInfo.widgetList[i].WidgetDefine);

            if (!haveTable) // ���û�б�������һ�δ���������Զ����㣬β���ݽ��
            {
                // ����û��Զ����ǩ
                NodeContext user = new NodeContext(ENodeType.User);
                node.AddChildNode(user);

                // ��ӱ�Ľ�β��������
                NodeContext endTable = new NodeContext(ENodeType.Content);
                node.AddChildNode(endTable);
            }

            node.SetContentToFristChild(builder.ToString());
            node.SetContentToLastChild("}\r\n");
        }


    }
}