using System;
using System.Collections.Generic;
using System.Text;
using System.Text.RegularExpressions;
using UnityEngine;

namespace UICodeGenerator
{
    public class WindowFunctionNodeHandler : INodeHandler
    {
        static public string CtorLabel = "";
        static public string InitLabel = "";
        static public string CloseLabel = "";
        static public string SubscribeLabel = "";
        static public string UnsubscribeLabel = "";
        static public string OnShowLabel = "";
        static public string OnHideLabel = "";
        static public string BuildUpdateDataLabel = "";
        static public string UpdateUILabel = "";
        static public string SetInputParam = "";
        
        public class FuncCheck
        {
            //��������
            public string label;
            //ע��
            public string note = "";
            //Դ�ļ����Ƿ����
            public bool exist = false;
            //������ʼλ��
            public int startIndex = -1;
            public string config = "";

            public FuncCheck(ref string config)
            {
                foreach (Match match in Regex.Matches(config, @"^--\[\[(.*)\]\]"))
                {
                    note = match.Value;
                    break;
                }

                foreach (Match match in Regex.Matches(config, @"function(.*):(.*)\((.*)\)"))
                {
                    label = match.Value;
                    break;
                }

                this.config = config;
            }
        }

        public class BtnCheck
        {
            public ButtonInfo btn;
            public bool existFunc = false;
            public bool existProxy = false;
            public bool existAddListhener = false;
            public bool existRemoveListhener = false;
            public BtnCheck(ButtonInfo btn) { this.btn = btn; }
        }

        public class ScrollCheck
        {
            public ScrollRectInfo scr;
            public bool existInit;
            public bool existClose;
            public ScrollCheck(ScrollRectInfo scr) { this.scr = scr; }
        }

        private void AddFuncCheck(List<FuncCheck> funcList, string config, ref string label)
        {
            FuncCheck c = new FuncCheck(ref config);
            label = c.label;
            funcList.Add(c);
        }

        public void Handle(INodeContext node)
        {
            //if (!Config.ContainMask(Config.EUpdateMask.WinodwFunction))
            //    return;
            node.AddContentNodeIfNotExist();

            List<FuncCheck> funcList = new List<FuncCheck>();
            AddFuncCheck(funcList, Config.WindowCtor, ref CtorLabel);
            AddFuncCheck(funcList, Config.WindowInit, ref InitLabel);
            AddFuncCheck(funcList, Config.WindowOnShow, ref OnShowLabel);
            AddFuncCheck(funcList, Config.WindowOnHide, ref OnHideLabel);
            AddFuncCheck(funcList, Config.WindowSetInputParam, ref SetInputParam);
            AddFuncCheck(funcList, Config.WindowBuildUpdateData, ref BuildUpdateDataLabel);
            AddFuncCheck(funcList, Config.WindowUpdateUI, ref UpdateUILabel);
            AddFuncCheck(funcList, Config.WindowClose, ref CloseLabel);
            AddFuncCheck(funcList, Config.WindowSubscribe, ref SubscribeLabel);
            AddFuncCheck(funcList, Config.WindowUnsubscribe, ref UnsubscribeLabel);


            List<BtnCheck> btnList = new List<BtnCheck>();
            List<ScrollCheck> scrList = new List<ScrollCheck>();

            List<ButtonInfo> oldBtnList = new List<ButtonInfo>();
            List<ScrollRectInfo> oldScrList = new List<ScrollRectInfo>();

            // ��ȡ��ť���б�����б�
            int widgetCount = PrefabInfo.widgetList.Count;
            for (int i = 0; i < widgetCount; ++i)
            {
                if (PrefabInfo.widgetList[i].Type == EWidgetType.Button)
                    btnList.Add(new BtnCheck(new ButtonInfo(PrefabInfo.widgetList[i].Name)));
                else if (PrefabInfo.widgetList[i].Type == EWidgetType.ScrollRectTable)
                    scrList.Add(new ScrollCheck(new ScrollRectInfo(PrefabInfo.widgetList[i].Name)));
            }

            int funcCount = funcList.Count;
            int btnCount = btnList.Count;
            int scrCount = scrList.Count;
            int btnProxyIndex = -1;             // ��ťί�к�����ʼ���

            List<string> contentList = node.GetChildrenContentList();
            int lineCount = contentList.Count;
            for (int i = 0; i < lineCount; ++i)
            {
                for (int j = 0; j < funcCount; j++)
                {
                    if (contentList[i].Contains(funcList[j].label))
                    {
                        funcList[j].exist = true;       // �жϺ����Ƿ����
                        funcList[j].startIndex = i;
                        break;
                    }
                }

                for (int k = 0; k < btnCount; k++)      // ����Ƿ������ť����
                    if (contentList[i].Contains(btnList[k].btn.BtnFunctionLabel))
                        btnList[k].existFunc = true;

                if (contentList[i].Contains(Config.ButtonProxyMark)) // �ҵ�ע���¼������ķָ���
                    btnProxyIndex = i;
            }

            for (int i = 0; i < funcCount; i++)
            {
                if (funcList[i].exist)      // ����������ڣ������������ж�
                {
                    if (funcList[i].label == InitLabel)
                    {
                        for (int j = funcList[i].startIndex; j < lineCount; j++)
                        {
                            if (contentList[j].Contains(Config.FunctionEnd)) break;
                            int k = 0;
                            for (k = 0; k < scrCount; k++)
                            {
                                // �ж�Scroll.Init�����Ƿ����
                                //if (contentList[j].Contains(scrList[k].scr.ScrollListInitCall))
                                //{
                                //    scrList[k].existInit = true;
                                //    continue;
                                //}
                            }
                            if (k == scrCount)  // k ���� scrCountʱ��˵��û�����������ѭ��������û���жϳ��б�Init
                            {
                                // �ж��Ƿ��оɵĲ����ڵ�Scroll
                                string checkStr = "self:Create_Scroll_";
                                int tem1 = contentList[j].IndexOf(checkStr);
                                int tem2 = contentList[j].IndexOf("()");
                                if (tem1 != -1 && tem2 != -1)
                                    oldScrList.Add(new ScrollRectInfo(
                                        contentList[j].Substring(tem1 + checkStr.Length, tem2 - tem1 - checkStr.Length)));
                            }

                        }
                    }
                    else if (funcList[i].label == CloseLabel)
                    {
                        // �ж�Scroll.Close�����Ƿ����
                        for (int j = funcList[i].startIndex; j < lineCount; j++)
                        {
                            if (contentList[j].Contains(Config.FunctionEnd)) break;
                            int k = 0;
                            //for (k = 0; k < scrCount; k++)
                            //    if (contentList[j].Contains(scrList[k].scr.ScrollListCloseCall))
                            //        scrList[k].existClose = true;
                            if (k == scrCount)
                            {
                                // �ж��Ƿ��оɵĲ����ڵ�Scroll
                                string checkStr = "self:Release_Scroll_";
                                int tem1 = contentList[j].IndexOf(checkStr);
                                int tem2 = contentList[j].IndexOf("()");
                                if (tem1 != -1 && tem2 != -1)
                                    oldScrList.Add(new ScrollRectInfo(
                                        contentList[j].Substring(tem1 + checkStr.Length, tem2 - tem1 - checkStr.Length)));
                            }
                        }
                    }
                    else if (funcList[i].label == SubscribeLabel)
                    {
                        if (btnProxyIndex == -1)        // ���û�зָ���ǣ�ֱ������
                            continue;
                        // �ж�ί���Ƿ����
                        for (int j = funcList[i].startIndex; j < btnProxyIndex; j++)
                        {
                            bool isSrcBtn = false;
                            for (int k = 0; k < btnCount; k++)
                            {
                                if (contentList[j].Contains(btnList[k].btn.BtnFuncProxyLabel))
                                {
                                    btnList[k].existProxy = true;
                                    isSrcBtn = true;
                                }
                            }
                            if (!isSrcBtn)
                            {
                                string checkStr = "self.On";
                                int tem1 = contentList[j].IndexOf(checkStr);
                                int tem2 = contentList[j].IndexOf("ClickedProxy = function()");
                                if (tem1 != -1 && tem2 != -1)
                                    oldBtnList.Add(new ButtonInfo(
                                        contentList[j].Substring(tem1 + checkStr.Length, tem2 - tem1 - checkStr.Length)));
                            }
                        }
                    }
                    else if (funcList[i].label == UnsubscribeLabel)
                    {
                        // ���������������¼�����ÿ����������
                    }
                }
            }

            string updateString = UpdateString(node.GetFristContentChild().GetContentList(), funcList, btnList, scrList, btnProxyIndex);

            // �Ƴ��ɵ��Զ����ɵİ�ťί��
            int oldBtnCount = oldBtnList.Count;
            for (int i = 0; i < oldBtnCount; i++)
                updateString = updateString.Replace(oldBtnList[i].BtnFuncProxy, "");

            // �Ƴ��ɵ��Զ����ɵ��б����
            int oldScrCount = oldScrList.Count;
            for (int i = 0; i < oldScrCount; i++)
            {
                //updateString = updateString.Replace(oldScrList[i].ScrollListInitCall, "");
                //updateString = updateString.Replace(oldScrList[i].ScrollListCloseCall, "");
            }

            node.SetContentToFristChild(updateString);

        }

        private int GetFuncStartIndex(List<FuncCheck> funcList, string label)
        {
            int funcCount = funcList.Count;
            for (int i = 0; i < funcCount; i++)
                if (funcList[i].label == label)
                    return funcList[i].startIndex;
            return -1;
        }

        private string UpdateString(List<string> contentList, List<FuncCheck> funcList, List<BtnCheck> btnList, List<ScrollCheck> scrollList, int btnProxyIndex)
        {
            int lineCount = contentList.Count;
            int btnCount = btnList.Count;
            int scrollCount = scrollList.Count;
            int funcCount = funcList.Count;
            StringBuilder builder = new StringBuilder();
            FuncCheck func = null;
            for (int j = 0; j < funcCount; j++)
            {
                func = funcList[j];
                if (!func.exist)     // Ĭ�Ϻ���������ʱ��ֱ�Ӵ���
                {
                    if (func.label == CtorLabel)
                    {
                        //д��ע��
                        if (func.note.Length > 0)
                        {
                            builder.AppendLine(func.note);
                        }

                        builder.AppendLine(CtorLabel);
                        builder.AppendLine(Config.FunctionEnd);
                    }
                    else if (func.label == InitLabel) // ��ʼ�������ڣ����ȱ�ٵ�Scroll��ʼ����������
                    {
                        //д��ע��
                        if (func.note.Length > 0)
                        {
                            builder.AppendLine(func.note);
                        }

                        builder.AppendLine(InitLabel);
                        builder.AppendLine("    self:SubscribeEvents()");
                        builder.AppendLine("    net_route.RegisterMsgHandlers(" + Config.MsgTableName + ")");
                        //for (int k = 0; k < scrollCount; k++)
                        //    builder.AppendLine("    " + scrollList[k].scr.ScrollListInitCall);
                        builder.AppendLine(Config.FunctionEnd);
                    }
                    else if (func.label == CloseLabel)
                    {
                        //д��ע��
                        if (func.note.Length > 0)
                        {
                            builder.AppendLine(func.note);
                        }

                        builder.AppendLine(CloseLabel);
                        builder.AppendLine("    self:UnsubscribeEvents()");
                        builder.AppendLine("    net_route.UnregisterMsgHandlers(" + Config.MsgTableName + ")");
                        //for (int k = 0; k < scrollCount; k++)
                        //    builder.AppendLine("    " + scrollList[k].scr.ScrollListCloseCall);
                        builder.AppendLine("    self.__base:Close()");
                        builder.AppendLine("    window = nil");
                        builder.AppendLine(Config.FunctionEnd);
                    }
                    else if (func.label == SubscribeLabel)
                    {
                        //д��ע��
                        if (func.note.Length > 0)
                        {
                            builder.AppendLine(func.note);
                        }

                        builder.AppendLine(SubscribeLabel);
                        for (int k = 0; k < btnCount; k++)
                            builder.AppendLine(btnList[k].btn.BtnFuncProxy);
                        builder.AppendLine(Config.ButtonProxyMark);
                        builder.AppendLine();
                        for (int k = 0; k < btnCount; k++)
                            builder.AppendLine(btnList[k].btn.BtnAddListener);
                        builder.AppendLine(Config.FunctionEnd);    
                    }
                    else if (func.label == UnsubscribeLabel)
                    {
                        //д��ע��
                        if (func.note.Length > 0)
                        {
                            builder.AppendLine(func.note);
                        }

                        builder.AppendLine(UnsubscribeLabel);
                        for (int k = 0; k < btnCount; k++)
                            builder.AppendLine(btnList[k].btn.BtnRemoveListener);
                        builder.AppendLine(Config.FunctionEnd);
                    }
                    else
                    {
                        builder.Append(func.config);
                        builder.AppendLine("");
                    }
                }
            }

            int i = -1;
            int initIndex = GetFuncStartIndex(funcList, InitLabel);
            int closeIndex = GetFuncStartIndex(funcList, CloseLabel);
            int subIndex = GetFuncStartIndex(funcList, SubscribeLabel);
            int unsubIndex = GetFuncStartIndex(funcList, UnsubscribeLabel);
            while (++i <= lineCount - 1)
            {
                if (i == initIndex + 1 && initIndex != -1)      // Init�����ĵ�3�У���������ͷ�͵���ע���¼�
                {
                    //for (int k = 0; k < scrollCount; k++)
                    //    if (scrollList[k].existInit == false)
                    //        builder.AppendLine("    " + scrollList[k].scr.ScrollListInitCall);
                }
                if (i == closeIndex + 1 && closeIndex != -1)
                {
                    //for (int k = 0; k < scrollCount; k++)
                    //    if (scrollList[k].existClose == false)
                    //        builder.AppendLine("    " + scrollList[k].scr.ScrollListCloseCall);
                }
                if (i == subIndex + 1 && subIndex != -1)        // ����ע�ắ������Ӳ����ڵİ�ť
                {
                    if (btnProxyIndex == -1)
                    {
                        for (int k = 0; k < btnCount; k++)
                            builder.AppendLine(btnList[k].btn.BtnFuncProxy);
                        builder.AppendLine(Config.ButtonProxyMark);
                    }
                    else
                    {
                        // ���ȱ�ٵ�ί��
                        for (int k = 0; k < btnCount; k++)
                            if (!btnList[k].existProxy)
                                builder.AppendLine(btnList[k].btn.BtnFuncProxy);
                        // ���ԭ�е����ݵ��м�ָ�㣨�����ָ�㣩
                        for (int k = i; k <= btnProxyIndex; k++)
                            builder.AppendLine(contentList[k]);
                        // ԭ�ı�������ֱ�������м�ָ��
                        i = btnProxyIndex;
                    }
                    // ��ť�¼�ֱ��ȫ�����
                    for (int j = 0; j < btnCount; j++)
                        builder.AppendLine(btnList[j].btn.BtnAddListener);
                    // ����ֱ��ָ������β��
                    while (i++ <= lineCount - 2)
                        if (contentList[i].Contains(Config.FunctionEnd.Split('\r')[0])) break;
                }
                if (i == unsubIndex + 1 && unsubIndex != -1)    // ����ע����������Ӳ����ڵİ�ť
                {
                    for (int j = 0; j < btnCount; j++)
                        builder.AppendLine(btnList[j].btn.BtnRemoveListener);
                    while (i++ <= lineCount - 2)
                        if (contentList[i].Contains(Config.FunctionEnd.Split('\r')[0])) break;
                }
                builder.AppendLine(contentList[i]);     // ���ԭ��������
            }

            //��Ӳ����ڵİ�ť����
            for (int j = 0; j < btnCount; j++)
                if (btnList[j].existFunc == false)
                    builder.AppendLine(btnList[j].btn.BtnFunction);
            return builder.ToString();
        }

    }

}
