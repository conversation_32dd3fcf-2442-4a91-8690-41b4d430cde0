
using System.Collections.Generic;
using System.Text;

namespace UICodeGenerator
{
    class WindowUnsubscribeEventsNodeHandler : INodeHandler
    {
        public void Handle(INodeContext node)
        {
            if (!Config.ContainMask(Config.EUpdateMask.WindowUnsubscribeEvents))
                return;

            FixedFuncNodeHandler fixedFunc = new FixedFuncNodeHandler(Config.WindowUnsubscribe);
            FunctionCheck check = new FunctionCheck(Config.WindowUnsubscribe);

            string allContent = node.GetAllContent();

            StringBuilder builder = new StringBuilder();

            int widgetCount = PrefabInfo.widgetList.Count;
            ButtonCheck tem;
            for (int i = 0; i < widgetCount; ++i)
            {
                if (PrefabInfo.widgetList[i].Type == EWidgetType.Button)
                {
                    tem = new ButtonCheck(new ButtonInfo(PrefabInfo.widgetList[i].Name));
                    builder.AppendLine(tem.btn.BtnRemoveListener);
                }
            }
            fixedFunc.Handle(node, builder.ToString());
            
        }
        
    }
}
