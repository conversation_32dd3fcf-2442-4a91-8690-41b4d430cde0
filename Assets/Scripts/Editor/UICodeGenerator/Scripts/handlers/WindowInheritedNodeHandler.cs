
namespace UICodeGenerator
{
    public class WindowInheritedNodeHandler : INodeHandler
    {
        public void Handle(INodeContext node)
        {
            if (!Config.ContainMask(Config.EUpdateMask.WindowInherited))
                return;
            node.AddContentNodeIfNotExist();
            node.CleanAllContentChildren();
            node.SetContentToFristChild(Config.WindowInheritedDefine);
        }
    }
}
