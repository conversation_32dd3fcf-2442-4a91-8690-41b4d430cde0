
using System.Collections.Generic;
using System.Text;

namespace UICodeGenerator
{
    class FixedFuncNodeHandler : INodeHandler
    {
        private FunctionCheck funcCheck;

        /// <summary>
        /// 固定格式函数节点
        /// </summary>
        /// <param name="func">函数内容（包括注释、函数头、内容、尾）</param>
        public FixedFuncNodeHandler(string func)
        {
            funcCheck = new FunctionCheck(func);
        }

        public void Handle(INodeContext node)
        {
            Handle(node, string.Empty);
        }

        public void Handle(INodeContext node, string other, bool addBeforeEnd = true)
        {
            node.AddContentNodeIfNotExist();
            string allContent = node.GetAllContent();
            node.GetFristContentChild().CleanContent();
            bool isLabelExist = allContent.Contains(funcCheck.label);

            StringBuilder builder = new StringBuilder();
            if (addBeforeEnd)
            {
                string beforeEndStr = funcCheck.config.Substring(0, funcCheck.config.IndexOf(Config.FunctionEnd));
                builder.Append(beforeEndStr);
                builder.Append(other);
            }
            else
            {
                int funcContentIndex = funcCheck.config.IndexOf(funcCheck.label) + funcCheck.label.Length;
                string beforeStartStr = funcCheck.config.Substring(0, funcContentIndex);
                builder.AppendLine(beforeStartStr);
                builder.Append(other);
                string beforeEndStr = funcCheck.config.Substring(funcContentIndex, funcCheck.config.IndexOf(Config.FunctionEnd) - funcContentIndex);
                builder.Append(beforeEndStr);
            }

            if (!isLabelExist)
            {
                // 添加用户自定义标签
                NodeContext user = new NodeContext(ENodeType.User);
                node.AddChildNode(user);

                // 添加函数结尾
                NodeContext funcEnd = new NodeContext(ENodeType.Content);
                node.AddChildNode(funcEnd);
            }

            node.SetContentToFristChild(builder.ToString());
            node.SetContentToLastChild(Config.FunctionEnd + "\r\n");
        }

    }
}
