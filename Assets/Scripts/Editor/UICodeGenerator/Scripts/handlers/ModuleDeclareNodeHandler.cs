
namespace UICodeGenerator
{
    class ModuleDeclareNodeHandler : INodeHandler
    {
        public void Handle(INodeContext node)
        {
            if (!Config.ContainMask(Config.EUpdateMask.ModuleDeclare))
                return;
            node.AddContentNodeIfNotExist();
            node.CleanAllContentChildren();
            node.SetContentToFristChild(Config.ModuleDeclare.Replace("#FileName#", Config.FileName));
        }
    }
}
