using System;
using System.Collections.Generic;
using System.Text;

namespace UICodeGenerator
{
    public class ScrollItemNodeHandler : INodeHandler
    {
        public class ScrollCheck
        {
            public bool isExist;        // 是否存在该标签
            public string label;        // 标签信息（函数头）
            public string detail;       // 代表细节（函数体等）

            public ScrollCheck(string label, string detail, bool isExist = false)
            {
                this.isExist = isExist;
                this.label = label;
                this.detail = detail;
            }
        }

        /// <summary>
        /// 初始化检测列表
        /// </summary>
        private List<ScrollCheck> InitCheckList(List<ScrollRectInfo> scrolls)
        {
            List<ScrollCheck> checkList = new List<ScrollCheck>();
            int scrollCount = scrolls.Count;
            ScrollRectInfo scroll;
            for (int i = 0; i < scrollCount; i++)
            {
                scroll = scrolls[i];
                checkList.Add(new ScrollCheck(scroll.ScrollInitListLabel, scroll.ScrollInitListFunc));
                checkList.Add(new ScrollCheck(scroll.ScrollItemRenderLabel, scroll.ScrollItemRenderFunc));
                checkList.Add(new ScrollCheck(scroll.ScrollItemDisposeLabel, scroll.ScrollItemDisposeFunc));
            }
            return checkList;
        }

        public void Handle(INodeContext node)
        {
            if (!Config.ContainMask(Config.EUpdateMask.ScrollItem))
                return;
            node.AddContentNodeIfNotExist();
            // 获取所有 列表组件
            List<ScrollRectInfo> scrollRectList = new List<ScrollRectInfo>();
            int widgetCount = PrefabInfo.widgetList.Count;
            for (int i = 0; i < widgetCount; ++i)
                if (PrefabInfo.widgetList[i].Type == EWidgetType.ScrollRectTable)
                    scrollRectList.Add(new ScrollRectInfo(PrefabInfo.widgetList[i].Name));

            // 检查列表
            List<ScrollCheck> checkList = InitCheckList(scrollRectList);
            int checkCount = checkList.Count;

            // 内容列表
            List<string> contentList = node.GetChildrenContentList();
            int lineCount = contentList.Count;

            List<ScrollRectInfo> oldScrList = new List<ScrollRectInfo>();

            for (int i = 0; i < lineCount; ++i)
            {
                bool isCheck = false;
                for (int j = 0; j < checkCount; j++)
                    if (contentList[i].Contains(checkList[j].label))
                    {
                        checkList[j].isExist = true;
                        isCheck =true;
                    }
                if (!isCheck)
                {
                    string checkStr = "local Item_";
                    int tem1 = contentList[i].IndexOf(checkStr);
                    int tem2 = contentList[i].IndexOf(" = com_class.CreateClass(CScrollListItemBase)");
                    if (tem1 != -1 && tem2 != -1)
                        oldScrList.Add(new ScrollRectInfo(
                            contentList[i].Substring(tem1 + checkStr.Length, tem2 - tem1 - checkStr.Length)));
                }
            }

            string updateString = UpdateString(node.GetFristContentChild().GetContentList(), checkList);

            // 移除旧的自动生成的列表调用
            int oldScrCount = oldScrList.Count;
            for (int i = 0; i < oldScrCount; i++)
            {
                //updateString = updateString.Replace(oldScrList[i].ScrollItemDefineField, "");
                //updateString = updateString.Replace(oldScrList[i].ScrollItemCreateFunc, "");
                //updateString = updateString.Replace(oldScrList[i].ScrollItemDestroyFunc, "");
                //updateString = updateString.Replace(oldScrList[i].ScrollItemDrawFunc, "");
                //updateString = updateString.Replace(oldScrList[i].ScrollListUpdateFunc, "");
                //updateString = updateString.Replace(oldScrList[i].ScrollListInitFunc, "");
                //updateString = updateString.Replace(oldScrList[i].ScrollListCloseFunc, "");
            }
            if (updateString == string.Empty)
                updateString = "\r\n";

            node.SetContentToFristChild(updateString);
        }

        /// <summary>
        /// 更新字符串，在Begin标签下一行添加新内容
        /// </summary>
        public string UpdateString(List<string> contentList, List<ScrollCheck> checkList)
        {
            int lineCount = contentList.Count;
            int checkCount = checkList.Count;
            StringBuilder builder = new StringBuilder();

            for (int i = 0; i < checkCount; i++)
                if (!checkList[i].isExist)
                    builder.AppendLine(checkList[i].detail);

            for (int i = 0; i < lineCount; i++)
                builder.AppendLine(contentList[i]);

            return builder.ToString();
        }
    }

}
