
using System.Collections.Generic;
using System.Text;

namespace UICodeGenerator
{
    class WindowButtonFunctionsNodeHandler : INodeHandler
    {
        public void Handle(INodeContext node)
        {
            if (!Config.ContainMask(Config.EUpdateMask.WindowBtnFunctions))
                return;
            node.AddContentNodeIfNotExist();
            node.CleanAllContentChildren();

            string allContent = node.GetAllContent();

            StringBuilder builder = new StringBuilder();

            int widgetCount = PrefabInfo.widgetList.Count;
            ButtonCheck tem;
            for (int i = 0; i < widgetCount; ++i)
            {
                if (PrefabInfo.widgetList[i].Type == EWidgetType.Button)
                {
                    tem = new ButtonCheck(new ButtonInfo(PrefabInfo.widgetList[i].Name));
                    builder.AppendLine(tem.btn.BtnFunction);
                }
            }
            node.SetContentToFristChild(builder.ToString());
        }
        
    }
}
