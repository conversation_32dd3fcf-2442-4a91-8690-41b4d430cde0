
using System.Collections.Generic;
using System.Text;

namespace UICodeGenerator
{
    class WindowSubscribeEventsNodeHandler : INodeHandler
    {
        public void Handle(INodeContext node)
        {
            if (!Config.ContainMask(Config.EUpdateMask.WindowSubscribeEvents))
                return;
            FunctionCheck check = new FunctionCheck(Config.WindowSubscribe);

            List<ButtonCheck> btnList = new List<ButtonCheck>();
            List<ButtonInfo> oldBtnList = new List<ButtonInfo>();
            // 获取按钮和列表组件列表
            int widgetCount = PrefabInfo.widgetList.Count;
            for (int i = 0; i < widgetCount; ++i)
            {
                if (PrefabInfo.widgetList[i].Type == EWidgetType.Button)
                    btnList.Add(new ButtonCheck(new ButtonInfo(PrefabInfo.widgetList[i].Name)));
            }

            int btnCount = btnList.Count;
            int btnProxyIndex = -1;             // 按钮委托函数开始标记
            List<string> contentList = node.GetChildrenContentList();
            int lineCount = contentList.Count;
            for (int i = 0; i < lineCount; ++i)
            {
                if (contentList[i].Contains(check.label))
                {
                    check.isExist = true;
                    check.startIndex = i;
                }

                for (int k = 0; k < btnCount; k++)      // 检测是否包含按钮函数
                    if (contentList[i].Contains(btnList[k].btn.BtnFunctionLabel))
                        btnList[k].existFunc = true;

                if (contentList[i].Contains(Config.ButtonProxyMark)) // 找到注册事件函数的分割线
                    btnProxyIndex = i;
            }

            if (check.isExist)      // 如果函数存在，对其内容做判断
            {
                // 判断委托是否存在
                for (int j = check.startIndex; j < btnProxyIndex; j++)
                {
                    bool isSrcBtn = false;
                    for (int k = 0; k < btnCount; k++)
                    {
                        if (contentList[j].Contains(btnList[k].btn.BtnFuncProxyLabel))
                        {
                            btnList[k].existProxy = true;
                            isSrcBtn = true;
                        }
                    }
                    if (!isSrcBtn)
                    {
                        string checkStr = "self.On";
                        int tem1 = contentList[j].IndexOf(checkStr);
                        int tem2 = contentList[j].IndexOf("ClickedProxy = function()");
                        if (tem1 != -1 && tem2 != -1)
                            oldBtnList.Add(new ButtonInfo(
                                contentList[j].Substring(tem1 + checkStr.Length, tem2 - tem1 - checkStr.Length)));
                    }
                }
            }

            string updateString = UpdateString(node.GetFristContentChild().GetContentList(), check, btnList, btnProxyIndex);

            // 移除旧的自动生成的按钮委托
            int oldBtnCount = oldBtnList.Count;
            for (int i = 0; i < oldBtnCount; i++)
                updateString = updateString.Replace(oldBtnList[i].BtnFuncProxy, "");

            node.SetContentToFristChild(updateString);
        }

        private string UpdateString(List<string> contentList, FunctionCheck funcCheck, List<ButtonCheck> btnList, int btnProxyIndex)
        {
            int btnCount = btnList.Count;
            int lineCount = contentList.Count;
            StringBuilder builder = new StringBuilder();

            if (!funcCheck.isExist)
            {
                //写入注释
                if (funcCheck.note.Length > 0)
                    builder.AppendLine(funcCheck.note);

                builder.AppendLine(funcCheck.label);
                for (int k = 0; k < btnCount; k++)
                    builder.AppendLine(btnList[k].btn.BtnFuncProxy);
                builder.AppendLine(Config.ButtonProxyMark);
                builder.AppendLine();
                for (int k = 0; k < btnCount; k++)
                    builder.AppendLine(btnList[k].btn.BtnAddListener);
                for (int k = 0; k < btnCount; k++)
                    builder.AppendLine(btnList[k].btn.BtnFuncEventName);
                    
                builder.AppendLine(Config.FunctionEnd);
            }
            else if (funcCheck.startIndex != -1)
            {
                int i = funcCheck.startIndex;
                if (btnProxyIndex == -1)
                {
                    for (int k = 0; k < btnCount; k++)
                        builder.AppendLine(btnList[k].btn.BtnFuncProxy);
                    builder.AppendLine(Config.ButtonProxyMark);
                }
                else
                {
                    // 添加缺少的委托
                    for (int k = 0; k < btnCount; k++)
                        if (!btnList[k].existProxy)
                            builder.AppendLine(btnList[k].btn.BtnFuncProxy);
                    // 添加原有的内容到中间分割点（包括分割点）
                    for (int k = i; k <= btnProxyIndex; k++)
                        builder.AppendLine(contentList[k]);
                    // 原文本的索引直接跳到中间分割点
                    i = btnProxyIndex;
                }
                // 按钮事件直接全部添加
                for (int j = 0; j < btnCount; j++)
                    builder.AppendLine(btnList[j].btn.BtnAddListener);
                for (int k = 0; k < btnCount; k++)
                    builder.AppendLine(btnList[k].btn.BtnFuncEventName);
                // 索引直接指到函数尾部
                while (i++ <= lineCount - 2)
                    if (contentList[i].Contains(Config.FunctionEnd.Split('\r')[0]))
                        break;
                builder.AppendLine(contentList[i]);     // 添加原来的内容
            }
            return builder.ToString();
        }
    }
}
