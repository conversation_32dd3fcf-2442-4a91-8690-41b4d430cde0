using System.Collections.Generic;
using System.Text;
using UnityEngine;

namespace UICodeGenerator
{
    [System.Serializable]
    public class RegisterMessage
    {
        static public RegisterMessage instance;

        public RegisterMessage(string[] msgStructArr)
        {
            if(msgStructArr == null)
            {
                registerItemList.Clear();
                return;
            }

            int arrLength = msgStructArr.Length;
            for (int i = 0; i < arrLength; i++)
            {
                if (string.IsNullOrEmpty( msgStructArr[i]) || !msgStructArr[i].Contains(".T"))
                {
                    Debug.LogWarningFormat("注册消息格式不正确，已抛弃，索引：{0}，内容：{1}",i,msgStructArr[i]);
                    continue;
                }
                registerItemList.Add(new RegisterItem(msgStructArr[i]));
            }
        }

        [System.Serializable]
        public class RegisterItem
        {
            public string msgId = "msg_pb.MSG_XXX";
            public string handler = "OnXXX";
            public string msgStruct = "XXX_pb.TMSG_YYY";

            public RegisterItem(string msgStruct)
            {
                this.msgStruct = msgStruct;
                string msgRsp = msgStruct.Substring(msgStruct.IndexOf(".T") + 2);
                handler = "On" + msgRsp;
                msgId = "msg_pb." + msgRsp;
            }

        }

        public List<RegisterItem> registerItemList = new List<RegisterItem>();

        public int Count { get { return registerItemList.Count; } }

        static public bool IsVaild(RegisterItem item)
        {
            return !string.IsNullOrEmpty(item.msgId) &&
                !string.IsNullOrEmpty(item.handler) &&
                !string.IsNullOrEmpty(item.msgStruct);
        }

        static public string GetRegisterItemStr(RegisterItem item)
        {
            return "    {" + item.msgId + ", " + item.handler + ", " + item.msgStruct + "},\r\n";
        }

        public string GetAllRegisterItemStr()
        {
            return GetAllRegisterItemStr(this);
        }

        static public string GetAllRegisterItemStr(RegisterMessage message)
        {
            StringBuilder builder = new StringBuilder();
            int itemCount = message.registerItemList.Count;
            for (int i = 0; i < itemCount; i++)
                builder.AppendLine(GetRegisterItemStr(message.registerItemList[i]));
            return builder.ToString();
        }

        public string GetAllHandlerFuncStr()
        {
            return GetAllHandlerFuncStr(this);
        }

        static public string GetAllHandlerFuncStr(RegisterMessage message)
        {
            StringBuilder builder = new StringBuilder();
            int itemCount = message.registerItemList.Count;
            for (int i = 0; i < itemCount; i++)
                builder.AppendLine(GetHandlerFunc(message.registerItemList[i]));
            return builder.ToString();
        }

        static public string GetHandlerFunc(RegisterItem item)
        {
            return string.Format("function {0}(msg)\r\nend\r\n", item.handler);
        }
    }

}
