using System.Text.RegularExpressions;

namespace UICodeGenerator
{
    /// <summary>
    /// 检查函数
    /// </summary>
    public class FunctionCheck
    {
        //函数标题
        public string label;
        //注释
        public string note = "";
        //源文件中是否存在
        public bool isExist = false;
        //代码起始位置
        public int startIndex = -1;
        public string config = "";

        public FunctionCheck(string config)
        {
            foreach (Match match in Regex.Matches(config, @"^--\[\[(.*)\]\]"))
            {
                note = match.Value;
                break;
            }

            foreach (Match match in Regex.Matches(config, @"function(.*):(.*)\((.*)\)"))
            {
                label = match.Value;
                break;
            }

            this.config = config;
        }
    }
}
