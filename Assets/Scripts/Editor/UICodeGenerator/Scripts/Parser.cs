using System;
using System.IO;
using System.Text;
using System.Collections.Generic;
using UnityEngine;

namespace UICodeGenerator
{
    static public class Parser
    {
        /// <summary>
        /// �������ݵĽ�����Ϣ����
        /// </summary>
        public enum LineParseInfo { Normal, Error, NodeBegin, NodeEnd, }
        private static Stack<ENodeType> nodeStack = new Stack<ENodeType>();

        /// <summary>
        /// �����ļ�
        /// </summary>
        /// <param name="path">�ļ�·��</param>
        static public bool Create(string folderPath, string fileName, bool createInterface = true)
        {
            string srcFile = folderPath + '/' + fileName + ".txt";
            string iFile = folderPath + "/i" + fileName + ".txt";
            if (File.Exists(srcFile) || (createInterface && File.Exists(iFile)))
                return false;

            NodeContext context = NodeContext.GetDefualtNodeContext();  // ����Ĭ�Ͻ����
            context.DoHandler();    // ִ��Handler

            StringBuilder str = new StringBuilder();
            GetString(context, ref str);    // ת�����ַ���
			EditorHelp.CheckDir (srcFile);
            File.WriteAllText(srcFile, str.ToString());     // д���ļ�

            if (createInterface)
                CreateInterface(folderPath, fileName);
            return true;
        }

        /// <summary>
        /// �����ӿ��ļ�
        /// </summary>
        static public void CreateInterface(string folderPath, string fileName)
        {
            string iFile = folderPath + "/i" + fileName + ".txt";
            string defualt = InterfaceFile.GetDefualt();
            File.WriteAllText(iFile, InterfaceFile.Update(defualt));
        }

        /// <summary>
        /// ��������
        /// </summary>
        static public bool Update(string path)
        {
            if (!File.Exists(path))
                return false;

            NodeContext context = Parse(File.ReadAllLines(path));
            context.DoHandler();        // ��ȡ�ļ���ִ��Handler

            StringBuilder str = new StringBuilder();
            GetString(context, ref str);        // ת�����ַ���

            File.WriteAllText(path, str.ToString());
            return true;
        }

        /// <summary>
        /// �����ļ������νṹ
        /// </summary>
        static private NodeContext Parse(string[] lines)
        {
            NodeContext nodeContext = new NodeContext(ENodeType.Root);        // ���ڵ�

            Stack<NodeContext> stack = new Stack<NodeContext>();    // ���ջ
            stack.Push(nodeContext);    // ջ��Ϊ���ڵ�

            bool startContent = false;  // �Ƿ�ʼ�����ݽ��
            bool startUser = false;

            ENodeType nodeType;     // ��������ǰ�еĽ������

            int lineCount = lines.Length;
            for (int i = 0; i < lineCount; ++i)
            {
                LineParseInfo parseInfo = ParseLine(lines[i], out nodeType);
                if (i == lineCount - 1)
                    parseInfo = LineParseInfo.NodeEnd;
                // ����ÿһ�У����������ͣ���������Ǳ�ǩ��nodeTypeΪ�������
                switch (parseInfo)
                {
                    case LineParseInfo.Normal:
                        {
                            if (startContent == false)  // ����Ϊ��ͨ���ݣ��ҷ����ݽ�㣬��Ϊ���ݽ�㿪ͷ
                            {
                                startContent = true;
                                NodeContext node = new NodeContext(ENodeType.Content, false);
                                stack.Peek().childrenNodeList.Add(node);
                                stack.Push(node);
                            }

                            stack.Peek().AddContentLine(lines[i]); // Ϊ�����ӽڵ��������
                        }
                        break;
                    case LineParseInfo.NodeBegin:
                        {
                            if (startUser)
                            {
                                stack.Peek().AddContentLine(lines[i]);
                                continue;
                            }
                            if (startContent == true)        // ���ǰ�������ݽ�㣬������
                            {
                                stack.Pop();
                                startContent = false;
                            }
                            if (nodeType == ENodeType.User)
                                startUser = true;

                            NodeContext node = new NodeContext(nodeType, false);
                            stack.Peek().childrenNodeList.Add(node);
                            node.content = string.Empty;
                            stack.Push(node);
                        }
                        break;
                    case LineParseInfo.NodeEnd:
                        {
                            if (nodeType == ENodeType.User)
                                startUser = false;
                            if (startUser == true)
                            {
                                stack.Peek().AddContentLine(lines[i]);
                                continue;
                            }
                            if (startContent == true)        // ���ǰ�������ݽ�㣬������
                            {
                                stack.Pop();
                                startContent = false;
                            }

                            if (stack.Count <= 0 || (stack.Peek().NodeType != nodeType && nodeType != ENodeType.Content))   // û��ƥ�䣬����
                            {
                                Debug.LogError(string.Format("Label Not Match! Line:'{0}'. Type:'{1}'. Content:'{2}'", i, nodeType.ToString(), lines[i]));
                                return null;
                            }

                            stack.Pop();
                        }
                        break;
                    case LineParseInfo.Error:
                        {
                            if (startUser == true)
                            {
                                stack.Peek().AddContentLine(lines[i]);
                                continue;
                            }
                            Debug.LogError(string.Format("Error Label! Line:'{0}'. Content:'{1}'", i, lines[i]));
                            return null;
                        }
                }
            }
            if (stack.Count > 1)    // ��������£�ջ���ֻʣһ�����ڵ�
            {
                Debug.LogError("Label Not Match!");
                return null;
            }
            return nodeContext;
        }

        /// <summary>
        /// �����ַ�����eNodeTypeΪ��ǩ����
        /// </summary>
        /// <param name="str">��Ҫ�������ַ���</param>
        /// <param name="eNodeType">�ɹ�ʱ���صĽ������</param>
        static private LineParseInfo ParseLine(string str, out ENodeType eNodeType)
        {
            eNodeType = ENodeType.Content;
            if (str.TrimStart().StartsWith(Config.Prefix))
            {
                if (str.TrimStart().StartsWith(Config.PrefixBegin))
                {
                    ENodeType type = ENodeType.Content;
                    try
                    {
                        type = (ENodeType)Enum.Parse(typeof(ENodeType),
                            str.Substring(str.IndexOf(Config.PrefixBegin) + Config.PrefixBegin.Length).TrimEnd());
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine("������" + ex.Message);
                        return LineParseInfo.Error;
                    }
                    nodeStack.Push(type);
                    eNodeType = type;
                    return LineParseInfo.NodeBegin;
                }
                else if (str.TrimStart().StartsWith(Config.PrefixEnd))
                {
                    /*ENodeType type = ENodeType.Content;
                    try
                    {
                        type = (ENodeType)Enum.Parse(typeof(ENodeType),
                            str.Substring(str.IndexOf(Config.PrefixEnd) + Config.PrefixEnd.Length).TrimEnd());
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine("������" + ex.Message);
                        return LineParseInfo.Error;
                    }*/
                    eNodeType = /*type*/nodeStack.Pop();
                    return LineParseInfo.NodeEnd;
                }
            }
            return LineParseInfo.Normal;
        }

        /// <summary>
        /// �����ڵ㣬��ȡ���ַ���
        /// </summary>
        static private void GetString(INodeContext nodeContext, ref StringBuilder str)
        {
            // ��������ݽ�㣬ֱ��������ݲ�����
            if (nodeContext.IsContentNode())
            {
                str.Append(nodeContext.GetContent());
                return;
            }

            List<INodeContext> children = nodeContext.GetChildren();
            int childerCount = children.Count;

            // ���ͷ��ǩ
            if (!nodeContext.IsRootNode())
                str.AppendLine(nodeContext.GetNodeBeiginStr());

            // �����ӽڵ�
            for (int i = 0; i < childerCount; i++)
                GetString(children[i], ref str);

            // ���β��ǩ
            if (!nodeContext.IsRootNode())
                str.AppendLine(nodeContext.GetNodeEndStr());

        }

    }

}
