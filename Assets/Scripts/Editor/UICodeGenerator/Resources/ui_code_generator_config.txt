
Author = $无名氏$

WidgetPrefix = $Auto_$

LabelPrefix = $--@$
LabelBegin = $region $
LabelEnd = $endregion $

FileHead = 
$-- #FileName#.txt ---------------------------------
-- author:  #Author#
-- date:    #Date#
-- ver:     #Version#
-- desc:    #Description#
-------------------------------------------------
$

ModuleDeclare = 
$module("#FileName#")
--local interface = require "i#FileName#"
local window = nil
local #PrefabName# = {}
$

WidgetTable = $#PrefabName#.widget_table$

FunctionEnd = 
$end --///<<< function
$

WindowCtor =
$function #PrefabName#:ctor(selfType)
	self.__base:ctor(selfType)
#FunctionEnd#
$

WindowInit =
$--[[窗口初始化]]
function #PrefabName#:Init()
    self:SubscribeEvents()
#FunctionEnd#
$

WindowOnShow =
$--[[资源加载完成，被显示的时候调用]]
function #PrefabName#:OnShow()
    self:UpdateUIPage()
#FunctionEnd#
$

WindowOnHide =
$--[[界面隐藏时调用]]
function #PrefabName#:OnHide()
#FunctionEnd#
$

WindowSetInputParam =
$--[[设置窗口的输入参数。该参数通常是由其它模块或者外部设置进来。需要注意的是，
当调用这个函数的时候，窗口资源可能还是没有加载完成的。
@param p 参数表
]]
function #PrefabName#:SetInputParam(p)
	self.inputParam = p

    --如果正在显示，则更新一次窗口
    if self.UIRoot and self.UIRoot.activeSelf == true then
        self:UpdateUIPage()
    end
	
#FunctionEnd#
$

WindowBuildUpdateData =
$--[[构建UI更新数据]]
function #PrefabName#:BuildUpdateData()
#FunctionEnd#
$

WindowUpdateUI =
$--[[资源加载完成，被显示的时候调用]]
function #PrefabName#:UpdateUIPage()
	self:BuildUpdateData()
#FunctionEnd#
$

WindowClose =
$function #PrefabName#:Close()
    if self:IsValid() then
		self:UnsubscribeEvents()
	end
	self.__base:Close()
    window = nil
#FunctionEnd#
$

WindowSubscribe =
$--[[订阅UI事件]]
function #PrefabName#:SubscribeEvents()
#FunctionEnd#
$

WindowUnsubscribe =
$--[[退订UI事件]]
function #PrefabName#:UnsubscribeEvents()
#FunctionEnd#
$

WindowInheritedName = $C#PrefabName#$

WindowInheritedDefine = 
$local #WindowInheritedName# = class(ui_base, nil, #PrefabName#)
$

ModuleShow = 
$function Show()
    if window == nil then
        window = #WindowInheritedName#()
        window._NAME = _NAME
        window:LoadUIResource("#PrefabABPath#", nil, nil, nil)
    end
    window:Show()
    return window
end
$

ModuleHide =
$function Hide()
    if window ~= nil then
        window:Hide()
    end
end
$

ModuleClose =
$function Close()
    if window ~= nil then
        Hide()
        window:Close()
        window = nil
    end
end
$



PrefixText = $Text_$
PrefixButton = $Btn_$
PrefixScroll = $Scroll_$
PrefixImage = $Img_$
PrefixRectTransform = $Rtsf_$

TypeStrText = $"Text"$
TypeStrButton = $"Button"$
TypeStrScroll = $ScrollRectTable$
TypeStrImage = $"Image"$
TypeStrRectTransform = $"RectTransform"$

TextName = $#PrefixText##WidgetName#$
BtnName = $#PrefixButton##WidgetName#$
ScrollName = $#PrefixScroll##WidgetName#$
ImageName = $#PrefixImage##WidgetName#$

BtnFunctionName = $On#WidgetName#Clicked$
BtnFunction = $function #PrefabName#:#BtnFunctionName#()
end
$

ButtonProxyMark = $----///<<< Button Proxy Line >>>///-----$

BtnFuncProxyName = $#BtnFunctionName#Proxy$
BtnFuncProxy = 
$    self.#BtnFuncProxyName#  = function()
        self:#BtnFunctionName#()
    end
$

WidgetDefine = $    #WidgetName# = { path = "#WidgetPath#", type = #WidgetTypeStr#, event_name = "#BtnFuncProxyName#" },$

ScrollInitListFunc = 
$--[[初始化列表]]
function #PrefabName#:InitList()
    self.#WidgetName#.onItemRender = OnItemRender
    self.#WidgetName#.onItemDispose = OnItemDispose
end
$

ScrollItemRenderFunc = 
$--[[渲染子项时调用，用于初始化item]]
function OnItemRender(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem
end
$

ScrollItemDisposeFunc = 
$--[[销毁子项时调用]]
function OnItemDispose(scroll_rect_item, index)
    if scroll_rect_item and scroll_rect_item.data then
        if scroll_rect_item.data.itemUI ~= nil then
            scroll_rect_item.data.itemUI:Dispose()
            scroll_rect_item.data.itemUI = nil
        end
    end
end
$

MsgTableName = $MessageTable$

MsgTableStart = $--///<<< tableStart$
MsgTableEnd = $--///<<< tableEnd$

MsgTable =
$#MsgTableName# =
{ #MsgTableStart#
} #MsgTableEnd#
$

RegisterMsg = 
$net_route.RegisterMsgHandlers(#MsgTableName#)$

UnregisterMsg = 
$net_route.UnregisterMsgHandlers(#MsgTableName#)$
