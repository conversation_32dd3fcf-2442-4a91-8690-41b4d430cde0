-- #IFileNameWithSuffix#.lua -----------------------------------
-- author:  #Author#
-- date:    #Date#
-- ver:     #Version#
-- desc:    #Description#
----------------------------------------------------------------
local print = print
local require = require
local table = table
local pairs = pairs

local log = require "log"
local windowMgr = require "ui_window_mgr"

module("#IFileName#")

local MODULE_NAME = "#IFileName#"

------------------------------------------------------------------------
-- /// 对外接口：提供给外部使用的接口，请写在下面
------------------------------------------------------------------------

--[[窗口是否显示]]
function IsWindowShown()
  return windowMgr:IsModuleShown(MODULE_NAME)
end

--[[显示窗口，改函数会使用windowMgr来打开窗口
@param param 要设置的窗口数据
]]
function ShowWindow(data)
    local window = windowMgr:ShowModule(MODULE_NAME)
	if data ~= nil and window then
		window:SetInputParam(data)
	end
end

--[[关闭窗口，改函数会使用windowMgr来关闭窗口]]
function HideWindow()
    windowMgr:HideModule(MODULE_NAME)
end

--[[获取窗口对象]]
function GetWindow()
    return windowMgr:GetWindowObj(MODULE_NAME)
end

------------------------------------------------------------------------
-- /// UI模块接口：下面的接口都是WindowMgr来调用的，不要直接调用。
-- /// 假如绕过WindowMgr来调用可能会导致一些问题
------------------------------------------------------------------------
function Show()
    if IsWindowShown() == false then
        local w = require "#FileName#"
        return w.Show()
    else
        return GetWindow()
    end
end

function Hide()
    if IsWindowShown() == true then
        local w = require "#FileName#"
        w.Hide()
    end
end

function Close()
    if IsWindowShown() == true then
        local w = require "#FileName#"
        w.Close()
    end
end


------------------------------------------------------------------------
-- /// 消息注册
------------------------------------------------------------------------

local MessageTable = 
{
#MessageItems#
}

#MessageFunc#

local net_route = require "net_route"
net_route.RegisterMsgHandlers(MessageTable)

