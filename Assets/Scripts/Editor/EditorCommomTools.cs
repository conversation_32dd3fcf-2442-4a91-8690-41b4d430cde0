using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using UnityEngine.UI;
using System.Text;

public class EditorCommomTools : Editor
{
    /// <summary>
    /// ��ȡ����ǰ׺��Ӧ���
    /// </summary>
    private static Dictionary<string, string> componetDic = new Dictionary<string, string>()
    {
        { "txt", "Text"},
        { "rt",  "RectTransform"},
        { "btn", "Button" },
        { "scr", "ScrollRect" },
        { "tr",  "Transform" },
        { "obj", "GameObject" },
        { "input", "InputField"},
        { "img", "Image" },        
    };

    /// <summary>
    /// ������Ҫ��ӵ��ֶ�
    /// </summary>
    private static Dictionary<string, string> specialSuffix = new Dictionary<string, string>()
    {
        { "Button", "backEvent = true"}
    };

    /// <summary>
    /// ��������
    /// </summary>
    private static int MAX_DEPTH = 99;

    private static readonly TextEditor textTool = new TextEditor();

    [MenuItem("GameObject/Copy Widget", false, 0)]
    public static void CopyWidget()
    {
        Transform trans = Selection.activeTransform;
        if (null == trans) return;

        CheckIsValid(trans);
    }

    [MenuItem("GameObject/Copy Path", false, 0)]
    public static void CopyPath()
    {
        Transform trans = Selection.activeTransform;
        if (null == trans) return;

        textTool.text = GetPath(trans);
        textTool.SelectAll();
        textTool.Copy();
    }

    static string GetPath(Transform trans)
    {
        if (null == trans) return string.Empty;
        if (null == trans.parent) return trans.name;
        return GetPath(trans.parent) + "/" + trans.name;
    }

    static void CheckIsValid(Transform root)
    {
        int curDepth = 0;
        StringBuilder sb = new StringBuilder();
        var rootName = root.name;
        foreach (Transform trans in root.GetComponentsInChildren<Transform>(true))
        {
            if (trans == root) continue;
            var myName = trans.name;
            foreach (var prefix in componetDic)
            {
                if (curDepth >= MAX_DEPTH)
                {
                    Debug.Log("��⵽�����������ޣ��������޲��ֲ��踴�ƣ���");
                    break;
                }
                if(myName.StartsWith(prefix.Key))
                {
                    var myPath = GetPath(trans);

                    var replace = myPath.Replace(rootName + "/", "\\");
                    var split = replace.Split('\\');
                    
                    if (split.Length > 1)
                    {
                        myPath = split[1];
                    }

                    string copyContent = myName + " = {";
                    string content = "";
                    if(specialSuffix.TryGetValue(prefix.Value, out string suffix))
                    {                                      
                        content = string.Format("path = \"{0}\", type = \"{1}\", {2}", myPath, prefix.Value, suffix);                        
                    }
                    else
                    {                                                
                        content = string.Format("path = \"{0}\", type = \"{1}\"", myPath, prefix.Value);                        
                    }
                    copyContent += content + "},";

                    sb.Append(copyContent);
                    sb.Append("\n\t");

                    curDepth++;
                }
            }
        }

        textTool.text = sb.ToString();
        textTool.SelectAll();
        textTool.Copy();

    }
}
