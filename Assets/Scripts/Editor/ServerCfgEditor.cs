#if UNITY_EDITOR_WIN
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System.IO;
using UnityEngine.UI;
using LitJson;
using War.UI;
using War.Script;
using UnityEditor.Experimental.SceneManagement;
using System;
using UnityEditorInternal;
using Debug = UnityEngine.Debug;
using System.Diagnostics;
using System.Text;
using LitJson;
using UnityEngine.Networking;

public class ServerConfigEditorWin : EditorWindow
{


    private static string clientServerCfgPath;//服务配置相关文件地址
    private static string serverRootCfgPath; //需要配置的目录路径
    private static string serverBatPath; //服务器bat文件路径
    private static ServerConfigEditorWin win;
    private static string luaServerInfoPaht; //客户端lua服务器列表文件，用于做注入
    private static string world = "";
    private static string region = "";
    private static string csvPath = ""; //配置路径地址
    private static string skill_dat_srcPath = ""; //需要修复用的skill.dat文件
    private static string skill_dat_destPath = ""; //skill.dat位置

    private static GUIContent[] tabContents = new GUIContent[] { new GUIContent("服务器配置"),new GUIContent("修改角色创建时间"),new GUIContent("查看活动配置") };
    private static int selectTab = 0;

    [MenuItem("ServerCtrl(私服)/打开私人服务器配置")]
    public static void OpenWindow()
    {
        if (win == null)
        {
            win = EditorWindow.CreateWindow<ServerConfigEditorWin>();
        }
        InitConst();
        win.Show();
    }
    private static void InitConst()
    {
        hadReadDetailInfo = false;
        clientServerCfgPath = Application.dataPath.Replace("Assets", "ServerCfg/"); //服务配置相关文件地址
        serverRootCfgPath = Application.dataPath.Replace("Client/Assets", "") + "3rdparty/zookeeper"; //需要配置的目录路径
        serverBatPath = Application.dataPath.Replace("Client/Assets", "") + "Server/"; //服务器bat文件路径
        csvPath = Application.dataPath.Replace("BinClient/Client/Assets", "") + "Tools/csv_script/";
        luaServerInfoPaht = Application.dataPath + "/Lua/server_info.txt";
        skill_dat_srcPath = clientServerCfgPath + "Skill.dat";

        skill_dat_destPath = serverBatPath + "BattleServer/Data/BattleScp/Skill.dat";
        world = Environment.UserName;
        region = Environment.UserDomainName;
    }
    private static ServerDbEnum curDbType = ServerDbEnum.位面数据库2;
    private static int userId = 0;
    private static int timeStamp = 1615988057;
    private static int yy = 2020;
    private static int mm = 12;
    private static int dd = 12;

    private void OnGUI()
    {
        if (string.IsNullOrEmpty(region))
        {
            InitConst();
        }
        selectTab = GUILayout.SelectionGrid(selectTab, tabContents, 5);
        switch (selectTab) {
            case 0:DrawServerCfg();
                break;
            case 1:DrawRoleTimeChange();
                break;
            case 2:DrawActInfo();
                break;
            default:
                break;
        
        }
        ShowErrorLog();
    }
#region 绘制服务器配置设置界面
    private void DrawServerCfg()
    {
        if (GUILayout.Button("一键配置个人服务器"))
        {
            string tmpelatePath = clientServerCfgPath + "ZCfgTemplate.bat";
            string tarPath = clientServerCfgPath + "res/ZConfig.bat";
            string tarPath2 = serverBatPath + "ZConfig.bat";
            string dbCfg = clientServerCfgPath + "dbCfg/";
            string dbCfgTar = clientServerCfgPath + "res/config/";

            var ps = Process.Start(clientServerCfgPath + "runReplace.exe", world + " " + region + " " + tmpelatePath + " " + clientServerCfgPath + "ZConfig.bat");
            ps.WaitForExit();

            if (!File.Exists(clientServerCfgPath + "ZConfig.bat"))
            {
                LocalError("初始化延迟，请重试");
                return;
            }
            File.Copy(clientServerCfgPath + "ZConfig.bat", tarPath, true);
            File.Copy(clientServerCfgPath + "ZConfig.bat", tarPath2, true);
            File.Delete(clientServerCfgPath + "ZConfig.bat");
            if (curDbType == ServerDbEnum.位面数据库1)
            {
                File.Copy(dbCfg + "Public/DBConfig.xml", dbCfgTar + "Public/DBConfig.xml", true);
                File.Copy(dbCfg + "RegionPublic/DBConfig.xml", dbCfgTar + "RegionPublic/DBConfig.xml", true);
            }
            else
            {
                File.Copy(dbCfg + "Public/DBConfig2.xml", dbCfgTar + "Public/DBConfig.xml", true);
                File.Copy(dbCfg + "RegionPublic/DBConfig2.xml", dbCfgTar + "RegionPublic/DBConfig.xml", true);
            }


            //配置完毕后复制文件夹
            CopyFolder(clientServerCfgPath + "res", serverRootCfgPath + "/cfg1/");
            //复制完毕，启动脚本挂载更新


            var startInfo = new ProcessStartInfo();
            startInfo.WorkingDirectory = serverRootCfgPath + "/cfg1";
            startInfo.FileName = "UpdateWorldAndRegionConfig.bat";
            Process proc1 = Process.Start(startInfo);
            proc1.WaitForExit();

            var startInfo2 = new ProcessStartInfo();
            startInfo2.WorkingDirectory = serverRootCfgPath;
            startInfo2.FileName = "UpdateAllConfig.bat";
            Process proc2 = Process.Start(startInfo2);
            proc2.WaitForExit();


        }
        EditorGUILayout.LabelField("REGIONWORLD", region);
        EditorGUILayout.LabelField("WORLD", world);
        curDbType = (ServerDbEnum)EditorGUILayout.EnumPopup("数据库", curDbType);
        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("启动服务器"))
        {

            StartServer();
        }
        if (GUILayout.Button("关闭服务器"))
        {
            CloseServer();
        }

        EditorGUILayout.EndHorizontal();

        //if (GUILayout.Button("临时修复服务器Skill.dat文件"))
        //{
        //    File.Copy(skill_dat_srcPath, skill_dat_destPath, true);
        //    Debug.Log("修复成功");
        //}

        GUILayout.TextArea("1.xGame换皮项目目前暂时连接位面数据库2的配置\n2.个人服务器只需要切换数据库或者更新的时候才需要配置，配置之后每次需要启动服务器，只需要点击启动服务器即可");
     
    }
#endregion
#region 绘制时间修改界面
    private void DrawRoleTimeChange()
    {
        GUILayout.Label("设置角色创建时间");

        if (GUILayout.Button("获得角色id", GUILayout.Width(100)))
        {
            if (Application.isPlaying && LuaManager.Instance != null)
            {
                LuaManager.Instance.luaEnv.DoString("GlobalTmp = player_mgr.GetPlayerRoleID()");
                userId = LuaManager.Instance.luaEnv.Global.Get<int>("GlobalTmp");
            }
        }
        userId = EditorGUILayout.IntField("用户id:", userId);
        var tmpType = curDbType;
        curDbType = (ServerDbEnum)EditorGUILayout.EnumPopup("所属数据库", curDbType);
        if (tmpType != curDbType)
        {
            if (curDbType == ServerDbEnum.位面数据库1)
            {
                curDbTb = ServerDbTableEnum.idle2_Gamedb_49私服数据表;
            }
            else
            {
                curDbTb = ServerDbTableEnum.wm2_gamedb_52私服数据表;
            }
        }
        curDbTb = (ServerDbTableEnum)EditorGUILayout.EnumPopup("数据所在表", curDbTb);
        switch (curDbTb)
        {
            case ServerDbTableEnum.idle2_Gamedb_49私服数据表:
                dbTableString = "idle2_Gamedb";
                break;
            case ServerDbTableEnum.wm2_gamedb_52私服数据表:
                dbTableString = "wm2_gamedb";
                break;
            case ServerDbTableEnum.xgame_gamedb_49内测服数据表:
                dbTableString = "xgame_gamedb";
                break;
            case ServerDbTableEnum.自定义数据表字段:
                break;
        }
        if (curDbTb == ServerDbTableEnum.自定义数据表字段)
        {
            dbTableString = EditorGUILayout.TextField("自定义数据表字段:", dbTableString);
        }
        int tmp = timeStamp;
        timeStamp = EditorGUILayout.IntField("目标时间戳:", timeStamp);
        if (tmp != timeStamp)
        {

            TimeChange();
        }
        GUILayout.BeginHorizontal();
        GUILayout.Label("格式化日期显示：");
        GUILayout.Space(47);
        tmp = yy;
        yy = EditorGUILayout.IntField(yy);
        if (yy >= 2037)
        {
            yy = 2037;
        }
        if (yy != tmp)
        {
            TimeChange2();
        }
        GUILayout.Label("年");
        tmp = mm;
        mm = EditorGUILayout.IntField(mm);
        if (mm != tmp)
        {
            TimeChange2();
        }
        GUILayout.Label("月");
        tmp = dd;
        dd = EditorGUILayout.IntField(dd);
        if (dd != tmp)
        {
            TimeChange2();
        }
        GUILayout.Label("日");
        GUILayout.EndHorizontal();
        if (GUILayout.Button("修改用户创建时间"))
        {
            if (userId == 0)
            {
                LocalError("请选择要修改的用户id");
                return;
            }
            if (Application.isPlaying && LuaManager.Instance != null)
            {
                LuaManager.Instance.luaEnv.DoString("GlobalTmp = player_mgr.GetPlayerRoleID()");
                if(userId == LuaManager.Instance.luaEnv.Global.Get<int>("GlobalTmp"))
                {
                    LocalError("角色修改数据需要先退出登陆");
                    return;
                }
            }
            if (timeStamp < 0)
            {
                LocalError("时间戳小于0,无法修改");

                return;
            }
        
            if (string.IsNullOrEmpty(dbTableString))
            {
                LocalError("请指定要修改内容的表");
                return;
            }

 

            string program = clientServerCfgPath + "DBCtrl/DBCtrl/bin/Debug/DBCtrl.exe";
            string dbConn = "";
            if (curDbType == ServerDbEnum.位面数据库1)
            {
                dbConn = db1;
            }
            else
            {
                dbConn = db2;
            }
            dbConn = dbConn.Replace("**dbReplace**", dbTableString);
            var ps = Process.Start(program, dbConn + " " + userId + " " + " " + timeStamp);
            ps.WaitForExit();
        }

    
        GUILayout.TextArea("1.修改前需先退出登陆\n2.修改内测账号数据，请选择位面数据库1，Xgame_gamedb_49内测服数据表\n3.私服账号选择位面数据库1为49私服，位面数据库2为52私服，换皮项目目前使用的52私服表");
       
    }

    void ShowErrorLog()
    {
        if (!string.IsNullOrEmpty(logInfo))
        {
            GUILayout.BeginHorizontal();
            GUILayout.Label(EditorGUIUtility.TrTextContentWithIcon(logInfo, MessageType.Error));
            GUILayout.EndHorizontal();
            if (logRecordTime-- <= 0)
            {
                logInfo = "";
            }
        }

    }


    private static int localTimeSet = 0;
    private static string searchOpt = "";
    private static GUIContent[] actTab = new GUIContent[] { new GUIContent("活动详情表"), new GUIContent("活动UI表") };
    private static int actInfoSelected = 0;
#endregion
#region 绘制配置显示界面
    private void DrawActInfo()
    {
        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("FestivalActivity.csv数据更新"))
        {
            OutputFestivalActInfo();
            UpdateFDataLs();
        }
        if (GUILayout.Button("FestivalUIDetail.csv数据更新"))
        {
            uiDetailData = null; 
            curShowData = null;
            UpdateUIdetailInfo();
        }
        EditorGUILayout.EndHorizontal();


        actInfoSelected = GUILayout.SelectionGrid(actInfoSelected, actTab, 3);
        EditorGUILayout.BeginHorizontal();
        GUILayout.Label("搜索框",GUILayout.Width(100));
        searchOpt = GUILayout.TextField(searchOpt,GUILayout.Width(400));
        if (GUILayout.Button("X", GUILayout.Width(50)))
        {
            searchOpt = "";
            Repaint();
        }
        EditorGUILayout.EndHorizontal(); 
        if(actInfoSelected == 0)
        {
            if (fDataLs == null || fDataLs.Count <= 0)
            {
                UpdateFDataLs();
                Event.current.Use();
                return;
            }
            DrawActInfo1();
        }else if(actInfoSelected == 1)
        {
            if (fDataLs2 == null || fDataLs2.Count <= 0)
            {
                UpdateFDataLs2();
                Event.current.Use();
                return;
            }
            DrawActInfo2();
        }
       

        EditorGUILayout.BeginHorizontal();
        localTimeSet = EditorGUILayout.IntField(localTimeSet);
        if (GUILayout.Button("设置本地时间"))
        {
            ChangeSysTime(localTimeSet);
        }
        if ((GUILayout.Button("还原本地时间")))
        {
            ChangeSysTime(0);
        }
        EditorGUILayout.EndHorizontal();




        if (curShowData != null)
        {
            GUILayout.Label("", GUILayout.Height(20));
            EditorGUILayout.BeginHorizontal();
            GUILayout.Label("字段搜索框", GUILayout.Width(100));
           
            detailIdSearchOpt = GUILayout.TextField(detailIdSearchOpt, GUILayout.Width(400));
            if (GUILayout.Button("X", GUILayout.Width(50)))
            {
                detailIdSearchOpt = "";
                Repaint();
            }
            if (GUILayout.Button("全部复制", GUILayout.Width(100)))
            {
                string strs = "";
                for (int i = 0; i < uidetailKey.Length; i++)
                { 
                    EditorGUILayout.BeginHorizontal();
                    if (GUILayout.Button("更新字段", GUILayout.Width(80)))
                    {
                        UpdateUIDetailToCsv(curShowData["atyId"].ToString(), uidetailKey[i], uidetailDataInfo[i]);
                    }
                    strs += uidetailKey[i] + " : " + uidetailDataInfo[i]+"\n"; 
                }
                GUIUtility.systemCopyBuffer = strs;
            }
            EditorGUILayout.EndHorizontal();
            detailInfoPos = EditorGUILayout.BeginScrollView(detailInfoPos, GUILayout.Height(450));
            for (int i = 0; i < uidetailKey.Length; i++)
            { 
                if (!string.IsNullOrEmpty(detailIdSearchOpt))
                {
                    if (!uidetailKey[i].ToLower().Contains(detailIdSearchOpt.ToLower()))
                    {

                        continue;
                    }
                }
                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("更新字段",GUILayout.Width(80)))
                {
                    UpdateUIDetailToCsv(curShowData["atyId"].ToString(), uidetailKey[i], uidetailDataInfo[i]);
                }
                GUILayout.Label(uidetailKey[i],GUILayout.Width(150));
                uidetailDataInfo[i]  = GUILayout.TextField(uidetailDataInfo[i],GUILayout.Width(200));
                GUILayout.Label(uidescInfo[i], GUILayout.Width(600));
                EditorGUILayout.EndHorizontal();
            }
            EditorGUILayout.EndScrollView();
        }

        if (GUILayout.Button("打表不打lang", GUILayout.Width(100))){
            string path = Application.dataPath.Replace("BinClient/Client/Assets", "") + "Tools/bdr";
            var startInfo = new ProcessStartInfo();
            startInfo.WorkingDirectory = path;
            startInfo.FileName = "客户端打表C# 不打lang表.bat";
            Process proc = Process.Start(startInfo);
        }

    }

    private void DrawActInfo1()
    {
        EditorGUILayout.BeginHorizontal();
        GUILayout.Label("活动名", GUILayout.Width(250));
        GUILayout.Label("活动开始时间", GUILayout.Width(250));
        GUILayout.Label("UIDetailID");
        EditorGUILayout.EndHorizontal();
        GUILayout.Label("", GUILayout.Height(15));
        fScroppPos = EditorGUILayout.BeginScrollView(fScroppPos, GUILayout.Height(150));
        for (int i = 0; i < fDataLs.Count; i++)
        {
            var dt = fDataLs[i];
            if (!string.IsNullOrEmpty(searchOpt))
            {
                if (!dt.actName.Contains(searchOpt))
                {
                    continue;
                }
            }
            EditorGUILayout.BeginHorizontal();
            GUILayout.Label(dt.actName, GUILayout.Width(250));
            if (GUILayout.Button(dt.startTime, GUILayout.Width(250)))
            {
                DateTime timeNow = System.DateTime.UtcNow;
                string[] timeArr = dt.startTime.Split('-');

                DateTime dateTime = new DateTime(int.Parse(timeArr[0]), int.Parse(timeArr[1]), int.Parse(timeArr[2]), timeNow.Hour, timeNow.Minute, timeNow.Second);
                TimeSpan ts = dateTime - new DateTime(1970, 1, 1, 0, 0, 0, 0);
                localTimeSet = (int)(Convert.ToInt64(ts.TotalMilliseconds / 1000));
            }
            if (GUILayout.Button(dt.uidetailId, GUILayout.Width(50)))
            {
                if (!string.IsNullOrEmpty(dt.uidetailId))
                {
                    GetCurUIDetailInfo(dt.uidetailId);
                }

            }
            EditorGUILayout.EndHorizontal();
        }
        EditorGUILayout.EndScrollView();

    }

    private void DrawActInfo2()
    {
        EditorGUILayout.BeginHorizontal();
        GUILayout.Label("UI配置界面名", GUILayout.Width(250));
        GUILayout.Label("UIModule", GUILayout.Width(250));
        GUILayout.Label("UIDetailID");
        EditorGUILayout.EndHorizontal();
        GUILayout.Label("", GUILayout.Height(15));
        fScroppPos = EditorGUILayout.BeginScrollView(fScroppPos, GUILayout.Height(150));
        for (int i = 0; i < fDataLs2.Count; i++)
        {
            var dt = fDataLs2[i];
            if (!string.IsNullOrEmpty(searchOpt))
            {
                if (!(dt.actName.Contains(searchOpt) || dt.startTime.Contains(searchOpt)))
                {
                    continue;
                }
            }
            EditorGUILayout.BeginHorizontal();
            GUILayout.Label(dt.actName, GUILayout.Width(250));
            GUILayout.Label(dt.startTime, GUILayout.Width(250));
            if (GUILayout.Button(dt.uidetailId, GUILayout.Width(50)))
            {
                if (!string.IsNullOrEmpty(dt.uidetailId))
                {
                    GetCurUIDetailInfo(dt.uidetailId);
                }

            }
  
         
            EditorGUILayout.EndHorizontal();
        }
        EditorGUILayout.EndScrollView();
    }
    static string detailIdSearchOpt = "";
    /// <summary>
    /// 更新字段到表，同时更新本地数据
    /// </summary>
    /// <param name="uiDetail"></param>
    /// <param name="field"></param>
    /// <param name="newParam"></param>
    private static void UpdateUIDetailToCsv(string uiDetail,string field,string newParam)
    {
        if(curShowData[field].ToString() == newParam)
        {
            LocalError("没有任何改变");
            return;
        }
        string path = csvPath + "FestivalUIDetail.csv";
        curShowData[field] = newParam;
        //JsonData jd = new JsonData();

        //jd["path"] = path;
        //jd["atyId"] = uiDetail;
        //jd["field"] = field;
        //jd["newVal"] = newParam;
        var startInfo = new ProcessStartInfo();
        startInfo.WorkingDirectory = clientServerCfgPath + "CsvReader/CsvReader/bin/Debug";
        startInfo.FileName = "CsvReader.exe";
        if (string.IsNullOrEmpty(newParam))
        {
            newParam = "*empty*";
        }
        startInfo.Arguments = "2 " + path+" "+uiDetail+" "+field+" "+newParam;
        Process proc = Process.Start(startInfo);
        proc.WaitForExit();
       

    }
    private static JsonData uiDetailData;
    private static JsonData curShowData;
    private static string[] uidetailDataInfo;
    private static string[] uidetailKey;
    private static string[] uidescInfo;

    private static bool hadReadDetailInfo = false;
    private static void GetCurUIDetailInfo(string detailId)
    {
        if (uiDetailData == null || hadReadDetailInfo == false)
        {
            string detailDataPath = Application.dataPath.Replace("Assets", "ServerCfg/") + "CsvReader/CsvReader/bin/Debug/festivalUIDetail.json";
            if (!hadReadDetailInfo) //没初始化过数据，先初始化
            {
                UpdateUIdetailInfo();
                return;
            } 
            string dt = File.ReadAllText(detailDataPath);
            uiDetailData = JsonMapper.ToObject(dt);
         
            IDictionary dict = uiDetailData[0] as IDictionary;
            
            List<string> keys = new List<string>();
            foreach (string key in dict.Keys)
            {
                if(string.IsNullOrEmpty(key))
                {
                    continue;
                } 
                keys.Add(key);
            } 
            uidetailKey = new string[keys.Count] ;
            uidescInfo = new string[keys.Count];
            var descInfo = uiDetailData[uiDetailData.Count - 1];
            for (int i = 0;i<keys.Count;i++ )
            {
                uidetailKey[i] = keys[i];
                if(keys[i] == "atyId")
                {
                    uidescInfo[i] = "ui配置标识Id"; 
                }
                else
                {
                    uidescInfo[i] = descInfo[keys[i]].ToString();
                }
               

            }  
        }
        bool reach = false;
        for(int i = 0; i < uiDetailData.Count - 1; i++) //最后一个是文本描述，不读取
        {
            if(uiDetailData[i]["atyId"].ToString() == detailId)
            {
                reach = true;
                curShowData = uiDetailData[i];
                uidetailDataInfo = new string[uidetailKey.Length];
                for(int j = 0; j < uidetailKey.Length; j++)
                { 
                    uidetailDataInfo[j] = curShowData[uidetailKey[j]].ToString();
                }
                break;
            }
        }
        if (!reach)
        {
            LocalError("错误,没有找到这个UIDetailID"+detailId);
        } 
    }
    private static void UpdateUIdetailInfo()
    {
        hadReadDetailInfo = true;
         var startInfo = new ProcessStartInfo();
        startInfo.WorkingDirectory = clientServerCfgPath + "CsvReader/CsvReader/bin/Debug";
        startInfo.FileName = "CsvReader.exe";
        startInfo.Arguments ="1 "+ csvPath + "FestivalUIDetail.csv";
        startInfo.CreateNoWindow = true;
        Process proc = Process.Start(startInfo); 
        proc.WaitForExit();
    }
#endregion
    private static Vector2 fScroppPos = Vector2.zero;
    private static Vector2 detailInfoPos = Vector2.zero;
    private static void UpdateFDataLs()
    {
        string fDataPath  = Application.dataPath.Replace("Assets", "ServerCfg/") + "CsvReader/CsvReader/bin/Debug/festivalActivity.json";
        if (!File.Exists(fDataPath))
        {
            OutputFestivalActInfo(); 
        }
        fDataLs = new List<FestivalActInfoData>(); 
        string str = File.ReadAllText(fDataPath);
        JsonData jd = JsonMapper.ToObject(str);
        for(int i = 0; i < jd.Count; i++)
        {
            FestivalActInfoData fd = new FestivalActInfoData();
            fd.actName = ((string)jd[i]["activityRemark"]);
            fd.startTime = ((string)jd[i]["timeStart"]);
            fd.uidetailId = ((string)jd[i]["uidetailID"]);
            fDataLs.Add(fd);
        }
    }
    private static void UpdateFDataLs2()
    {
        string fDataPath = Application.dataPath.Replace("Assets", "ServerCfg/") + "CsvReader/CsvReader/bin/Debug/festivalUIDetail.json";
        if (!File.Exists(fDataPath))
        {
            UpdateUIdetailInfo();
        }
        fDataLs2 = new List<FestivalActInfoData>();
        string str = File.ReadAllText(fDataPath);
        JsonData jd = JsonMapper.ToObject(str);
        for (int i = 0; i < jd.Count-1; i++)
        {
            FestivalActInfoData fd = new FestivalActInfoData();
            fd.actName = ((string)jd[i]["desc"]);
            fd.startTime = ((string)jd[i]["uiModule"]);
            fd.uidetailId = ((string)jd[i]["atyId"]);
            fDataLs2.Add(fd);
        }
    }
    private static List<FestivalActInfoData> fDataLs;
    private static List<FestivalActInfoData> fDataLs2;
    private static void OutputFestivalActInfo()
    {
        var startInfo = new ProcessStartInfo();
        startInfo.WorkingDirectory = clientServerCfgPath + "CsvReader/CsvReader/bin/Debug";
        startInfo.FileName = "CsvReader.exe";
        startInfo.Arguments = "0 "+ csvPath + "festivalActivity.csv";
        Process proc = Process.Start(startInfo);
        proc.WaitForExit();

   
    }

    private static ServerDbTableEnum curDbTb = ServerDbTableEnum.wm2_gamedb_52私服数据表;
    private static string dbTableString = "";
    const string db1 = "host=*************;port=3306;Database=**dbReplace**;user=idle_svr;password=******;charset=utf8;";
    const string db2 = "host=***********;port=3306;Database=**dbReplace**;user=root;password=***********;charset=utf8;";

    //const string db1 = "host=*************;port=3306;Database=idle2_Gamedb;user=idle_svr;password=******;charset=utf8;";
    //const string db2 = "host=***********;port=3306;Database=wm2_gamedb;user=root;password=***********;charset=utf8;";

    static string logInfo = "";
    static int logRecordTime = 0;
    private static void SetLogInfo(string info)
    {
        logInfo = info;
        logRecordTime = 10;
    }
    private static void TimeChange()
    {
        System.DateTime startTime = TimeZone.CurrentTimeZone.ToLocalTime(new System.DateTime(1970, 1, 1));
        DateTime dt = startTime.AddSeconds(timeStamp);
        yy = dt.Year;
        mm = dt.Month;
        dd = dt.Day;

    }
    private static void TimeChange2()
    {
        try
        {
            System.DateTime startTime = TimeZone.CurrentTimeZone.ToLocalTime(new System.DateTime(yy, mm, dd));
            TimeSpan ts = startTime - new DateTime(1970, 1, 1, 0, 0, 0, 0);
            timeStamp = (int)(Convert.ToInt64(ts.TotalMilliseconds / 1000));
        }
        catch
        {
            TimeChange();
        } 
    }

    private static void ChangeSysTime(int targetTime = 0)
    {
        string program = clientServerCfgPath + "DBCtrl/DBCtrl/bin/Debug/DBCtrl.exe"; 
        var ps = Process.Start(program, targetTime.ToString());
        ps.WaitForExit();
    }

    private static void LocalError(string msg)
    {
        SetLogInfo(msg);
    }
    /// <summary>
    /// 将配置文件夹复制到制定目录
    /// </summary>
    /// <param name="sourceFolder"></param>
    /// <param name="destFolder"></param>
    public static void CopyFolder(string sourceFolder, string destFolder)
    {
        //如果目标路径不存在,则创建目标路径
        if (!Directory.Exists(destFolder))
        {
            Directory.CreateDirectory(destFolder);
        }
        //得到原文件根目录下的所有文件
        string[] files = Directory.GetFiles(sourceFolder);
        foreach (string file in files)
        {
            string name = Path.GetFileName(file);
            string dest = Path.Combine(destFolder, name);
            File.Copy(file, dest, true);//复制文件
        }
        //得到原文件根目录下的所有文件夹
        string[] folders = Directory.GetDirectories(sourceFolder);
        foreach (string folder in folders)
        {
            string name = Path.GetFileName(folder);
            string dest = Path.Combine(destFolder, name);
            CopyFolder(folder, dest);//构建目标路径,递归复制文件
        }

    }


    public static void StartServer()
    {
        string path = Application.dataPath.Replace("Client/Assets", "") + "Server/";

        var startInfo = new ProcessStartInfo();
        startInfo.WorkingDirectory = path;
        startInfo.FileName = "StartAll.bat";
        Process proc = Process.Start(startInfo);

    }


    public static void CloseServer()
    {
        string path = Application.dataPath.Replace("Client/Assets", "") + "Server/";
        var startInfo = new ProcessStartInfo();
        startInfo.WorkingDirectory = path;
        startInfo.FileName = "CloseAll.bat";
        Process proc = Process.Start(startInfo);

    }
    enum ServerDbEnum
    {
        位面数据库1 = 1,
        位面数据库2 = 2
    }

    enum ServerDbTableEnum
    {
        xgame_gamedb_49内测服数据表 = 1,
        idle2_Gamedb_49私服数据表 = 2,
        wm2_gamedb_52私服数据表 = 3,
        自定义数据表字段 = 4
    }

    public struct FestivalActInfoData
    {
        public string actName;
        public string startTime;
        public string uidetailId;
    }

}
#endif