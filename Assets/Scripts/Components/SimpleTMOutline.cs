
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Profiling;
using UnityEngine.Rendering;

namespace TextMeshUtil
{
    [ExecuteInEditMode]
    public class SimpleTMOutline : MonoBehaviour
    {
        #region private filed
        private TextMesh m_textMesh;

        [SerializeField]
        private List<TextMesh> m_outlineClone;

        [SerializeField] 
        private Color m_outlineColor = Color.black;

        [SerializeField]
        [Range(0,0.03f)]
        private float m_outlineThickness = 0.01f;

        private string m_text;

        [SerializeField]
        private int m_sortingOrder;
        private SortingGroup m_sortGroup;
        private MeshRenderer m_renderer;
        #endregion

        #region public method
        public void ManualUpdateOutline()
        {
            UpdateText();
        }
        #endregion

        #region private method
        private void SetTransformOffset(Transform transform, int index)
        {
            var localPos = transform.localPosition;
            var _x = (index & 0x1) > 0 ? 1 : -1;
            var _y = (index & 0x2) > 0 ? 1 : -1;
            localPos.x = m_outlineThickness * _x;
            localPos.y = m_outlineThickness * _y;
            localPos.z = 0.001f;
            transform.localPosition = localPos;
        }

        private void OnCreated()
        {
            if (m_textMesh == null)
                return;

            if(m_outlineClone==null||m_outlineClone.Count<=0)
            {
                Profiler.BeginSample("SimpleTMOutline.OnCreated");
                m_outlineClone = new List<TextMesh>();

                for (int crateIndex = 0; crateIndex < 4; crateIndex++)
                {
                    GameObject go = new GameObject("outline_" + crateIndex);
                    go.layer = transform.gameObject.layer;
                    //go.hideFlags = HideFlags.HideAndDontSave;
                    go.transform.parent = transform;
                    go.transform.localPosition = Vector3.zero;
                    go.transform.localRotation = Quaternion.identity;
                    go.transform.localScale = Vector3.one;
                    if (m_renderer != null)
                    {
                        var mr = go.GetComponent<MeshRenderer>();
                        if (mr == null)
                            mr = go.AddComponent<MeshRenderer>();
                        mr.sharedMaterial = m_renderer.sharedMaterial;
                    }

                    if (m_sortGroup != null)
                    {
                        var sr = go.GetComponent<SortingGroup>();
                        if (sr == null)
                            sr = go.AddComponent<SortingGroup>();
                        sr.sortingLayerID = m_sortGroup.sortingLayerID;
                    }

                    if (m_textMesh != null)
                    {
                        var tm = go.GetComponent<TextMesh>();
                        if (tm == null)
                            tm = go.AddComponent<TextMesh>();

                        tm.font = m_textMesh.font;
                        tm.alignment = m_textMesh.alignment;
                        tm.offsetZ = m_textMesh.offsetZ;
                        tm.lineSpacing = m_textMesh.lineSpacing;
                        tm.tabSize = m_textMesh.tabSize;
                        tm.fontSize = m_textMesh.fontSize;
                        tm.fontStyle = m_textMesh.fontStyle;
                        tm.richText = m_textMesh.richText;
                        tm.characterSize = m_textMesh.characterSize;
                        tm.anchor = m_textMesh.anchor;

                        m_outlineClone.Add(tm);
                    }

                }

                Profiler.EndSample();
            }        
        }

        private void Dispose()
        {
            if (m_textMesh == null || m_outlineClone == null || m_outlineClone.Count <= 0)
                return;

            Profiler.BeginSample("SimpleTMOutline.Dispose");

            for (int tmpIndex = 0; tmpIndex < m_outlineClone.Count; tmpIndex++)
            {
                if (Application.isPlaying == false)
                {
                    DestroyImmediate(m_outlineClone[tmpIndex].gameObject);
                }
                else
                {
                    Destroy(m_outlineClone[tmpIndex].gameObject);
                }                
            }

            m_outlineClone.Clear();

            Profiler.EndSample();
        }

        void UpdateOutline()
        {
            if (m_textMesh==null||m_outlineClone == null || m_outlineClone.Count <= 0)
                return;

            Profiler.BeginSample("SimpleTMOutline.UpdateOutline");

            for (int tmpIndex = 0; tmpIndex < m_outlineClone.Count; tmpIndex++)
            {
                SetTransformOffset(m_outlineClone[tmpIndex].transform, tmpIndex);
                m_outlineClone[tmpIndex].color = m_outlineColor;
            }

            Profiler.EndSample();
        }

        void UpdateText()
        {
            if (m_textMesh == null || m_outlineClone == null || m_outlineClone.Count <= 0)
                return;

            if (m_textMesh.text == m_text)
                return;

            Profiler.BeginSample("SimpleTMOutline.UpdateText");

            m_text = m_textMesh.text;
            for (int tmpIndex = 0; tmpIndex < m_outlineClone.Count; tmpIndex++)
            {
                m_outlineClone[tmpIndex].text = m_text;
            }

            Profiler.EndSample();
        }

        void UpdateSortingOrder()
        {
            if (m_textMesh == null || m_outlineClone == null || m_outlineClone.Count <= 0)
                return;

            if (m_sortGroup!=null)
            {
                if (m_sortingOrder == m_sortGroup.sortingOrder)
                    return;

                Profiler.BeginSample("SimpleTMOutline.UpdateSortingOrder");

                m_sortingOrder = m_sortGroup.sortingOrder;
                for (int tmpIndex = 0; tmpIndex < m_outlineClone.Count; tmpIndex++)
                {
                    var sortGroup =  m_outlineClone[tmpIndex].GetComponent<SortingGroup>();
                    if(sortGroup!=null)
                    {
                        sortGroup.sortingOrder =  - 1;
                    }
                }

                Profiler.EndSample();
            }
            else if(m_renderer!=null)
            {
                if (m_sortingOrder == m_renderer.sortingOrder)
                    return;

                Profiler.BeginSample("SimpleTMOutline.UpdateSortingOrder");

                m_sortingOrder = m_renderer.sortingOrder;
                for (int tmpIndex = 0; tmpIndex < m_outlineClone.Count; tmpIndex++)
                {
                    var renderer = m_outlineClone[tmpIndex].gameObject.GetComponent<MeshRenderer>();
                    if (renderer != null)
                    {
                        renderer.sortingOrder = m_sortingOrder - 1;
                    }
                }

                Profiler.EndSample();
            }

          
        }
        #endregion

        #region unity loop
        private void Awake()
        {
            m_textMesh = GetComponent<TextMesh>();
            m_sortGroup = GetComponent<SortingGroup>();
            m_renderer = GetComponent<MeshRenderer>();
        }

        private void Start()
        {
            if (Application.isPlaying)
                return;

            OnCreated();

            UpdateSortingOrder();
            UpdateOutline();
        }

        private void OnEnable()
        {
            //if (Application.isPlaying)
                //return;

            if (m_textMesh == null || m_outlineClone == null || m_outlineClone.Count <= 0)
               return;

            UpdateOutline();
        }

        private void OnValidate()
        {
            UpdateOutline();
            UpdateSortingOrder();
            UpdateText();
        }

        private void Update()
        {
            if(Application.isPlaying==false)
            {
                UpdateSortingOrder();
                UpdateText();
            }
        }


        private void OnDisable()
        {
            //if (Application.isPlaying)
                //return;

            if (m_textMesh == null || m_outlineClone == null || m_outlineClone.Count <= 0)
                return;

            for (int tmpIndex = 0; tmpIndex < m_outlineClone.Count; tmpIndex++)
            {
               m_outlineClone[tmpIndex].transform.localPosition = new Vector3(-5000, 0, 0);
            }
        }

        private void OnDestroy()
        {
            if(Application.isPlaying==false)
            {
                Dispose();
            }
        }
        #endregion
    }

}
