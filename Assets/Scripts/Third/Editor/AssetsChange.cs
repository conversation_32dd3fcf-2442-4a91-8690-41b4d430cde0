using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
 
	public class AssetsChange : AssetPostprocessor
	{
        public static System.Action<string[]> OnChangeFile;
		

		private static void OnPostprocessAllAssets(string[] importedAssets, string[] deletedAssets, string[] movedAssets, string[] movedFromAssetPaths)
		{
             if(OnChangeFile!=null)
             {
                 OnChangeFile(importedAssets);
             }
		}
	} 
