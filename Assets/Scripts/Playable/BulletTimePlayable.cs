using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.Timeline;

public class BulletTimePlayable : PlayableBehaviour
{
	public float BulletTimeTimeScale;

	private float _originalTimeScale = 1f;

	public override void ProcessFrame(Playable playable, FrameData info, object playerData) 
	{
		//检查是否在播放，防止在短条前开始
		if (playable.GetTime() <= 0)
			return;
		Time.timeScale = Mathf.Lerp (_originalTimeScale, BulletTimeTimeScale, (float)(playable.GetTime() / playable.GetDuration()));
	}
	public override void OnBehaviourPlay(Playable playable, FrameData info)
	{
		_originalTimeScale = Time.timeScale;
	}
}