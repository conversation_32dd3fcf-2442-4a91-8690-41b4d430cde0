#if UNITY_EDITOR
using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

public class SpaceExplorationMapChildEditHelper : MonoBehaviour {

    [HideInInspector]
    public SpaceExplorationMapEditHelper helper;
    [HideInInspector]
    public int number;
    [HideInInspector]
    public int ID;

    public void Create(int id)
    {
        ID = id;
        helper.DrawPoint(number, id);
    }

    public void Modify(int id)
    {
        ID = id;
        helper.ModifyPoint(number, id);
    }

    public void Delete()
    {
        helper.DeletePoint(number);
    }

    public void Link(string info)
    {
        helper.DrawLine(info);
    }

    public void Revoke()
    {
        helper.Revoke();
    }
}
#endif