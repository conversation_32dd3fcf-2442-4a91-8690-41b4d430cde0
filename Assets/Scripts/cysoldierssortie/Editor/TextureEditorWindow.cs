using UnityEditor;
using UnityEngine;
using System.IO;
using System.Linq;
using System.Collections.Generic;
using Sirenix.Utilities;
public class TextureEditorWindow : EditorWindow
{
    private string folderPath = "Assets";
    private Texture2D[] textures;
    private bool enableMipmaps = false;
    private bool enableStreamingMipmaps = false;

    [MenuItem("SToolCollection/Tool/Texture Editor Window")]
    public static void ShowWindow()
    {
        GetWindow<TextureEditorWindow>("Texture Editor");
    }

    private void OnGUI()
    {
        GUILayout.Label("Texture Editor", EditorStyles.boldLabel);

        // Select folder
        GUILayout.Label("Folder Path", EditorStyles.label);
        folderPath = EditorGUILayout.TextField(folderPath);
        if (GUILayout.Button("Select Folder"))
        {
            folderPath = EditorUtility.OpenFolderPanel("Select Texture Folder", "Assets", "");
            if (!string.IsNullOrEmpty(folderPath))
            {
                folderPath = FileUtil.GetProjectRelativePath(folderPath);
            }
        }

        // Load textures button
        if (GUILayout.Button("Load Textures"))
        {
            LoadTextures();
        }

        // Display texture count
        if (textures != null)
        {
            GUILayout.Label($"Loaded Textures: {textures.Length}", EditorStyles.label);
        }

        // Toggle mipmaps
        enableMipmaps = EditorGUILayout.Toggle("Enable Mipmaps", enableMipmaps);

        // Toggle streaming mipmaps
        enableStreamingMipmaps = EditorGUILayout.Toggle("Enable Streaming Mipmaps", enableStreamingMipmaps);

        // Apply changes button
        if (GUILayout.Button("Apply Changes"))
        {
            ApplyChanges();
        }
    }

    private void LoadTextures()
    {
        if (string.IsNullOrEmpty(folderPath))
        {
            Debug.LogError("Folder path is empty. Please select a folder.");
            return;
        }

        string[] files = Directory.GetFiles(folderPath, "*.*", SearchOption.AllDirectories);
        List<string> m_texLst = new List<string>();
        for(int i = 0;i<files.Length;i++)
        {
            if (files[i].Contains(".png")|| files[i].Contains(".tga")|| files[i].Contains(".jpg"))
            {
                if (!files[i].Contains("meta"))
                {
                    files[i] = files[i].Replace("\\", "/");
                    m_texLst.Add(files[i]);
                }
            }
        }

        textures = new Texture2D[m_texLst.Count];
        for (int i =0;i<m_texLst.Count;i++)
        {
          var texture2D =   AssetDatabase.LoadAssetAtPath<Texture2D>(m_texLst[i]);
          textures[i] = texture2D;
        }

        Debug.Log($"Loaded {textures.Length} textures from {folderPath}.");
    }

    private void ApplyChanges()
    {
        if (textures == null || textures.Length == 0)
        {
            Debug.LogError("No textures loaded. Please load textures first.");
            return;
        }

        foreach (var texture in textures)
        {
            string path = AssetDatabase.GetAssetPath(texture);
            TextureImporter importer = AssetImporter.GetAtPath(path) as TextureImporter;

            if (importer != null)
            {
                importer.mipmapEnabled = enableMipmaps;
                importer.streamingMipmaps = enableStreamingMipmaps;

                EditorUtility.SetDirty(importer);
                importer.SaveAndReimport();
            }
        }

        Debug.Log("Changes applied to all loaded textures.");
    }
}
