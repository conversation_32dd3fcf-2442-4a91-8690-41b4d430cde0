using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;

public class EnemyGroupSorter : EditorWindow
{
    private string targetFolder = "Assets/Prefabs/Enemies";

    [MenuItem("SToolCollection/Tool/Enemy Group Sorter")]
    public static void ShowWindow()
    {
        GetWindow<EnemyGroupSorter>("Enemy Sorter");
    }

    void OnGUI()
    {
        GUILayout.Label("Enemy Group Sorting Settings", EditorStyles.boldLabel);
        targetFolder = EditorGUILayout.TextField("Target Folder", targetFolder);

        if (GUILayout.Button("Process Prefabs"))
        {
            ProcessAllPrefabs();
        }
    }

    void ProcessAllPrefabs()
    {
        string[] prefabGUIDs = AssetDatabase.FindAssets("t:Prefab", new[] { targetFolder });
        int total = prefabGUIDs.Length;

        for (int i = 0; i < total; i++)
        {
            string guid = prefabGUIDs[i];
            string path = AssetDatabase.GUIDToAssetPath(guid);

            EditorUtility.DisplayProgressBar("Processing Prefabs",
                $"Processing {Path.GetFileName(path)} ({i + 1}/{total})",
                (float)i / total);

            ProcessPrefab(path);
        }

        EditorUtility.ClearProgressBar();
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
    }

    void ProcessPrefab(string prefabPath)
    {
        // ����Ԥ����
        GameObject prefabRoot = PrefabUtility.LoadPrefabContents(prefabPath);

        // ����EnemyGroup
        Transform enemyGroup = FindDeepChild(prefabRoot.transform, "EnemyGroup");
        if (enemyGroup == null)
        {
            PrefabUtility.UnloadPrefabContents(prefabRoot);
            return;
        }

        // �ռ������岢����
        List<Transform> children = new List<Transform>();
        foreach (Transform child in enemyGroup)
        {
            children.Add(child);
        }

        // ��Z����������
        children.Sort((a, b) => a.localPosition.z.CompareTo(b.localPosition.z));

        // Ӧ����˳��
        for (int i = 0; i < children.Count; i++)
        {
            children[i].SetSiblingIndex(i);
        }

        // �����޸�
        PrefabUtility.SaveAsPrefabAsset(prefabRoot, prefabPath);
        PrefabUtility.UnloadPrefabContents(prefabRoot);
    }

    Transform FindDeepChild(Transform parent, string name)
    {
        if (parent.name == name) return parent;

        foreach (Transform child in parent)
        {
            Transform result = FindDeepChild(child, name);
            if (result != null) return result;
        }
        return null;
    }
}