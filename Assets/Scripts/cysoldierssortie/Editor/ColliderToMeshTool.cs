using UnityEditor;
using UnityEngine;

namespace cysoldierssortie.Editor
{
    public class ColliderToMeshTool : MonoBehaviour
    {
        [MenuItem("Tools/Collider To Mesh Converter")]
        static void ConvertCollidersToMeshes()
        {
            GameObject[] selectedObjects = Selection.gameObjects;

            if (selectedObjects.Length == 0)
            {
                Debug.LogWarning("请先在层级视图中选择一个父节点。");
                return;
            }

            foreach (GameObject root in selectedObjects)
            {
                // 遍历所有子节点，包括自己
                Transform[] allChildren = root.GetComponentsInChildren<Transform>(true);

                foreach (Transform child in allChildren)
                {
                    GameObject go = child.gameObject;

                    // 处理 BoxCollider
                    BoxCollider box = go.GetComponent<BoxCollider>();
                    if (box != null)
                    {
                        ReplaceWithPrimitive(go, PrimitiveType.Cube, box.size, box.center);
                        DestroyImmediate(box);
                        continue; // 避免一个物体有多个碰撞器时重复替换
                    }

                    // 处理 SphereCollider
                    SphereCollider sphere = go.GetComponent<SphereCollider>();
                    if (sphere != null)
                    {
                        Vector3 size = Vector3.one * sphere.radius * 2f; // Sphere直径
                        ReplaceWithPrimitive(go, PrimitiveType.Sphere, size, sphere.center);
                        DestroyImmediate(sphere);
                    }
                }
            }

            Debug.Log("转换完成！");
        }

        static void ReplaceWithPrimitive(GameObject target, PrimitiveType type, Vector3 size, Vector3 localCenter)
        {
            // 创建替代物
            GameObject primitive = GameObject.CreatePrimitive(type);
            primitive.transform.SetParent(target.transform, false);
            primitive.transform.localPosition = localCenter;
            primitive.transform.localRotation = Quaternion.identity;
            primitive.transform.localScale = size;

            // 删除多余的Collider，保留MeshRenderer+MeshFilter
            DestroyImmediate(primitive.GetComponent<Collider>());

            // 把primitive的MeshRenderer和MeshFilter搬到target上
            MeshRenderer mr = primitive.GetComponent<MeshRenderer>();
            MeshFilter mf = primitive.GetComponent<MeshFilter>();

            if (!target.GetComponent<MeshRenderer>())
                target.AddComponent<MeshRenderer>().sharedMaterial = mr.sharedMaterial;
            if (!target.GetComponent<MeshFilter>())
                target.AddComponent<MeshFilter>().sharedMesh = mf.sharedMesh;

            DestroyImmediate(primitive); // 清理临时物体
        }
    }
}