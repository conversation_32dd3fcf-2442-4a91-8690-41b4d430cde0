using UnityEngine;
using UnityEditor;
using System.Collections;
using System.IO;

public class ParticleBakerWindow : EditorWindow
{
    private Camera targetCamera;
    private int resolutionWidth = 1920;
    private int resolutionHeight = 1080;

    [MenuItem("SToolCollection/Tool/ParticleBakerWindow")]
    public static void ShowWindow()
    {
        GetWindow<ParticleBakerWindow>("Camera Capture");
    }

    void OnGUI()
    {
        GUILayout.Label("Camera Capture Settings", EditorStyles.boldLabel);
        targetCamera = (Camera)EditorGUILayout.ObjectField("Target Camera", targetCamera, typeof(Camera), true);

        resolutionWidth = EditorGUILayout.IntField("Resolution Width", resolutionWidth);
        resolutionHeight = EditorGUILayout.IntField("Resolution Height", resolutionHeight);

        if (GUILayout.Button("Capture TGA with Alpha"))
        {
            if (targetCamera == null)
            {
                EditorUtility.DisplayDialog("Error", "Please select a camera first!", "OK");
                return;
            }
            CaptureCameraView();
        }
    }

    void CaptureCameraView()
    {
        // ������ʱRenderTexture
        RenderTexture rt = new RenderTexture(resolutionWidth, resolutionHeight, 24, RenderTextureFormat.ARGB32);

        // ����ԭʼ�������
        RenderTexture originalRT = targetCamera.targetTexture;
        CameraClearFlags originalClearFlags = targetCamera.clearFlags;
        Color originalBackgroundColor = targetCamera.backgroundColor;

        try
        {
            // �����������
            targetCamera.targetTexture = rt;
            targetCamera.clearFlags = CameraClearFlags.SolidColor;
            targetCamera.backgroundColor = new Color(0, 0, 0, 0); // ͸������
            // ��Ⱦ��Texture
            targetCamera.Render();

            // ��ȡ��������
            Texture2D tex = new Texture2D(rt.width, rt.height, TextureFormat.ARGB32, false);
            RenderTexture.active = rt;
            tex.ReadPixels(new Rect(0, 0, rt.width, rt.height), 0, 0);
            tex.Apply();

            // ����ΪTGA
            SaveAsTGA(tex);
        }
        finally
        {
            // �ָ��������
            targetCamera.targetTexture = originalRT;
            targetCamera.clearFlags = originalClearFlags;
            targetCamera.backgroundColor = originalBackgroundColor;
            RenderTexture.active = null;
            rt.Release();
            DestroyImmediate(rt);
        }
    }

    void SaveAsTGA(Texture2D tex)
    {
        string path = EditorUtility.SaveFilePanel("Save TGA", "", "CameraCapture", "tga");
        if (string.IsNullOrEmpty(path)) return;

        byte[] bytes = EncodeTGA(tex);
        File.WriteAllBytes(path, bytes);
        AssetDatabase.Refresh();
    }

    byte[] EncodeTGA(Texture2D tex)
    {
        // TGA�ļ�ͷ��18�ֽڣ�
        byte[] header = new byte[18];
        header[2] = 2;  // δѹ��RGB��ʽ
        header[12] = (byte)(tex.width & 0xFF);
        header[13] = (byte)((tex.width >> 8) & 0xFF);
        header[14] = (byte)(tex.height & 0xFF);
        header[15] = (byte)((tex.height >> 8) & 0xFF);
        header[16] = 32; // 32λ���أ�RGBA��

        // �������ݣ�BGRA��ʽ��
        Color32[] pixels = tex.GetPixels32();
        byte[] pixelData = new byte[pixels.Length * 4];

        for (int i = 0; i < pixels.Length; i++)
        {
            pixelData[i * 4] = pixels[i].b;
            pixelData[i * 4 + 1] = pixels[i].g;
            pixelData[i * 4 + 2] = pixels[i].r;
            pixelData[i * 4 + 3] = pixels[i].a;
        }

        // �ϲ�ͷ�ļ�����������
        byte[] fileBytes = new byte[header.Length + pixelData.Length];
        System.Buffer.BlockCopy(header, 0, fileBytes, 0, header.Length);
        System.Buffer.BlockCopy(pixelData, 0, fileBytes, header.Length, pixelData.Length);

        return fileBytes;
    }
}