using CasualGame.lib_ChuagnYi;
using System;
using UnityEditor;
using System.Collections.Generic;
using bc.MiniGameBase;
using XLua;
using System.Diagnostics;
using UnityEngine;
using Debug = UnityEngine.Debug;
using War.Script;
using CasualGame.lib_ChuagnYi.NeeG;
using System.IO;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using DebuggingEssentials;
using Google.Android.AppBundle.Editor;
using static Cinemachine.DocumentationSortingAttribute;
using static War.UI.SubtitleData;
using UnityEditor.Experimental.SceneManagement;
using UnityEngine.Analytics;
using UI.UGUIExtend;
using UnityEngine.UI;
public class EditorLevelUtils
{

    [MenuItem("GameObject/SToolCollection/士兵突击/a生成当前关卡数据")]
    public static void CreateLevelData (GameObject selectGameObject = null)
    {
        var selectGo = selectGameObject;
        if (selectGameObject == null)
        {
            selectGo = PrefabStageUtility.GetCurrentPrefabStage().prefabContentsRoot;


        }
         
        if(selectGo==null)
        {
            EditorUtility.DisplayDialog("选择一个关卡", "你要选择一个关卡才能检查关卡ID", "好的");
            return;
        }

        
        var propsRoot = selectGo.transform.Find("Props");
        if(propsRoot==null)
        {
            EditorUtility.DisplayDialog("找不到关卡道具节点", "找不到当前关卡道具节点", "好的");
            return;
        }

        var enemyRoot = selectGo.transform.Find("EnemyGroup");
        if(enemyRoot==null)
        {
            EditorUtility.DisplayDialog("找不到关卡敌人节点", "找不到关卡敌人节点", "好的");
            return;
        }
        JObject levelData = new JObject();
        JArray propArr = new JArray();
        levelData["Props"] = propArr;
        

        //LEVEL133
        var curLevelName = selectGo.name;
        var levelNum =  curLevelName.Substring(5);

        var prop_child_count = propsRoot.childCount;
        for(int childIndex = 0;childIndex<prop_child_count;childIndex++)
        {
            var child = propsRoot.GetChild(childIndex);
            if(child.name!= "WorldCanvas")
            {
         
                    JObject propData = new JObject();
                    SetGameObjectSourceInfo(propData, child.gameObject);
                    propArr.Add(propData);
                    JObject luaDataObj = new JObject();
                var luaData = child.GetComponent<LuaData>();
                if (luaData)
                {
                    propData["luaData"] = luaDataObj;
                    var values = luaData.SerializedValues;
                    foreach (var value in values)
                    {
                        luaDataObj.Add(value.key, value.jsonStr);
                        if (value.key == "miniObstacleId")
                        {
                            var str = value.jsonStr;
                            if (str.Length < 4)
                            {
                                continue;
                            }

                            string tmp_level = "";
                            for (int strIndex = 0; strIndex < str.Length - 3; strIndex++)
                            {
                                tmp_level = tmp_level + str[strIndex];
                            }
                            var id = str.Substring(str.Length - 3);
                            if (!levelNum.Equals(tmp_level))
                            {
                                str = levelNum + id;
                                value.jsonStr = str;
                            }
                        }
                    }
                }
                
            }
        }
        JArray enemyGroupArr = new JArray();
        levelData["EnemyGroup"] = enemyGroupArr;

        var enemy_child_count = enemyRoot.childCount;
        for (int childIndex = 0; childIndex < enemy_child_count; childIndex++)
        {
            var child = enemyRoot.GetChild(childIndex);
                var luaData = child.GetComponent<LuaData>();
                if (luaData)
                {
                JObject enemyData = new JObject();
                JObject luaDataObj = new JObject();
                enemyData["luaData"] = luaDataObj;
                SetGameObjectSourceInfo(enemyData,child.gameObject);
                enemyGroupArr.Add(enemyData);
                var values = luaData.SerializedValues;
                    foreach (var value in values)
                    {
                    luaDataObj.Add(value.key, value.jsonStr);
                    if (value.key == "miniUnitId")
                        {
                            var str = value.jsonStr;
                            string[] IDs = str.Split('#');
                            string multipleNewUnitIds = "";
                            for(int unitIdIndex = 0;unitIdIndex< IDs.Length;unitIdIndex++)
                            {
                                var unitID =  IDs[unitIdIndex];
                                if(unitID.Length<4)
                                {
                                    continue;
                                }
                                var id = unitID.Substring(unitID.Length - 3);
                                var levelInt = (Int32.Parse(levelNum) - 8);
                                if(levelInt<0)
                                {
                                    continue;
                                }
                                unitID = levelInt.ToString() + id;
                                multipleNewUnitIds = multipleNewUnitIds + unitID;
                                if (unitIdIndex!=IDs.Length-1)
                                {
                                    multipleNewUnitIds += "#";
                                }
                            }
                            value.jsonStr = multipleNewUnitIds;
                        }
                    }
                }
            
        }
        string baseUrl= "cysoldierssortie/Data/Level/Level" + levelNum + ".json";
        string saveUrl = "Assets/"+ baseUrl;
        File.WriteAllText(saveUrl, levelData.ToString());
        AssetImporter importer = AssetImporter.GetAtPath(saveUrl);
        if (importer != null)
        {
            string newAssetBundleName = baseUrl.ToLower();
            importer.SetAssetBundleNameAndVariant(newAssetBundleName,null);
            importer.SaveAndReimport();
        }


        EditorUtility.DisplayDialog("保存完毕", "保存完毕当前关卡数据:" + levelNum + "", "OK");
    }
    private static void SetGameObjectSourceInfo(JObject data,GameObject child)
    {
        GameObject SourceObj = PrefabUtility.GetCorrespondingObjectFromSource(child);
        if (SourceObj != null)
        {
            string childUrl = AssetDatabase.GetAssetPath(SourceObj);
            string abName = AssetDatabase.GetImplicitAssetBundleName(childUrl);
            data["pathUrl"] = childUrl;
            data["abName"] = abName;
        }
        else
        {

            Debug.LogError("当前物件没打单独打预置=" + child.name);
        }


        data["childNmae"] = child.name;
        data["posX"] = child.transform.localPosition.x;
        data["posY"] = child.transform.localPosition.y;
        data["posZ"] = child.transform.localPosition.z;

        data["rX"] = child.transform.localEulerAngles.x;
        data["rY"] = child.transform.localEulerAngles.y;
        data["rZ"] = child.transform.localEulerAngles.z;

        data["scaleX"] = child.transform.localScale.x;
        data["scaleY"] = child.transform.localScale.y;
        data["scaleZ"] = child.transform.localScale.z;
    }

    [MenuItem("GameObject/SToolCollection/士兵突击/打印当前关卡信息")]
    public static void PrintCurLevelInfo()
    {
        if(NeeGame.Instance!=null)
        {
            var curLevel = NeeGame.Instance.curLevel;
            var debugEnemyFunc =   curLevel.GetComponent<LuaMono>().luaComp.GetInPath<LuaFunction>("DebugEnemyLevelInfo");
            object[] results =  debugEnemyFunc?.Call(curLevel.GetComponent<LuaMono>().luaComp);
            string debugEnemyLevelInfo = results[0].ToString();
            Debug.LogError(debugEnemyLevelInfo);

            var debugPropFunc = curLevel.GetComponent<LuaMono>().luaComp.GetInPath<LuaFunction>("DebugPropLevelInfo");
            results = debugPropFunc?.Call(curLevel.GetComponent<LuaMono>().luaComp);
            string debugPropLevelInfo = results[0].ToString();
            Debug.LogError(debugPropLevelInfo);

            string enemyFilePath = EditorUtility.SaveFilePanel("Save Enemy Path", "", "CurLevelEnemyInfo", "txt");
            if (!string.IsNullOrEmpty(enemyFilePath))
            {
                File.WriteAllText(enemyFilePath, debugEnemyLevelInfo);
            }

            string propFilePath = EditorUtility.SaveFilePanel("Save Prop Path", "", "CurLevelPropInfo", "txt");
            if (!string.IsNullOrEmpty(propFilePath))
            {
                File.WriteAllText(propFilePath, debugPropLevelInfo);
            }

        }
    }


    [MenuItem("GameObject/SToolCollection/士兵突击/检查当前关卡单位ID")]
    public static void CheckUnitID()
    {
        var selectGo = Selection.activeGameObject;
        if (selectGo == null)
        {
            EditorUtility.DisplayDialog("选择一个关卡", "你要选择一个关卡才能检查关卡ID", "好的");
            return;
        }

        var propsRoot = selectGo.transform.Find("Props");
        if (propsRoot == null)
        {
            EditorUtility.DisplayDialog("找不到关卡道具节点", "找不到当前关卡道具节点", "好的");
            return;
        }

        var enemyRoot = selectGo.transform.Find("EnemyGroup");
        if (enemyRoot == null)
        {
            EditorUtility.DisplayDialog("找不到关卡敌人节点", "找不到关卡敌人节点", "好的");
            return;
        }
        //LEVEL133
        var curLevelName = selectGo.name;
        var levelNum = curLevelName.Substring(5);
        JsonObjectAttribute jsonArrayAttribute = new JsonObjectAttribute();
  
        var prop_child_count = propsRoot.childCount;
        for (int childIndex = 0; childIndex < prop_child_count; childIndex++)
        {
            var child = propsRoot.GetChild(childIndex);
            if (child.name.Contains("barrel") || child.name.Contains("propsDoor"))
            {
                var luaData = child.GetComponent<LuaData>();
                if (luaData)
                {
                    var values = luaData.SerializedValues;
                    foreach (var value in values)
                    {
                        if (value.key == "miniObstacleId")
                        {
                            var str = value.jsonStr;
                            if (str.Length < 4)
                            {
                                continue;
                            }

                            string tmp_level = "";
                            for (int strIndex = 0; strIndex < str.Length - 3; strIndex++)
                            {
                                tmp_level = tmp_level + str[strIndex];
                            }
                            var id = str.Substring(str.Length - 3);
                            if (!levelNum.Equals(tmp_level))
                            {
                                str = levelNum + id;
                                value.jsonStr = str;
                            }
                        }
                    }
                }
            }
        }

        var enemy_child_count = enemyRoot.childCount;
        for (int childIndex = 0; childIndex < enemy_child_count; childIndex++)
        {
            var child = enemyRoot.GetChild(childIndex);
            if (child.name.Contains("spawnPoint"))
            {
                var luaData = child.GetComponent<LuaData>();
                if (luaData)
                {
                    var values = luaData.SerializedValues;
                    foreach (var value in values)
                    {
                        if (value.key == "miniUnitId")
                        {
                            var str = value.jsonStr;
                            string[] IDs = str.Split('#');
                            string multipleNewUnitIds = "";
                            for (int unitIdIndex = 0; unitIdIndex < IDs.Length; unitIdIndex++)
                            {
                                var unitID = IDs[unitIdIndex];
                                if (unitID.Length < 4)
                                {
                                    continue;
                                }
                                var id = unitID.Substring(unitID.Length - 3);
                                var levelInt = (Int32.Parse(levelNum) - 8);
                                if (levelInt < 0)
                                {
                                    continue;
                                }
                                unitID = levelInt.ToString() + id;
                                multipleNewUnitIds = multipleNewUnitIds + unitID;
                                if (unitIdIndex != IDs.Length - 1)
                                {
                                    multipleNewUnitIds += "#";
                                }
                            }
                            value.jsonStr = multipleNewUnitIds;
                        }
                    }
                }
            }
        }

        EditorUtility.DisplayDialog("检查完毕", "检查完毕当前关卡:" + levelNum + " 传送门+滚桶一律使用关卡ID+编号，敌人一律使用(关卡ID-8) + 编号", "OK");
    }

    [InitializeOnLoadMethod]
    public static void Init()
    {
        //打开Prefab事件
      //  PrefabStage.prefabStageOpened += OnPrefabStageOpened;
        //关闭Prefab事件
        PrefabStage.prefabStageClosing += OnPrefabStageClosing;
    }

    private static void OnPrefabStageOpened(PrefabStage prefabStage)
    {

        //if(prefabStage.prefabAssetPath.StartsWith("Assets/···"))
    }
   
    private static void OnPrefabStageClosing(PrefabStage prefabStage)
    {
        string baseUrl = "Assets/cysoldierssortie/Prefab/Level/Level";
        string troopClashBaseUrl = "Assets/cysoldierssortie/TroopClash/Editor/Level";
        string kingShootBaseUrl = "Assets/cysoldierssortie/KingShot/Editor/Level";
        
        var assetPath = prefabStage.assetPath;
        if (assetPath.StartsWith(baseUrl))
        {
            Debug.Log("aaaaa");
            GameObject prefabRoot = prefabStage.prefabContentsRoot;
            if (prefabRoot == null)
                return;
            CreateLevelData(prefabRoot);
        }
        else if (assetPath.StartsWith(troopClashBaseUrl))
        {
            //小兵大作战
            Debug.Log("小兵大作战：CreateLevelData_TroopClash");
            GameObject prefabRoot = prefabStage.prefabContentsRoot;
            if (prefabRoot == null)
                return;
            CreateLevelData_TroopClash(prefabRoot);
        }
        else if (assetPath.StartsWith(kingShootBaseUrl))
        {
            Debug.Log("CreateLevelData_KingShot");
            GameObject prefabRoot = prefabStage.prefabContentsRoot;
            if (prefabRoot == null)
                return;
            CreateLevelData_KingShot(prefabRoot);
        }
    }

    public static void CreateLevelData_TroopClash(GameObject selectGameObject)
    {
        var selectGo = selectGameObject;
        if (selectGameObject == null)
        {
            selectGo = PrefabStageUtility.GetCurrentPrefabStage().prefabContentsRoot;
        }

        if (selectGo == null)
        {
            EditorUtility.DisplayDialog("选择一个关卡", "你要选择一个关卡才能检查关卡ID", "好的");
            return;
        }

        var propsRoot = selectGo.transform.Find("Props");
        if (propsRoot == null)
        {
            EditorUtility.DisplayDialog("找不到关卡道具节点", "找不到当前关卡道具节点", "好的");
            return;
        }

        var enemyRoot = selectGo.transform.Find("EnemyGroup");
        if (enemyRoot == null)
        {
            EditorUtility.DisplayDialog("找不到关卡敌人节点", "找不到关卡敌人节点", "好的");
            return;
        }

        var playerRoot = selectGo.transform.Find("Player");
        if (playerRoot == null)
        {
            EditorUtility.DisplayDialog("找不到关卡玩家出生点", "找不到关卡玩家出生点", "好的");
            return;
        }

        char splitChar = '_';
        //LEVEL133
        var curLevelName = selectGo.name;
        var levelNum = curLevelName.Substring(5);

        JObject levelData = new JObject();
        JObject playerData = new JObject();
        levelData["Player"] = playerData;
        JObject playerPos = new JObject();
        playerData["Pos"] = playerPos;
        SetGameObjectSourceInfo_TroopClash(playerPos, playerRoot.gameObject);

        JArray propArr = new JArray();
        levelData["Props"] = propArr;
        var prop_child_count = propsRoot.childCount;
        for (int childIndex = 0; childIndex < prop_child_count; childIndex++)
        {
            var child = propsRoot.GetChild(childIndex);
            var objName = child.name;
            var splitStr = objName.Split(splitChar);
            if (splitStr != null && splitStr.Length >= 2)
            {
                var typeFlag = -1;
                if (splitStr[1] == "Pos")
                {
                    typeFlag = 0;
                }
                else if (splitStr[1] == "Timer")
                {
                    typeFlag = 1;
                }
                if (typeFlag >= 0)
                {
                    JObject propData = new JObject();
                    propArr.Add(propData);
                    propData.Add("PropID", int.Parse(splitStr[0]));
                    propData.Add("Type", splitStr[1]);
                    if (typeFlag == 0)
                    {
                        JObject posObj = new JObject();
                        propData["Pos"] = posObj;
                        SetGameObjectSourceInfo_TroopClash(posObj, child.gameObject);
                    }
                    else if (typeFlag == 1)
                    {
                        propData.Add("Delay", int.Parse(splitStr[2]));
                    }
                }
            }
        }
        JArray enemyGroupArr = new JArray();
        levelData["EnemyGroup"] = enemyGroupArr;
        var enemy_child_count = enemyRoot.childCount;
        for (int childIndex = 0; childIndex < enemy_child_count; childIndex++)
        {
            var child = enemyRoot.GetChild(childIndex);
            var objName = child.name;
            var splitStr = objName.Split(splitChar);
            if (splitStr != null && splitStr.Length >= 2)
            {
                var typeFlag = -1;
                if (splitStr[1] == "Pos")
                {
                    typeFlag = 0;
                }
                else if (splitStr[1] == "Timer")
                {
                    typeFlag = 1;
                }
                if (typeFlag >= 0)
                {
                    JObject enemyData = new JObject();
                    enemyGroupArr.Add(enemyData);
                    enemyData.Add("TeamID", int.Parse(splitStr[0]));
                    enemyData.Add("Type", splitStr[1]);
                    if (typeFlag == 0)
                    {
                        JObject posObj = new JObject();
                        enemyData["Pos"] = posObj;
                        SetGameObjectSourceInfo_TroopClash(posObj, child.gameObject);
                    }
                    else if (typeFlag == 1)
                    {
                        enemyData.Add("Delay", int.Parse(splitStr[2]));
                    }
                }
            }
        }

        string baseUrl = $"cysoldierssortie/troopclash/data/level/level{levelNum}";
        string saveUrl = $"Assets/{baseUrl}.json";
        File.WriteAllText(saveUrl, levelData.ToString());
        AssetDatabase.Refresh();
        AssetImporter importer = AssetImporter.GetAtPath(saveUrl);
        if (importer != null)
        {
            string newAssetBundleName = $"{baseUrl.ToLower()}.data";
            importer.SetAssetBundleNameAndVariant(newAssetBundleName, null);
            importer.SaveAndReimport();
        }
        EditorUtility.DisplayDialog("保存完毕", "保存完毕当前关卡数据:" + levelNum + "", "OK");
    }

    private static void SetGameObjectSourceInfo_TroopClash(JObject data, GameObject child)
    {
        data["x"] = child.transform.localPosition.x;
        data["y"] = child.transform.localPosition.y;
        data["z"] = child.transform.localPosition.z;
    }
    
    public static void CreateLevelData_KingShot(GameObject selectGameObject,bool isTips = true)
    {
        var selectGo = selectGameObject;
        if (selectGameObject == null)
        {
            selectGo = PrefabStageUtility.GetCurrentPrefabStage().prefabContentsRoot;
        }
        if (selectGo == null)
        {
            EditorUtility.DisplayDialog("选择一个关卡", "你要选择一个关卡才能检查关卡ID", "好的");
            return;
        }
        var buildingsRoot = selectGo.transform.Find("Buildings");
        if (buildingsRoot == null)
        {
            EditorUtility.DisplayDialog("找不到关卡建筑节点", "找不到关卡建筑节点", "好的");
            return;
        }
        var enemyRoot = selectGo.transform.Find("EnemyBirth");
        if (enemyRoot == null)
        {
            EditorUtility.DisplayDialog("找不到关卡敌人出生节点", "找不到关卡敌人节点", "好的");
            return;
        }

        var playerRoot = selectGo.transform.Find("Player");
        if (playerRoot == null)
        {
            EditorUtility.DisplayDialog("找不到关卡玩家出生点", "找不到关卡玩家出生点", "好的");
            return;
        }

        char splitChar = '_';
        //LEVEL133
        var curLevelName = selectGo.name;
        var levelNum = curLevelName.Substring(5);

        JObject levelData = new JObject();
        JObject playerData = new JObject();
        levelData["Player"] = playerData;
        JObject playerPos = new JObject();
        playerData["Pos"] = playerPos;
        SetGameObjectSourceInfo_TroopClash(playerPos, playerRoot.gameObject);

        JArray propArr = new JArray();
        levelData["Props"] = propArr;
        
        JArray buildingArr = new JArray();
        levelData["Buildings"] = buildingArr;
        var building_child_count = buildingsRoot.childCount;
        for (int childIndex = 0; childIndex < building_child_count; childIndex++)
        {
            var child = buildingsRoot.GetChild(childIndex);
            var objName = child.name;
            var splitStr = objName.Split(splitChar);
            if (splitStr != null && splitStr.Length >= 2)
            {
                var typeFlag = -1;
                if (splitStr[1] == "Pos")
                {
                    typeFlag = 0;
                }
                else if (splitStr[1] == "Timer")
                {
                    typeFlag = 1;
                }
                else if (splitStr[1] == "Skill")
                {
                    typeFlag = 2;
                }
                if (typeFlag >= 0)
                {
                    JObject buildingData = new JObject();
                    buildingArr.Add(buildingData);
                    buildingData.Add("BuildingID", int.Parse(splitStr[0]));
                    buildingData.Add("Type", splitStr[1]);
                    buildingData.Add("SkillUid",  int.Parse(splitStr[2]));
                    if (typeFlag == 0)
                    {
                        JObject posObj = new JObject();
                        buildingData["Pos"] = posObj;
                        SetGameObjectSourceInfo_TroopClash(posObj, child.gameObject);
                    }
                }
            }
        }
        
        JArray enemyGroupArr = new JArray();
        levelData["EnemyBirth"] = enemyGroupArr;
        var enemy_child_count = enemyRoot.childCount;
        for (int childIndex = 0; childIndex < enemy_child_count; childIndex++)
        {
            var child = enemyRoot.GetChild(childIndex);
            var objName = child.name;
            var splitStr = objName.Split(splitChar);
            if (splitStr != null && splitStr.Length >= 2)
            {
                var typeFlag = -1;
                if (splitStr[1] == "Pos")
                {
                    typeFlag = 0;
                }
                else if (splitStr[1] == "Timer")
                {
                    typeFlag = 1;
                }
                if (typeFlag >= 0)
                {
                    JObject enemyData = new JObject();
                    enemyGroupArr.Add(enemyData);
                    enemyData.Add("TeamID", int.Parse(splitStr[0]));
                    enemyData.Add("Type", splitStr[1]);
                    JObject posObj = new JObject();
                    enemyData["Pos"] = posObj;
                    SetGameObjectSourceInfo_TroopClash(posObj, child.gameObject);
                    if (typeFlag == 1)
                    {
                        enemyData.Add("Delay", int.Parse(splitStr[2]));
                    }
                    if (splitStr.Length >= 4)
                    {
                        enemyData.Add("Path", splitStr[3]);
                    }
                }
            }
        }
        
        var enemyBaseRoot = selectGo.transform.Find("EnemyBase");
        if (enemyBaseRoot != null)
        {
            JArray enemyBaseArr = new JArray();
            levelData["EnemyBase"] = enemyBaseArr;
            var enemy_base_count = enemyBaseRoot.childCount;
            for (int childIndex = 0; childIndex < enemy_base_count; childIndex++)
            {
                var child = enemyBaseRoot.GetChild(childIndex);
                var objName = child.name;
                var splitStr = objName.Split(splitChar);
                if (splitStr != null)
                {
                    JObject enemyBaseData = new JObject();
                    enemyBaseArr.Add(enemyBaseData);
                    enemyBaseData.Add("ID", objName);
                    JObject posObj = new JObject();
                    enemyBaseData["Pos"] = posObj;
                    SetGameObjectSourceInfo_TroopClash(posObj, child.gameObject);
                }
            } 
        }
        
        var pathRoot = selectGo.transform.Find("EnemyPath");
        if (pathRoot != null)
        {
            JArray enemyBaseArr = new JArray();
            levelData["EnemyPath"] = enemyBaseArr;
            var enemy_base_count = pathRoot.childCount;
            for (int childIndex = 0; childIndex < enemy_base_count; childIndex++)
            {
                var child = pathRoot.GetChild(childIndex);
                var objName = child.name;
                var splitStr = objName.Split(splitChar);
                JObject pathData = new JObject();
                enemyBaseArr.Add(pathData);
                pathData.Add("ID", objName);
                JObject posObj = new JObject();
                pathData["Pos"] = posObj;
                SetGameObjectSourceInfo_TroopClash(posObj, child.gameObject);
            } 
        }
        
        var soldierRoot = selectGo.transform.Find("SoldierBirth");
        if (soldierRoot != null)
        {
            JArray SoldierBirthArr = new JArray();
            levelData["SoldierBirth"] = SoldierBirthArr;
            var SoldierBirth_count = soldierRoot.childCount;
            for (int childIndex = 0; childIndex < SoldierBirth_count; childIndex++)
            {
                var child = soldierRoot.GetChild(childIndex);
                var objName = child.name;
                var splitStr = objName.Split(splitChar);
                if (splitStr != null && splitStr.Length >= 2)
                {
                    JObject data = new JObject();
                    SoldierBirthArr.Add(data);
                    data.Add("ID", int.Parse(splitStr[0]));
                    var splitStr2 = splitStr[1];
                    //如果splitStr2是数字 就转成数字 如果是Pos就转成Pos
                    if (splitStr2 == "Pos")
                    {
                        data.Add("Skill", 0);
                    }
                    else
                    {
                        data.Add("Skill", int.Parse(splitStr[1]));
                    }

                    if ( splitStr.Length > 2)
                    {
                        data.Add("Uid", int.Parse(splitStr[2]));
                    }
                 
                    JObject posObj = new JObject();
                    data["Pos"] = posObj;
                    SetGameObjectSourceInfo_TroopClash(posObj, child.gameObject);
                }
            } 
        }
        

        string baseUrl = $"cysoldierssortie/kingshot/data/level/level{levelNum}";
        string saveUrl = $"Assets/{baseUrl}.json";
        File.WriteAllText(saveUrl, levelData.ToString());
        AssetDatabase.Refresh();
        AssetImporter importer = AssetImporter.GetAtPath(saveUrl);
        if (importer != null)
        {
            string newAssetBundleName = $"{baseUrl.ToLower()}.data";
            importer.SetAssetBundleNameAndVariant(newAssetBundleName, null);
            importer.SaveAndReimport();
        }

        if (isTips)
        {
            EditorUtility.DisplayDialog("保存完毕", "保存完毕当前kingshot关卡数据:" + levelNum + "", "OK");
        }
    }

    private static void SetGameObjectSourceInfo_KingShot(JObject data, GameObject child)
    {
        data["x"] = child.transform.localPosition.x;
        data["y"] = child.transform.localPosition.y;
        data["z"] = child.transform.localPosition.z;
    }
}