using UnityEngine;
using War.Script;
using XLua;

public class SceneLoadMgr
{
    //public const string LOBBY_SCENE_ABNAME = "scene/lobby.unity";
    
    public static void LoadLevelAsync(string assetBundleName, string levelName, bool isAdditive = false, LuaFunction callback = null)
    {
        if (Launch.LaunchMgr.Ins != null)
        {
            Launch.LaunchMgr.Ins.SceneMgr.LoadSceneAsync(assetBundleName, levelName, isAdditive, () =>
            {
                if (callback != null)
                {
                    callback.Action();
                    callback.Dispose();
                }
            });
        }
        else
        {
            IOSystem.LoadLevelAsync(assetBundleName, levelName, isAdditive, callback);
        }
    }
    
    public static void UnloadAssetBundle(string assetBundleName, bool unloadAllLoadedObjects = false)
    {
        if (Launch.LaunchMgr.Ins != null)
        {
            //todo
        }
        else
        {
            IOSystem.UnloadAssetBundle(assetBundleName, unloadAllLoadedObjects);
        }
    }
}
