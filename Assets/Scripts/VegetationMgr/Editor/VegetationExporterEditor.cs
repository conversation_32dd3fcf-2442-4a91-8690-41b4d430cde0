
using System.IO;
using UnityEditor;
using UnityEngine;
using UnityEngine.WSA;

namespace Vegetation
{

    public class VegetationExporterWindow:EditorWindow
    {
        #region const
        private const string FOLD_PATH = "VEGETATION_EXPORTER_FOLD_PATH";
        private const string DATA_PATH = "DATA_PATH";
        #endregion

        #region private
        private Object m_folder;
        private VegetationExporter m_exporter;
        private string m_savePath;
        #endregion

        #region unity loop
        private void OnEnable()
        {
          
            m_exporter = FindObjectOfType<VegetationExporter>();
            if(m_exporter==null)
            {
                GameObject exporterGo = new GameObject("Vegetation Exporter");
                m_exporter = exporterGo.AddComponent<VegetationExporter>();
            }
            LoadPrefsSetting();
        }

        private void OnDisable()
        {
            if(m_exporter)
            {
                m_exporter.OnWindowCloseCallBack();
                DestroyImmediate(m_exporter.gameObject);
            }
        }
        #endregion

        #region gui loop
        [MenuItem("SToolCollection/Tool/Vegetation Data Exporter Window")]
        static void CreateVegetationExporterWindow()
        {
            GetWindow<VegetationExporterWindow>("Vegetation Exporter Window");
        }
        public  void OnGUI()
        {
            if (m_exporter.ExporterData == null)
            {
                FoldPathSetting();

                if (GUILayout.Button("Creater Exporter Data"))
                {
                    CreateExporterData();
                }
            }
            else
            {
                DrawProperty();

                if (GUILayout.Button("Exporter Instance Data"))
                {
                    m_exporter.Exporter();
                }
            }
        }
        #endregion

        #region private method
        private void DrawProperty()
        {
            EditorGUILayout.LabelField("Exporter Data");
            m_exporter.ExporterData = (VegetationExporterData)EditorGUILayout.ObjectField("Exporter Data", m_exporter.ExporterData, typeof(VegetationExporterData), true);

            GUILayout.Space(10);

            EditorGUILayout.LabelField("Debug");
            m_exporter.DebugView = EditorGUILayout.Toggle("Debug View", m_exporter.DebugView);
            EditorGUI.BeginChangeCheck();
            m_exporter.P_DebugLOD = (DebugLOD)EditorGUILayout.EnumPopup("Debug LOD", m_exporter.P_DebugLOD);

            GUILayout.Space(10);

            if (EditorGUI.EndChangeCheck())
            {
                m_exporter.DebugLODChange(m_exporter.P_DebugLOD);
            }
        }
        private void FoldPathSetting()
        {
            EditorGUILayout.LabelField("Save Vegetation Exporter Data Path (Specific path of folder)");
            EditorGUI.BeginChangeCheck();
            m_folder = EditorGUILayout.ObjectField(m_folder, typeof(Object), false);
            if (EditorGUI.EndChangeCheck())
            {
                if (m_folder)
                {
                    var id = m_folder.GetInstanceID();
                    m_savePath = AssetDatabase.GetAssetPath(id) + "/";
                    m_exporter.FolderPath = m_savePath;
                    EditorPrefs.SetString(FOLD_PATH, AssetDatabase.GetAssetPath(id));
                }
            }
        }

        private void CreateExporterData()
        {
            string dataPath = m_exporter.FolderPath + "VegetationExporterData.asset";
            if (m_exporter.ExporterData == null)
            {
                m_exporter.ExporterData = ScriptableObject.CreateInstance<VegetationExporterData>();
            }
            AssetDatabase.CreateAsset(m_exporter.ExporterData, dataPath);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            EditorPrefs.SetString(DATA_PATH, dataPath);
            m_exporter.ExporterData = AssetDatabase.LoadAssetAtPath<VegetationExporterData>(dataPath);
        }

        void LoadDefaultData()
        {
            string defaultDataPath = EditorPrefs.GetString(DATA_PATH, "Assets/Scripts/Q1Engine/VegetationMgr/Profile/VegetationExporterData.asset");
            var exporterData = AssetDatabase.LoadAssetAtPath<VegetationExporterData>(defaultDataPath);
            if (exporterData != null)
            {
                m_exporter.ExporterData = exporterData;
            }
        }

        void LoadPrefsSetting()
        {
            m_savePath = EditorPrefs.GetString(FOLD_PATH, "");
            if (!string.IsNullOrEmpty(m_savePath))
            {
                m_exporter.FolderPath = m_savePath + "/";
                m_folder = AssetDatabase.LoadAssetAtPath<Object>(m_savePath);
            }

            LoadDefaultData();
        }
        #endregion
    }

    [CustomEditor(typeof(VegetationExporter))]
    public class VegetationExporterEditor : Editor
    {
        #region const
        private const string FOLD_PATH = "VEGETATION_EXPORTER_FOLD_PATH";
        private const string DATA_PATH = "DATA_PATH";
        #endregion

        #region private
        private Object m_folder;
        private VegetationExporter m_exporter;
        private string m_savePath;
        #endregion

        #region unity loop
        private void OnEnable()
        {
            m_exporter = (VegetationExporter)target;
            LoadPrefsSetting();
        }
        #endregion

        #region gui loop
        public override void OnInspectorGUI()
        {
            base.OnInspectorGUI();
            if(m_exporter.ExporterData == null)
            {
                FoldPathSetting();

                if(GUILayout.Button("Creater Exporter Data"))
                {
                    CreateExporterData();
                }
            }
            else
            {
                DrawProperty();

                if(GUILayout.Button("Exporter Instance Data"))
                {
                    m_exporter.Exporter();
                }
            }
        }
        #endregion

        #region private method
        private void DrawProperty()
        {
            EditorGUILayout.LabelField("Exporter Data");
            m_exporter.ExporterData = (VegetationExporterData) EditorGUILayout.ObjectField("Exporter Data",m_exporter.ExporterData,typeof(VegetationExporterData),true);

            GUILayout.Space(10);

            EditorGUILayout.LabelField("Debug");
            m_exporter.DebugView = EditorGUILayout.Toggle("Debug View", m_exporter.DebugView);
            EditorGUI.BeginChangeCheck();
            m_exporter.P_DebugLOD = (DebugLOD)EditorGUILayout.EnumPopup("Debug LOD", m_exporter.P_DebugLOD);

            GUILayout.Space(10);

            if (EditorGUI.EndChangeCheck())
            {
                m_exporter.DebugLODChange(m_exporter.P_DebugLOD);
            }
        }
        private void FoldPathSetting()
        {
            EditorGUILayout.LabelField("Save Vegetation Exporter Data Path (Specific path of folder)");
            EditorGUI.BeginChangeCheck();
            m_folder = EditorGUILayout.ObjectField(m_folder, typeof(Object), false);
            if (EditorGUI.EndChangeCheck())
            {
                if (m_folder)
                {
                    var id = m_folder.GetInstanceID();
                    m_savePath = AssetDatabase.GetAssetPath(id) + "/";
                    m_exporter.FolderPath = m_savePath;
                    EditorPrefs.SetString(FOLD_PATH, AssetDatabase.GetAssetPath(id));
                }
            }
        }

        private void CreateExporterData()
        {
            string dataPath = m_exporter.FolderPath + "VegetationExporterData.asset";
            if(!File.Exists(dataPath))
            {
                if (m_exporter.ExporterData == null)
                {
                    m_exporter.ExporterData = ScriptableObject.CreateInstance<VegetationExporterData>();
                }
                AssetDatabase.CreateAsset(m_exporter.ExporterData, dataPath);
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
            }

            EditorPrefs.SetString(DATA_PATH, dataPath);
            m_exporter.ExporterData = AssetDatabase.LoadAssetAtPath<VegetationExporterData>(dataPath);
        }

        void LoadDefaultData()
        {
            string defaultDataPath = EditorPrefs.GetString(DATA_PATH, "Assets/Art/GreatWorld/Sand/Res/VegetationData/VegetationExporterData.asset");
            var exporterData = AssetDatabase.LoadAssetAtPath<VegetationExporterData>(defaultDataPath);
            if(exporterData !=null)
            {
                m_exporter.ExporterData = exporterData;
            }
        }

        void LoadPrefsSetting()
        {
            m_savePath = EditorPrefs.GetString(FOLD_PATH, "");
            if(!string.IsNullOrEmpty(m_savePath))
            {
                m_exporter.FolderPath = m_savePath + "/";
                m_folder = AssetDatabase.LoadAssetAtPath<Object>(m_savePath);
            }

            LoadDefaultData();
        }
        #endregion

    }

}
