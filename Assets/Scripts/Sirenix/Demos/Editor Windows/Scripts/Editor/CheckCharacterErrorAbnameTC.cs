#if UNITY_EDITOR
using Sirenix.OdinInspector;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;
using War.Base;

namespace Assets.Scripts.Sirenix.Demos.Editor_Windows.Scripts.Editor.ToolObj
{
    [System.Serializable] 
    public class CheckCharacterErrorAbnameTC
    {

        [HideInInspector]
        public const string TIPS = "CheckCharacterErrorAbnameTC";
        public List<string> listError = new List<string>();

        [Button]
        void Check()
        {
            listError.Clear();
            var path = @"Assets\Animations\Characters";
            var guids = AssetDatabase.FindAssets("t:prefab", new string[] { path });

            foreach (var g in guids)
            {
                var apath = AssetDatabase.GUIDToAssetPath(g);
                if (Path.GetFileName(apath).StartsWith("edit_")) continue;
                var ai = AssetImporter.GetAtPath(apath);
                if (ai)
                {
                    if (!string.IsNullOrEmpty(ai.assetBundleName))
                    {
                        listError.Add(apath);
                    }
                }
            }
            "".PrintError("these paths should not own abnames !!!!!", UIHelper.ToJson(listError));
        }

        [Button]
        void FixAll()
        {
            var list = new List<string>();

            foreach (var apath in listError)
            {

                var ai = AssetImporter.GetAtPath(apath);
                if (ai)
                {
                    ai.assetBundleName = null;
                    list.Add(apath);
                }
            }
            UIHelper.ImportAssets(list.ToArray());
            "".PrintError(UIHelper.ToJson(list));
        }
    }
}

#endif