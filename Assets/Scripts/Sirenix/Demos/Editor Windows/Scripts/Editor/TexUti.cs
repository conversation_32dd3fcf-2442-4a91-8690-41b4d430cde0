using Sirenix.Utilities;
using System.IO;
using UnityEditor;
using UnityEngine;
using War.Base;
namespace War.Base
{
    public class TexUti : Singleton<TexUti>
    {
        public  void Exe()
        {
            var ob = Selection.objects;
            if (ob == null) return;
            foreach (var o in ob)
            {
                FixTex(o);
            }
        }

        private static void FixTex(Object ob)
        {
            var path = AssetDatabase.GetAssetPath(ob);
            if (ob is Texture2D tex)
            {
                var p32 = tex.GetPixels32();

                float h, s, v;

                var t321 = new Color32[p32.Length];
                var t322 = new Color32[p32.Length];
                var t323 = new Color32[p32.Length];
                var t324 = new Color32[p32.Length];
                for (int i = 0; i < p32.Length; i++)
                {
                    Color32 color = p32[i];
                    var c = (Color32)color;
                    Color.RGBToHSV(c, out h, out s, out v);
                    t321[i] = new Color() { r = h };
                    t322[i] = new Color() { r = s };
                    t323[i] = new Color() { r = v };
                    t324[i] = new Color() { r = c.a };

                    FillColor(ref t321[i]);
                    FillColor(ref t322[i]);
                    FillColor(ref t323[i]);
                    FillColor(ref t324[i]);
                    //t321[i] = new Color() { r = c.r /255f,g=c.g / 255f, b=c.b / 255f, a=c.a / 255f };
                }

                var newT = new Texture2D(tex.width, tex.height);
                newT.SetPixels32(t321);
                newT.Apply();
                var bs = newT.EncodeToPNG();
                File.WriteAllBytes(path + ".h.png", bs);
                newT.SetPixels32(t322);
                newT.Apply();
                bs = newT.EncodeToPNG();
                File.WriteAllBytes(path + ".s.png", bs);
                newT.SetPixels32(t323);
                newT.Apply();
                bs = newT.EncodeToPNG();
                File.WriteAllBytes(path + ".v.png", bs);
                newT.SetPixels32(t324);
                newT.Apply();
                bs = newT.EncodeToPNG();
                File.WriteAllBytes(path + ".a.png", bs);
                AssetDatabase.Refresh();
            }
        }
        public static void FillColor(ref Color32 color32)
        {
            color32.g = color32.r;
            color32.b = color32.r;
            color32.a = color32.r;
        }
    }


}