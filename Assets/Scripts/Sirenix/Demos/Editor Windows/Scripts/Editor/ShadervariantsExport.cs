#if UNITY_EDITOR

using System.Collections.Generic;
using UnityEditor;
using Sirenix.Utilities;
using UnityEngine;
using System.IO;
using System.Linq;
using System.Reflection;
using UnityEngine.Rendering;
using War.Render;
using Debug = UnityEngine.Debug;
using Object = UnityEngine.Object;

namespace Sirenix.OdinInspector.Demos
{
    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Objects")]
    public class ShadervariantsExport : GlobalConfig<ShadervariantsExport>
    {
        public List<Shader> shaderList;
        [FolderPath]
        public List<string> folderList;
        [FolderPath]
        public string outputPath;
        
        
        protected SerializedProperty svcCollections;


        public void OnEnable()
        {
            
        }

        [Button("导出变体")]
        public void StartExport()
        {
            if (shaderList == null || shaderList.Count == 0)
            {
                Debug.LogError("请添加shader");
                return;
            }
            if (folderList == null || folderList.Count == 0)
            {
                Debug.LogError("请添加对比文件夹");
                return;
            }
            if (string.IsNullOrEmpty(outputPath))
            {
                Debug.LogError("请选择生成目录");
                return;
            }
            //处理输出路径 
            if (!outputPath.EndsWith(".shadervariants"))
            {
                outputPath = outputPath +".shadervariants";
            }
            ExportShadervariants(shaderList, folderList);
            EditorUtility.SetDirty(this);
            AssetDatabase.SaveAssets();
        }
        Dictionary<Shader, List<string>> dictShaderFeature = new Dictionary<Shader, List<string>>();
        /// <summary>
        /// 导出变体列表文件
        /// </summary>
        /// <param name="shaderPath"></param>
        /// <param name="matFolder"></param>
        
        public void ExportShadervariants(List<Shader> shaders,List<string> matFolders)
        {
            for(int i = 0; i < shaders.Count; i++)
            {
                var shader = shaders[i];
                if (!dictShaderFeature.ContainsKey(shader))
                {
                    var ls = GetShaderFeatureKeywords(shader);
                    dictShaderFeature.Add(shader, ls);
                } 
                
            } 
            List<Material> mats = new List<Material>();
            List<string> matPath = new List<string>();
            var shaderDict = new Dictionary<Shader, List<Material>>(); 
            //收集文件夹下所有材质球
            for (int i = 0; i < matFolders.Count; i++)
            {
                var folderPath = matFolders[i];
                var files = Directory.GetFiles(folderPath, "*.mat", SearchOption.AllDirectories);
                for(int j = 0; j < files.Length; j++)
                {
                    if (!matPath.Contains(files[j]))
                    {
                        matPath.Add(files[j]);
                    } 
                }
            }
            for(int i = 0; i < matPath.Count; i++)
            {
                var mat = AssetDatabase.LoadAssetAtPath<Material>(matPath[i]);
     
                if (mat != null)
                {
                    if (shaders.Contains(mat.shader))
                    { 
                        mats.Add(mat);
                    }
                }
            }
            ShaderVariantCollection svc = new ShaderVariantCollection(); 
            if (!File.Exists(outputPath))
            {
                AssetDatabase.CreateAsset(svc, outputPath);
                AssetDatabase.Refresh();
            } 
            svc = AssetDatabase.LoadAssetAtPath<ShaderVariantCollection>(outputPath);
            svc.Clear();
            List<ShaderVariantCollection.ShaderVariant> vList = new List<ShaderVariantCollection.ShaderVariant>();
            for (int i=0;i< mats.Count; i++)
            {
                var mat = mats[i];
              
                var st = new ShaderVariantCollection.ShaderVariant();
                var kwLs = mat.shaderKeywords.ToList();
                var listSave = dictShaderFeature[mat.shader]; 
                for(int j = 0; j < kwLs.Count; j++)
                {
                    if (!listSave.Contains(kwLs[j]))
                    {
                        kwLs.Remove(kwLs[j]);
                        j--;
                    }
                }
                var finalKeywords = kwLs.ToArray();
                if (finalKeywords.Length == 0) //没有内容无需添加变体
                {
                    continue;
                }
                bool alreadyExist = false;
                foreach(var val in vList)
                {
                    if (val.shader != mat.shader)
                    {
                        continue;
                    }
                    if (System.Linq.Enumerable.SequenceEqual(val.keywords, finalKeywords))
                    { 
                        alreadyExist = true;
                        break;
                    }
                }
                if (!alreadyExist)
                {
                    st.shader = mat.shader;
                    st.keywords = finalKeywords;
                    st.passType = PassType.ForwardBase; //默认全都用forwardBase的
                    svc.Add(st);
                    vList.Add(st);
                }
            


            }
            EditorUtility.SetDirty(svc);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            Resources.UnloadUnusedAssets();
            SetABName(outputPath, "shaders_for_warmup");
            OnExportFinish();
            //ProcessMaterials(mats);
        }
        
        public void OnExportFinish()
        {
            //EditorApplication.update -= EditorUpdate;
            AssetDatabase.Refresh(); 
            ShaderVariantCollection svc = AssetDatabase.LoadAssetAtPath<ShaderVariantCollection>(outputPath);
            InsertToCollections(svc);
            Debug.LogError(svc.shaderCount);
            Debug.LogError(svc.variantCount);
        }
        
        public List<string> GetShaderFeatureKeywords(Shader shader)
        {
  
            var getKeywordsMethod = typeof(ShaderUtil).GetMethod("GetShaderLocalKeywords", BindingFlags.Static | BindingFlags.NonPublic);
            var keywords = (string[])getKeywordsMethod.Invoke(null, new object[] { shader });
            return keywords.ToList();
        }
        
        public void InsertToCollections(ShaderVariantCollection svc)
        {
            var perfab =AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Art/ShaderVariants/ShaderCollectionWarmup.prefab");
            ShaderCollectionWarmUp shaderCollectionWarmUp = perfab.GetComponent<ShaderCollectionWarmUp>();
            List<ShaderVariantCollection> collections = shaderCollectionWarmUp.m_ShaderVariantCollections?.ToList() ??
                                                        new List<ShaderVariantCollection>();
            if (!collections.Contains(svc))
            {
                collections = collections.Where(c => c != null).ToList();
                collections.Add(svc);
                shaderCollectionWarmUp.m_ShaderVariantCollections = collections.ToArray();
            }

            RefreshShadersToSVC(shaderCollectionWarmUp);


            // int len = shaderCollectionWarmUp.m_ShaderVariantCollections.Length;
            // bool isRepetition = false;
            // for (int i = 0; i < len; i++)
            // {
            //     if (shaderCollectionWarmUp.m_ShaderVariantCollections[i]==svc)
            //     {
            //         isRepetition = true;
            //         return;
            //     }
            // }
            // if (!isRepetition)
            // {
            //     Array.Resize(ref shaderCollectionWarmUp.m_ShaderVariantCollections,shaderCollectionWarmUp.m_ShaderVariantCollections.Length+1);
            //     shaderCollectionWarmUp.m_ShaderVariantCollections[len]=svc;
            // }
        }

        public void RefreshShadersToSVC(ShaderCollectionWarmUp shaderCollectionWarmUp)
        {
            shaderCollectionWarmUp.m_Shaders.Clear();
            var extraShaderNames = shaderCollectionWarmUp.m_ExtraShaderNames;
            var svcCollections = shaderCollectionWarmUp.m_ShaderVariantCollections;
            for (int i = 0; i < svcCollections.Length; i++)
            {
                var shadersProperty =new SerializedObject(svcCollections[i]).FindProperty("m_Shaders");
                var shaderNames = GetShaderNames(shadersProperty);
                var assetPath = AssetDatabase.GetAssetPath(svcCollections[i]);
                AssetImporter assetImporter = AssetImporter.GetAtPath(assetPath);
                var assetBundleName = assetImporter.assetBundleName;
                var shaderObjs = LoadAllAssetsByBundleName(assetBundleName);
                if (shaderObjs != null)
                {
                    foreach (var shaderObj in shaderObjs)
                    {
                        var shader = shaderObj as Shader;
                        if (shader != null)
                        {
                            if (shaderNames.Contains(shader.name) || extraShaderNames.Contains(shader.name))
                            {
                                shaderCollectionWarmUp.m_Shaders.Add(shader);
                            }
                        }
                    }
                }
            }

        }
        
        private List<string> GetShaderNames(SerializedProperty shadersProperty)
        {
            int shadersSize = shadersProperty.arraySize;
            List<string> result = new List<string>();

            for (int i = 0; i < shadersSize; ++i)
            {
                var shaderProperty = shadersProperty.GetArrayElementAtIndex(i);
                Shader objectReferenceValue = (Shader)shaderProperty.FindPropertyRelative("first").objectReferenceValue;
                if (objectReferenceValue)
                {
                    result.Add(objectReferenceValue.name);
                }
            }

            return result;
        }
        
        private Object[] LoadAllAssetsByBundleName(string assetBundleName)
        {
            string[] assetBundlePaths = AssetDatabase.GetAssetPathsFromAssetBundle(assetBundleName);
            if (assetBundlePaths.Length == 0)
            {
                ///@TODO: The error needs to differentiate that an asset bundle name doesn't exist
                //        from that there right scene does not exist in the asset bundle...

                Debug.LogError("There is no asset bundle with name \"" + assetBundleName + "\"");
                return null;
            }

            Object[] assets = new Object[assetBundlePaths.Length];

            for (int i = 0, count = assetBundlePaths.Length; i < count; ++i)
            {
                string assetPath = assetBundlePaths[i];
                assets[i] = AssetDatabase.LoadAssetAtPath<Object>(assetPath);
            }

            return assets;
        }
        
        static void SetABName(string oPath, string assetBundleName)
        {
            if (File.Exists(oPath) == false) return;
            var assetPath = oPath;
            if (!Path.HasExtension(assetPath))return;
            var assetImporter = AssetImporter.GetAtPath(assetPath);
            assetImporter.assetBundleName = assetBundleName;
        }

    }
}

#endif



