using UnityEditor;
using Sirenix.Utilities;
using System.Collections.Generic;
using UnityEngine;
using War.Base;
using Assets.Scripts.Sirenix.Demos.Editor_Windows.Scripts.Editor.ToolObj;
using System.IO;

namespace Sirenix.OdinInspector.Demos
{
    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Objects")]
    class ModifyPictureSize : GlobalConfig<ModifyPictureSize>
    {
        //[FoldoutGroup("ModifyPictureSize")]
        //[StringPopup(new string[] { "32", "128", "256","512","1024","2048" })]
        //public string pictureSize = "512";
        [FoldoutGroup("ModifyPictureSize")]
        [FolderPath]
        public List<string> checkPaths;
        [InfoBox("文件类型 例: png")]
        [FoldoutGroup("ModifyPictureSize")]
        [FolderPath]
        public List<string> checkPictureType;

        private int[] mSizes = new int[] { 32, 64, 128, 256, 512, 1024, 2048 };

        [FoldoutGroup("ModifyPictureSize")]
        [<PERSON><PERSON>("修改图片尺寸")]
        void SetModifyPictureSize()
        {
            List<string> files = new List<string>();
            for (int i = 0; i < checkPaths.Count; i++)
            {
                for (int j = 0; j < checkPictureType.Count; j++)
                {
                    files.AddRange(Directory.GetFiles(checkPaths[i], "*." + checkPictureType[j], SearchOption.AllDirectories));
                }
            }
            for (int i = 0; i < files.Count; i++)
            {
                string p = files[i].Replace("\\", "/");
                TextureImporter textureImporter = AssetImporter.GetAtPath(p) as TextureImporter;
                UnityEngine.Texture tex = AssetDatabase.LoadAssetAtPath<Texture>(p) as UnityEngine.Texture;
                textureImporter.mipmapEnabled = false;
                int maxSize = GetMaxSize(tex.width, tex.height);
                TextureImporterPlatformSettings textureImporterPlatformSettings = textureImporter.GetPlatformTextureSettings(BuildTarget.iOS.ToString());
                textureImporterPlatformSettings.maxTextureSize = maxSize;
                textureImporter.SetPlatformTextureSettings(textureImporterPlatformSettings);
                textureImporterPlatformSettings = textureImporter.GetPlatformTextureSettings(BuildTarget.Android.ToString());
                textureImporterPlatformSettings.overridden = true;
                textureImporterPlatformSettings.maxTextureSize = maxSize;
                textureImporter.SetPlatformTextureSettings(textureImporterPlatformSettings);
                EditorUtility.SetDirty(textureImporter);
                textureImporter.SaveAndReimport();
            }
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }

        private int GetMaxSize(int width, int height)
        {
            int x = 0;
            int y = 0;
            for (int i = 0; i < mSizes.Length; i++)
            {
                if(width < mSizes[i])
                {
                    x = i - 1;
                    break;
                }
            }
            for (int i = 0; i < mSizes.Length; i++)
            {
                if(height < mSizes[i])
                {
                    y= i - 1;
                    break;
                }
            }

            return x > y ? mSizes[x] : mSizes[y];
        }
    }
}
