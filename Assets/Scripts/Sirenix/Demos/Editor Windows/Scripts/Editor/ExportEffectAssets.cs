#if UNITY_EDITOR
//导出正式特效资源
namespace Sirenix.OdinInspector.Demos
{
    using UnityEngine;
    using UnityEditor;
    using Sirenix.Utilities;
    using System.IO;
    using System.Security.Cryptography;
    using System.Collections.Generic;
    using System;
    using War.UI;
    using UnityEngine.Timeline;
    using UnityEngine.Playables;
    using Newtonsoft.Json;


    [Serializable]
    public class StrProto
    {
        [HideLabel]
        [ShowInInspector]
        [HorizontalGroup]
        public string abname;

        [HideLabel]
        [ShowInInspector]
        [HorizontalGroup]
        public UnityEngine.Object assetSource;

        // [HorizontalGroup]
        // public int size;
        [HideLabel]
        [HorizontalGroup]
        public int count;

        [HideLabel]
        [HorizontalGroup]
        public int width;

        [HideLabel]
        [HorizontalGroup]
        public int height;

        [HideLabel]
        [HorizontalGroup]
        public int widthMulHeight;
        [ShowInInspector]
        public List<UnityEngine.Object> dependencies;

    }

    [System.Serializable]
    public class DictionaryItem
    {
        public string key;

        public string value;
    }

    [System.Serializable]
    public class DependencieRes
    {
        public string path;

        [ShowInInspector]
        public UnityEngine.Object da
        {
            set
            {
                var _path = AssetDatabase.GetAssetPath(value);
                path = _path;
            }
            get
            {
                var ob = AssetDatabase.LoadMainAssetAtPath(path);
                return ob;
            }
        }
    }
    [System.Serializable]
    public class SimpleResObject
    {
        public string path;

        public string newPath;
    }

    [System.Serializable]
    [JsonObject(MemberSerialization.OptIn)]
    public class ResObject
    {
        [JsonProperty]
        public string path;
        [ShowInInspector]
        public UnityEngine.Object target
        {
            set
            {
                path = AssetDatabase.GetAssetPath(value);
            }
            get
            {
                var ob = AssetDatabase.LoadMainAssetAtPath(path);
                return ob;
            }
        }

        [JsonProperty]
        public string newPath;
        [ShowInInspector]
        public UnityEngine.Object newTarget
        {
            set
            {
                newPath = AssetDatabase.GetAssetPath(value);
            }
            get
            {
                var ob = AssetDatabase.LoadMainAssetAtPath(newPath);
                return ob;
            }
        }

        public List<DependencieRes> dependencies = new List<DependencieRes>();
    }

    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Objects")]
    public class ExportEffectAssets : GlobalConfig<ExportEffectAssets>
    {
        [FoldoutGroup("ExportEffectAssets")]
        [FolderPath]
        public string checkPath = "";


        [FoldoutGroup("ExportEffectAssets")]
        [Sirenix.OdinInspector.FilePath]
        public List<string> filePaths = new List<string> { };


        [FoldoutGroup("ExportEffectAssets")]
        [FolderPath]
        public string targetExportPath = "";

        [FoldoutGroup("ExportEffectAssets")]
        [FolderPath]
        public string checkPathOfDependencies = ""; //依赖资源需要检测的范围，影响文件老旧md5的检测

        // [TextArea]
        // public string blackboard = "";

        // //[Bool]
        // [InfoBox("是否创建重复sprite的引用资源对象")]
        // [HideLabel]
        // public bool isCreateSameSpriteQuoteAsset = false; //是否创建重复sprite的引用资源对象，引用目标sprite为程序第一个遍历到的同md5文件

        // //[HorizontalGroup]
        // [InfoBox("是否删除已创建引用spriteAsset文件的原始文件")]
        // [HideLabel]
        // public bool isDeleteSameSprite = false; //是否删除已创建引用spriteAsset文件的原始文件

        // //[HorizontalGroup]
        // [InfoBox("是否替换cardAssets中重复的sprite")]
        // [HideLabel]
        // public bool isReplaceSameCardAssets = false; //是否替换cardAssets中重复的sprite
        [FoldoutGroup("ExportEffectAssets")]
        public List<ResObject> dependencies = new List<ResObject>();
        [FoldoutGroup("ExportEffectAssets")]
        //需要筛选的后缀名
        public List<string> exts = new List<string>();

        [FoldoutGroup("ExportEffectAssets")]
        //本次删掉的导出特效
        public List<string> deleteOutPutEffects = new List<string>();

        // [FoldoutGroup("ExportEffectAssets")]
        //不打入图集的贴图对应shader列表
        private List<string> excludeShaderOfAtlas = new List<string>();


        public Dictionary<string, Rect> textureAtlasRects = new Dictionary<string, Rect>();

        private string currentAtlasTexture = "";
        [FoldoutGroup("ExportEffectAssets")]
        public Dictionary<string, string> extAndTargetPath = new Dictionary<string, string>() {
                { ".fbx", "Models" },
                { ".anim", "Animations" },
                { ".controller", "Animations" },
                { ".mat", "Materials" },
                { ".prefab", "Prefabs" },
                { ".tga", "Textures" },
                { ".png", "Textures" },
                { ".jpg", "Textures" },
                
            };

        private Dictionary<UnityEngine.Object, string> newAssets = new Dictionary<UnityEngine.Object, string>();

        private const string changedMd5Name = "lastEffectChangedMd5.txt";
        private const string initialMd5Name = "effectMd5files.txt";

        private const string exportPrefabs = "exportPrefabs.txt";

        public Dictionary<string, List<string>> changedFileDic = new Dictionary<string, List<string>>();
        // [ShowInInspector]
        // public List<string> newAssetList = new List<string>();


        [FoldoutGroup("ExportEffectAssets")]
        [Button("开始导出资源")]
        void startExportEffectAssets()
        {
            excludeShaderOfAtlas = new List<string>(){
              "CBB/UIFangXing",
              "CBB/Additive_Mask",
              "CBB/Blended_Mask",
              "CBB/Dissolution_Additive",
              "CBB/Dissolution_Blend",
              "CBB/CBB_niuqu",
              "CBB_NoCulling/Dissolution_Additive",
              "CBB_NoCulling/Blended_Mask",
              "CBB_NoCulling/Dissolution_Blend",
              "CBB/UIYuanXing_Blend",
              "CBB_NoCulling/CBB_niuqu",
              "CBB/Additive_Mask_JZB",
            };
            // string fileInfos = "";
            // long allfileCount = 0;
            //Debug.Log("checkPath:" + checkPath + "  !!!" + excludeShaderOfAtlas.Count + "_____" + excludeShaderOfAtlas[8] + "   " + excludeShaderOfAtlas.IndexOf("CBB/UIYuanXing_Blend"));
            dependencies.Clear();
            StartCheckFiles(checkPath);
            
            // GameObject.StartCoroutine(StartCheckAllFile(checkPath));
            // string directorySizeInfo = string.Format("{0}\t{1}\t{2}\t\tFile folder\t{3}", checkPath, allSize,
            //  EditorUtility.FormatBytes(allSize), allfileCount);
            // fileInfos = string.Format("{0}\n{1}", directorySizeInfo, fileInfos);
            // blackboard = fileInfos;
        }

        [FoldoutGroup("ExportEffectAssets")]
        [Button("导出新老文件键值exportPrefab文件")]
        void startExportPrefabsKeyValueFile()
        {
            File.WriteAllText(string.Format("{0}/{1}", targetExportPath, exportPrefabs), JsonConvert.SerializeObject(dependencies));
        }

        // [Button]
        void setNewDependencies()
        {
            foreach (var item in dependencies)
            {
                if (item.newPath != null && item.newPath != "")
                {
                    Debug.LogError("SetNewResDependencies:" + item.newPath);
                    SetNewResDependencies(item.newPath);
                }
            }
        }

        /// 获取预制件依赖 <summary>
        /// 
        /// </summary>
        /// <typeparam name="T">欲获取的类型</typeparam>
        /// <param name="go"></param>
        /// <returns></returns>
        static List<T> GetPrefabDepe<T>(GameObject go)
        {
            List<T> results = new List<T>();
            UnityEngine.Object[] roots = new UnityEngine.Object[] { go };
            UnityEngine.Object[] dependObjs = EditorUtility.CollectDependencies(roots);
            foreach (UnityEngine.Object dependObj in dependObjs)
            {
                if (dependObj != null && dependObj.GetType() == typeof(T))
                {
                    results.Add((T)System.Convert.ChangeType(dependObj, typeof(T)));
                }
            }

            return results;
        }

        /// 获取预制件所有依赖 <summary>
        /// 
        /// </summary>
        /// <param name="go"></param>
        /// <returns></returns>
        static UnityEngine.Object[] GetPrefabAllDepe(GameObject go)
        {
            UnityEngine.Object[] roots = new UnityEngine.Object[] { go };
            UnityEngine.Object[] dependObjs = EditorUtility.CollectDependencies(roots);
            return dependObjs;
        }

        public List<ResObject> LoadExportPrefabDatas(string checkTargetPath)
        {
            string targetLuaMd5Path = string.Format("{0}/{1}", checkTargetPath, exportPrefabs);
            string targetFileContent = null;
            if (File.Exists(targetLuaMd5Path))
            {
                targetFileContent = File.ReadAllText(targetLuaMd5Path);
            }

            if (string.IsNullOrEmpty(targetFileContent))
            {
                Debug.Log("检查md5值失败：无法找到需要对比的lua md5信息文件，path:" + targetLuaMd5Path);
                return new List<ResObject>();
            }


            Dictionary<string, string[]> oldLuaFileDic = new Dictionary<string, string[]>();
            var tempNewestPatchFileDic = UIHelper.ToObj<List<ResObject>>(targetFileContent);
            return tempNewestPatchFileDic;
        }

        public void StartCheckFiles(string targetPath)
        {
            List<FileInfo> allFileInfos = new List<FileInfo>();
            //判断给定的路径是否存在,如果不存在则退出
            if (filePaths != null && filePaths.Count > 0)
            {
                foreach (var itemPath in filePaths)
                {
                    FileInfo file = new FileInfo(itemPath);
                    allFileInfos.Add(file);
                    Debug.LogError("批量导出 文件:" + itemPath);
                }
            }
            else
            {
                if (!Directory.Exists(targetPath))
                {
                    // yield break;
                    //判断是否为单个文件
                    if (File.Exists(targetPath))
                    {
                        FileInfo file = new FileInfo(targetPath);
                        allFileInfos.Add(file);
                        Debug.LogError(" 判断为单个文件:" + targetPath);
                    }
                    else
                    {
                        Debug.LogError(" 无效文件:" + targetPath);
                        return;
                    }
                }
                else
                {
                    //判断路径为文件夹
                    DirectoryInfo di = new DirectoryInfo(targetPath);
                    allFileInfos.AddRange(di.GetFiles("*.prefab", SearchOption.AllDirectories));
                    Debug.LogError(" 判断为文件夹:" + targetPath);
                }
            }




            newAssets.Clear();
            // newAssetList.Clear();

            // long len = 0;
            //定义一个DirectoryInfo对象

            //读取已导出的特效预制体
            StartLoadExportPrefabDatas();
            // dependencies = LoadExportPrefabDatas(targetExportPath);

            //根据文件md5，检测相距上次导出资源有变化的文件列表，只重新导出有变化的文件
            Dictionary<string, List<string>> currentmd5Dic = GetMd5FileInfo(checkPathOfDependencies);//, targetExportPath, initialMd5Name);
            changedFileDic = CheckMd5(targetExportPath, currentmd5Dic);

            string changedLuaJson = UIHelper.ToJson(changedFileDic);
            Debug.Log("BuildLuaPatch 变化的lua文件列表:" + changedLuaJson);
            //导出本次md5变化的信息
            File.WriteAllText(string.Format("{0}/{1}", targetExportPath, changedMd5Name), changedLuaJson);

            var dic = new Dictionary<string, object>();
            dic["list"] = currentmd5Dic;
            //导出本次md5信息
            File.WriteAllText(string.Format("{0}/{1}", targetExportPath, initialMd5Name), UIHelper.ToJson(dic));

            List<SimpleResObject> exportPrefabList = new List<SimpleResObject>();

            string extension = "";

            Dictionary<string, bool> allDependencies = new Dictionary<string, bool>();
            ResObject resObject;
            foreach (FileInfo file in allFileInfos)
            {
                extension = GetExtension(file.FullName);
                if (extension != ".prefab")
                {
                    continue;
                }
                Debug.LogError("file.FullName:" + file.FullName);
                string assetPath = file.FullName.Substring(file.FullName.IndexOf("Assets\\"));
                var go = AssetDatabase.LoadAssetAtPath<GameObject>(assetPath);
                var sourceAssetPath = AssetDatabase.GetAssetPath(go);

                UnityEngine.Object[] depes = GetPrefabAllDepe(go);
                bool isChanged = true;
                //检测资源是否有变化，是否需要执行导出
                if (!changedFileDic.ContainsKey(sourceAssetPath)) //预制体是否发生改变
                {
                    isChanged = false;
                    //检查预制体依赖的资源是否发生了变化
                    foreach (var d in depes)
                    {
                        var depesPath = AssetDatabase.GetAssetPath(d);
                        if (changedFileDic.ContainsKey(depesPath))
                        {
                            isChanged = true;
                            break;
                        }
                    }
                }

                //如果资源没有发生变化，则跳过导出
                if (isChanged == false)
                {
                    Debug.LogError("md5没有变化，跳过导出：" + sourceAssetPath);
                    continue;
                }

                resObject = new ResObject()
                {
                    path = assetPath
                };
                string sourceExt = Path.GetExtension(file.FullName);
                string effectName = file.Name.Replace(sourceExt, "");
                //如果有文件夹,则删除旧资源；
                string targetRootPath = Path.Combine(targetExportPath, "Effects");
                targetRootPath = Path.Combine(targetRootPath, effectName);
                if (Directory.Exists(targetRootPath))
                {
                    Directory.Delete(targetRootPath, true);
                }
                //如果有文件夹后刷新
                UnityEditor.AssetDatabase.Refresh();
                allDependencies.Clear();
                foreach (var d in depes)
                {
                    var _path = AssetDatabase.GetAssetPath(d);
                    //检测后缀名
                    string ext = GetExtension(_path);
                    Debug.LogError("原始依赖！！依赖路径:" + _path + " ext:" + ext);

                    if (exts.IndexOf(ext) <= -1)
                    {
                        continue;
                    }

                    //排除不在原始目录内的依赖资源
                    if (_path.IndexOf(checkPathOfDependencies) < 0)
                    {
                        Debug.LogError("排除不在原始目录内的依赖资源:" + _path + " checkPath:" + checkPathOfDependencies);
                        continue;
                    }

                    //防止依赖资源重复执行，增加字典标识已执行资源
                    if (!allDependencies.ContainsKey(_path))
                    {
                        allDependencies[_path] = true;
                        Debug.LogError("依赖路径:" + _path + " _path:" + _path);
                        string exportPath = CopyResToExportPath(_path, effectName);

                        resObject.dependencies.Add(new DependencieRes()
                        {
                            path = exportPath
                        });
                    }
                }
                //如果当前资源是导出的主资源，则记录路径
                var exportMainPath = Path.Combine(targetExportPath, "Effects");
                exportMainPath = Path.Combine(exportMainPath, effectName);
                exportMainPath = Path.Combine(exportMainPath, extAndTargetPath[extension]);
                exportMainPath = Path.Combine(exportMainPath, file.Name);
                resObject.newPath = exportMainPath;
                exportPrefabList.Add(new SimpleResObject()
                {
                    path = assetPath,
                    newPath = exportMainPath
                });
                // Debug.LogError("newPath:" + exportMainPath);
                //如果列表中有该资源信息，则先移除现有资源
                CheckAndRemoveSameSourceAsset(assetPath);
                dependencies.Add(resObject);

                UnityEditor.AssetDatabase.Refresh();



                //将该特效依赖贴图合成图集
                var textureRootPath = Path.Combine(targetExportPath, "Effects");
                textureRootPath = Path.Combine(textureRootPath, effectName);
                textureRootPath = Path.Combine(textureRootPath, extAndTargetPath[".png"]);
                targetAtlasPath = textureRootPath;
                CreateAtlas();

                // UnityEditor.AssetDatabase.Refresh();
                // yield return new WaitForSeconds(0.1f);
                if (exportMainPath != "")
                {
                    Debug.LogError("SetNewResDependencies:" + exportMainPath);
                    SetNewResDependencies(exportMainPath);
                    //设置abname
                    ToolUti.SetABNameByPath(exportMainPath);
                }

            }
            Debug.LogError("export effects complete!");
            startExportPrefabsKeyValueFile();
            // File.WriteAllText(string.Format("{0}/{1}", targetExportPath, exportPrefabs), JsonConvert.SerializeObject(dependencies));

        }

        public void CheckAndRemoveSameSourceAsset(string assetPath)
        {
            int count = dependencies.Count;
            ResObject item = null;
            for (int i = count - 1; i >= 0; i--)
            {
                item = dependencies[i];
                if (item.path == assetPath)
                {
                    dependencies.RemoveAt(i);
                    break;
                }
            }

        }

        //执行导入新老资源对照信息
        [FoldoutGroup("ExportEffectAssets")]
        [Button("执行导入新老资源对照信息")]
        public void StartLoadExportPrefabDatas()
        {
            Dictionary<string, string[]> oldMd5FileDic = ReadInitialMd5Names(targetExportPath);
            deleteOutPutEffects.Clear();
            //读取已导出的特效预制体
            dependencies = LoadExportPrefabDatas(targetExportPath);
            int count = dependencies.Count;
            ResObject item = null;
            for (int i = count - 1; i >= 0; i--)
            {
                item = dependencies[i];

                //如果原始资源有为空的，需要删除导出的资源
                if (item.target == null)
                {
                    deleteOutPutEffects.Add(item.path);
                    FileInfo file_info = new FileInfo(item.newPath);
                    string ext = Path.GetExtension(file_info.FullName);
                    string effectName = file_info.Name.Replace(ext, "");
                    //如果有文件夹,则删除旧资源；
                    string targetDeleteRootPath = Path.Combine(targetExportPath, "Effects");
                    targetDeleteRootPath = Path.Combine(targetDeleteRootPath, effectName);
                    if (Directory.Exists(targetDeleteRootPath))
                    {
                        Directory.Delete(targetDeleteRootPath, true);
                    }
                    dependencies.RemoveAt(i);
                }
                //如果新资源有为空的，从列表中移除；并且在md5列表中标记移除;用于重新导出
                else if (item.newTarget == null)
                {
                    dependencies.RemoveAt(i);
                    oldMd5FileDic.Remove(item.path.Replace('\\', '/'));
                    Debug.LogError("清楚无效资源：sourceTarget:" + item.path + "  isnull" + (item.target == null) + "newTarget:" + item.newPath + "  isnull" + (item.newTarget == null));
                }

            }
            var dic = new Dictionary<string, object>();
            dic["list"] = oldMd5FileDic;
            //导出修改后的md5信息
            File.WriteAllText(string.Format("{0}/{1}", targetExportPath, initialMd5Name), UIHelper.ToJson(dic));

            UnityEditor.AssetDatabase.Refresh();
        }

        public string GetExtension(string sourcePath)
        {
            string ext = Path.GetExtension(sourcePath);
            if (ext != null)
            {
                ext = ext.ToLower();
            }
            return ext;
        }

        //拷贝资源到导出目录
        public string CopyResToExportPath(string sourcePath, string effectName)
        {
            string ext = GetExtension(sourcePath);

            if (targetExportPath == "")
            {
                Debug.LogError("没有配置导出路径: targetExportPath");
                return "";
            }
            string targetRootPath = "";

            var sourceObject = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(sourcePath);
            //如果是图片，则判断是否超过256*256，如果超过，则当作公共资源
            if (sourceObject is Texture)
            {
                Texture texture = sourceObject as Texture;
                int textureSize = texture.width * texture.height;
                if (textureSize >= 256 * 256)
                {
                    targetRootPath = Path.Combine(targetExportPath, "Common");
                    targetRootPath = Path.Combine(targetRootPath, extAndTargetPath[ext]);
                    string targetPath = CopyRes(sourcePath, targetRootPath);

                    //刷新资源后设置abname
                    UnityEditor.AssetDatabase.Refresh();
                    ToolUti.SetABNameByPath(targetPath);
                    return targetPath;
                }
            }

            Debug.LogError("effectName:" + effectName);
            //拼接导出目录
            targetRootPath = Path.Combine(targetExportPath, "Effects");

            targetRootPath = Path.Combine(targetRootPath, effectName);
            targetRootPath = Path.Combine(targetRootPath, extAndTargetPath[ext]);

            return CopyRes(sourcePath, targetRootPath);

        }

        //拷贝资源到导出目录
        public string CopyRes(string sourcePath, string targetRootPath)
        {
            if (!Directory.Exists(targetRootPath))
            {
                try
                {
                    Directory.CreateDirectory(targetRootPath);
                }
                catch (System.Exception ex)
                {

                    throw new System.Exception("创建目标失败：" + ex.Message);
                }
            }
            string targetPath = Path.Combine(targetRootPath, Path.GetFileName(sourcePath));
            targetPath = targetPath.Replace('\\', '/');
            if (sourcePath == targetPath)
            {
                Debug.LogError("跳过拷贝，拷贝资源路径相同：" + targetPath);
                return targetPath;
            }
            File.Copy(sourcePath, targetPath, true);
            //UnityEditor.AssetDatabase.Refresh();
            AssetDatabase.ImportAsset(targetPath);
            var sourceGo = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(sourcePath);
            // var go = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(targetPath);
            // Debug.LogError("copy Res Name:" + targetPath + "  :" + go);
            // if (go == null)
            // {
            //     return "";
            // }

            // var goType = go.GetType();
            // Dictionary<int, string> assetList = new Dictionary<int, string>();
            // if (newAssets.ContainsKey(goType))
            // {
            //     assetList = newAssets[goType];
            //     assetList[sourceGo.GetInstanceID()] = targetPath;
            //     newAssets[goType] = assetList;
            // }
            // else
            // {

            // }
            newAssets[sourceGo] = targetPath;
            // newAssetList.Add(targetPath);
            Debug.Log("新资源信息 完整路径：" + targetPath + "   原始资源：" + sourcePath);

            //复制贴图原始导入信息
            if (sourceGo is Texture)
            {
                TextureImporter targetImporter = AssetImporter.GetAtPath(targetPath) as TextureImporter;
                TextureImporter sourceImporter = AssetImporter.GetAtPath(sourcePath) as TextureImporter;
                targetImporter.mipmapEnabled = false;
                targetImporter.alphaIsTransparency = sourceImporter.alphaIsTransparency;
                targetImporter.alphaSource = sourceImporter.alphaSource;
                targetImporter.wrapMode = sourceImporter.wrapMode;
                targetImporter.SaveAndReimport();
            }

            return targetPath;
        }



        //设置新资源引用
        public void SetNewResDependencies(string targetPath)
        {
            // FileInfo file_info = new FileInfo(targetPath);
            // DirectoryInfo dir_info = file_info.Directory;

            var go = AssetDatabase.LoadAssetAtPath<GameObject>(targetPath);
            Animator[] animators = go.GetComponentsInChildren<Animator>(true);
            RuntimeAnimatorController oldController;
            foreach (Animator animator in animators)
            {
                oldController = animator.runtimeAnimatorController;
                if (oldController == null)
                {
                    continue;
                }
                Debug.LogError("Animator替换新资源：" + targetPath + " 资源：" + oldController.name);
                RuntimeAnimatorController newController = GetNewAssetBy<RuntimeAnimatorController>(oldController);

                // AnimationClip clip = newController.animationClips[0];
                //设置新引用
                animator.runtimeAnimatorController = newController;
            }

            SkinnedMeshRenderer[] renderers = go.GetComponentsInChildren<SkinnedMeshRenderer>(true);
            Material oldMaterials;
            Material[] materials;
            // foreach (SkinnedMeshRenderer renderer in renderers)
            // {
            //     materials = renderer.sharedMaterials;
            //     int count = materials.Length;

            //     for (int i = 0; i < count; i++)
            //     {
            //         oldMaterials = materials[i];
            //         if (oldMaterials == null)
            //         {
            //             continue;
            //         }
            //         Material newMaterial = GetNewAssetBy<Material>(oldMaterials);
            //         if (newMaterial == null)
            //         {
            //             continue;
            //         }
            //         materials[i] = ReplaceTexture(newMaterial);
            //         UnityEditor.EditorUtility.SetDirty(newMaterial);
            //         Debug.LogError("SkinnedMeshRenderer- Material替换新资源：" + targetPath + " 资源：" + oldMaterials.name + " type:" + newMaterial);
            //     }
            //     renderer.sharedMaterials = materials;
            // }

            Renderer[] particleSystems = go.GetComponentsInChildren<Renderer>(true);
            // Material oldMaterials;
            // Material[] materials;
            foreach (Renderer particleSystem in particleSystems)
            {
                materials = particleSystem.sharedMaterials;
                int count = materials.Length;

                for (int i = 0; i < count; i++)
                {
                    oldMaterials = materials[i];
                    if (oldMaterials == null)
                    {
                        continue;
                    }
                    Material newMaterial = GetNewAssetBy<Material>(oldMaterials);
                    if (newMaterial == null)
                    {
                        continue;
                    }
                    materials[i] = ReplaceTexture(newMaterial);
                    Debug.LogError("SkinnedMeshRenderer- Material替换新资源：" + targetPath + " 资源：" + oldMaterials.name + " type:" + newMaterial);
                    // UnityEditor.EditorUtility.SetDirty(newMaterial);
                }
                particleSystem.sharedMaterials = materials;
                // UnityEditor.AssetDatabase.SaveAssets();
                // UnityEditor.AssetDatabase.Refresh();
            }

            UnityEditor.EditorUtility.SetDirty(go);
            UnityEditor.AssetDatabase.SaveAssets();
            UnityEditor.AssetDatabase.Refresh();
        }


        //替换材质球上所有的贴图
        private Material ReplaceTexture(Material _mat)
        {
            if (_mat == null)
            {
                return _mat;
            }

            List<string> results = new List<string>();

            Shader shader = _mat.shader;
            for (int i = 0; i < ShaderUtil.GetPropertyCount(shader); ++i)
            {
                if (ShaderUtil.GetPropertyType(shader, i) == ShaderUtil.ShaderPropertyType.TexEnv)
                {
                    string propertyName = ShaderUtil.GetPropertyName(shader, i);
                    Texture tex = _mat.GetTexture(propertyName);
                    if (tex == null)
                    {
                        continue;
                    }
                    string texPath = AssetDatabase.GetAssetPath(tex.GetInstanceID());
                    Texture newTexture = GetNewAssetBy<Texture>(tex);
                    if (newTexture == null)
                    {
                        Debug.LogError("未获取到新的材质球贴图：" + _mat.name + " type:" + tex.GetType() + " texname:" + tex.name + " propertyName:" + propertyName);
                        continue;
                    }
                    //获取贴图合并图集之后的信息
                    string nexTexPath = AssetDatabase.GetAssetPath(newTexture);
                    var sourceOffset = _mat.GetTextureOffset(propertyName);
                    var sourceScale = _mat.GetTextureScale(propertyName);
                    if (!string.IsNullOrEmpty(currentAtlasTexture) && textureAtlasRects.ContainsKey(nexTexPath) &&
                        sourceOffset.Equals(Vector2.zero) && sourceScale.Equals(Vector2.one) //判断原始贴图uv数据是否为默认0、0、1、1，如果不符合该要求，则不适用图集引用，改为引用贴图
                        && excludeShaderOfAtlas.IndexOf(shader.name) < 0 //特殊shader排除，这种shader未使用shader自带offset和scale；不能使用图集
                        && newTexture.wrapMode != TextureWrapMode.Clamp) //美术k动画需求：因为要K材质球里面的offest的动画，所以Clamp下的动画不可以加入图集
                    {
                        Rect textureRect = textureAtlasRects[nexTexPath];
                        var atlasTexture = AssetDatabase.LoadAssetAtPath<Texture2D>(currentAtlasTexture);
                        _mat.SetTexture(propertyName, atlasTexture);
                        _mat.SetTextureOffset(propertyName, new Vector2(textureRect.x, textureRect.y));
                        _mat.SetTextureScale(propertyName, new Vector2(textureRect.width, textureRect.height));

                    }
                    else
                    {
                        _mat.SetTexture(propertyName, newTexture);
                    }
                    Debug.LogError("材质球贴图：" + _mat.name + " type:" + tex.GetType() + " texname:" + tex.name + " propertyName:" + propertyName + " newTexture:" + newTexture.name
                    + " 贴图path：" + nexTexPath + " shader.name:" + shader.name + "  是否在排除shader内：" + excludeShaderOfAtlas.IndexOf(shader.name));
                    // results.Add(texPath);
                }
            }
            return _mat;
            // return results.ToArray();
        }

        public T GetNewAssetBy<T>(UnityEngine.Object component) where T : UnityEngine.Object
        {
            // Dictionary<int, string> paths = newAssets[classType];
            // var sourceAssetPath = AssetDatabase.GetAssetPath(component);
            if (!newAssets.ContainsKey(component))
            {
                return default;
            }
            // var go = newAssets[component] as T;
            var go = AssetDatabase.LoadAssetAtPath<T>(newAssets[component]);
            return go;
        }


        [FoldoutGroup("CreateAtlas")]
        [FolderPath]
        public string targetAtlasPath = "";
        [FoldoutGroup("CreateAtlas")]
        [Button("手动创建图集")]
        public void CreateAtlas()
        {
            currentAtlasTexture = "";
            textureAtlasRects.Clear();

            string spritePath = targetAtlasPath;
            if (!Directory.Exists(spritePath))
            {
                return;
            }
            // spritePath = EditorUtility.OpenFolderPanel("选择文件夹", "", "");

            List<Sprite> sprites = new List<Sprite>();
            List<Texture2D> newTexs = new List<Texture2D>();
            List<string> imagePaths = new List<string>();
            List<string> extensions = new List<string>() { ".png", ".jpg", ".psd" };
            // 找出单独的sprite
            string[] files = Directory.GetFiles(spritePath, "*.*", SearchOption.AllDirectories);
            for (int i = 0; i < files.Length; i++)
            {
                string relativePath = files[i].Substring(files[i].IndexOf("Asset")).Replace('\\', '/');
                // Rect t_Rect = sprites[i].rect;
                var t_SourceTex = AssetDatabase.LoadAssetAtPath<Texture2D>(relativePath);
                if (t_SourceTex == null)
                {
                    continue;
                }
                Debug.Log("filesName:" + relativePath);
                // int textureSize = texture.width * texture.height;
                // 开启源Spirte的可读
                TextureImporter t_Importer = AssetImporter.GetAtPath(relativePath) as TextureImporter;
                //排除特殊FromGrayScale类型的贴图接入图集，防止表现异常
                if (t_Importer.alphaSource == TextureImporterAlphaSource.FromGrayScale)
                {
                    continue;
                }
                t_Importer.isReadable = true;
                t_Importer.textureCompression = TextureImporterCompression.Uncompressed;

                t_Importer.SaveAndReimport();
                // AssetDatabase.ImportAsset(relativePath);
                // 裁剪出新的Texture
                // Color[] t_Colors = t_SourceTex.GetPixels((int)t_Rect.x, (int)t_Rect.y, (int)t_Rect.width, (int)t_Rect.height);
                newTexs.Add(t_SourceTex);
                imagePaths.Add(relativePath);
                // newTexs[i].SetPixels(t_Colors);
            }

            // 打包成Atlas
            string atlasPath = spritePath + "/atlas.png";
            // if (File.Exists(atlasPath))
            // {
            //     File.Delete(atlasPath);
            // }
            // UnityEditor.AssetDatabase.Refresh();
            string atlasRelativePath = atlasPath.Substring(atlasPath.IndexOf("Assets"));
            Texture2D atlasTex = new Texture2D(1024, 1024);
            Rect[] atlasRects = atlasTex.PackTextures(newTexs.ToArray(), 0, 4096);
            SpriteMetaData[] atlasSheets = new SpriteMetaData[atlasRects.Length];
            File.WriteAllBytes(atlasPath, atlasTex.EncodeToPNG());
            // 设置Atlas的sprite
            for (int i = 0; i < atlasSheets.Length; i++)
            {
                // SpriteMetaData t_Meta = new SpriteMetaData();
                // t_Meta.name = sprites[i].name;
                Rect rect = atlasRects[i];
                // t_Meta.rect.Set(
                //     t_Meta.rect.x * atlasTex.width,
                //     t_Meta.rect.y * atlasTex.height,
                //      t_Meta.rect.width * atlasTex.width,
                //      t_Meta.rect.height * atlasTex.height
                // );
                textureAtlasRects[imagePaths[i]] = rect;
                Debug.LogError("贴图信息：" + imagePaths[i] + "rect.x: " + rect.x + "rect.y: " + rect.y + "rect.width: " + rect.width + "rect.height: " + rect.height);
                // t_Meta.alignment = 9;
                // Rect t_Rect = sprites[i].rect;
                // t_Meta.pivot = new Vector2(sprites[i].pivot.x / t_Rect.width, sprites[i].pivot.y / t_Rect.height);
                // atlasSheets[i] = t_Meta;
            }

            UnityEditor.AssetDatabase.Refresh();

            // 设置Atlas Texture属性
            TextureImporter atlas_Importer = AssetImporter.GetAtPath(atlasPath) as TextureImporter;
            // atlas_Importer.textureType = TextureImporterType.Sprite;
            // atlas_Importer.spriteImportMode = SpriteImportMode.Multiple;
            atlas_Importer.textureCompression = TextureImporterCompression.Compressed;
            atlas_Importer.mipmapEnabled = false;
            atlas_Importer.alphaIsTransparency = true;
            atlas_Importer.alphaSource = TextureImporterAlphaSource.FromInput;

            // TextureImporterPlatformSettings textureImporterPlatformSettings = atlas_Importer.GetPlatformTextureSettings(BuildTarget.iOS.ToString());
            // textureImporterPlatformSettings.maxTextureSize = maxSize;
            //     textureImporter.SetPlatformTextureSettings(textureImporterPlatformSettings);
            //     textureImporterPlatformSettings = textureImporter.GetPlatformTextureSettings(BuildTarget.Android.ToString());
            //     textureImporterPlatformSettings.overridden = true;
            //     textureImporterPlatformSettings.maxTextureSize = maxSize;
            //     textureImporter.SetPlatformTextureSettings(textureImporterPlatformSettings);
            // atlas_Importer.spritesheet = atlasSheets;
            //设置abname
            //ToolUti.SetABNameByPath(atlasPath);
            atlas_Importer.SaveAndReimport();

            AssetDatabase.ImportAsset(atlasRelativePath);
            AssetDatabase.Refresh();
            currentAtlasTexture = atlasPath;
        }

#region CheckDepeCount
        [FoldoutGroup("CheckDepeCount")]
        [FolderPath]
        public List<string> findassets = new List<string>{
            "Assets/Art/Effects/Textures",
        };
        [FoldoutGroup("CheckDepeCount")]
        [FolderPath]
        public List<string> dependenciesAssets = new List<string>{
            "Assets/Art/Effects",
        };

        [FoldoutGroup("CheckDepeCount")]
        //是否只展示预制体依赖；（用来判断资源被对象引用的次数）
        public bool isOnlyShowTargetPrefab = true;

        [FoldoutGroup("CheckDepeCount")]
        public List<StrProto> fList = new List<StrProto>();
        [FoldoutGroup("CheckDepeCount")]
        public Dictionary<string, List<string>> dependenciesList = new Dictionary<string, List<string>>();

        [FoldoutGroup("CheckDepeCount")]
        public List<UnityEngine.Object> disableAssets = new List<UnityEngine.Object>();
        Func<string, bool> isPng = (s) => s.EndsWith(".png", StringComparison.CurrentCultureIgnoreCase) || s.EndsWith(".tga", StringComparison.CurrentCultureIgnoreCase);
        //检测引用信息
        [FoldoutGroup("CheckDepeCount")]
        [Button("检测引用信息")]
        void CheckDepeCount()
        {
            var fas = AssetDatabase.FindAssets("", findassets.ToArray());
            dependenciesList = new Dictionary<string, List<string>>();
            disableAssets = new List<UnityEngine.Object>();
            var abnames = new Dictionary<string, int>();
            //Debug.Log("所有文件列表：" + string.Join("/n", fas));
            // ToolUti.Iter(fas, (fa) =>
            // {
            foreach (var fa in fas)
            {
                var ap = AssetDatabase.GUIDToAssetPath(fa);
                var ai = AssetImporter.GetAtPath(ap);
                string ext = GetExtension(ap);
                // if (isPng(ap))
                // {
                //GetTextureSizeBy(ap);
                if (ext != "")
                {
                    abnames[ap] = 0;
                    dependenciesList[ap] = new List<string>();
                    Debug.Log("长度" + ap + name + dependenciesList.Count);

                }
                // }
            }

            var allnames = AssetDatabase.FindAssets("", dependenciesAssets.ToArray());
            foreach (var abname in allnames)
            {
                var ap = AssetDatabase.GUIDToAssetPath(abname);
                var ai = AssetImporter.GetAtPath(ap);
                // if (string.IsNullOrEmpty(ai.assetBundleName))
                // {
                //     //Debug.Log("被依赖文件：" + ap + "没有abname!!!!!!!!!!!!!!!!!!!!!!!");
                //     return ap;
                // }
                var abs = AssetDatabase.GetDependencies(ap, true);
                foreach (var ab in abs)
                {
                    var num = 0;
                    //Debug.Log(abname + "依赖项：" + ab);
                    if (ab == ap)
                    {
                        continue;
                    }

                    if (abnames.TryGetValue(ab, out num))
                    {
                        if (dependenciesList[ab].IndexOf(ap) < 0)
                        {
                            //判断是否过滤预制体
                            if (!isOnlyShowTargetPrefab || ap.EndsWith(".prefab", StringComparison.CurrentCultureIgnoreCase))
                            {
                                abnames[ab] = num + 1;
                                dependenciesList[ab].Add(ap);
                            }
                        }
                        //if (abnames[ab] >= 2)
                        //{
                        //    Debug.LogError(ab +"---- \n" + string.Join("\n", dependenciesList[ab].ToArray()));
                        //}
                    }
                }
            }


            Debug.Log(UIHelper.ToJson(abnames));
            fList.Clear();
            List<UnityEngine.Object> dependenciesAsset;
            foreach (var abn in abnames.Keys)
            {
                //var ai = AssetImporter.GetAtPath(abn);
                AssetImporter ai = AssetImporter.GetAtPath(abn) as AssetImporter;
                var obj = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(abn);
                Texture2D texture = null;
                if (obj is Texture2D)
                {
                    texture = obj as Texture2D;
                }
                var abName = "";
                if (ai)
                {
                    abName = ai.assetBundleName;
                }
                //计算依赖资源
                dependenciesAsset = new List<UnityEngine.Object>();
                dependenciesList[abn].ToArray().ForEach(ap =>
                {
                    UnityEngine.Object depObj = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(ap);
                    if (dependenciesAsset.IndexOf(depObj) < 0)
                    {
                        dependenciesAsset.Add(depObj);
                    }
                });
                fList.Add(new StrProto()
                {
                    abname = abName,
                    assetSource = obj,
                    count = abnames[abn],
                    width = texture ? texture.width : 0,
                    height = texture ? texture.height : 0,
                    widthMulHeight = texture ? texture.width * texture.height : 0,
                    dependencies = dependenciesAsset,
                });
                if (abnames[abn] <= 0)
                {
                    disableAssets.Add(obj);
                }
            }

            fList.Sort((Comparison<StrProto>)((f1, f2) =>
            {
                return (int)(f2.count - f1.count);
            }));

        }

        [FoldoutGroup("CheckDepeCount")]
        [Button("删除未使用公共资源")]
        void deleteDisableAssets()
        {
            string path;
            foreach (var item in disableAssets)
            {
                path = AssetDatabase.GetAssetPath(item);
                Debug.LogFormat("获取路径：{0}", path);
                if (File.Exists(path))
                {
                    File.Delete(path);
                    Debug.LogFormat("删除路径：{0}", path);
                }

            }
            UnityEditor.AssetDatabase.Refresh();
        }

#endregion


#region ReplaceTimeLineDependencies 替换timeline引用的相关预制体

        [FoldoutGroup("ReplaceTimeLineDependencies")]
        [FolderPath]
        //检测timeline的父目录（和目标文件列表互斥，如果有目标文件列表，则检测目录无效）
        public string replaceCheckPath = "";


        [FoldoutGroup("ReplaceTimeLineDependencies")]
        [Sirenix.OdinInspector.FilePath]
        //指定要替换的timeline文件（和检测目录互斥，如果该列表生效，则检测目录无效）
        public List<string> replaceTargetFilePaths = new List<string> { };

        [FoldoutGroup("ReplaceTimeLineDependencies")]
        public Boolean isCheckMd5 = true;

        //替换timeline引用资源
        [FoldoutGroup("ReplaceTimeLineDependencies")]
        [Button("替换timeline引用预制体")]
        void ReplaceTimeLineDependencies()
        {

            List<FileInfo> allFileInfos = new List<FileInfo>();
            //判断给定的路径是否存在,如果不存在则退出
            if (replaceTargetFilePaths != null && replaceTargetFilePaths.Count > 0)
            {
                foreach (var itemPath in replaceTargetFilePaths)
                {
                    FileInfo file = new FileInfo(itemPath);
                    allFileInfos.Add(file);
                    Debug.LogError("批量替换timeline 文件:" + itemPath);
                }
            }
            else
            {
                if (!Directory.Exists(replaceCheckPath))
                {
                    // yield break;
                    //判断是否为单个文件
                    if (File.Exists(replaceCheckPath))
                    {
                        FileInfo file = new FileInfo(replaceCheckPath);
                        allFileInfos.Add(file);
                        Debug.LogError(" 判断为单个文件:" + replaceCheckPath);
                    }
                    else
                    {
                        Debug.LogError(" 无效文件:" + replaceCheckPath);
                        return;
                    }
                }
                else
                {
                    //判断路径为文件夹
                    DirectoryInfo di = new DirectoryInfo(replaceCheckPath);
                    allFileInfos.AddRange(di.GetFiles("*.playable", SearchOption.AllDirectories));
                    Debug.LogError(" 判断为文件夹:" + replaceCheckPath);
                }
            }

            foreach (FileInfo file in allFileInfos)
            {
                string targetRootPath = Path.Combine(targetExportPath, "Timeline_battle");

                string assetPath = file.FullName.Substring(file.FullName.IndexOf("Assets\\"));
                if (isCheckMd5 && !changedFileDic.ContainsKey(assetPath)) //预制体是否发生改变)
                {
                    continue;
                }
                string targetPath = CopyRes(assetPath, targetRootPath);
                Debug.Log("替换timeline：" + targetPath + "    source:" + assetPath);
                var timeline = AssetDatabase.LoadAssetAtPath<TimelineAsset>(targetPath);
                // var sourceAssetPath = AssetDatabase.GetAssetPath(go);
                DoReplaceDependencies(timeline);
            }
            UnityEditor.AssetDatabase.SaveAssets();
            UnityEditor.AssetDatabase.Refresh();

        }


        private void DoReplaceDependencies(TimelineAsset timeline)
        {
            bool dirty = false;
            string effectPath;
            string outsideSkillPath;
            UnityEngine.Object newObject;
            foreach (TrackAsset track in timeline.GetOutputTracks())
            {
                if (track is ParticleAttackTrack)
                {
                    foreach (TimelineClip clip in track.GetClips())
                    {
                        ParticleAttackClip targetClip = clip.asset as ParticleAttackClip;
                        if (targetClip.template.prefab != null)
                        {
                            effectPath = AssetDatabase.GetAssetPath(targetClip.template.prefab);
                            newObject = GetNewObjectBy(targetClip.template.prefab);
                            if (newObject != null)
                            {
                                targetClip.template.prefab = newObject as GameObject;
                                Debug.LogFormat("替换timeline引用特效：原始资源：{0};   替换资源{1}", effectPath, AssetDatabase.GetAssetPath(targetClip.template.prefab));
                                dirty = true;
                            }
                        }
                        if (targetClip.template.outsideSkillPrefab != null)
                        {
                            outsideSkillPath = AssetDatabase.GetAssetPath(targetClip.template.outsideSkillPrefab);
                            newObject = GetNewObjectBy(targetClip.template.outsideSkillPrefab);
                            if (newObject != null)
                            {
                                targetClip.template.outsideSkillPrefab = newObject as GameObject;
                                // targetClip.template.outsideSkillPrefab = GetNewAssetBy<GameObject>(targetClip.template.outsideSkillPrefab);
                                Debug.LogFormat("替换timeline引用插屏：原始资源：{0};   替换资源{1}", outsideSkillPath, AssetDatabase.GetAssetPath(targetClip.template.outsideSkillPrefab));
                                dirty = true;
                            }
                            else
                            {
                                Debug.LogFormat("失败：替换timeline引用插屏：原始资源：{0};   替换资源{1}", outsideSkillPath, AssetDatabase.GetAssetPath(targetClip.template.outsideSkillPrefab));
                            }
                        }
                    }
                }
            }
            if (dirty)
            {
                EditorUtility.SetDirty(timeline);
            }
        }

        public UnityEngine.Object GetNewObjectBy(UnityEngine.Object sourceObj)
        {
            foreach (var item in dependencies)
            {
                // Debug.LogError("GetNewObjectBy:" + dependencies.Count + "  " + dependencies.IndexOf(item) + "   " + item.path + "  sourcePath:  " + AssetDatabase.GetAssetPath(sourceObj) + " equale " + item.target.Equals(sourceObj));

                if (item.target.Equals(sourceObj))
                {
                    return item.newTarget;
                }

            }
            return null;
        }

#endregion

        public static Dictionary<string, string[]> ReadInitialMd5Names(string checkTargetPath)
        {
            string targetLuaMd5Path = string.Format("{0}/{1}", checkTargetPath, initialMd5Name);
            string targetFileContent = null;
            if (File.Exists(targetLuaMd5Path))
            {
                targetFileContent = File.ReadAllText(targetLuaMd5Path);
            }

            if (string.IsNullOrEmpty(targetFileContent))
            {
                Debug.Log("检查md5值失败：无法找到需要对比的lua md5信息文件，path:" + targetLuaMd5Path);
                return null;
            }

            Dictionary<string, string[]> oldLuaFileDic = new Dictionary<string, string[]>();
            var tempNewestPatchFileDic =
                UIHelper.ToObj<Dictionary<string, Dictionary<string, string[]>>>(targetFileContent);
            if (tempNewestPatchFileDic.ContainsKey("list"))
            {
                oldLuaFileDic = tempNewestPatchFileDic["list"];
            }
            return oldLuaFileDic;
        }

        public static Dictionary<string, List<string>> CheckMd5(string checkTargetPath, Dictionary<string, List<string>> currentLuaDic)
        {
            // string targetLuaMd5Path = string.Format("{0}/{1}", checkTargetPath, initialMd5Name);
            // string targetFileContent = null;
            // if (File.Exists(targetLuaMd5Path))
            // {
            //     targetFileContent = File.ReadAllText(targetLuaMd5Path);
            // }

            // if (string.IsNullOrEmpty(targetFileContent))
            // {
            //     Debug.Log("检查md5值失败：无法找到需要对比的lua md5信息文件，path:" + targetLuaMd5Path);
            //     return currentLuaDic;
            // }


            // Dictionary<string, string[]> oldLuaFileDic = new Dictionary<string, string[]>();
            // var tempNewestPatchFileDic =
            //     UIHelper.ToObj<Dictionary<string, Dictionary<string, string[]>>>(targetFileContent);
            // if (tempNewestPatchFileDic.ContainsKey("list"))
            // {
            //     oldLuaFileDic = tempNewestPatchFileDic["list"];
            // }

            Dictionary<string, string[]> oldLuaFileDic = ReadInitialMd5Names(checkTargetPath);

            if (oldLuaFileDic == null)
            {
                return currentLuaDic;
            }

            Dictionary<string, List<string>> changedLuaFileDic = new Dictionary<string, List<string>>();
            List<string> currentLuaInfo;
            List<string> oldLuaInfo;
            foreach (string key in currentLuaDic.Keys)
            {
                currentLuaInfo = currentLuaDic[key];
                if (!oldLuaFileDic.ContainsKey(key))
                {
                    //新增lua文件
                    changedLuaFileDic.Add(key, currentLuaInfo);
                    continue;
                }

                oldLuaInfo = new List<string>(oldLuaFileDic[key]);

                //检测md5值和长度值是否有变化
                if (currentLuaInfo[0].Equals(oldLuaInfo[0]) && currentLuaInfo[1].Equals(oldLuaInfo[1]))
                {
                    continue;
                }

                Debug.Log("检测md5值和长度值是否有变化md5:" + currentLuaInfo[0] + " old:" + oldLuaInfo[0] + " length:" +
                          currentLuaInfo[1] + " old:" + oldLuaInfo[1]);
                changedLuaFileDic.Add(key, currentLuaInfo);
            }

            return changedLuaFileDic;
        }


        //[Button("创建md5文件")]
        public static Dictionary<string, List<string>> GetMd5FileInfo(string souceFilesRootPath)//, string outputPath, string fileName)
        {
            /// create patch file
            // if (!Directory.Exists(outputPath))
            // {
            //     Directory.CreateDirectory(outputPath);
            // }

            try
            {
                var fileDic = new Dictionary<string, List<string>>();

                var files = Directory.GetFiles(souceFilesRootPath, "*.*", SearchOption.AllDirectories);
                foreach (var f in files)
                {
                    var md5 = War.Base.BuildScript.File2MD5(f);
                    var shortf = f.Trim('\\', '/').Replace("\\", "/");

                    var list = new List<string>() { md5, new FileInfo(f).Length + "" };
                    fileDic.Add(shortf, list);
                }

                //var content = string.Join("\n", fileList.ToArray());
                // var dic = new Dictionary<string, object>();
                // dic["list"] = fileDic;

                // if (fileName == "")
                // {
                //     fileName = initialMd5Name;
                // }

                // File.WriteAllText(string.Format("{0}/{1}", outputPath, fileName), UIHelper.ToJson(dic));
                return fileDic;
            }
            catch (Exception e)
            {
                Debug.LogError(e.ToString());
            }

            return null;
        }
    }
}
#endif
