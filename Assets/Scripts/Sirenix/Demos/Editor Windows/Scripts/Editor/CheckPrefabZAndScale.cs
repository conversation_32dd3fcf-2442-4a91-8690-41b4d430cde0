using System;
using System.Collections.Generic;
using System.IO;
using Sirenix.Utilities;
using UnityEditor;
using UnityEditor.Experimental.SceneManagement;
using UnityEngine;

#if UNITY_EDITOR
namespace Sirenix.OdinInspector.Demos
{
    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Objects")]
    public class CheckPrefabZAndScale : GlobalConfig<CheckPrefabZAndScale>
    {
        [InfoBox("这个工具是填写目录然后把该目录底下的预制体z轴不为零或者大小不为一的输出出来")] [Title("如果要开启该功能请打开IsOn")]
        public bool IsOn;

        [Title("输入目录，格式为此项目内目录，例如：Assets\\CasualGame\\CrowdRunnersSuperHero\\Prefab")] [FolderPath]
        public string Path;

        [Title("输出目录，点击PrefabName内的预制体再点击Scene内的按钮跳转第一个有问题的物体上")]
        public List<GameObject> PrefabName = new List<GameObject>();

        public Dictionary<GameObject, List<GameObject>> NotGoName = new Dictionary<GameObject, List<GameObject>>();

        public List<GameObject> lsGoObject = new List<GameObject>();
        private GameObject goPrefab;

        [Button("开始解析文件")]
        private void Start()
        {
            //Instance = this;
            if (!Directory.Exists(Path))
            {
                Debug.LogError("没有此文件夹，请检查文件路径是否正确！" + Path);
                return;
            }

            PrefabName.Clear();
            NotGoName.Clear();
            lsGoObject.Clear();
            string[] guids = AssetDatabase.FindAssets("t:Prefab", new string[] {Path});
            foreach (var item in guids)
            {
                string assetAtPath = AssetDatabase.GUIDToAssetPath(item);
                goPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(assetAtPath);
                GMO(goPrefab, goPrefab);
            }
        }

        //递归遍历所有子物体
        public void GMO(GameObject GO, GameObject GoPrefab)
        {
            if (GO.GetComponentsInChildren<RectTransform>(true).Length != 1)
            {
                foreach (Transform item in GO.transform)
                {
                    GMO(item.gameObject, GoPrefab);
                }
            }

            if (null != GO.GetComponent<RectTransform>())
            {
                //如果Object Z轴不为0或者大小不为1
                if (GO.GetComponent<RectTransform>().localPosition.z != 0 ||
                    GO.GetComponent<RectTransform>().localScale != Vector3.one)
                {
                    //如果里面已经存了那就拿出来塞入异常object
                    if (NotGoName.ContainsKey(GoPrefab))
                    {
                        lsGoObject = NotGoName[GoPrefab];
                        if (!lsGoObject.Contains(GO))
                        {
                            lsGoObject.Add(GO);
                            NotGoName[GoPrefab] = lsGoObject;
                        }
                    }
                    else
                    {
                        PrefabName.Add(GoPrefab);
                        List<GameObject> lsGoObject1 = new List<GameObject>
                        {
                            GO
                        };
                        Debug.Log(GO);
                        NotGoName.Add(GoPrefab, lsGoObject1);
                    }
                }
                else if (lsGoObject.Contains(GO))
                {
                    lsGoObject.Remove(GO);
                    NotGoName[GoPrefab] = lsGoObject;
                }
            }
        }

        public static List<GameObject> FindGameObject(GameObject gameObject)
        {
            List<GameObject> gameObjects = new List<GameObject>();
            if (gameObject.GetComponentsInChildren<RectTransform>(true).Length != 1)
            {
                foreach (Transform item in gameObject.transform)
                {
                    gameObjects.AddRange(FindGameObject(item.gameObject));
                }
            }

            if (null != gameObject.GetComponent<RectTransform>())
            {
                //如果Object Z轴不为0或者大小不为1            
                if (gameObject.GetComponent<RectTransform>().localPosition.z != 0 ||
                    gameObject.GetComponent<RectTransform>().localScale != Vector3.one)
                {
                    gameObjects.Add(gameObject);
                }
            }

            return gameObjects;
        }

        public class PrefabOpenListener : EditorWindow
        {
            private static int goNum = 0;
            private static GameObject goPrefab;
            private static List<GameObject> goObject;

            [InitializeOnLoadMethod]
            [Obsolete]
            public static void Init()
            {
                //加入事件
                PrefabStage.prefabStageOpened += OnPrefabStageOpened;
                PrefabStage.prefabStageClosing += OnPrefabStageClosing;
            }

            [Obsolete]
            private static void OnPrefabStageOpened(PrefabStage prefabStage)
            {
                if (prefabStage.prefabAssetPath.EndsWith(".prefab") && Instance.IsOn)
                {
                    goNum = 0;
                    SceneView.duringSceneGui += DuringSceneGui;
                }
            }

            [Obsolete]
            private static void OnPrefabStageClosing(PrefabStage prefabStage)
            {
                SceneView.duringSceneGui -= DuringSceneGui;
            }

            private static Rect buttonRect = new Rect(5, 50, 200, 20);

            [Obsolete]
            private static void DuringSceneGui(SceneView sceneView)
            {
                Handles.BeginGUI();
                if (GUI.Button(buttonRect, "下一个Object，如果没有则会退出", GUI.skin.box))
                {
                    CheckPrefab();
                }

                Handles.EndGUI();
            }

            [Obsolete]
            private static void CheckPrefab()
            {
                //获取当前打开的Prefab
                PrefabStage prefabStage = PrefabStageUtility.GetCurrentPrefabStage();
                if (prefabStage == null)
                    return;
                GameObject prefabRoot = prefabStage.prefabContentsRoot;
                if (prefabRoot == null)
                    return;

                var findGameObject = FindGameObject(prefabStage.prefabContentsRoot);
                if (findGameObject.Count == 0)
                {
                    GetWindow<PrefabOpenListener>().Show();
                    return;
                }

                Selection.objects = findGameObject.ToArray();
            }

            private void OnGUI()
            {
                GUILayout.Label("该预制体内已经没有不符合规范的Object了");
                if (GUILayout.Button("确定"))
                {
                    GetWindow<PrefabOpenListener>().Close();
                }
            }
        }
    }
}
#endif