#if UNITY_EDITOR
namespace Sirenix.OdinInspector.Demos
{
    using Sirenix.Utilities;
    using UnityEditor;

    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Objects")]
    public class FileSizeConfig : GlobalConfig<FileSizeConfig>
    {
        [ShowInInspector]
        [LabelText("update.json tmp path:")]
        public  string UpdatePath
        {
            get
            {
                return FileSizeMgr.UpdatePath;
            }
            set
            {
                FileSizeMgr.UpdatePath = value;
            }
        }
        [ShowInInspector]
        [LabelText("update.json url:")]
        public  string UpdateUrl
        {
            get
            {
                return FileSizeMgr.UpdateUrl;
            }
            set
            {
                FileSizeMgr.UpdateUrl = value;
            }
        }
        [ShowInInspector]
        [LabelText("file.txt tmp path:")]
        public  string filePath
        {
            get
            {
                return FileSizeMgr.filePath;
            }
            set
            {
                FileSizeMgr.filePath = value;
            }
        }
        [Button]
        void Reset()
        {
            FileSizeMgr.Reset();
        }
    }
}
#endif
