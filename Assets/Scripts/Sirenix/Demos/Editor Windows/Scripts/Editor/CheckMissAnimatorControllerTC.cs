#if UNITY_EDITOR
using Sirenix.OdinInspector;
using SuperTools.Editor;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

namespace Assets.Scripts.Sirenix.Demos.Editor_Windows.Scripts.Editor.ToolObj
{
    [System.Serializable]
    public class CheckMissAnimatorControllerTC
    {
        [HideInInspector]
        public const string TIPS = "检查Animator丢失controller";
        public List<GoTObj> fix_list = new List<GoTObj>();

        [Button]
        void Check()
        {
            fix_list.Clear();
            var guids = UnityEditor.AssetDatabase.FindAssets("t:GameObject", new string[] { });
            ToolUti.Iter(guids, (g) =>
            {
                var asset_path = AssetDatabase.GUIDToAssetPath(g);
                var ai = AssetDatabase.LoadMainAssetAtPath(asset_path) as GameObject;
                if (
                    ai
                    )
                {
                    var tfs = ai.GetComponentsInChildren<Animator>(true);

                    foreach (var c in tfs)
                    { 
                        if (c.runtimeAnimatorController==null)
                        {
                            var tobj = new GoTObj();
                            tobj.da = c.gameObject;
                            if (tobj.path.ToLower().EndsWith(".fbx")) continue;
                            fix_list.Add(tobj);
                            //break;
                        } 
                    }
                }
                return asset_path;
            }, () =>
            {
                "".Print("finish");

            });

        }

        [Button]
        void FixAll()
        {
            var list = new List<string>();

            foreach (var go in fix_list)
            {
                var ggg = go._da as GameObject;
                if(ggg)
                {
                    var ani = ggg.GetComponent<Animator>();
                    if (ani && ani.runtimeAnimatorController == null)
                    {
                        GameObject.DestroyImmediate(ani);
                        var tggg = PrefabUtility.FindRootGameObjectWithSameParentPrefab(ggg);
                        var pggg = PrefabUtility.GetPrefabParent(tggg) ;

                        PrefabUtility.ReplacePrefab(tggg, pggg);
                        pggg = PrefabUtility.GetPrefabParent(tggg);
                        EditorUtility.SetDirty(pggg);
                        //GameObject.DestroyImmediate(tggg);

                        list.Add(go.path);
                        //break;
                    }

                }
            }
            UIHelper.ImportAssets(list.ToArray());
            "".PrintError(UIHelper.ToJson(list));
            ClearDirtyInScene();
        }
        [Button]
        void ClearDirtyInScene()
        {
            var fix_list = GoTObj.dirtyList;
            foreach (var ago in fix_list)
            {
                //"".PrintError(ago.path,ago.subpath);

                var ob = ago as GameObject;
                if (!ob) continue;

                var go = PrefabUtility.FindRootGameObjectWithSameParentPrefab(ob);
                if(go)
                {
                    GameObject.DestroyImmediate(go);
                }
            }
            GoTObj.dirtyList.Clear();
            AssetDatabase.Refresh();
        }

    }
}

#endif