#if UNITY_EDITOR
namespace Sirenix.OdinInspector.Demos
{
    using UnityEngine;
    using UnityEditor;
    using Sirenix.Utilities;
    using System.Collections.Generic;
    using System;
    using System.IO;
    using System.Net;
    using System.Text.RegularExpressions;
    using War.Base;
    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Objects")]

    public class CharacterResConfig : GlobalConfig<CharacterResConfig>
    {
        private List<string> allCharacters;
        private Dictionary<string, List<string>> dic;

        [Serializable]
        public class Proto { }

        [Serializable]
        public class StrProto : Proto
        {

            [HideLabel]
            [HorizontalGroup]
            public string name;

            [HorizontalGroup("Prop")]
            public int size;
            [HorizontalGroup("Prop")]
            public int texSize;

            [HorizontalGroup("Prop")]
            [Button]
            void Locate()
            {
                var ass = AssetDatabase.LoadMainAssetAtPath(name);
                if (ass)
                {
                    Selection.activeObject = ass;
                }
            }
        }

        [Serializable]
        public class Proto1 : Proto
        {
            [HideLabel]
            [HorizontalGroup]
            public string name;

            //[HorizontalGroup]
            //[Button]
            //void GetResConfig()
            //{

            //}
        }

        public string UpdateUrl = "http://172.18.2.24/rogue_patch/Android/resource/482/files.txt";
        [Button()]
        void UpdateFileTxt()
        {
            string path = "Assets/Resources/files.txt";
            if (DownloadFile(UpdateUrl, path))
            {
                var dic = new Dictionary<string, long>();
                if (string.IsNullOrEmpty(path) == false)
                {
                    string files = File.ReadAllText(path);
                    var hashC = hashCheck.Parse(files);

                    foreach (var abname in hashC.list.Keys)
                    {
                        dic[abname] = hashC.GetSize(abname);
                    }

                }
                File.WriteAllText("Assets/Resources/ABSize.json", UIHelper.ToJson(dic));
            }
            else
            {
                Debug.LogError("files.txt更新失败");
            }
        }


        public string characterAssets = "Assets/Animations/Characters";

        [Serializable]
        public class Proto2 : Proto
        {
            [HideLabel]
            [HorizontalGroup]
            public string name;

            [HorizontalGroup]
            public int size;

            [VerticalGroup]
            public List<Proto1> pathList;

            [VerticalGroup]
            public List<StrProto> resConfig;

            //[HorizontalGroup]
            //[Button]
            //void GetAllRes()
            //{
            //}
        }
        
        public List<Proto2> CharacterList;

        private bool DownloadFile(string URL, string filename)
        {
            try
            {
                HttpWebRequest Myrq = (System.Net.HttpWebRequest)System.Net.HttpWebRequest.Create(URL);
                HttpWebResponse myrp = (System.Net.HttpWebResponse)Myrq.GetResponse();
                Stream st = myrp.GetResponseStream();
                Stream so = new System.IO.FileStream(filename, System.IO.FileMode.Create);
                byte[] by = new byte[1024];
                int osize = st.Read(by, 0, (int)by.Length);
                while (osize > 0)
                {
                    so.Write(by, 0, osize);
                    osize = st.Read(by, 0, (int)by.Length);
                }
                so.Close();
                st.Close();
                myrp.Close();
                Myrq.Abort();
                return true;
            }
            catch (System.Exception e)
            {
                return false;
            }
        }

        [Button()]
        void GetAllCharacters()
        {
            var path = "Assets/Animations/Characters";
            var dirs = Directory.GetDirectories(path);
            var match = new Regex(@"[0-9]+_(.*)", RegexOptions.Singleline);
            var listChname = new List<string>(dirs.Length);
            foreach (var d in dirs)
            {
                var m = match.Match(d);
                if (m.Success)
                {
                    var chname = m.Groups[1];
                    listChname.Add(chname.ToString());
                }
            }

            dic = new Dictionary<string, List<string>>();
            foreach (var chname in listChname)
            {
                if (dic.ContainsKey(chname) == false)
                {
                    dic[chname] = new List<string>();
                }
            }

            var abnames = AssetDatabase.GetAllAssetBundleNames();
            foreach (var abn in abnames)
            {
                if (abn.EndsWith(".prefab") && (abn.Contains("edit_") == false)) continue;
                if (!(abn.EndsWith(".playable") || abn.EndsWith(".prefab"))) continue;

                var split = abn.Split('_', '.');
                foreach (var sp in split)
                {
                    if (dic.ContainsKey(sp))
                    {
                        if (dic[sp].Contains(abn) == false)
                        {
                            dic[sp].Add(abn);
                        }
                    }
                }
            }

            var textAsset = Resources.Load<TextAsset>("ABSize");
            var sizeConfig = UIHelper.ToObj<Dictionary<string, int>>(textAsset.text);

            CharacterList = new List<Proto2>();
            foreach (var chname in dic.Keys)
            {
                if (dic[chname].Count <= 0 )
                {
                    continue;
                }

                var v = dic[chname];
                var listA = new List<string>();
                foreach (var vv in v)
                {
                    listA.AddRange(AssetDatabase.GetAssetPathsFromAssetBundle(vv));
                }
                var ds = AssetDatabase.GetDependencies(listA.ToArray(), true);
                long total = 0;
                listA.Clear();

                var sizeDic = new Dictionary<string, int>();
                foreach (var p in ds)
                {
                    var ab = AssetDatabase.GetImplicitAssetBundleName(p);
                    if (string.IsNullOrEmpty(ab)) continue;
                    if (listA.Contains(ab) == false)
                    {
                        listA.Add(ab);
                    }
                    var size = 0;
                    if (sizeConfig.ContainsKey(ab))
                    {
                        size = sizeConfig[ab];
                    }
                    sizeDic[p] = size;
                }
                foreach (var ab in listA)
                {
                    if (ab.Contains("shaders_for_warmup")) continue;
                    if (sizeConfig.ContainsKey(ab))
                    {
                        total += sizeConfig[ab];
                    }
                }

                List<Proto1> cfgList = new List<Proto1>();
                for (int i = 0; i<dic[chname].Count; i++)
                {
                    cfgList.Add(new Proto1() { name = dic[chname][i]});
                }

                List<StrProto> list = new List<StrProto>();
                foreach (var key in sizeDic.Keys)
                {
                    list.Add(new StrProto() { name = key.ToString(), size = (int)sizeDic[key] });
                }

                List<StrProto> resCfg = list;
                resCfg.Sort((a, b) => (int)b.size - (int)a.size);

                CharacterList.Add(new Proto2() { name = chname.ToString(), size = (int)total, pathList = cfgList, resConfig = resCfg });
            }
            CharacterList.Sort((a, b) => (int)b.size - (int)a.size);
        }
    }
}
#endif
