#if UNITY_EDITOR

using Sirenix.OdinInspector;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using UnityEditor;
using UnityEngine;

namespace Assets.Scripts.Sirenix.Demos.Editor_Windows.Scripts.Editor.ToolObj
{
    [System.Serializable]
    public class CheckFeedbackTC {

        [HideInInspector]
        public const string TIPS = "CheckFeedbackTC";
        public string path;

        public List<string[]> steps = new List<string[]>()
        {
            new string[]{"","total"},
            new string[]{ "update_file_start", "开始files下载"},
            new string[]{ "update_file_finish", "files下载完成"},
            new string[]{ "manifest加载完成",""},
            new string[]{ "luascript_load", ""},
            new string[]{ "加载lua组件完成", ""},
            new string[]{ "remote_files", ""},
            new string[]{ "server_info数据请求成功", ""},
            new string[]{ "CheckFinishAll\t1", "登录前更新"},
            new string[]{ "CheckFinishAll\t2", "lua补充资源热更"},
            new string[]{ "CheckFinishAll\t3", "服务器列表"},
            new string[]{ "CheckFinishAll\t4", "PID配置"},
            new string[]{ "CheckFinishAll\t5", "MiniGameEndCb"},


            new string[]{ "CheckFinishAll 1", "登录前更新"},
            new string[]{ "CheckFinishAll 2", "lua补充资源热更"},
            new string[]{ "CheckFinishAll 3", "服务器列表"},
            new string[]{ "CheckFinishAll 4", "PID配置"},
            new string[]{ "CheckFinishAll 5", "MiniGameEndCb"},

            new string[]{ "init_game", ""},
            new string[]{ "can not load", ""},
            new string[]{ "on_one_callback_error", ""},
            new string[]{ "allcomplete", "lua阶段初始化完成"},
            new string[]{ "show_choose_area_login", "打开登录选区界面"},
            new string[]{ "choose_area", "登录选区选择大区"},
            new string[]{ "init_preload", ""},
            new string[]{ "open_login_window", ""},
            new string[]{ "开始自动登录", ""},
            new string[]{ "开始游客登录", ""},
            new string[]{ "开始sdk登录", ""},
            new string[]{ "SDK登录回调", ""},
            new string[]{ "最近登录记录获取成功", ""},
            new string[]{ "SdkLoginError:", ""},
            new string[]{ "maintain:true", ""},
            new string[]{ "ShowServerMaintenance true", ""},
            new string[]{ "开始连接网关", ""},
            new string[]{ "发送网关握手消息", ""},
            new string[]{ "收到握手成功消息", ""},
            new string[]{ "连接服务器成功", ""},
            new string[]{ "发送登录消息", ""},
            new string[]{ "stLoginData", ""},
            new string[]{ "登录回复返回的值", ""},
            new string[]{ "登录收到角色数据", ""},
            new string[]{ "场景加载完成", ""},
            new string[]{ "AutoLoginUI Close", ""},
            new string[]{ "main_data_ready_complete", "城建数据准备完成"},
            new string[]{ "main_res_load_complete", "城建资源准备完成"},
            new string[]{ "open_main_slg", ""},
            new string[]{ "close_login_loading", ""},
            
            //new string[]{"","total"},
            //new string[]{ "update_file_start", "开始files下载"},
            //new string[]{"manifest加载完成",""},
            //new string[]{ "加载lua组件完成", ""},
            //new string[]{ "CheckFinishAll\t1", "登录前更新"},
            //new string[]{ "CheckFinishAll\t2", "lua补充资源热更"},
            //new string[]{ "CheckFinishAll\t3", "服务器列表"},
            //new string[]{ "CheckFinishAll\t4", "PID配置"},
            //new string[]{ "init_game", ""},
            //new string[]{ "allcomplete", "lua阶段初始化完成"},
            //new string[]{ "show_choose_area_login", "打开登录选区界面"},
            //new string[]{ "choose_area", "登录选区选择大区"},
            //new string[]{ "开始自动登录", ""},
            //new string[]{ "开始游客登录", ""},
            //new string[]{ "开始sdk登录", ""},
            //new string[]{ "SDK登录回调", ""},
            //new string[]{ "最近登录记录获取成功", ""},
            //new string[]{ "SdkLoginError:", ""},
            //new string[]{ "maintain:true", ""},
            //new string[]{ "开始连接网关", ""},
            //new string[]{ "发送网关握手消息", ""},
            //new string[]{ "收到握手成功消息", ""},
            //new string[]{ "连接服务器成功", ""},
            //new string[]{ "发送登录消息", ""},
            //new string[]{ "stLoginData", ""},
            //new string[]{ "登录回复返回的值", ""},
            //new string[]{ "登录收到角色数据", ""},
            //new string[]{ "checkCloseTicker", ""},
            //new string[]{ "场景加载完成", ""},
            //new string[]{ "开始加载大厅", ""},
            //new string[]{ "大厅加载完成", ""},
            //new string[]{ "进入完成，time", ""},
            //new string[]{ "AutoLoginUI Close", ""},
        };
        [Button]
        public string Check(Action<object> onfinish = null)
        {
            if(string.IsNullOrEmpty(path))
            {
                path = EditorUtility.OpenFolderPanel("打开日志存放目录",path,"");
            }
            var files = Directory.GetFiles(path, "*", SearchOption.AllDirectories);
            Dictionary<string, int> ctx = new Dictionary<string, int>();
            Dictionary<string, List<string>> filterList = new Dictionary<string, List<string>>();
            foreach (var f in files)
            {
                if (f.EndsWith(".zip")) continue;
                if (File.Exists(f)==false) continue;
                var content = File.ReadAllText(f);
                Dictionary<string, int> ccc = new Dictionary<string, int>();

                foreach (var s in steps)
                {
                    if (ccc.ContainsKey(s[0])) continue;
                    ccc[s[0]] = 1;
                    Filter(content, s, ctx, filterList,Path.GetFileName(f));
                }
            }
            var sb = new StringBuilder();
            var listSOut = new List<string>();
            foreach (var s in steps)
            {
                ctx.TryGetValue(s[0],out var count);
                sb.AppendLine(
                    string.Format("{0} {1}",
                        string.IsNullOrEmpty(s[1])
                            ? s[0] : s[1],
                        count));
                listSOut.Add(
                    string.Format("{0} {1}",
                        string.IsNullOrEmpty(s[1])
                            ? s[0] : s[1],
                        count));


            }
            var lout = new List<object>()
            {
                listSOut,
                filterList,
            };

            var keys = steps;
            for (int i = 0; i < keys.Count; i++)
            {
                string item = keys[i][0];
                string itemD = GetDisplayName(keys[i]);
                for (int j = i+1; j < keys.Count; j++)
                {
                    string item2 = keys[j][0];
                    string item2D = GetDisplayName(keys[j]);
                    if (item == item2) continue;
                    filterList.TryGetValue(item, out var fitem);
                    filterList.TryGetValue(item2, out var fitem2);

                    List<string> listUnlike = ToolUti.CalDiff(fitem, fitem2);
                    if(listUnlike.Count>0)
                    {
                        filterList[string.Format("{0}|{1}", itemD, item2D)] = listUnlike;
                    }
                }
            }

            string result = ToolUti.ToJson(lout);
            LogHelp.clipboard = result;
            "".Print("\n", LogHelp.clipboard);
            return result;
        }


        string GetDisplayName(string[] s)
        {
            return string.IsNullOrEmpty(s[1])
                            ? s[0] : s[1];
        }


        void Filter(string content, string[] mark, Dictionary<string, int> ctx, Dictionary<string, List<string>> filterList = null, string filename = "")
        {
            string mark0 = mark[0];
            if (content.Contains(mark0))
            {
                var i = 0;
                ctx.TryGetValue(mark0, out i);
                ctx[mark0] = i + 1;
                if(filterList!=null)
                {
                    filterList.TryGetValue(mark0, out var listFile);
                    listFile = listFile ?? new List<string>();
                    filterList[mark0] = listFile;
                    listFile.Add(filename);
                }
            }
        }


    }
}

#endif