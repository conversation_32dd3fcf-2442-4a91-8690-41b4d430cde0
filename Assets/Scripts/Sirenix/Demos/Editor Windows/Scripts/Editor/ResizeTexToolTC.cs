#if UNITY_EDITOR
using Sirenix.OdinInspector;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;

namespace Assets.Scripts.Sirenix.Demos.Editor_Windows.Scripts.Editor.ToolObj
{
    [System.Serializable]
    public class ResizeTexToolTC
    {
        [HideInInspector]
        public const string TIPS = "ResizeTexToolTC";

        [Range(0, 2)]
        public float xSize = 1;
        [Range(0, 2)]
        public float ySize = 1;

        //[Title("半透明贴图修改尺寸后可能会导致半透明程度变化，请慎重修改")]
        [InlineEditor(InlineEditorModes.LargePreview)]
        public Texture2D tex;
        [InlineEditor(InlineEditorModes.LargePreview)]
        public Texture2D tex_slice; 
        [Button]
        void Exec()
        {
            if (tex == null) return;
            var path = AssetDatabase.GetAssetPath(tex); 
            Texture2D tex_new = new Texture2D((int)(tex.width * xSize), (int)(tex.height * ySize),TextureFormat.ARGB32,false);

            EditorHelp.ApplyUnReadTex____(tex, tex_new);


            //var bs = tex_new.EncodeToPNG();
            //File.WriteAllBytes(tmpPath, bs);
            //AssetDatabase.Refresh();
            //tex_slice = AssetDatabase.LoadMainAssetAtPath(tmpPath) as Texture2D;
            tex_new.alphaIsTransparency = tex.alphaIsTransparency;
            tex_slice = tex_new;
        }
        [Button]
        void Replace()
        {
            if (tex == null) return;
            if (tex_slice == null) return;

            var choice = EditorUtility.DisplayDialog("", "确定替换原图吗", "ok", "cancel");
            if (choice)
            {
                var path = AssetDatabase.GetAssetPath(tex);

                var bs = ToolUti.GetTexBytes(tex_slice);
                File.WriteAllBytes(path, bs);
                AssetDatabase.Refresh();
                tex = AssetDatabase.LoadMainAssetAtPath(path) as Texture2D;
            }
        }

    }
}

#endif