#if UNITY_EDITOR
using Sirenix.OdinInspector;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;

namespace Assets.Scripts.Sirenix.Demos.Editor_Windows.Scripts.Editor.ToolObj
{
    [System.Serializable]
    public class Slice9ToolTC
    {
        [HideInInspector]
        public const string TIPS = "Slice9ToolTC（九宫格切图工具）";

        public static List<string> sides = new List<string>
        {
            "horizonal",
            "vertical",
            "9slice",
        };
        [ValueDropdown("sides")]
        public string sides_type = "horizonal";


        //[OnValueChanged("CheckMin")]
        [ShowIf("CheckEnable", 3)]
        [Range(0, 1)]
        public float min;
        //[OnValueChanged("CheckMax")]
        [ShowIf("CheckEnable", 3)]
        [Range(0, 1)]
        public float max;


        //[OnValueChanged("CheckMin")]
        [ShowIf("CheckEnable", 4)]
        [Range(0, 1)]
        public float xmin;
        //[OnValueChanged("CheckMax")]
        [ShowIf("CheckEnable", 4)]
        [Range(0, 1)]
        public float xmax;
        //[OnValueChanged("CheckMin")]
        [ShowIf("CheckEnable", 4)]
        [Range(0, 1)]
        public float ymin;
        //[OnValueChanged("CheckMax")]
        [ShowIf("CheckEnable", 4)]
        [Range(0, 1)]
        public float ymax;


        [InlineEditor(InlineEditorModes.LargePreview)]
        public Texture2D tex;
        [InlineEditor(InlineEditorModes.LargePreview)]
        public Texture2D tex_slice;
        void CheckMin()
        {
            max = Mathf.Max(min, max);
        }
        void CheckMax()
        {
            min = Mathf.Min(min, max);
        }
        int CheckEnable(int op)
        {
            var ind = sides.IndexOf(sides_type);
            if (ind == 2) return 4;
            return 3;
            //return (ind & op) > 0;
        }
        [Button]
        void Slice()
        {
            if (tex == null) return;
            var path = AssetDatabase.GetAssetPath(tex);
            var tmpPath = string.Format("{0}/{1}_slice_{3}{2}", Path.GetDirectoryName(path), Path.GetFileNameWithoutExtension(path), Path.GetExtension(path), sides_type);
            Texture2D tex_new = null;
            switch (sides_type)
            {
                case "horizonal":
                    tex_new = ToolUti.SliceTex2Tex(tex, min, max, 0, 1);
                    break;
                case "vertical":
                    tex_new = ToolUti.SliceTex2Tex(tex, 0, 1, min, max);
                    break;
                default:
                    tex_new = ToolUti.SliceTex2Tex(tex, xmin, xmax, ymin, ymax);
                    break;
            }


            //var bs = tex_new.EncodeToPNG();
            //File.WriteAllBytes(tmpPath, bs);
            //AssetDatabase.Refresh();
            //tex_slice = AssetDatabase.LoadMainAssetAtPath(tmpPath) as Texture2D;
            tex_new.alphaIsTransparency = tex.alphaIsTransparency;
            tex_slice = tex_new;
        }
        [Button]
        void Replace()
        {
            if (tex == null) return;
            if (tex_slice == null) return;

            var choice = EditorUtility.DisplayDialog("", "确定替换原图吗", "ok", "cancel");
            if (choice)
            {
                var path = AssetDatabase.GetAssetPath(tex);

                var bs = ToolUti.GetTexBytes(tex_slice);
                File.WriteAllBytes(path, bs);
                AssetDatabase.Refresh();
                tex = AssetDatabase.LoadMainAssetAtPath(path) as Texture2D;
            }
        }

    }
}

#endif