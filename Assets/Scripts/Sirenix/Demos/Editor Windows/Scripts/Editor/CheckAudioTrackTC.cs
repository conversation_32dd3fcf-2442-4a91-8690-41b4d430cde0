#if UNITY_EDITOR

using Sirenix.OdinInspector;
using SuperTools.Editor;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.Timeline;

namespace Assets.Scripts.Sirenix.Demos.Editor_Windows.Scripts.Editor.ToolObj
{
    [System.Serializable]
    public class CheckAudioTrackTC
    {
        [HideInInspector]
        public const string TIPS_CHECK_AUDIO = "检查Audio配置";

        public List<AssetTObj> fix_list = new List<AssetTObj>();

        public bool dofix = false;
        [Button]
        void check()
        {
            var guids = UnityEditor.AssetDatabase.FindAssets("t:TimelineAsset", new string[] { });

            foreach (var g in guids)
            {
                var asset_path = AssetDatabase.GUIDToAssetPath(g);
                var ai = AssetDatabase.LoadMainAssetAtPath(asset_path) as TimelineAsset;
                if (
                    ai
                    )
                {
                    var ots = ai.outputs;
                    var list = new List<TrackAsset>();
                    foreach (var ot in ots)
                    {
                        if (ot.sourceObject == null)
                        { 

                            "".Print(ot, "sourceObject null", ot.streamName, asset_path);
                        }

                    } 

                    var otrs = ai.GetOutputTracks();
                    foreach (var ott in otrs)
                    {
                        if(ott.GetType().Name == "MarkerTrack")
                        {
                            "".Print(asset_path, ott.GetType().Name);
                            list.Add(ott);
                        }
                    }
                    if(dofix)
                    foreach (var t in list)
                    {
                        ai.DeleteTrack(t);
                    }
                    EditorUtility.SetDirty(ai);
                    //AssetDatabase.SaveAssets();
                    var c1 = new List<PlayableBinding>(ai.outputs).Count;
                    var c2 = new List<TrackAsset>(ai.GetOutputTracks()).Count;
                    if (c1!=c2)
                    {
                        "".Print(asset_path, c1, c2);

                    }
                    //fix_list.Add(new AssetTObj()
                    //{
                    //    path = asset_path
                    //});
                    //"".Print("check_audio loadtype == UnityEngine.AudioClipLoadType.DecompressOnLoad", asset_path);
                }
            }
        }

    }
}

#endif