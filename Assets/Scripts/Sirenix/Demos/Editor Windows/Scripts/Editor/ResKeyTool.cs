#if UNITY_EDITOR
namespace Sirenix.OdinInspector.Demos
{
    using UnityEngine;
    using Sirenix.Utilities;
    using War.Common;
    using UnityEditor;
    using System.IO;

    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Editor/Config")]
    public class ResKeyTool : GlobalConfig<ResKeyTool>
    {
        [InfoBox("在Ressources下生成二进制key")]
        public string mark;
        [Toolt<PERSON>("字段名")]
        [OnValueChanged("FromKey")]
        public string key;
        public string value;

        public ResKey keyCollection;

        [But<PERSON>("From Key")]
        void FromKey()
        {
            if (!keyCollection) return;

            var keys = keyCollection.keys;
            var v = keys.Find((p) => p.key == key);
            if (v != null)
            {
                value = System.Text.UTF8Encoding.Default.GetString(v.value);
            }
            else
            {
                value = "novalue!!";
            }
        }
        [But<PERSON>("CreateKey")]
        void CreateKey()
        {
            var path =  "Assets/Resources/bconfig.asset";
            if (!System.IO.File.Exists(path))
            {
                EditorHelp.CheckDir(path);
                var rk = ScriptableObject.CreateInstance<ResKey>();
                rk.keys = new System.Collections.Generic.List<ResKey.KeyValuePair>();
                UnityEditor.AssetDatabase.CreateAsset(rk, path);
            }
            var rk1 = AssetDatabase.LoadAssetAtPath<ResKey>(path);
            if (!rk1) return;
            keyCollection = rk1;
            Selection.activeObject = rk1;
        }
        [Button("AddKey")]
        void AddKey()
        {
            if(!AddKey(keyCollection, key, value))
            {
                return;
            }

            UnityEditor.EditorUtility.SetDirty(keyCollection);
            UnityEditor.AssetDatabase.SaveAssets();
            UnityEditor.AssetDatabase.Refresh();
        }

        public static bool AddKey(ResKey reskey, string key, string value)
        {
            if (!reskey) return false;

            var keys = reskey.keys;
            var v = keys.Find((p) => p.key == key);
            if (v == null)
            {
                v = new ResKey.KeyValuePair();
                v.key = key;
                keys.Add(v);
            }
            v.value = System.Text.UTF8Encoding.Default.GetBytes(value);
            return true;
        }

        [BoxGroup("Xor")]
        [FolderPath(AbsolutePath=true)]
        public string[] folders;
        [BoxGroup("Xor")]
        public string fileMark="*";
        [BoxGroup("Xor")]
        [Button("Xor")]
        void Xor()
        {

            var workparam = ResKey.Instance.GetInt("worker");
            foreach (var folder in folders)
            {
                var files = Directory.GetFiles(folder, fileMark, SearchOption.AllDirectories);
                foreach (var f in files)
                {
                    var content = File.ReadAllBytes(f);
                    var length = content.Length;
                    Debug.Log(string.Format("ResKey.CalContent:{0} :{1} :{2}",f, length,workparam));
                    ResKey.CalContent(content, length, workparam);
                    File.WriteAllBytes(f, content);
                }
            }
        }
    }
}
#endif
