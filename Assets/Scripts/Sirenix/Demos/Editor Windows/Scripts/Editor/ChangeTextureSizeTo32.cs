using System.IO;
using System.Reflection;
using System.Text;
using UnityEngine;
using UnityEngine.Profiling;

#if UNITY_EDITOR
namespace Sirenix.OdinInspector.Demos
{
    using Sirenix.Utilities;
    using System;
    using System.Collections.Generic;
    using UnityEditor;


    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Objects")]
    public class ChangeTextureSizeTo32 : GlobalConfig<ChangeTextureSizeTo32>
    {
        [StringPopup(new string[] { "32", "128", "256", "512", "1024", "2048" })]
        public string pictureSize = "32";
        public bool isMustSet = true;
        [FolderPath]
        public List<string> findassets = new List<string>
        {
        };
        [FolderPath]
        public List<string> cullassets = new List<string>
        {
        };
        [Button]
        void ChangeSize()
        {
            if (findassets.Count <= 0)
            {
                Debug.Log("请选择路径");
                return;
            }
            var textureList = AssetDatabase.FindAssets("t:texture2D", findassets.ToArray());

            int count = textureList.Length;
            for (int i = 0; i < count; i++)
            {
                string gid = textureList[i];
                var path = AssetDatabase.GUIDToAssetPath(gid);
                if (CheckIfNotCullFolder(path))
                {
                    var texture = AssetDatabase.LoadAssetAtPath<Texture2D>(path);
                    int size = int.Parse(pictureSize);
                    if (!isMustSet && texture.width <= size && texture.height <= size)
                    {
                        continue;
                    }
                    var textureImporter = TextureImporter.GetAtPath(AssetDatabase.GetAssetPath(texture)) as TextureImporter;
                    TextureImporterPlatformSettings textureImporterPlatformSettings = textureImporter.GetPlatformTextureSettings(BuildTarget.iOS.ToString());
                    if(textureImporterPlatformSettings.maxTextureSize > size)
                    {
                        textureImporterPlatformSettings.maxTextureSize = size;
                        textureImporter.SetPlatformTextureSettings(textureImporterPlatformSettings);
                    }
                    textureImporterPlatformSettings = textureImporter.GetPlatformTextureSettings(BuildTarget.Android.ToString());
                    if (textureImporterPlatformSettings.maxTextureSize > size)
                    {
                        textureImporterPlatformSettings.overridden = true;
                        textureImporterPlatformSettings.maxTextureSize = size;
                        textureImporter.SetPlatformTextureSettings(textureImporterPlatformSettings);
                    }
                    EditorUtility.SetDirty(textureImporter);
                    textureImporter.SaveAndReimport();

                }
            }
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

        }

        public bool CheckIfNotCullFolder(string path)
        {
            if (cullassets.Count <= 0) return true;
            for (int i = 0; i < cullassets.Count; i++)
            {
                if (path.Contains(cullassets[i])) return false;
            }
            return true;
        }
    }

}
#endif