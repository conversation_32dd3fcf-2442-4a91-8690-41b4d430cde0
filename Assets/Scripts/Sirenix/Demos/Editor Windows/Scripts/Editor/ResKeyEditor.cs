/// Credit drobina, w34edrtfg, playemgames 
/// Sourced from - http://forum.unity3d.com/threads/sprite-icons-with-text-e-g-emoticons.265927/

using System.Collections.Generic;
using System.IO;
using System.Text.RegularExpressions;
using UnityEditor;
using War.Common;

namespace UnityEngine.UI.Extensions
{
    [CustomEditor(typeof(ResKey))]
    public class ResKeyEditor : UnityEditor.Editor
    {
        string selectlabel = "";
        string showedScr = "";
        bool showList = true;

        readonly List<ResKey> channelResKey = new List<ResKey>();
        readonly List<KeyValuePair<string, string>> selectKeyValue = new List<KeyValuePair<string, string>>();


        public override void OnInspectorGUI()
        {
            //base.OnInspectorGUI();


            var t = target as ResKey;

            serializedObject.Update();
            var s = "";
            showList = EditorGUILayout.Toggle("showList", showList);

            if (showList)
            {
                for (int i = 0; i < t.keys.Count; i++)
                {
                    s += string.Format("{2}\tkey: {0} \t\tlength:{1}\n", t.keys[i].key, t.keys[i].value.Length, i);
                }
                EditorGUILayout.TextArea(s);

            }
            OnChannelResKeyInspectorGUI();

            GUILayout.Label("Maching Types");

            if (EditorGUILayout.DropdownButton(new GUIContent(selectlabel), FocusType.Passive))
            {

                var menu = new GenericMenu();

                for (int i = 0; i < t.keys.Count; i++)
                {
                    ResKey.KeyValuePair tt = t.keys[i];
                    string name = tt.key;
                    var isChecked = selectlabel == name;
                    menu.AddItem(new GUIContent(ObjectNames.NicifyVariableName(i + ":" + name)),
                        isChecked,
                        () =>
                        {
                            selectlabel = name;
                        });
                }

                menu.ShowAsContext();
            }

            //if (!string.IsNullOrEmpty(selectlabel) && showedScr != selectlabel)
            //{
            //    var rk = t.GetBytes(selectlabel);
            //    EditorGUILayout.TextArea(System.Text.Encoding.UTF8.GetString(rk));
            //}

            if (!string.IsNullOrEmpty(selectlabel))
            {
                var rk = t.GetBytes(selectlabel);
                EditorGUILayout.TextArea(System.Text.Encoding.UTF8.GetString(rk));
            }

            serializedObject.ApplyModifiedProperties();
        }

        public void OnChannelResKeyInspectorGUI()
        {

            if (!string.IsNullOrEmpty(selectlabel) && GUILayout.Button("Check Channel Resource"))
            {
                channelResKey.Clear();
                selectKeyValue.Clear();
                string reskeyFolderName = "ResKey_Channel";
                string resKeyWorkPath = $"./Assets/{reskeyFolderName}/";
                if (Directory.Exists(resKeyWorkPath))
                {
                    Directory.Delete(resKeyWorkPath, true);
                }
                Directory.CreateDirectory(resKeyWorkPath);

                var assetFileList = new Dictionary<string, string>();
                var match = new Regex(@"/channel_res/([^/]*)/", RegexOptions.Singleline);
                string[] files = Directory.GetFiles($"./channel_res/", $"{target.name}.asset", SearchOption.AllDirectories);
                foreach (var filePath in files)
                {
                    var _filePath = filePath.Replace("\\", "/");
                    var m = match.Match(_filePath);
                    if (!m.Success || m.Groups.Count < 2)
                    {
                        continue;
                    }
                    var packageName = m.Groups[1].ToString();

                    //因为有些包名以.meta作为文件夹名结尾会有导致文件夹会被删掉，所以在文件夹后面加_folder后缀
                    var desAssetPath = _filePath.Replace($"./channel_res/{packageName}/Assets/", $"{resKeyWorkPath}/{packageName}_folder/");
                    var folderPath = Path.GetDirectoryName(desAssetPath);


                    if (!Directory.Exists(folderPath))
                    {
                        Directory.CreateDirectory(folderPath);
                    }
                    File.Copy(_filePath, desAssetPath);
                    assetFileList[packageName] = desAssetPath.Replace("./", "");
                }
                if (assetFileList.Count > 0)
                {
                    AssetDatabase.Refresh();

                    foreach (var assetFile in assetFileList)
                    {
                        var reskey = AssetDatabase.LoadAssetAtPath<ResKey>(assetFile.Value);
                        if (reskey == null)
                        {
                            continue;
                        }
                        var value = reskey.Get(selectlabel);
                        selectKeyValue.Add(new KeyValuePair<string, string>(assetFile.Key, value));
                    }
                }
            }
            if (selectKeyValue.Count > 0)
            {
                selectKeyValue.Sort(SortBconfig);
                foreach (var item in selectKeyValue)
                {
                    EditorGUILayout.LabelField(item.Key, item.Value);
                }
            }
            EditorGUILayout.Space();
        }

        int SortBconfig(KeyValuePair<string, string> config1, KeyValuePair<string, string> config2)
        {
            int worker1;
            int worker2;
            int.TryParse(config1.Value, out worker1);
            int.TryParse(config2.Value, out worker2);
            return worker1.CompareTo(worker2);
        }

        public static List<string> Export(string folder, ResKey resKey, bool refresh = true)
        {
            "".Print("Export", folder);
            if (!System.IO.Directory.Exists(folder))
            {
                System.IO.Directory.CreateDirectory(folder);
            }
            List<string> files = new List<string>();
            foreach (var item in resKey.keys)
            {
                //"".Print("Exportss",folder, item.key);
                var p = folder + "/" + item.key + ".txt";
                System.IO.File.WriteAllBytes(p, item.value);
                files.Add(p);
            }
            if (refresh)
            {
                AssetDatabase.Refresh();
            }
            return files;
        }
    }
}
