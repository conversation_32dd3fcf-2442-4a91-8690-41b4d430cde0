#if UNITY_EDITOR
namespace Sirenix.OdinInspector.Demos
{
    using UnityEngine;
    using UnityEditor;
    using Sirenix.Utilities;
    using System.Collections.Generic;
    using System;
    using System.IO;

    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Objects")]
    public class Sprite2PackingTag : GlobalConfig<Sprite2PackingTag>
    {
        [Serializable]
        public class Proto { }

        [Serializable]
        public class StrProto : Proto
        {

            [HideLabel]
            [HorizontalGroup]
            public string name;
            [HorizontalGroup]
            public string spritePackingTag;
            [HorizontalGroup("2")]
            [HideLabel]
            public int filterMode;

            [HorizontalGroup]
            [Button]
            void Locate()
            {
                var ass = AssetDatabase.LoadMainAssetAtPath(name);
                if (ass)
                {
                    Selection.activeObject = ass;
                }
            }

        }
        [Sirenix.OdinInspector.FilePath] 
        public List<string> assetPaths = new List<string>{
            "Assets/UI",
            "Assets/Art/Maps"
        };

        public List<StrProto> SpriteList;

        //[OnValueChanged("OnFilter")]

        [HorizontalGroup("3")]
        public string filter;
        [HorizontalGroup("3")]
        [Button()]
        void OnFilter()
        {
            if (string.IsNullOrEmpty(this.filter)) return;
            var _filter = this.filter.ToLower();
            FilterSpriteList = SpriteList.FindAll((s) => s.spritePackingTag.ToLower().Contains(_filter) || s.name.ToLower().Contains(_filter) || (Enum.GetName(typeof(FilterMode), s.filterMode) != null && Enum.GetName(typeof(FilterMode), s.filterMode).ToLower().Contains(_filter)));
        }
        [InfoBox("输入point,bilinear,trilinear匹配filterMode\n输入packingtag匹配packingtag\n输入匹配资源名")]
        public List<StrProto> FilterSpriteList;
        [Button()]
        public void FindUsed()
        {
            var allSet = new Dictionary<string, bool>();
            var usedSet = new Dictionary<string, bool>();
            SpriteList.Clear();
            int startIndex = 0;
            var findassets = AssetDatabase.FindAssets("t:Sprite", assetPaths.ToArray());

            EditorApplication.update = delegate ()
            {
                try
                {
                    int count = findassets.Length;
                    for (int i = 0; i < count/*finda.Length*/; i++)
                    {

                        string gid = findassets[i];
                        var path = AssetDatabase.GUIDToAssetPath(gid);
                        bool isCancel = EditorUtility.DisplayCancelableProgressBar("Apply匹配资源中", path, (float)startIndex / (float)findassets.Length);
                        var ai = AssetImporter.GetAtPath(path) as TextureImporter;
                        if (ai)
                        {
                            var sp = new StrProto() { name = path, spritePackingTag = ai.spritePackingTag, filterMode = (int)ai.filterMode };
                            SpriteList.Add(sp);

                            //list.Add(string.Format("{0},{1},{2}", path, ai.spritePackingTag, ai.wrapMode));
                        }




                        startIndex++;
                        if (isCancel || startIndex >= findassets.Length)
                        {
                            EditorUtility.ClearProgressBar();
                            EditorApplication.update = null;
                            startIndex = 0;

                            Debug.Log("匹配结束");

                            return;
                        }
                    }
                }
                catch (Exception e)
                {
                    Debug.LogError(e.ToString());
                    EditorApplication.update = null;
                    EditorUtility.ClearProgressBar();
                }

            };
        }

    }
}
#endif
