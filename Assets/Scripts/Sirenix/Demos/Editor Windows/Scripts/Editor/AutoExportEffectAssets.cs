#if UNITY_EDITOR
//导出正式特效资源
namespace Sirenix.OdinInspector.Demos
{
    using UnityEngine;
    using UnityEditor;
    using Sirenix.Utilities;
    using System.IO;
    using System.Security.Cryptography;
    using System.Collections.Generic;
    using System;
    using War.UI;
    using UnityEngine.Timeline;
    using UnityEngine.Playables;
    using Newtonsoft.Json;
    using UnityEngine.PostProcessing;
    using System.Linq;
    using System.Text;
    using System.Text.RegularExpressions;
    using UnityEditor.TestTools.TestRunner.Api;

    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Objects")]
    public class AutoExportEffectAssets : GlobalConfig<AutoExportEffectAssets>
    {
        public static void CLog(object message)
        {
            string time = DateTime.Now.ToString("HH:mm:ss");
            Debug.Log($"[CLog] {time} ==================================================================={message}");
        }
        public static void ExportRes()
        {
            //导出之前先记录本地的common ,md5资源，
            CLog("Start Save Common Texture Before Export");
            if (!AutoExportEffectAssets.Instance.GenPrePackCommonTexture())
            {
                CLog("Faild to export !!!,PrePackTexture Faild");
                return;
            }
            CLog("Check Missing Ref Before Export");
            FindMissing(); //打包前统计丢失的资源
            CLog("Start Execute Export Effect");
            AutoExportEffectAssets.Instance.ExportByOneKey();
            CLog("Deleting Redundancy...");
            DeleteRedundancy(); //删除冗余资源
            CLog("Replace timeline Reference...");
            AutoExportEffectAssets.Instance.ReplaceTimeLineDependencies(); //timeline引用重设
            CLog("End Export"); 
            CLog("Check Missing Ref After Export");
            FindMissing(); //打包后统计丢失的资源
        }
        /// <summary>
        /// 外部调用一键导出，自动设置参数
        /// </summary>
        public void ExportByOneKey()
        { 
            string checkPath = "Assets/Art/Effects_Source";
            //string checkPath = "Assets/Art/Effects_Source/Prefabs/Buff/Effect_dahan_Buff01.prefab";  //测试路径
            string exportPath = "Assets/Art/Effects";
            string checkPathOfDependencies = "Assets/Art/Effects_Source";
            string timelineReplaceCheckPath = "Assets/Art/Effects_Source/Timeline_battle";
            List<string> extsLs = new List<string>() {".fbx",".anim",".controller",".mat",".prefab",".tga",".png",".jpg" };
            excludePath = "Assets/Art/Effects_Source/Prefabs/common"; // 通用资源不导出
            this.checkPath = checkPath;
            this.targetExportPath = exportPath;
            this.checkPathOfDependencies = checkPathOfDependencies;
            this.exts = extsLs;
            this.replaceCheckPath = timelineReplaceCheckPath;
            this.startExportEffectAssets();
            //ef.checkPath
        }
        [FoldoutGroup("ExportEffectAssets")]
        [FolderPath]
        public string checkPath = "";


        [FoldoutGroup("ExportEffectAssets")]
        [Sirenix.OdinInspector.FilePath]
        public List<string> filePaths = new List<string> { };


        [FoldoutGroup("ExportEffectAssets")]
        [FolderPath]
        public string targetExportPath = "";

        [FoldoutGroup("ExportEffectAssets")]
        [FolderPath]
        public string checkPathOfDependencies = ""; //依赖资源需要检测的范围，影响文件老旧md5的检测

        // [TextArea]
        // public string blackboard = "";

        // //[Bool]
        // [InfoBox("是否创建重复sprite的引用资源对象")]
        // [HideLabel]
        // public bool isCreateSameSpriteQuoteAsset = false; //是否创建重复sprite的引用资源对象，引用目标sprite为程序第一个遍历到的同md5文件

        // //[HorizontalGroup]
        // [InfoBox("是否删除已创建引用spriteAsset文件的原始文件")]
        // [HideLabel]
        // public bool isDeleteSameSprite = false; //是否删除已创建引用spriteAsset文件的原始文件

        // //[HorizontalGroup]
        // [InfoBox("是否替换cardAssets中重复的sprite")]
        // [HideLabel]
        // public bool isReplaceSameCardAssets = false; //是否替换cardAssets中重复的sprite
        [FoldoutGroup("ExportEffectAssets")]
        public List<ResObject> dependencies = new List<ResObject>();
        [FoldoutGroup("ExportEffectAssets")]
        //需要筛选的后缀名
        public List<string> exts = new List<string>();

        [FoldoutGroup("ExportEffectAssets")]
        //本次删掉的导出特效
        public List<string> deleteOutPutEffects = new List<string>();

        [FoldoutGroup("ExportEffectAssets")]
        //排除目录
        [FolderPath]
        public string excludePath = "";

        // [FoldoutGroup("ExportEffectAssets")]
        //不打入图集的贴图对应shader列表
        private List<string> excludeShaderOfAtlas = new List<string>();


        public Dictionary<string, Rect> textureAtlasRects = new Dictionary<string, Rect>();

        private string currentAtlasTexture = "";
        [FoldoutGroup("ExportEffectAssets")]
        public Dictionary<string, string> extAndTargetPath = new Dictionary<string, string>() {
                { ".fbx", "Models" },
                { ".anim", "Animations" },
                { ".controller", "Animations" },
                { ".mat", "Materials" },
                { ".prefab", "Prefabs" },
                { ".tga", "Textures" },
                { ".png", "Textures" },
                { ".jpg", "Textures" },
                
            };

        private Dictionary<UnityEngine.Object, string> newAssets = new Dictionary<UnityEngine.Object, string>();

        private const string changedMd5Name = "lastEffectChangedMd5.txt";
        private const string initialMd5Name = "effectMd5files.txt";

        private const string exportPrefabs = "exportPrefabs.txt";

        public Dictionary<string, List<string>> changedFileDic = new Dictionary<string, List<string>>();
        // [ShowInInspector]
        // public List<string> newAssetList = new List<string>();


        [FoldoutGroup("ExportEffectAssets")]
        [Button("开始导出资源")]
        void startExportEffectAssets()
        {
            excludeShaderOfAtlas = new List<string>(){
              "CBB/UIFangXing",
              "CBB/Additive_Mask",
              "CBB/Blended_Mask",
              "CBB/Dissolution_Additive",
              "CBB/Dissolution_Blend",
              "CBB/CBB_niuqu",
              "CBB_NoCulling/Dissolution_Additive",
              "CBB_NoCulling/Blended_Mask",
              "CBB_NoCulling/Dissolution_Blend",
              "CBB/UIYuanXing_Blend",
              "CBB_NoCulling/CBB_niuqu",
              "CBB/Additive_Mask_JZB",
            };
            // string fileInfos = "";
            // long allfileCount = 0;
            //Debug.Log("checkPath:" + checkPath + "  !!!" + excludeShaderOfAtlas.Count + "_____" + excludeShaderOfAtlas[8] + "   " + excludeShaderOfAtlas.IndexOf("CBB/UIYuanXing_Blend"));
            //string oldFileDepenciesPath = "Assets/Art_old/Effects_Source"; //老资源目录，这个目录是苍穹前期放老资源用，特效有时候资源没有完全拷贝过新目录，依赖目录需要加上
            try
            {
                if (Directory.Exists(checkPath)) //多个文件处理
                {
                    List<string> ls = Directory.GetFiles(checkPath, "*.prefab", SearchOption.AllDirectories).ToList();
                    CLog($"Prefab GetFiles Count:{ls.Count}");
                    Dictionary<string, List<string>> dependenceDict = new Dictionary<string, List<string>>();
                    string fileContent = "";
                    string filePath = targetExportPath + "/oldPrefabs.txt";
                    string[] strs = fileContent.Split('\n');
                    filePath = Path.GetFullPath(filePath);
                    if (File.Exists(filePath))
                    {
                        fileContent = System.IO.File.ReadAllText(filePath);
                    }
                    for (int i = ls.Count - 1; i >= 0; i--)
                    {
                        if (string.IsNullOrEmpty(fileContent))
                        {
                            break;
                        }
                        string pp = Path.GetFileName(ls[i]).Split('.')[0];
                        Debug.LogError(pp);
                        if (fileContent.Contains(pp))
                        {
                            ls.RemoveAt(i);
                        }
                    }
                    string[] files = ls.ToArray();
                    for (int i = 0; i < files.Length; i++)
                    {
                        string[] dps = AssetDatabase.GetDependencies(files[i]);
                        dependenceDict.Add(files[i], dps.ToList<string>());
                        EditorUtility.DisplayProgressBar("预制体检查中", $"{i + 1}/{files.Length}", ((i + 1) * 1.0f) / files.Length);
                        CLog($"Prefab Checking:{i + 1}/{files.Length} {files[i]}");
                        ///加载当前所有预制体文件的依赖信息
                    }
                    ///输出所有预制体依赖，检查依赖是否发生变化，如果发生变化，则加入导出，否则跳过处理
                    Dictionary<string, List<string>> curSourceMd5 = GetMd5FileInfo("Assets/Art/Effects_Source");
                   // Dictionary<string, List<string>> curSourceOldMd5 = GetMd5FileInfo(oldFileDepenciesPath);

                    //foreach (KeyValuePair<string, List<string>> dict in curSourceOldMd5)
                    //{
                    //    if (!curSourceMd5.ContainsKey(dict.Key))
                    //    {

                    //        curSourceMd5.Add(dict.Key, dict.Value);
                    //    }
                    //    else
                    //    {
                    //        Debug.LogError($"{dict.Key} already exist in currentMd5Dict");
                    //    }
                    //} 

                    Dictionary<string, string[]> oldMd5 = ReadInitialMd5Names(targetExportPath);  //读取已保存的md5资源
                    if (oldMd5 == null)
                    {
                        oldMd5 = new Dictionary<string, string[]>();
                    }
                    List<string> diffRes = new List<string>();
                    foreach (KeyValuePair<string,List<string>> kv in curSourceMd5){ //当前所有资源的md5，需要与本地文件做对比确认是否有差异变化

                        if (oldMd5.ContainsKey(kv.Key)) //已有资源需要判断md5是否相同，如果相同则不需要重新导出
                        {
                            if (!oldMd5[kv.Key][0].Equals(kv.Value[0])) //资源md5有差异，需要重新导出对应的预制体
                            {
                                diffRes.Add(kv.Key);
                            }
                        } 
                        else  //如果没有的资源必定需要导出处理
                        {
                            diffRes.Add(kv.Key);
                        }

                    }
                    List<string> prefabNeedToExport = new List<string>();
                    ///遍历所有差异文件，找到对应依赖的预制体,加入到导出列表中
                    for(int i = 0; i < diffRes.Count; i++)
                    {
                        EditorUtility.DisplayProgressBar("预制体检查中", $"{i + 1}/{files.Length}", ((i + 1) * 1.0f) / files.Length);
                        CLog($"Diff Checking:{i + 1}/{diffRes.Count}");
                        foreach (KeyValuePair<string,List<string>> dpVal in dependenceDict)
                        {
                            if (dpVal.Value.Contains(diffRes[i]))
                            {
                                if (!prefabNeedToExport.Contains(dpVal.Key))
                                {
                                    prefabNeedToExport.Add(dpVal.Key);
                                }
                            }
                        }
                    }
                    CLog($"Prefab Export Count:{prefabNeedToExport.Count}");
                     
                    files = prefabNeedToExport.ToArray();
                    for (int i = 0; i < files.Length; i++)
                    {
                        dependencies.Clear();
                        checkPath = files[i];
                        StartCheckFiles(checkPath,true); 
                        EditorUtility.DisplayProgressBar("导出资源中", $"{i+1}/{files.Length}", ((i + 1) * 1.0f) / files.Length); 
                        CLog($"Exporting progress:{i + 1}/{files.Length} {checkPath}"); 
                    }
                    EditorUtility.ClearProgressBar();
                }
                else //单个文件处理
                {
                    dependencies.Clear();
                    StartCheckFiles(checkPath); 
                }

                //操作完毕需要执行写文件
                Dictionary<string, List<string>> currentmd5Dic = GetMd5FileInfo(checkPathOfDependencies);//, targetExportPath, initialMd5Name);
               
                //Dictionary<string, List<string>> md5OldDict = GetMd5FileInfo(oldFileDepenciesPath);

                //foreach(KeyValuePair<string,List<string>> dict in md5OldDict)
                //{
                //    if (!currentmd5Dic.ContainsKey(dict.Key))
                //    {

                //        currentmd5Dic.Add(dict.Key, dict.Value);
                //    }
                //    else
                //    {
                //        Debug.LogError($"{dict.Key} already exist in currentMd5Dict");
                //    }
                //}

                changedFileDic = CheckMd5(targetExportPath, currentmd5Dic);
                string changedLuaJson = UIHelper.ToJson(changedFileDic);
                CLog("BuildLuaPatch 变化的lua文件列表:" + changedLuaJson);
                //导出本次md5变化的信息
                File.WriteAllText(string.Format("{0}/{1}", targetExportPath, changedMd5Name), changedLuaJson);

                var dic = new Dictionary<string, object>();
                dic["list"] = currentmd5Dic;
                //导出本次md5信息
                File.WriteAllText(string.Format("{0}/{1}", targetExportPath, initialMd5Name), UIHelper.ToJson(dic));

            }
            catch(Exception e)
            {
                CLog("Error Export Effect!!!!!!!! please check "+e.ToString());
                Debug.LogError("异常抛出");
                EditorUtility.ClearProgressBar();
                AssetDatabase.StopAssetEditing();
            }

            string [] prefabsFile = Directory.GetFiles(this.targetExportPath + "/Effects", "*.prefab", SearchOption.AllDirectories);
            for (int i = 0; i < prefabsFile.Length; i++)
            {
                string pp = prefabsFile[i];
                SetABNameByPath(pp);
            }
        }

        [FoldoutGroup("ExportEffectAssets")]
        [Button("导出新老文件键值exportPrefab文件")]
        void startExportPrefabsKeyValueFile()
        {
            File.WriteAllText(string.Format("{0}/{1}", targetExportPath, exportPrefabs), JsonConvert.SerializeObject(dependencies));
        }
        public static void SetABNameByPath(string oPath)
        {
            if (File.Exists(oPath) == false) return;
            var assetPath = oPath;
            var assetImporter = AssetImporter.GetAtPath(assetPath);
            string assetBundleName = assetPath.Substring(assetPath.IndexOf("/") + 1);
            assetImporter.assetBundleName = assetBundleName;
        }
        // [Button]
        void setNewDependencies()
        {
            foreach (var item in dependencies)
            {
                if (item.newPath != null && item.newPath != "")
                {
                    Debug.LogError("SetNewResDependencies:" + item.newPath);
                    SetNewResDependencies(item.newPath);
                }
            }
        }

        /// 获取预制件依赖 <summary>
        /// 
        /// </summary>
        /// <typeparam name="T">欲获取的类型</typeparam>
        /// <param name="go"></param>
        /// <returns></returns>
        static List<T> GetPrefabDepe<T>(GameObject go)
        {
            List<T> results = new List<T>();
            UnityEngine.Object[] roots = new UnityEngine.Object[] { go };
            UnityEngine.Object[] dependObjs = EditorUtility.CollectDependencies(roots);
            foreach (UnityEngine.Object dependObj in dependObjs)
            {
                if (dependObj != null && dependObj.GetType() == typeof(T))
                {
                    results.Add((T)System.Convert.ChangeType(dependObj, typeof(T)));
                }
            }

            return results;
        }

        /// 获取预制件所有依赖 <summary>
        /// 
        /// </summary>
        /// <param name="go"></param>
        /// <returns></returns>
        static UnityEngine.Object[] GetPrefabAllDepe(GameObject go)
        {
            UnityEngine.Object[] roots = new UnityEngine.Object[] { go };
            UnityEngine.Object[] dependObjs = EditorUtility.CollectDependencies(roots);
            return dependObjs;
        }

        public List<ResObject> LoadExportPrefabDatas(string checkTargetPath)
        {
            string targetLuaMd5Path = string.Format("{0}/{1}", checkTargetPath, exportPrefabs);
            string targetFileContent = null;
            if (File.Exists(targetLuaMd5Path))
            {
                targetFileContent = File.ReadAllText(targetLuaMd5Path);
            }

            if (string.IsNullOrEmpty(targetFileContent))
            {
                Debug.Log("检查md5值失败：无法找到需要对比的lua md5信息文件，path:" + targetLuaMd5Path);
                return new List<ResObject>();
            }


            Dictionary<string, string[]> oldLuaFileDic = new Dictionary<string, string[]>();
            var tempNewestPatchFileDic = UIHelper.ToObj<List<ResObject>>(targetFileContent);
            return tempNewestPatchFileDic;
        }

        private List<string> textureNeedToSetAbName = new List<string>(); //需要根据路径设置anname的贴图，放到流程最后一步设置
        private List<string> textureNeedToClearAbName = new List<string>(); //需要根据路径删除误添加anname的贴图，放到流程最后一步
        public void StartCheckFiles(string targetPath,bool forceExportWithoutCheck = false)
        { 
            textureNeedToSetAbName.Clear();
            textureNeedToClearAbName.Clear();
            AssetDatabase.StartAssetEditing();
            List<FileInfo> allFileInfos = new List<FileInfo>();
            //判断给定的路径是否存在,如果不存在则退出
            if (filePaths != null && filePaths.Count > 0)
            {
                foreach (var itemPath in filePaths)
                {
                    FileInfo file = new FileInfo(itemPath);
                    allFileInfos.Add(file);
                    CLog("批量导出 文件:" + itemPath);
                }
            }
            else
            {
                if (!Directory.Exists(targetPath))
                {
                    // yield break;
                    //判断是否为单个文件
                    if (File.Exists(targetPath))
                    {
                        FileInfo file = new FileInfo(targetPath);
                        allFileInfos.Add(file);
                        Debug.Log(" 判断为单个文件:" + targetPath);
                    }
                    else
                    {
                        Debug.Log(" 无效文件:" + targetPath);
                        return;
                    }
                }
                else
                {
                    //判断路径为文件夹
                    DirectoryInfo di = new DirectoryInfo(targetPath);
                    allFileInfos.AddRange(di.GetFiles("*.prefab", SearchOption.AllDirectories));
                    Debug.LogError(" 判断为文件夹:" + targetPath);
                }
            }




            newAssets.Clear();
            // newAssetList.Clear(); 
            // long len = 0;
            //定义一个DirectoryInfo对象

            //读取已导出的特效预制体



            ///强制导出不需要 检查
            if (!forceExportWithoutCheck)
            {
                StartLoadExportPrefabDatas();
                // dependencies = LoadExportPrefabDatas(targetExportPath);

                //根据文件md5，检测相距上次导出资源有变化的文件列表，只重新导出有变化的文件
                Dictionary<string, List<string>> currentmd5Dic = GetMd5FileInfo(checkPathOfDependencies);//, targetExportPath, initialMd5Name);
                changedFileDic = CheckMd5(targetExportPath, currentmd5Dic);

                string changedLuaJson = UIHelper.ToJson(changedFileDic);
                //Debug.Log("BuildLuaPatch 变化的lua文件列表:" + changedLuaJson);
                //导出本次md5变化的信息
                //File.WriteAllText(string.Format("{0}/{1}", targetExportPath, changedMd5Name), changedLuaJson);
                var dic = new Dictionary<string, object>();
                dic["list"] = currentmd5Dic;

            }

          
            
            //导出本次md5信息
           // File.WriteAllText(string.Format("{0}/{1}", targetExportPath, initialMd5Name), UIHelper.ToJson(dic));
            //写文件放到最后一步
            List<SimpleResObject> exportPrefabList = new List<SimpleResObject>();

            string extension = "";

            Dictionary<string, bool> allDependencies = new Dictionary<string, bool>();
            ResObject resObject;
            foreach (FileInfo file in allFileInfos)
            {
                //屏蔽目录排除
                if (excludePath != "" && file.FullName.Replace('\\', '/').IndexOf(excludePath) >= 0 )
                {
                    CLog("excludePah will not export: file.FullName:" + file.FullName + " excludePath: " + excludePath);
                    Debug.LogError("屏蔽目录排除:file.FullName:" + file.FullName + " excludePath:" + excludePath);
                    continue;
                }
                extension = GetExtension(file.FullName);
                if (extension != ".prefab")
                {
                    continue;
                }
                bool isChanged = true;

                CLog("file.FullName:" + file.FullName);
                string separator = Path.DirectorySeparatorChar.ToString();
                string assetPath = file.FullName.Substring(file.FullName.IndexOf("Assets"+separator));
                var go = AssetDatabase.LoadAssetAtPath<GameObject>(assetPath);
                var sourceAssetPath = AssetDatabase.GetAssetPath(go);

                UnityEngine.Object[] depes = GetPrefabAllDepe(go);
                if (forceExportWithoutCheck)
                {
                    isChanged = true;
                 
                }
                else
                {
                   

                    //检测资源是否有变化，是否需要执行导出
                    if (!changedFileDic.ContainsKey(sourceAssetPath)) //预制体是否发生改变
                    {
                        isChanged = false;
                        //检查预制体依赖的资源是否发生了变化
                        foreach (var d in depes)
                        {
                            var depesPath = AssetDatabase.GetAssetPath(d);
                            if (changedFileDic.ContainsKey(depesPath))
                            {
                                isChanged = true;
                                break;
                            }
                        }
                        //如果引用键值队列表中没有对应的资源，为避免资源漏掉，需要导出
                        if (isChanged == false && GetNewObjectBy(go) == null)
                        {
                            CLog("引用键值队列没有该资源file.FullName:" + sourceAssetPath);
                            isChanged = true;
                        }
                    }

                }
             
                
                //如果资源没有发生变化，则跳过导出
                if (isChanged == false)
                {
                    CLog("md5没有变化，跳过导出：" + sourceAssetPath);
                    continue;
                }

                resObject = new ResObject()
                {
                    path = assetPath
                };
                string sourceExt = Path.GetExtension(file.FullName);
                string effectName = file.Name.Replace(sourceExt, "");
                //如果有文件夹,则删除旧资源；
                string targetRootPath = Path.Combine(targetExportPath, "Effects");
                targetRootPath = Path.Combine(targetRootPath, effectName);
                if (Directory.Exists(targetRootPath))
                {
                    SaveDirFilesGUID(targetRootPath); //保存删除前文件的guid
                    Directory.Delete(targetRootPath, true);
                }
                else
                {
                    dirGUIds = new Dictionary<string, string>(); //清空
                }
                //如果有文件夹后刷新
                UnityEditor.AssetDatabase.Refresh();
                allDependencies.Clear();
                foreach (var d in depes)
                {
                    var _path = AssetDatabase.GetAssetPath(d);
                    //检测后缀名
                    string ext = GetExtension(_path);
                    Debug.LogError("原始依赖！！依赖路径:" + _path + " ext:" + ext);

                    if (exts.IndexOf(ext) <= -1)
                    {
                        continue;
                    }

                    //排除不在原始目录内的依赖资源
                    if (_path.IndexOf(checkPathOfDependencies) < 0 || (excludePath != "" && _path.IndexOf(excludePath) >= 0))
                    {
                        Debug.LogError("排除不在原始目录内的依赖资源:" + _path + " checkPath:" + checkPathOfDependencies + " excludePath:" + excludePath);
                        continue;
                    }

                    //防止依赖资源重复执行，增加字典标识已执行资源
                    if (!allDependencies.ContainsKey(_path))
                    {
                        allDependencies[_path] = true;
                        Debug.LogError("依赖路径:" + _path + " _path:" + _path);
                        string exportPath = CopyResToExportPath(_path, effectName);

                        resObject.dependencies.Add(new DependencieRes()
                        {
                            path = exportPath
                        });
                    }
                }
                //如果当前资源是导出的主资源，则记录路径
                var exportMainPath = Path.Combine(targetExportPath, "Effects");
                exportMainPath = Path.Combine(exportMainPath, effectName);
                exportMainPath = Path.Combine(exportMainPath, extAndTargetPath[extension]);
                exportMainPath = Path.Combine(exportMainPath, file.Name);
                resObject.newPath = exportMainPath;
                exportPrefabList.Add(new SimpleResObject()
                {
                    path = assetPath,
                    newPath = exportMainPath
                });
                // Debug.LogError("newPath:" + exportMainPath);
                //如果列表中有该资源信息，则先移除现有资源
                CheckAndRemoveSameSourceAsset(assetPath);
                dependencies.Add(resObject);

                UnityEditor.AssetDatabase.Refresh();



                //将该特效依赖贴图合成图集
                var textureRootPath = Path.Combine(targetExportPath, "Effects");
                textureRootPath = Path.Combine(textureRootPath, effectName);
                textureRootPath = Path.Combine(textureRootPath, extAndTargetPath[".png"]);
                targetAtlasPath = textureRootPath;
                BuildAtlasImagePaths(targetAtlasPath);

            }
            AssetDatabase.StopAssetEditing(); 
            SetDirFilesGUID();
            AssetDatabase.Refresh();
            for (int i = 0; i < exportPrefabList.Count; i++)
            {
                var exportMainPath = exportPrefabList[i].newPath;
                if (exportMainPath != "")
                {
                    Debug.LogError("SetNewResDependencies:" + exportMainPath);
                    SetNewResDependencies(exportMainPath);
                    //设置abname
                    SetABNameByPath(exportMainPath);
                }
            }
            for(int i = 0; i < textureNeedToSetAbName.Count;i++)
            {
                var textureTargetPath = textureNeedToSetAbName[i]; 
                SetABNameByPath(textureTargetPath);
            }
            textureNeedToSetAbName.Clear();
            for(int i = 0; i < textureNeedToClearAbName.Count;i++)
            {
                var textureTargetPath = textureNeedToClearAbName[i]; 
                RemoveABNameByPath(textureTargetPath);
            }
            textureNeedToClearAbName.Clear();
            CLog("export effects complete!");
            startExportPrefabsKeyValueFile();
            // File.WriteAllText(string.Format("{0}/{1}", targetExportPath, exportPrefabs), JsonConvert.SerializeObject(dependencies));

        }
        public void RemoveABNameByPath(string oPath)
        {
            if (File.Exists(oPath) == false) return;
            var assetPath = oPath;
            var assetImporter = AssetImporter.GetAtPath(assetPath);
            assetImporter.assetBundleName = "";
        }
        /// <summary>
        /// 保存目录中已有文件的guid
        /// </summary>
        private Dictionary<string, string> dirGUIds = new Dictionary<string, string>();
        private void SaveDirFilesGUID(string dirs)
        {
            dirGUIds = new Dictionary<string, string>();
            string[] files = Directory.GetFiles(dirs, "*.*", SearchOption.AllDirectories);
            for(int i = 0; i < files.Length; i++)
            {
                string file = files[i];
                if (file.EndsWith(".meta"))
                {
                    continue;
                }
                string guid = AssetDatabase.AssetPathToGUID(file);
                dirGUIds.Add(file, guid);  
            }
        }
        /// <summary>
        /// 设置文件夹下meta的guid
        /// </summary>
        /// <param name="dirs"></param>
        private void SetDirFilesGUID()
        {
            foreach (KeyValuePair<string,string> kv in dirGUIds)
            {
                if (File.Exists(kv.Key)) //路径存在
                {
                    string guid = AssetDatabase.AssetPathToGUID(kv.Key);
                    if (kv.Value != guid)
                    {
                        string content = File.ReadAllText(kv.Key + ".meta");
                        content = content.Replace(guid, kv.Value);
                        File.WriteAllText(kv.Key+".meta",content);
                    }
                }
            }
        }

        public void CheckAndRemoveSameSourceAsset(string assetPath)
        {
            int count = dependencies.Count; 
            ResObject item = null;
            for (int i = count - 1; i >= 0; i--)
            {
                item = dependencies[i];
                if (item.path == assetPath)
                {
                    dependencies.RemoveAt(i);
                    break;
                }
            }

        }

        //执行导入新老资源对照信息
        [FoldoutGroup("ExportEffectAssets")]
        [Button("执行导入新老资源对照信息")]
        public void StartLoadExportPrefabDatas()
        {
            Dictionary<string, string[]> oldMd5FileDic = ReadInitialMd5Names(targetExportPath);
            if (oldMd5FileDic == null)
            {
                oldMd5FileDic = new Dictionary<string, string[]>();
            }
            deleteOutPutEffects.Clear();
            //读取已导出的特效预制体
            dependencies = LoadExportPrefabDatas(targetExportPath);
            int count = dependencies.Count;
            ResObject item = null;
            for (int i = count - 1; i >= 0; i--)
            {
                item = dependencies[i];

                //如果原始资源有为空的，需要删除导出的资源
                if (item.target == null)
                {
                    deleteOutPutEffects.Add(item.path);
                    FileInfo file_info = new FileInfo(item.newPath);
                    string ext = Path.GetExtension(file_info.FullName);
                    string effectName = file_info.Name.Replace(ext, "");
                    //如果有文件夹,则删除旧资源；
                    string targetDeleteRootPath = Path.Combine(targetExportPath, "Effects");
                    targetDeleteRootPath = Path.Combine(targetDeleteRootPath, effectName);
                    if (Directory.Exists(targetDeleteRootPath))
                    {
                        Directory.Delete(targetDeleteRootPath, true);
                    }
                    dependencies.RemoveAt(i);
                }
                //如果新资源有为空的，从列表中移除；并且在md5列表中标记移除;用于重新导出
                else if (item.newTarget == null)
                {
                    dependencies.RemoveAt(i);
                    string path = item.path.Replace('\\', '/');
                    if ((excludePath == "" || path.IndexOf(excludePath) == -1) && oldMd5FileDic.ContainsKey(path))
                    {
                        oldMd5FileDic.Remove(path);
                    }
                    Debug.LogError("清除无效资源：sourceTarget:" + path + "  isnull" + (item.target == null) + "newTarget:" + item.newPath + "  isnull" + 
                        (item.newTarget == null) + "  oldMd5FileDic.ContainsKey(path):" + oldMd5FileDic.ContainsKey(path) + "   path.IndexOf(excludePath):" + path.IndexOf(excludePath));
                }

            }
            var dic = new Dictionary<string, object>();
            dic["list"] = oldMd5FileDic;
            //导出修改后的md5信息
            File.WriteAllText(string.Format("{0}/{1}", targetExportPath, initialMd5Name), UIHelper.ToJson(dic));

            UnityEditor.AssetDatabase.Refresh();
        }

        public string GetExtension(string sourcePath)
        {
            string ext = Path.GetExtension(sourcePath);
            if (ext != null)
            {
                ext = ext.ToLower();
            }
            return ext;
        }

        //拷贝资源到导出目录
        public string CopyResToExportPath(string sourcePath, string effectName)
        {
            string ext = GetExtension(sourcePath);

            if (targetExportPath == "")
            {
                Debug.LogError("没有配置导出路径: targetExportPath");
                return "";
            }
            string targetRootPath = "";

            var sourceObject = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(sourcePath);
            //如果是图片，则判断是否超过256*256，如果超过，则当作公共资源
            if (sourceObject is Texture)
            {
                Texture texture = sourceObject as Texture;
                int textureSize = texture.width * texture.height;
                if (textureSize > 256 * 256)
                {
                    targetRootPath = Path.Combine(targetExportPath, "Common");
                    targetRootPath = Path.Combine(targetRootPath, extAndTargetPath[ext]);
                    string targetPath = CopyRes(sourcePath, targetRootPath);
                    textureNeedToSetAbName.Add(targetPath);
                    return targetPath;
                }
            }

            Debug.LogError("effectName:" + effectName);
            //拼接导出目录
            targetRootPath = Path.Combine(targetExportPath, "Effects");

            targetRootPath = Path.Combine(targetRootPath, effectName);
            targetRootPath = Path.Combine(targetRootPath, extAndTargetPath[ext]);

            string p = CopyRes(sourcePath, targetRootPath);
            if (sourceObject is Texture)
            {
                if (textureNeedToClearAbName.Contains(p) == false)
                    textureNeedToClearAbName.Add(p);
            }
            return p;
        }
        private Dictionary<string, bool> createNewDict = new Dictionary<string, bool>(); //每次调用
        //拷贝资源到导出目录
        public string CopyRes(string sourcePath, string targetRootPath, bool isNeedImportAsset = true)
        {
            if (!Directory.Exists(targetRootPath))
            {
                try
                {
                    Directory.CreateDirectory(targetRootPath);
                }
                catch (System.Exception ex)
                {

                    throw new System.Exception("创建目标失败：" + ex.Message);
                }
            }
            string targetPath = Path.Combine(targetRootPath, Path.GetFileName(sourcePath));
            targetPath = targetPath.Replace('\\', '/');
            if (sourcePath == targetPath)
            {
                Debug.LogError("跳过拷贝，拷贝资源路径相同：" + targetPath);
                return targetPath;
            }

            if (File.Exists(targetPath)) //已存在则直接复制资源
            {
                File.Copy(sourcePath, targetPath, true);
            }
            else
            { 
                AssetDatabase.CopyAsset(sourcePath, targetPath);
            }


            //UnityEditor.AssetDatabase.Refresh();
            if (isNeedImportAsset)
            {
                AssetDatabase.ImportAsset(targetPath);
            }
            var sourceGo = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(sourcePath);
            if(sourceGo != null)
            { 
                newAssets[sourceGo] = targetPath;
            }
            else
            {
                CLog("Error check !! SourceGo is null" + sourcePath);
                Debug.LogError("SourceGo is null" + sourcePath);
            }
            // var go = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(targetPath);
            // Debug.LogError("copy Res Name:" + targetPath + "  :" + go);
            // if (go == null)
            // {
            //     return "";
            // }

            // var goType = go.GetType();
            // Dictionary<int, string> assetList = new Dictionary<int, string>();
            // if (newAssets.ContainsKey(goType))
            // {
            //     assetList = newAssets[goType];
            //     assetList[sourceGo.GetInstanceID()] = targetPath;
            //     newAssets[goType] = assetList;
            // }
            // else
            // {

            // }

            // newAssetList.Add(targetPath);
            Debug.Log("新资源信息 完整路径：" + targetPath + "   原始资源：" + sourcePath);

            //复制贴图原始导入信息
            //if (sourceGo is Texture)
            //{
            //    TextureImporter targetImporter = AssetImporter.GetAtPath(targetPath) as TextureImporter;
            //    TextureImporter sourceImporter = AssetImporter.GetAtPath(sourcePath) as TextureImporter;
            //    targetImporter.mipmapEnabled = false;
            //    targetImporter.alphaIsTransparency = sourceImporter.alphaIsTransparency;
            //    targetImporter.alphaSource = sourceImporter.alphaSource;
            //    targetImporter.wrapMode = sourceImporter.wrapMode;
            //    targetImporter.SaveAndReimport();
            //}

            return targetPath;
        }



        //设置新资源引用
        public void SetNewResDependencies(string targetPath)
        {
            // FileInfo file_info = new FileInfo(targetPath);
            // DirectoryInfo dir_info = file_info.Directory;

            var go = AssetDatabase.LoadAssetAtPath<GameObject>(targetPath);
            Animator[] animators = go.GetComponentsInChildren<Animator>(true);
            RuntimeAnimatorController oldController;
            foreach (Animator animator in animators)
            {
                oldController = animator.runtimeAnimatorController;
                if (oldController == null)
                {
                    continue;
                }
                Debug.LogError("Animator替换新资源：" + targetPath + " 资源：" + oldController.name);
                RuntimeAnimatorController newController = GetNewAssetBy<RuntimeAnimatorController>(oldController);
                if (newController != null)
                    // AnimationClip clip = newController.animationClips[0];
                    //设置新引用
                    animator.runtimeAnimatorController = newController;
            }

            SkinnedMeshRenderer[] renderers = go.GetComponentsInChildren<SkinnedMeshRenderer>(true);
            Material oldMaterials;
            Material[] materials;
            // foreach (SkinnedMeshRenderer renderer in renderers)
            // {
            //     materials = renderer.sharedMaterials;
            //     int count = materials.Length;

            //     for (int i = 0; i < count; i++)
            //     {
            //         oldMaterials = materials[i];
            //         if (oldMaterials == null)
            //         {
            //             continue;
            //         }
            //         Material newMaterial = GetNewAssetBy<Material>(oldMaterials);
            //         if (newMaterial == null)
            //         {
            //             continue;
            //         }
            //         materials[i] = ReplaceTexture(newMaterial);
            //         UnityEditor.EditorUtility.SetDirty(newMaterial);
            //         Debug.LogError("SkinnedMeshRenderer- Material替换新资源：" + targetPath + " 资源：" + oldMaterials.name + " type:" + newMaterial);
            //     }
            //     renderer.sharedMaterials = materials;
            // }

            Renderer[] particleSystems = go.GetComponentsInChildren<Renderer>(true);
            // Material oldMaterials;
            // Material[] materials;
            //第一遍替换独立的texture
            Material newMaterial;
            foreach (Renderer particleSystem in particleSystems)
            {
                materials = particleSystem.sharedMaterials;
                int count = materials.Length;

                for (int i = 0; i < count; i++)
                {
                    oldMaterials = materials[i];
                    if (oldMaterials == null)
                    {
                        continue;
                    }
                    newMaterial = GetNewAssetBy<Material>(oldMaterials);
                    if (newMaterial == null)
                    {
                        continue;
                    }
                    materials[i] = ReplaceTexture(newMaterial);
                    Debug.LogError("SkinnedMeshRenderer- Material替换新资源：" + targetPath + " 资源：" + oldMaterials.name + " type:" + newMaterial);
                    // UnityEditor.EditorUtility.SetDirty(newMaterial);
                }
                particleSystem.sharedMaterials = materials;
                // UnityEditor.AssetDatabase.SaveAssets();
                // UnityEditor.AssetDatabase.Refresh();
                Animator anim; 
            }


            //图集子项数量大于0，则根据最终组成图集子项信息，创建图集
            if (atlasImagePaths.Count > 0)
            {
                CreateAtlasBy(targetAtlasPath, atlasImagePaths);
                //第二遍替换图集的texture

                foreach (Renderer particleSystem in particleSystems)
                {
                    materials = particleSystem.sharedMaterials;
                    int count = materials.Length;

                    for (int i = 0; i < count; i++)
                    {
                        newMaterial = materials[i];
                        if (newMaterial == null)
                        {
                            continue;
                        }
                        materials[i] = ReplaceAtlasTexture(newMaterial);
                        // Material newMaterial = GetNewAssetBy<Material>(oldMaterials);
                        // if (newMaterial == null)
                        // {
                        //     continue;
                        // }
                        Debug.LogError("SkinnedMeshRenderer- Material替换新资源：" + targetPath + " type:" + newMaterial);
                        // UnityEditor.EditorUtility.SetDirty(newMaterial);
                    }
                    particleSystem.sharedMaterials = materials;
                    // UnityEditor.AssetDatabase.SaveAssets();
                    // UnityEditor.AssetDatabase.Refresh();
                }
            }
            //取消pp
            PostProcessingBehaviour[] ppbs = go.GetComponentsInChildren<PostProcessingBehaviour>(true);
            foreach (var item in ppbs)
            {
                item.enabled = false;
            }


            UnityEditor.EditorUtility.SetDirty(go);
            UnityEditor.AssetDatabase.SaveAssets();
            UnityEditor.AssetDatabase.Refresh();
        }


        //替换材质球上所有的贴图(不包含图集)
        private Material ReplaceTexture(Material _mat)
        {
            if (_mat == null)
            {
                return _mat;
            }

            List<string> results = new List<string>();

            Shader shader = _mat.shader;
            for (int i = 0; i < ShaderUtil.GetPropertyCount(shader); ++i)
            {
                if (ShaderUtil.GetPropertyType(shader, i) == ShaderUtil.ShaderPropertyType.TexEnv)
                {
                    string propertyName = ShaderUtil.GetPropertyName(shader, i);
                    Texture tex = _mat.GetTexture(propertyName);
                    if (tex == null)
                    {
                        continue;
                    }
                    string texPath = AssetDatabase.GetAssetPath(tex.GetInstanceID());
                    Texture newTexture = GetNewAssetBy<Texture>(tex);
                    if (newTexture == null)
                    {
                        Debug.LogError("未获取到新的材质球贴图：" + _mat.name + " type:" + tex.GetType() + " texname:" + tex.name + " propertyName:" + propertyName);
                        continue;
                    }
                    //获取贴图合并图集之后的信息
                    string nexTexPath = AssetDatabase.GetAssetPath(newTexture);
                    var sourceOffset = _mat.GetTextureOffset(propertyName);
                    var sourceScale = _mat.GetTextureScale(propertyName);
                    //if (!string.IsNullOrEmpty(currentAtlasTexture) && atlasImagePaths.ContainsKey(nexTexPath) && //textureAtlasRects.ContainsKey(nexTexPath) &&
                    if (atlasImagePaths.IndexOf(nexTexPath) >= 0 && //textureAtlasRects.ContainsKey(nexTexPath) &&
                        sourceOffset.Equals(Vector2.zero) && sourceScale.Equals(Vector2.one) //判断原始贴图uv数据是否为默认0、0、1、1，如果不符合该要求，则不适用图集引用，改为引用贴图
                        && excludeShaderOfAtlas.IndexOf(shader.name) < 0 //特殊shader排除，这种shader未使用shader自带offset和scale；不能使用图集
                        && newTexture.wrapMode != TextureWrapMode.Clamp) //美术k动画需求：因为要K材质球里面的offest的动画，所以Clamp下的动画不可以加入图集
                    {
                        
                        // Rect textureRect = textureAtlasRects[nexTexPath];
                        // var atlasTexture = AssetDatabase.LoadAssetAtPath<Texture2D>(currentAtlasTexture);
                        // _mat.SetTexture(propertyName, atlasTexture);
                        // _mat.SetTextureOffset(propertyName, new Vector2(textureRect.x, textureRect.y));
                        // _mat.SetTextureScale(propertyName, new Vector2(textureRect.width, textureRect.height));

                    }
                    else
                    {
                        if (atlasImagePaths.IndexOf(nexTexPath) >= 0)
                        {
                            Debug.LogError("删除图集内的贴图：" + nexTexPath);
                            atlasImagePaths.RemoveAt(atlasImagePaths.IndexOf(nexTexPath));
                        }
                        _mat.SetTexture(propertyName, newTexture);
                    }
                    Debug.LogError("材质球贴图：" + _mat.name + " type:" + tex.GetType() + " texname:" + tex.name + " propertyName:" + propertyName + " newTexture:" + newTexture.name
                    + " 贴图path：" + nexTexPath + " shader.name:" + shader.name + "  是否在排除shader内：" + excludeShaderOfAtlas.IndexOf(shader.name));
                    // results.Add(texPath);
                }
            }
            return _mat;
            // return results.ToArray();
        }


        //替换材质球上的图集
        private Material ReplaceAtlasTexture(Material _mat)
        {
            if (_mat == null)
            {
                return _mat;
            }

            // List<string> results = new List<string>();

            Shader shader = _mat.shader;
            for (int i = 0; i < ShaderUtil.GetPropertyCount(shader); ++i)
            {
                if (ShaderUtil.GetPropertyType(shader, i) == ShaderUtil.ShaderPropertyType.TexEnv)
                {
                    string propertyName = ShaderUtil.GetPropertyName(shader, i);
                    Texture tex = _mat.GetTexture(propertyName);
                    if (tex == null)
                    {
                        continue;
                    }
                    string texPath = AssetDatabase.GetAssetPath(tex.GetInstanceID());
                    Texture newTexture = GetNewAssetBy<Texture>(tex);
                    if (newTexture == null)
                    {
                        Debug.LogError("未获取到新的材质球贴图：" + _mat.name + " type:" + tex.GetType() + " texname:" + tex.name + " propertyName:" + propertyName);
                        continue;
                    }
                    //获取贴图合并图集之后的信息
                    string nexTexPath = AssetDatabase.GetAssetPath(newTexture);
                    var sourceOffset = _mat.GetTextureOffset(propertyName);
                    var sourceScale = _mat.GetTextureScale(propertyName);
                    if (!string.IsNullOrEmpty(currentAtlasTexture) && textureAtlasRects.ContainsKey(nexTexPath))
                    {
                        Rect textureRect = textureAtlasRects[nexTexPath];
                        var atlasTexture = AssetDatabase.LoadAssetAtPath<Texture2D>(currentAtlasTexture);
                        _mat.SetTexture(propertyName, atlasTexture);
                        _mat.SetTextureOffset(propertyName, new Vector2(textureRect.x + 0.01f, textureRect.y + 0.01f));
                        _mat.SetTextureScale(propertyName, new Vector2(textureRect.width - 0.01f * 2, textureRect.height - 0.01f * 2));

                    }
                    Debug.LogError("材质球贴图：" + _mat.name + " type:" + tex.GetType() + " texname:" + tex.name + " propertyName:" + propertyName + " newTexture:" + newTexture.name
                    + " 贴图path：" + nexTexPath + " shader.name:" + shader.name + "  是否在排除shader内：" + excludeShaderOfAtlas.IndexOf(shader.name));
                    // results.Add(texPath);
                }
            }
            return _mat;
            // return results.ToArray();
        }

        public T GetNewAssetBy<T>(UnityEngine.Object component) where T : UnityEngine.Object
        {
            // Dictionary<int, string> paths = newAssets[classType];
            // var sourceAssetPath = AssetDatabase.GetAssetPath(component);
            if (!newAssets.ContainsKey(component))
            {
                return default;
            }
            // var go = newAssets[component] as T;
            var go = AssetDatabase.LoadAssetAtPath<T>(newAssets[component]);
            return go;
        }




        [FoldoutGroup("CreateAtlas")]
        [FolderPath]
        public string targetAtlasPath = "";
        public List<string> atlasImagePaths = new List<string>();
        [FoldoutGroup("CreateAtlas")]
        [Button("手动创建图集")]
        public void CreateAtlas()
        {
            currentAtlasTexture = "";
            textureAtlasRects.Clear();

            if (!Directory.Exists(targetAtlasPath))
            {
                return;
            }
            BuildAtlasImagePaths(targetAtlasPath);
            CreateAtlasBy(targetAtlasPath, atlasImagePaths);
        }

        //创建图集子元素路径列表
        public void BuildAtlasImagePaths(string targetAtlasRootPath)
        {
            atlasImagePaths.Clear();
            string spritePath = targetAtlasRootPath;
            if (!Directory.Exists(spritePath))
            {
                return;
            }
            string[] files = Directory.GetFiles(spritePath, "*.*", SearchOption.AllDirectories);
            for (int i = 0; i < files.Length; i++)
            {
                string relativePath = files[i].Substring(files[i].IndexOf("Asset")).Replace('\\', '/');
                var t_SourceTex = AssetDatabase.LoadAssetAtPath<Texture2D>(relativePath);
                if (t_SourceTex == null)
                {
                    continue;
                }

                if (relativePath.Contains("noexport") == false)
                    atlasImagePaths.Add(relativePath);
            }
        }

        public void CreateAtlasBy(string targetAtlasRootPath, List<string> atlasSubImagePaths)
        {
            currentAtlasTexture = "";
            textureAtlasRects.Clear();

            string spritePath = targetAtlasRootPath;
            if (!Directory.Exists(spritePath))
            {
                return;
            }
            // spritePath = EditorUtility.OpenFolderPanel("选择文件夹", "", "");

            List<Sprite> sprites = new List<Sprite>();
            List<Texture2D> newTexs = new List<Texture2D>();
            
            for (int i = 0; i < atlasSubImagePaths.Count; i++)
            {
                string relativePath = atlasSubImagePaths[i];
                Debug.Log("filesName:" + relativePath + " index:" + i);
                // Rect t_Rect = sprites[i].rect;
                var t_SourceTex = AssetDatabase.LoadAssetAtPath<Texture2D>(relativePath);
                if (t_SourceTex == null)
                {
                    continue;
                }
                // int textureSize = texture.width * texture.height;
                // 开启源Spirte的可读
                TextureImporter t_Importer = AssetImporter.GetAtPath(relativePath) as TextureImporter;
                //排除特殊FromGrayScale类型的贴图接入图集，防止表现异常
                if (t_Importer.alphaSource == TextureImporterAlphaSource.FromGrayScale)
                {
                    continue;
                }
                t_Importer.isReadable = true;
                t_Importer.textureCompression = TextureImporterCompression.Uncompressed;
                t_Importer.assetBundleName = "";
                t_Importer.SaveAndReimport();
                // AssetDatabase.ImportAsset(relativePath);
                // 裁剪出新的Texture
                // Color[] t_Colors = t_SourceTex.GetPixels((int)t_Rect.x, (int)t_Rect.y, (int)t_Rect.width, (int)t_Rect.height);
                newTexs.Add(t_SourceTex);
                // newTexs[i].SetPixels(t_Colors);
            }

            // 打包成Atlas
            string atlasPath = spritePath + "/atlas.png";
            // if (File.Exists(atlasPath))
            // {
            //     File.Delete(atlasPath);
            // }
            // UnityEditor.AssetDatabase.Refresh();
            string atlasRelativePath = atlasPath.Substring(atlasPath.IndexOf("Assets"));
            Texture2D atlasTex = new Texture2D(1024, 1024);
            Rect[] atlasRects = atlasTex.PackTextures(newTexs.ToArray(), 0, 4096);
            SpriteMetaData[] atlasSheets = new SpriteMetaData[atlasRects.Length];
            File.WriteAllBytes(atlasPath, atlasTex.EncodeToPNG());
            // 设置Atlas的sprite
            for (int i = 0; i < atlasSheets.Length; i++)
            {
                // SpriteMetaData t_Meta = new SpriteMetaData();
                // t_Meta.name = sprites[i].name;
                Rect rect = atlasRects[i];
                // t_Meta.rect.Set(
                //     t_Meta.rect.x * atlasTex.width,
                //     t_Meta.rect.y * atlasTex.height,
                //      t_Meta.rect.width * atlasTex.width,
                //      t_Meta.rect.height * atlasTex.height
                // );
                textureAtlasRects[atlasSubImagePaths[i]] = rect;
                Debug.LogError("贴图信息：" + atlasSubImagePaths[i] + "rect.x: " + rect.x + "rect.y: " + rect.y + "rect.width: " + rect.width + "rect.height: " + rect.height);
                // t_Meta.alignment = 9;
                // Rect t_Rect = sprites[i].rect;
                // t_Meta.pivot = new Vector2(sprites[i].pivot.x / t_Rect.width, sprites[i].pivot.y / t_Rect.height);
                // atlasSheets[i] = t_Meta;
            }

            UnityEditor.AssetDatabase.Refresh();

            // 设置Atlas Texture属性
            TextureImporter atlas_Importer = AssetImporter.GetAtPath(atlasPath) as TextureImporter;
            // atlas_Importer.textureType = TextureImporterType.Sprite;
            // atlas_Importer.spriteImportMode = SpriteImportMode.Multiple;
            atlas_Importer.textureCompression = TextureImporterCompression.Compressed;
            atlas_Importer.mipmapEnabled = false;
            atlas_Importer.alphaIsTransparency = true;
            atlas_Importer.alphaSource = TextureImporterAlphaSource.FromInput;

            atlas_Importer.SaveAndReimport();

            AssetDatabase.ImportAsset(atlasRelativePath);
            AssetDatabase.Refresh();
            currentAtlasTexture = atlasPath;
        }

#region CheckDepeCount
        [FoldoutGroup("CheckDepeCount")]
        [FolderPath]
        public List<string> findassets = new List<string>{
            "Assets/Art/Effects/Textures",
        };
        [FoldoutGroup("CheckDepeCount")]
        [FolderPath]
        public List<string> dependenciesAssets = new List<string>{
            "Assets/Art/Effects",
        };

        [FoldoutGroup("CheckDepeCount")]
        //是否只展示预制体依赖；（用来判断资源被对象引用的次数）
        public bool isOnlyShowTargetPrefab = true;

        [FoldoutGroup("CheckDepeCount")]
        public List<StrProto> fList = new List<StrProto>();
        [FoldoutGroup("CheckDepeCount")]
        public Dictionary<string, List<string>> dependenciesList = new Dictionary<string, List<string>>();

        [FoldoutGroup("CheckDepeCount")]
        public List<UnityEngine.Object> disableAssets = new List<UnityEngine.Object>();
        Func<string, bool> isPng = (s) => s.EndsWith(".png", StringComparison.CurrentCultureIgnoreCase) || s.EndsWith(".tga", StringComparison.CurrentCultureIgnoreCase) || s.EndsWith(".jpg", StringComparison.CurrentCultureIgnoreCase);
        //检测引用信息
        [FoldoutGroup("CheckDepeCount")]
        [Button("检测引用信息")]
        void CheckDepeCount()
        {
            var fas = AssetDatabase.FindAssets("", findassets.ToArray());
            dependenciesList = new Dictionary<string, List<string>>();
            disableAssets = new List<UnityEngine.Object>();
            var abnames = new Dictionary<string, int>();
            //Debug.Log("所有文件列表：" + string.Join("/n", fas));
            // ToolUti.Iter(fas, (fa) =>
            // {
            foreach (var fa in fas)
            {
                var ap = AssetDatabase.GUIDToAssetPath(fa);
                var ai = AssetImporter.GetAtPath(ap);
                string ext = GetExtension(ap);
                // if (isPng(ap))
                // {
                //GetTextureSizeBy(ap);
                if (ext != "")
                {
                    abnames[ap] = 0;
                    dependenciesList[ap] = new List<string>();
                    Debug.Log("长度" + ap + name + dependenciesList.Count);

                }
                // }
            }

            var allnames = AssetDatabase.FindAssets("", dependenciesAssets.ToArray());
            foreach (var abname in allnames)
            {
                var ap = AssetDatabase.GUIDToAssetPath(abname);
                var ai = AssetImporter.GetAtPath(ap);
                // if (string.IsNullOrEmpty(ai.assetBundleName))
                // {
                //     //Debug.Log("被依赖文件：" + ap + "没有abname!!!!!!!!!!!!!!!!!!!!!!!");
                //     return ap;
                // }
                var abs = AssetDatabase.GetDependencies(ap, true);
                foreach (var ab in abs)
                {
                    var num = 0;
                    //Debug.Log(abname + "依赖项：" + ab);
                    if (ab == ap)
                    {
                        continue;
                    }

                    if (abnames.TryGetValue(ab, out num))
                    {
                        if (dependenciesList[ab].IndexOf(ap) < 0)
                        {
                            //判断是否过滤预制体
                            if (!isOnlyShowTargetPrefab || ap.EndsWith(".prefab", StringComparison.CurrentCultureIgnoreCase))
                            {
                                abnames[ab] = num + 1;
                                dependenciesList[ab].Add(ap);
                            }
                        }
                        //if (abnames[ab] >= 2)
                        //{
                        //    Debug.LogError(ab +"---- \n" + string.Join("\n", dependenciesList[ab].ToArray()));
                        //}
                    }
                }
            }


            Debug.Log(UIHelper.ToJson(abnames));
            fList.Clear();
            List<UnityEngine.Object> dependenciesAsset;
            foreach (var abn in abnames.Keys)
            {
                //var ai = AssetImporter.GetAtPath(abn);
                AssetImporter ai = AssetImporter.GetAtPath(abn) as AssetImporter;
                var obj = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(abn);
                Texture2D texture = null;
                if (obj is Texture2D)
                {
                    texture = obj as Texture2D;
                }
                var abName = "";
                if (ai)
                {
                    abName = ai.assetBundleName;
                }
                //计算依赖资源
                dependenciesAsset = new List<UnityEngine.Object>();
                dependenciesList[abn].ToArray().ForEach(ap =>
                {
                    UnityEngine.Object depObj = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(ap);
                    if (dependenciesAsset.IndexOf(depObj) < 0)
                    {
                        dependenciesAsset.Add(depObj);
                    }
                });
                fList.Add(new StrProto()
                {
                    abname = abName,
                    assetSource = obj,
                    count = abnames[abn],
                    width = texture ? texture.width : 0,
                    height = texture ? texture.height : 0,
                    widthMulHeight = texture ? texture.width * texture.height : 0,
                    dependencies = dependenciesAsset,
                });
                if (abnames[abn] <= 0)
                {
                    disableAssets.Add(obj);
                }
            }

            fList.Sort((Comparison<StrProto>)((f1, f2) =>
            {
                return (int)(f2.count - f1.count);
            }));

        }

        [FoldoutGroup("CheckDepeCount")]
        [Button("删除未使用公共资源")]
        void deleteDisableAssets()
        {
            string path;
            foreach (var item in disableAssets)
            {
                path = AssetDatabase.GetAssetPath(item);
                Debug.LogFormat("获取路径：{0}", path);
                if (File.Exists(path))
                {
                    File.Delete(path);
                    Debug.LogFormat("删除路径：{0}", path);
                }

            }
            UnityEditor.AssetDatabase.Refresh();
        }

#endregion


#region ReplaceTimeLineDependencies 替换timeline引用的相关预制体

        [FoldoutGroup("ReplaceTimeLineDependencies")]
        [FolderPath]
        //检测timeline的父目录（和目标文件列表互斥，如果有目标文件列表，则检测目录无效）
        public string replaceCheckPath = "";


        [FoldoutGroup("ReplaceTimeLineDependencies")]
        [Sirenix.OdinInspector.FilePath]
        //指定要替换的timeline文件（和检测目录互斥，如果该列表生效，则检测目录无效）
        public List<string> replaceTargetFilePaths = new List<string> { };

        [FoldoutGroup("ReplaceTimeLineDependencies")]
        public Boolean isCheckMd5 = true;

        //替换timeline引用资源
        [FoldoutGroup("ReplaceTimeLineDependencies")]
        [Button("替换timeline引用预制体")]
        public void ReplaceTimeLineDependencies()
        {
            //StartLoadExportPrefabDatas();  
            string replaceDir = "Assets/Art/Effects/Effects";
            string[] dirs = Directory.GetDirectories(replaceDir); //找到导出资源目标文件夹，文件夹名就是source预制体名字对应的导出预制体资源名
            Dictionary<string, string> outputEffect = new Dictionary<string, string>();
            for(int i = 0; i < dirs.Length; i++)
            {
                string dir = dirs[i];
                string dirName = Path.GetFileName(dir);
                string prefabName = $"{dir}/Prefabs/{dirName}.prefab";
                if (!File.Exists(prefabName))
                {
                    Debug.LogError($"错误！预制体不存在！{prefabName}");
                    CLog($"Error，Prefab not exits！！{prefabName}");
                    continue;
                }
                outputEffect.Add(dirName, prefabName);
            }  
            List<FileInfo> allFileInfos = new List<FileInfo>();
            //判断给定的路径是否存在,如果不存在则退出
            if (replaceTargetFilePaths != null && replaceTargetFilePaths.Count > 0)
            {
                foreach (var itemPath in replaceTargetFilePaths)
                {
                    FileInfo file = new FileInfo(itemPath);
                    allFileInfos.Add(file);
                    Debug.LogError("批量替换timeline 文件:" + itemPath);
                }
            }
            else
            {
                if (!Directory.Exists(replaceCheckPath))
                {
                    // yield break;
                    //判断是否为单个文件
                    if (File.Exists(replaceCheckPath))
                    {
                        FileInfo file = new FileInfo(replaceCheckPath);
                        allFileInfos.Add(file);
                        Debug.LogError(" 判断为单个文件:" + replaceCheckPath);
                    }
                    else
                    {
                        Debug.LogError(" 无效文件:" + replaceCheckPath);
                        return;
                    }
                }
                else
                {
                    //判断路径为文件夹
                    DirectoryInfo di = new DirectoryInfo(replaceCheckPath);
                    allFileInfos.AddRange(di.GetFiles("*.playable", SearchOption.AllDirectories));
                    Debug.LogError(" 判断为文件夹:" + replaceCheckPath);
                }
            }

            foreach (FileInfo file in allFileInfos)
            {
                string targetRootPath = Path.Combine(targetExportPath, "Timeline_battle");

                string assetPath = file.FullName.Substring(file.FullName.IndexOf("Assets\\"));
                //if (isCheckMd5 && !changedFileDic.ContainsKey(assetPath)) //预制体是否发生改变)
                //{
                //    continue;
                //}
                //string targetPath = CopyRes(assetPath, targetRootPath, false);
                //Debug.Log("替换timeline：备份地址：" + targetPath + "    原始地址:" + assetPath);
                var timeline = AssetDatabase.LoadAssetAtPath<TimelineAsset>(assetPath);
                // var sourceAssetPath = AssetDatabase.GetAssetPath(go);
                DoReplaceDependencies(timeline, outputEffect);
            }
            UnityEditor.AssetDatabase.SaveAssets();
            UnityEditor.AssetDatabase.Refresh();

        }


        private void DoReplaceDependencies(TimelineAsset timeline, Dictionary<string, string> outputEffect)
        {
            bool dirty = false;
            string effectPath;
            string outsideSkillPath;
            foreach (TrackAsset track in timeline.GetOutputTracks())
            {
                UnityEngine.Object newObject = null; 
                if (track is ParticleAttackTrack)
                {
                    foreach (TimelineClip clip in track.GetClips())
                    {
                        ParticleAttackClip targetClip = clip.asset as ParticleAttackClip;
                        if (targetClip.template.prefab != null)
                        {
                           
                            effectPath = AssetDatabase.GetAssetPath(targetClip.template.prefab);
                            string prefabName = targetClip.template.prefab.name;
                            if (outputEffect.ContainsKey(prefabName))
                            {
                                newObject = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(outputEffect[prefabName]);
                            }
                           
                           
                            if (newObject != null)
                            {
                                targetClip.template.prefab = newObject as GameObject;
                                Debug.LogFormat("替换timeline引用特效：原始资源：{0};   替换资源{1}", effectPath, AssetDatabase.GetAssetPath(targetClip.template.prefab));
                                dirty = true;
                            }
                            else
                            {
                                Debug.LogError($"有资源没有引用到新资源路径，请检查{timeline.name}");
                                CLog($"Some timeline use SourceEffect!!=>>>>>>> : {timeline.name}");
                            }
                        }
                        if (targetClip.template.outsideSkillPrefab != null)
                        {
                            outsideSkillPath = AssetDatabase.GetAssetPath(targetClip.template.outsideSkillPrefab);
                            string prefabName = targetClip.template.outsideSkillPrefab.name;
                            if (outputEffect.ContainsKey(prefabName))
                            {
                                newObject = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(outputEffect[prefabName]);
                            }
                            if (newObject != null)
                            {
                                targetClip.template.outsideSkillPrefab = newObject as GameObject;
                                // targetClip.template.outsideSkillPrefab = GetNewAssetBy<GameObject>(targetClip.template.outsideSkillPrefab);
                                Debug.LogFormat("替换timeline引用插屏：原始资源：{0};   替换资源{1}", outsideSkillPath, AssetDatabase.GetAssetPath(targetClip.template.outsideSkillPrefab));
                                dirty = true;
                            }
                            else
                            {
                                Debug.LogFormat("失败：替换timeline引用插屏：原始资源：{0};   替换资源{1}", outsideSkillPath, AssetDatabase.GetAssetPath(targetClip.template.outsideSkillPrefab));
                            }
                        }
                    }
                }
            }
            if (dirty)
            {
                EditorUtility.SetDirty(timeline);
            }
        }


        private string SubStringSingle(string source,string startStr,string endStr)
        {
            Regex rg = new Regex("(?<=(" + startStr + "))[.\\s\\S]*?(?=(" + endStr + "))", RegexOptions.Multiline | RegexOptions.Singleline);
            return rg.Match(source).Value;
        }


        /// <summary>
        /// 检查timeline是否丢失的资源
        /// </summary>
        public void CheckMissRefInTimeline()
        {
            List<FileInfo> allFileInfos = new List<FileInfo>();
            tmp = "";
            DirectoryInfo di = new DirectoryInfo("Assets/Art/Effects_Source/Timeline_battle");
            allFileInfos.AddRange(di.GetFiles("*.playable", SearchOption.AllDirectories));
            foreach (FileInfo file in allFileInfos)
            {
                string targetRootPath = Path.Combine(targetExportPath, "Timeline_battle");

                string assetPath = file.FullName.Substring(file.FullName.IndexOf("Assets\\")); 
                var timeline = AssetDatabase.LoadAssetAtPath<TimelineAsset>(assetPath);
                DoCheckTimeLineRef(timeline);
            }
            Debug.LogError(tmp);
        }
        string tmp = "";
        private void DoCheckTimeLineRef(TimelineAsset timeline)
        {
            
            foreach (TrackAsset track in timeline.GetOutputTracks())
            {
                if (track is ParticleAttackTrack)
                {
                    foreach (TimelineClip clip in track.GetClips())
                    {
                        ParticleAttackClip targetClip = clip.asset as ParticleAttackClip;
                        if (targetClip.template.prefab == null)
                        {
                            //Debug.LogError($"引用疑似丢失：{timeline.name}");
                            tmp += $"引用疑似丢失：{timeline.name}\n";
                            break;
                        }
                      
                    }
                }
            }
        }

        public UnityEngine.Object GetNewObjectBy(UnityEngine.Object sourceObj)
        {
            foreach (var item in dependencies)
            {
                // Debug.LogError("GetNewObjectBy:" + dependencies.Count + "  " + dependencies.IndexOf(item) + "   " + item.path + "  sourcePath:  " + AssetDatabase.GetAssetPath(sourceObj) + " equale " + item.target.Equals(sourceObj));

                if (item.target.Equals(sourceObj))
                {
                    return item.newTarget;
                }

            }
            return null;
        }

#endregion

        public static Dictionary<string, string[]> ReadInitialMd5Names(string checkTargetPath)
        {
            string targetLuaMd5Path = string.Format("{0}/{1}", checkTargetPath, initialMd5Name);
            string targetFileContent = null;
            if (File.Exists(targetLuaMd5Path))
            {
                targetFileContent = File.ReadAllText(targetLuaMd5Path);
            }

            if (string.IsNullOrEmpty(targetFileContent))
            {
                Debug.Log("检查md5值失败：无法找到需要对比的lua md5信息文件，path:" + targetLuaMd5Path);
                return null;
            }

            Dictionary<string, string[]> oldLuaFileDic = new Dictionary<string, string[]>();
            var tempNewestPatchFileDic =
                UIHelper.ToObj<Dictionary<string, Dictionary<string, string[]>>>(targetFileContent);
            if (tempNewestPatchFileDic.ContainsKey("list"))
            {
                oldLuaFileDic = tempNewestPatchFileDic["list"];
            }
            return oldLuaFileDic;
        }

        public static Dictionary<string, List<string>> CheckMd5(string checkTargetPath, Dictionary<string, List<string>> currentLuaDic)
        {
            // string targetLuaMd5Path = string.Format("{0}/{1}", checkTargetPath, initialMd5Name);
            // string targetFileContent = null;
            // if (File.Exists(targetLuaMd5Path))
            // {
            //     targetFileContent = File.ReadAllText(targetLuaMd5Path);
            // }

            // if (string.IsNullOrEmpty(targetFileContent))
            // {
            //     Debug.Log("检查md5值失败：无法找到需要对比的lua md5信息文件，path:" + targetLuaMd5Path);
            //     return currentLuaDic;
            // }


            // Dictionary<string, string[]> oldLuaFileDic = new Dictionary<string, string[]>();
            // var tempNewestPatchFileDic =
            //     UIHelper.ToObj<Dictionary<string, Dictionary<string, string[]>>>(targetFileContent);
            // if (tempNewestPatchFileDic.ContainsKey("list"))
            // {
            //     oldLuaFileDic = tempNewestPatchFileDic["list"];
            // }

            Dictionary<string, string[]> oldLuaFileDic = ReadInitialMd5Names(checkTargetPath);

            if (oldLuaFileDic == null)
            {
                return currentLuaDic;
            }

            Dictionary<string, List<string>> changedLuaFileDic = new Dictionary<string, List<string>>();
            List<string> currentLuaInfo;
            List<string> oldLuaInfo;
            foreach (string key in currentLuaDic.Keys)
            {
                currentLuaInfo = currentLuaDic[key];
                if (!oldLuaFileDic.ContainsKey(key))
                {
                    //新增lua文件
                    changedLuaFileDic.Add(key, currentLuaInfo);
                    continue;
                }

                oldLuaInfo = new List<string>(oldLuaFileDic[key]);

                //检测md5值和长度值是否有变化
                if (currentLuaInfo[0].Equals(oldLuaInfo[0]) && currentLuaInfo[1].Equals(oldLuaInfo[1]))
                {
                    continue;
                }

                Debug.Log("检测md5值和长度值是否有变化md5:" + currentLuaInfo[0] + " old:" + oldLuaInfo[0] + " length:" +
                          currentLuaInfo[1] + " old:" + oldLuaInfo[1]);
                changedLuaFileDic.Add(key, currentLuaInfo);
            }

            return changedLuaFileDic;
        }


        //[Button("创建md5文件")]
        public static Dictionary<string, List<string>> GetMd5FileInfo(string souceFilesRootPath)//, string outputPath, string fileName)
        {
            /// create patch file
            // if (!Directory.Exists(outputPath))
            // {
            //     Directory.CreateDirectory(outputPath);
            // }

            try
            {
                var fileDic = new Dictionary<string, List<string>>();

                var files = Directory.GetFiles(souceFilesRootPath, "*.*", SearchOption.AllDirectories);
                foreach (var f in files)
                {
                    var md5 = War.Base.BuildScript.File2MD5(f);
                    var shortf = f.Trim('\\', '/').Replace("\\", "/");

                    var list = new List<string>() { md5, new FileInfo(f).Length + "" };
                    fileDic.Add(shortf, list);
                }

                //var content = string.Join("\n", fileList.ToArray());
                // var dic = new Dictionary<string, object>();
                // dic["list"] = fileDic;

                // if (fileName == "")
                // {
                //     fileName = initialMd5Name;
                // }

                // File.WriteAllText(string.Format("{0}/{1}", outputPath, fileName), UIHelper.ToJson(dic));
                return fileDic;
            }
            catch (Exception e)
            {
                Debug.LogError(e.ToString());
            }

            return null;
        }
        public static Dictionary<string, string> localPrePackCommonTexture = new Dictionary<string, string>(); //本地，打包前Common内资源表，如果有冗余资源，需要优先使用已有表内容
        public static Dictionary<string, ST_Res_Md5> localPrePackCommonTextureSTDict = new Dictionary<string, ST_Res_Md5>(); //上面的md5对照
        /// <summary>
        /// 预生成公用资源表,以本地md5作为唯一引用键值，如果有两个重复的md5,则需要报错处理，流程上不允许存在两个相同的md5在文件夹下
        /// </summary>
        public bool GenPrePackCommonTexture()
        {
            localPrePackCommonTexture.Clear();
            string texPath = "Assets/Art/Effects/Common/Textures";
            string[] files = Directory.GetFiles(texPath,"*",SearchOption.AllDirectories); // 
           
            for(int i = 0; i < files.Length; i++)
            {
                if (files[i].EndsWith(".meta"))
                {
                    continue;
                }
                string path = files[i].Replace('\\', '/');
                string md5 = War.Base.BuildScript.File2MD5(path);
                if (!localPrePackCommonTexture.ContainsKey(md5))
                {
                    localPrePackCommonTexture.Add(md5, path);
                }
                else
                {
                    CLog("Error Deleting RedundancyRes,There May Exist Files With Same Md5 Code! Please Check Before Export Res");
                    //return false;//如果有就先不打包，直接返回
                }
            }
            return true;
        }
        #region 处理资源冗余关系，删除文件夹冗余资源,删除冗余不能单独使用，防止所有预制体发生改变
       // [MenuItem("Zone/DeleteRedundancyTest")]
        /// <summary>
        /// 对特效文件目录下的资源进行冗余剔除的处理
        /// </summary>
        public static void DeleteRedundancy()
        { 
            localPrePackCommonTextureSTDict.Clear();
            string deletePath = "Assets/Art/Effects/";
            //剔除规则，textures下的资源，如果有md5重复的，删除其他的，保留一份，其他资源的引用全部设置为新的这一份
            //对应特效目录路径下的资源，按特效目录区分，单独剔除处理，剔除操作只会删除资源，不会删除材质球
            //贴图资源保留规则（避免新的资源贴图会被保留，导致所有预制体ab包发生变化，需要使用唯一资源）
            string[] mats = Directory.GetFiles(deletePath, "*.mat", SearchOption.AllDirectories); //找打所有的材质球
            List<ST_Mat_Texture> matSt = new List<ST_Mat_Texture>();

            List<ST_Res_Md5> md5St = new List<ST_Res_Md5>(); //生成的所有贴图路径以及对应的md5
            Dictionary<string, int> globalMd5Counter = new Dictionary<string, int>(); //计数器而已

            Dictionary<string, CS_Texture_Dir> handlerTextureDict = new Dictionary<string, CS_Texture_Dir>(); //索引为目录
            for (int i = 0; i < mats.Length; i++)
            {
                matSt.Add(new ST_Mat_Texture(mats[i]));
            }
            var texes = Directory.GetFiles(deletePath, "*.*", SearchOption.AllDirectories).Where((s)=> { s = s.ToLower(); return s.EndsWith(".tga") || s.EndsWith(".png") || s.EndsWith(".jpg"); }).ToArray();

            for(int i = 0; i < texes.Length; i++)
            {
                ST_Res_Md5 stmd5 = new ST_Res_Md5(texes[i]);
                md5St.Add(stmd5);
                if (localPrePackCommonTexture.ContainsKey(stmd5.md5) && (stmd5.path == localPrePackCommonTexture[stmd5.md5]))
                {
                    localPrePackCommonTextureSTDict.Add(stmd5.md5, stmd5);
                }
              
            }

            for(int i = 0; i < md5St.Count; i++)
            {
                var stmd5 = md5St[i];
                if (!handlerTextureDict.ContainsKey(stmd5.belongToDir))
                {
                    handlerTextureDict.Add(stmd5.belongToDir, new CS_Texture_Dir(stmd5.belongToDir));
                }
                var csTDir = handlerTextureDict[stmd5.belongToDir];
                csTDir.Add_ST_Texture_Md5(stmd5); //添加贴图到目录分区，在目录分区做判断处理
            }

            for(int i = 0; i < matSt.Count; i++)
            {
                var matInfo = matSt[i];
                if (handlerTextureDict.ContainsKey("Common"))
                {
                    var commonSt = handlerTextureDict["Common"];
                    matInfo.HandleDirTextureRes(commonSt);

                }
                else
                {
                    Debug.LogError("没有Common dir");
                }
                if (handlerTextureDict.ContainsKey(matInfo.belongToDir))
                {
                    var mDirSt = handlerTextureDict[matInfo.belongToDir];
                    matInfo.HandleDirTextureRes(mDirSt);
                }
                else //都是用的公用资源路径的内容
                {
                    //Debug.LogError($"没有这个dir:{matInfo.belongToDir}");
                }
          
                EditorUtility.DisplayProgressBar("贴图重定向", $"{i}/{matSt.Count}", i*1.0f/matSt.Count);
            }
            AssetDatabase.SaveAssets();
            //删除冗余资源
            List<string> assetsDeleteLs = new List<string>();
            foreach (KeyValuePair<string, CS_Texture_Dir> dirRes in handlerTextureDict)
            {
                for(int i=0;i < dirRes.Value.textureRedundancyLs.Count; i++)
                {
                    assetsDeleteLs.Add(dirRes.Value.textureRedundancyLs[i].path);
                }
            }
            AssetDatabase.DeleteAssets(assetsDeleteLs.ToArray(),new List<string>());

           
            EditorUtility.ClearProgressBar();
            ////#打印全资源冗余情况
            //foreach (KeyValuePair<string, int> ct in globalMd5Counter)
            //{
            //    Debug.LogError($"计数器：{ct.Key}:{ct.Value}");
            //    if (ct.Value >= 10)
            //    {
            //        for (int i = 0; i < md5St.Count; i++)
            //        {
            //            if (md5St[i].md5 == ct.Key)
            //            {
            //                Debug.LogWarning($"{md5St[i].path}:{ct.Value}");
            //                break;
            //            }
            //        }
            //    }
            //}
            Resources.UnloadUnusedAssets();
            CLog("Start Delete Unuse Common Texture");
            DeleteUnUseCommonTextures();
        }

        private static void DeleteUnUseCommonTextures()
        {
            string commonTexPath = "Assets/Art/Effects/Common/Textures";
            string commonEffectPrefab = "Assets/Art/Effects/";
            string[] textures = Directory.GetFiles(commonTexPath, "*.*", SearchOption.AllDirectories);
            string[] prefabs = Directory.GetFiles(commonEffectPrefab, "*.prefab", SearchOption.AllDirectories);
            List<string> useTextures = new List<string>();
            for (int i = 0; i < textures.Length; i++)
            {
                if (textures[i].EndsWith(".meta"))
                {
                    continue;
                }
                string name = textures[i].Replace("\\", "/").Replace("//", "/");
                if (useTextures.Contains(name) == false)
                    useTextures.Add(name);
            }
            for (int i = 0; i < prefabs.Length; i++)
            {
                var dps = AssetDatabase.GetDependencies(prefabs[i]);
                for (int j = 0; j < dps.Length; j++)
                {
                    if (useTextures.Contains(dps[j]))
                    {
                        useTextures.Remove(dps[j]);
                    }
                }
            }
            for (int i = 0; i < useTextures.Count; i++)
            {
                if (File.Exists(useTextures[i]))
                    File.Delete(useTextures[i]);
                if (File.Exists(useTextures[i] + ".meta"))
                    File.Delete(useTextures[i] + ".meta");
                CLog("Delete Unuse Common Texture" + useTextures[i]);
            }
            ShowNotExportRes();
        }

        /// <summary>
        /// 打印未导出的资源
        /// </summary>
        private static void ShowNotExportRes()
        {
            CLog("Detect Res Export State");
            string effectSourcePath = "Assets/Art/Effects_Source/Prefabs";
            string effectRoot = "Assets/Art/Effects/Effects";
            string[] prefabs = Directory.GetFiles(effectSourcePath, "*.prefab", SearchOption.AllDirectories);
            List<string> useTextures = new List<string>();
            for (int i = 0; i < prefabs.Length; i++)
            {
                EditorUtility.DisplayCancelableProgressBar("进度", i + "/" + effectSourcePath.Length, i * 1.0f / effectSourcePath.Length);
                string efName = prefabs[i];
                string abName = AssetDatabase.GetImplicitAssetBundleName(efName);
                if (!string.IsNullOrEmpty(abName))
                {
                    if (!efName.Replace("\\", "/").Replace("//", "/").Contains(AutoExportEffectAssets.Instance.excludePath)) //非指定目录下的资源需要移除abname
                    { 
                        CLog("error :: effect source has abName " + abName);
                    } 
                }
                else
                {
                    string fileName = Path.GetFileNameWithoutExtension(efName);
                    string exportName = effectRoot + "/" + fileName + "/Prefabs/" + fileName + ".prefab";
                    if (!File.Exists(exportName))
                    {
                        CLog("error :: file not export!!" + exportName);
                    }
                }
            }
        }
        /// <summary>
        /// 删除模型的冗余
        /// </summary>
        public void DeleteModelRedundancy()
        {
            string commonModelPath = "Assets/Art/Effects/Common/Models"; //公用模型资源文件夹
            if (!Directory.Exists(commonModelPath))
            {
                Directory.CreateDirectory(commonModelPath);
            }
            string deletePath = "Assets/Art/Effects/";
            string[] prefabs = Directory.GetFiles(deletePath, "*.prefab", SearchOption.AllDirectories); //找打所有的预制体资源，找到上面的引用mesh
            List<ST_Prefab_Model> prefabModelInfoList = new List<ST_Prefab_Model>();
            for(int i = 0; i < prefabs.Length; i++)
            {
                string path = prefabs[i];
                prefabModelInfoList.Add(new ST_Prefab_Model(path));
               
            }
            Dictionary<string, int> models = new Dictionary<string, int>();
            Dictionary<string, string> modelMd5 = new Dictionary<string, string>();
            for (int i = 0; i < prefabModelInfoList.Count; i++)
            {
                var info = prefabModelInfoList[i];
                if (info.nodeToMesh == null)
                {
                    continue;
                }
                foreach(KeyValuePair<string,ST_Res_Md5> kv in info.nodeToMesh)
                { 
                    if (!modelMd5.ContainsKey(kv.Value.md5))
                    {
                        models.Add(kv.Value.path, 1);
                        modelMd5.Add(kv.Value.md5, kv.Value.path);
                    }
                    else
                    {
                        string pth = modelMd5[kv.Value.md5];
                        models[pth] = models[pth] + 1;
                    } 
                }
            }
            AssetDatabase.StartAssetEditing();
            foreach(KeyValuePair<string,int> kv in models)
            {
        
                if (kv.Value >= 2)
                {
                    Debug.LogError($"{kv.Key}:{kv.Value}");
                    AssetDatabase.CopyAsset(kv.Key,$"{commonModelPath}/{Path.GetFileName(kv.Key)}");
                }
            }
            AssetDatabase.StopAssetEditing();
            Resources.UnloadUnusedAssets();
        }

        public struct ST_Prefab_Model
        {
            public string prefabName;
            public Dictionary<string, ST_Res_Md5> nodeToMesh; //对应节点的名字，以及对应的mesh信息

           

            public ST_Prefab_Model(string path)
            {
                prefabName = path;
                nodeToMesh = new Dictionary<string, ST_Res_Md5>();
                GameObject go = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                ParticleSystemRenderer[] renders = go.GetComponentsInChildren<ParticleSystemRenderer>(true);
                for (int j = 0; j < renders.Length; j++)
                {
                    var render = renders[j];
                    if (render.mesh != null && render.renderMode == ParticleSystemRenderMode.Mesh)
                    {
                        string meshPath = AssetDatabase.GetAssetPath(render.mesh);
                        if(!meshPath.Contains("Library/unity default resources"))
                        {
                            ST_Res_Md5 rMd5 = new ST_Res_Md5(meshPath);
                            string nodePath = NodePathFind(go, render.gameObject);
                            if (!nodeToMesh.ContainsKey(nodePath))
                            {
                                nodeToMesh.Add(nodePath, rMd5);
                            }
                        }
                     
                    }
                }

               
            }
        }


        ///节点回溯
        static string NodePathFind(GameObject parent,GameObject child)
        {
            string pName = "";
            var tmp = child;
            while (tmp!=null && tmp != parent)
            {
                if (string.IsNullOrEmpty(pName))
                {
                    pName = tmp.name;
                }
                else
                {
                    pName = $"{tmp.name}/{pName}";
                }
                tmp = tmp.transform.parent.gameObject;
            }

            return pName;
        }
        /// <summary>
        /// 资源以及对应的md5,以及从属文件夹
        /// </summary>
        public struct ST_Res_Md5
        {
            public string path;
            public string md5;
            public string belongToDir;
            public ST_Res_Md5(string p)
            {
                path = p.Replace('\\','/');
                md5 = War.Base.BuildScript.File2MD5(path);
                string dirName = Path.GetDirectoryName(path);
                dirName = Path.GetDirectoryName(dirName);
                dirName = Path.GetFileName(dirName);
                belongToDir = dirName;
            }
        }
        /// <summary>
        /// 目录下的贴图列表,按目录搜索去获取，在这里处理唯一指向引用资源，
        /// </summary>
        class CS_Texture_Dir
        {
            public string dir;
            public List<ST_Res_Md5> totalTextureLsInDir = new List<ST_Res_Md5>(); //文件夹下的所有texture信息
            public List<ST_Res_Md5> textureUniqueLs = new List<ST_Res_Md5>(); //非冗余列表，最后留下的是这个列表中的资源
            public List<ST_Res_Md5> textureRedundancyLs = new List<ST_Res_Md5>(); //冗余出来的列表，用于做资源检查，材质包涵的贴图在列表中需要重新定位，最后这里面的资源需要删除处理
            public Dictionary<ST_Res_Md5, ST_Res_Md5> refOldToNew = new Dictionary<ST_Res_Md5, ST_Res_Md5>(); //冗余资源的列表，对应新的索引位置<旧资源索引，新资源索引>
            private List<string> dirTmpLs = new List<string>(); //临时保存的记录列表
            public CS_Texture_Dir(string p)
            {
                dir = p;
            }
            public void Add_ST_Texture_Md5(ST_Res_Md5 stmd5)
            { 
                bool isMd5AlreadyExist = false; 
                totalTextureLsInDir.Add(stmd5);

                //先判断md5是否已经存在一份在列表
                if (AutoExportEffectAssets.localPrePackCommonTextureSTDict.ContainsKey(stmd5.md5)) //判断是否已经存在已有md5，不允许重新指定引用其他资源
                {
                    var targetTMD5 = AutoExportEffectAssets.localPrePackCommonTextureSTDict[stmd5.md5];
                    //还需要判断该资源是否属于此文件夹
                    if (targetTMD5.belongToDir == dir) //该md5未加入唯一列表
                    {
                       if (!dirTmpLs.Contains(stmd5.md5))
                        {
                            textureUniqueLs.Add(targetTMD5);
                            dirTmpLs.Add(stmd5.md5);
                        }
                        if(stmd5.path == targetTMD5.path) //同文件夹同资源，不需要处理
                        {
                            return;
                        }
                      
                    }  
                } 
                for (int i = 0; i < textureUniqueLs.Count; i++)
                {
                    if(stmd5.md5 == textureUniqueLs[i].md5)
                    { 
                        isMd5AlreadyExist = true; 
                        refOldToNew.Add(stmd5, textureUniqueLs[i]); //加入冗余索引字典，指向新的资源路径 
                        textureRedundancyLs.Add(stmd5);
                        break;
                    }
                }
                if (!isMd5AlreadyExist) //如果没有就直接往里面新增，新增前需要判断，本地字典是否有相同的md5，如果有，则需要用此唯一资源
                {
                    textureUniqueLs.Add(stmd5); //此表是md5唯一表，最终冗余删除后，引用会指向这个表的资源内容
                }
            }
        }
        /// <summary>
        /// 材质球对应引用的预制体结构，需要预生成对应关系
        /// </summary>
        struct ST_Mat_Texture
        {

            public string matPath; //材质球路径
            public Dictionary<string,string> texturesPathDict; //贴图路径列表
            public bool isChange;
            public string belongToDir;
            public ST_Mat_Texture(string mPath)
            {
                matPath = mPath.Replace('\\', '/');
                texturesPathDict = new Dictionary<string, string>();
                isChange = false;


                List<string> results = new List<string>();
                var mat = AssetDatabase.LoadAssetAtPath<Material>(matPath);
                Shader shader = mat.shader;
                for (int i = 0; i < ShaderUtil.GetPropertyCount(shader); ++i)
                {
                    if (ShaderUtil.GetPropertyType(shader, i) == ShaderUtil.ShaderPropertyType.TexEnv)
                    {
                        string propertyName = ShaderUtil.GetPropertyName(shader, i);
                        Texture tex = mat.GetTexture(propertyName);
                        if (tex == null)
                        {
                            continue;
                        }
                        string texPath = AssetDatabase.GetAssetPath(tex.GetInstanceID());
                        texturesPathDict.Add(propertyName, texPath.Replace('\\', '/'));
                    }
                }

                string dirName = Path.GetDirectoryName(mPath);
                dirName = Path.GetDirectoryName(dirName);
                dirName = Path.GetFileName(dirName);
                belongToDir = dirName;
            }

            /// <summary>
            /// 传入资源进行材质处理
            /// </summary>
            /// <param name="dir"></param>
            public void HandleDirTextureRes(CS_Texture_Dir dir)
            {
                bool isMatChange = false;
                var mat = AssetDatabase.LoadAssetAtPath<Material>(matPath);

                if (dir.dir == "Common" || dir.dir == belongToDir) //只用处理自己目录的文件夹和公用文件夹下的引用重置
                {
                    for(int i = 0; i < dir.textureRedundancyLs.Count; i++) //找到文件夹下所有需要删除的冗余文件列表
                    {
                        ST_Res_Md5 rd = dir.textureRedundancyLs[i]; //每个冗余文件列表，都有个对应的新的需要重定向的资源
                        foreach(KeyValuePair<string,string> tpDict in texturesPathDict)  //遍历所有材质球上的属性，找到对应的贴图
                        {
                            if(tpDict.Value== rd.path) //材质球上的资源需要进行重定向，如果对应资源是需要删除的冗余资源，需要重定向到新的资源去
                            {
                                string newPath = dir.refOldToNew[rd].path; //文件夹下已经生成好的新资源路径
                                Debug.LogError($"{Path.GetFileName(matPath)}贴图资源重定向:{tpDict.Value}=>{newPath}");
                                var tex2d = AssetDatabase.LoadAssetAtPath<Texture2D>(newPath);
                                mat.SetTexture(tpDict.Key, tex2d);
                                isMatChange = true; 
                            }
                        }
                    }
                }

                if (isMatChange)
                {
                    EditorUtility.SetDirty(mat);
                }
            }
  
        }
        #endregion
        #region 资源检查丢失情况
        private static void FindMissing()
        { 
            string prefabPath = "Assets/Art/Effects/Effects/";
            string[] prefabs = Directory.GetFiles(prefabPath, "*.prefab", SearchOption.AllDirectories);
            string missStr = "";
            for (int i = 0; i < prefabs.Length; i++)
            {
                FindMissing(prefabs[i]);
              
            }
           
        }
        private static void FindMissing(string path)
        {
            path = path.Replace(Application.dataPath, "Assets/");
            var go = AssetDatabase.LoadAssetAtPath<GameObject>(path);
            if (go == null)
            { 
                return ;
            }
            Component[] cps = go.GetComponentsInChildren<Component>(true);
            string missStr = "";
            foreach (var cp in cps)
            {
                if (cp == null)
                {
                    Debug.LogError("组件为空");
                }
                else
                {
                    SerializedObject so = new SerializedObject(cp);
                    SerializedProperty iterator = so.GetIterator();
                    while (iterator.NextVisible(true))
                    {
                        if (iterator.propertyType == SerializedPropertyType.ObjectReference)
                        {
                            if (iterator.objectReferenceValue == null && iterator.objectReferenceInstanceIDValue != 0)
                            {
                                if ("PPtr<Material>" == iterator.type)
                                {
                                    CLog($"missing Reference:{path}== {cp.name}"); 
                                }
                            }
                        }

                    }
                }
            }
            Resources.UnloadUnusedAssets(); 
        }
        #endregion
    }
}
#endif
