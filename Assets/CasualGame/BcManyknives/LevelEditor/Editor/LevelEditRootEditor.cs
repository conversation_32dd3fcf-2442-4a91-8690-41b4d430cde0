using System;
using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

[CustomEditor(typeof(LevelEditRoot))]
public class LevelEditRootEditor: Editor
{
    private LevelEditRoot _target;
    private void OnEnable()
    {
        _target = target as LevelEditRoot;
    }

    private void OnDisable()
    {
        //_target.ExportCsv();
    }
    private void OnValidate()
    {
        //���׵���unity��ס��������
        // _target.ReadCsv();
        // _target.DestroyLevelObjs();
        // _target.CreateLevelObjs();
    }

    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();
        EditorGUI.BeginDisabledGroup(true);
        _target.curLevelID = EditorGUILayout.IntField("��ǰ�ؿ�ID", _target.curLevelID);
        EditorGUI.EndDisabledGroup();
        //int curlevelID = EditorGUILayout.IntSlider("��ǰ�ؿ�ID", _target.curLevelID, 1, _target.maxLevelID);
        int curlevelID = _target.curLevelID;
        bool isExistLevelPrefab = _target.IsExistLevelPrefab();
        if (curlevelID != _target.curLevelID)
        {
            _target.ChangeLevelID(curlevelID);
        }
        
        _target.levelCsvPath = EditorGUILayout.TextField("�ؿ�����Ŀ¼��", _target.levelCsvPath);
        _target.levelPrePath = EditorGUILayout.TextField("�ؿ�Ԥ��Ŀ¼��", _target.levelPrePath);
        _target.levelObjPath = EditorGUILayout.TextField("�ؿ����Ŀ¼��", _target.levelObjPath);

        _target.addObjectID = EditorGUILayout.IntField("�������ID", _target.addObjectID);
        GUILayout.Space(5);
        if (GUILayout.Button("���ݳ������ID�����������"))
        {
            _target.CreateOneLevelObj();
        }
        
        GUILayout.Space(5);
        if (GUILayout.Button("��ȡ����������ɵ�ͼ���"))
        {
            if (!isExistLevelPrefab)
            {
                _target.LoadLevelPrefab(Math.Max(1, _target.curLevelID));
            }
            _target.ReadCsv();
            _target.DestroyLevelObjs();
            _target.CreateLevelObjs();
        }
        
        GUILayout.Space(5);
        if (GUILayout.Button("ɾ�����е�ͼ���(�����浱ǰ����)"))
        {
            _target.DestroyLevelObjs();
        }

        GUILayout.Space(5);
        _target.LinkCsvInLevelPrefab = EditorGUILayout.Toggle("��������Ƿ�󶨵��ؿ�Ԥ����", _target.LinkCsvInLevelPrefab);

        GUILayout.Space(5);
        if(GUILayout.Button("�������ݵ����"))
        {
            _target.ExportCsv();
        }
        
        GUILayout.Space(5);
        GUILayout.Label("������߶�ȡ���ñ�ʱ���벻Ҫ�����ñ���ֹIO����");
        GUILayout.Label("������LevelEditRoot��һ���Ӷ��������");
    }
}
