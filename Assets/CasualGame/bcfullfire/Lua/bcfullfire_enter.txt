local RenderSettings = CS.UnityEngine.RenderSettings
local FogMode = CS.UnityEngine.FogMode
local AmbientMode = CS.UnityEngine.Rendering.AmbientMode
local QualitySettings = CS.UnityEngine.QualitySettings
local ShadowQuality = CS.UnityEngine.ShadowQuality
local ShadowmaskMode = CS.UnityEngine.ShadowmaskMode
local Physics2D = CS.UnityEngine.Physics2D
local state = {
    NONE = 0,    --未开始
    LOADING = 1, --加载中
    PLAYING = 2, --正在游戏中
    PAUSE = 3,   --暂停
    FINISH = 4,  --结束
    SUSPEND = 5, --挂起状态，游戏场景不显示，退出小游戏界面，但是核心资源不销毁
}
local contex = {
    state = state.NONE,
}
local gameData = {
    name = "bcfullfire",
    type = 1040,
    useCommonUI = true,
}
local SendMessager = function(even_name, data)
    if contex and contex.messager then
        contex.messager(even_name, data)
    end
end
local ambientMode_data
local ambientColor_data
local fog_data
local fog_mode
local fog_color
local fog_disMix
local fog_disMax
local shadow_data
local shadowDistance
local shadowMode
local fixedTime_data
local frameRate

local getdevice = function()
    local devInfo = require "bcfullfire_device_param"
    if devInfo and devInfo.GetDeviceLevel then
        local level = devInfo.GetDeviceLevel()
        return level
    end
end

local setrender = function(level)
    local devlevel = getdevice()
    print("当前设备等级", devlevel)
    if devlevel then
        ambientMode_data = RenderSettings.ambientMode
        ambientColor_data = RenderSettings.ambientLight
        RenderSettings.ambientMode = AmbientMode.Flat
        RenderSettings.ambientLight = CS.UnityEngine.Color(0.38039, 0.48627, 0.69411)


        fog_data = RenderSettings.fog
        fog_mode = RenderSettings.fogMode
        fog_color = RenderSettings.fogColor
        fog_disMix = RenderSettings.fogStartDistance
        fog_disMax = RenderSettings.fogEndDistance
        frameRate = CS.UnityEngine.Application.targetFrameRate

        RenderSettings.fog = true
        RenderSettings.fogColor = CS.UnityEngine.Color(0.3294, 0.73725, 0.94117)
        RenderSettings.fogMode = FogMode.Linear
        RenderSettings.fogStartDistance = 40
        RenderSettings.fogEndDistance = 150

        shadow_data = QualitySettings.shadows
        shadowDistance = QualitySettings.shadowDistance
        shadowMode = QualitySettings.shadowmaskMode
        QualitySettings.shadows = ShadowQuality.All
        QualitySettings.shadowmaskMode = ShadowmaskMode.Shadowmask
        QualitySettings.shadowDistance = 100
        if CS.UnityEngine.Application.platform == CS.UnityEngine.RuntimePlatform.WebGLPlayer then
            CS.UnityEngine.Application.targetFrameRate = 30
        else
            CS.UnityEngine.Application.targetFrameRate = 60
        end
        --if level == 1 then
        --    RenderSettings.ambientLight = CS.UnityEngine.Color(0.05, 0.05, 0.23)
        --    RenderSettings.fogColor = CS.UnityEngine.Color(0.329, 0.737, 0.94)
        --    RenderSettings.fogStartDistance = 70
        --    RenderSettings.fogEndDistance = 230
        --elseif level == 2 then
        --    RenderSettings.ambientLight = CS.UnityEngine.Color(0.158, 0.166, 0.273)
        --    RenderSettings.fogColor = CS.UnityEngine.Color(0.773, 0.902, 1)
        --    RenderSettings.fogStartDistance = 60
        --    RenderSettings.fogEndDistance = 102
        --elseif level == 3 then
        --    RenderSettings.ambientLight = CS.UnityEngine.Color(0.06, 0.06, 0.16)
        --    RenderSettings.fogColor = CS.UnityEngine.Color(0.759, 0.827, 1)
        --    RenderSettings.fogStartDistance = 60
        --    RenderSettings.fogEndDistance = 170
        --elseif level == 4 then
        --    RenderSettings.ambientLight = CS.UnityEngine.Color(0.216, 0.236, 0.356)
        --    RenderSettings.fogColor = CS.UnityEngine.Color(0.816, 0.891, 1)
        --    RenderSettings.fogStartDistance = 60
        --    RenderSettings.fogEndDistance = 117
        --elseif level == 5 then
        --    RenderSettings.ambientLight = CS.UnityEngine.Color(0.31, 0.339, 0.453)
        --    RenderSettings.fog = false
        --elseif level == 6 then
        --    RenderSettings.ambientLight = CS.UnityEngine.Color(0.169, 0.214, 0.33)
        --    RenderSettings.fogColor = CS.UnityEngine.Color(0.483, 0.854, 1)
        --    RenderSettings.fogStartDistance = 70
        --    RenderSettings.fogEndDistance = 100
        --elseif level == 7 then
        --    RenderSettings.ambientLight = CS.UnityEngine.Color(0.292, 0.32, 0.358)
        --    RenderSettings.fog = false
        --end
    else
        ambientMode_data = RenderSettings.ambientMode
        ambientColor_data = RenderSettings.ambientLight
        RenderSettings.ambientMode = AmbientMode.Flat
        RenderSettings.ambientLight = CS.UnityEngine.Color(0.219, 0.2745, 0.3843)
        frameRate = CS.UnityEngine.Application.targetFrameRate

        fog_data = RenderSettings.fog
        RenderSettings.fog = false

        shadow_data = QualitySettings.shadows
        QualitySettings.shadows = ShadowQuality.Disable
        CS.UnityEngine.Application.targetFrameRate = 30
    end
    fixedTime_data = CS.UnityEngine.Time.fixedDeltaTime
    CS.UnityEngine.Time.fixedDeltaTime = 0.02
end

local resetrender = function()
    if CS.UnityEngine.RenderSettings.ambientLight ~= nil and ambientColor_data ~= nil and getdevice() ~= 1 then
        RenderSettings.ambientLight = ambientColor_data
        RenderSettings.ambientMode = ambientMode_data
        ambientMode_data = nil
        ambientColor_data = nil

        RenderSettings.fog = fog_data
        RenderSettings.fogColor = fog_color
        RenderSettings.fogMode = fog_mode
        RenderSettings.fogStartDistance = fog_disMix
        RenderSettings.fogEndDistance = fog_disMax

        fog_data = nil
        fog_color = nil
        fog_mode = nil
        fog_disMix = nil
        fog_disMax = nil

        QualitySettings.shadowDistance = shadowDistance
        QualitySettings.shadowmaskMode = shadowMode
        QualitySettings.shadows = shadow_data

        shadowDistance = nil
        shadowMode = nil
        shadow_data = nil
    else
        RenderSettings.ambientMode = ambientMode_data
        RenderSettings.ambientLight = ambientColor_data

        ambientMode_data = nil
        ambientColor_data = nil

        RenderSettings.fog = fog_data
        fog_data = nil

        QualitySettings.shadows = shadow_data
        shadow_data = nil
    end
    if frameRate then
        CS.UnityEngine.Application.targetFrameRate = frameRate
        frameRate = nil
    end
    if fixedTime_data then
        CS.UnityEngine.Time.fixedDeltaTime = fixedTime_data
        fixedTime_data = nil
    end
end
--场景L12
--红人L13
--蓝人L14
--门L15
--其他道具L16
--下一关Ground
--相机渲染 L12 + L13 + L14 + L15 + L16
--场景平行灯 L12 + L15 + L16
--小人平行灯 L13 + L14

local L13 = 13
local L15 = 15
local L16 = 16
local record1
local record2

local setphysic = function()
    record1 = Physics2D.GetIgnoreLayerCollision(L13, L15)
    record2 = Physics2D.GetIgnoreLayerCollision(L13, L16)
    Physics2D.IgnoreLayerCollision(L13, L15, true)
    Physics2D.IgnoreLayerCollision(L13, L16, true)
end
local resetphysic = function()
    Physics2D.IgnoreLayerCollision(L13, L15, record1)
    Physics2D.IgnoreLayerCollision(L13, L16, record2)
end

---加载器
local loaderList = nil
return {
    --启动,如果已经存在清理项目重新打开 loader(resName)异步返回要加载的资源 messager(eventName,...)
    -- messager: 位面消息派发接口, e.g. Finish事件 ,是否完成，评分参数（100) 派发到位面主项目
    -- loader: 位面资源加载接口
    Open = function(self, level, loader, messager, ...)
        -- level = 1
        contex.level = level
        contex.messager = messager
        contex.state = state.LOADING

        ---@class bc_common_game_ctrl 全局配置
        local bc_common_game_ctrl = require "bc_common_game_ctrl"

        bc_common_game_ctrl.Init(level, loader, messager, ...)

        ---全局常量
        require("bcfullfire_config")

        ---全局方法
        require("bcfullfire_helper")

        require "bcfullfire_msg_code"


        ---@class bcfullfire_MsgCenter 全局事件管理器
        bcfullfire_MsgCenter = bc_EventMessager.New()

        ---@type tiny_lang_new lang表相关
        bcfullfire_LangMgr = require("tiny_lang_new").New()

        Debug = CS.UnityEngine.Debug
        --设置渲染环境
        setrender(level)
        --设置物体碰撞
        setphysic()

        local function loadCSVByString(data, headLine, maxCol)
            local tmpMaxCol = maxCol or 9999
            -- 按行划分
            local lineStr = string.split(data, '\r\n')
            local allLine = {}
            local lineStrLength = #lineStr
            for i = headLine + 1, lineStrLength, 1 do
                if string.IsNullOrEmpty(string.trim(lineStr[i])) then
                    break
                end
                -- 一行中，每一列的内容
                local content = string.split(lineStr[i], ",")
                local tmpRow = i - headLine
                if maxCol == nil then
                    allLine[tmpRow] = content
                else
                    local col = math.min(tmpMaxCol, #content)
                    allLine[tmpRow] = {}
                    for j = 1, col, 1 do
                        allLine[tmpRow][j] = content[j]
                    end
                end
            end
            return allLine
        end

        local rolePrefabPair = {}
        local loadCountStep1 = 2
        local function taskCompletedStep1()
            loadCountStep1 = loadCountStep1 - 1
            if loadCountStep1 < 1 then
                bc_Logger.Error("Lang TEST >>>>>>>>>>>" .. bcfullfire_LangMgr:GetLangById(115800001))
                --加载完成 ,进入关卡
                bcfullfireMgr = require("bcfullfire_game_mgr"):New(level, contex, state, self, rolePrefabPair)
            end
        end
        loader(bcfullfire_AssetPath.langCSV, function(ta)
            bcfullfire_LangMgr:ParseData(ta.text)
            taskCompletedStep1()
        end)
        ---CSV
        --加载小人配置
        loader(bcfullfire_AssetPath.roleConfig, function(ta)
            local allDatas = loadCSVByString(ta.text, 1)
            local loadCount = #allDatas
            local function loadCompleted()
                loadCount = loadCount - 1
                if loadCount < 1 then
                    taskCompletedStep1()
                end
            end
            local asset_loader = require("asset_loader")
            local loaderTag = "bcfullfire_load"
            loaderList = {}
            for i, line in ipairs(allDatas) do
                local tmpName = line[1]
                local tmpPath = line[2]
                local loader = asset_loader(tmpPath, loaderTag)
                loader:load(function(loader)
                    rolePrefabPair[tmpName] = loader.asset
                    loadCompleted()
                end)
                loaderList[i] = loader
            end
        end)
    end,
    -- 游戏信息
    GameData = function(self)
        return gameData
    end,
    --当前关卡状态
    State = function(self)
        return contex.state
    end,
    --重置当前关卡
    Reset = function(self)
        if bcfullfireMgr then
            bcfullfireMgr:Restore()
        end
    end,
    --更换关卡
    ChangeLevel = function(self)
        --local modecontrol_new_hero_mgr = require("modecontrol_new_hero_mgr")
        --modecontrol_new_hero_mgr.SetCurFailTimes(0)
        if bcfullfireMgr then
            bcfullfireMgr:ChangeLevel()
        end
    end,
    --暂停
    Pause = function(self)
        if bcfullfireMgr then
            bcfullfireMgr:Pause()
        end
    end,
    --继续
    Resume = function(self)

    end,
    -- 主动结束游戏并触发失败
    Fail = function(self)
        if bcfullfire_MsgCenter then
            bcfullfire_MsgCenter:GetInstance():Broadcast(bcfullfire_EventID.GameOver, true, true)
        end
        SendMessager("Finish", { false, 0 })
        local minigameMgr = require "minigame_mgr"
        minigameMgr.MiniGameFinish(false)
    end,
    -- 主动结束游戏并触发失败
    Finish = function(self)
        SendMessager("Finish", { true, 0 })
        local minigameMgr = require "minigame_mgr"
        minigameMgr.MiniGameFinish(true)
    end,
    -- 主动结束游戏并触发失败
    Exit = function(self)
        local minigameMgr = require "minigame_mgr"
        minigameMgr.MiniGameClose()
    end,
    --清理项目并退出，可能会与open连续调用
    Close = function(self)
        contex.state = state.NONE
        if bcfullfireMgr then
            bcfullfireMgr:Delete()
            bcfullfireMgr = nil
            if loaderList ~= nil then
                for _, v in ipairs(loaderList) do
                    v:Dispose()
                end
                loaderList = nil
            end
            resetrender()
            resetphysic()
            local bc_common_game_ctrl = require "bc_common_game_ctrl"
            if bc_common_game_ctrl then
                bc_common_game_ctrl:Dispose()
            end
            local obj = bc_CS_GameObject.Find("CommonGlobalGameObject")
            if not bc_IsNull(obj) then
                bc_CS_GameObject.DestroyImmediate(obj)
            end
            local gameroot = bc_CS_GameObject.Find("bcfullfireGameRoot")
            if gameroot then
                bc_CS_GameObject.DestroyImmediate(gameroot)
            end
            bc_common_game_ctrl = nil
        end
        -- local t = {}
        -- for k, v in pairs(package.loaded) do
        --     if string.find(k, "bcfullfire_") then
        --         t[#t + 1] = k
        --     end
        -- end
        -- for k, v in pairs(t) do
        --     package.loaded[v] = nil
        -- end
        SendMessager("Close", { true, 0 })
    end,
}
