/****************************************************
 *  Copyright © 2018-2023 冰川网络  All rights reserved.
 *  文件：TinyLocalizationText
 *  作者：wjy
 *  日期：2023/03/10 12:10
 *  功能：小游戏专属多语言组件
*****************************************************/

using System.Collections;
using TinyStaticData;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class TinyLocalizationText : MonoBehaviour
{
    public int id;

    private Text uguiText;

    private TextMeshProUGUI tmpText;

    private void Awake()
    {
        uguiText = gameObject.GetComponent<Text>();

        if (uguiText == null)
        {
            tmpText = gameObject.GetComponent<TextMeshProUGUI>();
        }
    }

    IEnumerator Start()
    {
        yield return new WaitUntil(() => TinyLang.LangCompleted);
        if (uguiText != null)
        {
            uguiText.text = TinyLang.Get(id);
        }

        if (tmpText != null)
        {
            tmpText.text = TinyLang.Get(id);
        }
    }
}