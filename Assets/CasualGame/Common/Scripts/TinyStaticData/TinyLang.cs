/****************************************************
 *  文件：TinyLang.cs
 *  作者：wjy
 *  日期：2023-02-12
 *  功能：
*****************************************************/


using System;
using bc.MiniGameBase;
using UnityEngine;
using War.Script;
using XLua;

namespace TinyStaticData
{
    public class TinyLang
    {
        /// <summary>
        /// 获取配表的ab路径，通过该方法获取包名，可以多一层检查包名及路径的配置是否正确
        /// </summary>
        /// <param name="gameName">当前小游戏的英文名，全局唯一</param>
        /// <returns></returns>
        public static string GetAbPath(string gameName)
        {
            return $"casualgame/{gameName}/tablecsv/lang.csv";
        }

        private static LuaEnv env = null;

        private static bool envInit = false;

        private static LuaEnv GetLuaEnv()
        {
            if (env == null)
            {
                env = LuaEnvironment.LuaEnv;
            }

            if (env == null)
            {
                Debug.LogError("XLuaManager.Instance is null.");
            }
            else
            {
                envInit = true;
            }

            return env;
        }

        private static bool langTableInit = false;

        private static LuaTable langTable = null;


        private static bool parserLangTextComplete = false;

        public static bool LangCompleted => envInit && langTableInit && parserLangTextComplete;


        private static LuaTable GetLangTable()
        {
            if (!langTableInit)
            {
                var dataMid = GetLuaEnv()?.Global.Get<LuaTable>("Tiny_Data_Mid");
                if (dataMid != null)
                {
                    langTable = dataMid.Get<LuaTable>("Lang");
                    if (langTable != null)
                    {
                        langTableInit = true;
                    }
                    else
                    {
                        Debug.LogError("can not find Lang table in tiny_data_mid");
                    }

                    return langTable;
                }
                else
                {
                    Debug.LogError("can not find tiny_data_mid");
                }

                return null;
            }
            else
            {
                return langTable;
            }
        }


        public static void Init()
        {
            //1.获取luaenv
            GetLuaEnv();
            //2.获取独立lang的table
            GetLangTable();
        }


        public static void ParserLangText(string text)
        {
            if (envInit && langTableInit)
            {
                Action<string> parser = langTable.GetInPath<Action<string>>("ParserText");

                if (parser != null)
                {
                    parser(text);
                    parserLangTextComplete = true;
                }
                else
                {
                    Debug.LogError("can not find  ParserText() in lang table");
                }
            }
            else
            {
                Debug.LogError("try call Init() correctly first");
            }
        }


        public static string Get(int id)
        {
            if (envInit && langTableInit)
            {
                //在老的版本的xluagenconfig中只找到这种现成能用的类型，所以选择了多一个参数的类型
                Func<bool, int, string> getFunction = langTable.GetInPath<Func<bool, int, string>>("GetLang");

                if (getFunction != null)
                {
                    return getFunction(false, id);
                }
                else
                {
                    Debug.LogError("can not find  GetLang() in lang table ");
                }
            }

            Debug.LogError("try call Init()  correctly first");
            return "";
        }
    }
}