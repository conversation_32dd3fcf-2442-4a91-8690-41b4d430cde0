using System;
using System.Collections.Generic;
using UnityEngine;

namespace bc.MiniGameBase
{
    public class TimeItem : IBinaryHeapElement, IPoolable
    {
        private float mDelayTime;
        private bool mIsEnable = true;
        private int mRepeatCount;
        private float mSortScore;
        private Action mCallback;
        private int mCallbackTick;
        private int mHeapIndex;
        private bool mIsCache;

        public static TimeItem Acquire(Action callback, float delayTime, int repeatCount = 1)
        {
            TimeItem item = ObjectPool.Acquire<TimeItem>();
            item.Set(callback, delayTime, repeatCount);
            return item;
        }

        public void Recycle()
        {
            ObjectPool.Release(this);
        }

        void IPoolable.Init()
        {
            mCallbackTick = 0;
            IsRecycled = false;
            mIsEnable = true;
            mHeapIndex = 0;
        }

        void IPoolable.Reset()
        {
            IsRecycled = true;
            mCallback = null;
        }

        public void Set(Action callback, float delayTime, int repeatCount)
        {
            mCallbackTick = 0;
            mCallback = callback;
            mDelayTime = delayTime;
            mRepeatCount = repeatCount;
        }

        public void OnTimeTick()
        {
            if (mCallback != null)
            {
                ++mCallbackTick;
                mCallback();
            }

            if (mRepeatCount > 0)
            {
                --mRepeatCount;
            }
        }

        public Action callback
        {
            get { return mCallback; }
        }

        public float SortScore
        {
            get { return mSortScore; }
            set { mSortScore = value; }
        }

        public int HeapIndex
        {
            get { return mHeapIndex; }
            set { mHeapIndex = value; }
        }

        public bool isEnable
        {
            get { return mIsEnable; }
        }

        public bool IsRecycled
        {
            get
            {
                return mIsCache;
            }

            set
            {
                mIsCache = value;
            }
        }

        public float Ticks
        {
            get
            {
                return mCallbackTick;
            }
        }

        public void Cancel()
        {
            if (mIsEnable)
            {
                mIsEnable = false;
                mCallback = null;
            }
        }

        public bool NeedRepeat()
        {
            if (mRepeatCount == 0)
            {
                return false;
            }
            return true;
        }

        public float DelayTime()
        {
            return mDelayTime;
        }

        public void RebuildHeap<T>(BinaryHeap<T> heap) where T : IBinaryHeapElement
        {
            heap.RebuildAtIndex(mHeapIndex);
        }
    }
}