/****************************************************
 *  Copyright © 2018-2024 冰川网络  All rights reserved.
 *  文件：MouseEventBehavior.cs
 *  作者：wjy
 *  日期：2024-08-08 10:57
 *  功能：
 *****************************************************/

using System;
using DG.Tweening;
using UnityEngine;
using UnityEngine.Events;

namespace bc.MiniGameBase
{
    public class MouseEventBehavior : MonoBehaviour
    {
        public UnityEvent<string> onMouseEvent;

        public void OnMouseDown()
        {
            onMouseEvent.Invoke("OnMouseDown");
        }

        private void OnMouseUp()
        {
            onMouseEvent.Invoke("OnMouseUp");
        }

        private void OnMouseEnter()
        {
            onMouseEvent.Invoke("OnMouseEnter");
        }

        private void OnMouseExit()
        {
            onMouseEvent.Invoke("OnMouseExit");
        }
    }
}