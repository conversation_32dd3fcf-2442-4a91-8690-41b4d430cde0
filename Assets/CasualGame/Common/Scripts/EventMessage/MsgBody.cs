using System;
using System.Collections.Generic;

namespace bc.MiniGameBase
{
    public class MsgBody : IPoolable
    {
        public virtual int EventID { get; set; }

        public bool Processed { get; set; }

        public bool ReuseAble { get; set; }

        #region Object Pool

        public static T Allocate<T, E>(E eventId) where T : MsgBody, new() where E : IConvertible
        {
            T msg = ObjectPool.Acquire<T>();
            msg.EventID = eventId.ToInt32(null);
            msg.ReuseAble = true;
            return msg;
        }

        public virtual void Recycle()
        {
            ObjectPool.Release(this);
        }

        protected virtual void Init()
        {
            Processed = false;
        }

        protected virtual void Reset()
        {
            Processed = false;
        }

        void IPoolable.Init()
        {
            Init();
        }

        void IPoolable.Reset()
        {
            Reset();
        }

        #endregion Object Pool
    }
}