using System;
using System.Collections.Generic;
using UnityEngine;

namespace bc.MiniGameBase
{
    #region 事件接口

    public delegate void OnEvent(int key, object param);

    #endregion 事件接口

    public class EventMessager : Singleton<EventMessager>
    {
        private readonly Dictionary<int, ListenerWrap> mAllListenerMap = new Dictionary<int, ListenerWrap>(50);

        #region 内部结构

        private class ListenerWrap : IPoolable
        {
            private LinkedList<OnEvent> mEventList;

            public bool Fire(int key, object param)
            {
                if (mEventList == null)
                {
                    return false;
                }

                var next = mEventList.First;
                OnEvent call = null;
                LinkedListNode<OnEvent> nextCache = null;

                while (next != null)
                {
                    call = next.Value;
                    nextCache = next.Next;
                    call(key, param);

                    next = next.Next ?? nextCache;
                }

                return true;
            }

            public bool Add(OnEvent listener)
            {
                if (mEventList == null)
                {
                    mEventList = ObjectPool.AcquireLinkedList<OnEvent>();
                }
                else if (mEventList.Contains(listener))
                {
                    return false;
                }
                mEventList.AddLast(listener);
                return true;
            }

            public void Remove(OnEvent listener)
            {
                if (mEventList == null)
                {
                    return;
                }
                mEventList.Remove(listener);
            }

            public void RemoveAll()
            {
                if (mEventList == null)
                {
                    return;
                }
                mEventList.Clear();
            }

            public bool IsEmpty()
            {
                if (mEventList == null)
                {
                    return true;
                }
                return mEventList.Count == 0;
            }

            void IPoolable.Init()
            {
                RemoveAll();
            }

            void IPoolable.Reset()
            {
                if (mEventList != null)
                {
                    ObjectPool.Release(mEventList);
                    mEventList = null;
                }
                RemoveAll();
            }
        }

        #endregion 内部结构

        #region 功能函数

        public bool Register(int key, OnEvent fun)
        {
            ListenerWrap wrap;
            if (!mAllListenerMap.TryGetValue(key, out wrap))
            {
                wrap = ObjectPool.Acquire<ListenerWrap>();
                mAllListenerMap.Add(key, wrap);
            }

            if (wrap.Add(fun))
            {
                return true;
            }

            Debug.LogWarning("Already Register Same Event:" + key);
            return false;
        }

        public void UnRegister(int key, OnEvent fun)
        {
            ListenerWrap wrap;
            if (mAllListenerMap.TryGetValue(key, out wrap))
            {
                wrap.Remove(fun);
                if (wrap.IsEmpty())
                {
                    UnRegister(key);
                }
            }
        }

        public void UnRegister(int key)
        {
            ListenerWrap wrap;
            if (mAllListenerMap.TryGetValue(key, out wrap))
            {
                wrap.RemoveAll();
                ObjectPool.Release(ref wrap);

                mAllListenerMap.Remove(key);
            }
        }

        public bool Send(int key, object param)
        {
            ListenerWrap wrap;
            if (mAllListenerMap.TryGetValue(key, out wrap))
            {
                return wrap.Fire(key, param);
            }
            return false;
        }

        public void OnRecycled()
        {
            mAllListenerMap.Clear();
        }

        #endregion 功能函数

        public static bool SendEvent(int key, object param)
        {
            return Instance.Send(key, param);
        }

        public static bool RegisterEvent(int key, IEventSink sink)
        {
            return Instance.Register(key, sink.OnExcute);
        }

        public static void UnRegisterEvent(int key, IEventSink sink)
        {
            Instance.UnRegister(key, sink.OnExcute);
        }
    }
}