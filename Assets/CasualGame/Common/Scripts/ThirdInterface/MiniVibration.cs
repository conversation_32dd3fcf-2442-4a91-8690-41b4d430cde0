using Lofelt.NiceVibrations;

namespace bc.MiniGameBase
{
    /// <summary>
    /// 振动Api，目前是调用NiceVibrations插件实现
    /// 根据机型的马达不同，体感会有所差异
    /// </summary>
    public static class MiniVibration
    {
        /// <summary>
        /// 表示不同的振动预设类型
        /// 根据机型的马达不同，体感会有所差异
        /// </summary>
        public enum PresetType
        {
            /// <summary>
            /// 选择
            /// </summary>
            Selection = 0,

            /// <summary>
            /// 成功
            /// </summary>
            Success = 1,

            /// <summary>
            /// 警告
            /// </summary>
            Warning = 2,

            /// <summary>
            /// 失败
            /// </summary>
            Failure = 3,

            /// <summary>
            /// 轻度
            /// </summary>
            LightImpact = 4,

            /// <summary>
            /// 中等强度
            /// </summary>
            MediumImpact = 5,

            /// <summary>
            /// 重度
            /// </summary>
            HeavyImpact = 6,

            /// <summary>
            /// 刚性冲击
            /// </summary>
            RigidImpact = 7,

            /// <summary>
            /// 柔性冲击
            /// </summary>
            SoftImpact = 8,

            /// <summary>
            /// 无
            /// </summary>
            None = -1
        }

        /// <summary>
        /// 播放一个持续0.1s的振动.
        /// </summary>
        /// <param name="amplitude">振动幅度from 0.0 to 1.0</param>
        /// <param name="frequency">振动频率from 0.0 to 1.0</param>
        public static void PlayEmphasis(float amplitude = 0.3f, float frequency = 0.3f)
        {
            HapticPatterns.PlayEmphasis(amplitude, frequency);
        }

        /// <summary>
        /// 播放一个具有恒定振幅和频率的振动
        /// Plays a haptic with constant amplitude and frequency.
        /// </summary>
        /// <param name="duration">持续时间,大于0</param>
        /// <param name="amplitude">振动幅度from 0.0 to 1.0</param>
        /// <param name="frequency">振动频率from 0.0 to 1.0</param>
        public static void PlayConstant(float duration, float amplitude = 0.3f, float frequency = 0.3f)
        {
            HapticPatterns.PlayConstant(amplitude, frequency, duration);
        }

        /// <summary>
        /// 播放一个振动预设
        /// </summary>
        /// <param name="presetType">预设类型</param>
        public static void PlayPreset(PresetType presetType)
        {
            HapticPatterns.PlayPreset((HapticPatterns.PresetType) (int) presetType);
        }
    }
}