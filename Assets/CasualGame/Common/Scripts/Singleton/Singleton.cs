using System;
using System.Collections.Generic;
using UnityEngine;

namespace bc.MiniGameBase
{
    public abstract class Singleton<T> where T : class, new()
    {
        private static T m_instance;

        public static T Instance
        {
            get
            {
                if (m_instance == null)
                {
                    m_instance = Activator.CreateInstance<T>();
                    if (m_instance != null)
                    {
                        (m_instance as Singleton<T>).OnSingletonInit();
                    }
                }

                return m_instance;
            }
        }

        public void StartUp()
        {

        }

        public static void Dispose()
        {
            if (m_instance != null)
            {
                (m_instance as Singleton<T>).OnDispose();
                m_instance = (T)((object)null);
            }
        }

        protected virtual void OnSingletonInit()
        {
        }

        protected virtual void OnDispose()
        {

        }
    }
}