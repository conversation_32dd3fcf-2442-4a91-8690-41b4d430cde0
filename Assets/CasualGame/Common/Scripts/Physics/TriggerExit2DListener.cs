using UnityEngine;
using UnityEngine.Events;

namespace bc.MiniGameBase
{

    public class TriggerExit2DListener : MonoBehaviour
    {
        public static TriggerExit2DListener Get(GameObject go)
        {
            var listener = go.GetComponent<TriggerExit2DListener>();
            if (listener == null) listener = go.AddComponent<TriggerExit2DListener>();
            return listener;
        }

        private UnityEvent<Collider2D> mEvents;

        private void s_Register(UnityAction<Collider2D> action)
        {
            if (mEvents == null)
                mEvents = new UnityEvent<Collider2D>();
            (mEvents).AddListener(action);
        }

        private void s_UnRegister(UnityAction<Collider2D> action)
        {
            (mEvents)?.RemoveListener(action);
        }

        private void Dispatch<T>(T value)
        {

        }

        public void Register(UnityAction<Collider2D> action)
        {
            s_Register(action);
        }

        public void UnRegister(UnityAction<Collider2D> action)
        {
            s_UnRegister(action);
        }

        private void OnTriggerExit2D(Collider2D collider)
        {
            (mEvents)?.Invoke(collider);
        }

        private void OnDestroy()
        {
            mEvents?.RemoveAllListeners();
        }
    }
}