using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace bc.MiniGameBase
{
    public class PhysicsRaycast : MonoBehaviour
    {
        public bool Raycast(Vector3 origin, Vector3 direction, out RaycastHit hitInfo, float maxDistance, int layerMask, QueryTriggerInteraction queryTriggerInteraction = QueryTriggerInteraction.UseGlobal)
        {
            return Physics.Raycast(origin, direction, out hitInfo, maxDistance, layerMask, queryTriggerInteraction);
        }
    }
}