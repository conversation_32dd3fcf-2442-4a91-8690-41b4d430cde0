using UnityEngine;
using UnityEngine.Events;

namespace bc.MiniGameBase
{

    public class TriggerStayListener : MonoBehaviour
    {
        public static TriggerStayListener Get(GameObject go)
        {
            var listener = go.GetComponent<TriggerStayListener>();
            if (listener == null) listener = go.AddComponent<TriggerStayListener>();
            return listener;
        }

        private UnityEvent<Collider> mEvents;

        private void s_Register(UnityAction<Collider> action)
        {
            if (mEvents == null)
                mEvents = new UnityEvent<Collider>();
            (mEvents).AddListener(action);
        }

        private void s_UnRegister(UnityAction<Collider> action)
        {
            (mEvents)?.RemoveListener(action);
        }

        private void Dispatch(Collider value)
        {
         
        }

        public void Register(UnityAction<Collider> action)
        {
            s_Register(action);
        }

        public void UnRegister(UnityAction<Collider> action)
        {
            s_UnRegister(action);
        }

        private void OnTriggerStay(Collider collision)
        {
            (mEvents)?.Invoke(collision);
        }
        private void OnDestroy()
        {
            mEvents?.RemoveAllListeners();
        }
    }
}