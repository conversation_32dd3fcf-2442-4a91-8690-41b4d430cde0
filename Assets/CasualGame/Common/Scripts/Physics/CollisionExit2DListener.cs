using UnityEngine;
using UnityEngine.Events;

namespace bc.MiniGameBase
{

    public class CollisionExit2DListener : MonoBehaviour
    {
        public static CollisionExit2DListener Get(GameObject go)
        {
            var listener = go.GetComponent<CollisionExit2DListener>();
            if (listener == null) listener = go.AddComponent<CollisionExit2DListener>();
            return listener;
        }

        private UnityEvent<Collision2D> mEvents;

        private void s_Register(UnityAction<Collision2D> action)
        {
            if (mEvents == null)
                mEvents = new UnityEvent<Collision2D>();
            (mEvents).AddListener(action);
        }

        private void s_UnRegister(UnityAction<Collision2D> action)
        {
            (mEvents)?.RemoveListener(action);
        }

        private void Dispatch<T>(T value)
        {

        }

        public void Register(UnityAction<Collision2D> action)
        {
            s_Register(action);
        }

        public void UnRegister(UnityAction<Collision2D> action)
        {
            s_UnRegister(action);
        }

        private void OnCollisionExit2D(Collision2D collision)
        {
            (mEvents)?.Invoke(collision);
        }

        private void OnDestroy()
        {
            mEvents?.RemoveAllListeners();
        }
    }
}