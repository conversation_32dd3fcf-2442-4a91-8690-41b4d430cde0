using UnityEngine.Events;

namespace bc.MiniGameBase
{
    public class PhysicsEvent<T> : UnityEvent<T> { }

    enum PhysicsEventName
    {
        CollisionEnter,
        CollisionExit,
        CollisionStay,
        CollisionEnter2D,
        CollisionExit2D,
        CollisionStay2D,
        TriggerEnter,
        TriggerExit,
        TriggerStay,
        TriggerEnter2D,
        TriggerExit2D,
        TriggerStay2D,
    }
    public class PhysicsBase
    {

    }
}