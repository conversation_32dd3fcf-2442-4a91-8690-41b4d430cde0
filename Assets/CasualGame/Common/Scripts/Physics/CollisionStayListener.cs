using UnityEngine;
using UnityEngine.Events;

namespace bc.MiniGameBase
{

    public class CollisionStayListener : MonoBehaviour
    {
        public static CollisionStayListener Get(GameObject go)
        {
            var listener = go.GetComponent<CollisionStayListener>();
            if (listener == null) listener = go.AddComponent<CollisionStayListener>();
            return listener;
        }

        private UnityEvent<Collision> mEvents;

        private void s_Register(UnityAction<Collision> action)
        {
            if (mEvents == null)
                mEvents = new UnityEvent<Collision>();
            (mEvents).AddListener(action);
        }
        private void s_UnRegister(UnityAction<Collision> action)
        {
            (mEvents)?.RemoveListener(action);
        }

        private void Dispatch<T>(T value)
        {

        }

        public void Register(UnityAction<Collision> action)
        {
            s_Register(action);
        }

        public void UnRegister(UnityAction<Collision> action)
        {
            s_UnRegister(action);
        }

        private void OnCollisionStay(Collision collision)
        {
            (mEvents)?.Invoke(collision);
        }

        private void OnDestroy()
        {
            mEvents?.RemoveAllListeners();
        }
    }
}