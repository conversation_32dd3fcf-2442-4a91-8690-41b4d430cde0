using UnityEngine;
using UnityEngine.Events;

namespace bc.MiniGameBase
{

    public class CollisionEnterListener : MonoBehaviour
    {
        public static CollisionEnterListener Get(GameObject go)
        {
            var listener = go.GetComponent<CollisionEnterListener>();
            if (listener == null) listener = go.AddComponent<CollisionEnterListener>();
            return listener;
        }

        private UnityEvent<Collision> mEvents;

        private void s_Register(UnityAction<Collision> action)
        {
            if (mEvents == null)
                mEvents = new UnityEvent<Collision>();
            (mEvents).AddListener(action);
        }
        private void s_UnRegister(UnityAction<Collision> action)
        {
            (mEvents)?.RemoveListener(action);
        }

        private void Dispatch<T>(T value)
        {

        }

        public void Register(UnityAction<Collision> action)
        {
            s_Register(action);
        }

        public void UnRegister(UnityAction<Collision> action)
        {
            s_UnRegister(action);
        }
        private void OnCollisionEnter(Collision collision)
        {
            (mEvents)?.Invoke(collision);
        }

        private void OnDestroy()
        {
            mEvents?.RemoveAllListeners();
        }
    }
}