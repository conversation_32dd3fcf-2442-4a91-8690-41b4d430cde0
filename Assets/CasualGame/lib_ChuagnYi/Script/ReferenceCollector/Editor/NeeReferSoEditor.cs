using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEditor.Experimental.GraphView;
using UnityEngine;
using Object = UnityEngine.Object;


namespace CasualGame.lib_ChuagnYi.NeeG
{
    [CustomEditor(typeof(NeeReferSo))]
    [CanEditMultipleObjects]
    public class NeeReferSoEditor : Editor
    {
        private string searchKey
        {
            get { return _searchKey; }
            set
            {
                if (_searchKey != value)
                {
                    _searchKey = value;
                    heroPrefab = _neeReferCollection.Get<Object>(searchKey);
                }
            }
        }

        private NeeReferSo _neeReferCollection;

        private Object heroPrefab;

        private string _searchKey = "";

        private void DelNullReference()
        {
            var dataProperty = serializedObject.FindProperty("data");
            for (int i = dataProperty.arraySize - 1; i >= 0; i--)
            {
                var gameObjectProperty = dataProperty.GetArrayElementAtIndex(i).FindPropertyRelative("gameObject");
                if (gameObjectProperty.objectReferenceValue == null)
                {
                    dataProperty.DeleteArrayElementAtIndex(i);
                }
            }
        }

        private void OnEnable()
        {
            _neeReferCollection = (NeeReferSo) target;
        }

        public override void OnInspectorGUI()
        {
            Undo.RecordObject(_neeReferCollection, "Changed Settings");
            var dataProperty = serializedObject.FindProperty("data");
            GUILayout.BeginHorizontal();
            if (GUILayout.Button("添加引用"))
            {
                AddReference(dataProperty, Guid.NewGuid().GetHashCode().ToString(), null);
            }

            if (GUILayout.Button("全部删除"))
            {
                dataProperty.ClearArray();
            }

            if (GUILayout.Button("删除空引用"))
            {
                DelNullReference();
            }

            if (GUILayout.Button("排序"))
            {
                _neeReferCollection.Sort();
            }

            EditorGUILayout.EndHorizontal();
            EditorGUILayout.BeginHorizontal();
            GUILayout.Label("过滤器:", GUILayout.Width(50));
            searchKey = EditorGUILayout.TextField(searchKey);
            EditorGUILayout.ObjectField(heroPrefab, typeof(Object), false);
            if (GUILayout.Button("删除key或者object"))
            {
                _neeReferCollection.Remove(searchKey);
                heroPrefab = null;
            }

            GUILayout.EndHorizontal();
            EditorGUILayout.Space();

            var                delList = new List<int>();
            SerializedProperty property;
            for (int i = dataProperty.arraySize - 1; i >= 0; i--)
            {
                property = dataProperty.GetArrayElementAtIndex(i).FindPropertyRelative("key");
                if (!string.IsNullOrEmpty(searchKey) &&
                    !property.stringValue.ToLower().Contains(searchKey.ToLower())) continue;
                GUILayout.BeginHorizontal();
                property.stringValue = EditorGUILayout.TextField(property.stringValue, GUILayout.Width(150));
                property             = dataProperty.GetArrayElementAtIndex(i).FindPropertyRelative("gameObject");
                property.objectReferenceValue =
                        EditorGUILayout.ObjectField(property.objectReferenceValue, typeof(Object), true);
                if (GUILayout.Button("X"))
                {
                    delList.Add(i);
                }

                GUILayout.EndHorizontal();
            }

            var eventType = Event.current.type;
            if (eventType == EventType.DragUpdated || eventType == EventType.DragPerform)
            {
                // Show a copy icon on the drag
                DragAndDrop.visualMode = DragAndDropVisualMode.Copy;

                if (eventType == EventType.DragPerform)
                {
                    DragAndDrop.AcceptDrag();
                    foreach (var o in DragAndDrop.objectReferences)
                    {
                        switch (o)
                        {
                            case GameObject go:
                                var allComp = go.GetComponents<Component>();
                                _provider ??= ScriptableObject.CreateInstance<ComppnentsSearchProvider>();
                                _provider.Init(allComp, x =>
                                {
                                    AddReference(dataProperty, o.name, x.userData as Object);
                                    
                                    foreach (var i in delList)
                                    {
                                        dataProperty.DeleteArrayElementAtIndex(i);
                                    }

                                    serializedObject.ApplyModifiedProperties();
                                    serializedObject.UpdateIfRequiredOrScript();
                                });
                                SearchWindow.Open(
                                        new SearchWindowContext(
                                                GUIUtility.GUIToScreenPoint(Event.current.mousePosition)), _provider
                                );
                                break;
                            case Component go:
                                AddReference(dataProperty, o.name, o);
                                break;
                            default:
                                Debug.Log("PoolSo 只能添加Gameobject或者Component");
                                // AddReference(dataProperty, o.name, o);
                                break;
                        }
                    }
                }

                Event.current.Use();
            }

            foreach (var i in delList)
            {
                dataProperty.DeleteArrayElementAtIndex(i);
            }

            serializedObject.ApplyModifiedProperties();
            serializedObject.UpdateIfRequiredOrScript();
        }

        private ComppnentsSearchProvider _provider;

        private void AddReference(SerializedProperty dataProperty, string key, Object obj)
        {
            int index = dataProperty.arraySize;
            dataProperty.InsertArrayElementAtIndex(index);
            var element = dataProperty.GetArrayElementAtIndex(index);
            element.FindPropertyRelative("key").stringValue                 = key;
            element.FindPropertyRelative("gameObject").objectReferenceValue = obj;
        }
    }
}