using System;
using System.Collections.Generic;
using CasualGame.lib_ChuagnYi.NeeG.Pool;
using UnityEditor.Experimental.GraphView;
using UnityEngine;

namespace CasualGame.lib_ChuagnYi.NeeG
{
    public class ComppnentsSearchProvider : ScriptableObject, ISearchWindowProvider
    {
        public Component[] arr;

        public System.Action<SearchTreeEntry> callBack;
        // public List<SearchTreeEntry> list;

        public void Init(Component[] arr2, System.Action<SearchTreeEntry> callBack)
        {
            this.arr      = arr2;
            this.callBack = callBack;
        }

        public List<SearchTreeEntry> CreateSearchTree(SearchWindowContext context)
        {
            // if (list != null)
            // {
            //     NeeListPool<SearchTreeEntry>.Free(list);
            // }
            var list = NeeListPool<SearchTreeEntry>.New();
            list.Add(new SearchTreeGroupEntry(new GUIContent(arr[0].gameObject.name), 0));
            var entryGo = new SearchTreeEntry(new GUIContent("gameObject"));
            entryGo.userData = arr[0].gameObject;
            entryGo.level    = 1;
            list.Add(entryGo);
            foreach (var item in arr)
            {
                var entry = new SearchTreeEntry(new GUIContent(item.GetType().Name));
                entry.userData = item;
                entry.level    = 1;
                list.Add(entry);
            }

            return list;
        }

        public bool OnSelectEntry(SearchTreeEntry SearchTreeEntry, SearchWindowContext context)
        {
            callBack?.Invoke(SearchTreeEntry);
            return true;
        }
    }
}