using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEditor.Experimental.GraphView;
using UnityEngine;
using Object = UnityEngine.Object;


namespace CasualGame.lib_ChuagnYi.NeeG
{
    [CustomEditor(typeof(NeeReferCollection))]
    // [CanEditMultipleObjects]
    public class NeeReferCollectionEditor : Editor
    {
        private string searchKey
        {
            get { return _searchKey; }
            set
            {
                if (_searchKey != value)
                {
                    _searchKey = value;
                    heroPrefab = _neeReferCollection.Get<Object>(searchKey);
                }
            }
        }

        private NeeReferCollection _neeReferCollection;

        private Object heroPrefab;

        private string _searchKey = "";

        private void DelNullReference()
        {
            var dataProperty = serializedObject.FindProperty("data");
            for (int i = dataProperty.arraySize - 1; i >= 0; i--)
            {
                var gameObjectProperty = dataProperty.GetArrayElementAtIndex(i).FindPropertyRelative("gameObject");
                if (gameObjectProperty.objectReferenceValue == null)
                {
                    dataProperty.DeleteArrayElementAtIndex(i);
                }
            }
        }

        private GUIStyle changedStyle;

        private void OnEnable()
        {
            _neeReferCollection = (NeeReferCollection) target;
            changedStyle        = new GUIStyle();
        }

        public override void OnInspectorGUI()
        {
            Undo.RecordObject(_neeReferCollection, "Changed Settings");
            var dataProperty = serializedObject.FindProperty("data");
            GUILayout.BeginHorizontal();
            var isPrefabOverrided = PrefabUtility.IsPartOfPrefabInstance(_neeReferCollection) &&
                                    (PrefabUtility.GetObjectOverrides(_neeReferCollection.gameObject).Find(x=>x.instanceObject==_neeReferCollection)!=null );

            if (GUILayout.Button("添加引用"))
            {
                AddReference(dataProperty, Guid.NewGuid().GetHashCode().ToString(), null);
            }

            if (GUILayout.Button("全部删除"))
            {
                dataProperty.ClearArray();
            }

            if (GUILayout.Button("删除空引用"))
            {
                DelNullReference();
            }

            if (GUILayout.Button("排序"))
            {
                _neeReferCollection.Sort();
            }

            EditorGUILayout.EndHorizontal();
            EditorGUILayout.BeginHorizontal();
            GUILayout.Label("过滤器:", GUILayout.Width(50));
            searchKey = EditorGUILayout.TextField(searchKey);
            EditorGUILayout.ObjectField(heroPrefab, typeof(Object), false);
            if (GUILayout.Button("删除key"))
            {
                _neeReferCollection.Remove(searchKey);
                heroPrefab = null;
            }

            GUILayout.EndHorizontal();
            EditorGUILayout.Space();

            var                delList = new List<int>();
            SerializedProperty property;
            SerializedProperty property2;
            var                colorPre  = GUI.backgroundColor;
            var                colorPre2 = GUI.backgroundColor;

            NeeReferCollectionEditor prefabEditor;
            SerializedProperty       prefabEditorDataProperty = null;
            if (isPrefabOverrided)
            {
                var s = new GUIStyle();
                s.normal.textColor = new Color(1f, 0.37f, 0.37f);
                GUILayout.Label("检测到与prefab有如下差异", s);
                EditorGUILayout.PropertyField(dataProperty, true);
                GUILayout.Label("----------------------------------------------", s);

                GUI.backgroundColor = new Color(0.89f, 0.97f, 1f);

                var prefabTarget = PrefabUtility.GetCorrespondingObjectFromSource(_neeReferCollection.gameObject);
                if (prefabTarget)
                {
                    SerializedObject temp = new SerializedObject(prefabTarget.GetComponent<NeeReferCollection>());
                    prefabEditorDataProperty = temp.FindProperty("data");
                }
            }

            for (int i = dataProperty.arraySize - 1; i >= 0; i--)
            {
                property  = dataProperty.GetArrayElementAtIndex(i).FindPropertyRelative("key");
                property2 = dataProperty.GetArrayElementAtIndex(i).FindPropertyRelative("gameObject");

                bool isChanged = false;
                if (prefabEditorDataProperty != null)
                {
                    isChanged = true;
                    for (int j = prefabEditorDataProperty.arraySize - 1; j >= 0; j--)
                    {
                        var propertyTemp = prefabEditorDataProperty.GetArrayElementAtIndex(j)
                                                                   .FindPropertyRelative("key").stringValue;
                        // var go           = prefabEditorDataProperty.GetArrayElementAtIndex(j).FindPropertyRelative("gameObject").objectReferenceValue;

                        if (propertyTemp == property.stringValue)
                        {
                            isChanged = false;
                            break;
                        }
                    }
                }

                if (isChanged)
                {
                    GUI.backgroundColor = new Color(0.83f, 0.92f, 1f);
                }

                if (!string.IsNullOrEmpty(searchKey) &&
                    !property.stringValue.ToLower().Contains(searchKey.ToLower())) continue;
                GUILayout.BeginHorizontal();
                property.stringValue = EditorGUILayout.TextField(property.stringValue, GUILayout.Width(150));
                property2.objectReferenceValue =
                        EditorGUILayout.ObjectField(property2.objectReferenceValue, typeof(Object), true);
                if (GUILayout.Button("X"))
                {
                    delList.Add(i);
                }

                if (isChanged)
                {
                    GUI.backgroundColor = new Color(0.89f, 0.97f, 1f);
                }

                GUILayout.EndHorizontal();
            }

            if (isPrefabOverrided)
            {
                GUI.backgroundColor = colorPre;
            }

            var eventType = Event.current.type;
            if (eventType == EventType.DragUpdated || eventType == EventType.DragPerform)
            {
                // Show a copy icon on the drag
                DragAndDrop.visualMode = DragAndDropVisualMode.Copy;

                if (eventType == EventType.DragPerform)
                {
                    DragAndDrop.AcceptDrag();
                    if (DragAndDrop.objectReferences.Length > 1)
                    {
                        foreach (var o in DragAndDrop.objectReferences)
                        {
                            AddReference(dataProperty, o.name, o);
                        }
                    }
                    else
                    {
                        foreach (var o in DragAndDrop.objectReferences)
                        {
                            switch (o)
                            {
                                case GameObject go:
                                    var allComp = go.GetComponents<Component>();
                                    _provider ??= ScriptableObject.CreateInstance<ComppnentsSearchProvider>();
                                    _provider.Init(allComp, x =>
                                    {
                                        AddReference(dataProperty, o.name, x.userData as Object);

                                        foreach (var i in delList)
                                        {
                                            dataProperty.DeleteArrayElementAtIndex(i);
                                        }

                                        serializedObject.ApplyModifiedProperties();
                                        serializedObject.UpdateIfRequiredOrScript();
                                    });
                                    SearchWindow.Open(
                                            new SearchWindowContext(
                                                    GUIUtility.GUIToScreenPoint(Event.current.mousePosition)), _provider
                                    );

                                    break;
                                default:
                                    AddReference(dataProperty, o.name, o);
                                    break;
                            }
                        }
                    }
                }

                Event.current.Use();
            }

            foreach (var i in delList)
            {
                dataProperty.DeleteArrayElementAtIndex(i);
            }

            serializedObject.ApplyModifiedProperties();
            serializedObject.UpdateIfRequiredOrScript();
        }

        private ComppnentsSearchProvider _provider;

        private void AddReference(SerializedProperty dataProperty, string key, Object obj)
        {
            int index = dataProperty.arraySize;
            dataProperty.InsertArrayElementAtIndex(index);
            var element = dataProperty.GetArrayElementAtIndex(index);
            element.FindPropertyRelative("key").stringValue                 = key;
            element.FindPropertyRelative("gameObject").objectReferenceValue = obj;
        }
    }
}