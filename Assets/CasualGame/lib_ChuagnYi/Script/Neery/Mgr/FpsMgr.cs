using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace CasualGame.lib_ChuagnYi.NeeG
{
    /// <summary>
    /// Fps
    /// </summary>
    [ScrptsOrder(EScriptsOrder.Init + 10)]
    public class FpsMgr : NeeMonoSingleton<FpsMgr>
    {
        float timeDelta;
        float frameDelta;
        int   setFPsCounter = 0;

        public int  tarFps;
        public bool showFps;

        private System.Action<int> onLastFps;

        public                        int             fps;
        [System.NonSerialized] public LinkedList<int> fpsList;

        public int averageFpsSinceStart
        {
            get
            {
                if (fpsList.Count > 30)
                {
                    float sum = 0;
                    foreach (var fps in fpsList)
                    {
                        sum += fps;
                    }

                    return Mathf.FloorToInt(sum / fpsList.Count);
                }
                else
                {
                    return -1;
                }
            }
        }

        private void Awake()
        {
            fpsList = new LinkedList<int>();
            if (tarFps >= 30)
            {
                Application.targetFrameRate = tarFps;
            }

            StartCoroutine(UpdateCounter());
            //if (!Log.useLog)
            //{
            //    this.gameObject.SetActive(false);
            //}
        }

        protected override void OnApplicationQuit()
        {
            base.OnApplicationQuit();
            if (fpsList.Count > 30)
            {
                PlayerPrefs.SetInt("neegame_Fps", this.averageFpsSinceStart);
                this.fpsList.Clear();
                PlayerPrefs.Save();
            }
        }

        public int lastFps(int count, int defaultV = 40)
        {
            if (fpsList.Count > count)
            {
                float sum     = 0;
                var   i       = fpsList.Last;
                var   counter = count;
                while (counter > 0)
                {
                    counter--;
                    sum += i.Value;
                    i   =  i.Previous;
                }

                return Mathf.FloorToInt(sum / count);
            }
            else
            {
                return defaultV;
            }
        }

        private void OnGUI()
        {
            if (!showFps) return;
            GUIStyle guiStyle = new GUIStyle();
            guiStyle.normal.background = null;
            guiStyle.normal.textColor  = new Color(0.29f, 1f, 0.32f);
            guiStyle.fontSize          = (int) (Screen.width * 0.045f);

            //居中显示FPS
            GUI.Label(new Rect(Screen.width * 0.8f, 0, Screen.width * 0.05f, Screen.height * 0.05f),
                    "FPS: " + (int) fps, guiStyle);
        }

        IEnumerator UpdateCounter()
        {
            while (true)
            {
                var lastFrameCount = Time.frameCount;
                var lastTime       = Time.realtimeSinceStartup;

                yield return new WaitForSeconds(1);

                timeDelta  = Time.realtimeSinceStartup - lastTime;
                frameDelta = Time.frameCount           - lastFrameCount;

                fps = Mathf.FloorToInt(frameDelta / timeDelta);
                if (fpsList.Count > 600)
                {
                    fpsList.RemoveFirst();
                }

                fpsList.AddLast(fps);

                setFPsCounter++;
                if (setFPsCounter > 5)
                {
                    setFPsCounter = 0;
                    var lastFps = this.lastFps(5, -1);
                    if (lastFps > 0)
                    {
                        onLastFps?.Invoke(lastFps);
                        // NeeGame.GetMgr<DelegateMgr>()?.Fire<(FpsMgr, int)>((this, lastFps));
                    }
                }
            }
        }
    }
}