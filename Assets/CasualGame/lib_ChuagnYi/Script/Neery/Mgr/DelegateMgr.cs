using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CasualGame.lib_ChuagnYi.NeeG.Pool;
using Sirenix.OdinInspector;
using UnityEngine;
using Object = UnityEngine.Object;

namespace CasualGame.lib_ChuagnYi.NeeG
{
    public interface IEventSuber
    {
        public bool isEventSuber { get; set; }
        public int  GetSuberId();
    }

    /// <summary>
    ///分为Action,ActionT以及Action T,Task
    /// ActionT以及Action T,Task> 每个suber只能suber一个！
    /// </summary>
    public partial class DelegateMgr : NeeMonoSingleton<DelegateMgr>
    {
      
        [ShowInInspector] public readonly Dictionary<(string, int), Dictionary<int, Action>> eveDictAction =
                new Dictionary<(string, int), Dictionary<int, Action>>();

        [ShowInInspector] public readonly Dictionary<int, HashSet<(string, int)>> keysDict =
                new Dictionary<int, HashSet<(string, int)>>();

        protected override void OnDestroy()
        {
            eveDictActionGeneric.Clear();
            eveDictAction.Clear();
            base.OnDestroy();
        }

       
        public void UnSubAll(IEventSuber suber)
        {
            if (keysDict.TryGetValue(suber.GetSuberId(), out var keyList))
            {
                foreach (var kId in keyList)
                {
                    //eveDict.Remove(kId);
                    //eveDict2.Remove(kId);
                    if (eveDictActionGeneric.TryGetValue(kId, out var set1))
                    {
                        set1.Remove(suber.GetSuberId());
                        if (set1.Count == 0)
                        {
                            DictionaryPool<int, Delegate>.Free(eveDictActionGeneric[(kId)]);
                            eveDictActionGeneric.Remove(kId);
                        }
                    }

                    if (eveDictAction.TryGetValue(kId, out var set2))
                    {
                        set2.Remove(suber.GetSuberId());
                        if (set2.Count == 0)
                        {
                            DictionaryPool<int, Action>.Free(eveDictAction[(kId)]);
                            eveDictAction.Remove(kId);
                        }
                    }
                }

                keyList.Clear();
                HashSetPool<(string, int)>.Free(keysDict[suber.GetSuberId()]);
                keysDict.Remove(suber.GetSuberId());
            }
        }

        /// <summary>
        ///     
        /// </summary>
        public void SubAcion(IEventSuber suber, Action action, string key, Object targerId = null)
        {
            if (!string.IsNullOrEmpty(key))
                key = key.Trim();
            targerId           = targerId ?? NeeGame.Instance;
            suber.isEventSuber = true;
            //if (targerId <= 0)
            //    targerId = 0;
            if (eveDictAction.TryGetValue((key, targerId.GetInstanceID()), out var list))
            {
                if (!list.ContainsKey(suber.GetSuberId()))
                    list.Add(suber.GetSuberId(), action);
                else
                {
                    list[suber.GetSuberId()] += action;
                }
            }
            else
            {
                // var newList = new Dictionary<IEventSuber, Action>();
                var newList = DictionaryPool<int, Action>.New();
                newList.Add(suber.GetSuberId(), action);
                eveDictAction.Add((key, targerId.GetInstanceID()), newList);
            }

            if (keysDict.TryGetValue(suber.GetSuberId(), out var keyList))
            {
                keyList.Add((key, targerId.GetInstanceID()));
            }
            else
            {
                // var newList = new HashSet<(string, Object)>();
                var newList = HashSetPool<(string, int)>.New();
                newList.Add((key, targerId.GetInstanceID()));
                keysDict.Add(suber.GetSuberId(), newList);
            }
        }

        /// <summary>
        ///     
        /// </summary>
        public void UnSubAction(IEventSuber suber, Action action, string key, Object targerId = null)
        {
            if (!string.IsNullOrEmpty(key))
                key = key.Trim();
            targerId = targerId ?? NeeGame.Instance;
            //if (targerId <= 0)
            //    targerId = 0;
            if (eveDictAction.TryGetValue((key, targerId.GetInstanceID()), out var list))
            {
                // list.Remove(suber.GetSuberId());
                list[suber.GetSuberId()] -= action;
                if (list.Count == 0) eveDictAction.Remove((key, targerId.GetInstanceID()));
            }
            else
            {
                Debug.Log($"不存在delegate： {key}");
            }

            if (keysDict.TryGetValue(suber.GetSuberId(), out var keyList))
            {
                keyList.Remove((key, targerId.GetInstanceID()));
                if (list.Count == 0)
                {
                    HashSetPool<(string, int)>.Free(keysDict[(suber.GetSuberId())]);
                    keysDict.Remove(suber.GetSuberId());
                }
            }
        }

        /// <summary>
        ///     
        /// </summary>
        public void FireAction(string key, Object targerId = null)
        {
            if (!string.IsNullOrEmpty(key))
                key = key.Trim();
            if (needAysncInit && isAsyncInited == false)
            {
                Debug.LogError("尚未初始化完成");
                return;
            }

            targerId = targerId ?? NeeGame.Instance;
            //if (targerId <= 0)
            //    targerId = 0;
            if (eveDictAction.TryGetValue((key, targerId.GetInstanceID()), out var list))
                foreach (var i in list.Values)
                    i?.Invoke();
        }
    }
}