using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CasualGame.lib_ChuagnYi.NeeG.Pool;
using Sirenix.OdinInspector;
using UnityEngine;
using Object = UnityEngine.Object;

namespace CasualGame.lib_ChuagnYi.NeeG
{
    public partial class DelegateMgr
    {
        [ShowInInspector] public readonly Dictionary<(string, int), Dictionary<int, Delegate>> eveDictActionGeneric =
                new Dictionary<(string, int), Dictionary<int, Delegate>>();

        /// <summary>
        /// ActionT以及Action T,Task> 每个suber只能suber一个！
        /// </summary>
        private void _SubCore<T>(IEventSuber suber, Delegate action, string extraKey = null, Object targerId = null)
        {
            suber.isEventSuber = true;
            targerId           = targerId ?? NeeGame.Instance;
            extraKey           = string.IsNullOrEmpty(extraKey) ? typeof(T).Name : $"{typeof(T).Name}_{extraKey}";
            //if (targerId <= 0)
            //    targerId = 0;

            if (eveDictActionGeneric.TryGetValue((extraKey, targerId.GetInstanceID()), out var list))
            {
                if (!list.ContainsKey(suber.GetSuberId()))
                    list.Add(suber.GetSuberId(), action);
            }
            else
            {
                // var newList = new Dictionary<IEventSuber, Delegate>();
                var newList = DictionaryPool<int, Delegate>.New();
                newList.Add(suber.GetSuberId(), action);
                eveDictActionGeneric.Add((extraKey, targerId.GetInstanceID()), newList);
            }

            if (keysDict.TryGetValue(suber.GetSuberId(), out var keyList))
            {
                keyList.Add((extraKey, targerId.GetInstanceID()));
            }
            else
            {
                // var newList = new HashSet<(string, int)>();
                var newList = HashSetPool<(string, int)>.New();
                newList.Add((extraKey, targerId.GetInstanceID()));
                keysDict.Add(suber.GetSuberId(), newList);
            }
        }

        /// <summary>
        /// ActionT以及Action T,Task> 每个suber只能suber一个！
        /// </summary>
        public void Sub<T>(IEventSuber suber, Action<T> action, string extraKey = null, Object targerId = null)
        {
            if (!string.IsNullOrEmpty(extraKey))
                extraKey = extraKey.Trim();
            targerId = targerId ?? NeeGame.Instance;
            _SubCore<T>(suber, action, extraKey, targerId);
        }

        /// <summary>
        /// ActionT以及Action T,Task> 每个suber只能suber一个！
        /// </summary>
        public void Sub<T>(IEventSuber suber, Func<T, Task> action, string extraKey = null, Object targerId = null)
        {
            if (!string.IsNullOrEmpty(extraKey))
                extraKey = extraKey.Trim();
            targerId = targerId ?? NeeGame.Instance;
            _SubCore<T>(suber, action, extraKey, targerId);
        }

        /// <summary>
        /// ActionT以及Action T,Task> 每个suber只能suber一个！
        /// </summary>
        public void UnSub<T>(IEventSuber suber, Delegate action, string extraKey = null, Object targerId = null)
        {
            if (!string.IsNullOrEmpty(extraKey))
                extraKey = extraKey.Trim();
            targerId = targerId ?? NeeGame.Instance;
            extraKey = string.IsNullOrEmpty(extraKey) ? typeof(T).Name : $"{typeof(T).Name}_{extraKey}";
            //if (targerId <= 0)
            //    targerId = 0;
            if (eveDictActionGeneric.TryGetValue((extraKey, targerId.GetInstanceID()), out var list))
            {
                list.Remove(suber.GetSuberId());
                if (list.Count == 0)
                {
                    DictionaryPool<int, Delegate>.Free(eveDictActionGeneric[(extraKey, targerId.GetInstanceID())]);
                    eveDictActionGeneric.Remove((extraKey, targerId.GetInstanceID()));
                }
            }
            else
            {
                Debug.Log($"不存在delegate： {extraKey}");
            }

            if (keysDict.TryGetValue(suber.GetSuberId(), out var keyList))
            {
                keyList.Remove((extraKey, targerId.GetInstanceID()));
                if (list.Count == 0)
                {
                    HashSetPool<(string, int)>.Free(keysDict[(suber.GetSuberId())]);
                    keysDict.Remove(suber.GetSuberId());
                }
            }
        }
        
        
        /// <summary>
        ///     最好自身释放
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="extraKey"></param>
        /// <param name="targerId"></param>
        //public void Clear<T>(string extraKey = null, long targerId = 0)
        //{
        //    extraKey = $"{typeof(T).Name}_{extraKey}" ?? typeof(T).Name;
        //    if (targerId <= 0)
        //        targerId = 0;
        //    if (eveDict.TryGetValue((extraKey, targerId), out var list))
        //    {
        //        list.Clear();
        //        eveDict.Remove((extraKey, targerId));
        //    }
        //    else
        //    {
        //        Debug.LogRed($"不存在delegate： {extraKey}");
        //    }
        //}
        
        /// <summary>
        /// ActionT以及Action T,Task> 每个suber只能suber一个！
        /// </summary>
        public Task FireAsync<T>(T obj, string extraKey = null, Object targerId = null)
        {
            if (!string.IsNullOrEmpty(extraKey))
                extraKey = extraKey.Trim();
            if (needAysncInit && isAsyncInited == false)
            {
                Debug.LogError("尚未初始化完成");
                return default;
            }

            targerId = targerId ?? NeeGame.Instance;
            extraKey = string.IsNullOrEmpty(extraKey) ? typeof(T).Name : $"{typeof(T).Name}_{extraKey}";
            //if (targerId <= 0)
            //    targerId = 0;
            var taskList = new List<Task>();

            if (eveDictActionGeneric.TryGetValue((extraKey, targerId.GetInstanceID()), out var list))
            {
                foreach (var i in list.Values)
                        //(i as Action<T>)?.Invoke(obj);
                    if (i is Action<T> act)
                        act?.Invoke(obj);
                    else if (i is Func<T, Task> func)
                        if (func != null)
                            taskList.Add(func.Invoke(obj));
                //func?.Invoke(obj);
                return Task.WhenAll(taskList);
            }

            //Debug.LogRed($"不存在delegate： {extraKey}");
            return default;
        }
        
        /// <summary>
        /// ActionT以及Action T,Task> 每个suber只能suber一个！
        /// </summary>
        public void Fire<T>(T obj, string extraKey = null, Object targerId = null)
        {
            if (!string.IsNullOrEmpty(extraKey))
                extraKey = extraKey.Trim();
            if (needAysncInit && isAsyncInited == false)
            {
                Debug.LogError("尚未初始化完成");
                return;
            }

            targerId = targerId ?? NeeGame.Instance;
            extraKey = string.IsNullOrEmpty(extraKey) ? typeof(T).Name : $"{typeof(T).Name}_{extraKey}";
            //if (targerId <= 0)
            //    targerId = 0;
            if (eveDictActionGeneric.TryGetValue((extraKey, targerId.GetInstanceID()), out var list))
            {
                foreach (var i in list.Values)
                        //(i as Action<T>)?.Invoke(obj);
                    if (i is Action<T> act)
                        act?.Invoke(obj);
                    else if (i is Func<T, Task> func) func?.Invoke(obj);
            }
        }
    }
}