using System.Collections.Generic;
using System;
using System.IO;
using System.Linq;
using System.Threading;
using System.Reflection;
using System.Reflection.Emit;
using bc.MiniGameBase;
using CasualGame.lib_ChuagnYi.NeeG.Pool;
using UnityEngine;
using UnityEditor;
using XLua;

namespace CasualGame.lib_ChuagnYi
{
    [CustomEditor(typeof(LuaData), true)]
    [CanEditMultipleObjects]
    public class LuaDataEditor : Editor
    {
        private LuaMono tarMono;

        public void TarMonoOnInspectorGUI()
        {
            int selected = 0;

            EditorGUI.BeginChangeCheck();
            string[] options = tarMono.LuaCompsDropDownFunc();
            for (int i = 0; i < options.Length; i++)
            {
                if (options[i] == tarMono.luaName)
                {
                    selected = i;
                }
            }

            selected        = EditorGUILayout.Popup("Label", selected, options);
            tarMono.luaName = options[selected];
            if (EditorGUI.EndChangeCheck())
            {
                tarMono.SetLuaData();
            }
        }

        public class SerializedInfo
        {
            public string ValueName;
            public Type   ValueType;
            public string ValueExplain;
        }

        static Dictionary<Type, Type> objWrapEditorDict = new Dictionary<Type, Type>();

        static ModuleBuilder editorModule;

        //
        static LuaDataEditor()
        {
            AppDomain    myDomain  = Thread.GetDomain();
            AssemblyName myAsmName = new AssemblyName();
            myAsmName.Name = "LuaDataEditor";
            AssemblyBuilder myAsmBuilder = myDomain.DefineDynamicAssembly(myAsmName, AssemblyBuilderAccess.RunAndSave);
            editorModule = myAsmBuilder.DefineDynamicModule("LuaDataEEditorModule", "LuaDataEditor.dll");
        }

        //
        static Type GetWrapType(Type objType)
        {
            if (objWrapEditorDict.ContainsKey(objType))
            {
                return objWrapEditorDict[objType];
            }

            TypeBuilder wrapTypeBld = editorModule.DefineType("wrap" + objType.FullName, TypeAttributes.Public,
                    typeof(ScriptableObject));
            FieldBuilder objField = wrapTypeBld.DefineField("obj", objType, FieldAttributes.Public);
            Type         wrapType = wrapTypeBld.CreateType();
            objWrapEditorDict.Add(objType, wrapType);
            return wrapType;
        }

        public TextAsset LuaScript
        {
            get
            {
                return AssetDatabase.LoadAssetAtPath(m_LuaScriptPath.stringValue,
                        typeof(TextAsset)) as TextAsset;
            }
            set
            {
                string path = AssetDatabase.GetAssetPath(value);
                if ((path.EndsWith(".txt") || value == null) && m_LuaScriptPath.stringValue != path)
                {
                    m_LuaScriptPath.stringValue = path;
                    serializedObject.ApplyModifiedProperties();
                    lastWriteTime                 = 0;
                    infoList                      = new List<SerializedInfo>();
                    _mLuaData.SerializedObjValues = new List<LuaData.SerializedObjValue>();
                    _mLuaData.SerializedValues    = new List<LuaData.SerializedValue>();
                    serializedObject.Update();
                }
            }
        }

        private SerializedProperty   m_LuaScriptPath;
        private LuaData              _mLuaData;
        private List<SerializedInfo> infoList;
        private long                 lastWriteTime = 0;

        protected void OnEnable()
        {
            m_LuaScriptPath = serializedObject.FindProperty("LuaScriptPath");
            _mLuaData       = target as LuaData;
            lastWriteTime   = 0;
            infoList        = new List<SerializedInfo>();
        }

        //如果Lua文件改变了，则需要重新初始化一遍需要序列化的信息。同时保留已经序列化的数据。
        private void ReloadLua()
        {
            if (File.Exists(m_LuaScriptPath.stringValue))
            {
                long curTime = File.GetLastWriteTime(m_LuaScriptPath.stringValue).Ticks;
                if (curTime != lastWriteTime)
                {
                    lastWriteTime = curTime;
                    infoList      = GetInfoList();
                    if (_mLuaData.SerializedValues == null)
                        _mLuaData.SerializedValues = new List<LuaData.SerializedValue>();
                    if (_mLuaData.SerializedObjValues == null)
                        _mLuaData.SerializedObjValues = new List<LuaData.SerializedObjValue>();
                    var preObjValues = _mLuaData.SerializedObjValues;
                    var preValues    = _mLuaData.SerializedValues;
                    _mLuaData.SerializedObjValues = new List<LuaData.SerializedObjValue>();
                    _mLuaData.SerializedValues    = new List<LuaData.SerializedValue>();
                    foreach (var info in infoList)
                    {
                        if (info.ValueType.IsSubclassOf(typeof(UnityEngine.Object)))
                        {
                            _mLuaData.SerializedObjValues.Add(
                                    new LuaData.SerializedObjValue()
                                    {
                                            key   = info.ValueName,
                                            value = GetObjValueInLuaBehavior(info, preObjValues)
                                    });
                        }
                        else
                        {
                            _mLuaData.SerializedValues.Add(
                                    new LuaData.SerializedValue()
                                    {
                                            key     = info.ValueName,
                                            jsonStr = GetValueInLuaBehavior(info, preValues)
                                    });
                        }
                    }
                }
            }
        }

        private UnityEngine.Object GetObjValueInLuaBehavior(SerializedInfo                   info,
                                                            List<LuaData.SerializedObjValue> values)
        {
            foreach (var value in values)
            {
                if (value.key == info.ValueName)
                {
                    return value.value;
                }
            }

            return null;
        }

        private string GetValueInLuaBehavior(SerializedInfo info, List<LuaData.SerializedValue> values)
        {
            foreach (var value in values)
            {
                if (value.key == info.ValueName)
                {
                    var obj1 = LuaData.JsonToValue(value.jsonStr, info.ValueType);
                    return LuaData.ValueToJson(obj1);
                }
            }

            Type wrapType = typeof(ObjWrap<>).MakeGenericType(info.ValueType);
            if (info.ValueType == LuaData.StringType)
            {
                return string.Empty;
            }
            else
            {
                return JsonUtility.ToJson(target);
            }
        }

        private static Dictionary<string, LuaTable> typeToDictStatic;
        private static LuaEnv                       luaEnvStatic;

        //运行时和编辑时获取的方式不一样，运行时直接从实例中取即可，编辑时需要起一个虚拟机来加载序列化信息。
        private List<SerializedInfo> GetInfoList()
        {
            List<SerializedInfo> infoList = new List<SerializedInfo>();
            LuaEnv               luaEnv   = null;
            LuaTable             luaClass = null;
            LuaTable             defineTable;
            if (!Application.isPlaying)
            {
                typeToDictStatic ??= new Dictionary<string, LuaTable>();
                if (luaEnvStatic == null)
                {
                    luaEnvStatic = new LuaEnv();
                    luaEnvStatic.AddLoader(LuaDataLoader.CustomLoader);
                    luaEnvStatic.DoString("ExecuteInEditorScript = true");
                    luaEnvStatic.DoString($"return require \"{"bc_Class"}\"");
                }

                luaEnv = luaEnvStatic;
                if (!typeToDictStatic.ContainsKey(LuaScript.name))
                {
                    var rets = luaEnv.DoString($"return require \"{LuaScript.name}\"");
                    luaClass = (LuaTable) rets[0];
                    typeToDictStatic.Add(LuaScript.name, luaClass);
                }
                else
                {
                    luaClass = typeToDictStatic[LuaScript.name];
                }
            }
            else
            {
                typeToDictStatic ??= new Dictionary<string, LuaTable>();
                if (!typeToDictStatic.ContainsKey(LuaScript.name))
                {
                    luaEnv = XLuaManager.Instance.GetLuaEnv();
                    var rets = luaEnv.DoString($"return require \"{LuaScript.name}\"");
                    luaClass = (LuaTable) rets[0];
                }
                else
                {
                    luaClass = typeToDictStatic[LuaScript.name];
                }
            }

            luaClass.Get("_DefineList", out defineTable);
            if (defineTable != null)
            {
                defineTable.ForEach<int, LuaTable>((index, infoTable) =>
                {
                    SerializedInfo info = new SerializedInfo();
                    infoTable.Get("name",    out info.ValueName);
                    infoTable.Get("type",    out info.ValueType);
                    infoTable.Get("explain", out info.ValueExplain);
                    if (Application.isPlaying)
                    {
                        bool contains = false;
                        foreach (var ele in _mLuaData.SerializedValues)
                        {
                            contains = ele.key == info.ValueName;
                            if (contains) break;
                        }

                        if (!contains)
                            foreach (var ele in _mLuaData.SerializedObjValues)
                            {
                                contains = ele.key == info.ValueName;
                                if (contains) break;
                            }

                        if (contains)
                        {
                            infoList.Add(info);
                        }
                    }
                    else
                    {
                        infoList.Add(info);
                    }
                });
                // if (!Application.isPlaying)
                // {
                //     luaEnv.Dispose();
                // }
            }
            else
            {
                Debug.LogError($"not find _DefineList");
            }

            var dict = DictionaryPool<string, SerializedInfo>.New();
            foreach (var ele in infoList)
            {
                if (!dict.ContainsKey(ele.ValueName))
                {
                    dict.Add(ele.ValueName, ele);
                }
            }

            infoList = dict.Select(x => x.Value).ToList();
            DictionaryPool<string, SerializedInfo>.Free(dict);

            return infoList;
        }

        private string filter = "";

        public override void OnInspectorGUI()
        {
            _mLuaData.InspectorAutoUpdate();
            tarMono ??= _mLuaData.GetComponent<LuaMono>();
            if (tarMono)
                TarMonoOnInspectorGUI();

            base.OnInspectorGUI();
            serializedObject.Update();
            //绘制Lua路径
            // LuaScript = EditorGUILayout.ObjectField("Lua Script", LuaScript, typeof(TextAsset),
            //         true) as TextAsset;
            //绘制所有需要注入的对象
            GUILayout.BeginHorizontal();
            if (GUILayout.Button("刷新面板", GUILayout.Width(100)))
            {
                luaEnvStatic = null;
                typeToDictStatic?.Clear();
                Debug.Log(tarMono.luaName);
                tarMono.SetLuaData();
            }

            ReloadLua();

            GUILayout.Label("过滤器:", GUILayout.Width(50));
            filter = GUILayout.TextField(filter);
            GUILayout.EndHorizontal();
            GUILayout.Space(10);

            {
                _mLuaData.SerializedValuesRemoveSameKey();
                foreach (var info in infoList)
                {
                    if (info.ValueType.IsSubclassOf(typeof(UnityEngine.Object)))
                    {
                        DrawObjValueView(info);
                    }
                    else
                    {
                        DrawValueView(info);
                    }
                }
            }

            serializedObject.ApplyModifiedProperties();
        }

        //UnityEngine.Object都一个样，所以直接绘制即可
        private void DrawObjValueView(SerializedInfo info)
        {
            for (int i = 0; i < _mLuaData.SerializedObjValues.Count; i++)
            {
                if (info.ValueName == _mLuaData.SerializedObjValues[i].key)
                {
                    bool filterPass = string.IsNullOrEmpty(filter) ||
                                      _mLuaData.SerializedObjValues[i].key.ToLower().Contains(filter.ToLower());
                    if (!filterPass) continue;
                    _mLuaData.SerializedObjValues[i].value =
                            EditorGUILayout.ObjectField(
                                    _mLuaData.SerializedObjValues[i].key,
                                    _mLuaData.SerializedObjValues[i].value,
                                    info.ValueType,
                                    true
                            );
                }
            }
        }

        //感觉分类型去绘制有点麻烦，所以用ScriptableObject包装一下，通过SerializedObject让unity自己绘制。
        private void DrawValueView(SerializedInfo info)
        {
            for (int i = 0; i < _mLuaData.SerializedValues.Count; i++)
            {
                if (info.ValueName == _mLuaData.SerializedValues[i].key)
                {
                    bool filterPass = string.IsNullOrEmpty(filter) ||
                                      _mLuaData.SerializedValues[i].key.ToLower().Contains(filter.ToLower());
                    if (!filterPass) continue;
                    EditorGUI.BeginChangeCheck();
                    var editorType = GetWrapType(info.ValueType);
                    var objField   = editorType.GetField("obj");
                    var target     = CreateInstance(editorType);
                    var value      = LuaData.JsonToValue(_mLuaData.SerializedValues[i].jsonStr, info.ValueType);
                    objField.SetValue(target, value);
                    var so = new SerializedObject(target);
                    var content = string.IsNullOrEmpty(info.ValueExplain)
                            ? $"{info.ValueName}    ({info.ValueType})"
                            : $"{info.ValueName}  :  {info.ValueExplain}    ({info.ValueType})";
                    EditorGUILayout.PropertyField(so.FindProperty("obj"), new GUIContent(content));
                    so.ApplyModifiedProperties();
                    _mLuaData.SerializedValues[i].jsonStr = LuaData.ValueToJson(objField.GetValue(target));
                    var change = EditorGUI.EndChangeCheck();
                    if (change)
                    {
                        if (Application.isPlaying)
                        {
                            _mLuaData.SyncToLua(info.ValueName);
                            if (_mLuaData.callEventOnInspectorChange && _mLuaData.luaMono &&
                                _mLuaData.infoDict != null)
                            {
                                _mLuaData.luaMono.luaBehaviour.CallLuaFunction("CallOnLuaDataChange");
                            }
                        }

                        EditorUtility.SetDirty(_mLuaData);
                    }
                }
            }
        }
    }
}