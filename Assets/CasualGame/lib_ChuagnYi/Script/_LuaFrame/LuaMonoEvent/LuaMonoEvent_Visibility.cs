using System;
using XLua;

namespace CasualGame.lib_ChuagnYi.MonoFunc
{
    public class LuaMonoEvent_Visibility : LuaMonoEvent
    {
        public Action<object> onBecameInvisible;
        public Action<object> onBecameVisible;
        public LuaTable       luaComp;

        public override void Bind(LuaMono luaMono)
        {
            luaComp           = luaMono.luaComp;
            onBecameInvisible = luaMono.luaComp.GetInPath<Action<object>>("OnBecameInvisible");
            onBecameVisible   = luaMono.luaComp.GetInPath<Action<object>>("OnBecameVisible");
        }

        private void OnBecameInvisible()
        {
            onBecameInvisible?.Invoke(luaComp);
        }

        private void OnBecameVisible()
        {
            onBecameVisible?.Invoke(luaComp);
        }
    }
}