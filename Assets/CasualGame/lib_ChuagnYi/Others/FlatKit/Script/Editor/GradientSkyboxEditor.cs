using UnityEngine;
using UnityEditor;

namespace CasualGame.lib_ChuagnYi.FlaKit
{
    public class GradientSkyboxEditor : UnityEditor.MaterialEditor
    {
        public override void OnInspectorGUI()
        {
            serializedObject.Update();

            var theShader = serializedObject.FindProperty("m_Shader");

            if (isVisible && !theShader.hasMultipleDifferentValues && theShader.objectReferenceValue != null)
            {
                EditorGUI.BeginChangeCheck();

                base.OnInspectorGUI();

                if (EditorGUI.EndChangeCheck())
                {
                    var dirPitch = GetMaterialProperty(targets, "_DirectionPitch");
                    var dirYaw   = GetMaterialProperty(targets, "_DirectionYaw");

                    var dirPitchRad = dirPitch.floatValue * Mathf.Deg2Rad;
                    var dirYawRad   = dirYaw.floatValue   * Mathf.Deg2Rad;

                    var direction = new Vector4(Mathf.Sin(dirPitchRad) * Mathf.<PERSON>(dirYawRad), <PERSON><PERSON><PERSON>(dirPitchRad),
                            <PERSON><PERSON>.Sin(dirPitchRad)                     * Mathf.Cos(dirYawRad), 0.0f);
                    GetMaterialProperty(targets, "_Direction").vectorValue = direction;

                    PropertiesChanged();
                }
            }
        }
    }
}