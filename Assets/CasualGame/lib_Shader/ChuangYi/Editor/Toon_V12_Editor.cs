#if UNITY_EDITOR

using System;
using System.Collections.Generic;
using System.Reflection;
using System.Text.RegularExpressions;
using UnityEngine;
using UnityEditor;

namespace CasualGame.lib_Shader.ChuangYi
{
    public static class DeepCloneEx
    {
        private static readonly MethodInfo CloneMethod =
                typeof(object).GetMethod("MemberwiseClone", BindingFlags.NonPublic | BindingFlags.Instance);

        public static bool IsPrimitive(this Type type)
        {
            if (type == typeof(string)) return true;
            return type.IsValueType & type.IsPrimitive;
        }

        public static object Copy(this object originalObject)
        {
            return InternalCopy(originalObject, new Dictionary<object, object>(new ReferenceEqualityComparer()));
        }

        private static object InternalCopy(object originalObject, IDictionary<object, object> visited)
        {
            if (originalObject == null) return null;
            var typeToReflect = originalObject.GetType();
            if (IsPrimitive(typeToReflect)) return originalObject;
            if (visited.ContainsKey(originalObject)) return visited[originalObject];
            if (typeof(Delegate).IsAssignableFrom(typeToReflect)) return null;
            var cloneObject = CloneMethod.Invoke(originalObject, null);
            if (typeToReflect.IsArray)
            {
                var arrayType = typeToReflect.GetElementType();
                if (IsPrimitive(arrayType) == false)
                {
                    var clonedArray = (Array) cloneObject;
                    clonedArray.ForEach((array, indices) =>
                            array.SetValue(InternalCopy(clonedArray.GetValue(indices), visited), indices));
                }
            }

            visited.Add(originalObject, cloneObject);
            CopyFields(originalObject, visited, cloneObject, typeToReflect);
            RecursiveCopyBaseTypePrivateFields(originalObject, visited, cloneObject, typeToReflect);
            return cloneObject;
        }

        private static void RecursiveCopyBaseTypePrivateFields(object                      originalObject,
                                                               IDictionary<object, object> visited, object cloneObject,
                                                               Type                        typeToReflect)
        {
            if (typeToReflect.BaseType != null)
            {
                RecursiveCopyBaseTypePrivateFields(originalObject, visited, cloneObject, typeToReflect.BaseType);
                CopyFields(originalObject,                              visited, cloneObject, typeToReflect.BaseType,
                        BindingFlags.Instance | BindingFlags.NonPublic, info => info.IsPrivate);
            }
        }

        private static void CopyFields(object originalObject, IDictionary<object, object> visited, object cloneObject,
                                       Type typeToReflect,
                                       BindingFlags bindingFlags = BindingFlags.Instance | BindingFlags.NonPublic |
                                                                   BindingFlags.Public | BindingFlags.FlattenHierarchy,
                                       Func<FieldInfo, bool> filter = null)
        {
            foreach (var fieldInfo in typeToReflect.GetFields(bindingFlags))
            {
                if (filter != null && filter(fieldInfo) == false) continue;
                if (IsPrimitive(fieldInfo.FieldType)) continue;
                var originalFieldValue = fieldInfo.GetValue(originalObject);
                var clonedFieldValue   = InternalCopy(originalFieldValue, visited);
                fieldInfo.SetValue(cloneObject, clonedFieldValue);
            }
        }

        public static T Copy<T>(this T original)
        {
            return (T) Copy((object) original);
        }
    }

    public class ReferenceEqualityComparer : EqualityComparer<object>
    {
        public override bool Equals(object x, object y)
        {
            return ReferenceEquals(x, y);
        }

        public override int GetHashCode(object obj)
        {
            if (obj == null) return 0;
            return obj.GetHashCode();
        }
    }

    public static class ArrayExtensions
    {
        public static void ForEach(this Array array, Action<Array, int[]> action)
        {
            if (array.LongLength == 0) return;
            var walker = new ArrayTraverse(array);
            do
            {
                action(array, walker.Position);
            } while (walker.Step());
        }
    }

    internal class ArrayTraverse
    {
        private readonly int[] maxLengths;
        public           int[] Position;

        public ArrayTraverse(Array array)
        {
            maxLengths = new int[array.Rank];
            for (var i = 0; i < array.Rank; ++i) maxLengths[i] = array.GetLength(i) - 1;
            Position = new int[array.Rank];
        }

        public bool Step()
        {
            for (var i = 0; i < Position.Length; ++i)
                if (Position[i] < maxLengths[i])
                {
                    Position[i]++;
                    for (var j = 0; j < i; j++) Position[j] = 0;
                    return true;
                }

            return false;
        }
    }

    public class Toon_V12_Editor : UnityEditor.ShaderGUI
    {
        private Material           _target;
        private MaterialEditor     _editor;
        private MaterialProperty[] _properties;

        private static readonly Dictionary<string, bool> _foldoutStates = new Dictionary<string, bool>();
        private static readonly Color                    hashColor = new Color(0.85023f, 0.85034f, 0.85045f, 0.85056f);
        private static readonly GUIContent               staticLabel = new GUIContent();
        private static readonly int                      ColorPropertyName = Shader.PropertyToID("_Color");

        void DrawStandard(MaterialProperty property)
        {
            string displayName = property.displayName;
            // Remove everything in square brackets.
            displayName = Regex.Replace(displayName, @" ?\[.*?\]", string.Empty);
            _editor.ShaderProperty(property, displayName);
        }


        MaterialProperty FindProperty(string name)
        {
            return FindProperty(name, _properties);
        }

        bool HasProperty(string name)
        {
            return _target != null && _target.HasProperty(name);
        }

        private bool foldTest;

        Texture2D MakeTex(int width, int height, Color col)
        {
            var pix = new Color[width * height];

            for (var i = 0; i < pix.Length; i++)
            {
                pix[i] = col;
            }

            var result = new Texture2D(width, height);
            result.SetPixels(pix);
            result.Apply();
            return result;
        }

        private static GUIStyle s2;

        public override void OnGUI(MaterialEditor materialEditor, MaterialProperty[] properties)
        {
            _editor     = materialEditor;
            _properties = properties;
            _target     = materialEditor.target as Material;
            Debug.Assert(_target != null, "_target != null");

            int  originalIntentLevel   = EditorGUI.indentLevel;
            int  foldoutRemainingItems = 0;
            bool latestFoldoutState    = false;

            var skipPropertyFoldout = false;
            var needCloseOrExpand   = false;
            var closeExpandValue    = false;


            var preColor  = EditorStyles.foldoutHeader.normal.background;
            var preColor2 = EditorStyles.foldoutHeader.normal.textColor;
            if (s2 == null)
            {
                s2 = EditorStyles.foldoutHeader;
            }

            s2.normal.background = MakeTex(600, 1, new Color(0.26f, 0.34f, 0.27f, 0.78f));
            s2.normal.textColor  = new Color(0.91f, 0.66f, 0.78f, 0.69f);


            foreach (MaterialProperty property in properties)
            {
                bool   skipProperty = false;
                string displayName  = property.displayName;

                if (displayName == "展开/关闭所有")
                {
                    var pre = _target.GetInt(property.name) == 1;
                    var value = EditorGUILayout.BeginFoldoutHeaderGroup(_target.GetInt(property.name) == 1,
                            "<" + (_target.GetInt(property.name) == 0 ? "展开所有" : "折叠所有") + ">", s2);
                    _target.SetInt(property.name, value ? 1 : 0);
                    skipProperty = true;
                    if (pre != value)
                    {
                        needCloseOrExpand = true;
                        closeExpandValue  = value;
                    }
                }

                if (displayName.StartsWith("Fold_"))
                {
                    EditorGUILayout.EndFoldoutHeaderGroup();
                    var value = EditorGUILayout.BeginFoldoutHeaderGroup(_target.GetInt(property.name) == 1,
                            "<" + displayName.Substring(5, displayName.Length - 5) + ">", s2);
                    if (needCloseOrExpand)
                    {
                        value = closeExpandValue;
                    }

                    _target.SetInt(property.name, value ? 1 : 0);
                    skipProperty        = true;
                    skipPropertyFoldout = !value;


                    // skipProperty          |=  !_target.IsKeywordEnabled("SF_Shadow");
                    // EditorGUI.indentLevel += 1;
                }

                if (displayName.Contains("[SF_Shadow]"))
                {
                    skipProperty          |= !_target.IsKeywordEnabled("SF_Shadow");
                    EditorGUI.indentLevel += 1;
                }

                if (displayName.Contains("[SF_ColorDark]"))
                {
                    skipProperty          |= !_target.IsKeywordEnabled("SF_ColorDark");
                    EditorGUI.indentLevel += 1;
                }

                if (displayName.Contains("[SF_MainTex]"))
                {
                    skipProperty          |= !_target.IsKeywordEnabled("SF_MainTex");
                    EditorGUI.indentLevel += 1;
                }

                if (displayName.Contains("[SF_CustomLightDir]"))
                {
                    skipProperty          |= !_target.IsKeywordEnabled("SF_CustomLightDir");
                    EditorGUI.indentLevel += 1;
                }

                if (displayName.Contains("[SF_RampTex]"))
                {
                    skipProperty          |= !_target.IsKeywordEnabled("SF_RampTex");
                    EditorGUI.indentLevel += 1;
                }

                if (displayName.Contains("[SF_CubeReflect]"))
                {
                    skipProperty          |= !_target.IsKeywordEnabled("SF_CubeReflect");
                    EditorGUI.indentLevel += 1;
                }

                if (displayName.Contains("[SF_MatCap]"))
                {
                    skipProperty          |= !_target.IsKeywordEnabled("SF_MatCap");
                    EditorGUI.indentLevel += 1;
                }

                if (displayName.Contains("[SF_Weight]"))
                {
                    skipProperty          |= !_target.IsKeywordEnabled("SF_Weight");
                    EditorGUI.indentLevel += 1;
                }

                if (displayName.Contains("[SF_CubeReflectRotation]"))
                {
                    skipProperty          |= !_target.IsKeywordEnabled("SF_CubeReflectRotation");
                    EditorGUI.indentLevel += 1;
                }

                if (displayName.Contains("[SF_Specular]"))
                {
                    skipProperty |= !_target.IsKeywordEnabled("SF_Specular");
                    if (!displayName.Contains("高光2"))
                    {
                        EditorGUI.indentLevel += 1;
                    }
                }

                if (displayName.Contains("[SF_Rim]"))
                {
                    skipProperty          |= !_target.IsKeywordEnabled("SF_Rim");
                    EditorGUI.indentLevel += 1;
                }

                if (displayName.Contains("[SF_Specular2]"))
                {
                    skipProperty          |= !_target.IsKeywordEnabled("SF_Specular2");
                    EditorGUI.indentLevel += 1;
                }

                if (displayName.Contains("[SF_RimUp]"))
                {
                    skipProperty          |= !_target.IsKeywordEnabled("SF_RimUp");
                    EditorGUI.indentLevel += 1;
                }
                
                if (displayName.Contains("[SF_ShadowBlockOutline]"))
                {
                    skipProperty          |= !_target.IsKeywordEnabled("SF_ShadowBlockOutline");
                    EditorGUI.indentLevel += 1;
                }
                
                if (displayName.Contains("[SF_DirFog]"))
                {
                    skipProperty          |= !_target.IsKeywordEnabled("SF_DirFog");
                    EditorGUI.indentLevel += 1;
                }

                if (displayName.Contains("FOLDOUT"))
                {
                    string foldoutName      = displayName.Split('(', ')')[1];
                    string foldoutItemCount = displayName.Split('{', '}')[1];
                    foldoutRemainingItems = Convert.ToInt32(foldoutItemCount);
                    if (!_foldoutStates.ContainsKey(property.name))
                    {
                        _foldoutStates.Add(property.name, false);
                    }

                    EditorGUILayout.Space();
                    _foldoutStates[property.name] =
                            EditorGUILayout.Foldout(_foldoutStates[property.name], foldoutName);
                    latestFoldoutState = _foldoutStates[property.name];
                }

                if (foldoutRemainingItems > 0)
                {
                    skipProperty          |= skipProperty || !latestFoldoutState;
                    EditorGUI.indentLevel += 1;
                    --foldoutRemainingItems;
                }

                if (!skipProperty                                          &&
                    property.type       == MaterialProperty.PropType.Color &&
                    property.colorValue == hashColor)
                {
                    property.colorValue = _target.GetColor(ColorPropertyName);
                }

                bool hideInInspector = (property.flags & MaterialProperty.PropFlags.HideInInspector) != 0;
                if (!hideInInspector && !skipProperty && !skipPropertyFoldout)
                {
                    DrawStandard(property);
                }

                EditorGUI.indentLevel = originalIntentLevel;
            }

            EditorGUILayout.EndFoldoutHeaderGroup();
            if (EditorGUI.EndChangeCheck())
            {
                var dirPitch = _target.GetFloat("_CustomLightDirPitch");
                var dirYaw   = _target.GetFloat("_CustomLightDirYaw");

                var dirPitchRad = dirPitch * Mathf.Deg2Rad;
                var dirYawRad   = dirYaw   * Mathf.Deg2Rad;

                var direction = new Vector4(Mathf.Sin(dirPitchRad) * Mathf.Sin(dirYawRad), Mathf.Cos(dirPitchRad),
                        Mathf.Sin(dirPitchRad)                     * Mathf.Cos(dirYawRad), 0.0f);
                _target.SetVector("_CustomLightDir", direction);
                //
                var dirPitch2 = _target.GetFloat("_RimUpDirPitch");
                var dirYaw2   = _target.GetFloat("_RimUpDirYaw");
                var _RimUpDir = _target.GetVector("_RimUpDir");

                var dirPitchRad2 = dirPitch2 * Mathf.Deg2Rad;
                var dirYawRad2   = dirYaw2   * Mathf.Deg2Rad;

                var direction2 = new Vector4(
                        Mathf.Sin(dirPitchRad2) * Mathf.Sin(dirYawRad2) * _RimUpDir.w,
                        Mathf.Cos(dirPitchRad2) * _RimUpDir.w,
                        Mathf.Sin(dirPitchRad2) * Mathf.Cos(dirYawRad2) * _RimUpDir.w,
                        _RimUpDir.w);
                _target.SetVector("_RimUpDir", direction2);
                //
                var dirPitch3  = _target.GetFloat("_Specular2Pitch");
                var dirYaw3    = _target.GetFloat("_Specular2Yaw");
                var _Specular2 = _target.GetVector("_Specular2Offset");

                var dirPitchRad3 = dirPitch3 * Mathf.Deg2Rad;
                var dirYawRad3   = dirYaw3   * Mathf.Deg2Rad;

                var direction3 = new Vector4(
                        Mathf.Sin(dirPitchRad3) * Mathf.Sin(dirYawRad3) * _Specular2.w,
                        Mathf.Cos(dirPitchRad3) * _Specular2.w,
                        Mathf.Sin(dirPitchRad3) * Mathf.Cos(dirYawRad3) * _Specular2.w,
                        _Specular2.w);
                _target.SetVector("_Specular2Offset", direction3);
                //
                // var dirPitch4  = _target.GetFloat("_CubeReflectPitch");
                // var dirYaw4    = _target.GetFloat("_CubeReflectYaw");
                // var dirPitchRad4 = dirPitch4 * Mathf.Deg2Rad;
                // var dirYawRad4   = dirYaw4   * Mathf.Deg2Rad;
                //
                // var direction4 = new Vector3(
                //         Mathf.Sin(dirPitchRad4) * Mathf.Sin(dirYawRad4),
                //         Mathf.Cos(dirPitchRad4) ,
                //         Mathf.Sin(dirPitchRad4) * Mathf.Cos(dirYawRad4));
                //
                // _target.SetVector("_CubeReflectDir", direction4);
                //
                // var dirPitch5    = _target.GetFloat("_MatCapPitch");
                // var dirYaw5      = _target.GetFloat("_MatCapYaw");
                // var dirPitchRad5 = dirPitch5 * Mathf.Deg2Rad;
                // var dirYawRad5   = dirYaw5   * Mathf.Deg2Rad;
                //
                // var direction5 = new Vector3(
                //         Mathf.Sin(dirPitchRad5) * Mathf.Sin(dirYawRad5),
                //         Mathf.Cos(dirPitchRad5) ,
                //         Mathf.Sin(dirPitchRad5) * Mathf.Cos(dirYawRad5));
                //
                // _target.SetVector("_MatCapDir", direction5);
            }

            EditorGUILayout.Separator();
            _editor.EnableInstancingField();

            EditorStyles.foldoutHeader.normal.background = preColor;
            EditorStyles.foldoutHeader.normal.textColor  = preColor2;
        }
    }
}
#endif