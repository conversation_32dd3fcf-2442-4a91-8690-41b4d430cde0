#if UNITY_EDITOR

using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using UnityEngine;
using UnityEditor;

namespace CasualGame.lib_Shader.ChuangYi
{
    public class Toon_V11_Editor : UnityEditor.ShaderGUI
    {
        private Material           _target;
        private MaterialEditor     _editor;
        private MaterialProperty[] _properties;

        private static readonly Dictionary<string, bool> _foldoutStates = new Dictionary<string, bool>();
        private static readonly Color                    hashColor = new Color(0.85023f, 0.85034f, 0.85045f, 0.85056f);
        private static readonly GUIContent               staticLabel = new GUIContent();
        private static readonly int                      ColorPropertyName = Shader.PropertyToID("_Color");

        void DrawStandard(MaterialProperty property)
        {
            string displayName = property.displayName;
            // Remove everything in square brackets.
            displayName = Regex.Replace(displayName, @" ?\[.*?\]", string.Empty);
            _editor.ShaderProperty(property, displayName);
        }


        MaterialProperty FindProperty(string name)
        {
            return FindProperty(name, _properties);
        }

        bool HasProperty(string name)
        {
            return _target != null && _target.HasProperty(name);
        }

        public override void OnGUI(MaterialEditor materialEditor, MaterialProperty[] properties)
        {
            _editor     = materialEditor;
            _properties = properties;
            _target     = materialEditor.target as Material;
            Debug.Assert(_target != null, "_target != null");

            int  originalIntentLevel   = EditorGUI.indentLevel;
            int  foldoutRemainingItems = 0;
            bool latestFoldoutState    = false;
            foreach (MaterialProperty property in properties)
            {
                bool   skipProperty = false;
                string displayName  = property.displayName;

                if (displayName.Contains("[SF_Shadow]"))
                {
                    skipProperty          =  !_target.IsKeywordEnabled("SF_Shadow");
                    EditorGUI.indentLevel += 1;
                }
                
                if (displayName.Contains("[SF_ColorDark]"))
                {
                    skipProperty          =  !_target.IsKeywordEnabled("SF_ColorDark");
                    EditorGUI.indentLevel += 1;
                }

                if (displayName.Contains("[SF_MainTex]"))
                {
                    skipProperty          =  !_target.IsKeywordEnabled("SF_MainTex");
                    EditorGUI.indentLevel += 1;
                }

                if (displayName.Contains("[SF_CustomLightDir]"))
                {
                    skipProperty          =  !_target.IsKeywordEnabled("SF_CustomLightDir");
                    EditorGUI.indentLevel += 1;
                }

                if (displayName.Contains("[SF_RampTex]"))
                {
                    skipProperty          =  !_target.IsKeywordEnabled("SF_RampTex");
                    EditorGUI.indentLevel += 1;
                }

                if (displayName.Contains("[SF_CubeReflect]"))
                {
                    skipProperty          =  !_target.IsKeywordEnabled("SF_CubeReflect");
                    EditorGUI.indentLevel += 1;
                }

                if (displayName.Contains("[SF_Specular]"))
                {
                    skipProperty = !_target.IsKeywordEnabled("SF_Specular");
                    if (!displayName.Contains("高光2"))
                    {
                        EditorGUI.indentLevel += 1;
                    }
                }

                if (displayName.Contains("[SF_Rim]"))
                {
                    skipProperty          =  !_target.IsKeywordEnabled("SF_Rim");
                    EditorGUI.indentLevel += 1;
                }

                if (displayName.Contains("[SF_Specular2]"))
                {
                    skipProperty          =  !_target.IsKeywordEnabled("SF_Specular2");
                    EditorGUI.indentLevel += 1;
                }

                if (displayName.Contains("[SF_RimUp]"))
                {
                    skipProperty          =  !_target.IsKeywordEnabled("SF_RimUp");
                    EditorGUI.indentLevel += 1;
                }

                if (displayName.Contains("FOLDOUT"))
                {
                    string foldoutName      = displayName.Split('(', ')')[1];
                    string foldoutItemCount = displayName.Split('{', '}')[1];
                    foldoutRemainingItems = Convert.ToInt32(foldoutItemCount);
                    if (!_foldoutStates.ContainsKey(property.name))
                    {
                        _foldoutStates.Add(property.name, false);
                    }

                    EditorGUILayout.Space();
                    _foldoutStates[property.name] =
                            EditorGUILayout.Foldout(_foldoutStates[property.name], foldoutName);
                    latestFoldoutState = _foldoutStates[property.name];
                }

                if (foldoutRemainingItems > 0)
                {
                    skipProperty          =  skipProperty || !latestFoldoutState;
                    EditorGUI.indentLevel += 1;
                    --foldoutRemainingItems;
                }

                if (!skipProperty                                          &&
                    property.type       == MaterialProperty.PropType.Color &&
                    property.colorValue == hashColor)
                {
                    property.colorValue = _target.GetColor(ColorPropertyName);
                }

                bool hideInInspector = (property.flags & MaterialProperty.PropFlags.HideInInspector) != 0;
                if (!hideInInspector && !skipProperty)
                {
                    DrawStandard(property);
                }

                EditorGUI.indentLevel = originalIntentLevel;
            }

            if (EditorGUI.EndChangeCheck())
            {
                var dirPitch = _target.GetFloat("_CustomLightDirPitch");
                var dirYaw   = _target.GetFloat("_CustomLightDirYaw");

                var dirPitchRad = dirPitch * Mathf.Deg2Rad;
                var dirYawRad   = dirYaw   * Mathf.Deg2Rad;

                var direction = new Vector4(Mathf.Sin(dirPitchRad) * Mathf.Sin(dirYawRad), Mathf.Cos(dirPitchRad),
                        Mathf.Sin(dirPitchRad)                     * Mathf.Cos(dirYawRad), 0.0f);
                _target.SetVector("_CustomLightDir", direction);

                var dirPitch2 = _target.GetFloat("_RimUpDirPitch");
                var dirYaw2   = _target.GetFloat("_RimUpDirYaw");
                var _RimUpDir = _target.GetVector("_RimUpDir");

                var dirPitchRad2 = dirPitch2 * Mathf.Deg2Rad;
                var dirYawRad2   = dirYaw2   * Mathf.Deg2Rad;

                var direction2 = new Vector4(
                        Mathf.Sin(dirPitchRad2) * Mathf.Sin(dirYawRad2) * _RimUpDir.w,
                        Mathf.Cos(dirPitchRad2) * _RimUpDir.w,
                        Mathf.Sin(dirPitchRad2) * Mathf.Cos(dirYawRad2) * _RimUpDir.w,
                        _RimUpDir.w);
                _target.SetVector("_RimUpDir", direction2);

                var dirPitch3  = _target.GetFloat("_Specular2Pitch");
                var dirYaw3    = _target.GetFloat("_Specular2Yaw");
                var _Specular2 = _target.GetVector("_Specular2Offset");

                var dirPitchRad3 = dirPitch3 * Mathf.Deg2Rad;
                var dirYawRad3   = dirYaw3   * Mathf.Deg2Rad;

                var direction3 = new Vector4(
                        Mathf.Sin(dirPitchRad3) * Mathf.Sin(dirYawRad3) * _Specular2.w,
                        Mathf.Cos(dirPitchRad3) * _Specular2.w,
                        Mathf.Sin(dirPitchRad3) * Mathf.Cos(dirYawRad3) * _Specular2.w,
                        _Specular2.w);
                _target.SetVector("_Specular2Offset", direction3);
            }

            EditorGUILayout.Separator();
            _editor.EnableInstancingField();
        }
    }
}
#endif