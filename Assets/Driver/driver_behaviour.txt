------------------------------------------------------
-- 兼容不支持C# LuaBehaviour 类/没有Utility.LoadAssetFileAsync接口版本，提供lua实现
-- 此类中可以使用 IOSystem 加载 assetbundle 接口，但其它模块实现中为兼容 luascript,manifest 等资源加载前逻辑
-- 不得使用 IOSystem 等通用加载接口
------------------------------------------------------

local package = package
local print = print
local os = os
local pairs = pairs
local tonumber = tonumber
local debug = debug
local xpcall = xpcall
local tostring = tostring
local table = table
local _G = _G
local type = type

local typeof = typeof
local GameObject = CS.UnityEngine.GameObject
local RectTransform = CS.UnityEngine.RectTransform
local IOSystem = CS.War.Script.IOSystem
local Debug = CS.UnityEngine.Debug
local Vector2 = CS.UnityEngine.Vector2
local Utility = CS.War.Script.Utility
local EventSystem = CS.UnityEngine.EventSystems.EventSystem
local LuaBehaviour = CS.War.Script.LuaBehaviour
local LuaManager = CS.War.Script.LuaManager
local AssetLoader = CS.War.Script.AssetLoader
local TextAsset = CS.UnityEngine.TextAsset
local AlgorithmUtils = CS.War.Common.AlgorithmUtils
local Config = CS.War.Base.Config
local LeanTween = CS.LeanTween
local I18NText = CS.I18NText
local Path = CS.System.IO.Path
local WebRequest = CS.System.Net.WebRequest
local HttpWebRequest = CS.System.Net.HttpWebRequest
local Stream = CS.System.IO.Stream
local LogDebug = CS.War.Base.LogDebug
local StringComparison = CS.System.StringComparison
local ServicePointManager = CS.System.Net.ServicePointManager
local RemoteCertificateValidationCallback = CS.System.Net.Security.RemoteCertificateValidationCallback
local StreamReader = CS.System.IO.StreamReader
local BinaryReader = CS.System.IO.BinaryReader
local MemoryStream = CS.System.IO.MemoryStream
local UTF8 = CS.System.Text.Encoding.UTF8
local Encoding = CS.System.Text.Encoding
local NameValueCollection = CS.System.Collections.Specialized.NameValueCollection
local DateTime = CS.System.DateTime
local AsyncCallback = CS.System.AsyncCallback
local WWWForm = CS.UnityEngine.WWWForm
local UnityWebRequest = CS.UnityEngine.Networking.UnityWebRequest
local GetHostEntry =  CS.System.Net.Dns.GetHostEntry
local Q1SDK = CS.Q1.Q1SDK
local RuntimePlatform = CS.UnityEngine.RuntimePlatform
local Application = CS.UnityEngine.Application
local AssetsUpdator = CS.AssetsUpdator
local Resources = CS.UnityEngine.Resources

local M = {}
M.__index = M

local ScriptTextBytes = {}
local ScriptModules = {}
g_ModuleMap = {}
local database = nil
local databaseInitSuccess = false
local ipResult = {}
local ErrorInfo = {}
local extInitCb = nil -- 扩展参数初始化回调
local driverUtil = nil
local game_config = nil

local const = 
{
    ENABLE_OSS_REPORT = true,
}
const.EnableOSSReport = function()
    if not const.ENABLE_OSS_REPORT then
        return false
    end

    if not game_config then
        game_config = driverUtil.GetGameConfig()
    end

    if not LogDebug.UnityWebReqPostBytesWithFiled then
        return false
    end
    return true
end

const.GetGameID = function ()
    --local appid = 2131
    local appid = 2162
    if driverUtil then
        if not game_config then
            game_config = driverUtil.GetGameConfig()
        end
        if game_config then
            local channel_tag = game_config.CHANNEL_TAG
            if channel_tag and channel_tag == "com.wmzz.q1" or channel_tag == "default" then
                appid = 2118
            end
        end
    end
    return appid
end

local url_mgr = 
{
    REPORT_OSS_ROOT_URL = {
        LAN = "http://api.dev.q1op.com/oss",        -- 内网
        DOMESTIC = "https://api-cn.cool-play.com/oss",   -- 国内
        EN = "https://api-ea.q1.com/oss",           -- 欧美
        SA = "https://api-sa.q1.com/oss",			-- 东南亚
    },

    REPORT_OSS_ROOT_URL_PATTERN = {
        LAN = "http://api.dev.q1op.com/oss",        -- 内网
        DOMESTIC = "https://$apiDomain.$mainDomain/oss",   -- 国内
        EN = "https://api-$eaDomain/oss",           -- 欧美
        SA = "https://api-$saDomain/oss",			-- 东南亚
    }
}
local util = {
    UTC0Seconds = function()
        local tss = DateTime.UtcNow - DateTime(1970, 1, 1, 0, 0, 0)
        return tss.TotalSeconds
    end
}

string.IsNullOrEmpty = function ( str )
    return (not str) or str == ""
end


function SetClassEmpty(obj)
    if type(obj) ~= 'table' then
        return
    end

    local k,v
    local memberType
    for k,v in pairs(obj) do
        memberType = type(v)
        if memberType ~= 'function' then
            obj[k] = nil
        end
    end
end

function M:Awake(UIRoot)
    print("driver_behaviour Awake:"..tostring(UIRoot))
    self.UIRoot = UIRoot
    local luaBehaviour = nil
    if database == nil then
        luaBehaviour = UIRoot:GetComponent(typeof(LuaBehaviour))
    end
    if luaBehaviour then
        self:SetDatabase(luaBehaviour.Database)
    end
end

local start_time = DateTime.Now.Ticks
function Warning(...)
    --if Utility.IsInEditor() then
    --    Debug.LogWarning("["..Time.time.."] "..msg)
    --end
    local tb = debug.traceback()
    local d = {...}
    local dd = {}
    local ind = 1
    for k,v in pairs(d) do
        d[k] =  v and tostring(v) or "nil"
        dd[ind] = d[k]
        ind= ind +1
    end
    Debug.LogWarning("[LUA_Drive] "..(tostring((DateTime.Now.Ticks-start_time)/10000))..table.concat( dd, "\t")..'\n'..tb)
end

function M:PrintIpInfo()
    if self.b_print_ipinfo then
        return
    end
    self.b_print_ipinfo = true

    local ip_urls = {
        -- "https://ipinfo.io/widget",
        -- "https://ip.cn/?ip=",
        -- "https://api.ip.sb/geoip",
        --"http://www.cip.cc/",
        -- "http://myip.ipip.net/",
        -- "https://ifconfig.me/all.json",
        "https://api-ea.q1.com/ip/api/ips/selfcountry?dataType=0"
    }
    for _,url in pairs(ip_urls) do
        local www = UnityWebRequest.Get(url)
        www.timeout = 3000
        local webrequest = www:SendWebRequest()
        LuaStartCoroutine(webrequest, function ()
            if www.isNetworkError or www.isHttpError then
                Warning("+ip_info\n",url,www.error,"\n-ip_info")
            else
                Warning("+ip_info\n",url,www.downloadHandler.text,"\n-ip_info")
                ipResult[url] = www.downloadHandler.text
            end
        end)
    end
end

function InitExtParams()
    -- 判断sdk取值是否成功
    if (not Q1SDK.Instance or not Q1SDK.Instance.GetExtParams) then
        -- 老包
        InitExtParamsByHttp()
    else
        if not _G['_DOMAIN_DATA'] then
            if AssetsUpdator.ExtParams then
                local jsonContain,jsonStr = AssetsUpdator.ExtParams:TryGetValue("jsonStr")
                local json = require "dkjson"
                if jsonContain then
                    local jsonData = json.decode(jsonStr) or {}
                    local extConfigStr = jsonData.extConfig
                    local extkey = jsonData.extConfig
                    if extConfigStr then
                        local urlCount = 0
                        local urlGroup = {}
                        local updateCount = 0
                        local updateGroup = {}
                        local extConfig
                        local domainJson = ""
                        if type(extConfigStr) == 'table' then
                            extConfig =  extConfigStr
                        else
                            extConfig =  json.decode(extConfigStr) or {}
                        end
                        for k,v in pairs(extConfig)do
                            -- 多组key value
                            -- 有两套数据 domain 和 update json
                            local key = ""
                            for m,n in pairs(v)do
                                if m == "Key" then
                                    key = n
                                end
                                if key == "domain" and m == 'Value' then
                                    domainJson = n
                                    local jsonValue = json.decode(n)
                                    for c,d in pairs(jsonValue)do
                                        local uKey = tostring(c)
                                        urlGroup[uKey] = d
                                        urlCount = urlCount + 1
                                    end
                                elseif key == "update" and m == 'Value' then
                                    local jsonValue = json.decode(n)
                                    for c,d in pairs(jsonValue)do
                                        local uKey = tostring(c)
                                        updateGroup[uKey] = d
                                        updateCount = updateCount + 1
                                    end
                                elseif key == "domainNew" and m == 'Value' then
                                    SetDomainNew(n)
                                elseif key == "channelMark" and m == 'Value' then
                                    SetChannelMark(n)
                                elseif key == "operationConfig" and m == 'Value' then
                                    SetOperationConfig(n)
                                elseif m == 'Value' then
                                    if not _G['_EXT_PARAMS'] then
                                        _G['_EXT_PARAMS'] = {}
                                    end
                                    _G['_EXT_PARAMS'][key] = n
                                end
                            end
                        end
                        
                        if urlGroup and urlCount > 0 then
                            _G['_DOMAIN_DATA'] = urlGroup
                            Debug.LogWarning("driver urlCount不为空 :"..urlCount.." domainJson= "..domainJson)
                        else
                            Debug.LogWarning("driver urlCount为空 :"..urlCount.." domainJson= "..domainJson)
                        end

                        if updateCount and updateCount > 0 then
                            if not _G['_EXT_PARAMS'] then
                                _G['_EXT_PARAMS'] = {}
                            end
                            _G['_EXT_PARAMS']['update'] = updateGroup
                        end
                    else
                        InitExtParamsByHttp()
                    end

                    local chatAddressStr = jsonData.chatAddress
                    if chatAddressStr then
                        local chatAddress
                        if type(chatAddressStr) == 'table' then
                            chatAddress =  chatAddressStr
                        else
                            chatAddress =  json.decode(chatAddressStr) or {}
                        end
                        if chatAddress then
                            local chatData = {}
                            for k,v in pairs(chatAddress)do
                                local keyi = tostring(k)
                                if keyi == "VipIsOpen" then
                                    _G["_CHAT_EXTPARAMS_DATA_VIPISOPEN"] = v
                                end
                                chatData[k]=v
                            end
                            _G["_CHAT_EXTPARAMS_DATA"] = chatData
                        end
                    end
                else
                    InitExtParamsByHttp()
                end
            else
                InitExtParamsByHttp()
            end
        else
            InitExtParamsByHttp()
        end
    end
end


-- 使用driver_util工具类中的方法获取
-- local game_config = nil
-- function GetGameConfig()
--     if game_config ~= nil then
--         return game_config
--     end
--     local textAsset = Resources.Load("game_config", typeof(TextAsset))
--     game_config = CS.MiniJSON.Json.Deserialize(textAsset.text)
    
--     return game_config
-- end


-- Q1PID = nil -- 分包pid
-- function GetQ1PID()
--     if not Q1PID then
--         local f,res = xpcall(function ()
--             Q1PID = GetAppProperty("Q1_Pid","default",true)
--             if Q1PID and Q1PID ~= 0 then
--                 return Q1PID
--             end
--         end,debug.traceback)
--         if not f then
--             Debug.LogError(res)
--         end
--     end
-- 	return Q1PID
-- end

function InitExtParamsByHttp()
    local isDomestic = IsDomestic()
    -- 海外屏蔽https请求
    if isDomestic and isDomestic == true then
        local appid = const.GetGameID() -- 位面2118，位面2 2124
        local pid = 20006
        local isDebugMode = false
        if driverUtil then
            local game_config = driverUtil.GetGameConfig()
            pid = game_config.CHANNEL_ID-- TODO默认用youyi的渠道id
            isDebugMode = game_config.ENABLE_Q1_DEBUG_MODE -- TODO默认用youyi的渠道id 
        else
            Debug.LogError("InitExtParamsByHttp driver_util is null")
        end
        
        local clientType = 2
        if Application.platform == RuntimePlatform.IPhonePlayer then
            clientType = 1
        end
        local uuid = "b863-cfb5-225e-4022-d13f" -- 因为此时sdk未初始化完成所以用一个固定值
    
        local baseUrl = "http://sdkapi.4g.q1.com:800/api/sdk/v2/appsetting/getextconfig?pid=%s&clientType=%s&appid=%s&uuid=%s"
        if not isDebugMode then
            baseUrl = "https://sdkapi.cool-play.com/api/sdk/v2/appsetting/getextconfig?pid=%s&clientType=%s&appid=%s&uuid=%s"
        end
        local uri = string.format(baseUrl,pid,clientType,appid,uuid)
        local www = UnityWebRequest.Get(uri)
        www.timeout = 3000
        local webrequest = www:SendWebRequest()
        local urlGroup = {}
        LuaStartCoroutine(webrequest, function ()
            if www.isNetworkError or www.isHttpError then
                Warning("+ip_info\n",uri,www.error,"\n-ip_info")
            else
                Warning("+ip_info\n",uri,www.downloadHandler.text,"\n-ip_info")
                local str = www.downloadHandler.text
                EncodeExtParams(str)
            end
        end)
    end
end

function EncodeExtParams(str)
    local json = require "dkjson"
    local jsonData = json.decode(str) or {}
    local resultStr = jsonData.result
    local resultData
    local extConfig
    if not resultStr then
        if jsonData and jsonData.extConfig then
            extConfig = jsonData.extConfig
        end
    else
        resultData = json.decode(resultStr) or {}
        if resultData then
            extConfig = resultData.extConfig
        end
    end
    local urlGroup = {}
    local urlCount = 0
    local updateGroup = {}
    local updateCount = 0
    local domainJson = ""
    if extConfig then
        for k,v in pairs(extConfig)do
            -- 多组key value
            -- 有两套数据 domain 和 update json
            local key = ""
            for m,n in pairs(v)do
                if m == "Key" then
                    key = n
                end
                if key == "domain" and m == 'Value' then
                    domainJson = n
                    local jsonValue = json.decode(n)
                    for c,d in pairs(jsonValue)do
                        local uKey = tostring(c)
                        urlGroup[uKey] = d
                        urlCount = urlCount + 1
                    end
                elseif key == "update" and m == 'Value' then
                    local jsonValue = json.decode(n)
                    for c,d in pairs(jsonValue)do
                        local uKey = tostring(c)
                        updateGroup[uKey] = d
                        updateCount = updateCount + 1
                    end
                elseif key == "domainNew" and m == 'Value' then
                    SetDomainNew(n)
                elseif key == "channelMark" and m == 'Value' then
                    SetChannelMark(n)
                elseif key == "operationConfig" and m == 'Value' then
                    SetOperationConfig(n)
                elseif m == 'Value' then
                    if not _G['_EXT_PARAMS'] then
                        _G['_EXT_PARAMS'] = {}
                    end
                    _G['_EXT_PARAMS'][key] = n
                end
            end
        end
    end
    if urlGroup and urlCount > 0 then
        _G['_DOMAIN_DATA'] = urlGroup
        Debug.LogWarning("driver urlCount不为空 :"..urlCount.." domainJson= "..domainJson)
    else
        Debug.LogWarning("driver urlCount为空 :"..urlCount.." domainJson= "..domainJson)
    end

    if not _G['_EXT_PARAMS'] then
        _G['_EXT_PARAMS'] = {}
    end
    _G['_EXT_PARAMS']['jsonStr'] = str
    if updateCount and updateCount > 0 then
        _G['_EXT_PARAMS']['update'] = updateGroup
    end

    local chatAddress
    if not resultData then
        if jsonData and jsonData.chatAddress then
            chatAddress = jsonData.chatAddress
        end
    else
        chatAddress = resultData.chatAddress
    end
    if chatAddress then
        local chatData = {}
        for k,v in pairs(chatAddress)do
            local keyi = tostring(k)
            if keyi == "VipIsOpen" then
                _G["_CHAT_EXTPARAMS_DATA_VIPISOPEN"] = v
            end
            chatData[k]=v
        end
        _G["_CHAT_EXTPARAMS_DATA"] = chatData
    end
end

---@param str string @ domainNew信息，简化后台配置
function SetDomainNew(str)
    local json = require "dkjson"
    local domainNewGroup = {}
    local domainNewCount = 0
    local jsonValue = json.decode(str)
    for c,d in pairs(jsonValue)do
        local uKey = "$" .. tostring(c) -- 加上$前缀
        domainNewGroup[uKey] = d
        domainNewCount = domainNewCount + 1
    end

    if domainNewGroup and domainNewCount > 0 then -- 添加domainNew配置，减少后台配置量
        _G['_DOMAIN_NEW'] = domainNewGroup
        if not domainNewGroup["$mainDomain"] or domainNewGroup["$mainDomain"] == "" then
            Debug.LogError("domainNew mainDomain未配置")
        end

        if not domainNewGroup["$apiDomain"] or domainNewGroup["$apiDomain"] == "" then
            Debug.LogError("domainNew $apiDomain未配置")
        end

        if not domainNewGroup["$gameID"] or domainNewGroup["$gameID"] == "" then
            Debug.LogError("domainNew gameID未配置")
        end
    else
        Debug.LogWarning("未设置domainNew")
    end
end
--设置运营配置
function SetOperationConfig(str)
    local OperationConfigGroup = {}
    local OperationConfigCount = 0
    local json = require "dkjson"
    local jsonValue = json.decode(str)
    for c, d in pairs(jsonValue) do
        --local uKey = "$" .. tostring(c) -- 加上$前缀
        OperationConfigGroup[c] = d
        OperationConfigCount = OperationConfigCount + 1
        print("设置OperationConfig ",c,d)
    end

    if OperationConfigGroup and OperationConfigCount > 0 then -- 添加domainNew配置，减少后台配置量
        _G['_OperationConfig'] = OperationConfigGroup
        print("设置OperationConfig 成功:", OperationConfigCount)
    else
        print("未设置OperationConfig")
    end
end
---@param str string @ 渠道标识信息
function SetChannelMark(str)
    local json = require "dkjson"
    local channelMark
    local jsonValue = json.decode(str)
    for _,d in pairs(jsonValue)do
        channelMark = d
    end

    if channelMark then
        _G['_GAME_CHANNEL_MARK'] = channelMark
        Debug.LogWarning("driver channelMark不为空 :"..tostring(channelMark))
    else
        Debug.LogWarning("driver channelMark为空")
    end
end


function M:SetDatabase(_database)
    if databaseInitSuccess and database == _database then
        return
    end
    databaseInitSuccess = false
    ScriptTextBytes = {}
    database = _database
    self:LoadLuaScript()
end

function M:CreateLoader(parentName, objName, uiAssetBundle, uiScriptBundle, loaderAssetBundle)
    print(tostring(parentName)..","..tostring(objName)..",uiAssetBundle:"..uiAssetBundle..",uiScriptBundle:"..uiScriptBundle)
    if uiAssetBundle == nil or database == nil then
        Debug.LogError("创建资源加载器失败")
        return
    end
    self.parentObj = nil
    if parentName then
        self.parentObj = GameObject.Find(parentName)
        if not self.parentObj then
            Debug.LogWarning("未能找到父节点：" .. parentName)
            return
        end
        local objLoaderTrans = self.parentObj.transform:Find(objName)
        if objLoaderTrans and objLoaderTrans.gameObject.activeSelf then
            -- 界面已创建
            Debug.LogWarning(objName .. " 已经存在")
            return
        end
    else
        if GameObject.Find(objName) then
            -- 界面已创建
            return
        end
    end

    if loaderAssetBundle == nil then
        -- 使用默认创建方式
        loaderAssetBundle = "ui_update_loader"
    end
    self.uiAssetBundle = uiAssetBundle
    self.uiScriptBundle = uiScriptBundle

    self.parentTrans = nil
    if self.parentObj then
        self.parentTrans = self.parentObj.transform
    end

    self:LoadModuleFromDatabase(loaderAssetBundle, function (bSuccess, scriptModule)
        if not bSuccess then
            return
        end
        self:CreateUILoader(self.parentTrans, objName, scriptModule)
    end)
end

-- 使用asset bundle方式加载脚本，弃用。出于安全目的，使用预制体的方式加载
-- function M:CreateScriptLoader(parentName, objName, uiAssetBundle, uiScriptBundle, loaderAssetBundle)
--     print(tostring(parentName)..","..tostring(objName)..",uiAssetBundle"..uiAssetBundle..",uiScriptBundle"..uiScriptBundle..",assetbundle"..tostring(loaderAssetBundle))
--     if uiAssetBundle == nil then
--         return
--     end
--     self.parentObj = nil
--     if parentName then
--         self.parentObj = GameObject.Find(parentName)
--         if not self.parentObj then
--             Warning("未能找到父节点：" .. parentName)
--             return
--         end
--         local objLoaderTrans = self.parentObj.transform:Find(objName)
--         if objLoaderTrans and objLoaderTrans.gameObject.activeSelf then
--             -- 界面已创建
--             Debug.LogWarning(objName .. " 已经存在")
--             return
--         end
--     else
--         if GameObject.Find(objName) then
--             -- 界面已创建
--             return
--         end
--     end

--     if loaderAssetBundle == nil then
--         -- 使用默认创建方式
--         loaderAssetBundle = "driver/ui_update/ui_update_loader.txt"
--     end
--     self.uiAssetBundle = uiAssetBundle
--     self.uiScriptBundle = uiScriptBundle

--     self.parentTrans = nil
--     if self.parentObj then
--         self.parentTrans = self.parentObj.transform
--     end

--     local loaderModule = g_ModuleMap[loaderAssetBundle]
--     if loaderModule == nil then
--         local assetName = Path.GetFileName(loaderAssetBundle)
--         self:LoadAssetFileAsync(loaderAssetBundle, assetName, function (txt, assetPath, assetbundle)
--             if not txt or not txt.text then
--                 Debug.LogError("CreateScriptLoader load loader failed:"..loaderAssetBundle)
--                 return
--             end
--             local f,scriptModule = self:LoadLuaModule(txt)
--             if f then
--                 self:CreateUILoader(self.parentTrans, objName, scriptModule)
--             end
--         end)
--     else
--         self:CreateUILoader(self.parentTrans, objName, loaderModule)
--     end
-- end

function M:CreateUILoader(parentTrans, loaderName, loaderModule)
    if self.uiAssetBundle == nil then
        return
    end

    local loaderObj = GameObject(loaderName)
    self.loaderObj = loaderObj
    local rectTrans = loaderObj:AddComponent(typeof(RectTransform))
    rectTrans:SetParent(parentTrans, false)
    
    rectTrans.anchorMin = Vector2(0, 0)
    rectTrans.anchorMax = Vector2(1, 1)
    rectTrans.offsetMin = Vector2(0, 0)
    rectTrans.offsetMax = Vector2(0, 0)

    self.loaderModule = loaderModule:New()
    if self.loaderModule.Awake then
        self.loaderModule:Awake(loaderObj, self.uiAssetBundle, self.uiScriptBundle, self)
    end
end

function M:TranslateGameObject(uiRoot, callback)
    if Utility.LoadAssetFileAsync then
        -- 新版本可使用I18NText完成初始化
        return
    end

    -- 旧版本中I18NText使用的json资源中未包含所需要的字符串，只能销毁，通过 lang_util 来初始化
    local localTextList = uiRoot:GetComponentsInChildren(typeof(I18NText), true)
    local i = 1
    for i=0,localTextList.Length-1 do
        GameObject.Destroy(localTextList[i])
    end

    LeanTween.delayedCall(0, function ()
        if Utility.IsObjNull(uiRoot) then
            if callback then
                callback()
            end
            return
        end
        --国际化处理 
        local Lang = require "lang_util"
        Lang.TranslateGameObject(uiRoot)
        if callback then
            callback()
        end
    end)
end

function M:OnDestroy()
    if self.loaderModule then
        self.loaderModule:OnDestroy()
        self.loaderModule = nil
    end
    if self.loaderObj and not Utility.IsObjNull(self.loaderObj) then
        GameObject.Destroy(self.loaderObj)
    end
    ipResult = {}
    SetClassEmpty(self)
end

function GetIPResult()
    return ipResult
end

function M:LoadLuaModule(textAsset)
    if textAsset == nil then
        Debug.LogError("LoadLuaModule content is null")
        return
    end
    local scriptModule = g_ModuleMap[textAsset.name]
    if scriptModule ~= nil then
        return scriptModule
    end
    
    function loadStringEx()
        return loadstring(textAsset.text, textAsset.name)()
    end
    
    local f,res = xpcall(loadStringEx, debug.traceback)
    if not f then
        Debug.LogError(res)
    else
        g_ModuleMap[textAsset.name] = res
    end
    return f,res
end

function M:LoadAssetFileAsync(assetbundle, assetName, callback)
    if g_LuaScriptInitFinished then
        IOSystem.LoadAssetAsync(assetbundle, nil, function(obj)
            if not obj then
                Debug.LogError("LoadAssetFileAsync load failed:"..assetbundle)
                return
            end
    
            callback(obj)
        end)
        return
    end

    local enumeratorBehaviour = nil
    if Utility.LoadAssetFileAsync then
        if self.parentObj then
            enumeratorBehaviour = self.parentObj:GetComponent(typeof(LuaBehaviour))
        end
        if not enumeratorBehaviour then
            enumeratorBehaviour = self:GetDefaultBehaviour()
        end
        if enumeratorBehaviour then
            enumeratorBehaviour:StartCoroutine(Utility.LoadAssetFileAsync(assetbundle, assetName, callback))
        else
            Debug.LogError("LoadAssetFileAsync can not load asset:"..assetbundle)
        end
    else
        -- Utility.LoadAssetFileAsync 与 g_LuaScriptInitFinished 必须有一个接口可使用
        Debug.LogError("LoadAssetFileAsync can not find load asset interface:"..assetbundle)
    end
end

function M:LoadModuleFromDatabase(assetbundle, callback)
    local loaderModule = g_ModuleMap[assetbundle]
    if loaderModule then
        callback(true, loaderModule)
        return
    end

    if database == nil then
        Debug.LogError("LoadModuleFromDatabase 未找到资源配置")
        return nil
    end
    local obj = database:Get(assetbundle)
    if not obj or not obj.text then
        Debug.LogError("LoadModuleFromDatabase load loader failed:"..assetbundle)
        return
    end
    local f,scriptModule = self:LoadLuaModule(obj)
    if callback then
        callback(f, scriptModule)
    end
end

function M:LoadAllAssetAsync(assetbundle, callback)
    if g_LuaScriptInitFinished then
        IOSystem.LoadAssetBundleAllObjects(assetbundle, function(obj)
            if not obj then
                Debug.LogError("LoadAllAssetAsync load failed:"..assetbundle)
                return
            end
    
            callback(obj)
        end)
        return
    end

    local enumeratorBehaviour = nil
    if Utility.LoadAllAssetAsync then
        if self.parentObj then
            enumeratorBehaviour = self.parentObj:GetComponent(typeof(LuaBehaviour))
        end
        if not enumeratorBehaviour then
            enumeratorBehaviour = self:GetDefaultBehaviour()
        end
        if enumeratorBehaviour then
            enumeratorBehaviour:StartCoroutine(Utility.LoadAllAssetAsync(assetbundle, callback, "t:TextAsset"))
        else
            Debug.LogError("LoadAllAssetAsync can not load asset:"..assetbundle)
        end
    else
        -- Utility.LoadAllAssetAsync 与 g_LuaScriptInitFinished 必须有一个接口可使用
        Debug.LogError("LoadAllAssetAsync can not find load asset interface:"..assetbundle)
    end
end

function M:LoadAllAssetFromGataBase(database, callback)
    if database == nil then
        print("database is nil\n"..debug.traceback())
        return nil
    end
    local objSet = {}
    local count = database.Length
    print("driver database count:"..count)
    local i = 1
    local names = database.names
    --Get方法里会自动减1 -_-!
    for i=1,count do
        local obj = database:Get(i)
        if obj then
            local name = names[i - 1]
            objSet[i] = {
                name = name,
                text = obj.text
            }
        end
    end

    callback(objSet)
end

function M:GetDefaultBehaviour()
    -- 在不强更 update.unity 前提下，AssetLoader尚未创建情况下
    -- 没有太多可用于启动协程的组件选择，此处使用 EventSystem
    local eventSystem = GameObject.Find("EventSystem")
    if eventSystem then
        return eventSystem:GetComponent(typeof(EventSystem))
    end
end

function M:LoadLuaScript()
    if databaseInitSuccess then
        return
    end
    M:LoadAllAssetFromGataBase(database, function (objSet)
        if objSet == nil then
            Debug.LogError("Load Asset Failed:"..LuaScriptAssetBundleName)
            return
        end

        local i = 1
        for i=1,#objSet do
            local textAsset = objSet[i]
            if textAsset ~= nil then
                ScriptTextBytes[textAsset.name] = textAsset.text
            end
        end
        databaseInitSuccess = true
        
        self:LoadDriverUtil()
        self:PrintIpInfo()

        -- 扩展参数初始化
        if extInitCb then
            extInitCb()
        else
            InitExtParams()
        end
        --以下方法必须通过导出 CSharpCallLua 新接口，需要强更
        -- local luaEnv = LuaManager.luaEnv
        -- if luaEnv == nil  then
        --     if type(typeof(LuaEnvironment)) == "userdata" then
        --         luaEnv = LuaEnvironment.LuaEnv
        --     end
        -- end
        -- -- This type must add to CSharpCallLua: XLua.LuaEnv+CustomLoader
        -- luaEnv:AddLoader(ScriptLoader)
    end)
end

function M:LoadDriverUtil()
    self:LoadModuleFromDatabase("driver_util", function (bSuccess, scriptModule)
        if not bSuccess then
            return
        end

        driverUtil = scriptModule     
    end)
end

function M:GetErrorInfo()
    return ErrorInfo
 end

local _require = require
function M:LuaScriptInitFinished()
    require = _require
    M:Register()
end

function LuaScriptInitFinished()
    require = _require
    M:Register()
 end 

 function M:Register()
    local event = package.loaded['event']
    if event then

        event.Register("ERROR_LOG_INFO",function(eventName,errorInfo)
               if ErrorInfo[0] == nil or ErrorInfo[0] == "" then
               ErrorInfo[0]=errorInfo
                end
        end)
    
       event.Register("SERVERERROR_LOG_INFO",function(eventName,systemTitle, strTips)
    
       if ErrorInfo[1] == nil or ErrorInfo[1] == "" then
           ErrorInfo[1]=systemTitle
       end
        if ErrorInfo[2] == nil or ErrorInfo[2] == "" then
           ErrorInfo[2]=strTips
        end
        end)
    end
end

-- function ScriptLoader(filePath)
-- end

-- template module
local TM = {}
function TM:New()
    local newObj = {}
    setmetatable(newObj, M)
    return newObj
end

print("update driver node")

-- 将 lu_behaviour 模版暴露给全局变量
if g_LuaBehaviourModule == nil then
    g_LuaBehaviourModule = TM

    function driverRequire(moduleName)
        local scriptModule = ScriptModules[moduleName]
        if scriptModule then
            return scriptModule
        else
            local driverModule = moduleName -- ..".txt"
            scriptModule = ScriptTextBytes[driverModule]
            if scriptModule then
                scriptModule = loadstring(scriptModule, moduleName)()
                ScriptModules[moduleName] = scriptModule
            end
        end
        if scriptModule then
            return scriptModule
        else
            return _require(moduleName)

            -- 默认require数据不能缓存 why?
            -- scriptModule = _require(moduleName)
            -- if scriptModule then
            --     ScriptModules[moduleName] = scriptModule
            -- end
        end
        return scriptModule
    end
    require = driverRequire
end

--------------------------- http -------------------------------------
function CheckValidationResult(sender, certificate, chain, errors)
    return true
end

function CheckDelegateValid(delegateType)
    local f,res = xpcall(function()
        local testCheckFnc = function ()end
        local testDelegate = delegateType(testCheckFnc)
        testDelegate = nil
    end, debug.traceback)
    return f
end

function WebRequestSupportHttps()
    return CheckDelegateValid(RemoteCertificateValidationCallback)
end

function InternalResPostBytes(resStream, callback)
    print("InternalResPostBytes")
    --local reader = StreamReader(resStream, Encoding.UTF8)
    local reader = nil
    if Application.isEditor then
        reader = StreamReader(resStream, Encoding.GetEncoding("GB2312"))
    else
        --真机的GetEncoding不能传参
        reader = StreamReader(resStream, Encoding.UTF8)
    end
    local text = reader:ReadToEnd()

    print(text.." callback:"..tostring(callback))
    if callback then
        CS.U3D.Threading.Dispatcher.instance:ToMainThread(function ()
            callback(text)
        end)
    end
    reader:Dispose()
end

function InternalStartPostData(request, callback)
    print("InternalStartPostData")
    if CheckDelegateValid(AsyncCallback) then
        print("https")
        request:BeginGetResponse(function (iasyncResult)
            local f,res = xpcall(function()
                local response = request:EndGetResponse(iasyncResult)
                local resStream = response:GetResponseStream()
                InternalResPostBytes(resStream, callback)
                resStream:Dispose()
            end, debug.traceback)
            if not f then
                Debug.LogError(res)
            end
            request = nil
        end, nil)
    else
        print("http")
        local response = request:GetResponse()
        local resStream = response:GetResponseStream()
        InternalResPostBytes(resStream, callback)
        resStream:Dispose()
        request = nil
        resStream = nil
    end
end

function InternalReqPostBytes(uri, fileName, data, size, callback)
    --print("InternalReqPostBytes:"..uri)
    local request = HttpWebRequest.Create(uri)
    if string.startwith(uri, "https") then
    -- if uri:StartsWith("https", StringComparison.OrdinalIgnoreCase) then
        ServicePointManager.ServerCertificateValidationCallback = CheckValidationResult
    end
    CS.System.GC.Collect()
    request.Timeout = 10000
    --ServicePointManager.DefaultConnectionLimit = 200
    ServicePointManager.DefaultConnectionLimit = 1000
    --local dataSize = size

    -- 边界符
    local ticks = DateTime.UtcNow.Ticks
    ticks = tostring(ticks)
    local boundary = string.format('%x', tonumber(ticks))..""
    request.ContentType = "multipart/form-data; boundary="..boundary

    boundary = "--"..boundary;

    -- 添加上传文件参数格式边界
    local paramFormat = boundary.."\r\nContent-Disposition: form-data; name=\"%s\";\r\n\r\n%s\r\n"
    local param = NameValueCollection()
    param:Add("fname", Path.GetExtension(fileName))

    local memStream = MemoryStream()
    
    --写上参数
    local i
    local paramKeys = param.AllKeys
    local paramLen = paramKeys.Length - 1
    for i=0,paramLen do
        local key = paramKeys[i]
        local formitem = string.format(paramFormat, key, param:Get(key))
        --local formitembytes = Encoding.Unicode.GetBytes(formitem)
        --memStream:Write(formitembytes, 0, formitembytes.Length)
        memStream:Write(formitem, 0, #formitem)
        --dataSize = dataSize + #formitem
    end

    -- 添加上传文件数据格式边界
    local dataFormat = boundary.."\r\nContent-Disposition: form-data; name=\"%s\";filename=\"%s\"\r\nContent-Type:application/octet-stream\r\n\r\n";
    local header = string.format(dataFormat, "Filedata", fileName)
    --local headerbytes = Encoding.UTF8.GetBytes(header)
    --memStream:Write(headerbytes, 0, headerbytes.Length)
    memStream:Write(header, 0, #header)
    --dataSize = dataSize + #header

    -- 文件数据
    memStream:Write(data, 0, size)
    --dataSize = dataSize + size

    -- 添加文件结束边界
    local endBoundary = "\r\n\n"..boundary.."\r\nContent-Disposition: form-data; name=\"Upload\"\r\n\nSubmit Query\r\n"..boundary.."--"
    --local boundarybytes = Encoding.UTF8.GetBytes(endBoundary)
    --memStream:Write(boundarybytes, 0, boundarybytes.Length)
    memStream:Write(endBoundary, 0, #endBoundary)
    --dataSize = dataSize + #endBoundary

    local memoryLength = memStream.Length
    --设置请求长度
    request.ContentLength = memoryLength
    request.Method = "POST"

    --将内存流数据读取位置归零
    memStream.Position = 0

    local tempBuffer = memStream:ToArray()
    memStream:Close()
    memStream:Dispose()

    --将内存流中的buffer写入到请求写入流
    print("write post memory leng:"..tostring(memoryLength))
    local writer = request:GetRequestStream()
    writer:Write(tempBuffer, 0, memoryLength)
    writer:Close()
    local f,res = xpcall(function()
        print("start post data")
        InternalStartPostData(request, function (result)
            print("callback")
            --writer:Close()
            writer:Dispose()
            if callback then
                callback(result)
            end
        end)
    end, debug.traceback)
    if not f then
        Debug.LogError(res)
        callback(res)
    end
end

function LuaStartCoroutine(ienumerator, callback)
    local util = require 'xlua.util'
    local t_fun = util.cs_generator(function()
        coroutine.yield(ienumerator)
        if callback then
            callback()
        end
    end)
    
    local defBehaviour = M:GetDefaultBehaviour()
    defBehaviour:StartCoroutine(t_fun)
end

function InternalUnityReqPostBytes(uri, fileName, data, size, callback)
    -- pc正常，真机报错：No constructor for UnityEngine.WWWForm
    local form = CS.UnityEngine.WWWForm()
    form:AddBinaryData("fileUpload", data, fileName)

    local www = UnityWebRequest.Post(uri, form)
    www.timeout = 3000
    local webrequest = www:SendWebRequest()
    LuaStartCoroutine(webrequest, function ()
        if callback == nil then
            return
        end
        if www.isNetworkError or www.isHttpError then
            callback(www.error)
        else
            callback(www.downloadHandler.text)
        end
    end)
end

function InternalFormFileReqPostBytes(uri, fileName, data, size, callback)
    local formSection = CS.System.Collections.Generic["List`1[UnityEngine.Networking.IMultipartFormSection]"]()
    formSection:Add(CS.UnityEngine.Networking.MultipartFormFileSection(fileName, data))

    local www = UnityWebRequest.Post(uri, formSection)
    www.timeout = 3000
    local webrequest = www:SendWebRequest()
    LuaStartCoroutine(webrequest, function ()
        if callback == nil then
            return
        end
        if www.isNetworkError or www.isHttpError then
            callback(www.error)
        else
            callback(www.downloadHandler.text)
        end
    end)
end

-- 需要兼容热更实现 -_-!
function ReqPostBytes(uri, fileName, data, size, callback)
    local f,res = xpcall(function()
        --lua 5.1中xpcall不支持直接传参，此处包装一次
        -- In Lua 5.2 and 5.3 xpcall now accepts function arguments directly:xpcall (f, msgh [, arg1, ···])
        -- 真机timeout
        -- InternalReqPostBytes(uri, fileName, data, size, callback)
        -- 真机不能构造WWWForm
        -- InternalUnityReqPostBytes(uri, fileName, data, size, callback)
        -- 真机 No such type: UnityEngine.Networking.MultipartFormFileSection
        -- InternalFormFileReqPostBytes(uri, fileName, data, size, callback)
        if const.EnableOSSReport() then
            if LogDebug.UnityWebReqPostBytesWithFiled then
            	if Config.IsTrue and Config.IsTrue("p_encryptLogFile") and AlgorithmUtils.XorEncrypt then -- 加密日志
                	data = AlgorithmUtils.XorEncrypt(data)
            	end
                LogDebug.UnityWebReqPostBytesWithFiled(uri, fileName, data, size, callback, "file")
            else
                if LogDebug.UnityWebReqPostBytes then
		            if Config.IsTrue and Config.IsTrue("p_encryptLogFile") and AlgorithmUtils.XorEncrypt then -- 加密日志
                	    data = AlgorithmUtils.XorEncrypt(data)
	                end
                    LogDebug.UnityWebReqPostBytes(uri, fileName, data, size, callback)
                else
                    callback("can not upload file")
                end
            end
        else
            if LogDebug.UnityWebReqPostBytes then
                LogDebug.UnityWebReqPostBytes(uri, fileName, data, size, callback)
            else
                callback("can not upload file")
            end
        end
    end, debug.traceback)
    if not f then
        Debug.LogError(res)
    end
end

function GetRegionID()
    if not game_config then
        game_config = driverUtil.GetGameConfig()
    end
    local REGION_ID = game_config.REGION_ID
    return REGION_ID
end

function IsDomestic()
    if not game_config then
        game_config = driverUtil.GetGameConfig()
    end
    local domestic = game_config.Q1SDK_DOMESTIC --Q1SDK_DOMESTIC 用于区分国内/外
    return domestic == true
end

function IsDebugMode()
    if not game_config then
        game_config = driverUtil.GetGameConfig()
    end
    local debugMode = game_config.ENABLE_Q1_DEBUG_MODE --ENABLE_Q1_DEBUG_MODE 用于区分内/外网
    return debugMode == true
end


local bugReportUrl = "https://res.ssl.q1.com/uploadg.axd?"
function GetBugReportUri()
    if WebRequestSupportHttps() then
        if Application.platform == RuntimePlatform.IPhonePlayer then
            bugReportUrl = "https://res-ssl.youyi-game.com/uploadg.axd?"
        end
        local url=bugReportUrl
        -- 使用新地址
        local urlGroup = _G['_DOMAIN_DATA']
        if urlGroup and urlGroup["BUG_REPORT_URL"] and urlGroup["BUG_REPORT_URL"] ~= "" then
            url = urlGroup["BUG_REPORT_URL"]
        end
        return url
    else
        return "http://res.szgla.com/UploadG.axd?"
    end
end

-- 2021/10/14 之前后台组提供的 http 上传方式链接
function GetBugReportSealUrl()
    local uri = GetBugReportUri()
    local uid = 1
    local aid = 1
    local time = os.time()
    local key = "154FAE4C-E33B-40D2-ADD4-38E3178061E1"
    -- local curTime = DateTime.UtcNow
    local GameId = const.GetGameID()
    --local wspath = string.format("ClientG/%s/%s/%s/", GameId, curTime.ToString("yyyyMM"), curTime.ToString("dd"))
    local fileType = "zip"
    local orgSign = string.format("%s%s%s%d%s", GameId, uid, aid, time, key)
    local sign = Utility.Md5(orgSign)
    local params = 
    {
        "gameid="..GameId,
        "uid="..uid,
        "aid="..aid,
        "time="..time,
        "sign="..string.lower(sign),
        --"wspath="..wspath,
        "type="..fileType,
    }

    uri = uri..table.concat( params, "&")

    return uri
end


-- oss 方式上传日志文件链接
function GetBugReportOSSUrl()
    local uri = ""
    local key = "6c468901541542a59e687014c3842ed8"
    if not game_config then
        game_config = driverUtil.GetGameConfig()
    end

    local isDebug = IsDebugMode()
    local isDomestic = IsDomestic()
    if isDebug then
        uri = url_mgr.REPORT_OSS_ROOT_URL.LAN
        if driverUtil then uri = driverUtil.CheckUrl(url_mgr.REPORT_OSS_ROOT_URL.LAN, url_mgr.REPORT_OSS_ROOT_URL_PATTERN.LAN) end
        key = "12345678"
    elseif isDomestic then
        uri = url_mgr.REPORT_OSS_ROOT_URL.DOMESTIC
        if driverUtil then uri = driverUtil.CheckUrl(url_mgr.REPORT_OSS_ROOT_URL.DOMESTIC, url_mgr.REPORT_OSS_ROOT_URL_PATTERN.DOMESTIC) end
    else
        local regionID = GetRegionID()
        if regionID == 2 then
            -- 亚太
            uri = url_mgr.REPORT_OSS_ROOT_URL.SA
            if driverUtil then uri = driverUtil.CheckUrl(url_mgr.REPORT_OSS_ROOT_URL.SA, url_mgr.REPORT_OSS_ROOT_URL_PATTERN.SA) end
        elseif regionID == 1 then
            -- 欧美
            uri = url_mgr.REPORT_OSS_ROOT_URL.EN
            if driverUtil then uri = driverUtil.CheckUrl(url_mgr.REPORT_OSS_ROOT_URL.EN, url_mgr.REPORT_OSS_ROOT_URL_PATTERN.EN) end
        else
            -- 审核服
            uri = url_mgr.REPORT_OSS_ROOT_URL.SA
            if driverUtil then uri = driverUtil.CheckUrl(url_mgr.REPORT_OSS_ROOT_URL.SA, url_mgr.REPORT_OSS_ROOT_URL_PATTERN.SA) end
        end
    end

    local GameId = const.GetGameID()
    local time = math.floor(util.UTC0Seconds())
    -- 参数说明 https://q1doc.yuque.com/staff-nseb80/kef0tf/ngy040
    -- 注意参数不能为空，若为空则需要从数组中移除后再计算 md5 ,目前不可能为空，所以暂不增加判断移除操作
    local params = 
    {
        "platform=1", -- 云存储平台(1阿里云 2腾讯云) 目前只支持阿里云
        "dir=bugreport/"..GameId,
        -- fileName: 指定上传文件名(为空时将自动使用文件MD5作为文件名)
        "isReplace=0",
        -- ext: 扩展信息(json格式字符串)
        "time="..time, -- 时间戳，UTC时间秒数，允许误差20分钟内有效
    }
    table.sort( params )

    local orgParams = table.concat( params, "&" )
    local orgSign = orgParams .. "&key=" .. key
    local sign = Utility.Md5(orgSign)
    sign = string.lower(sign)
    uri = uri .. "/api/upload/file?" .. orgParams .. "&sign=" .. sign
    Debug.LogWarning(uri)

    return uri
end

function GetJsonFiled( jsonDic, filedName )
    if (not jsonDic) or string.IsNullOrEmpty(filedName) then
        return nil
    end

    local bSuccess,code = jsonDic:TryGetValue(filedName)
    if not bSuccess then
        return nil
    end
    return code
end



function SendBugReport(callback)
    local logDebug = LogDebug.Instance
    local logPath = logDebug:GetCurrentLogFilePath()
    if logPath == nil or logPath.Length == 0 then
        Debug.LogError("当前日志文件不存在")
        return
    end

    local uri = ""
    local isOSSReport = const.EnableOSSReport()
    if isOSSReport then
        uri = GetBugReportOSSUrl()
    else
        uri = GetBugReportSealUrl()
    end

    Debug.Log("上传日志:"..logPath.." 到问题反馈服，url:"..uri)
    logDebug:ReadFile(logPath, function (fileInfo)
        --local fileName = Path.GetFileName(logPath).."."..fileType
        local fileType = "zip"
        local fileName = Path.GetFileNameWithoutExtension(logPath).."."..fileType
        ReqPostBytes(uri, fileName, fileInfo.content, fileInfo.size, function (result)
            if callback then
                -- callback(result)
                if type(result) == "string" then
                    callback(result)
                    print("上传日志完成,callback:"..result)
                else
                    local wwwResponse = "upload file failed"
                    local logDownloadUri = nil
                    if result.isNetworkError or result.isHttpError then
                        wwwResponse = result.error
                        callback(result.error)
                    else
                        wwwResponse = result.downloadHandler.text
                        -- --[[ oss 上报方式返回码示例
                        -- {
                        --     "code": 1, //返回值 -1系统出错 0参数错误 1成功 2签名不正确 3请求已超时 
                        --     "message": "上传成功", //返回消息
                        --     "data":"https://com-q1-res-ad-dev.oss-cn-shenzhen.aliyuncs.com/upload/test/202109/f708b2ad633f00da60e671464b434cb0.png"//文件Url
                        -- }
                        -- -- 原 http 上传返回示例
                        -- 1|https://res.ssl.q1.com/images/upload/ClientG/2118/202101/23/142813473121345.zip|上传成功
                        -- ]]
                        if isOSSReport and (not string.IsNullOrEmpty(wwwResponse)) then
                            local jsonData = CS.MiniJSON.Json.Deserialize(wwwResponse)
                            local code = GetJsonFiled(jsonData, "code")
                            local tmpData = GetJsonFiled(jsonData, "data")
                            -- code: userdata
                            code = tostring(code)
                            if code ~= nil and code == "1" and not string.IsNullOrEmpty(tmpData) then
                                logDownloadUri = tostring(tmpData)
                            end
                        end
                    end
                    print("上传日志完成,callback:"..wwwResponse)
                    callback(wwwResponse, logDownloadUri)
                end
            end
        end)
    end)
end

function StringSplit(str, delimma, func)
    delimma = delimma or ','
    local ret = {}

    -- 下面使用的string.find是普通文本模式
    local lastPos = 1
    local _start, _end = string.find(str, delimma, lastPos, true)
    while _start and _end do
        table.insert(ret, string.sub(str, lastPos, _start - 1))
        lastPos = _end + 1
        _start, _end = string.find(str, delimma, lastPos, true)
    end

    if string.len(str) >= lastPos then
        table.insert(ret, string.sub(str, lastPos))
    end
    if func then
        local parsed = {}
        table.foreach(ret, function(k, v)
            table.insert(parsed, func(v))
        end)
        ret = parsed
    end
    return ret
end

function GetAppProperty(key, defaultValue, isInt, filedName, propertyType, isDomestic)
    if Application.isEditor then
        return defaultValue
    end

    if Application.platform == RuntimePlatform.IPhonePlayer then
        if (not Q1SDK.Instance.GetAppIntProperty) then
            return defaultValue
        end

        if isInt then
            return Q1SDK.Instance:GetAppIntProperty(key, defaultValue)
        end
        local valueType = type(defaultValue)
        if valueType == "boolean" then
            return Q1SDK.Instance:GetAppBoolProperty(key, defaultValue)
        end
    else
        if (not Utility.GetAppProperty) then
            return defaultValue
        end

        if filedName == nil then
            filedName = "metaData"
        end
        if propertyType == nil then
            propertyType = 128 -- PackageManager.GET_META_DATA
        end

        if isInt then
            -- lua 5.1 int 自动转化为 double，不能自动区分类型
            if isDomestic then
                if Utility.GetAppIntProperty then
                    return Utility.GetAppIntProperty(key, defaultValue, filedName, propertyType)
                end
            else
                if Utility.GetApplicationIntProperty then
                    local errorCode,value = Utility.GetApplicationIntProperty(key, defaultValue, filedName, propertyType)
                    if errorCode ~= 0 then
                        Debug.Log("获取数值,"..key..",错误："..errorCode)
                    end
                    return value
                end
            end
            
        else
            if isDomestic then
                if Utility.GetAppProperty then
                    return Utility.GetAppProperty(key, defaultValue, filedName, propertyType)
                end
            else
                if Utility.GetApplicationProperty then
                    local errorCode,value = Utility.GetApplicationProperty(key, defaultValue, filedName, propertyType)
                    if errorCode ~= 0 then
                        Debug.Log("获取数值,"..key..",错误："..errorCode)
                    end
                    return value
                end
            end
        end
    end
    return defaultValue
end

function UpdateDomainName(str)
    if databaseInitSuccess then
        EncodeExtParams(str)
    else
        extInitCb = function ()
            EncodeExtParams(str)
        end
    end
end

if AssetsUpdator.RegisterDriverNodeUpdateCb then
    AssetsUpdator.RegisterDriverNodeUpdateCb(UpdateDomainName)
end

return TM

