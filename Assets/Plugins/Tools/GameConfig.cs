
using System;
using System.Collections.Generic;
using UnityEngine;

public class GameConfig
{
    public  Dictionary<string, object> gameConfig = null;
    public bool NotRepaceGameConfig = true;

    private GameConfig() 
    {
        TextAsset textAsset = Resources.Load<TextAsset>("game_config");
        gameConfig = (Dictionary<string, object>)MiniJSON.Json.Deserialize(textAsset.text);
        if (TryGetBool("ENABLE_SUB_PACKAGE"))
        {
                string replaceGameConfig = AssetLoadSubPackageCfg.SubPackageCfg.GetReplaceGameconfig();
                gameConfig = (Dictionary<string, object>)MiniJSON.Json.Deserialize(replaceGameConfig);
        }  
    }

    protected static GameConfig instance = null;

    public static GameConfig Instance()
    {
        if(instance != null)
        {
            if (instance.TryGetBool("ENABLE_SUB_PACKAGE") && AssetLoadSubPackageCfg.SubPackageCfg.isFinishReplace && instance.NotRepaceGameConfig)
            {
                instance = new GameConfig();
                instance.NotRepaceGameConfig = false;
            }
            return instance;
        }
        else
        {
            instance = new GameConfig();
            return instance;
        }
    }

    public string TryGetString(string key, string defaultValue = null)
    {
        object value;
        if (gameConfig != null && gameConfig.TryGetValue(key, out value))
        {
            return Convert.ToString(value);
        }
        return defaultValue;
    }

    public bool TryGetBool(string key, bool defaultValue = false)
    {
        object value;
        if (gameConfig != null && gameConfig.TryGetValue(key, out value))
        {
            return Convert.ToBoolean(value);
        }
        return defaultValue;
    }
}
