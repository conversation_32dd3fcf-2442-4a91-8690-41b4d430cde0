using System;
using System.Collections;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using System.Runtime.ExceptionServices;
using UnityEngine;

namespace Launch
{
    public enum AwaiterStatus
    {
        Pending = 0,
        Succeeded = 1,
        Faulted = 2,
    }

    public class LaunchTask : INotifyCompletion
    {
        public bool IsCompleted { get { return state != AwaiterStatus.Pending; } }
        public AwaiterStatus state;

        private Action callback;
        private ExceptionDispatchInfo exception;

        public LaunchTask GetAwaiter()
        {
            return this;
        }

        public void GetResult()
        {
            switch (this.state)
            {
                case AwaiterStatus.Succeeded:
                    this.Recycle();
                    break;
                case AwaiterStatus.Faulted:
                    var tempException = exception;
                    this.Recycle();
                    tempException?.Throw();
                    break;
                default:
                    throw new NotSupportedException("LaunchTask state error");
            }
        }

        public void SetStateSucceeded()
        {
            this.state = AwaiterStatus.Succeeded;
        }

        public void SetStateFaulted(Exception e)
        {
            this.state = AwaiterStatus.Faulted;
            exception = ExceptionDispatchInfo.Capture(e);
        }

        public void CallStateComplete()
        {
            switch (state)
            {
                case AwaiterStatus.Succeeded:
                    SetResult();
                    break;
                case AwaiterStatus.Faulted:
                    SetException();
                    break;
            }
        }

        public void SetResult()
        {
            state = AwaiterStatus.Succeeded;
            callback?.Invoke();
        }

        public void SetException(Exception e)
        {
            exception = ExceptionDispatchInfo.Capture(e);
            SetException();
        }

        public void SetException()
        {
            this.state = AwaiterStatus.Faulted;
            callback?.Invoke();
        }

        public void OnCompleted(Action continuation)
        {
            callback = continuation;
        }

        public void Recycle()
        {
            state = AwaiterStatus.Pending;
            callback = null;
            exception = null;

            if (queue.Count > 100)
                return;

            queue.Enqueue(this);
        }

        private static readonly ConcurrentQueue<LaunchTask> queue = new ConcurrentQueue<LaunchTask>();

        public static LaunchTask Create()
        {
            if (queue.TryDequeue(out LaunchTask task))
                return task;

            return new LaunchTask();
        }
    }

    public class LaunchTask<T> : INotifyCompletion
    {
        public bool IsCompleted { get { return state != AwaiterStatus.Pending; } }
        public AwaiterStatus state;
        private T value;

        private Action<T> invokeBefore;

        private Action callback;
        private ExceptionDispatchInfo exception;

        public LaunchTask<T> GetAwaiter()
        {
            return this;
        }

        public T GetResult()
        {
            switch (this.state)
            {
                case AwaiterStatus.Succeeded:
                    var tempValue = value;
                    Recycle();
                    return tempValue;
                case AwaiterStatus.Faulted:
                    var tempException = exception;
                    this.Recycle();
                    tempException?.Throw();
                    return default(T);
                default:
                    throw new NotSupportedException("LaunchTask state error");
            }
        }

        public void SetInvokeBefore(Action<T> invokeBefore)
        {
            this.invokeBefore = invokeBefore;
        }

        public void SetResult(T value)
        {
            state = AwaiterStatus.Succeeded;
            this.value = value;

            invokeBefore?.Invoke(this.value);
            callback?.Invoke();
        }

        public void SetException(Exception e)
        {
            exception = ExceptionDispatchInfo.Capture(e);
            SetException();
        }

        public void SetException()
        {
            this.state = AwaiterStatus.Faulted;
            callback?.Invoke();
        }

        public void OnCompleted(Action continuation)
        {
            callback = continuation;
        }

        public void Recycle()
        {
            state = AwaiterStatus.Pending;
            value = default(T);
            callback = null;
            invokeBefore = null;
            exception = null;

            if (queue.Count > 100)
                return;

            queue.Enqueue(this);
        }

        private static readonly ConcurrentQueue<LaunchTask<T>> queue = new ConcurrentQueue<LaunchTask<T>>();

        public static LaunchTask<T> Create()
        {
            if (queue.TryDequeue(out LaunchTask<T> task))
                return task;

            return new LaunchTask<T>();
        }
    }
}

