<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <LangVersion>latest</LangVersion>
    <_TargetFrameworkDirectories>non_empty_path_generated_by_unity.rider.package</_TargetFrameworkDirectories>
    <_FullFrameworkReferenceAssemblyPaths>non_empty_path_generated_by_unity.rider.package</_FullFrameworkReferenceAssemblyPaths>
    <DisableHandlePackageFileConflicts>true</DisableHandlePackageFileConflicts>
  <CodeAnalysisRuleSet></CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>10.0.20506</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <RootNamespace></RootNamespace>
    <ProjectGuid>{e896ed27-c824-2844-449b-ec855873b490}</ProjectGuid>
    <ProjectTypeGuids>{E097FAD1-6243-4DAD-9C02-E9B9EFC3FFC1};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>Unity.Collections.BurstCompatibilityGen</AssemblyName>
    <TargetFrameworkVersion>v4.7.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp\Bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE;UNITY_2020_2_5;UNITY_2020_2;UNITY_2020;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_INCLUDE_TESTS;USE_SEARCH_ENGINE_API;SCENE_TEMPLATE_MODULE;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_UNET;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_COLLAB;ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_UNET;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_RUNTIME_PERMISSIONS;ENABLE_ENGINE_CODE_STRIPPING;ENABLE_ONSCREEN_KEYBOARD;INCLUDE_DYNAMIC_GI;ENABLE_MONO_BDWGC;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;ENABLE_VIDEO;PLATFORM_ANDROID;UNITY_ANDROID;UNITY_ANDROID_API;ENABLE_EGL;ENABLE_NETWORK;ENABLE_RUNTIME_GI;ENABLE_CRUNCH_TEXTURE_COMPRESSION;UNITY_CAN_SHOW_SPLASH_SCREEN;UNITY_HAS_GOOGLEVR;UNITY_HAS_TANGO;ENABLE_SPATIALTRACKING;UNITY_ASTC_ONLY_DECOMPRESS;PLATFORM_EXTENDS_VULKAN_DEVICE;PLATFORM_HAS_MULTIPLE_SWAPCHAINS;UNITY_ANDROID_SUPPORTS_SHADOWFILES;ENABLE_UNITYADS_RUNTIME;UNITY_UNITYADS_API;ENABLE_MONO;NET_4_6;ENABLE_PROFILER;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;UNITY_PRO_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_LEGACY_INPUT_MANAGER;CROSS_PLATFORM_INPUT;MOBILE_INPUT;ODIN_INSPECTOR;HOTFIX_ENABLE;ODIN_INSPECTOR_3;OBI_ONI_SUPPORTED;DREAMTECK_SPLINES;DISABLE_UWA_SDK;SUPER_TOOLS_UNITYMENUITEM_PACKUP;SUPER_TOOLS_MENUITEM;LETAI_TRUESHADOW;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER;NET_STANDARD_2_0</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn></NoWarn>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>Temp\bin\Release\</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn></NoWarn>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup>
    <NoConfig>true</NoConfig>
    <NoStdLib>true</NoStdLib>
    <AddAdditionalExplicitAssemblyReferences>false</AddAdditionalExplicitAssemblyReferences>
    <ImplicitlyExpandNETStandardFacades>false</ImplicitlyExpandNETStandardFacades>
    <ImplicitlyExpandDesignTimeFacades>false</ImplicitlyExpandDesignTimeFacades>
  </PropertyGroup>
  <ItemGroup>
     <Compile Include="Packages\com.unity.collections@1.4.0\Unity.Collections.BurstCompatibilityGen\BurstCompatibilityTests.cs" />
 <Reference Include="UnityEngine">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AIModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ARModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AccessibilityModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AndroidJNIModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AnimationModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AssetBundleModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AudioModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ClothModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ClusterInputModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ClusterRendererModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.CoreModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.CrashReportingModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.DSPGraphModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.DirectorModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.GIModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.GameCenterModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.GridModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.HotReloadModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.IMGUIModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ImageConversionModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.InputModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.InputLegacyModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.JSONSerializeModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.LocalizationModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ParticleSystemModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.PerformanceReportingModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.PhysicsModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.Physics2DModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ProfilerModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ScreenCaptureModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.SharedInternalsModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.SpriteMaskModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.SpriteShapeModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.StreamingModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.SubstanceModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.SubsystemsModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TLSModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TerrainModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TerrainPhysicsModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TextCoreModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TextRenderingModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TilemapModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UIModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UIElementsModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UIElementsNativeModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsNativeModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UNETModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.UNETModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UmbraModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityAnalyticsModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityConnectModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityCurlModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityTestProtocolModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityWebRequestModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityWebRequestAudioModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityWebRequestTextureModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityWebRequestWWWModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.VFXModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.VRModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.VehiclesModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.VideoModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.VirtualTexturingModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.WindModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.XRModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEditor.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.CoreModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.GraphViewModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.PackageManagerUIModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEditor.PackageManagerUIModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.SceneTemplateModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.UIElementsModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.UIElementsSamplesModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.UIServiceModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEditor.UIServiceModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.UnityConnectModule">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.Graphs">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/Managed/UnityEditor.Graphs.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.Android.Extensions">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.WindowsStandalone.Extensions">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll</HintPath>
 </Reference>
 <Reference Include="ExternalCSharpCompiler">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Library/PackageCache/com.unity.roslyn@0.2.2-preview/ExternalCSharpCompiler.dll</HintPath>
 </Reference>
 <Reference Include="Unity.Analytics.StandardEvents">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Library/PackageCache/com.unity.analytics@3.5.3/AnalyticsStandardEvents/Unity.Analytics.StandardEvents.dll</HintPath>
 </Reference>
 <Reference Include="Sirenix.OdinInspector.Attributes">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Plugins/Sirenix/Assemblies/Sirenix.OdinInspector.Attributes.dll</HintPath>
 </Reference>
 <Reference Include="Sirenix.Serialization.Config">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Plugins/Sirenix/Assemblies/Sirenix.Serialization.Config.dll</HintPath>
 </Reference>
 <Reference Include="Render">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Scripts/Render.dll</HintPath>
 </Reference>
 <Reference Include="Unity.Analytics.Editor">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Library/PackageCache/com.unity.analytics@3.5.3/Unity.Analytics.Editor.dll</HintPath>
 </Reference>
 <Reference Include="Unity.Burst.Unsafe">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Packages/com.unity.burst@1.6.6/Unity.Burst.Unsafe.dll</HintPath>
 </Reference>
 <Reference Include="Unity.Collections.LowLevel.ILSupport">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Packages/com.unity.collections@1.4.0/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll</HintPath>
 </Reference>
 <Reference Include="GoogleMobileAds.Unity">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Plugins/Admob/GoogleMobileAds/GoogleMobileAds.Unity.dll</HintPath>
 </Reference>
 <Reference Include="Unity.Burst.Cecil.Pdb">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Packages/com.unity.burst@1.6.6/Unity.Burst.CodeGen/Unity.Burst.Cecil.Pdb.dll</HintPath>
 </Reference>
 <Reference Include="LitJson">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Plugins/LitJson/LitJson.dll</HintPath>
 </Reference>
 <Reference Include="AOT">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Scripts/AOT.dll</HintPath>
 </Reference>
 <Reference Include="Firebase.Analytics">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Firebase/Plugins/Firebase.Analytics.dll</HintPath>
 </Reference>
 <Reference Include="Google.VersionHandler">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/ExternalDependencyManager/Editor/Google.VersionHandler.dll</HintPath>
 </Reference>
 <Reference Include="ExcelDataReader">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Scripts/Editor/HotReload/EditorDll/ExcelDataReader.dll</HintPath>
 </Reference>
 <Reference Include="PandaBehaviour">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Scripts/Third/PandaBehaviour/Core/PandaBehaviour.dll</HintPath>
 </Reference>
 <Reference Include="AssetStudio">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Scripts/Editor/Statistics/Plugins/AssetStudio/AssetStudio.dll</HintPath>
 </Reference>
 <Reference Include="Firebase.Crashlytics">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Firebase/Plugins/Firebase.Crashlytics.dll</HintPath>
 </Reference>
 <Reference Include="dnlib">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Packages/com.code-philosophy.hybridclr/Plugins/dnlib.dll</HintPath>
 </Reference>
 <Reference Include="Firebase.Messaging.Editor">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Firebase/Editor/Firebase.Messaging.Editor.dll</HintPath>
 </Reference>
 <Reference Include="DOTween">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Scripts/Demigiant/DOTween/DOTween.dll</HintPath>
 </Reference>
 <Reference Include="PandaBehaviourEditor">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Scripts/Third/PandaBehaviour/Core/Editor/PandaBehaviourEditor.dll</HintPath>
 </Reference>
 <Reference Include="Firebase.App">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Firebase/Plugins/Firebase.App.dll</HintPath>
 </Reference>
 <Reference Include="Scene">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Scripts/Scene.dll</HintPath>
 </Reference>
 <Reference Include="Common">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Scripts/Common.dll</HintPath>
 </Reference>
 <Reference Include="Google.IOSResolver">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/ExternalDependencyManager/Editor/1.2.181/Google.IOSResolver.dll</HintPath>
 </Reference>
 <Reference Include="BaseToolsLib">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Scripts/BaseToolsLib.dll</HintPath>
 </Reference>
 <Reference Include="ICSharpCode.SharpZipLib">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Scripts/GameUpdate/Plugins/ICSharpCode.SharpZipLib.dll</HintPath>
 </Reference>
 <Reference Include="Sirenix.Utilities">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Plugins/Sirenix/Assemblies/Sirenix.Utilities.dll</HintPath>
 </Reference>
 <Reference Include="Sirenix.Serialization">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Plugins/Sirenix/Assemblies/Sirenix.Serialization.dll</HintPath>
 </Reference>
 <Reference Include="K4os.Compression.LZ4">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Scripts/Editor/Statistics/Plugins/AssetStudio/K4os.Compression.LZ4.dll</HintPath>
 </Reference>
 <Reference Include="Firebase.Messaging">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Firebase/Plugins/Firebase.Messaging.dll</HintPath>
 </Reference>
 <Reference Include="Firebase.Editor">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Firebase/Editor/Firebase.Editor.dll</HintPath>
 </Reference>
 <Reference Include="UI">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Scripts/UI.dll</HintPath>
 </Reference>
 <Reference Include="System.Buffers">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Scripts/Editor/Statistics/Plugins/AssetStudio/System.Buffers.dll</HintPath>
 </Reference>
 <Reference Include="GoogleMobileAds.Core">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Plugins/Admob/GoogleMobileAds/GoogleMobileAds.Core.dll</HintPath>
 </Reference>
 <Reference Include="Unity.Compat">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Parse/Plugins/dotNet45/Unity.Compat.dll</HintPath>
 </Reference>
 <Reference Include="Base">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Scripts/Base.dll</HintPath>
 </Reference>
 <Reference Include="Editor">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Scripts/Editor/Editor.dll</HintPath>
 </Reference>
 <Reference Include="Controller">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Scripts/Controller.dll</HintPath>
 </Reference>
 <Reference Include="Firebase.TaskExtension">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Firebase/Plugins/Firebase.TaskExtension.dll</HintPath>
 </Reference>
 <Reference Include="Unity.Analytics.Tracker">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Library/PackageCache/com.unity.analytics@3.5.3/Unity.Analytics.Tracker.dll</HintPath>
 </Reference>
 <Reference Include="GoogleMobileAds.Common">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Plugins/Admob/GoogleMobileAds/GoogleMobileAds.Common.dll</HintPath>
 </Reference>
 <Reference Include="ExcelDataReader.DataSet">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Scripts/Editor/HotReload/EditorDll/ExcelDataReader.DataSet.dll</HintPath>
 </Reference>
 <Reference Include="GoogleMobileAds">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Plugins/Admob/GoogleMobileAds/GoogleMobileAds.dll</HintPath>
 </Reference>
 <Reference Include="DemiEditor">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Scripts/Demigiant/DemiLib/Core/Editor/DemiEditor.dll</HintPath>
 </Reference>
 <Reference Include="UWAShared">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Plugins/UWA/UWA_SDK/Runtime/ManagedLibs/UWAShared.dll</HintPath>
 </Reference>
 <Reference Include="PsdFile">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Scripts/Editor/PSD2UGUI/PsdFile.dll</HintPath>
 </Reference>
 <Reference Include="Firebase.RemoteConfig">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Firebase/Plugins/Firebase.RemoteConfig.dll</HintPath>
 </Reference>
 <Reference Include="Sirenix.OdinInspector.Editor">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Plugins/Sirenix/Assemblies/Sirenix.OdinInspector.Editor.dll</HintPath>
 </Reference>
 <Reference Include="Unity.Burst.Cecil.Mdb">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Packages/com.unity.burst@1.6.6/Unity.Burst.CodeGen/Unity.Burst.Cecil.Mdb.dll</HintPath>
 </Reference>
 <Reference Include="Unity.Burst.Cecil">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Packages/com.unity.burst@1.6.6/Unity.Burst.CodeGen/Unity.Burst.Cecil.dll</HintPath>
 </Reference>
 <Reference Include="Google.MiniJson">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Firebase/Plugins/Google.MiniJson.dll</HintPath>
 </Reference>
 <Reference Include="Unity.Burst.Cecil.Rocks">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Packages/com.unity.burst@1.6.6/Unity.Burst.CodeGen/Unity.Burst.Cecil.Rocks.dll</HintPath>
 </Reference>
 <Reference Include="Script">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Scripts/Script.dll</HintPath>
 </Reference>
 <Reference Include="GoogleMobileAds.Ump">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Plugins/Admob/GoogleMobileAds/GoogleMobileAds.Ump.dll</HintPath>
 </Reference>
 <Reference Include="Google.JarResolver">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/ExternalDependencyManager/Editor/1.2.181/Google.JarResolver.dll</HintPath>
 </Reference>
 <Reference Include="System.Numerics.Vectors">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Scripts/Editor/Statistics/Plugins/AssetStudio/System.Numerics.Vectors.dll</HintPath>
 </Reference>
 <Reference Include="Sirenix.Utilities.Editor">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Plugins/Sirenix/Assemblies/Sirenix.Utilities.Editor.dll</HintPath>
 </Reference>
 <Reference Include="UWAEditor">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Plugins/UWA/UWA_SDK/Editor/UWAEditor.dll</HintPath>
 </Reference>
 <Reference Include="Google.PackageManagerResolver">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/ExternalDependencyManager/Editor/1.2.181/Google.PackageManagerResolver.dll</HintPath>
 </Reference>
 <Reference Include="System.Memory">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Scripts/Editor/Statistics/Plugins/AssetStudio/System.Memory.dll</HintPath>
 </Reference>
 <Reference Include="Game">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Scripts/Game.dll</HintPath>
 </Reference>
 <Reference Include="Mono.Cecil">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Library/PackageCache/com.unity.nuget.mono-cecil@1.11.4/Mono.Cecil.dll</HintPath>
 </Reference>
 <Reference Include="Firebase.Crashlytics.Editor">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Firebase/Editor/Firebase.Crashlytics.Editor.dll</HintPath>
 </Reference>
 <Reference Include="Newtonsoft.Json">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Library/PackageCache/com.unity.nuget.newtonsoft-json@2.0.0-preview/Runtime/Newtonsoft.Json.dll</HintPath>
 </Reference>
 <Reference Include="DOTweenPro">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Scripts/Demigiant/DOTweenPro/DOTweenPro.dll</HintPath>
 </Reference>
 <Reference Include="DemiLib">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Scripts/Demigiant/DemiLib/Core/DemiLib.dll</HintPath>
 </Reference>
 <Reference Include="GoogleMobileAds.Ump.Unity">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Plugins/Admob/GoogleMobileAds/GoogleMobileAds.Ump.Unity.dll</HintPath>
 </Reference>
 <Reference Include="DOTweenProEditor">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Scripts/Demigiant/DOTweenPro/Editor/DOTweenProEditor.dll</HintPath>
 </Reference>
 <Reference Include="Battle">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Scripts/Battle.dll</HintPath>
 </Reference>
 <Reference Include="DOTweenEditor">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Scripts/Demigiant/DOTween/Editor/DOTweenEditor.dll</HintPath>
 </Reference>
 <Reference Include="Unity.Tasks">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Parse/Plugins/dotNet45/Unity.Tasks.dll</HintPath>
 </Reference>
 <Reference Include="Google.VersionHandlerImpl">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/ExternalDependencyManager/Editor/1.2.181/Google.VersionHandlerImpl.dll</HintPath>
 </Reference>
 <Reference Include="Firebase.Platform">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Assets/Firebase/Plugins/Firebase.Platform.dll</HintPath>
 </Reference>
 <Reference Include="nunit.framework">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Library/PackageCache/com.unity.ext.nunit@1.0.6/net35/unity-custom/nunit.framework.dll</HintPath>
 </Reference>
 <Reference Include="mscorlib">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/mscorlib.dll</HintPath>
 </Reference>
 <Reference Include="System">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.dll</HintPath>
 </Reference>
 <Reference Include="System.Core">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Core.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Serialization">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Runtime.Serialization.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.Linq">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.Linq.dll</HintPath>
 </Reference>
 <Reference Include="System.Numerics">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.dll</HintPath>
 </Reference>
 <Reference Include="System.Numerics.Vectors">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.Vectors.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Http">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Net.Http.dll</HintPath>
 </Reference>
 <Reference Include="Microsoft.CSharp">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Microsoft.CSharp.dll</HintPath>
 </Reference>
 <Reference Include="System.Data">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Data.dll</HintPath>
 </Reference>
 <Reference Include="Microsoft.Win32.Primitives">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/Microsoft.Win32.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="netstandard">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/netstandard.dll</HintPath>
 </Reference>
 <Reference Include="System.AppContext">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.AppContext.dll</HintPath>
 </Reference>
 <Reference Include="System.Collections.Concurrent">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Concurrent.dll</HintPath>
 </Reference>
 <Reference Include="System.Collections">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.dll</HintPath>
 </Reference>
 <Reference Include="System.Collections.NonGeneric">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.NonGeneric.dll</HintPath>
 </Reference>
 <Reference Include="System.Collections.Specialized">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Specialized.dll</HintPath>
 </Reference>
 <Reference Include="System.ComponentModel.Annotations">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Annotations.dll</HintPath>
 </Reference>
 <Reference Include="System.ComponentModel">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.dll</HintPath>
 </Reference>
 <Reference Include="System.ComponentModel.EventBasedAsync">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.EventBasedAsync.dll</HintPath>
 </Reference>
 <Reference Include="System.ComponentModel.Primitives">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.ComponentModel.TypeConverter">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.TypeConverter.dll</HintPath>
 </Reference>
 <Reference Include="System.Console">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Console.dll</HintPath>
 </Reference>
 <Reference Include="System.Data.Common">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Data.Common.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.Contracts">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Contracts.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.Debug">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Debug.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.FileVersionInfo">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.FileVersionInfo.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.Process">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Process.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.StackTrace">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.StackTrace.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.TextWriterTraceListener">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TextWriterTraceListener.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.Tools">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Tools.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.TraceSource">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TraceSource.dll</HintPath>
 </Reference>
 <Reference Include="System.Drawing.Primitives">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Drawing.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.Dynamic.Runtime">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Dynamic.Runtime.dll</HintPath>
 </Reference>
 <Reference Include="System.Globalization.Calendars">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Calendars.dll</HintPath>
 </Reference>
 <Reference Include="System.Globalization">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.dll</HintPath>
 </Reference>
 <Reference Include="System.Globalization.Extensions">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Extensions.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.Compression.ZipFile">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Compression.ZipFile.dll</HintPath>
 </Reference>
 <Reference Include="System.IO">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.FileSystem">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.FileSystem.DriveInfo">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.DriveInfo.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.FileSystem.Primitives">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.FileSystem.Watcher">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Watcher.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.IsolatedStorage">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.IsolatedStorage.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.MemoryMappedFiles">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.MemoryMappedFiles.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.Pipes">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Pipes.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.UnmanagedMemoryStream">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.UnmanagedMemoryStream.dll</HintPath>
 </Reference>
 <Reference Include="System.Linq">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.dll</HintPath>
 </Reference>
 <Reference Include="System.Linq.Expressions">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Expressions.dll</HintPath>
 </Reference>
 <Reference Include="System.Linq.Parallel">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Parallel.dll</HintPath>
 </Reference>
 <Reference Include="System.Linq.Queryable">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Queryable.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Http.Rtc">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Http.Rtc.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.NameResolution">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NameResolution.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.NetworkInformation">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NetworkInformation.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Ping">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Ping.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Primitives">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Requests">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Requests.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Security">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Security.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Sockets">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Sockets.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.WebHeaderCollection">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebHeaderCollection.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.WebSockets.Client">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.Client.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.WebSockets">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.dll</HintPath>
 </Reference>
 <Reference Include="System.ObjectModel">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ObjectModel.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection.Emit">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection.Emit.ILGeneration">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.ILGeneration.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection.Emit.Lightweight">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.Lightweight.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection.Extensions">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Extensions.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection.Primitives">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.Resources.Reader">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Reader.dll</HintPath>
 </Reference>
 <Reference Include="System.Resources.ResourceManager">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.ResourceManager.dll</HintPath>
 </Reference>
 <Reference Include="System.Resources.Writer">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Writer.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.CompilerServices.VisualC">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.CompilerServices.VisualC.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Extensions">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Extensions.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Handles">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Handles.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.InteropServices">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Numerics">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Numerics.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Serialization.Formatters">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Formatters.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Serialization.Json">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Json.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Serialization.Primitives">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Serialization.Xml">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Xml.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Claims">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Claims.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Cryptography.Algorithms">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Algorithms.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Cryptography.Csp">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Csp.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Cryptography.Encoding">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Encoding.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Cryptography.Primitives">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Cryptography.X509Certificates">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.X509Certificates.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Principal">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Principal.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.SecureString">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.SecureString.dll</HintPath>
 </Reference>
 <Reference Include="System.ServiceModel.Duplex">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Duplex.dll</HintPath>
 </Reference>
 <Reference Include="System.ServiceModel.Http">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Http.dll</HintPath>
 </Reference>
 <Reference Include="System.ServiceModel.NetTcp">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.NetTcp.dll</HintPath>
 </Reference>
 <Reference Include="System.ServiceModel.Primitives">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.ServiceModel.Security">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Security.dll</HintPath>
 </Reference>
 <Reference Include="System.Text.Encoding">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.dll</HintPath>
 </Reference>
 <Reference Include="System.Text.Encoding.Extensions">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.Extensions.dll</HintPath>
 </Reference>
 <Reference Include="System.Text.RegularExpressions">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.RegularExpressions.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.Overlapped">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Overlapped.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.Tasks">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.Tasks.Parallel">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.Parallel.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.Thread">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Thread.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.ThreadPool">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.ThreadPool.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.Timer">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Timer.dll</HintPath>
 </Reference>
 <Reference Include="System.ValueTuple">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ValueTuple.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.ReaderWriter">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.ReaderWriter.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.XDocument">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XDocument.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.XmlDocument">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlDocument.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.XmlSerializer">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlSerializer.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.XPath">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.XPath.XDocument">
 <HintPath>C:/Program Files/Unity_2020_2_5/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.XDocument.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.TestRunner">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Library/ScriptAssemblies/UnityEditor.TestRunner.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TestRunner">
 <HintPath>E:/SVN/xMan250507/BinClient/Client/Library/ScriptAssemblies/UnityEngine.TestRunner.dll</HintPath>
 </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="Unity.Burst.csproj">
      <Project>{c6e34459-199c-c024-514a-14fdedcefeb8}</Project>
      <Name>Unity.Burst</Name>
    </ProjectReference>
    <ProjectReference Include="Unity.Collections.csproj">
      <Project>{28faf74c-dbc2-6925-5643-4190f188ba2f}</Project>
      <Name>Unity.Collections</Name>
    </ProjectReference>
    <ProjectReference Include="UnityEditor.UI.csproj">
      <Project>{aa10a73a-0fb6-07c5-fcce-bc576a35745b}</Project>
      <Name>UnityEditor.UI</Name>
    </ProjectReference>
    <ProjectReference Include="UnityEngine.UI.csproj">
      <Project>{4e97dac8-471b-8ebc-e6a8-7bf55a8b1a61}</Project>
      <Name>UnityEngine.UI</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>
