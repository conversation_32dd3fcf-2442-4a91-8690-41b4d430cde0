
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     TextTransform Samples/Packages/com.unity.collections/Unity.Collections/HeapString.tt
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Runtime.CompilerServices;
using Unity.Collections.LowLevel.Unsafe;
using UnityEngine.Internal;
#if UNITY_PROPERTIES_EXISTS
using Unity.Properties;
#endif



namespace Unity.Collections
{
    /// <summary>
    /// An unmanaged, allocated, mutable, resizable UTF-8 string.
    /// </summary>
    /// <remarks>
    /// The string is always null-terminated, meaning a zero byte always immediately follows the last character.
    /// </remarks>
    [BurstCompatible]
    [Obsolete("HeapString has been removed and replaced with NativeText (RemovedAfter 2021-07-21) (UnityUpgradable) -> NativeText", false)]
    public partial struct HeapString
        : INativeList<byte>
        , IDisposable
        , IUTF8Bytes
        , IComparable<String>
        , IEquatable<String>
        , IComparable<HeapString>
        , IEquatable<HeapString>
        , IComparable<FixedString32Bytes>
        , IEquatable<FixedString32Bytes>
        , IComparable<FixedString64Bytes>
        , IEquatable<FixedString64Bytes>
        , IComparable<FixedString128Bytes>
        , IEquatable<FixedString128Bytes>
        , IComparable<FixedString512Bytes>
        , IEquatable<FixedString512Bytes>
        , IComparable<FixedString4096Bytes>
        , IEquatable<FixedString4096Bytes>
    {
        // NOTE! This Length is always > 0, because we have a null terminating byte.
        // We hide this byte from HeapString users.
        private NativeList<byte> m_Data;

        /// <summary>
        /// The current length in bytes of this string.
        /// </summary>
        /// <remarks>
        /// The length does not include the null terminator byte.
        /// </remarks>
        /// <value>The current length in bytes of the UTF-8 encoded string.</value>
        public int Length
        {
            get {
                return m_Data.Length - 1;
            }
            set {
                m_Data.Resize(value + 1, NativeArrayOptions.UninitializedMemory);
                m_Data[value] = 0;
            }
        }

        /// <summary>
        /// The current capacity of this string.
        /// </summary>
        /// <remarks>
        /// The null-terminator byte is not included in the Capacity, so the string's character buffer is `Capacity + 1` in size.
        /// </remarks>
        /// <value>The current capacity of the string.</value>
        public int Capacity
        {
            get {
                return m_Data.Capacity - 1;
            }
            set {
                m_Data.Capacity = value + 1;
            }
        }

        /// <summary>
        /// Attempt to set the length in bytes of this string.
        /// </summary>
        /// <param name="newLength">The new length in bytes of the string.</param>
        /// <param name="clearOptions">Whether any bytes added should be zeroed out.</param>
        /// <returns>Always true.</returns>
        public bool TryResize(int newLength, NativeArrayOptions clearOptions = NativeArrayOptions.ClearMemory)
        {
            // this can't ever fail, because if we can't resize malloc will abort
            Length = newLength;
            return true;
        }

        /// <summary>
        /// Whether this string has no characters.
        /// </summary>
        /// <value>True if this string has no characters.</value>
        public bool IsEmpty => m_Data.Length == 1;

        /// <summary>
        /// Whether this string's character buffer has been allocated and not yet deallocated.
        /// </summary>
        /// <value>Whether this string's character buffer has been allocated and not yet deallocated.</value>
        public bool IsCreated => m_Data.IsCreated;

        /// <summary>
        /// Returns a pointer to this string's character buffer.
        /// </summary>
        /// <remarks>
        /// The pointer is made invalid by operations that reallocate the character buffer, such as setting <see cref="Capacity"/>.
        /// </remarks>
        /// <returns>A pointer to this string's character buffer.</returns>
        public unsafe byte* GetUnsafePtr()
        {
            return (byte*) m_Data.GetUnsafePtr();
        }

        /// <summary>
        /// The byte at an index.
        /// </summary>
        /// <param name="index">A zero-based byte index.</param>
        /// <value>The byte at the index.</value>
        /// <exception cref="IndexOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public byte this[int index]
        {
            get {
                CheckIndexInRange(index);
                return m_Data[index];
            }
            set {
                CheckIndexInRange(index);
                m_Data[index] = value;
            }
        }

        /// <summary>
        /// Returns the reference to the byte (not character) at an index.
        /// </summary>
        /// <remarks>
        /// Deallocating or reallocating this string's character buffer makes the reference invalid.
        /// </remarks>
        /// <param name="index">A byte index.</param>
        /// <returns>A reference to the byte at the index.</returns>
        /// <exception cref="IndexOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public ref byte ElementAt(int index)
        {
            CheckIndexInRange(index);
            return ref m_Data.ElementAt(index);
        }

        /// <summary>
        /// Sets the length to 0.
        /// </summary>
        public void Clear()
        {
            Length = 0;
        }

        /// <summary>
        /// Appends a byte.
        /// </summary>
        /// <remarks>
        /// A zero byte will always follow the newly appended byte.
        ///
        /// No validation is performed: it is your responsibility for the bytes of the string to form valid UTF-8 when you're done appending bytes.
        /// </remarks>
        /// <param name="value">A byte to append.</param>
        public void Add(in byte value)
        {
            this[Length++] = value;
        }

        /// <summary>
        /// Returns the lexicographical sort order of this string relative to another.
        /// </summary>
        /// <param name="other">Another string to compare with.</param>
        /// <returns>A number denoting the lexicographical sort order of this string relative to the other string:
        ///
        /// 0 denotes both strings have the same sort position.<br/>
        /// -1 denotes that this string should be sorted to precede the other.<br/>
        /// +1 denotes that this string should be sorted to follow the other.<br/>
        /// </returns>
        public int CompareTo(HeapString other)
        {
            return FixedStringMethods.CompareTo(ref this, other);
        }

        /// <summary>
        /// Returns true if this string and another are equal.
        /// </summary>
        /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="other">Another string to compare with.</param>
        /// <returns>True if the two strings are equal.</returns>
        public bool Equals(HeapString other)
        {
            return FixedStringMethods.Equals(ref this, other);
        }

        /// <summary>
        /// Releases all resources (memory and safety handles).
        /// </summary>
        public void Dispose()
        {
            m_Data.Dispose();
        }

        /// <summary>
        /// A copy of this string as a managed string.
        /// </summary>
        /// <remarks>
        /// For internal use only. Use <see cref="ToString"/> instead.
        /// </remarks>
        /// <value>A copy of this string as a managed string.</value>
        [CreateProperty]
        [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
        [NotBurstCompatible]
        public string Value => ToString();

        /// <summary>
        /// An enumerator over the characters (not bytes) of a HeapString.
        /// </summary>
        /// <remarks>
        /// In an enumerator's initial state, its index is invalid. The first <see cref="MoveNext"/> call advances the enumerator's index to the first character.
        /// </remarks>
        public struct Enumerator : IEnumerator<Unicode.Rune>
        {
            HeapString target;
            int offset;
            Unicode.Rune current;

            /// <summary>
            /// Initializes and returns an instance of HeapString.Enumerator.
            /// </summary>
            /// <param name="source">A HeapString for which to create an enumerator.</param>
            public Enumerator(HeapString source)
            {
                target = source;
                offset = 0;
                current = default;
            }

            /// <summary>
            /// Does nothing.
            /// </summary>
            public void Dispose()
            {
            }

            /// <summary>
            /// Advances the enumerator to the next character, returning true if <see cref="Current"/> is valid to read afterwards.
            /// </summary>
            /// <returns>True if <see cref="Current"/> is valid to read after the call.</returns>
            public bool MoveNext()
            {
                if (offset >= target.Length)
                    return false;

                unsafe
                {
                    Unicode.Utf8ToUcs(out current, target.GetUnsafePtr(), ref offset, target.Length);
                }

                return true;
            }

            /// <summary>
            /// Resets the enumerator to its initial state.
            /// </summary>
            public void Reset()
            {
                offset = 0;
                current = default;
            }

            object IEnumerator.Current => Current;

            /// <summary>
            /// The current character.
            /// </summary>
            /// <value>The current character.</value>
            public Unicode.Rune Current => current;
        }

        /// <summary>
        /// Returns an enumerator for iterating over the characters of the HeapString.
        /// </summary>
        /// <returns>An enumerator for iterating over the characters of the HeapString.</returns>
        public Enumerator GetEnumerator()
        {
            return new Enumerator(this);
        }

        /// <summary>
        /// Returns the lexicographical sort order of this string relative to another.
        /// </summary>
        /// <param name="other">Another string to compare with.</param>
        /// <returns>A number denoting the lexicographical sort order of this string relative to the other string:
        ///
        /// 0 denotes both strings have the same sort position.<br/>
        /// -1 denotes that this string should be sorted to precede the other.<br/>
        /// +1 denotes that this string should be sorted to follow the other.<br/>
        /// </returns>
        [NotBurstCompatible]
        public int CompareTo(String other)
        {
            return ToString().CompareTo(other);
        }

        /// <summary>
        /// Returns true if this string and another are equal.
        /// </summary>
        /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="other">Another string to compare with.</param>
        /// <returns>True if the two strings are equal.</returns>
        [NotBurstCompatible]
        public bool Equals(String other)
        {
            return ToString().Equals(other);
        }

        /// <summary>
        /// Initializes and returns an instance of HeapString with the characters copied from another string.
        /// </summary>
        /// <param name="source">A string to copy characters from.</param>
        /// <param name="allocator">The allocator to use.</param>
        [NotBurstCompatible]
        public HeapString(String source, Allocator allocator)
        {
            m_Data = new NativeList<byte>(source.Length * 2 + 1, allocator);
            Length = source.Length * 2; // maximum possible
            unsafe
            {
                fixed (char* sourceptr = source)
                {
                    var error = UTF8ArrayUnsafeUtility.Copy(GetUnsafePtr(), out var actualBytes, Capacity, sourceptr, source.Length);
                    if (error != CopyError.None)
                    {
                        m_Data.Dispose();
                        m_Data = default;
                        ThrowCopyError(error, source);
                    }
                    this.Length = actualBytes;
                }
            }
        }

        /// <summary>
        /// Initializes and returns an instance of HeapString with a specified initial capacity.
        /// </summary>
        /// <param name="capacity">The initial capacity in bytes.</param>
        /// <param name="allocator">The allocator to use.</param>
        public HeapString(int capacity, Allocator allocator)
        {
            m_Data = new NativeList<byte>(capacity + 1, allocator);
            this.Length = 0;
        }

        /// <summary>
        /// Initializes and returns an instance of HeapString with an initial capacity of 128 bytes.
        /// </summary>
        /// <param name="allocator">The allocator to use.</param>
        public HeapString(Allocator allocator)
        {
            m_Data = new NativeList<byte>(128 + 1, allocator);
            this.Length = 0;
        }


        /// <summary>
        /// Returns the lexicographical sort order of this string relative to another.
        /// </summary>
        /// <param name="other">Another string to compare with.</param>
        /// <returns>A number denoting the lexicographical sort order of this string relative to the other string:
        ///
        /// 0 denotes both strings have the same sort position.<br/>
        /// -1 denotes that this string should be sorted to precede the other.<br/>
        /// +1 denotes that this string should be sorted to follow the other.<br/>
        /// </returns>
        public int CompareTo(FixedString32Bytes other)
        {
            return FixedStringMethods.CompareTo(ref this, other);
        }


        /// <summary>
        /// Initializes and returns an instance of HeapString with the characters copied from another string.
        /// </summary>
        /// <param name="source">A string to copy characters from.</param>
        /// <param name="allocator">The allocator to use.</param>
        public HeapString(in FixedString32Bytes source, Allocator allocator)
        {
            m_Data = new NativeList<byte>(source.utf8LengthInBytes + 1, allocator);
            Length = source.utf8LengthInBytes;

            unsafe {
                byte* sbytes = (byte*) UnsafeUtilityExtensions.AddressOf(source.bytes);
                byte* dbytes = (byte*) m_Data.GetUnsafePtr();
                UnsafeUtility.MemCpy(dbytes, sbytes, source.utf8LengthInBytes);
            }
        }

        /// <summary>
        /// Returns true if two strings are equal.
        /// </summary>
        /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="a">A string to compare.</param>
        /// <param name="b">Another string to compare.</param>
        /// <returns>True if the two strings are equal.</returns>
        public static bool operator ==(in HeapString a, in FixedString32Bytes b)
        {
            unsafe {
                var aref = UnsafeUtilityExtensions.AsRef(a);
                int alen = aref.Length;
                int blen = b.utf8LengthInBytes;
                byte* aptr = (byte*) aref.GetUnsafePtr();
                byte* bptr = (byte*) UnsafeUtilityExtensions.AddressOf(b.bytes);
                return UTF8ArrayUnsafeUtility.EqualsUTF8Bytes(aptr, alen, bptr, blen);
            }
        }

        /// <summary>
        /// Returns true if two strings are unequal.
        /// </summary>
        /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="a">A string to compare.</param>
        /// <param name="b">Another string to compare.</param>
        /// <returns>True if the two strings are unequal.</returns>
        public static bool operator !=(in HeapString a, in FixedString32Bytes b)
        {
            return !(a == b);
        }

        /// <summary>
        /// Returns true if this string and another are equal.
        /// </summary>
        /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="other">Another string to compare with.</param>
        /// <returns>True if the two strings are equal.</returns>
        public bool Equals(FixedString32Bytes other)
        {
            return this == other;
        }

        /// <summary>
        /// Returns the lexicographical sort order of this string relative to another.
        /// </summary>
        /// <param name="other">Another string to compare with.</param>
        /// <returns>A number denoting the lexicographical sort order of this string relative to the other string:
        ///
        /// 0 denotes both strings have the same sort position.<br/>
        /// -1 denotes that this string should be sorted to precede the other.<br/>
        /// +1 denotes that this string should be sorted to follow the other.<br/>
        /// </returns>
        public int CompareTo(FixedString64Bytes other)
        {
            return FixedStringMethods.CompareTo(ref this, other);
        }


        /// <summary>
        /// Initializes and returns an instance of HeapString with the characters copied from another string.
        /// </summary>
        /// <param name="source">A string to copy characters from.</param>
        /// <param name="allocator">The allocator to use.</param>
        public HeapString(in FixedString64Bytes source, Allocator allocator)
        {
            m_Data = new NativeList<byte>(source.utf8LengthInBytes + 1, allocator);
            Length = source.utf8LengthInBytes;

            unsafe {
                byte* sbytes = (byte*) UnsafeUtilityExtensions.AddressOf(source.bytes);
                byte* dbytes = (byte*) m_Data.GetUnsafePtr();
                UnsafeUtility.MemCpy(dbytes, sbytes, source.utf8LengthInBytes);
            }
        }

        /// <summary>
        /// Returns true if two strings are equal.
        /// </summary>
        /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="a">A string to compare.</param>
        /// <param name="b">Another string to compare.</param>
        /// <returns>True if the two strings are equal.</returns>
        public static bool operator ==(in HeapString a, in FixedString64Bytes b)
        {
            unsafe {
                var aref = UnsafeUtilityExtensions.AsRef(a);
                int alen = aref.Length;
                int blen = b.utf8LengthInBytes;
                byte* aptr = (byte*) aref.GetUnsafePtr();
                byte* bptr = (byte*) UnsafeUtilityExtensions.AddressOf(b.bytes);
                return UTF8ArrayUnsafeUtility.EqualsUTF8Bytes(aptr, alen, bptr, blen);
            }
        }

        /// <summary>
        /// Returns true if two strings are unequal.
        /// </summary>
        /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="a">A string to compare.</param>
        /// <param name="b">Another string to compare.</param>
        /// <returns>True if the two strings are unequal.</returns>
        public static bool operator !=(in HeapString a, in FixedString64Bytes b)
        {
            return !(a == b);
        }

        /// <summary>
        /// Returns true if this string and another are equal.
        /// </summary>
        /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="other">Another string to compare with.</param>
        /// <returns>True if the two strings are equal.</returns>
        public bool Equals(FixedString64Bytes other)
        {
            return this == other;
        }

        /// <summary>
        /// Returns the lexicographical sort order of this string relative to another.
        /// </summary>
        /// <param name="other">Another string to compare with.</param>
        /// <returns>A number denoting the lexicographical sort order of this string relative to the other string:
        ///
        /// 0 denotes both strings have the same sort position.<br/>
        /// -1 denotes that this string should be sorted to precede the other.<br/>
        /// +1 denotes that this string should be sorted to follow the other.<br/>
        /// </returns>
        public int CompareTo(FixedString128Bytes other)
        {
            return FixedStringMethods.CompareTo(ref this, other);
        }


        /// <summary>
        /// Initializes and returns an instance of HeapString with the characters copied from another string.
        /// </summary>
        /// <param name="source">A string to copy characters from.</param>
        /// <param name="allocator">The allocator to use.</param>
        public HeapString(in FixedString128Bytes source, Allocator allocator)
        {
            m_Data = new NativeList<byte>(source.utf8LengthInBytes + 1, allocator);
            Length = source.utf8LengthInBytes;

            unsafe {
                byte* sbytes = (byte*) UnsafeUtilityExtensions.AddressOf(source.bytes);
                byte* dbytes = (byte*) m_Data.GetUnsafePtr();
                UnsafeUtility.MemCpy(dbytes, sbytes, source.utf8LengthInBytes);
            }
        }

        /// <summary>
        /// Returns true if two strings are equal.
        /// </summary>
        /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="a">A string to compare.</param>
        /// <param name="b">Another string to compare.</param>
        /// <returns>True if the two strings are equal.</returns>
        public static bool operator ==(in HeapString a, in FixedString128Bytes b)
        {
            unsafe {
                var aref = UnsafeUtilityExtensions.AsRef(a);
                int alen = aref.Length;
                int blen = b.utf8LengthInBytes;
                byte* aptr = (byte*) aref.GetUnsafePtr();
                byte* bptr = (byte*) UnsafeUtilityExtensions.AddressOf(b.bytes);
                return UTF8ArrayUnsafeUtility.EqualsUTF8Bytes(aptr, alen, bptr, blen);
            }
        }

        /// <summary>
        /// Returns true if two strings are unequal.
        /// </summary>
        /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="a">A string to compare.</param>
        /// <param name="b">Another string to compare.</param>
        /// <returns>True if the two strings are unequal.</returns>
        public static bool operator !=(in HeapString a, in FixedString128Bytes b)
        {
            return !(a == b);
        }

        /// <summary>
        /// Returns true if this string and another are equal.
        /// </summary>
        /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="other">Another string to compare with.</param>
        /// <returns>True if the two strings are equal.</returns>
        public bool Equals(FixedString128Bytes other)
        {
            return this == other;
        }

        /// <summary>
        /// Returns the lexicographical sort order of this string relative to another.
        /// </summary>
        /// <param name="other">Another string to compare with.</param>
        /// <returns>A number denoting the lexicographical sort order of this string relative to the other string:
        ///
        /// 0 denotes both strings have the same sort position.<br/>
        /// -1 denotes that this string should be sorted to precede the other.<br/>
        /// +1 denotes that this string should be sorted to follow the other.<br/>
        /// </returns>
        public int CompareTo(FixedString512Bytes other)
        {
            return FixedStringMethods.CompareTo(ref this, other);
        }


        /// <summary>
        /// Initializes and returns an instance of HeapString with the characters copied from another string.
        /// </summary>
        /// <param name="source">A string to copy characters from.</param>
        /// <param name="allocator">The allocator to use.</param>
        public HeapString(in FixedString512Bytes source, Allocator allocator)
        {
            m_Data = new NativeList<byte>(source.utf8LengthInBytes + 1, allocator);
            Length = source.utf8LengthInBytes;

            unsafe {
                byte* sbytes = (byte*) UnsafeUtilityExtensions.AddressOf(source.bytes);
                byte* dbytes = (byte*) m_Data.GetUnsafePtr();
                UnsafeUtility.MemCpy(dbytes, sbytes, source.utf8LengthInBytes);
            }
        }

        /// <summary>
        /// Returns true if two strings are equal.
        /// </summary>
        /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="a">A string to compare.</param>
        /// <param name="b">Another string to compare.</param>
        /// <returns>True if the two strings are equal.</returns>
        public static bool operator ==(in HeapString a, in FixedString512Bytes b)
        {
            unsafe {
                var aref = UnsafeUtilityExtensions.AsRef(a);
                int alen = aref.Length;
                int blen = b.utf8LengthInBytes;
                byte* aptr = (byte*) aref.GetUnsafePtr();
                byte* bptr = (byte*) UnsafeUtilityExtensions.AddressOf(b.bytes);
                return UTF8ArrayUnsafeUtility.EqualsUTF8Bytes(aptr, alen, bptr, blen);
            }
        }

        /// <summary>
        /// Returns true if two strings are unequal.
        /// </summary>
        /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="a">A string to compare.</param>
        /// <param name="b">Another string to compare.</param>
        /// <returns>True if the two strings are unequal.</returns>
        public static bool operator !=(in HeapString a, in FixedString512Bytes b)
        {
            return !(a == b);
        }

        /// <summary>
        /// Returns true if this string and another are equal.
        /// </summary>
        /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="other">Another string to compare with.</param>
        /// <returns>True if the two strings are equal.</returns>
        public bool Equals(FixedString512Bytes other)
        {
            return this == other;
        }

        /// <summary>
        /// Returns the lexicographical sort order of this string relative to another.
        /// </summary>
        /// <param name="other">Another string to compare with.</param>
        /// <returns>A number denoting the lexicographical sort order of this string relative to the other string:
        ///
        /// 0 denotes both strings have the same sort position.<br/>
        /// -1 denotes that this string should be sorted to precede the other.<br/>
        /// +1 denotes that this string should be sorted to follow the other.<br/>
        /// </returns>
        public int CompareTo(FixedString4096Bytes other)
        {
            return FixedStringMethods.CompareTo(ref this, other);
        }


        /// <summary>
        /// Initializes and returns an instance of HeapString with the characters copied from another string.
        /// </summary>
        /// <param name="source">A string to copy characters from.</param>
        /// <param name="allocator">The allocator to use.</param>
        public HeapString(in FixedString4096Bytes source, Allocator allocator)
        {
            m_Data = new NativeList<byte>(source.utf8LengthInBytes + 1, allocator);
            Length = source.utf8LengthInBytes;

            unsafe {
                byte* sbytes = (byte*) UnsafeUtilityExtensions.AddressOf(source.bytes);
                byte* dbytes = (byte*) m_Data.GetUnsafePtr();
                UnsafeUtility.MemCpy(dbytes, sbytes, source.utf8LengthInBytes);
            }
        }

        /// <summary>
        /// Returns true if two strings are equal.
        /// </summary>
        /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="a">A string to compare.</param>
        /// <param name="b">Another string to compare.</param>
        /// <returns>True if the two strings are equal.</returns>
        public static bool operator ==(in HeapString a, in FixedString4096Bytes b)
        {
            unsafe {
                var aref = UnsafeUtilityExtensions.AsRef(a);
                int alen = aref.Length;
                int blen = b.utf8LengthInBytes;
                byte* aptr = (byte*) aref.GetUnsafePtr();
                byte* bptr = (byte*) UnsafeUtilityExtensions.AddressOf(b.bytes);
                return UTF8ArrayUnsafeUtility.EqualsUTF8Bytes(aptr, alen, bptr, blen);
            }
        }

        /// <summary>
        /// Returns true if two strings are unequal.
        /// </summary>
        /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="a">A string to compare.</param>
        /// <param name="b">Another string to compare.</param>
        /// <returns>True if the two strings are unequal.</returns>
        public static bool operator !=(in HeapString a, in FixedString4096Bytes b)
        {
            return !(a == b);
        }

        /// <summary>
        /// Returns true if this string and another are equal.
        /// </summary>
        /// <remarks>Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="other">Another string to compare with.</param>
        /// <returns>True if the two strings are equal.</returns>
        public bool Equals(FixedString4096Bytes other)
        {
            return this == other;
        }

        /// <summary>
        /// Returns a managed string copy of this string.
        /// </summary>
        /// <returns>A managed string copy of this string.</returns>>
        [NotBurstCompatible]
        public override String ToString()
        {
            if (!m_Data.IsCreated)
                return "";
            return this.ConvertToString();
        }

        /// <summary>
        /// Returns a hash code of this string.
        /// </summary>
        /// <remarks>The hash code is an integer that is always the same for two equal strings but (very likely) different for two unequal strings.</remarks>
        /// <returns>A hash code of this string.</returns>
        public override int GetHashCode()
        {
            return this.ComputeHashCode();
        }

        /// <summary>
        /// Returns true if this string and another object are equal.
        /// </summary>
        /// <remarks>For the object to be equal, it must itself be a managed string, HeapString, or FixedString*N*Bytes.
        ///
        /// Two strings are equal if they have equal length and all their characters match.</remarks>
        /// <param name="other">Another string to compare with.</param>
        /// <returns>True if this string and the object are equal.</returns>
        [NotBurstCompatible]
        public override bool Equals(object other)
        {
            if(ReferenceEquals(null, other)) return false;
            if(other is String aString) return Equals(aString);
            if(other is HeapString aHeapString) return Equals(aHeapString);
            if(other is FixedString32Bytes a32) return Equals(a32);
            if(other is FixedString64Bytes a64) return Equals(a64);
            if(other is FixedString128Bytes a128) return Equals(a128);
            if(other is FixedString512Bytes a512) return Equals(a512);
            if(other is FixedString4096Bytes a4096) return Equals(a4096);
            return false;
        }

        [Conditional("ENABLE_UNITY_COLLECTIONS_CHECKS")]
        void CheckIndexInRange(int index)
        {
            if (index < 0)
                throw new IndexOutOfRangeException($"Index {index} must be positive.");
            if (index >= Length)
                throw new IndexOutOfRangeException($"Index {index} is out of range in HeapString of {Length} length.");
        }

        [Conditional("ENABLE_UNITY_COLLECTIONS_CHECKS")]
        void ThrowCopyError(CopyError error, String source)
        {
            throw new ArgumentException($"HeapString: {error} while copying \"{source}\"");
        }
    }
}
