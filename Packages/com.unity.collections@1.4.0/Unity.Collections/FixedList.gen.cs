
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     TextTransform Samples/Packages/com.unity.collections/Unity.Collections/FixedList.tt
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.Collections.Generic;
using System.Collections;
using System.Diagnostics;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System;
using Unity.Collections.LowLevel.Unsafe;
using Unity.Mathematics;
using UnityEngine.Internal;
using UnityEngine;
#if UNITY_PROPERTIES_EXISTS
using Unity.Properties;
#endif

namespace Unity.Collections
{
    [BurstCompatible(GenericTypeArguments = new [] { typeof(int), typeof(FixedBytes30) })]
    [Serializable]
    internal struct FixedList<T,U>
    : INativeList<T>
    where T : unmanaged
    where U : unmanaged
    {
        [SerializeField] internal ushort length;
        [SerializeField] internal U buffer;

        /// <summary>
        /// The current number of items in this list.
        /// </summary>
        /// <value>The current number of items in this list.</value>
        [CreateProperty]
        public int Length
        {
            get => length;
            set
            {
                FixedList.CheckResize<U,T>(value);
                length = (ushort)value;
            }
        }

        /// <summary>
        /// A property in order to display items in the Entity Inspector.
        /// </summary>
        [CreateProperty] IEnumerable<T> Elements => this.ToArray();

        /// <summary>
        /// Whether the list is empty.
        /// </summary>
        /// <value>True if this string has no characters or if the container has not been constructed.</value>
        public bool IsEmpty => Length == 0;

        internal int LengthInBytes => Length * UnsafeUtility.SizeOf<T>();

        unsafe internal byte* Buffer
        {
            get
            {
                fixed(U* u = &buffer)
                    return (byte*)u + FixedList.PaddingBytes<T>();
            }
        }

        /// <summary>
        /// The number of elements that can fit in this list.
        /// </summary>
        /// <value>The number of elements that can fit in this list.</value>
        /// <remarks>The capacity of a FixedList cannot be changed. The setter is included only for conformity with <see cref="INativeList{T}"/>.</remarks>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the new value does not match the current capacity.</exception>
        public int Capacity
        {
          get
          {
              return FixedList.Capacity<U,T>();
          }
          set
          {
              CollectionHelper.CheckCapacityInRange(value, Length);
          }
        }

        /// <summary>
        /// The element at a given index.
        /// </summary>
        /// <param name="index">An index.</param>
        /// <value>The value to store at the index.</value>
        /// <exception cref="IndexOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public T this[int index]
        {
            get
            {
                CollectionHelper.CheckIndexInRange(index, length);
                unsafe
                {
                    return UnsafeUtility.ReadArrayElement<T>(Buffer, CollectionHelper.AssumePositive(index));
                }
            }

            set
            {
                CollectionHelper.CheckIndexInRange(index, length);
                unsafe
                {
                    UnsafeUtility.WriteArrayElement<T>(Buffer, CollectionHelper.AssumePositive(index), value);
                }
            }
        }

        /// <summary>
        /// Returns the element at a given index.
        /// </summary>
        /// <param name="index">An index.</param>
        /// <returns>A reference to the element at the index.</returns>
        public ref T ElementAt(int index)
        {
            CollectionHelper.CheckIndexInRange(index, length);
            unsafe
            {
                return ref UnsafeUtility.ArrayElementAsRef<T>(Buffer, index);
            }
        }

        /// <summary>
        /// Returns the hash code of this list.
        /// </summary>
        /// <remarks>
        /// Only the content of the list (the bytes of the elements) are included in the hash. Any bytes beyond the length are not part of the hash.</remarks>
        /// <returns>The hash code of this list.</returns>
        public override int GetHashCode()
        {
            unsafe
            {
                return (int)CollectionHelper.Hash(Buffer, LengthInBytes);
            }
        }

        /// <summary>
        /// Appends an element to the end of this list. Increments the length by 1.
        /// </summary>
        /// <remarks>The same as <see cref="AddNoResize"/> (because a fixed list is never resized).</remarks>
        /// <param name="item">The element to append at the end of the list.</param>
        /// <exception cref="IndexOutOfRangeException">Thrown if the append exceeds the capacity.</exception>
        public void Add(in T item)
        {
            this[Length++] = item;
        }

        /// <summary>
        /// Appends elements from a buffer to the end of this list. Increments the length by the number of appended elements.
        /// </summary>
        /// <remarks>The same as <see cref="AddRangeNoResize"/>. Remember that a fixed list is never resized.</remarks>
        /// <param name="ptr">A buffer.</param>
        /// <param name="length">The number of elements from the buffer to append.</param>
        /// <exception cref="IndexOutOfRangeException">Thrown if the append exceeds the capacity.</exception>
        public unsafe void AddRange(void* ptr, int length)
        {
            T* data = (T*)ptr;
            for (var i = 0; i < length; ++i)
            {
                this[Length++] = data[i];
            }
        }

        /// <summary>
        /// Appends an element to the end of this list. Increments the length by 1.
        /// </summary>
        /// <remarks>The same as <see cref="Add"/>. Included only for consistency with the other list types.</remarks>
        /// <param name="item">The element to append at the end of the list.</param>
        /// <exception cref="IndexOutOfRangeException">Thrown if the append exceeds the capacity.</exception>
        public void AddNoResize(in T item) => Add(item);

        /// <summary>
        /// Appends elements from a buffer to the end of this list. Increments the length by the number of appended elements.
        /// </summary>
        /// <remarks>The same as <see cref="AddRange"/>. Included only for consistency with the other list types.</remarks>
        /// <param name="ptr">A buffer.</param>
        /// <param name="length">The number of elements from the buffer to append.</param>
        /// <exception cref="IndexOutOfRangeException">Thrown if the append exceeds the capacity.</exception>
        public unsafe void AddRangeNoResize(void* ptr, int length) => AddRange(ptr, length);

        /// <summary>
        /// Sets the length to 0.
        /// </summary>
        /// <remarks> Does *not* zero out the bytes.</remarks>
        public void Clear()
        {
            Length = 0;
        }

        /// <summary>
        /// Shifts elements toward the end of this list, increasing its length.
        /// </summary>
        /// <remarks>
        /// Right-shifts elements in the list so as to create 'free' slots at the beginning or in the middle.
        ///
        /// The length is increased by `end - begin`.
        ///
        /// If `end` equals `begin`, the method does nothing.
        ///
        /// The element at index `begin` will be copied to index `end`, the element at index `begin + 1` will be copied to `end + 1`, and so forth.
        ///
        /// The indexes `begin` up to `end` are not cleared: they will contain whatever values they held prior.
        /// </remarks>
        /// <param name="begin">The index of the first element that will be shifted up.</param>
        /// <param name="end">The index where the first shifted element will end up.</param>
        /// <exception cref="IndexOutOfRangeException">Thrown if the new length exceeds the capacity.</exception>
        public void InsertRangeWithBeginEnd(int begin, int end)
        {
            int items = end - begin;
            if(items < 1)
                return;
            int itemsToCopy = length - begin;
            Length += items;
            if(itemsToCopy < 1)
                return;
            int bytesToCopy = itemsToCopy * UnsafeUtility.SizeOf<T>();
            unsafe
            {
                byte *b = Buffer;
                byte *dest = b + end * UnsafeUtility.SizeOf<T>();
                byte *src = b + begin * UnsafeUtility.SizeOf<T>();
                UnsafeUtility.MemMove(dest, src, bytesToCopy);
            }
        }

        /// <summary>
        /// Inserts a single element at an index. Increments the length by 1.
        /// </summary>
        /// <param name="index">The index at which to insert the element.</param>
        /// <param name="item">The element to insert.</param>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public void Insert(int index, in T item)
        {
            InsertRangeWithBeginEnd(index, index+1);
            this[index] = item;
        }

        /// <summary>
        /// Copies the last element of this list to an index. Decrements the length by 1.
        /// </summary>
        /// <remarks>Useful as a cheap way to remove elements from a list when you don't care about preserving order.</remarks>
        /// <param name="index">The index to overwrite with the last element.</param>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public void RemoveAtSwapBack(int index)
        {
            RemoveRangeSwapBack(index, 1);
        }

        /// <summary>
        /// Copies the last *N* elements of this list to a range in this list. Decrements the length by *N*.
        /// </summary>
        /// <remarks>
        /// Copies the last `count`-numbered elements to the range starting at `index`.
        ///
        /// Useful as a cheap way to remove elements from a list when you don't care about preserving order.
        ///
        /// Does nothing if the count is less than 1.
        /// </remarks>
        /// <param name="index">The first index of the destination range.</param>
        /// <param name="count">The number of elements to copy and the amount by which to decrement the length.</param>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public void RemoveRangeSwapBack(int index, int count)
        {
            if (count > 0)
            {
                int copyFrom = math.max(Length - count, index + count);

                unsafe
                {
                    var sizeOf = UnsafeUtility.SizeOf<T>();
                    void* dst = Buffer + index * sizeOf;
                    void* src = Buffer + copyFrom * sizeOf;
                    UnsafeUtility.MemCpy(dst, src, (Length - copyFrom) * sizeOf);
                }

                Length -= count;
            }
        }

        /// <summary>
        /// Truncates the list by replacing the item at the specified index range with the items from the end the list. The list
        /// is shortened by number of elements in range.
        /// </summary>
        /// <param name="begin">The first index of the item to remove.</param>
        /// <param name="end">The index past-the-last item to remove.</param>
        /// <exception cref="ArgumentException">Thrown if end argument is less than begin argument.</exception>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if begin or end arguments are not positive or out of bounds.</exception>
        [Obsolete("RemoveRangeSwapBackWithBeginEnd(begin, end) is deprecated, use RemoveRangeSwapBack(index, count) instead. (RemovedAfter 2021-06-02)", false)]
        public void RemoveRangeSwapBackWithBeginEnd(int begin, int end) => RemoveRangeSwapBack(begin, end - begin);

        /// <summary>
        /// Removes the element at an index. Shifts everything above the index down by one and decrements the length by 1.
        /// </summary>
        /// <param name="index">The index of the element to remove.</param>
        /// <remarks>
        /// If you don't care about preserving the order of the elements, `RemoveAtSwapBack` is a more efficient way to remove an element.
        /// </remarks>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public void RemoveAt(int index)
        {
            RemoveRange(index, 1);
        }

        /// <summary>
        /// Removes *N* elements of a range. Shifts everything above the range down by *N* and decrements the length by *N*.
        /// </summary>
        /// <remarks>
        /// If you don't care about preserving the order of the elements, `RemoveAtSwapBack` is a more efficient way to remove elements.
        /// </remarks>
        /// <param name="index">The first index of the range to remove.</param>
        /// <param name="count">The number of elements to remove.</param>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public void RemoveRange(int index, int count)
        {
            if (count > 0)
            {
                int copyFrom = math.min(index + count, Length);

                unsafe
                {
                    var sizeOf = UnsafeUtility.SizeOf<T>();
                    void* dst = Buffer + index * sizeOf;
                    void* src = Buffer + copyFrom * sizeOf;
                    UnsafeUtility.MemCpy(dst, src, (Length - copyFrom) * sizeOf);
                }

                Length -= count;
            }
        }

        /// <summary>
        /// Truncates the list by removing the items at the specified index range, and shifting all remaining items to replace removed items. The list
        /// is shortened by number of elements in range.
        /// </summary>
        /// <param name="begin">The first index of the item to remove.</param>
        /// <param name="end">The index past-the-last item to remove.</param>
        /// <remarks>
        /// This method of removing item(s) is useful only in case when list is ordered and user wants to preserve order
        /// in list after removal In majority of cases is not important and user should use more performant `RemoveRangeSwapBackWithBeginEnd`.
        /// </remarks>
        /// <exception cref="ArgumentException">Thrown if end argument is less than begin argument.</exception>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if begin or end arguments are not positive or out of bounds.</exception>
        [Obsolete("RemoveRangeWithBeginEnd(begin, end) is deprecated, use RemoveRange(index, count) instead. (RemovedAfter 2021-06-02)", false)]
        public void RemoveRangeWithBeginEnd(int begin, int end) => RemoveRange(begin, end - begin);

        /// <summary>
        /// Returns a managed array that is a copy of this list.
        /// </summary>
        /// <returns>A managed array that is a copy of this list.</returns>
        [NotBurstCompatible]
        public T[] ToArray()
        {
            var result = new T[Length];
            unsafe
            {
                byte* s = Buffer;
                fixed(T* d = result)
                    UnsafeUtility.MemCpy(d, s, LengthInBytes);
            }
            return result;
        }

        /// <summary>
        /// Returns an array that is a copy of this list.
        /// </summary>
        /// <param name="allocator">The allocator to use.</param>
        /// <returns>An array that is a copy of this list.</returns>
        public NativeArray<T> ToNativeArray(AllocatorManager.AllocatorHandle allocator)
        {
            unsafe
            {
                var copy = CollectionHelper.CreateNativeArray<T>(Length, allocator, NativeArrayOptions.UninitializedMemory);
                UnsafeUtility.MemCpy(copy.GetUnsafePtr(), Buffer, LengthInBytes);
                return copy;
            }
        }
    }

    [BurstCompatible]
    struct FixedList
    {
        [BurstCompatible(GenericTypeArguments = new [] { typeof(int) })]
        internal static int PaddingBytes<T>() where T : struct
        {
            return math.max(0, math.min(6, (1 << math.tzcnt(UnsafeUtility.SizeOf<T>())) - 2));
        }

        [BurstCompatible(GenericTypeArguments = new [] { typeof(int), typeof(int) })]
        internal static int StorageBytes<BUFFER,T>() where BUFFER : struct where T : struct
        {
            return UnsafeUtility.SizeOf<BUFFER>() - PaddingBytes<T>();
        }

        [BurstCompatible(GenericTypeArguments = new [] { typeof(int), typeof(int) })]
        internal static int Capacity<BUFFER,T>() where BUFFER : struct where T : struct
        {
            return StorageBytes<BUFFER,T>() / UnsafeUtility.SizeOf<T>();
        }

        [BurstCompatible(GenericTypeArguments = new [] { typeof(int), typeof(int) })]
        [Conditional("ENABLE_UNITY_COLLECTIONS_CHECKS"), Conditional("UNITY_DOTS_DEBUG")]
        internal static void CheckResize<BUFFER,T>(int newLength) where BUFFER : struct where T : struct
        {
            var Capacity = Capacity<BUFFER,T>();
            if (newLength < 0 || newLength > Capacity)
                throw new IndexOutOfRangeException($"NewLength {newLength} is out of range of '{Capacity}' Capacity.");
        }
    }

    [Obsolete("Renamed to FixedList32Bytes<T> (UnityUpgradable) -> FixedList32Bytes<T>", true)]
    public struct FixedList32<T> where T : unmanaged {}

    /// <summary>
    /// An unmanaged, resizable list whose content is all stored directly in the 32-byte struct. Useful for small lists.
    /// </summary>
    /// <typeparam name="T">The type of the elements.</typeparam>
    [Serializable]
    [DebuggerTypeProxy(typeof(FixedList32BytesDebugView<>))]
    [BurstCompatible(GenericTypeArguments = new [] { typeof(int) })]
    public struct FixedList32Bytes<T>
    : INativeList<T>
    , IEnumerable<T> // Used by collection initializers.
    , IEquatable<FixedList32Bytes<T>>
    , IComparable<FixedList32Bytes<T>>
    , IEquatable<FixedList64Bytes<T>>
    , IComparable<FixedList64Bytes<T>>
    , IEquatable<FixedList128Bytes<T>>
    , IComparable<FixedList128Bytes<T>>
    , IEquatable<FixedList512Bytes<T>>
    , IComparable<FixedList512Bytes<T>>
    , IEquatable<FixedList4096Bytes<T>>
    , IComparable<FixedList4096Bytes<T>>
    where T : unmanaged
    {
        [SerializeField] internal ushort length;
        [SerializeField] internal FixedBytes30 buffer;

        /// <summary>
        /// The current number of items in this list.
        /// </summary>
        /// <value>The current number of items in this list.</value>
        [CreateProperty]
        public int Length
        {
            get => length;
            set
            {
                FixedList.CheckResize<FixedBytes30,T>(value);
                length = (ushort)value;
            }
        }

        /// <summary>
        /// A property in order to display items in the Entity Inspector.
        /// </summary>
        [CreateProperty] IEnumerable<T> Elements => this.ToArray();

        /// <summary>
        /// Whether this list is empty.
        /// </summary>
        /// <value>True if this string has no characters or if the container has not been constructed.</value>
        public bool IsEmpty => Length == 0;

        internal int LengthInBytes => Length * UnsafeUtility.SizeOf<T>();

        unsafe internal byte* Buffer
        {
            get
            {
                fixed(byte* b = &buffer.offset0000.byte0000)
                    return b + FixedList.PaddingBytes<T>();
            }
        }

        /// <summary>
        /// The number of elements that can fit in this list.
        /// </summary>
        /// <value>The number of elements that can fit in this list.</value>
        /// <remarks>The capacity of a FixedList cannot be changed. The setter is included only for conformity with <see cref="INativeList{T}"/>.</remarks>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the new value does not match the current capacity.</exception>
        public int Capacity
        {
          get
          {
              return FixedList.Capacity<FixedBytes30,T>();
          }

          set
          {
              CollectionHelper.CheckCapacityInRange(value, Length);
          }
        }

        /// <summary>
        /// The element at a given index.
        /// </summary>
        /// <param name="index">An index.</param>
        /// <value>The value to store at the index.</value>
        /// <exception cref="IndexOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public T this[int index]
        {
            get
            {
                CollectionHelper.CheckIndexInRange(index, length);
                unsafe
                {
                    return UnsafeUtility.ReadArrayElement<T>(Buffer, CollectionHelper.AssumePositive(index));
                }
            }

            set
            {
                CollectionHelper.CheckIndexInRange(index, length);
                unsafe
                {
                    UnsafeUtility.WriteArrayElement<T>(Buffer, CollectionHelper.AssumePositive(index), value);
                }
            }
        }

        /// <summary>
        /// Returns the element at a given index.
        /// </summary>
        /// <param name="index">An index.</param>
        /// <returns>The list element at the index.</returns>
        public ref T ElementAt(int index)
        {
            CollectionHelper.CheckIndexInRange(index, length);
            unsafe
            {
                return ref UnsafeUtility.ArrayElementAsRef<T>(Buffer, index);
            }
        }

        /// <summary>
        /// Returns the hash code of this list.
        /// </summary>
        /// <remarks>
        /// Only the content of the list (the bytes of the elements) are included in the hash. Any bytes beyond the length are not part of the hash.</remarks>
        /// <returns>The hash code of this list.</returns>
        public override int GetHashCode()
        {
            unsafe
            {
                return (int)CollectionHelper.Hash(Buffer, LengthInBytes);
            }
        }

        /// <summary>
        /// Appends an element to the end of this list. Increments the length by 1.
        /// </summary>
        /// <remarks>The same as <see cref="AddNoResize"/>. Remember that a fixed list is never resized.</remarks>
        /// <param name="item">The element to append at the end of the list.</param>
        /// <exception cref="IndexOutOfRangeException">Thrown if the append exceeds the capacity.</exception>
        public void Add(in T item)
        {
            this[Length++] = item;
        }

        /// <summary>
        /// Appends elements from a buffer to the end of this list. Increments the length by the number of appended elements.
        /// </summary>
        /// <remarks>The same as <see cref="AddRangeNoResize"/>. Remember that a fixed list is never resized.</remarks>
        /// <param name="ptr">A buffer.</param>
        /// <param name="length">The number of elements from the buffer to append.</param>
        /// <exception cref="IndexOutOfRangeException">Thrown if the append exceeds the capacity.</exception>
        public unsafe void AddRange(void* ptr, int length)
        {
            T* data = (T*)ptr;
            for (var i = 0; i < length; ++i)
            {
                this[Length++] = data[i];
            }
        }

        /// <summary>
        /// Appends an element to the end of this list. Increments the length by 1.
        /// </summary>
        /// <remarks>The same as <see cref="Add"/>. Included only for consistency with the other list types.</remarks>
        /// <param name="item">The element to append at the end of the list.</param>
        /// <exception cref="IndexOutOfRangeException">Thrown if the append exceeds the capacity.</exception>
        public void AddNoResize(in T item) => Add(item);

        /// <summary>
        /// Appends elements from a buffer to the end of this list. Increments the length by the number of appended elements.
        /// </summary>
        /// <remarks>The same as <see cref="AddRange"/>. Included only for consistency with the other list types.</remarks>
        /// <param name="ptr">A buffer.</param>
        /// <param name="length">The number of elements from the buffer to append.</param>
        /// <exception cref="IndexOutOfRangeException">Thrown if the append exceeds the capacity.</exception>
        public unsafe void AddRangeNoResize(void* ptr, int length) => AddRange(ptr, length);

        /// <summary>
        /// Sets the length to 0.
        /// </summary>
        /// <remarks> Does *not* zero out the bytes.</remarks>
        public void Clear()
        {
            Length = 0;
        }

        /// <summary>
        /// Shifts elements toward the end of this list, increasing its length.
        /// </summary>
        /// <remarks>
        /// Right-shifts elements in the list so as to create 'free' slots at the beginning or in the middle.
        ///
        /// The length is increased by `end - begin`.
        ///
        /// If `end` equals `begin`, the method does nothing.
        ///
        /// The element at index `begin` will be copied to index `end`, the element at index `begin + 1` will be copied to `end + 1`, and so forth.
        ///
        /// The indexes `begin` up to `end` are not cleared: they will contain whatever values they held prior.
        /// </remarks>
        /// <param name="begin">The index of the first element that will be shifted up.</param>
        /// <param name="end">The index where the first shifted element will end up.</param>
        /// <exception cref="IndexOutOfRangeException">Thrown if the new length exceeds the capacity.</exception>
        public void InsertRangeWithBeginEnd(int begin, int end)
        {
            int items = end - begin;
            if(items < 1)
                return;
            int itemsToCopy = length - begin;
            Length += items;
            if(itemsToCopy < 1)
                return;
            int bytesToCopy = itemsToCopy * UnsafeUtility.SizeOf<T>();
            unsafe
            {
                byte *b = Buffer;
                byte *dest = b + end * UnsafeUtility.SizeOf<T>();
                byte *src = b + begin * UnsafeUtility.SizeOf<T>();
                UnsafeUtility.MemMove(dest, src, bytesToCopy);
            }
        }

        /// <summary>
        /// Inserts a single element at an index. Increments the length by 1.
        /// </summary>
        /// <param name="index">The index at which to insert the element.</param>
        /// <param name="item">The element to insert.</param>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public void Insert(int index, in T item)
        {
            InsertRangeWithBeginEnd(index, index+1);
            this[index] = item;
        }

        /// <summary>
        /// Copies the last element of this list to an index. Decrements the length by 1.
        /// </summary>
        /// <remarks>Useful as a cheap way to remove elements from a list when you don't care about preserving order.</remarks>
        /// <param name="index">The index to overwrite with the last element.</param>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public void RemoveAtSwapBack(int index)
        {
            RemoveRangeSwapBack(index, 1);
        }

        /// <summary>
        /// Copies the last *N* elements of this list to a range in this list. Decrements the length by *N*.
        /// </summary>
        /// <remarks>
        /// Copies the last `count`-numbered elements to the range starting at `index`.
        ///
        /// Useful as a cheap way to remove elements from a list when you don't care about preserving order.
        ///
        /// Does nothing if the count is less than 1.
        /// </remarks>
        /// <param name="index">The first index of the destination range.</param>
        /// <param name="count">The number of elements to copy and the amount by which to decrement the length.</param>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public void RemoveRangeSwapBack(int index, int count)
        {
            if (count > 0)
            {
                int copyFrom = math.max(Length - count, index + count);

                unsafe
                {
                    var sizeOf = UnsafeUtility.SizeOf<T>();
                    void* dst = Buffer + index * sizeOf;
                    void* src = Buffer + copyFrom * sizeOf;
                    UnsafeUtility.MemCpy(dst, src, (Length - copyFrom) * sizeOf);
                }

                Length -= count;
            }
        }

        /// <summary>
        /// Copies the last *N* elements of this list to a range in this list. Decrements the length by *N*.
        /// </summary>
        /// <param name="begin">The first index of the item to remove.</param>
        /// <param name="end">The index past-the-last item to remove.</param>
        /// <exception cref="ArgumentException">Thrown if end argument is less than begin argument.</exception>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if begin or end arguments are not positive or out of bounds.</exception>
        [Obsolete("RemoveRangeSwapBackWithBeginEnd(begin, end) is deprecated, use RemoveRangeSwapBack(index, count) instead. (RemovedAfter 2021-06-02)", false)]
        public void RemoveRangeSwapBackWithBeginEnd(int begin, int end) => RemoveRangeSwapBack(begin, end - begin);

        /// <summary>
        /// Removes the element at an index. Shifts everything above the index down by one and decrements the length by 1.
        /// </summary>
        /// <param name="index">The index of the element to remove.</param>
        /// <remarks>
        /// If you don't care about preserving the order of the elements, `RemoveAtSwapBack` is a more efficient way to remove an element.
        /// </remarks>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public void RemoveAt(int index)
        {
            RemoveRange(index, 1);
        }

        /// <summary>
        /// Removes *N* elements of a range. Shifts everything above the range down by *N* and decrements the length by *N*.
        /// </summary>
        /// <remarks>
        /// If you don't care about preserving the order of the elements, `RemoveAtSwapBack` is a more efficient way to remove elements.
        /// </remarks>
        /// <param name="index">The first index of the range to remove.</param>
        /// <param name="count">The number of elements to remove.</param>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public void RemoveRange(int index, int count)
        {
            if (count > 0)
            {
                int copyFrom = math.min(index + count, Length);

                unsafe
                {
                    var sizeOf = UnsafeUtility.SizeOf<T>();
                    void* dst = Buffer + index * sizeOf;
                    void* src = Buffer + copyFrom * sizeOf;
                    UnsafeUtility.MemCpy(dst, src, (Length - copyFrom) * sizeOf);
                }

                Length -= count;
            }
        }

        /// <summary>
        /// Removes *N* elements of a range. Shifts everything above the range down by *N* and decrements the length by *N*.
        /// </summary>
        /// <param name="begin">The first index of the item to remove.</param>
        /// <param name="end">The index past-the-last item to remove.</param>
        /// <remarks>
        /// This method of removing item(s) is useful only in case when list is ordered and user wants to preserve order
        /// in list after removal In majority of cases is not important and user should use more performant `RemoveRangeSwapBackWithBeginEnd`.
        /// </remarks>
        /// <exception cref="ArgumentException">Thrown if end argument is less than begin argument.</exception>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if begin or end arguments are not positive or out of bounds.</exception>
        [Obsolete("RemoveRangeWithBeginEnd(begin, end) is deprecated, use RemoveRange(index, count) instead. (RemovedAfter 2021-06-02)", false)]
        public void RemoveRangeWithBeginEnd(int begin, int end) => RemoveRange(begin, end - begin);

        /// <summary>
        /// Returns a managed array that is a copy of this list.
        /// </summary>
        /// <returns>A managed array that is a copy of this list.</returns>
        [NotBurstCompatible]
        public T[] ToArray()
        {
            var result = new T[Length];
            unsafe
            {
                byte* s = Buffer;
                fixed(T* d = result)
                    UnsafeUtility.MemCpy(d, s, LengthInBytes);
            }
            return result;
        }

        /// <summary>
        /// Returns an array that is a copy of this list.
        /// </summary>
        /// <param name="allocator">The allocator to use.</param>
        /// <returns>An array that is a copy of this list.</returns>
        public NativeArray<T> ToNativeArray(AllocatorManager.AllocatorHandle allocator)
        {
            unsafe
            {
                var copy = CollectionHelper.CreateNativeArray<T>(Length, allocator, NativeArrayOptions.UninitializedMemory);
                UnsafeUtility.MemCpy(copy.GetUnsafePtr(), Buffer, LengthInBytes);
                return copy;
            }
        }


        /// <summary>
        /// Returns true if two lists are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for equality.</param>
        /// <param name="b">The second list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public static bool operator ==(in FixedList32Bytes<T> a, in FixedList32Bytes<T> b)
        {
            unsafe
            {
                if(a.length != b.length)
                    return false;
                return UnsafeUtility.MemCmp(a.Buffer, b.Buffer, a.LengthInBytes) == 0;
            }
        }

        /// <summary>
        /// Returns true if two lists are unequal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for inequality.</param>
        /// <param name="b">The second list to compare for inequality.</param>
        /// <returns>True if the two lists are unequal.</returns>
        public static bool operator !=(in FixedList32Bytes<T> a, in FixedList32Bytes<T> b)
        {
            return !(a == b);
        }

        /// <summary>
        /// Returns a number denoting whether this list should be placed before or after another list in a sort.
        /// </summary>
        /// <param name="other">A list to to compare with.</param>
        /// <returns>An integer denoting the respective sort position of the list relative to the other:
        ///
        /// 0 denotes that both lists should have the same position in a sort.
        /// -1 denotes that this list should precede the other list in a sort.
        /// +1 denotes that this list should follow the other list in a sort.
        /// </returns>
        public int CompareTo(FixedList32Bytes<T> other)
        {
            unsafe
            {
                fixed(byte* a = &buffer.offset0000.byte0000)
                {
                    byte* b = &other.buffer.offset0000.byte0000;
                    var aa = a + FixedList.PaddingBytes<T>();
                    var bb = b + FixedList.PaddingBytes<T>();
                    var mini = math.min(Length, other.Length);
                    for(var i = 0; i < mini; ++i)
                    {
                        var j = UnsafeUtility.MemCmp(aa + sizeof(T) * i, bb + sizeof(T) * i, sizeof(T));
                        if(j != 0)
                            return j;
                    }
                    return Length.CompareTo(other.Length);
                }
            }
        }

        /// <summary>
        /// Returns true if this list and another list are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="other">The list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public bool Equals(FixedList32Bytes<T> other)
        {
            return CompareTo(other) == 0;
        }


        /// <summary>
        /// Returns true if two lists are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for equality.</param>
        /// <param name="b">The second list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public static bool operator ==(in FixedList32Bytes<T> a, in FixedList64Bytes<T> b)
        {
            unsafe
            {
                if(a.length != b.length)
                    return false;
                return UnsafeUtility.MemCmp(a.Buffer, b.Buffer, a.LengthInBytes) == 0;
            }
        }

        /// <summary>
        /// Returns true if two lists are unequal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for inequality.</param>
        /// <param name="b">The second list to compare for inequality.</param>
        /// <returns>True if the two lists are unequal.</returns>
        public static bool operator !=(in FixedList32Bytes<T> a, in FixedList64Bytes<T> b)
        {
            return !(a == b);
        }

        /// <summary>
        /// Returns a number denoting whether this list should be placed before or after another list in a sort.
        /// </summary>
        /// <param name="other">A list to to compare with.</param>
        /// <returns>An integer denoting the respective sort position of the list relative to the other:
        ///
        /// 0 denotes that both lists should have the same position in a sort.
        /// -1 denotes that this list should precede the other list in a sort.
        /// +1 denotes that this list should follow the other list in a sort.
        /// </returns>
        public int CompareTo(FixedList64Bytes<T> other)
        {
            unsafe
            {
                fixed(byte* a = &buffer.offset0000.byte0000)
                {
                    byte* b = &other.buffer.offset0000.byte0000;
                    var aa = a + FixedList.PaddingBytes<T>();
                    var bb = b + FixedList.PaddingBytes<T>();
                    var mini = math.min(Length, other.Length);
                    for(var i = 0; i < mini; ++i)
                    {
                        var j = UnsafeUtility.MemCmp(aa + sizeof(T) * i, bb + sizeof(T) * i, sizeof(T));
                        if(j != 0)
                            return j;
                    }
                    return Length.CompareTo(other.Length);
                }
            }
        }

        /// <summary>
        /// Returns true if this list and another list are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="other">The list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public bool Equals(FixedList64Bytes<T> other)
        {
            return CompareTo(other) == 0;
        }


        /// <summary>
        /// Initializes and returns an instance of FixedList32Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList32Bytes&lt;T&gt;.</exception>
        public FixedList32Bytes(in FixedList64Bytes<T> other)
        {
            this = default;
            var error = Initialize(other);
            if(error != 0)
                FixedList.CheckResize<FixedBytes30,T>(other.Length);
        }

        /// <summary>
        /// Initializes an instance of FixedList32Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>zero on success, or non-zero on error.</returns>
        internal int Initialize(in FixedList64Bytes<T> other)
        {
            if(other.Length > Capacity)
                return (int)CopyError.Truncation;
            length = other.length;
            buffer = new FixedBytes30();
            unsafe
            {
                UnsafeUtility.MemCpy(Buffer, other.Buffer, LengthInBytes);
            }
            return 0;
        }

        /// <summary>
        /// Returns a new list that is a copy of another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>A new list that is a copy of the other.</returns>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList32Bytes&lt;T&gt;.</exception>
        public static implicit operator FixedList32Bytes<T>(in FixedList64Bytes<T> other)
        {
            return new FixedList32Bytes<T>(other);
        }

        /// <summary>
        /// Returns true if two lists are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for equality.</param>
        /// <param name="b">The second list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public static bool operator ==(in FixedList32Bytes<T> a, in FixedList128Bytes<T> b)
        {
            unsafe
            {
                if(a.length != b.length)
                    return false;
                return UnsafeUtility.MemCmp(a.Buffer, b.Buffer, a.LengthInBytes) == 0;
            }
        }

        /// <summary>
        /// Returns true if two lists are unequal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for inequality.</param>
        /// <param name="b">The second list to compare for inequality.</param>
        /// <returns>True if the two lists are unequal.</returns>
        public static bool operator !=(in FixedList32Bytes<T> a, in FixedList128Bytes<T> b)
        {
            return !(a == b);
        }

        /// <summary>
        /// Returns a number denoting whether this list should be placed before or after another list in a sort.
        /// </summary>
        /// <param name="other">A list to to compare with.</param>
        /// <returns>An integer denoting the respective sort position of the list relative to the other:
        ///
        /// 0 denotes that both lists should have the same position in a sort.
        /// -1 denotes that this list should precede the other list in a sort.
        /// +1 denotes that this list should follow the other list in a sort.
        /// </returns>
        public int CompareTo(FixedList128Bytes<T> other)
        {
            unsafe
            {
                fixed(byte* a = &buffer.offset0000.byte0000)
                {
                    byte* b = &other.buffer.offset0000.byte0000;
                    var aa = a + FixedList.PaddingBytes<T>();
                    var bb = b + FixedList.PaddingBytes<T>();
                    var mini = math.min(Length, other.Length);
                    for(var i = 0; i < mini; ++i)
                    {
                        var j = UnsafeUtility.MemCmp(aa + sizeof(T) * i, bb + sizeof(T) * i, sizeof(T));
                        if(j != 0)
                            return j;
                    }
                    return Length.CompareTo(other.Length);
                }
            }
        }

        /// <summary>
        /// Returns true if this list and another list are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="other">The list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public bool Equals(FixedList128Bytes<T> other)
        {
            return CompareTo(other) == 0;
        }


        /// <summary>
        /// Initializes and returns an instance of FixedList32Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList32Bytes&lt;T&gt;.</exception>
        public FixedList32Bytes(in FixedList128Bytes<T> other)
        {
            this = default;
            var error = Initialize(other);
            if(error != 0)
                FixedList.CheckResize<FixedBytes30,T>(other.Length);
        }

        /// <summary>
        /// Initializes an instance of FixedList32Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>zero on success, or non-zero on error.</returns>
        internal int Initialize(in FixedList128Bytes<T> other)
        {
            if(other.Length > Capacity)
                return (int)CopyError.Truncation;
            length = other.length;
            buffer = new FixedBytes30();
            unsafe
            {
                UnsafeUtility.MemCpy(Buffer, other.Buffer, LengthInBytes);
            }
            return 0;
        }

        /// <summary>
        /// Returns a new list that is a copy of another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>A new list that is a copy of the other.</returns>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList32Bytes&lt;T&gt;.</exception>
        public static implicit operator FixedList32Bytes<T>(in FixedList128Bytes<T> other)
        {
            return new FixedList32Bytes<T>(other);
        }

        /// <summary>
        /// Returns true if two lists are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for equality.</param>
        /// <param name="b">The second list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public static bool operator ==(in FixedList32Bytes<T> a, in FixedList512Bytes<T> b)
        {
            unsafe
            {
                if(a.length != b.length)
                    return false;
                return UnsafeUtility.MemCmp(a.Buffer, b.Buffer, a.LengthInBytes) == 0;
            }
        }

        /// <summary>
        /// Returns true if two lists are unequal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for inequality.</param>
        /// <param name="b">The second list to compare for inequality.</param>
        /// <returns>True if the two lists are unequal.</returns>
        public static bool operator !=(in FixedList32Bytes<T> a, in FixedList512Bytes<T> b)
        {
            return !(a == b);
        }

        /// <summary>
        /// Returns a number denoting whether this list should be placed before or after another list in a sort.
        /// </summary>
        /// <param name="other">A list to to compare with.</param>
        /// <returns>An integer denoting the respective sort position of the list relative to the other:
        ///
        /// 0 denotes that both lists should have the same position in a sort.
        /// -1 denotes that this list should precede the other list in a sort.
        /// +1 denotes that this list should follow the other list in a sort.
        /// </returns>
        public int CompareTo(FixedList512Bytes<T> other)
        {
            unsafe
            {
                fixed(byte* a = &buffer.offset0000.byte0000)
                {
                    byte* b = &other.buffer.offset0000.byte0000;
                    var aa = a + FixedList.PaddingBytes<T>();
                    var bb = b + FixedList.PaddingBytes<T>();
                    var mini = math.min(Length, other.Length);
                    for(var i = 0; i < mini; ++i)
                    {
                        var j = UnsafeUtility.MemCmp(aa + sizeof(T) * i, bb + sizeof(T) * i, sizeof(T));
                        if(j != 0)
                            return j;
                    }
                    return Length.CompareTo(other.Length);
                }
            }
        }

        /// <summary>
        /// Returns true if this list and another list are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="other">The list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public bool Equals(FixedList512Bytes<T> other)
        {
            return CompareTo(other) == 0;
        }


        /// <summary>
        /// Initializes and returns an instance of FixedList32Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList32Bytes&lt;T&gt;.</exception>
        public FixedList32Bytes(in FixedList512Bytes<T> other)
        {
            this = default;
            var error = Initialize(other);
            if(error != 0)
                FixedList.CheckResize<FixedBytes30,T>(other.Length);
        }

        /// <summary>
        /// Initializes an instance of FixedList32Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>zero on success, or non-zero on error.</returns>
        internal int Initialize(in FixedList512Bytes<T> other)
        {
            if(other.Length > Capacity)
                return (int)CopyError.Truncation;
            length = other.length;
            buffer = new FixedBytes30();
            unsafe
            {
                UnsafeUtility.MemCpy(Buffer, other.Buffer, LengthInBytes);
            }
            return 0;
        }

        /// <summary>
        /// Returns a new list that is a copy of another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>A new list that is a copy of the other.</returns>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList32Bytes&lt;T&gt;.</exception>
        public static implicit operator FixedList32Bytes<T>(in FixedList512Bytes<T> other)
        {
            return new FixedList32Bytes<T>(other);
        }

        /// <summary>
        /// Returns true if two lists are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for equality.</param>
        /// <param name="b">The second list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public static bool operator ==(in FixedList32Bytes<T> a, in FixedList4096Bytes<T> b)
        {
            unsafe
            {
                if(a.length != b.length)
                    return false;
                return UnsafeUtility.MemCmp(a.Buffer, b.Buffer, a.LengthInBytes) == 0;
            }
        }

        /// <summary>
        /// Returns true if two lists are unequal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for inequality.</param>
        /// <param name="b">The second list to compare for inequality.</param>
        /// <returns>True if the two lists are unequal.</returns>
        public static bool operator !=(in FixedList32Bytes<T> a, in FixedList4096Bytes<T> b)
        {
            return !(a == b);
        }

        /// <summary>
        /// Returns a number denoting whether this list should be placed before or after another list in a sort.
        /// </summary>
        /// <param name="other">A list to to compare with.</param>
        /// <returns>An integer denoting the respective sort position of the list relative to the other:
        ///
        /// 0 denotes that both lists should have the same position in a sort.
        /// -1 denotes that this list should precede the other list in a sort.
        /// +1 denotes that this list should follow the other list in a sort.
        /// </returns>
        public int CompareTo(FixedList4096Bytes<T> other)
        {
            unsafe
            {
                fixed(byte* a = &buffer.offset0000.byte0000)
                {
                    byte* b = &other.buffer.offset0000.byte0000;
                    var aa = a + FixedList.PaddingBytes<T>();
                    var bb = b + FixedList.PaddingBytes<T>();
                    var mini = math.min(Length, other.Length);
                    for(var i = 0; i < mini; ++i)
                    {
                        var j = UnsafeUtility.MemCmp(aa + sizeof(T) * i, bb + sizeof(T) * i, sizeof(T));
                        if(j != 0)
                            return j;
                    }
                    return Length.CompareTo(other.Length);
                }
            }
        }

        /// <summary>
        /// Returns true if this list and another list are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="other">The list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public bool Equals(FixedList4096Bytes<T> other)
        {
            return CompareTo(other) == 0;
        }


        /// <summary>
        /// Initializes and returns an instance of FixedList32Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList32Bytes&lt;T&gt;.</exception>
        public FixedList32Bytes(in FixedList4096Bytes<T> other)
        {
            this = default;
            var error = Initialize(other);
            if(error != 0)
                FixedList.CheckResize<FixedBytes30,T>(other.Length);
        }

        /// <summary>
        /// Initializes an instance of FixedList32Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>zero on success, or non-zero on error.</returns>
        internal int Initialize(in FixedList4096Bytes<T> other)
        {
            if(other.Length > Capacity)
                return (int)CopyError.Truncation;
            length = other.length;
            buffer = new FixedBytes30();
            unsafe
            {
                UnsafeUtility.MemCpy(Buffer, other.Buffer, LengthInBytes);
            }
            return 0;
        }

        /// <summary>
        /// Returns a new list that is a copy of another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>A new list that is a copy of the other.</returns>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList32Bytes&lt;T&gt;.</exception>
        public static implicit operator FixedList32Bytes<T>(in FixedList4096Bytes<T> other)
        {
            return new FixedList32Bytes<T>(other);
        }

        /// <summary>
        /// Returns true if the list is equal to an object.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.
        ///
        /// A FixedList*N*&lt;T&gt; can only be equal to another FixedList*N*&lt;T&gt; with the same *N* and T.
        /// </remarks>
        /// <param name="obj">An object to compare for equality.</param>
        /// <returns>True if the list is equal to the object.</returns>
        [NotBurstCompatible]
        public override bool Equals(object obj)
        {
            if(obj is FixedList32Bytes<T> aFixedList32Bytes) return Equals(aFixedList32Bytes);
            if(obj is FixedList64Bytes<T> aFixedList64Bytes) return Equals(aFixedList64Bytes);
            if(obj is FixedList128Bytes<T> aFixedList128Bytes) return Equals(aFixedList128Bytes);
            if(obj is FixedList512Bytes<T> aFixedList512Bytes) return Equals(aFixedList512Bytes);
            if(obj is FixedList4096Bytes<T> aFixedList4096Bytes) return Equals(aFixedList4096Bytes);
            return false;
        }

        /// <summary>
        /// An enumerator over the elements of a FixedList32Bytes&lt;T&gt;.
        /// </summary>
        /// <remarks>
        /// In an enumerator's initial state, `Current` cannot be read. The first <see cref="MoveNext"/> call advances the enumerator to the first element.
        /// </remarks>
        public struct Enumerator : IEnumerator<T>
        {
            FixedList32Bytes<T> m_List;
            int m_Index;

            /// <summary>
            /// Initializes and returns an instance of FixedList32Bytes&lt;T&gt;.
            /// </summary>
            /// <param name="list">The list for which to create an enumerator.</param>
            public Enumerator(ref FixedList32Bytes<T> list)
            {
                m_List = list;
                m_Index = -1;
            }

            /// <summary>
            /// Does nothing.
            /// </summary>
            public void Dispose()
            {
            }

            /// <summary>
            /// Advances the enumerator to the next element.
            /// </summary>
            /// <returns>True if <see cref="Current"/> is valid to read after the call.</returns>
            public bool MoveNext()
            {
                m_Index++;
                return m_Index < m_List.Length;
            }

            /// <summary>
            /// Resets the enumerator to its initial state.
            /// </summary>
            public void Reset()
            {
                m_Index = -1;
            }

            /// <summary>
            /// The current element.
            /// </summary>
            /// <value>The current element.</value>
            public T Current => m_List[m_Index]; // Let FixedList32Bytes<T> indexer check for out of range.

            object IEnumerator.Current => Current;
        }

        /// <summary>
        /// Returns an enumerator for iterating over the elements of this list.
        /// </summary>
        /// <returns>An enumerator for iterating over the elements of this list.</returns>
        public Enumerator GetEnumerator()
        {
            return new Enumerator(ref this);
        }

        /// <summary>
        /// This method is not implemented. Use <see cref="GetEnumerator"/> instead.
        /// </summary>
        /// <returns>Nothing because it always throws <see cref="NotImplementedException"/>.</returns>
        /// <exception cref="NotImplementedException">Method is not implemented.</exception>
        IEnumerator IEnumerable.GetEnumerator()
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// This method is not implemented. Use <see cref="GetEnumerator"/> instead.
        /// </summary>
        /// <returns>Nothing because it always throws <see cref="NotImplementedException"/>.</returns>
        /// <exception cref="NotImplementedException">Method is not implemented.</exception>
        IEnumerator<T> IEnumerable<T>.GetEnumerator()
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Provides extension methods for FixedList32Bytes.
    /// </summary>
    [BurstCompatible]
    public unsafe static class FixedList32BytesExtensions
    {
        /// <summary>
        /// Finds the index of the first occurrence of a particular value in this list.
        /// </summary>
        /// <typeparam name="T">The type of elements in this list.</typeparam>
        /// <typeparam name="U">The value type.</typeparam>
        /// <param name="list">The list to search.</param>
        /// <param name="value">The value to locate.</param>
        /// <returns>The index of the first occurrence of the value. Returns -1 if no occurrence is found.</returns>
        [BurstCompatible(GenericTypeArguments = new [] { typeof(int), typeof(int) })]
        public static int IndexOf<T, U>(this ref FixedList32Bytes<T> list, U value) where T : unmanaged, IEquatable<U>
        {
            return NativeArrayExtensions.IndexOf<T, U>(list.Buffer, list.Length, value);
        }

        /// <summary>
        /// Returns true if a particular value is present in this list.
        /// </summary>
        /// <typeparam name="T">The type of elements in this list.</typeparam>
        /// <typeparam name="U">The value type.</typeparam>
        /// <param name="list">The list to search.</param>
        /// <param name="value">The value to locate.</param>
        /// <returns>True if the value is present in this list.</returns>
        [BurstCompatible(GenericTypeArguments = new [] { typeof(int), typeof(int) })]
        public static bool Contains<T, U>(this ref FixedList32Bytes<T> list, U value) where T : unmanaged, IEquatable<U>
        {
            return list.IndexOf(value) != -1;
        }

        /// <summary>
        /// Removes the first occurrence of a particular value in this list.
        /// </summary>
        /// <remarks>
        /// If a value is removed, all elements after it are shifted down by one, and the list's length is decremented by one.
        ///
        /// If you don't need to preserve the order of the remaining elements, <see cref="Unity.Collections.FixedList32BytesExtensions.RemoveSwapBack{T, U}"/> is a cheaper alternative.
        /// </remarks>
        /// <typeparam name="T">The type of elements in this list.</typeparam>
        /// <typeparam name="U">The value type.</typeparam>
        /// <param name="list">The list to search.</param>
        /// <param name="value">The value to locate and remove.</param>
        /// <returns>True if the value was found and removed.</returns>
        [BurstCompatible(GenericTypeArguments = new [] { typeof(int), typeof(int) })]
        public static bool Remove<T, U>(this ref FixedList32Bytes<T> list, U value) where T : unmanaged, IEquatable<U>
        {
            int index = list.IndexOf(value);
            if (index < 0)
            {
                return false;
            }

            list.RemoveAt(index);

            return true;
        }

        /// <summary>
        /// Removes the first occurrence of a particular value in this list.
        /// </summary>
        /// <remarks>
        /// If a value is removed, the last element of the list is copied to overwrite the removed value, and the list's length is decremented by one.
        ///
        /// This is cheaper than <see cref="Remove"/>, but the order of the remaining elements is not preserved.
        /// </remarks>
        /// <typeparam name="T">The type of elements in this list.</typeparam>
        /// <typeparam name="U">The value type.</typeparam>
        /// <param name="list">The list to search.</param>
        /// <param name="value">The value to locate and remove.</param>
        /// <returns>Returns true if the item is removed.</returns>
        [BurstCompatible(GenericTypeArguments = new [] { typeof(int), typeof(int) })]
        public static bool RemoveSwapBack<T, U>(this ref FixedList32Bytes<T> list, U value) where T : unmanaged, IEquatable<U>
        {
            var index = list.IndexOf(value);
            if (index == -1)
            {
                return false;
            }

            list.RemoveAtSwapBack(index);

            return true;
        }
    }

    sealed class FixedList32BytesDebugView<T> where T : unmanaged
    {
        FixedList32Bytes<T> m_List;
        public FixedList32BytesDebugView(FixedList32Bytes<T> list)
        {
            m_List = list;
        }
        public T[] Items => m_List.ToArray();
    }
    [Obsolete("Renamed to FixedList64Bytes<T> (UnityUpgradable) -> FixedList64Bytes<T>", true)]
    public struct FixedList64<T> where T : unmanaged {}

    /// <summary>
    /// An unmanaged, resizable list whose content is all stored directly in the 64-byte struct. Useful for small lists.
    /// </summary>
    /// <typeparam name="T">The type of the elements.</typeparam>
    [Serializable]
    [DebuggerTypeProxy(typeof(FixedList64BytesDebugView<>))]
    [BurstCompatible(GenericTypeArguments = new [] { typeof(int) })]
    public struct FixedList64Bytes<T>
    : INativeList<T>
    , IEnumerable<T> // Used by collection initializers.
    , IEquatable<FixedList32Bytes<T>>
    , IComparable<FixedList32Bytes<T>>
    , IEquatable<FixedList64Bytes<T>>
    , IComparable<FixedList64Bytes<T>>
    , IEquatable<FixedList128Bytes<T>>
    , IComparable<FixedList128Bytes<T>>
    , IEquatable<FixedList512Bytes<T>>
    , IComparable<FixedList512Bytes<T>>
    , IEquatable<FixedList4096Bytes<T>>
    , IComparable<FixedList4096Bytes<T>>
    where T : unmanaged
    {
        [SerializeField] internal ushort length;
        [SerializeField] internal FixedBytes62 buffer;

        /// <summary>
        /// The current number of items in this list.
        /// </summary>
        /// <value>The current number of items in this list.</value>
        [CreateProperty]
        public int Length
        {
            get => length;
            set
            {
                FixedList.CheckResize<FixedBytes62,T>(value);
                length = (ushort)value;
            }
        }

        /// <summary>
        /// A property in order to display items in the Entity Inspector.
        /// </summary>
        [CreateProperty] IEnumerable<T> Elements => this.ToArray();

        /// <summary>
        /// Whether this list is empty.
        /// </summary>
        /// <value>True if this string has no characters or if the container has not been constructed.</value>
        public bool IsEmpty => Length == 0;

        internal int LengthInBytes => Length * UnsafeUtility.SizeOf<T>();

        unsafe internal byte* Buffer
        {
            get
            {
                fixed(byte* b = &buffer.offset0000.byte0000)
                    return b + FixedList.PaddingBytes<T>();
            }
        }

        /// <summary>
        /// The number of elements that can fit in this list.
        /// </summary>
        /// <value>The number of elements that can fit in this list.</value>
        /// <remarks>The capacity of a FixedList cannot be changed. The setter is included only for conformity with <see cref="INativeList{T}"/>.</remarks>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the new value does not match the current capacity.</exception>
        public int Capacity
        {
          get
          {
              return FixedList.Capacity<FixedBytes62,T>();
          }

          set
          {
              CollectionHelper.CheckCapacityInRange(value, Length);
          }
        }

        /// <summary>
        /// The element at a given index.
        /// </summary>
        /// <param name="index">An index.</param>
        /// <value>The value to store at the index.</value>
        /// <exception cref="IndexOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public T this[int index]
        {
            get
            {
                CollectionHelper.CheckIndexInRange(index, length);
                unsafe
                {
                    return UnsafeUtility.ReadArrayElement<T>(Buffer, CollectionHelper.AssumePositive(index));
                }
            }

            set
            {
                CollectionHelper.CheckIndexInRange(index, length);
                unsafe
                {
                    UnsafeUtility.WriteArrayElement<T>(Buffer, CollectionHelper.AssumePositive(index), value);
                }
            }
        }

        /// <summary>
        /// Returns the element at a given index.
        /// </summary>
        /// <param name="index">An index.</param>
        /// <returns>The list element at the index.</returns>
        public ref T ElementAt(int index)
        {
            CollectionHelper.CheckIndexInRange(index, length);
            unsafe
            {
                return ref UnsafeUtility.ArrayElementAsRef<T>(Buffer, index);
            }
        }

        /// <summary>
        /// Returns the hash code of this list.
        /// </summary>
        /// <remarks>
        /// Only the content of the list (the bytes of the elements) are included in the hash. Any bytes beyond the length are not part of the hash.</remarks>
        /// <returns>The hash code of this list.</returns>
        public override int GetHashCode()
        {
            unsafe
            {
                return (int)CollectionHelper.Hash(Buffer, LengthInBytes);
            }
        }

        /// <summary>
        /// Appends an element to the end of this list. Increments the length by 1.
        /// </summary>
        /// <remarks>The same as <see cref="AddNoResize"/>. Remember that a fixed list is never resized.</remarks>
        /// <param name="item">The element to append at the end of the list.</param>
        /// <exception cref="IndexOutOfRangeException">Thrown if the append exceeds the capacity.</exception>
        public void Add(in T item)
        {
            this[Length++] = item;
        }

        /// <summary>
        /// Appends elements from a buffer to the end of this list. Increments the length by the number of appended elements.
        /// </summary>
        /// <remarks>The same as <see cref="AddRangeNoResize"/>. Remember that a fixed list is never resized.</remarks>
        /// <param name="ptr">A buffer.</param>
        /// <param name="length">The number of elements from the buffer to append.</param>
        /// <exception cref="IndexOutOfRangeException">Thrown if the append exceeds the capacity.</exception>
        public unsafe void AddRange(void* ptr, int length)
        {
            T* data = (T*)ptr;
            for (var i = 0; i < length; ++i)
            {
                this[Length++] = data[i];
            }
        }

        /// <summary>
        /// Appends an element to the end of this list. Increments the length by 1.
        /// </summary>
        /// <remarks>The same as <see cref="Add"/>. Included only for consistency with the other list types.</remarks>
        /// <param name="item">The element to append at the end of the list.</param>
        /// <exception cref="IndexOutOfRangeException">Thrown if the append exceeds the capacity.</exception>
        public void AddNoResize(in T item) => Add(item);

        /// <summary>
        /// Appends elements from a buffer to the end of this list. Increments the length by the number of appended elements.
        /// </summary>
        /// <remarks>The same as <see cref="AddRange"/>. Included only for consistency with the other list types.</remarks>
        /// <param name="ptr">A buffer.</param>
        /// <param name="length">The number of elements from the buffer to append.</param>
        /// <exception cref="IndexOutOfRangeException">Thrown if the append exceeds the capacity.</exception>
        public unsafe void AddRangeNoResize(void* ptr, int length) => AddRange(ptr, length);

        /// <summary>
        /// Sets the length to 0.
        /// </summary>
        /// <remarks> Does *not* zero out the bytes.</remarks>
        public void Clear()
        {
            Length = 0;
        }

        /// <summary>
        /// Shifts elements toward the end of this list, increasing its length.
        /// </summary>
        /// <remarks>
        /// Right-shifts elements in the list so as to create 'free' slots at the beginning or in the middle.
        ///
        /// The length is increased by `end - begin`.
        ///
        /// If `end` equals `begin`, the method does nothing.
        ///
        /// The element at index `begin` will be copied to index `end`, the element at index `begin + 1` will be copied to `end + 1`, and so forth.
        ///
        /// The indexes `begin` up to `end` are not cleared: they will contain whatever values they held prior.
        /// </remarks>
        /// <param name="begin">The index of the first element that will be shifted up.</param>
        /// <param name="end">The index where the first shifted element will end up.</param>
        /// <exception cref="IndexOutOfRangeException">Thrown if the new length exceeds the capacity.</exception>
        public void InsertRangeWithBeginEnd(int begin, int end)
        {
            int items = end - begin;
            if(items < 1)
                return;
            int itemsToCopy = length - begin;
            Length += items;
            if(itemsToCopy < 1)
                return;
            int bytesToCopy = itemsToCopy * UnsafeUtility.SizeOf<T>();
            unsafe
            {
                byte *b = Buffer;
                byte *dest = b + end * UnsafeUtility.SizeOf<T>();
                byte *src = b + begin * UnsafeUtility.SizeOf<T>();
                UnsafeUtility.MemMove(dest, src, bytesToCopy);
            }
        }

        /// <summary>
        /// Inserts a single element at an index. Increments the length by 1.
        /// </summary>
        /// <param name="index">The index at which to insert the element.</param>
        /// <param name="item">The element to insert.</param>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public void Insert(int index, in T item)
        {
            InsertRangeWithBeginEnd(index, index+1);
            this[index] = item;
        }

        /// <summary>
        /// Copies the last element of this list to an index. Decrements the length by 1.
        /// </summary>
        /// <remarks>Useful as a cheap way to remove elements from a list when you don't care about preserving order.</remarks>
        /// <param name="index">The index to overwrite with the last element.</param>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public void RemoveAtSwapBack(int index)
        {
            RemoveRangeSwapBack(index, 1);
        }

        /// <summary>
        /// Copies the last *N* elements of this list to a range in this list. Decrements the length by *N*.
        /// </summary>
        /// <remarks>
        /// Copies the last `count`-numbered elements to the range starting at `index`.
        ///
        /// Useful as a cheap way to remove elements from a list when you don't care about preserving order.
        ///
        /// Does nothing if the count is less than 1.
        /// </remarks>
        /// <param name="index">The first index of the destination range.</param>
        /// <param name="count">The number of elements to copy and the amount by which to decrement the length.</param>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public void RemoveRangeSwapBack(int index, int count)
        {
            if (count > 0)
            {
                int copyFrom = math.max(Length - count, index + count);

                unsafe
                {
                    var sizeOf = UnsafeUtility.SizeOf<T>();
                    void* dst = Buffer + index * sizeOf;
                    void* src = Buffer + copyFrom * sizeOf;
                    UnsafeUtility.MemCpy(dst, src, (Length - copyFrom) * sizeOf);
                }

                Length -= count;
            }
        }

        /// <summary>
        /// Copies the last *N* elements of this list to a range in this list. Decrements the length by *N*.
        /// </summary>
        /// <param name="begin">The first index of the item to remove.</param>
        /// <param name="end">The index past-the-last item to remove.</param>
        /// <exception cref="ArgumentException">Thrown if end argument is less than begin argument.</exception>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if begin or end arguments are not positive or out of bounds.</exception>
        [Obsolete("RemoveRangeSwapBackWithBeginEnd(begin, end) is deprecated, use RemoveRangeSwapBack(index, count) instead. (RemovedAfter 2021-06-02)", false)]
        public void RemoveRangeSwapBackWithBeginEnd(int begin, int end) => RemoveRangeSwapBack(begin, end - begin);

        /// <summary>
        /// Removes the element at an index. Shifts everything above the index down by one and decrements the length by 1.
        /// </summary>
        /// <param name="index">The index of the element to remove.</param>
        /// <remarks>
        /// If you don't care about preserving the order of the elements, `RemoveAtSwapBack` is a more efficient way to remove an element.
        /// </remarks>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public void RemoveAt(int index)
        {
            RemoveRange(index, 1);
        }

        /// <summary>
        /// Removes *N* elements of a range. Shifts everything above the range down by *N* and decrements the length by *N*.
        /// </summary>
        /// <remarks>
        /// If you don't care about preserving the order of the elements, `RemoveAtSwapBack` is a more efficient way to remove elements.
        /// </remarks>
        /// <param name="index">The first index of the range to remove.</param>
        /// <param name="count">The number of elements to remove.</param>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public void RemoveRange(int index, int count)
        {
            if (count > 0)
            {
                int copyFrom = math.min(index + count, Length);

                unsafe
                {
                    var sizeOf = UnsafeUtility.SizeOf<T>();
                    void* dst = Buffer + index * sizeOf;
                    void* src = Buffer + copyFrom * sizeOf;
                    UnsafeUtility.MemCpy(dst, src, (Length - copyFrom) * sizeOf);
                }

                Length -= count;
            }
        }

        /// <summary>
        /// Removes *N* elements of a range. Shifts everything above the range down by *N* and decrements the length by *N*.
        /// </summary>
        /// <param name="begin">The first index of the item to remove.</param>
        /// <param name="end">The index past-the-last item to remove.</param>
        /// <remarks>
        /// This method of removing item(s) is useful only in case when list is ordered and user wants to preserve order
        /// in list after removal In majority of cases is not important and user should use more performant `RemoveRangeSwapBackWithBeginEnd`.
        /// </remarks>
        /// <exception cref="ArgumentException">Thrown if end argument is less than begin argument.</exception>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if begin or end arguments are not positive or out of bounds.</exception>
        [Obsolete("RemoveRangeWithBeginEnd(begin, end) is deprecated, use RemoveRange(index, count) instead. (RemovedAfter 2021-06-02)", false)]
        public void RemoveRangeWithBeginEnd(int begin, int end) => RemoveRange(begin, end - begin);

        /// <summary>
        /// Returns a managed array that is a copy of this list.
        /// </summary>
        /// <returns>A managed array that is a copy of this list.</returns>
        [NotBurstCompatible]
        public T[] ToArray()
        {
            var result = new T[Length];
            unsafe
            {
                byte* s = Buffer;
                fixed(T* d = result)
                    UnsafeUtility.MemCpy(d, s, LengthInBytes);
            }
            return result;
        }

        /// <summary>
        /// Returns an array that is a copy of this list.
        /// </summary>
        /// <param name="allocator">The allocator to use.</param>
        /// <returns>An array that is a copy of this list.</returns>
        public NativeArray<T> ToNativeArray(AllocatorManager.AllocatorHandle allocator)
        {
            unsafe
            {
                var copy = CollectionHelper.CreateNativeArray<T>(Length, allocator, NativeArrayOptions.UninitializedMemory);
                UnsafeUtility.MemCpy(copy.GetUnsafePtr(), Buffer, LengthInBytes);
                return copy;
            }
        }


        /// <summary>
        /// Returns true if two lists are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for equality.</param>
        /// <param name="b">The second list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public static bool operator ==(in FixedList64Bytes<T> a, in FixedList32Bytes<T> b)
        {
            unsafe
            {
                if(a.length != b.length)
                    return false;
                return UnsafeUtility.MemCmp(a.Buffer, b.Buffer, a.LengthInBytes) == 0;
            }
        }

        /// <summary>
        /// Returns true if two lists are unequal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for inequality.</param>
        /// <param name="b">The second list to compare for inequality.</param>
        /// <returns>True if the two lists are unequal.</returns>
        public static bool operator !=(in FixedList64Bytes<T> a, in FixedList32Bytes<T> b)
        {
            return !(a == b);
        }

        /// <summary>
        /// Returns a number denoting whether this list should be placed before or after another list in a sort.
        /// </summary>
        /// <param name="other">A list to to compare with.</param>
        /// <returns>An integer denoting the respective sort position of the list relative to the other:
        ///
        /// 0 denotes that both lists should have the same position in a sort.
        /// -1 denotes that this list should precede the other list in a sort.
        /// +1 denotes that this list should follow the other list in a sort.
        /// </returns>
        public int CompareTo(FixedList32Bytes<T> other)
        {
            unsafe
            {
                fixed(byte* a = &buffer.offset0000.byte0000)
                {
                    byte* b = &other.buffer.offset0000.byte0000;
                    var aa = a + FixedList.PaddingBytes<T>();
                    var bb = b + FixedList.PaddingBytes<T>();
                    var mini = math.min(Length, other.Length);
                    for(var i = 0; i < mini; ++i)
                    {
                        var j = UnsafeUtility.MemCmp(aa + sizeof(T) * i, bb + sizeof(T) * i, sizeof(T));
                        if(j != 0)
                            return j;
                    }
                    return Length.CompareTo(other.Length);
                }
            }
        }

        /// <summary>
        /// Returns true if this list and another list are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="other">The list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public bool Equals(FixedList32Bytes<T> other)
        {
            return CompareTo(other) == 0;
        }


        /// <summary>
        /// Initializes and returns an instance of FixedList64Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList64Bytes&lt;T&gt;.</exception>
        public FixedList64Bytes(in FixedList32Bytes<T> other)
        {
            this = default;
            var error = Initialize(other);
            if(error != 0)
                FixedList.CheckResize<FixedBytes62,T>(other.Length);
        }

        /// <summary>
        /// Initializes an instance of FixedList64Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>zero on success, or non-zero on error.</returns>
        internal int Initialize(in FixedList32Bytes<T> other)
        {
            if(other.Length > Capacity)
                return (int)CopyError.Truncation;
            length = other.length;
            buffer = new FixedBytes62();
            unsafe
            {
                UnsafeUtility.MemCpy(Buffer, other.Buffer, LengthInBytes);
            }
            return 0;
        }

        /// <summary>
        /// Returns a new list that is a copy of another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>A new list that is a copy of the other.</returns>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList64Bytes&lt;T&gt;.</exception>
        public static implicit operator FixedList64Bytes<T>(in FixedList32Bytes<T> other)
        {
            return new FixedList64Bytes<T>(other);
        }

        /// <summary>
        /// Returns true if two lists are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for equality.</param>
        /// <param name="b">The second list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public static bool operator ==(in FixedList64Bytes<T> a, in FixedList64Bytes<T> b)
        {
            unsafe
            {
                if(a.length != b.length)
                    return false;
                return UnsafeUtility.MemCmp(a.Buffer, b.Buffer, a.LengthInBytes) == 0;
            }
        }

        /// <summary>
        /// Returns true if two lists are unequal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for inequality.</param>
        /// <param name="b">The second list to compare for inequality.</param>
        /// <returns>True if the two lists are unequal.</returns>
        public static bool operator !=(in FixedList64Bytes<T> a, in FixedList64Bytes<T> b)
        {
            return !(a == b);
        }

        /// <summary>
        /// Returns a number denoting whether this list should be placed before or after another list in a sort.
        /// </summary>
        /// <param name="other">A list to to compare with.</param>
        /// <returns>An integer denoting the respective sort position of the list relative to the other:
        ///
        /// 0 denotes that both lists should have the same position in a sort.
        /// -1 denotes that this list should precede the other list in a sort.
        /// +1 denotes that this list should follow the other list in a sort.
        /// </returns>
        public int CompareTo(FixedList64Bytes<T> other)
        {
            unsafe
            {
                fixed(byte* a = &buffer.offset0000.byte0000)
                {
                    byte* b = &other.buffer.offset0000.byte0000;
                    var aa = a + FixedList.PaddingBytes<T>();
                    var bb = b + FixedList.PaddingBytes<T>();
                    var mini = math.min(Length, other.Length);
                    for(var i = 0; i < mini; ++i)
                    {
                        var j = UnsafeUtility.MemCmp(aa + sizeof(T) * i, bb + sizeof(T) * i, sizeof(T));
                        if(j != 0)
                            return j;
                    }
                    return Length.CompareTo(other.Length);
                }
            }
        }

        /// <summary>
        /// Returns true if this list and another list are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="other">The list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public bool Equals(FixedList64Bytes<T> other)
        {
            return CompareTo(other) == 0;
        }


        /// <summary>
        /// Returns true if two lists are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for equality.</param>
        /// <param name="b">The second list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public static bool operator ==(in FixedList64Bytes<T> a, in FixedList128Bytes<T> b)
        {
            unsafe
            {
                if(a.length != b.length)
                    return false;
                return UnsafeUtility.MemCmp(a.Buffer, b.Buffer, a.LengthInBytes) == 0;
            }
        }

        /// <summary>
        /// Returns true if two lists are unequal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for inequality.</param>
        /// <param name="b">The second list to compare for inequality.</param>
        /// <returns>True if the two lists are unequal.</returns>
        public static bool operator !=(in FixedList64Bytes<T> a, in FixedList128Bytes<T> b)
        {
            return !(a == b);
        }

        /// <summary>
        /// Returns a number denoting whether this list should be placed before or after another list in a sort.
        /// </summary>
        /// <param name="other">A list to to compare with.</param>
        /// <returns>An integer denoting the respective sort position of the list relative to the other:
        ///
        /// 0 denotes that both lists should have the same position in a sort.
        /// -1 denotes that this list should precede the other list in a sort.
        /// +1 denotes that this list should follow the other list in a sort.
        /// </returns>
        public int CompareTo(FixedList128Bytes<T> other)
        {
            unsafe
            {
                fixed(byte* a = &buffer.offset0000.byte0000)
                {
                    byte* b = &other.buffer.offset0000.byte0000;
                    var aa = a + FixedList.PaddingBytes<T>();
                    var bb = b + FixedList.PaddingBytes<T>();
                    var mini = math.min(Length, other.Length);
                    for(var i = 0; i < mini; ++i)
                    {
                        var j = UnsafeUtility.MemCmp(aa + sizeof(T) * i, bb + sizeof(T) * i, sizeof(T));
                        if(j != 0)
                            return j;
                    }
                    return Length.CompareTo(other.Length);
                }
            }
        }

        /// <summary>
        /// Returns true if this list and another list are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="other">The list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public bool Equals(FixedList128Bytes<T> other)
        {
            return CompareTo(other) == 0;
        }


        /// <summary>
        /// Initializes and returns an instance of FixedList64Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList64Bytes&lt;T&gt;.</exception>
        public FixedList64Bytes(in FixedList128Bytes<T> other)
        {
            this = default;
            var error = Initialize(other);
            if(error != 0)
                FixedList.CheckResize<FixedBytes62,T>(other.Length);
        }

        /// <summary>
        /// Initializes an instance of FixedList64Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>zero on success, or non-zero on error.</returns>
        internal int Initialize(in FixedList128Bytes<T> other)
        {
            if(other.Length > Capacity)
                return (int)CopyError.Truncation;
            length = other.length;
            buffer = new FixedBytes62();
            unsafe
            {
                UnsafeUtility.MemCpy(Buffer, other.Buffer, LengthInBytes);
            }
            return 0;
        }

        /// <summary>
        /// Returns a new list that is a copy of another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>A new list that is a copy of the other.</returns>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList64Bytes&lt;T&gt;.</exception>
        public static implicit operator FixedList64Bytes<T>(in FixedList128Bytes<T> other)
        {
            return new FixedList64Bytes<T>(other);
        }

        /// <summary>
        /// Returns true if two lists are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for equality.</param>
        /// <param name="b">The second list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public static bool operator ==(in FixedList64Bytes<T> a, in FixedList512Bytes<T> b)
        {
            unsafe
            {
                if(a.length != b.length)
                    return false;
                return UnsafeUtility.MemCmp(a.Buffer, b.Buffer, a.LengthInBytes) == 0;
            }
        }

        /// <summary>
        /// Returns true if two lists are unequal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for inequality.</param>
        /// <param name="b">The second list to compare for inequality.</param>
        /// <returns>True if the two lists are unequal.</returns>
        public static bool operator !=(in FixedList64Bytes<T> a, in FixedList512Bytes<T> b)
        {
            return !(a == b);
        }

        /// <summary>
        /// Returns a number denoting whether this list should be placed before or after another list in a sort.
        /// </summary>
        /// <param name="other">A list to to compare with.</param>
        /// <returns>An integer denoting the respective sort position of the list relative to the other:
        ///
        /// 0 denotes that both lists should have the same position in a sort.
        /// -1 denotes that this list should precede the other list in a sort.
        /// +1 denotes that this list should follow the other list in a sort.
        /// </returns>
        public int CompareTo(FixedList512Bytes<T> other)
        {
            unsafe
            {
                fixed(byte* a = &buffer.offset0000.byte0000)
                {
                    byte* b = &other.buffer.offset0000.byte0000;
                    var aa = a + FixedList.PaddingBytes<T>();
                    var bb = b + FixedList.PaddingBytes<T>();
                    var mini = math.min(Length, other.Length);
                    for(var i = 0; i < mini; ++i)
                    {
                        var j = UnsafeUtility.MemCmp(aa + sizeof(T) * i, bb + sizeof(T) * i, sizeof(T));
                        if(j != 0)
                            return j;
                    }
                    return Length.CompareTo(other.Length);
                }
            }
        }

        /// <summary>
        /// Returns true if this list and another list are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="other">The list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public bool Equals(FixedList512Bytes<T> other)
        {
            return CompareTo(other) == 0;
        }


        /// <summary>
        /// Initializes and returns an instance of FixedList64Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList64Bytes&lt;T&gt;.</exception>
        public FixedList64Bytes(in FixedList512Bytes<T> other)
        {
            this = default;
            var error = Initialize(other);
            if(error != 0)
                FixedList.CheckResize<FixedBytes62,T>(other.Length);
        }

        /// <summary>
        /// Initializes an instance of FixedList64Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>zero on success, or non-zero on error.</returns>
        internal int Initialize(in FixedList512Bytes<T> other)
        {
            if(other.Length > Capacity)
                return (int)CopyError.Truncation;
            length = other.length;
            buffer = new FixedBytes62();
            unsafe
            {
                UnsafeUtility.MemCpy(Buffer, other.Buffer, LengthInBytes);
            }
            return 0;
        }

        /// <summary>
        /// Returns a new list that is a copy of another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>A new list that is a copy of the other.</returns>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList64Bytes&lt;T&gt;.</exception>
        public static implicit operator FixedList64Bytes<T>(in FixedList512Bytes<T> other)
        {
            return new FixedList64Bytes<T>(other);
        }

        /// <summary>
        /// Returns true if two lists are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for equality.</param>
        /// <param name="b">The second list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public static bool operator ==(in FixedList64Bytes<T> a, in FixedList4096Bytes<T> b)
        {
            unsafe
            {
                if(a.length != b.length)
                    return false;
                return UnsafeUtility.MemCmp(a.Buffer, b.Buffer, a.LengthInBytes) == 0;
            }
        }

        /// <summary>
        /// Returns true if two lists are unequal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for inequality.</param>
        /// <param name="b">The second list to compare for inequality.</param>
        /// <returns>True if the two lists are unequal.</returns>
        public static bool operator !=(in FixedList64Bytes<T> a, in FixedList4096Bytes<T> b)
        {
            return !(a == b);
        }

        /// <summary>
        /// Returns a number denoting whether this list should be placed before or after another list in a sort.
        /// </summary>
        /// <param name="other">A list to to compare with.</param>
        /// <returns>An integer denoting the respective sort position of the list relative to the other:
        ///
        /// 0 denotes that both lists should have the same position in a sort.
        /// -1 denotes that this list should precede the other list in a sort.
        /// +1 denotes that this list should follow the other list in a sort.
        /// </returns>
        public int CompareTo(FixedList4096Bytes<T> other)
        {
            unsafe
            {
                fixed(byte* a = &buffer.offset0000.byte0000)
                {
                    byte* b = &other.buffer.offset0000.byte0000;
                    var aa = a + FixedList.PaddingBytes<T>();
                    var bb = b + FixedList.PaddingBytes<T>();
                    var mini = math.min(Length, other.Length);
                    for(var i = 0; i < mini; ++i)
                    {
                        var j = UnsafeUtility.MemCmp(aa + sizeof(T) * i, bb + sizeof(T) * i, sizeof(T));
                        if(j != 0)
                            return j;
                    }
                    return Length.CompareTo(other.Length);
                }
            }
        }

        /// <summary>
        /// Returns true if this list and another list are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="other">The list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public bool Equals(FixedList4096Bytes<T> other)
        {
            return CompareTo(other) == 0;
        }


        /// <summary>
        /// Initializes and returns an instance of FixedList64Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList64Bytes&lt;T&gt;.</exception>
        public FixedList64Bytes(in FixedList4096Bytes<T> other)
        {
            this = default;
            var error = Initialize(other);
            if(error != 0)
                FixedList.CheckResize<FixedBytes62,T>(other.Length);
        }

        /// <summary>
        /// Initializes an instance of FixedList64Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>zero on success, or non-zero on error.</returns>
        internal int Initialize(in FixedList4096Bytes<T> other)
        {
            if(other.Length > Capacity)
                return (int)CopyError.Truncation;
            length = other.length;
            buffer = new FixedBytes62();
            unsafe
            {
                UnsafeUtility.MemCpy(Buffer, other.Buffer, LengthInBytes);
            }
            return 0;
        }

        /// <summary>
        /// Returns a new list that is a copy of another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>A new list that is a copy of the other.</returns>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList64Bytes&lt;T&gt;.</exception>
        public static implicit operator FixedList64Bytes<T>(in FixedList4096Bytes<T> other)
        {
            return new FixedList64Bytes<T>(other);
        }

        /// <summary>
        /// Returns true if the list is equal to an object.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.
        ///
        /// A FixedList*N*&lt;T&gt; can only be equal to another FixedList*N*&lt;T&gt; with the same *N* and T.
        /// </remarks>
        /// <param name="obj">An object to compare for equality.</param>
        /// <returns>True if the list is equal to the object.</returns>
        [NotBurstCompatible]
        public override bool Equals(object obj)
        {
            if(obj is FixedList32Bytes<T> aFixedList32Bytes) return Equals(aFixedList32Bytes);
            if(obj is FixedList64Bytes<T> aFixedList64Bytes) return Equals(aFixedList64Bytes);
            if(obj is FixedList128Bytes<T> aFixedList128Bytes) return Equals(aFixedList128Bytes);
            if(obj is FixedList512Bytes<T> aFixedList512Bytes) return Equals(aFixedList512Bytes);
            if(obj is FixedList4096Bytes<T> aFixedList4096Bytes) return Equals(aFixedList4096Bytes);
            return false;
        }

        /// <summary>
        /// An enumerator over the elements of a FixedList64Bytes&lt;T&gt;.
        /// </summary>
        /// <remarks>
        /// In an enumerator's initial state, `Current` cannot be read. The first <see cref="MoveNext"/> call advances the enumerator to the first element.
        /// </remarks>
        public struct Enumerator : IEnumerator<T>
        {
            FixedList64Bytes<T> m_List;
            int m_Index;

            /// <summary>
            /// Initializes and returns an instance of FixedList64Bytes&lt;T&gt;.
            /// </summary>
            /// <param name="list">The list for which to create an enumerator.</param>
            public Enumerator(ref FixedList64Bytes<T> list)
            {
                m_List = list;
                m_Index = -1;
            }

            /// <summary>
            /// Does nothing.
            /// </summary>
            public void Dispose()
            {
            }

            /// <summary>
            /// Advances the enumerator to the next element.
            /// </summary>
            /// <returns>True if <see cref="Current"/> is valid to read after the call.</returns>
            public bool MoveNext()
            {
                m_Index++;
                return m_Index < m_List.Length;
            }

            /// <summary>
            /// Resets the enumerator to its initial state.
            /// </summary>
            public void Reset()
            {
                m_Index = -1;
            }

            /// <summary>
            /// The current element.
            /// </summary>
            /// <value>The current element.</value>
            public T Current => m_List[m_Index]; // Let FixedList64Bytes<T> indexer check for out of range.

            object IEnumerator.Current => Current;
        }

        /// <summary>
        /// Returns an enumerator for iterating over the elements of this list.
        /// </summary>
        /// <returns>An enumerator for iterating over the elements of this list.</returns>
        public Enumerator GetEnumerator()
        {
            return new Enumerator(ref this);
        }

        /// <summary>
        /// This method is not implemented. Use <see cref="GetEnumerator"/> instead.
        /// </summary>
        /// <returns>Nothing because it always throws <see cref="NotImplementedException"/>.</returns>
        /// <exception cref="NotImplementedException">Method is not implemented.</exception>
        IEnumerator IEnumerable.GetEnumerator()
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// This method is not implemented. Use <see cref="GetEnumerator"/> instead.
        /// </summary>
        /// <returns>Nothing because it always throws <see cref="NotImplementedException"/>.</returns>
        /// <exception cref="NotImplementedException">Method is not implemented.</exception>
        IEnumerator<T> IEnumerable<T>.GetEnumerator()
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Provides extension methods for FixedList64Bytes.
    /// </summary>
    [BurstCompatible]
    public unsafe static class FixedList64BytesExtensions
    {
        /// <summary>
        /// Finds the index of the first occurrence of a particular value in this list.
        /// </summary>
        /// <typeparam name="T">The type of elements in this list.</typeparam>
        /// <typeparam name="U">The value type.</typeparam>
        /// <param name="list">The list to search.</param>
        /// <param name="value">The value to locate.</param>
        /// <returns>The index of the first occurrence of the value. Returns -1 if no occurrence is found.</returns>
        [BurstCompatible(GenericTypeArguments = new [] { typeof(int), typeof(int) })]
        public static int IndexOf<T, U>(this ref FixedList64Bytes<T> list, U value) where T : unmanaged, IEquatable<U>
        {
            return NativeArrayExtensions.IndexOf<T, U>(list.Buffer, list.Length, value);
        }

        /// <summary>
        /// Returns true if a particular value is present in this list.
        /// </summary>
        /// <typeparam name="T">The type of elements in this list.</typeparam>
        /// <typeparam name="U">The value type.</typeparam>
        /// <param name="list">The list to search.</param>
        /// <param name="value">The value to locate.</param>
        /// <returns>True if the value is present in this list.</returns>
        [BurstCompatible(GenericTypeArguments = new [] { typeof(int), typeof(int) })]
        public static bool Contains<T, U>(this ref FixedList64Bytes<T> list, U value) where T : unmanaged, IEquatable<U>
        {
            return list.IndexOf(value) != -1;
        }

        /// <summary>
        /// Removes the first occurrence of a particular value in this list.
        /// </summary>
        /// <remarks>
        /// If a value is removed, all elements after it are shifted down by one, and the list's length is decremented by one.
        ///
        /// If you don't need to preserve the order of the remaining elements, <see cref="Unity.Collections.FixedList64BytesExtensions.RemoveSwapBack{T, U}"/> is a cheaper alternative.
        /// </remarks>
        /// <typeparam name="T">The type of elements in this list.</typeparam>
        /// <typeparam name="U">The value type.</typeparam>
        /// <param name="list">The list to search.</param>
        /// <param name="value">The value to locate and remove.</param>
        /// <returns>True if the value was found and removed.</returns>
        [BurstCompatible(GenericTypeArguments = new [] { typeof(int), typeof(int) })]
        public static bool Remove<T, U>(this ref FixedList64Bytes<T> list, U value) where T : unmanaged, IEquatable<U>
        {
            int index = list.IndexOf(value);
            if (index < 0)
            {
                return false;
            }

            list.RemoveAt(index);

            return true;
        }

        /// <summary>
        /// Removes the first occurrence of a particular value in this list.
        /// </summary>
        /// <remarks>
        /// If a value is removed, the last element of the list is copied to overwrite the removed value, and the list's length is decremented by one.
        ///
        /// This is cheaper than <see cref="Remove"/>, but the order of the remaining elements is not preserved.
        /// </remarks>
        /// <typeparam name="T">The type of elements in this list.</typeparam>
        /// <typeparam name="U">The value type.</typeparam>
        /// <param name="list">The list to search.</param>
        /// <param name="value">The value to locate and remove.</param>
        /// <returns>Returns true if the item is removed.</returns>
        [BurstCompatible(GenericTypeArguments = new [] { typeof(int), typeof(int) })]
        public static bool RemoveSwapBack<T, U>(this ref FixedList64Bytes<T> list, U value) where T : unmanaged, IEquatable<U>
        {
            var index = list.IndexOf(value);
            if (index == -1)
            {
                return false;
            }

            list.RemoveAtSwapBack(index);

            return true;
        }
    }

    sealed class FixedList64BytesDebugView<T> where T : unmanaged
    {
        FixedList64Bytes<T> m_List;
        public FixedList64BytesDebugView(FixedList64Bytes<T> list)
        {
            m_List = list;
        }
        public T[] Items => m_List.ToArray();
    }
    [Obsolete("Renamed to FixedList128Bytes<T> (UnityUpgradable) -> FixedList128Bytes<T>", true)]
    public struct FixedList128<T> where T : unmanaged {}

    /// <summary>
    /// An unmanaged, resizable list whose content is all stored directly in the 128-byte struct. Useful for small lists.
    /// </summary>
    /// <typeparam name="T">The type of the elements.</typeparam>
    [Serializable]
    [DebuggerTypeProxy(typeof(FixedList128BytesDebugView<>))]
    [BurstCompatible(GenericTypeArguments = new [] { typeof(int) })]
    public struct FixedList128Bytes<T>
    : INativeList<T>
    , IEnumerable<T> // Used by collection initializers.
    , IEquatable<FixedList32Bytes<T>>
    , IComparable<FixedList32Bytes<T>>
    , IEquatable<FixedList64Bytes<T>>
    , IComparable<FixedList64Bytes<T>>
    , IEquatable<FixedList128Bytes<T>>
    , IComparable<FixedList128Bytes<T>>
    , IEquatable<FixedList512Bytes<T>>
    , IComparable<FixedList512Bytes<T>>
    , IEquatable<FixedList4096Bytes<T>>
    , IComparable<FixedList4096Bytes<T>>
    where T : unmanaged
    {
        [SerializeField] internal ushort length;
        [SerializeField] internal FixedBytes126 buffer;

        /// <summary>
        /// The current number of items in this list.
        /// </summary>
        /// <value>The current number of items in this list.</value>
        [CreateProperty]
        public int Length
        {
            get => length;
            set
            {
                FixedList.CheckResize<FixedBytes126,T>(value);
                length = (ushort)value;
            }
        }

        /// <summary>
        /// A property in order to display items in the Entity Inspector.
        /// </summary>
        [CreateProperty] IEnumerable<T> Elements => this.ToArray();

        /// <summary>
        /// Whether this list is empty.
        /// </summary>
        /// <value>True if this string has no characters or if the container has not been constructed.</value>
        public bool IsEmpty => Length == 0;

        internal int LengthInBytes => Length * UnsafeUtility.SizeOf<T>();

        unsafe internal byte* Buffer
        {
            get
            {
                fixed(byte* b = &buffer.offset0000.byte0000)
                    return b + FixedList.PaddingBytes<T>();
            }
        }

        /// <summary>
        /// The number of elements that can fit in this list.
        /// </summary>
        /// <value>The number of elements that can fit in this list.</value>
        /// <remarks>The capacity of a FixedList cannot be changed. The setter is included only for conformity with <see cref="INativeList{T}"/>.</remarks>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the new value does not match the current capacity.</exception>
        public int Capacity
        {
          get
          {
              return FixedList.Capacity<FixedBytes126,T>();
          }

          set
          {
              CollectionHelper.CheckCapacityInRange(value, Length);
          }
        }

        /// <summary>
        /// The element at a given index.
        /// </summary>
        /// <param name="index">An index.</param>
        /// <value>The value to store at the index.</value>
        /// <exception cref="IndexOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public T this[int index]
        {
            get
            {
                CollectionHelper.CheckIndexInRange(index, length);
                unsafe
                {
                    return UnsafeUtility.ReadArrayElement<T>(Buffer, CollectionHelper.AssumePositive(index));
                }
            }

            set
            {
                CollectionHelper.CheckIndexInRange(index, length);
                unsafe
                {
                    UnsafeUtility.WriteArrayElement<T>(Buffer, CollectionHelper.AssumePositive(index), value);
                }
            }
        }

        /// <summary>
        /// Returns the element at a given index.
        /// </summary>
        /// <param name="index">An index.</param>
        /// <returns>The list element at the index.</returns>
        public ref T ElementAt(int index)
        {
            CollectionHelper.CheckIndexInRange(index, length);
            unsafe
            {
                return ref UnsafeUtility.ArrayElementAsRef<T>(Buffer, index);
            }
        }

        /// <summary>
        /// Returns the hash code of this list.
        /// </summary>
        /// <remarks>
        /// Only the content of the list (the bytes of the elements) are included in the hash. Any bytes beyond the length are not part of the hash.</remarks>
        /// <returns>The hash code of this list.</returns>
        public override int GetHashCode()
        {
            unsafe
            {
                return (int)CollectionHelper.Hash(Buffer, LengthInBytes);
            }
        }

        /// <summary>
        /// Appends an element to the end of this list. Increments the length by 1.
        /// </summary>
        /// <remarks>The same as <see cref="AddNoResize"/>. Remember that a fixed list is never resized.</remarks>
        /// <param name="item">The element to append at the end of the list.</param>
        /// <exception cref="IndexOutOfRangeException">Thrown if the append exceeds the capacity.</exception>
        public void Add(in T item)
        {
            this[Length++] = item;
        }

        /// <summary>
        /// Appends elements from a buffer to the end of this list. Increments the length by the number of appended elements.
        /// </summary>
        /// <remarks>The same as <see cref="AddRangeNoResize"/>. Remember that a fixed list is never resized.</remarks>
        /// <param name="ptr">A buffer.</param>
        /// <param name="length">The number of elements from the buffer to append.</param>
        /// <exception cref="IndexOutOfRangeException">Thrown if the append exceeds the capacity.</exception>
        public unsafe void AddRange(void* ptr, int length)
        {
            T* data = (T*)ptr;
            for (var i = 0; i < length; ++i)
            {
                this[Length++] = data[i];
            }
        }

        /// <summary>
        /// Appends an element to the end of this list. Increments the length by 1.
        /// </summary>
        /// <remarks>The same as <see cref="Add"/>. Included only for consistency with the other list types.</remarks>
        /// <param name="item">The element to append at the end of the list.</param>
        /// <exception cref="IndexOutOfRangeException">Thrown if the append exceeds the capacity.</exception>
        public void AddNoResize(in T item) => Add(item);

        /// <summary>
        /// Appends elements from a buffer to the end of this list. Increments the length by the number of appended elements.
        /// </summary>
        /// <remarks>The same as <see cref="AddRange"/>. Included only for consistency with the other list types.</remarks>
        /// <param name="ptr">A buffer.</param>
        /// <param name="length">The number of elements from the buffer to append.</param>
        /// <exception cref="IndexOutOfRangeException">Thrown if the append exceeds the capacity.</exception>
        public unsafe void AddRangeNoResize(void* ptr, int length) => AddRange(ptr, length);

        /// <summary>
        /// Sets the length to 0.
        /// </summary>
        /// <remarks> Does *not* zero out the bytes.</remarks>
        public void Clear()
        {
            Length = 0;
        }

        /// <summary>
        /// Shifts elements toward the end of this list, increasing its length.
        /// </summary>
        /// <remarks>
        /// Right-shifts elements in the list so as to create 'free' slots at the beginning or in the middle.
        ///
        /// The length is increased by `end - begin`.
        ///
        /// If `end` equals `begin`, the method does nothing.
        ///
        /// The element at index `begin` will be copied to index `end`, the element at index `begin + 1` will be copied to `end + 1`, and so forth.
        ///
        /// The indexes `begin` up to `end` are not cleared: they will contain whatever values they held prior.
        /// </remarks>
        /// <param name="begin">The index of the first element that will be shifted up.</param>
        /// <param name="end">The index where the first shifted element will end up.</param>
        /// <exception cref="IndexOutOfRangeException">Thrown if the new length exceeds the capacity.</exception>
        public void InsertRangeWithBeginEnd(int begin, int end)
        {
            int items = end - begin;
            if(items < 1)
                return;
            int itemsToCopy = length - begin;
            Length += items;
            if(itemsToCopy < 1)
                return;
            int bytesToCopy = itemsToCopy * UnsafeUtility.SizeOf<T>();
            unsafe
            {
                byte *b = Buffer;
                byte *dest = b + end * UnsafeUtility.SizeOf<T>();
                byte *src = b + begin * UnsafeUtility.SizeOf<T>();
                UnsafeUtility.MemMove(dest, src, bytesToCopy);
            }
        }

        /// <summary>
        /// Inserts a single element at an index. Increments the length by 1.
        /// </summary>
        /// <param name="index">The index at which to insert the element.</param>
        /// <param name="item">The element to insert.</param>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public void Insert(int index, in T item)
        {
            InsertRangeWithBeginEnd(index, index+1);
            this[index] = item;
        }

        /// <summary>
        /// Copies the last element of this list to an index. Decrements the length by 1.
        /// </summary>
        /// <remarks>Useful as a cheap way to remove elements from a list when you don't care about preserving order.</remarks>
        /// <param name="index">The index to overwrite with the last element.</param>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public void RemoveAtSwapBack(int index)
        {
            RemoveRangeSwapBack(index, 1);
        }

        /// <summary>
        /// Copies the last *N* elements of this list to a range in this list. Decrements the length by *N*.
        /// </summary>
        /// <remarks>
        /// Copies the last `count`-numbered elements to the range starting at `index`.
        ///
        /// Useful as a cheap way to remove elements from a list when you don't care about preserving order.
        ///
        /// Does nothing if the count is less than 1.
        /// </remarks>
        /// <param name="index">The first index of the destination range.</param>
        /// <param name="count">The number of elements to copy and the amount by which to decrement the length.</param>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public void RemoveRangeSwapBack(int index, int count)
        {
            if (count > 0)
            {
                int copyFrom = math.max(Length - count, index + count);

                unsafe
                {
                    var sizeOf = UnsafeUtility.SizeOf<T>();
                    void* dst = Buffer + index * sizeOf;
                    void* src = Buffer + copyFrom * sizeOf;
                    UnsafeUtility.MemCpy(dst, src, (Length - copyFrom) * sizeOf);
                }

                Length -= count;
            }
        }

        /// <summary>
        /// Copies the last *N* elements of this list to a range in this list. Decrements the length by *N*.
        /// </summary>
        /// <param name="begin">The first index of the item to remove.</param>
        /// <param name="end">The index past-the-last item to remove.</param>
        /// <exception cref="ArgumentException">Thrown if end argument is less than begin argument.</exception>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if begin or end arguments are not positive or out of bounds.</exception>
        [Obsolete("RemoveRangeSwapBackWithBeginEnd(begin, end) is deprecated, use RemoveRangeSwapBack(index, count) instead. (RemovedAfter 2021-06-02)", false)]
        public void RemoveRangeSwapBackWithBeginEnd(int begin, int end) => RemoveRangeSwapBack(begin, end - begin);

        /// <summary>
        /// Removes the element at an index. Shifts everything above the index down by one and decrements the length by 1.
        /// </summary>
        /// <param name="index">The index of the element to remove.</param>
        /// <remarks>
        /// If you don't care about preserving the order of the elements, `RemoveAtSwapBack` is a more efficient way to remove an element.
        /// </remarks>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public void RemoveAt(int index)
        {
            RemoveRange(index, 1);
        }

        /// <summary>
        /// Removes *N* elements of a range. Shifts everything above the range down by *N* and decrements the length by *N*.
        /// </summary>
        /// <remarks>
        /// If you don't care about preserving the order of the elements, `RemoveAtSwapBack` is a more efficient way to remove elements.
        /// </remarks>
        /// <param name="index">The first index of the range to remove.</param>
        /// <param name="count">The number of elements to remove.</param>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public void RemoveRange(int index, int count)
        {
            if (count > 0)
            {
                int copyFrom = math.min(index + count, Length);

                unsafe
                {
                    var sizeOf = UnsafeUtility.SizeOf<T>();
                    void* dst = Buffer + index * sizeOf;
                    void* src = Buffer + copyFrom * sizeOf;
                    UnsafeUtility.MemCpy(dst, src, (Length - copyFrom) * sizeOf);
                }

                Length -= count;
            }
        }

        /// <summary>
        /// Removes *N* elements of a range. Shifts everything above the range down by *N* and decrements the length by *N*.
        /// </summary>
        /// <param name="begin">The first index of the item to remove.</param>
        /// <param name="end">The index past-the-last item to remove.</param>
        /// <remarks>
        /// This method of removing item(s) is useful only in case when list is ordered and user wants to preserve order
        /// in list after removal In majority of cases is not important and user should use more performant `RemoveRangeSwapBackWithBeginEnd`.
        /// </remarks>
        /// <exception cref="ArgumentException">Thrown if end argument is less than begin argument.</exception>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if begin or end arguments are not positive or out of bounds.</exception>
        [Obsolete("RemoveRangeWithBeginEnd(begin, end) is deprecated, use RemoveRange(index, count) instead. (RemovedAfter 2021-06-02)", false)]
        public void RemoveRangeWithBeginEnd(int begin, int end) => RemoveRange(begin, end - begin);

        /// <summary>
        /// Returns a managed array that is a copy of this list.
        /// </summary>
        /// <returns>A managed array that is a copy of this list.</returns>
        [NotBurstCompatible]
        public T[] ToArray()
        {
            var result = new T[Length];
            unsafe
            {
                byte* s = Buffer;
                fixed(T* d = result)
                    UnsafeUtility.MemCpy(d, s, LengthInBytes);
            }
            return result;
        }

        /// <summary>
        /// Returns an array that is a copy of this list.
        /// </summary>
        /// <param name="allocator">The allocator to use.</param>
        /// <returns>An array that is a copy of this list.</returns>
        public NativeArray<T> ToNativeArray(AllocatorManager.AllocatorHandle allocator)
        {
            unsafe
            {
                var copy = CollectionHelper.CreateNativeArray<T>(Length, allocator, NativeArrayOptions.UninitializedMemory);
                UnsafeUtility.MemCpy(copy.GetUnsafePtr(), Buffer, LengthInBytes);
                return copy;
            }
        }


        /// <summary>
        /// Returns true if two lists are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for equality.</param>
        /// <param name="b">The second list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public static bool operator ==(in FixedList128Bytes<T> a, in FixedList32Bytes<T> b)
        {
            unsafe
            {
                if(a.length != b.length)
                    return false;
                return UnsafeUtility.MemCmp(a.Buffer, b.Buffer, a.LengthInBytes) == 0;
            }
        }

        /// <summary>
        /// Returns true if two lists are unequal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for inequality.</param>
        /// <param name="b">The second list to compare for inequality.</param>
        /// <returns>True if the two lists are unequal.</returns>
        public static bool operator !=(in FixedList128Bytes<T> a, in FixedList32Bytes<T> b)
        {
            return !(a == b);
        }

        /// <summary>
        /// Returns a number denoting whether this list should be placed before or after another list in a sort.
        /// </summary>
        /// <param name="other">A list to to compare with.</param>
        /// <returns>An integer denoting the respective sort position of the list relative to the other:
        ///
        /// 0 denotes that both lists should have the same position in a sort.
        /// -1 denotes that this list should precede the other list in a sort.
        /// +1 denotes that this list should follow the other list in a sort.
        /// </returns>
        public int CompareTo(FixedList32Bytes<T> other)
        {
            unsafe
            {
                fixed(byte* a = &buffer.offset0000.byte0000)
                {
                    byte* b = &other.buffer.offset0000.byte0000;
                    var aa = a + FixedList.PaddingBytes<T>();
                    var bb = b + FixedList.PaddingBytes<T>();
                    var mini = math.min(Length, other.Length);
                    for(var i = 0; i < mini; ++i)
                    {
                        var j = UnsafeUtility.MemCmp(aa + sizeof(T) * i, bb + sizeof(T) * i, sizeof(T));
                        if(j != 0)
                            return j;
                    }
                    return Length.CompareTo(other.Length);
                }
            }
        }

        /// <summary>
        /// Returns true if this list and another list are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="other">The list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public bool Equals(FixedList32Bytes<T> other)
        {
            return CompareTo(other) == 0;
        }


        /// <summary>
        /// Initializes and returns an instance of FixedList128Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList128Bytes&lt;T&gt;.</exception>
        public FixedList128Bytes(in FixedList32Bytes<T> other)
        {
            this = default;
            var error = Initialize(other);
            if(error != 0)
                FixedList.CheckResize<FixedBytes126,T>(other.Length);
        }

        /// <summary>
        /// Initializes an instance of FixedList128Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>zero on success, or non-zero on error.</returns>
        internal int Initialize(in FixedList32Bytes<T> other)
        {
            if(other.Length > Capacity)
                return (int)CopyError.Truncation;
            length = other.length;
            buffer = new FixedBytes126();
            unsafe
            {
                UnsafeUtility.MemCpy(Buffer, other.Buffer, LengthInBytes);
            }
            return 0;
        }

        /// <summary>
        /// Returns a new list that is a copy of another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>A new list that is a copy of the other.</returns>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList128Bytes&lt;T&gt;.</exception>
        public static implicit operator FixedList128Bytes<T>(in FixedList32Bytes<T> other)
        {
            return new FixedList128Bytes<T>(other);
        }

        /// <summary>
        /// Returns true if two lists are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for equality.</param>
        /// <param name="b">The second list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public static bool operator ==(in FixedList128Bytes<T> a, in FixedList64Bytes<T> b)
        {
            unsafe
            {
                if(a.length != b.length)
                    return false;
                return UnsafeUtility.MemCmp(a.Buffer, b.Buffer, a.LengthInBytes) == 0;
            }
        }

        /// <summary>
        /// Returns true if two lists are unequal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for inequality.</param>
        /// <param name="b">The second list to compare for inequality.</param>
        /// <returns>True if the two lists are unequal.</returns>
        public static bool operator !=(in FixedList128Bytes<T> a, in FixedList64Bytes<T> b)
        {
            return !(a == b);
        }

        /// <summary>
        /// Returns a number denoting whether this list should be placed before or after another list in a sort.
        /// </summary>
        /// <param name="other">A list to to compare with.</param>
        /// <returns>An integer denoting the respective sort position of the list relative to the other:
        ///
        /// 0 denotes that both lists should have the same position in a sort.
        /// -1 denotes that this list should precede the other list in a sort.
        /// +1 denotes that this list should follow the other list in a sort.
        /// </returns>
        public int CompareTo(FixedList64Bytes<T> other)
        {
            unsafe
            {
                fixed(byte* a = &buffer.offset0000.byte0000)
                {
                    byte* b = &other.buffer.offset0000.byte0000;
                    var aa = a + FixedList.PaddingBytes<T>();
                    var bb = b + FixedList.PaddingBytes<T>();
                    var mini = math.min(Length, other.Length);
                    for(var i = 0; i < mini; ++i)
                    {
                        var j = UnsafeUtility.MemCmp(aa + sizeof(T) * i, bb + sizeof(T) * i, sizeof(T));
                        if(j != 0)
                            return j;
                    }
                    return Length.CompareTo(other.Length);
                }
            }
        }

        /// <summary>
        /// Returns true if this list and another list are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="other">The list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public bool Equals(FixedList64Bytes<T> other)
        {
            return CompareTo(other) == 0;
        }


        /// <summary>
        /// Initializes and returns an instance of FixedList128Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList128Bytes&lt;T&gt;.</exception>
        public FixedList128Bytes(in FixedList64Bytes<T> other)
        {
            this = default;
            var error = Initialize(other);
            if(error != 0)
                FixedList.CheckResize<FixedBytes126,T>(other.Length);
        }

        /// <summary>
        /// Initializes an instance of FixedList128Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>zero on success, or non-zero on error.</returns>
        internal int Initialize(in FixedList64Bytes<T> other)
        {
            if(other.Length > Capacity)
                return (int)CopyError.Truncation;
            length = other.length;
            buffer = new FixedBytes126();
            unsafe
            {
                UnsafeUtility.MemCpy(Buffer, other.Buffer, LengthInBytes);
            }
            return 0;
        }

        /// <summary>
        /// Returns a new list that is a copy of another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>A new list that is a copy of the other.</returns>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList128Bytes&lt;T&gt;.</exception>
        public static implicit operator FixedList128Bytes<T>(in FixedList64Bytes<T> other)
        {
            return new FixedList128Bytes<T>(other);
        }

        /// <summary>
        /// Returns true if two lists are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for equality.</param>
        /// <param name="b">The second list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public static bool operator ==(in FixedList128Bytes<T> a, in FixedList128Bytes<T> b)
        {
            unsafe
            {
                if(a.length != b.length)
                    return false;
                return UnsafeUtility.MemCmp(a.Buffer, b.Buffer, a.LengthInBytes) == 0;
            }
        }

        /// <summary>
        /// Returns true if two lists are unequal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for inequality.</param>
        /// <param name="b">The second list to compare for inequality.</param>
        /// <returns>True if the two lists are unequal.</returns>
        public static bool operator !=(in FixedList128Bytes<T> a, in FixedList128Bytes<T> b)
        {
            return !(a == b);
        }

        /// <summary>
        /// Returns a number denoting whether this list should be placed before or after another list in a sort.
        /// </summary>
        /// <param name="other">A list to to compare with.</param>
        /// <returns>An integer denoting the respective sort position of the list relative to the other:
        ///
        /// 0 denotes that both lists should have the same position in a sort.
        /// -1 denotes that this list should precede the other list in a sort.
        /// +1 denotes that this list should follow the other list in a sort.
        /// </returns>
        public int CompareTo(FixedList128Bytes<T> other)
        {
            unsafe
            {
                fixed(byte* a = &buffer.offset0000.byte0000)
                {
                    byte* b = &other.buffer.offset0000.byte0000;
                    var aa = a + FixedList.PaddingBytes<T>();
                    var bb = b + FixedList.PaddingBytes<T>();
                    var mini = math.min(Length, other.Length);
                    for(var i = 0; i < mini; ++i)
                    {
                        var j = UnsafeUtility.MemCmp(aa + sizeof(T) * i, bb + sizeof(T) * i, sizeof(T));
                        if(j != 0)
                            return j;
                    }
                    return Length.CompareTo(other.Length);
                }
            }
        }

        /// <summary>
        /// Returns true if this list and another list are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="other">The list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public bool Equals(FixedList128Bytes<T> other)
        {
            return CompareTo(other) == 0;
        }


        /// <summary>
        /// Returns true if two lists are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for equality.</param>
        /// <param name="b">The second list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public static bool operator ==(in FixedList128Bytes<T> a, in FixedList512Bytes<T> b)
        {
            unsafe
            {
                if(a.length != b.length)
                    return false;
                return UnsafeUtility.MemCmp(a.Buffer, b.Buffer, a.LengthInBytes) == 0;
            }
        }

        /// <summary>
        /// Returns true if two lists are unequal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for inequality.</param>
        /// <param name="b">The second list to compare for inequality.</param>
        /// <returns>True if the two lists are unequal.</returns>
        public static bool operator !=(in FixedList128Bytes<T> a, in FixedList512Bytes<T> b)
        {
            return !(a == b);
        }

        /// <summary>
        /// Returns a number denoting whether this list should be placed before or after another list in a sort.
        /// </summary>
        /// <param name="other">A list to to compare with.</param>
        /// <returns>An integer denoting the respective sort position of the list relative to the other:
        ///
        /// 0 denotes that both lists should have the same position in a sort.
        /// -1 denotes that this list should precede the other list in a sort.
        /// +1 denotes that this list should follow the other list in a sort.
        /// </returns>
        public int CompareTo(FixedList512Bytes<T> other)
        {
            unsafe
            {
                fixed(byte* a = &buffer.offset0000.byte0000)
                {
                    byte* b = &other.buffer.offset0000.byte0000;
                    var aa = a + FixedList.PaddingBytes<T>();
                    var bb = b + FixedList.PaddingBytes<T>();
                    var mini = math.min(Length, other.Length);
                    for(var i = 0; i < mini; ++i)
                    {
                        var j = UnsafeUtility.MemCmp(aa + sizeof(T) * i, bb + sizeof(T) * i, sizeof(T));
                        if(j != 0)
                            return j;
                    }
                    return Length.CompareTo(other.Length);
                }
            }
        }

        /// <summary>
        /// Returns true if this list and another list are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="other">The list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public bool Equals(FixedList512Bytes<T> other)
        {
            return CompareTo(other) == 0;
        }


        /// <summary>
        /// Initializes and returns an instance of FixedList128Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList128Bytes&lt;T&gt;.</exception>
        public FixedList128Bytes(in FixedList512Bytes<T> other)
        {
            this = default;
            var error = Initialize(other);
            if(error != 0)
                FixedList.CheckResize<FixedBytes126,T>(other.Length);
        }

        /// <summary>
        /// Initializes an instance of FixedList128Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>zero on success, or non-zero on error.</returns>
        internal int Initialize(in FixedList512Bytes<T> other)
        {
            if(other.Length > Capacity)
                return (int)CopyError.Truncation;
            length = other.length;
            buffer = new FixedBytes126();
            unsafe
            {
                UnsafeUtility.MemCpy(Buffer, other.Buffer, LengthInBytes);
            }
            return 0;
        }

        /// <summary>
        /// Returns a new list that is a copy of another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>A new list that is a copy of the other.</returns>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList128Bytes&lt;T&gt;.</exception>
        public static implicit operator FixedList128Bytes<T>(in FixedList512Bytes<T> other)
        {
            return new FixedList128Bytes<T>(other);
        }

        /// <summary>
        /// Returns true if two lists are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for equality.</param>
        /// <param name="b">The second list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public static bool operator ==(in FixedList128Bytes<T> a, in FixedList4096Bytes<T> b)
        {
            unsafe
            {
                if(a.length != b.length)
                    return false;
                return UnsafeUtility.MemCmp(a.Buffer, b.Buffer, a.LengthInBytes) == 0;
            }
        }

        /// <summary>
        /// Returns true if two lists are unequal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for inequality.</param>
        /// <param name="b">The second list to compare for inequality.</param>
        /// <returns>True if the two lists are unequal.</returns>
        public static bool operator !=(in FixedList128Bytes<T> a, in FixedList4096Bytes<T> b)
        {
            return !(a == b);
        }

        /// <summary>
        /// Returns a number denoting whether this list should be placed before or after another list in a sort.
        /// </summary>
        /// <param name="other">A list to to compare with.</param>
        /// <returns>An integer denoting the respective sort position of the list relative to the other:
        ///
        /// 0 denotes that both lists should have the same position in a sort.
        /// -1 denotes that this list should precede the other list in a sort.
        /// +1 denotes that this list should follow the other list in a sort.
        /// </returns>
        public int CompareTo(FixedList4096Bytes<T> other)
        {
            unsafe
            {
                fixed(byte* a = &buffer.offset0000.byte0000)
                {
                    byte* b = &other.buffer.offset0000.byte0000;
                    var aa = a + FixedList.PaddingBytes<T>();
                    var bb = b + FixedList.PaddingBytes<T>();
                    var mini = math.min(Length, other.Length);
                    for(var i = 0; i < mini; ++i)
                    {
                        var j = UnsafeUtility.MemCmp(aa + sizeof(T) * i, bb + sizeof(T) * i, sizeof(T));
                        if(j != 0)
                            return j;
                    }
                    return Length.CompareTo(other.Length);
                }
            }
        }

        /// <summary>
        /// Returns true if this list and another list are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="other">The list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public bool Equals(FixedList4096Bytes<T> other)
        {
            return CompareTo(other) == 0;
        }


        /// <summary>
        /// Initializes and returns an instance of FixedList128Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList128Bytes&lt;T&gt;.</exception>
        public FixedList128Bytes(in FixedList4096Bytes<T> other)
        {
            this = default;
            var error = Initialize(other);
            if(error != 0)
                FixedList.CheckResize<FixedBytes126,T>(other.Length);
        }

        /// <summary>
        /// Initializes an instance of FixedList128Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>zero on success, or non-zero on error.</returns>
        internal int Initialize(in FixedList4096Bytes<T> other)
        {
            if(other.Length > Capacity)
                return (int)CopyError.Truncation;
            length = other.length;
            buffer = new FixedBytes126();
            unsafe
            {
                UnsafeUtility.MemCpy(Buffer, other.Buffer, LengthInBytes);
            }
            return 0;
        }

        /// <summary>
        /// Returns a new list that is a copy of another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>A new list that is a copy of the other.</returns>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList128Bytes&lt;T&gt;.</exception>
        public static implicit operator FixedList128Bytes<T>(in FixedList4096Bytes<T> other)
        {
            return new FixedList128Bytes<T>(other);
        }

        /// <summary>
        /// Returns true if the list is equal to an object.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.
        ///
        /// A FixedList*N*&lt;T&gt; can only be equal to another FixedList*N*&lt;T&gt; with the same *N* and T.
        /// </remarks>
        /// <param name="obj">An object to compare for equality.</param>
        /// <returns>True if the list is equal to the object.</returns>
        [NotBurstCompatible]
        public override bool Equals(object obj)
        {
            if(obj is FixedList32Bytes<T> aFixedList32Bytes) return Equals(aFixedList32Bytes);
            if(obj is FixedList64Bytes<T> aFixedList64Bytes) return Equals(aFixedList64Bytes);
            if(obj is FixedList128Bytes<T> aFixedList128Bytes) return Equals(aFixedList128Bytes);
            if(obj is FixedList512Bytes<T> aFixedList512Bytes) return Equals(aFixedList512Bytes);
            if(obj is FixedList4096Bytes<T> aFixedList4096Bytes) return Equals(aFixedList4096Bytes);
            return false;
        }

        /// <summary>
        /// An enumerator over the elements of a FixedList128Bytes&lt;T&gt;.
        /// </summary>
        /// <remarks>
        /// In an enumerator's initial state, `Current` cannot be read. The first <see cref="MoveNext"/> call advances the enumerator to the first element.
        /// </remarks>
        public struct Enumerator : IEnumerator<T>
        {
            FixedList128Bytes<T> m_List;
            int m_Index;

            /// <summary>
            /// Initializes and returns an instance of FixedList128Bytes&lt;T&gt;.
            /// </summary>
            /// <param name="list">The list for which to create an enumerator.</param>
            public Enumerator(ref FixedList128Bytes<T> list)
            {
                m_List = list;
                m_Index = -1;
            }

            /// <summary>
            /// Does nothing.
            /// </summary>
            public void Dispose()
            {
            }

            /// <summary>
            /// Advances the enumerator to the next element.
            /// </summary>
            /// <returns>True if <see cref="Current"/> is valid to read after the call.</returns>
            public bool MoveNext()
            {
                m_Index++;
                return m_Index < m_List.Length;
            }

            /// <summary>
            /// Resets the enumerator to its initial state.
            /// </summary>
            public void Reset()
            {
                m_Index = -1;
            }

            /// <summary>
            /// The current element.
            /// </summary>
            /// <value>The current element.</value>
            public T Current => m_List[m_Index]; // Let FixedList128Bytes<T> indexer check for out of range.

            object IEnumerator.Current => Current;
        }

        /// <summary>
        /// Returns an enumerator for iterating over the elements of this list.
        /// </summary>
        /// <returns>An enumerator for iterating over the elements of this list.</returns>
        public Enumerator GetEnumerator()
        {
            return new Enumerator(ref this);
        }

        /// <summary>
        /// This method is not implemented. Use <see cref="GetEnumerator"/> instead.
        /// </summary>
        /// <returns>Nothing because it always throws <see cref="NotImplementedException"/>.</returns>
        /// <exception cref="NotImplementedException">Method is not implemented.</exception>
        IEnumerator IEnumerable.GetEnumerator()
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// This method is not implemented. Use <see cref="GetEnumerator"/> instead.
        /// </summary>
        /// <returns>Nothing because it always throws <see cref="NotImplementedException"/>.</returns>
        /// <exception cref="NotImplementedException">Method is not implemented.</exception>
        IEnumerator<T> IEnumerable<T>.GetEnumerator()
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Provides extension methods for FixedList128Bytes.
    /// </summary>
    [BurstCompatible]
    public unsafe static class FixedList128BytesExtensions
    {
        /// <summary>
        /// Finds the index of the first occurrence of a particular value in this list.
        /// </summary>
        /// <typeparam name="T">The type of elements in this list.</typeparam>
        /// <typeparam name="U">The value type.</typeparam>
        /// <param name="list">The list to search.</param>
        /// <param name="value">The value to locate.</param>
        /// <returns>The index of the first occurrence of the value. Returns -1 if no occurrence is found.</returns>
        [BurstCompatible(GenericTypeArguments = new [] { typeof(int), typeof(int) })]
        public static int IndexOf<T, U>(this ref FixedList128Bytes<T> list, U value) where T : unmanaged, IEquatable<U>
        {
            return NativeArrayExtensions.IndexOf<T, U>(list.Buffer, list.Length, value);
        }

        /// <summary>
        /// Returns true if a particular value is present in this list.
        /// </summary>
        /// <typeparam name="T">The type of elements in this list.</typeparam>
        /// <typeparam name="U">The value type.</typeparam>
        /// <param name="list">The list to search.</param>
        /// <param name="value">The value to locate.</param>
        /// <returns>True if the value is present in this list.</returns>
        [BurstCompatible(GenericTypeArguments = new [] { typeof(int), typeof(int) })]
        public static bool Contains<T, U>(this ref FixedList128Bytes<T> list, U value) where T : unmanaged, IEquatable<U>
        {
            return list.IndexOf(value) != -1;
        }

        /// <summary>
        /// Removes the first occurrence of a particular value in this list.
        /// </summary>
        /// <remarks>
        /// If a value is removed, all elements after it are shifted down by one, and the list's length is decremented by one.
        ///
        /// If you don't need to preserve the order of the remaining elements, <see cref="Unity.Collections.FixedList128BytesExtensions.RemoveSwapBack{T, U}"/> is a cheaper alternative.
        /// </remarks>
        /// <typeparam name="T">The type of elements in this list.</typeparam>
        /// <typeparam name="U">The value type.</typeparam>
        /// <param name="list">The list to search.</param>
        /// <param name="value">The value to locate and remove.</param>
        /// <returns>True if the value was found and removed.</returns>
        [BurstCompatible(GenericTypeArguments = new [] { typeof(int), typeof(int) })]
        public static bool Remove<T, U>(this ref FixedList128Bytes<T> list, U value) where T : unmanaged, IEquatable<U>
        {
            int index = list.IndexOf(value);
            if (index < 0)
            {
                return false;
            }

            list.RemoveAt(index);

            return true;
        }

        /// <summary>
        /// Removes the first occurrence of a particular value in this list.
        /// </summary>
        /// <remarks>
        /// If a value is removed, the last element of the list is copied to overwrite the removed value, and the list's length is decremented by one.
        ///
        /// This is cheaper than <see cref="Remove"/>, but the order of the remaining elements is not preserved.
        /// </remarks>
        /// <typeparam name="T">The type of elements in this list.</typeparam>
        /// <typeparam name="U">The value type.</typeparam>
        /// <param name="list">The list to search.</param>
        /// <param name="value">The value to locate and remove.</param>
        /// <returns>Returns true if the item is removed.</returns>
        [BurstCompatible(GenericTypeArguments = new [] { typeof(int), typeof(int) })]
        public static bool RemoveSwapBack<T, U>(this ref FixedList128Bytes<T> list, U value) where T : unmanaged, IEquatable<U>
        {
            var index = list.IndexOf(value);
            if (index == -1)
            {
                return false;
            }

            list.RemoveAtSwapBack(index);

            return true;
        }
    }

    sealed class FixedList128BytesDebugView<T> where T : unmanaged
    {
        FixedList128Bytes<T> m_List;
        public FixedList128BytesDebugView(FixedList128Bytes<T> list)
        {
            m_List = list;
        }
        public T[] Items => m_List.ToArray();
    }
    [Obsolete("Renamed to FixedList512Bytes<T> (UnityUpgradable) -> FixedList512Bytes<T>", true)]
    public struct FixedList512<T> where T : unmanaged {}

    /// <summary>
    /// An unmanaged, resizable list whose content is all stored directly in the 512-byte struct. Useful for small lists.
    /// </summary>
    /// <typeparam name="T">The type of the elements.</typeparam>
    [Serializable]
    [DebuggerTypeProxy(typeof(FixedList512BytesDebugView<>))]
    [BurstCompatible(GenericTypeArguments = new [] { typeof(int) })]
    public struct FixedList512Bytes<T>
    : INativeList<T>
    , IEnumerable<T> // Used by collection initializers.
    , IEquatable<FixedList32Bytes<T>>
    , IComparable<FixedList32Bytes<T>>
    , IEquatable<FixedList64Bytes<T>>
    , IComparable<FixedList64Bytes<T>>
    , IEquatable<FixedList128Bytes<T>>
    , IComparable<FixedList128Bytes<T>>
    , IEquatable<FixedList512Bytes<T>>
    , IComparable<FixedList512Bytes<T>>
    , IEquatable<FixedList4096Bytes<T>>
    , IComparable<FixedList4096Bytes<T>>
    where T : unmanaged
    {
        [SerializeField] internal ushort length;
        [SerializeField] internal FixedBytes510 buffer;

        /// <summary>
        /// The current number of items in this list.
        /// </summary>
        /// <value>The current number of items in this list.</value>
        [CreateProperty]
        public int Length
        {
            get => length;
            set
            {
                FixedList.CheckResize<FixedBytes510,T>(value);
                length = (ushort)value;
            }
        }

        /// <summary>
        /// A property in order to display items in the Entity Inspector.
        /// </summary>
        [CreateProperty] IEnumerable<T> Elements => this.ToArray();

        /// <summary>
        /// Whether this list is empty.
        /// </summary>
        /// <value>True if this string has no characters or if the container has not been constructed.</value>
        public bool IsEmpty => Length == 0;

        internal int LengthInBytes => Length * UnsafeUtility.SizeOf<T>();

        unsafe internal byte* Buffer
        {
            get
            {
                fixed(byte* b = &buffer.offset0000.byte0000)
                    return b + FixedList.PaddingBytes<T>();
            }
        }

        /// <summary>
        /// The number of elements that can fit in this list.
        /// </summary>
        /// <value>The number of elements that can fit in this list.</value>
        /// <remarks>The capacity of a FixedList cannot be changed. The setter is included only for conformity with <see cref="INativeList{T}"/>.</remarks>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the new value does not match the current capacity.</exception>
        public int Capacity
        {
          get
          {
              return FixedList.Capacity<FixedBytes510,T>();
          }

          set
          {
              CollectionHelper.CheckCapacityInRange(value, Length);
          }
        }

        /// <summary>
        /// The element at a given index.
        /// </summary>
        /// <param name="index">An index.</param>
        /// <value>The value to store at the index.</value>
        /// <exception cref="IndexOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public T this[int index]
        {
            get
            {
                CollectionHelper.CheckIndexInRange(index, length);
                unsafe
                {
                    return UnsafeUtility.ReadArrayElement<T>(Buffer, CollectionHelper.AssumePositive(index));
                }
            }

            set
            {
                CollectionHelper.CheckIndexInRange(index, length);
                unsafe
                {
                    UnsafeUtility.WriteArrayElement<T>(Buffer, CollectionHelper.AssumePositive(index), value);
                }
            }
        }

        /// <summary>
        /// Returns the element at a given index.
        /// </summary>
        /// <param name="index">An index.</param>
        /// <returns>The list element at the index.</returns>
        public ref T ElementAt(int index)
        {
            CollectionHelper.CheckIndexInRange(index, length);
            unsafe
            {
                return ref UnsafeUtility.ArrayElementAsRef<T>(Buffer, index);
            }
        }

        /// <summary>
        /// Returns the hash code of this list.
        /// </summary>
        /// <remarks>
        /// Only the content of the list (the bytes of the elements) are included in the hash. Any bytes beyond the length are not part of the hash.</remarks>
        /// <returns>The hash code of this list.</returns>
        public override int GetHashCode()
        {
            unsafe
            {
                return (int)CollectionHelper.Hash(Buffer, LengthInBytes);
            }
        }

        /// <summary>
        /// Appends an element to the end of this list. Increments the length by 1.
        /// </summary>
        /// <remarks>The same as <see cref="AddNoResize"/>. Remember that a fixed list is never resized.</remarks>
        /// <param name="item">The element to append at the end of the list.</param>
        /// <exception cref="IndexOutOfRangeException">Thrown if the append exceeds the capacity.</exception>
        public void Add(in T item)
        {
            this[Length++] = item;
        }

        /// <summary>
        /// Appends elements from a buffer to the end of this list. Increments the length by the number of appended elements.
        /// </summary>
        /// <remarks>The same as <see cref="AddRangeNoResize"/>. Remember that a fixed list is never resized.</remarks>
        /// <param name="ptr">A buffer.</param>
        /// <param name="length">The number of elements from the buffer to append.</param>
        /// <exception cref="IndexOutOfRangeException">Thrown if the append exceeds the capacity.</exception>
        public unsafe void AddRange(void* ptr, int length)
        {
            T* data = (T*)ptr;
            for (var i = 0; i < length; ++i)
            {
                this[Length++] = data[i];
            }
        }

        /// <summary>
        /// Appends an element to the end of this list. Increments the length by 1.
        /// </summary>
        /// <remarks>The same as <see cref="Add"/>. Included only for consistency with the other list types.</remarks>
        /// <param name="item">The element to append at the end of the list.</param>
        /// <exception cref="IndexOutOfRangeException">Thrown if the append exceeds the capacity.</exception>
        public void AddNoResize(in T item) => Add(item);

        /// <summary>
        /// Appends elements from a buffer to the end of this list. Increments the length by the number of appended elements.
        /// </summary>
        /// <remarks>The same as <see cref="AddRange"/>. Included only for consistency with the other list types.</remarks>
        /// <param name="ptr">A buffer.</param>
        /// <param name="length">The number of elements from the buffer to append.</param>
        /// <exception cref="IndexOutOfRangeException">Thrown if the append exceeds the capacity.</exception>
        public unsafe void AddRangeNoResize(void* ptr, int length) => AddRange(ptr, length);

        /// <summary>
        /// Sets the length to 0.
        /// </summary>
        /// <remarks> Does *not* zero out the bytes.</remarks>
        public void Clear()
        {
            Length = 0;
        }

        /// <summary>
        /// Shifts elements toward the end of this list, increasing its length.
        /// </summary>
        /// <remarks>
        /// Right-shifts elements in the list so as to create 'free' slots at the beginning or in the middle.
        ///
        /// The length is increased by `end - begin`.
        ///
        /// If `end` equals `begin`, the method does nothing.
        ///
        /// The element at index `begin` will be copied to index `end`, the element at index `begin + 1` will be copied to `end + 1`, and so forth.
        ///
        /// The indexes `begin` up to `end` are not cleared: they will contain whatever values they held prior.
        /// </remarks>
        /// <param name="begin">The index of the first element that will be shifted up.</param>
        /// <param name="end">The index where the first shifted element will end up.</param>
        /// <exception cref="IndexOutOfRangeException">Thrown if the new length exceeds the capacity.</exception>
        public void InsertRangeWithBeginEnd(int begin, int end)
        {
            int items = end - begin;
            if(items < 1)
                return;
            int itemsToCopy = length - begin;
            Length += items;
            if(itemsToCopy < 1)
                return;
            int bytesToCopy = itemsToCopy * UnsafeUtility.SizeOf<T>();
            unsafe
            {
                byte *b = Buffer;
                byte *dest = b + end * UnsafeUtility.SizeOf<T>();
                byte *src = b + begin * UnsafeUtility.SizeOf<T>();
                UnsafeUtility.MemMove(dest, src, bytesToCopy);
            }
        }

        /// <summary>
        /// Inserts a single element at an index. Increments the length by 1.
        /// </summary>
        /// <param name="index">The index at which to insert the element.</param>
        /// <param name="item">The element to insert.</param>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public void Insert(int index, in T item)
        {
            InsertRangeWithBeginEnd(index, index+1);
            this[index] = item;
        }

        /// <summary>
        /// Copies the last element of this list to an index. Decrements the length by 1.
        /// </summary>
        /// <remarks>Useful as a cheap way to remove elements from a list when you don't care about preserving order.</remarks>
        /// <param name="index">The index to overwrite with the last element.</param>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public void RemoveAtSwapBack(int index)
        {
            RemoveRangeSwapBack(index, 1);
        }

        /// <summary>
        /// Copies the last *N* elements of this list to a range in this list. Decrements the length by *N*.
        /// </summary>
        /// <remarks>
        /// Copies the last `count`-numbered elements to the range starting at `index`.
        ///
        /// Useful as a cheap way to remove elements from a list when you don't care about preserving order.
        ///
        /// Does nothing if the count is less than 1.
        /// </remarks>
        /// <param name="index">The first index of the destination range.</param>
        /// <param name="count">The number of elements to copy and the amount by which to decrement the length.</param>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public void RemoveRangeSwapBack(int index, int count)
        {
            if (count > 0)
            {
                int copyFrom = math.max(Length - count, index + count);

                unsafe
                {
                    var sizeOf = UnsafeUtility.SizeOf<T>();
                    void* dst = Buffer + index * sizeOf;
                    void* src = Buffer + copyFrom * sizeOf;
                    UnsafeUtility.MemCpy(dst, src, (Length - copyFrom) * sizeOf);
                }

                Length -= count;
            }
        }

        /// <summary>
        /// Copies the last *N* elements of this list to a range in this list. Decrements the length by *N*.
        /// </summary>
        /// <param name="begin">The first index of the item to remove.</param>
        /// <param name="end">The index past-the-last item to remove.</param>
        /// <exception cref="ArgumentException">Thrown if end argument is less than begin argument.</exception>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if begin or end arguments are not positive or out of bounds.</exception>
        [Obsolete("RemoveRangeSwapBackWithBeginEnd(begin, end) is deprecated, use RemoveRangeSwapBack(index, count) instead. (RemovedAfter 2021-06-02)", false)]
        public void RemoveRangeSwapBackWithBeginEnd(int begin, int end) => RemoveRangeSwapBack(begin, end - begin);

        /// <summary>
        /// Removes the element at an index. Shifts everything above the index down by one and decrements the length by 1.
        /// </summary>
        /// <param name="index">The index of the element to remove.</param>
        /// <remarks>
        /// If you don't care about preserving the order of the elements, `RemoveAtSwapBack` is a more efficient way to remove an element.
        /// </remarks>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public void RemoveAt(int index)
        {
            RemoveRange(index, 1);
        }

        /// <summary>
        /// Removes *N* elements of a range. Shifts everything above the range down by *N* and decrements the length by *N*.
        /// </summary>
        /// <remarks>
        /// If you don't care about preserving the order of the elements, `RemoveAtSwapBack` is a more efficient way to remove elements.
        /// </remarks>
        /// <param name="index">The first index of the range to remove.</param>
        /// <param name="count">The number of elements to remove.</param>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public void RemoveRange(int index, int count)
        {
            if (count > 0)
            {
                int copyFrom = math.min(index + count, Length);

                unsafe
                {
                    var sizeOf = UnsafeUtility.SizeOf<T>();
                    void* dst = Buffer + index * sizeOf;
                    void* src = Buffer + copyFrom * sizeOf;
                    UnsafeUtility.MemCpy(dst, src, (Length - copyFrom) * sizeOf);
                }

                Length -= count;
            }
        }

        /// <summary>
        /// Removes *N* elements of a range. Shifts everything above the range down by *N* and decrements the length by *N*.
        /// </summary>
        /// <param name="begin">The first index of the item to remove.</param>
        /// <param name="end">The index past-the-last item to remove.</param>
        /// <remarks>
        /// This method of removing item(s) is useful only in case when list is ordered and user wants to preserve order
        /// in list after removal In majority of cases is not important and user should use more performant `RemoveRangeSwapBackWithBeginEnd`.
        /// </remarks>
        /// <exception cref="ArgumentException">Thrown if end argument is less than begin argument.</exception>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if begin or end arguments are not positive or out of bounds.</exception>
        [Obsolete("RemoveRangeWithBeginEnd(begin, end) is deprecated, use RemoveRange(index, count) instead. (RemovedAfter 2021-06-02)", false)]
        public void RemoveRangeWithBeginEnd(int begin, int end) => RemoveRange(begin, end - begin);

        /// <summary>
        /// Returns a managed array that is a copy of this list.
        /// </summary>
        /// <returns>A managed array that is a copy of this list.</returns>
        [NotBurstCompatible]
        public T[] ToArray()
        {
            var result = new T[Length];
            unsafe
            {
                byte* s = Buffer;
                fixed(T* d = result)
                    UnsafeUtility.MemCpy(d, s, LengthInBytes);
            }
            return result;
        }

        /// <summary>
        /// Returns an array that is a copy of this list.
        /// </summary>
        /// <param name="allocator">The allocator to use.</param>
        /// <returns>An array that is a copy of this list.</returns>
        public NativeArray<T> ToNativeArray(AllocatorManager.AllocatorHandle allocator)
        {
            unsafe
            {
                var copy = CollectionHelper.CreateNativeArray<T>(Length, allocator, NativeArrayOptions.UninitializedMemory);
                UnsafeUtility.MemCpy(copy.GetUnsafePtr(), Buffer, LengthInBytes);
                return copy;
            }
        }


        /// <summary>
        /// Returns true if two lists are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for equality.</param>
        /// <param name="b">The second list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public static bool operator ==(in FixedList512Bytes<T> a, in FixedList32Bytes<T> b)
        {
            unsafe
            {
                if(a.length != b.length)
                    return false;
                return UnsafeUtility.MemCmp(a.Buffer, b.Buffer, a.LengthInBytes) == 0;
            }
        }

        /// <summary>
        /// Returns true if two lists are unequal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for inequality.</param>
        /// <param name="b">The second list to compare for inequality.</param>
        /// <returns>True if the two lists are unequal.</returns>
        public static bool operator !=(in FixedList512Bytes<T> a, in FixedList32Bytes<T> b)
        {
            return !(a == b);
        }

        /// <summary>
        /// Returns a number denoting whether this list should be placed before or after another list in a sort.
        /// </summary>
        /// <param name="other">A list to to compare with.</param>
        /// <returns>An integer denoting the respective sort position of the list relative to the other:
        ///
        /// 0 denotes that both lists should have the same position in a sort.
        /// -1 denotes that this list should precede the other list in a sort.
        /// +1 denotes that this list should follow the other list in a sort.
        /// </returns>
        public int CompareTo(FixedList32Bytes<T> other)
        {
            unsafe
            {
                fixed(byte* a = &buffer.offset0000.byte0000)
                {
                    byte* b = &other.buffer.offset0000.byte0000;
                    var aa = a + FixedList.PaddingBytes<T>();
                    var bb = b + FixedList.PaddingBytes<T>();
                    var mini = math.min(Length, other.Length);
                    for(var i = 0; i < mini; ++i)
                    {
                        var j = UnsafeUtility.MemCmp(aa + sizeof(T) * i, bb + sizeof(T) * i, sizeof(T));
                        if(j != 0)
                            return j;
                    }
                    return Length.CompareTo(other.Length);
                }
            }
        }

        /// <summary>
        /// Returns true if this list and another list are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="other">The list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public bool Equals(FixedList32Bytes<T> other)
        {
            return CompareTo(other) == 0;
        }


        /// <summary>
        /// Initializes and returns an instance of FixedList512Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList512Bytes&lt;T&gt;.</exception>
        public FixedList512Bytes(in FixedList32Bytes<T> other)
        {
            this = default;
            var error = Initialize(other);
            if(error != 0)
                FixedList.CheckResize<FixedBytes510,T>(other.Length);
        }

        /// <summary>
        /// Initializes an instance of FixedList512Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>zero on success, or non-zero on error.</returns>
        internal int Initialize(in FixedList32Bytes<T> other)
        {
            if(other.Length > Capacity)
                return (int)CopyError.Truncation;
            length = other.length;
            buffer = new FixedBytes510();
            unsafe
            {
                UnsafeUtility.MemCpy(Buffer, other.Buffer, LengthInBytes);
            }
            return 0;
        }

        /// <summary>
        /// Returns a new list that is a copy of another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>A new list that is a copy of the other.</returns>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList512Bytes&lt;T&gt;.</exception>
        public static implicit operator FixedList512Bytes<T>(in FixedList32Bytes<T> other)
        {
            return new FixedList512Bytes<T>(other);
        }

        /// <summary>
        /// Returns true if two lists are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for equality.</param>
        /// <param name="b">The second list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public static bool operator ==(in FixedList512Bytes<T> a, in FixedList64Bytes<T> b)
        {
            unsafe
            {
                if(a.length != b.length)
                    return false;
                return UnsafeUtility.MemCmp(a.Buffer, b.Buffer, a.LengthInBytes) == 0;
            }
        }

        /// <summary>
        /// Returns true if two lists are unequal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for inequality.</param>
        /// <param name="b">The second list to compare for inequality.</param>
        /// <returns>True if the two lists are unequal.</returns>
        public static bool operator !=(in FixedList512Bytes<T> a, in FixedList64Bytes<T> b)
        {
            return !(a == b);
        }

        /// <summary>
        /// Returns a number denoting whether this list should be placed before or after another list in a sort.
        /// </summary>
        /// <param name="other">A list to to compare with.</param>
        /// <returns>An integer denoting the respective sort position of the list relative to the other:
        ///
        /// 0 denotes that both lists should have the same position in a sort.
        /// -1 denotes that this list should precede the other list in a sort.
        /// +1 denotes that this list should follow the other list in a sort.
        /// </returns>
        public int CompareTo(FixedList64Bytes<T> other)
        {
            unsafe
            {
                fixed(byte* a = &buffer.offset0000.byte0000)
                {
                    byte* b = &other.buffer.offset0000.byte0000;
                    var aa = a + FixedList.PaddingBytes<T>();
                    var bb = b + FixedList.PaddingBytes<T>();
                    var mini = math.min(Length, other.Length);
                    for(var i = 0; i < mini; ++i)
                    {
                        var j = UnsafeUtility.MemCmp(aa + sizeof(T) * i, bb + sizeof(T) * i, sizeof(T));
                        if(j != 0)
                            return j;
                    }
                    return Length.CompareTo(other.Length);
                }
            }
        }

        /// <summary>
        /// Returns true if this list and another list are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="other">The list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public bool Equals(FixedList64Bytes<T> other)
        {
            return CompareTo(other) == 0;
        }


        /// <summary>
        /// Initializes and returns an instance of FixedList512Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList512Bytes&lt;T&gt;.</exception>
        public FixedList512Bytes(in FixedList64Bytes<T> other)
        {
            this = default;
            var error = Initialize(other);
            if(error != 0)
                FixedList.CheckResize<FixedBytes510,T>(other.Length);
        }

        /// <summary>
        /// Initializes an instance of FixedList512Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>zero on success, or non-zero on error.</returns>
        internal int Initialize(in FixedList64Bytes<T> other)
        {
            if(other.Length > Capacity)
                return (int)CopyError.Truncation;
            length = other.length;
            buffer = new FixedBytes510();
            unsafe
            {
                UnsafeUtility.MemCpy(Buffer, other.Buffer, LengthInBytes);
            }
            return 0;
        }

        /// <summary>
        /// Returns a new list that is a copy of another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>A new list that is a copy of the other.</returns>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList512Bytes&lt;T&gt;.</exception>
        public static implicit operator FixedList512Bytes<T>(in FixedList64Bytes<T> other)
        {
            return new FixedList512Bytes<T>(other);
        }

        /// <summary>
        /// Returns true if two lists are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for equality.</param>
        /// <param name="b">The second list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public static bool operator ==(in FixedList512Bytes<T> a, in FixedList128Bytes<T> b)
        {
            unsafe
            {
                if(a.length != b.length)
                    return false;
                return UnsafeUtility.MemCmp(a.Buffer, b.Buffer, a.LengthInBytes) == 0;
            }
        }

        /// <summary>
        /// Returns true if two lists are unequal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for inequality.</param>
        /// <param name="b">The second list to compare for inequality.</param>
        /// <returns>True if the two lists are unequal.</returns>
        public static bool operator !=(in FixedList512Bytes<T> a, in FixedList128Bytes<T> b)
        {
            return !(a == b);
        }

        /// <summary>
        /// Returns a number denoting whether this list should be placed before or after another list in a sort.
        /// </summary>
        /// <param name="other">A list to to compare with.</param>
        /// <returns>An integer denoting the respective sort position of the list relative to the other:
        ///
        /// 0 denotes that both lists should have the same position in a sort.
        /// -1 denotes that this list should precede the other list in a sort.
        /// +1 denotes that this list should follow the other list in a sort.
        /// </returns>
        public int CompareTo(FixedList128Bytes<T> other)
        {
            unsafe
            {
                fixed(byte* a = &buffer.offset0000.byte0000)
                {
                    byte* b = &other.buffer.offset0000.byte0000;
                    var aa = a + FixedList.PaddingBytes<T>();
                    var bb = b + FixedList.PaddingBytes<T>();
                    var mini = math.min(Length, other.Length);
                    for(var i = 0; i < mini; ++i)
                    {
                        var j = UnsafeUtility.MemCmp(aa + sizeof(T) * i, bb + sizeof(T) * i, sizeof(T));
                        if(j != 0)
                            return j;
                    }
                    return Length.CompareTo(other.Length);
                }
            }
        }

        /// <summary>
        /// Returns true if this list and another list are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="other">The list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public bool Equals(FixedList128Bytes<T> other)
        {
            return CompareTo(other) == 0;
        }


        /// <summary>
        /// Initializes and returns an instance of FixedList512Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList512Bytes&lt;T&gt;.</exception>
        public FixedList512Bytes(in FixedList128Bytes<T> other)
        {
            this = default;
            var error = Initialize(other);
            if(error != 0)
                FixedList.CheckResize<FixedBytes510,T>(other.Length);
        }

        /// <summary>
        /// Initializes an instance of FixedList512Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>zero on success, or non-zero on error.</returns>
        internal int Initialize(in FixedList128Bytes<T> other)
        {
            if(other.Length > Capacity)
                return (int)CopyError.Truncation;
            length = other.length;
            buffer = new FixedBytes510();
            unsafe
            {
                UnsafeUtility.MemCpy(Buffer, other.Buffer, LengthInBytes);
            }
            return 0;
        }

        /// <summary>
        /// Returns a new list that is a copy of another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>A new list that is a copy of the other.</returns>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList512Bytes&lt;T&gt;.</exception>
        public static implicit operator FixedList512Bytes<T>(in FixedList128Bytes<T> other)
        {
            return new FixedList512Bytes<T>(other);
        }

        /// <summary>
        /// Returns true if two lists are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for equality.</param>
        /// <param name="b">The second list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public static bool operator ==(in FixedList512Bytes<T> a, in FixedList512Bytes<T> b)
        {
            unsafe
            {
                if(a.length != b.length)
                    return false;
                return UnsafeUtility.MemCmp(a.Buffer, b.Buffer, a.LengthInBytes) == 0;
            }
        }

        /// <summary>
        /// Returns true if two lists are unequal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for inequality.</param>
        /// <param name="b">The second list to compare for inequality.</param>
        /// <returns>True if the two lists are unequal.</returns>
        public static bool operator !=(in FixedList512Bytes<T> a, in FixedList512Bytes<T> b)
        {
            return !(a == b);
        }

        /// <summary>
        /// Returns a number denoting whether this list should be placed before or after another list in a sort.
        /// </summary>
        /// <param name="other">A list to to compare with.</param>
        /// <returns>An integer denoting the respective sort position of the list relative to the other:
        ///
        /// 0 denotes that both lists should have the same position in a sort.
        /// -1 denotes that this list should precede the other list in a sort.
        /// +1 denotes that this list should follow the other list in a sort.
        /// </returns>
        public int CompareTo(FixedList512Bytes<T> other)
        {
            unsafe
            {
                fixed(byte* a = &buffer.offset0000.byte0000)
                {
                    byte* b = &other.buffer.offset0000.byte0000;
                    var aa = a + FixedList.PaddingBytes<T>();
                    var bb = b + FixedList.PaddingBytes<T>();
                    var mini = math.min(Length, other.Length);
                    for(var i = 0; i < mini; ++i)
                    {
                        var j = UnsafeUtility.MemCmp(aa + sizeof(T) * i, bb + sizeof(T) * i, sizeof(T));
                        if(j != 0)
                            return j;
                    }
                    return Length.CompareTo(other.Length);
                }
            }
        }

        /// <summary>
        /// Returns true if this list and another list are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="other">The list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public bool Equals(FixedList512Bytes<T> other)
        {
            return CompareTo(other) == 0;
        }


        /// <summary>
        /// Returns true if two lists are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for equality.</param>
        /// <param name="b">The second list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public static bool operator ==(in FixedList512Bytes<T> a, in FixedList4096Bytes<T> b)
        {
            unsafe
            {
                if(a.length != b.length)
                    return false;
                return UnsafeUtility.MemCmp(a.Buffer, b.Buffer, a.LengthInBytes) == 0;
            }
        }

        /// <summary>
        /// Returns true if two lists are unequal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for inequality.</param>
        /// <param name="b">The second list to compare for inequality.</param>
        /// <returns>True if the two lists are unequal.</returns>
        public static bool operator !=(in FixedList512Bytes<T> a, in FixedList4096Bytes<T> b)
        {
            return !(a == b);
        }

        /// <summary>
        /// Returns a number denoting whether this list should be placed before or after another list in a sort.
        /// </summary>
        /// <param name="other">A list to to compare with.</param>
        /// <returns>An integer denoting the respective sort position of the list relative to the other:
        ///
        /// 0 denotes that both lists should have the same position in a sort.
        /// -1 denotes that this list should precede the other list in a sort.
        /// +1 denotes that this list should follow the other list in a sort.
        /// </returns>
        public int CompareTo(FixedList4096Bytes<T> other)
        {
            unsafe
            {
                fixed(byte* a = &buffer.offset0000.byte0000)
                {
                    byte* b = &other.buffer.offset0000.byte0000;
                    var aa = a + FixedList.PaddingBytes<T>();
                    var bb = b + FixedList.PaddingBytes<T>();
                    var mini = math.min(Length, other.Length);
                    for(var i = 0; i < mini; ++i)
                    {
                        var j = UnsafeUtility.MemCmp(aa + sizeof(T) * i, bb + sizeof(T) * i, sizeof(T));
                        if(j != 0)
                            return j;
                    }
                    return Length.CompareTo(other.Length);
                }
            }
        }

        /// <summary>
        /// Returns true if this list and another list are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="other">The list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public bool Equals(FixedList4096Bytes<T> other)
        {
            return CompareTo(other) == 0;
        }


        /// <summary>
        /// Initializes and returns an instance of FixedList512Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList512Bytes&lt;T&gt;.</exception>
        public FixedList512Bytes(in FixedList4096Bytes<T> other)
        {
            this = default;
            var error = Initialize(other);
            if(error != 0)
                FixedList.CheckResize<FixedBytes510,T>(other.Length);
        }

        /// <summary>
        /// Initializes an instance of FixedList512Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>zero on success, or non-zero on error.</returns>
        internal int Initialize(in FixedList4096Bytes<T> other)
        {
            if(other.Length > Capacity)
                return (int)CopyError.Truncation;
            length = other.length;
            buffer = new FixedBytes510();
            unsafe
            {
                UnsafeUtility.MemCpy(Buffer, other.Buffer, LengthInBytes);
            }
            return 0;
        }

        /// <summary>
        /// Returns a new list that is a copy of another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>A new list that is a copy of the other.</returns>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList512Bytes&lt;T&gt;.</exception>
        public static implicit operator FixedList512Bytes<T>(in FixedList4096Bytes<T> other)
        {
            return new FixedList512Bytes<T>(other);
        }

        /// <summary>
        /// Returns true if the list is equal to an object.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.
        ///
        /// A FixedList*N*&lt;T&gt; can only be equal to another FixedList*N*&lt;T&gt; with the same *N* and T.
        /// </remarks>
        /// <param name="obj">An object to compare for equality.</param>
        /// <returns>True if the list is equal to the object.</returns>
        [NotBurstCompatible]
        public override bool Equals(object obj)
        {
            if(obj is FixedList32Bytes<T> aFixedList32Bytes) return Equals(aFixedList32Bytes);
            if(obj is FixedList64Bytes<T> aFixedList64Bytes) return Equals(aFixedList64Bytes);
            if(obj is FixedList128Bytes<T> aFixedList128Bytes) return Equals(aFixedList128Bytes);
            if(obj is FixedList512Bytes<T> aFixedList512Bytes) return Equals(aFixedList512Bytes);
            if(obj is FixedList4096Bytes<T> aFixedList4096Bytes) return Equals(aFixedList4096Bytes);
            return false;
        }

        /// <summary>
        /// An enumerator over the elements of a FixedList512Bytes&lt;T&gt;.
        /// </summary>
        /// <remarks>
        /// In an enumerator's initial state, `Current` cannot be read. The first <see cref="MoveNext"/> call advances the enumerator to the first element.
        /// </remarks>
        public struct Enumerator : IEnumerator<T>
        {
            FixedList512Bytes<T> m_List;
            int m_Index;

            /// <summary>
            /// Initializes and returns an instance of FixedList512Bytes&lt;T&gt;.
            /// </summary>
            /// <param name="list">The list for which to create an enumerator.</param>
            public Enumerator(ref FixedList512Bytes<T> list)
            {
                m_List = list;
                m_Index = -1;
            }

            /// <summary>
            /// Does nothing.
            /// </summary>
            public void Dispose()
            {
            }

            /// <summary>
            /// Advances the enumerator to the next element.
            /// </summary>
            /// <returns>True if <see cref="Current"/> is valid to read after the call.</returns>
            public bool MoveNext()
            {
                m_Index++;
                return m_Index < m_List.Length;
            }

            /// <summary>
            /// Resets the enumerator to its initial state.
            /// </summary>
            public void Reset()
            {
                m_Index = -1;
            }

            /// <summary>
            /// The current element.
            /// </summary>
            /// <value>The current element.</value>
            public T Current => m_List[m_Index]; // Let FixedList512Bytes<T> indexer check for out of range.

            object IEnumerator.Current => Current;
        }

        /// <summary>
        /// Returns an enumerator for iterating over the elements of this list.
        /// </summary>
        /// <returns>An enumerator for iterating over the elements of this list.</returns>
        public Enumerator GetEnumerator()
        {
            return new Enumerator(ref this);
        }

        /// <summary>
        /// This method is not implemented. Use <see cref="GetEnumerator"/> instead.
        /// </summary>
        /// <returns>Nothing because it always throws <see cref="NotImplementedException"/>.</returns>
        /// <exception cref="NotImplementedException">Method is not implemented.</exception>
        IEnumerator IEnumerable.GetEnumerator()
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// This method is not implemented. Use <see cref="GetEnumerator"/> instead.
        /// </summary>
        /// <returns>Nothing because it always throws <see cref="NotImplementedException"/>.</returns>
        /// <exception cref="NotImplementedException">Method is not implemented.</exception>
        IEnumerator<T> IEnumerable<T>.GetEnumerator()
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Provides extension methods for FixedList512Bytes.
    /// </summary>
    [BurstCompatible]
    public unsafe static class FixedList512BytesExtensions
    {
        /// <summary>
        /// Finds the index of the first occurrence of a particular value in this list.
        /// </summary>
        /// <typeparam name="T">The type of elements in this list.</typeparam>
        /// <typeparam name="U">The value type.</typeparam>
        /// <param name="list">The list to search.</param>
        /// <param name="value">The value to locate.</param>
        /// <returns>The index of the first occurrence of the value. Returns -1 if no occurrence is found.</returns>
        [BurstCompatible(GenericTypeArguments = new [] { typeof(int), typeof(int) })]
        public static int IndexOf<T, U>(this ref FixedList512Bytes<T> list, U value) where T : unmanaged, IEquatable<U>
        {
            return NativeArrayExtensions.IndexOf<T, U>(list.Buffer, list.Length, value);
        }

        /// <summary>
        /// Returns true if a particular value is present in this list.
        /// </summary>
        /// <typeparam name="T">The type of elements in this list.</typeparam>
        /// <typeparam name="U">The value type.</typeparam>
        /// <param name="list">The list to search.</param>
        /// <param name="value">The value to locate.</param>
        /// <returns>True if the value is present in this list.</returns>
        [BurstCompatible(GenericTypeArguments = new [] { typeof(int), typeof(int) })]
        public static bool Contains<T, U>(this ref FixedList512Bytes<T> list, U value) where T : unmanaged, IEquatable<U>
        {
            return list.IndexOf(value) != -1;
        }

        /// <summary>
        /// Removes the first occurrence of a particular value in this list.
        /// </summary>
        /// <remarks>
        /// If a value is removed, all elements after it are shifted down by one, and the list's length is decremented by one.
        ///
        /// If you don't need to preserve the order of the remaining elements, <see cref="Unity.Collections.FixedList512BytesExtensions.RemoveSwapBack{T, U}"/> is a cheaper alternative.
        /// </remarks>
        /// <typeparam name="T">The type of elements in this list.</typeparam>
        /// <typeparam name="U">The value type.</typeparam>
        /// <param name="list">The list to search.</param>
        /// <param name="value">The value to locate and remove.</param>
        /// <returns>True if the value was found and removed.</returns>
        [BurstCompatible(GenericTypeArguments = new [] { typeof(int), typeof(int) })]
        public static bool Remove<T, U>(this ref FixedList512Bytes<T> list, U value) where T : unmanaged, IEquatable<U>
        {
            int index = list.IndexOf(value);
            if (index < 0)
            {
                return false;
            }

            list.RemoveAt(index);

            return true;
        }

        /// <summary>
        /// Removes the first occurrence of a particular value in this list.
        /// </summary>
        /// <remarks>
        /// If a value is removed, the last element of the list is copied to overwrite the removed value, and the list's length is decremented by one.
        ///
        /// This is cheaper than <see cref="Remove"/>, but the order of the remaining elements is not preserved.
        /// </remarks>
        /// <typeparam name="T">The type of elements in this list.</typeparam>
        /// <typeparam name="U">The value type.</typeparam>
        /// <param name="list">The list to search.</param>
        /// <param name="value">The value to locate and remove.</param>
        /// <returns>Returns true if the item is removed.</returns>
        [BurstCompatible(GenericTypeArguments = new [] { typeof(int), typeof(int) })]
        public static bool RemoveSwapBack<T, U>(this ref FixedList512Bytes<T> list, U value) where T : unmanaged, IEquatable<U>
        {
            var index = list.IndexOf(value);
            if (index == -1)
            {
                return false;
            }

            list.RemoveAtSwapBack(index);

            return true;
        }
    }

    sealed class FixedList512BytesDebugView<T> where T : unmanaged
    {
        FixedList512Bytes<T> m_List;
        public FixedList512BytesDebugView(FixedList512Bytes<T> list)
        {
            m_List = list;
        }
        public T[] Items => m_List.ToArray();
    }
    [Obsolete("Renamed to FixedList4096Bytes<T> (UnityUpgradable) -> FixedList4096Bytes<T>", true)]
    public struct FixedList4096<T> where T : unmanaged {}

    /// <summary>
    /// An unmanaged, resizable list whose content is all stored directly in the 4096-byte struct. Useful for small lists.
    /// </summary>
    /// <typeparam name="T">The type of the elements.</typeparam>
    [Serializable]
    [DebuggerTypeProxy(typeof(FixedList4096BytesDebugView<>))]
    [BurstCompatible(GenericTypeArguments = new [] { typeof(int) })]
    public struct FixedList4096Bytes<T>
    : INativeList<T>
    , IEnumerable<T> // Used by collection initializers.
    , IEquatable<FixedList32Bytes<T>>
    , IComparable<FixedList32Bytes<T>>
    , IEquatable<FixedList64Bytes<T>>
    , IComparable<FixedList64Bytes<T>>
    , IEquatable<FixedList128Bytes<T>>
    , IComparable<FixedList128Bytes<T>>
    , IEquatable<FixedList512Bytes<T>>
    , IComparable<FixedList512Bytes<T>>
    , IEquatable<FixedList4096Bytes<T>>
    , IComparable<FixedList4096Bytes<T>>
    where T : unmanaged
    {
        [SerializeField] internal ushort length;
        [SerializeField] internal FixedBytes4094 buffer;

        /// <summary>
        /// The current number of items in this list.
        /// </summary>
        /// <value>The current number of items in this list.</value>
        [CreateProperty]
        public int Length
        {
            get => length;
            set
            {
                FixedList.CheckResize<FixedBytes4094,T>(value);
                length = (ushort)value;
            }
        }

        /// <summary>
        /// A property in order to display items in the Entity Inspector.
        /// </summary>
        [CreateProperty] IEnumerable<T> Elements => this.ToArray();

        /// <summary>
        /// Whether this list is empty.
        /// </summary>
        /// <value>True if this string has no characters or if the container has not been constructed.</value>
        public bool IsEmpty => Length == 0;

        internal int LengthInBytes => Length * UnsafeUtility.SizeOf<T>();

        unsafe internal byte* Buffer
        {
            get
            {
                fixed(byte* b = &buffer.offset0000.byte0000)
                    return b + FixedList.PaddingBytes<T>();
            }
        }

        /// <summary>
        /// The number of elements that can fit in this list.
        /// </summary>
        /// <value>The number of elements that can fit in this list.</value>
        /// <remarks>The capacity of a FixedList cannot be changed. The setter is included only for conformity with <see cref="INativeList{T}"/>.</remarks>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the new value does not match the current capacity.</exception>
        public int Capacity
        {
          get
          {
              return FixedList.Capacity<FixedBytes4094,T>();
          }

          set
          {
              CollectionHelper.CheckCapacityInRange(value, Length);
          }
        }

        /// <summary>
        /// The element at a given index.
        /// </summary>
        /// <param name="index">An index.</param>
        /// <value>The value to store at the index.</value>
        /// <exception cref="IndexOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public T this[int index]
        {
            get
            {
                CollectionHelper.CheckIndexInRange(index, length);
                unsafe
                {
                    return UnsafeUtility.ReadArrayElement<T>(Buffer, CollectionHelper.AssumePositive(index));
                }
            }

            set
            {
                CollectionHelper.CheckIndexInRange(index, length);
                unsafe
                {
                    UnsafeUtility.WriteArrayElement<T>(Buffer, CollectionHelper.AssumePositive(index), value);
                }
            }
        }

        /// <summary>
        /// Returns the element at a given index.
        /// </summary>
        /// <param name="index">An index.</param>
        /// <returns>The list element at the index.</returns>
        public ref T ElementAt(int index)
        {
            CollectionHelper.CheckIndexInRange(index, length);
            unsafe
            {
                return ref UnsafeUtility.ArrayElementAsRef<T>(Buffer, index);
            }
        }

        /// <summary>
        /// Returns the hash code of this list.
        /// </summary>
        /// <remarks>
        /// Only the content of the list (the bytes of the elements) are included in the hash. Any bytes beyond the length are not part of the hash.</remarks>
        /// <returns>The hash code of this list.</returns>
        public override int GetHashCode()
        {
            unsafe
            {
                return (int)CollectionHelper.Hash(Buffer, LengthInBytes);
            }
        }

        /// <summary>
        /// Appends an element to the end of this list. Increments the length by 1.
        /// </summary>
        /// <remarks>The same as <see cref="AddNoResize"/>. Remember that a fixed list is never resized.</remarks>
        /// <param name="item">The element to append at the end of the list.</param>
        /// <exception cref="IndexOutOfRangeException">Thrown if the append exceeds the capacity.</exception>
        public void Add(in T item)
        {
            this[Length++] = item;
        }

        /// <summary>
        /// Appends elements from a buffer to the end of this list. Increments the length by the number of appended elements.
        /// </summary>
        /// <remarks>The same as <see cref="AddRangeNoResize"/>. Remember that a fixed list is never resized.</remarks>
        /// <param name="ptr">A buffer.</param>
        /// <param name="length">The number of elements from the buffer to append.</param>
        /// <exception cref="IndexOutOfRangeException">Thrown if the append exceeds the capacity.</exception>
        public unsafe void AddRange(void* ptr, int length)
        {
            T* data = (T*)ptr;
            for (var i = 0; i < length; ++i)
            {
                this[Length++] = data[i];
            }
        }

        /// <summary>
        /// Appends an element to the end of this list. Increments the length by 1.
        /// </summary>
        /// <remarks>The same as <see cref="Add"/>. Included only for consistency with the other list types.</remarks>
        /// <param name="item">The element to append at the end of the list.</param>
        /// <exception cref="IndexOutOfRangeException">Thrown if the append exceeds the capacity.</exception>
        public void AddNoResize(in T item) => Add(item);

        /// <summary>
        /// Appends elements from a buffer to the end of this list. Increments the length by the number of appended elements.
        /// </summary>
        /// <remarks>The same as <see cref="AddRange"/>. Included only for consistency with the other list types.</remarks>
        /// <param name="ptr">A buffer.</param>
        /// <param name="length">The number of elements from the buffer to append.</param>
        /// <exception cref="IndexOutOfRangeException">Thrown if the append exceeds the capacity.</exception>
        public unsafe void AddRangeNoResize(void* ptr, int length) => AddRange(ptr, length);

        /// <summary>
        /// Sets the length to 0.
        /// </summary>
        /// <remarks> Does *not* zero out the bytes.</remarks>
        public void Clear()
        {
            Length = 0;
        }

        /// <summary>
        /// Shifts elements toward the end of this list, increasing its length.
        /// </summary>
        /// <remarks>
        /// Right-shifts elements in the list so as to create 'free' slots at the beginning or in the middle.
        ///
        /// The length is increased by `end - begin`.
        ///
        /// If `end` equals `begin`, the method does nothing.
        ///
        /// The element at index `begin` will be copied to index `end`, the element at index `begin + 1` will be copied to `end + 1`, and so forth.
        ///
        /// The indexes `begin` up to `end` are not cleared: they will contain whatever values they held prior.
        /// </remarks>
        /// <param name="begin">The index of the first element that will be shifted up.</param>
        /// <param name="end">The index where the first shifted element will end up.</param>
        /// <exception cref="IndexOutOfRangeException">Thrown if the new length exceeds the capacity.</exception>
        public void InsertRangeWithBeginEnd(int begin, int end)
        {
            int items = end - begin;
            if(items < 1)
                return;
            int itemsToCopy = length - begin;
            Length += items;
            if(itemsToCopy < 1)
                return;
            int bytesToCopy = itemsToCopy * UnsafeUtility.SizeOf<T>();
            unsafe
            {
                byte *b = Buffer;
                byte *dest = b + end * UnsafeUtility.SizeOf<T>();
                byte *src = b + begin * UnsafeUtility.SizeOf<T>();
                UnsafeUtility.MemMove(dest, src, bytesToCopy);
            }
        }

        /// <summary>
        /// Inserts a single element at an index. Increments the length by 1.
        /// </summary>
        /// <param name="index">The index at which to insert the element.</param>
        /// <param name="item">The element to insert.</param>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public void Insert(int index, in T item)
        {
            InsertRangeWithBeginEnd(index, index+1);
            this[index] = item;
        }

        /// <summary>
        /// Copies the last element of this list to an index. Decrements the length by 1.
        /// </summary>
        /// <remarks>Useful as a cheap way to remove elements from a list when you don't care about preserving order.</remarks>
        /// <param name="index">The index to overwrite with the last element.</param>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public void RemoveAtSwapBack(int index)
        {
            RemoveRangeSwapBack(index, 1);
        }

        /// <summary>
        /// Copies the last *N* elements of this list to a range in this list. Decrements the length by *N*.
        /// </summary>
        /// <remarks>
        /// Copies the last `count`-numbered elements to the range starting at `index`.
        ///
        /// Useful as a cheap way to remove elements from a list when you don't care about preserving order.
        ///
        /// Does nothing if the count is less than 1.
        /// </remarks>
        /// <param name="index">The first index of the destination range.</param>
        /// <param name="count">The number of elements to copy and the amount by which to decrement the length.</param>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public void RemoveRangeSwapBack(int index, int count)
        {
            if (count > 0)
            {
                int copyFrom = math.max(Length - count, index + count);

                unsafe
                {
                    var sizeOf = UnsafeUtility.SizeOf<T>();
                    void* dst = Buffer + index * sizeOf;
                    void* src = Buffer + copyFrom * sizeOf;
                    UnsafeUtility.MemCpy(dst, src, (Length - copyFrom) * sizeOf);
                }

                Length -= count;
            }
        }

        /// <summary>
        /// Copies the last *N* elements of this list to a range in this list. Decrements the length by *N*.
        /// </summary>
        /// <param name="begin">The first index of the item to remove.</param>
        /// <param name="end">The index past-the-last item to remove.</param>
        /// <exception cref="ArgumentException">Thrown if end argument is less than begin argument.</exception>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if begin or end arguments are not positive or out of bounds.</exception>
        [Obsolete("RemoveRangeSwapBackWithBeginEnd(begin, end) is deprecated, use RemoveRangeSwapBack(index, count) instead. (RemovedAfter 2021-06-02)", false)]
        public void RemoveRangeSwapBackWithBeginEnd(int begin, int end) => RemoveRangeSwapBack(begin, end - begin);

        /// <summary>
        /// Removes the element at an index. Shifts everything above the index down by one and decrements the length by 1.
        /// </summary>
        /// <param name="index">The index of the element to remove.</param>
        /// <remarks>
        /// If you don't care about preserving the order of the elements, `RemoveAtSwapBack` is a more efficient way to remove an element.
        /// </remarks>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public void RemoveAt(int index)
        {
            RemoveRange(index, 1);
        }

        /// <summary>
        /// Removes *N* elements of a range. Shifts everything above the range down by *N* and decrements the length by *N*.
        /// </summary>
        /// <remarks>
        /// If you don't care about preserving the order of the elements, `RemoveAtSwapBack` is a more efficient way to remove elements.
        /// </remarks>
        /// <param name="index">The first index of the range to remove.</param>
        /// <param name="count">The number of elements to remove.</param>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if the index is out of bounds.</exception>
        public void RemoveRange(int index, int count)
        {
            if (count > 0)
            {
                int copyFrom = math.min(index + count, Length);

                unsafe
                {
                    var sizeOf = UnsafeUtility.SizeOf<T>();
                    void* dst = Buffer + index * sizeOf;
                    void* src = Buffer + copyFrom * sizeOf;
                    UnsafeUtility.MemCpy(dst, src, (Length - copyFrom) * sizeOf);
                }

                Length -= count;
            }
        }

        /// <summary>
        /// Removes *N* elements of a range. Shifts everything above the range down by *N* and decrements the length by *N*.
        /// </summary>
        /// <param name="begin">The first index of the item to remove.</param>
        /// <param name="end">The index past-the-last item to remove.</param>
        /// <remarks>
        /// This method of removing item(s) is useful only in case when list is ordered and user wants to preserve order
        /// in list after removal In majority of cases is not important and user should use more performant `RemoveRangeSwapBackWithBeginEnd`.
        /// </remarks>
        /// <exception cref="ArgumentException">Thrown if end argument is less than begin argument.</exception>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if begin or end arguments are not positive or out of bounds.</exception>
        [Obsolete("RemoveRangeWithBeginEnd(begin, end) is deprecated, use RemoveRange(index, count) instead. (RemovedAfter 2021-06-02)", false)]
        public void RemoveRangeWithBeginEnd(int begin, int end) => RemoveRange(begin, end - begin);

        /// <summary>
        /// Returns a managed array that is a copy of this list.
        /// </summary>
        /// <returns>A managed array that is a copy of this list.</returns>
        [NotBurstCompatible]
        public T[] ToArray()
        {
            var result = new T[Length];
            unsafe
            {
                byte* s = Buffer;
                fixed(T* d = result)
                    UnsafeUtility.MemCpy(d, s, LengthInBytes);
            }
            return result;
        }

        /// <summary>
        /// Returns an array that is a copy of this list.
        /// </summary>
        /// <param name="allocator">The allocator to use.</param>
        /// <returns>An array that is a copy of this list.</returns>
        public NativeArray<T> ToNativeArray(AllocatorManager.AllocatorHandle allocator)
        {
            unsafe
            {
                var copy = CollectionHelper.CreateNativeArray<T>(Length, allocator, NativeArrayOptions.UninitializedMemory);
                UnsafeUtility.MemCpy(copy.GetUnsafePtr(), Buffer, LengthInBytes);
                return copy;
            }
        }


        /// <summary>
        /// Returns true if two lists are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for equality.</param>
        /// <param name="b">The second list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public static bool operator ==(in FixedList4096Bytes<T> a, in FixedList32Bytes<T> b)
        {
            unsafe
            {
                if(a.length != b.length)
                    return false;
                return UnsafeUtility.MemCmp(a.Buffer, b.Buffer, a.LengthInBytes) == 0;
            }
        }

        /// <summary>
        /// Returns true if two lists are unequal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for inequality.</param>
        /// <param name="b">The second list to compare for inequality.</param>
        /// <returns>True if the two lists are unequal.</returns>
        public static bool operator !=(in FixedList4096Bytes<T> a, in FixedList32Bytes<T> b)
        {
            return !(a == b);
        }

        /// <summary>
        /// Returns a number denoting whether this list should be placed before or after another list in a sort.
        /// </summary>
        /// <param name="other">A list to to compare with.</param>
        /// <returns>An integer denoting the respective sort position of the list relative to the other:
        ///
        /// 0 denotes that both lists should have the same position in a sort.
        /// -1 denotes that this list should precede the other list in a sort.
        /// +1 denotes that this list should follow the other list in a sort.
        /// </returns>
        public int CompareTo(FixedList32Bytes<T> other)
        {
            unsafe
            {
                fixed(byte* a = &buffer.offset0000.byte0000)
                {
                    byte* b = &other.buffer.offset0000.byte0000;
                    var aa = a + FixedList.PaddingBytes<T>();
                    var bb = b + FixedList.PaddingBytes<T>();
                    var mini = math.min(Length, other.Length);
                    for(var i = 0; i < mini; ++i)
                    {
                        var j = UnsafeUtility.MemCmp(aa + sizeof(T) * i, bb + sizeof(T) * i, sizeof(T));
                        if(j != 0)
                            return j;
                    }
                    return Length.CompareTo(other.Length);
                }
            }
        }

        /// <summary>
        /// Returns true if this list and another list are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="other">The list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public bool Equals(FixedList32Bytes<T> other)
        {
            return CompareTo(other) == 0;
        }


        /// <summary>
        /// Initializes and returns an instance of FixedList4096Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList4096Bytes&lt;T&gt;.</exception>
        public FixedList4096Bytes(in FixedList32Bytes<T> other)
        {
            this = default;
            var error = Initialize(other);
            if(error != 0)
                FixedList.CheckResize<FixedBytes4094,T>(other.Length);
        }

        /// <summary>
        /// Initializes an instance of FixedList4096Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>zero on success, or non-zero on error.</returns>
        internal int Initialize(in FixedList32Bytes<T> other)
        {
            if(other.Length > Capacity)
                return (int)CopyError.Truncation;
            length = other.length;
            buffer = new FixedBytes4094();
            unsafe
            {
                UnsafeUtility.MemCpy(Buffer, other.Buffer, LengthInBytes);
            }
            return 0;
        }

        /// <summary>
        /// Returns a new list that is a copy of another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>A new list that is a copy of the other.</returns>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList4096Bytes&lt;T&gt;.</exception>
        public static implicit operator FixedList4096Bytes<T>(in FixedList32Bytes<T> other)
        {
            return new FixedList4096Bytes<T>(other);
        }

        /// <summary>
        /// Returns true if two lists are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for equality.</param>
        /// <param name="b">The second list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public static bool operator ==(in FixedList4096Bytes<T> a, in FixedList64Bytes<T> b)
        {
            unsafe
            {
                if(a.length != b.length)
                    return false;
                return UnsafeUtility.MemCmp(a.Buffer, b.Buffer, a.LengthInBytes) == 0;
            }
        }

        /// <summary>
        /// Returns true if two lists are unequal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for inequality.</param>
        /// <param name="b">The second list to compare for inequality.</param>
        /// <returns>True if the two lists are unequal.</returns>
        public static bool operator !=(in FixedList4096Bytes<T> a, in FixedList64Bytes<T> b)
        {
            return !(a == b);
        }

        /// <summary>
        /// Returns a number denoting whether this list should be placed before or after another list in a sort.
        /// </summary>
        /// <param name="other">A list to to compare with.</param>
        /// <returns>An integer denoting the respective sort position of the list relative to the other:
        ///
        /// 0 denotes that both lists should have the same position in a sort.
        /// -1 denotes that this list should precede the other list in a sort.
        /// +1 denotes that this list should follow the other list in a sort.
        /// </returns>
        public int CompareTo(FixedList64Bytes<T> other)
        {
            unsafe
            {
                fixed(byte* a = &buffer.offset0000.byte0000)
                {
                    byte* b = &other.buffer.offset0000.byte0000;
                    var aa = a + FixedList.PaddingBytes<T>();
                    var bb = b + FixedList.PaddingBytes<T>();
                    var mini = math.min(Length, other.Length);
                    for(var i = 0; i < mini; ++i)
                    {
                        var j = UnsafeUtility.MemCmp(aa + sizeof(T) * i, bb + sizeof(T) * i, sizeof(T));
                        if(j != 0)
                            return j;
                    }
                    return Length.CompareTo(other.Length);
                }
            }
        }

        /// <summary>
        /// Returns true if this list and another list are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="other">The list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public bool Equals(FixedList64Bytes<T> other)
        {
            return CompareTo(other) == 0;
        }


        /// <summary>
        /// Initializes and returns an instance of FixedList4096Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList4096Bytes&lt;T&gt;.</exception>
        public FixedList4096Bytes(in FixedList64Bytes<T> other)
        {
            this = default;
            var error = Initialize(other);
            if(error != 0)
                FixedList.CheckResize<FixedBytes4094,T>(other.Length);
        }

        /// <summary>
        /// Initializes an instance of FixedList4096Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>zero on success, or non-zero on error.</returns>
        internal int Initialize(in FixedList64Bytes<T> other)
        {
            if(other.Length > Capacity)
                return (int)CopyError.Truncation;
            length = other.length;
            buffer = new FixedBytes4094();
            unsafe
            {
                UnsafeUtility.MemCpy(Buffer, other.Buffer, LengthInBytes);
            }
            return 0;
        }

        /// <summary>
        /// Returns a new list that is a copy of another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>A new list that is a copy of the other.</returns>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList4096Bytes&lt;T&gt;.</exception>
        public static implicit operator FixedList4096Bytes<T>(in FixedList64Bytes<T> other)
        {
            return new FixedList4096Bytes<T>(other);
        }

        /// <summary>
        /// Returns true if two lists are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for equality.</param>
        /// <param name="b">The second list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public static bool operator ==(in FixedList4096Bytes<T> a, in FixedList128Bytes<T> b)
        {
            unsafe
            {
                if(a.length != b.length)
                    return false;
                return UnsafeUtility.MemCmp(a.Buffer, b.Buffer, a.LengthInBytes) == 0;
            }
        }

        /// <summary>
        /// Returns true if two lists are unequal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for inequality.</param>
        /// <param name="b">The second list to compare for inequality.</param>
        /// <returns>True if the two lists are unequal.</returns>
        public static bool operator !=(in FixedList4096Bytes<T> a, in FixedList128Bytes<T> b)
        {
            return !(a == b);
        }

        /// <summary>
        /// Returns a number denoting whether this list should be placed before or after another list in a sort.
        /// </summary>
        /// <param name="other">A list to to compare with.</param>
        /// <returns>An integer denoting the respective sort position of the list relative to the other:
        ///
        /// 0 denotes that both lists should have the same position in a sort.
        /// -1 denotes that this list should precede the other list in a sort.
        /// +1 denotes that this list should follow the other list in a sort.
        /// </returns>
        public int CompareTo(FixedList128Bytes<T> other)
        {
            unsafe
            {
                fixed(byte* a = &buffer.offset0000.byte0000)
                {
                    byte* b = &other.buffer.offset0000.byte0000;
                    var aa = a + FixedList.PaddingBytes<T>();
                    var bb = b + FixedList.PaddingBytes<T>();
                    var mini = math.min(Length, other.Length);
                    for(var i = 0; i < mini; ++i)
                    {
                        var j = UnsafeUtility.MemCmp(aa + sizeof(T) * i, bb + sizeof(T) * i, sizeof(T));
                        if(j != 0)
                            return j;
                    }
                    return Length.CompareTo(other.Length);
                }
            }
        }

        /// <summary>
        /// Returns true if this list and another list are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="other">The list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public bool Equals(FixedList128Bytes<T> other)
        {
            return CompareTo(other) == 0;
        }


        /// <summary>
        /// Initializes and returns an instance of FixedList4096Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList4096Bytes&lt;T&gt;.</exception>
        public FixedList4096Bytes(in FixedList128Bytes<T> other)
        {
            this = default;
            var error = Initialize(other);
            if(error != 0)
                FixedList.CheckResize<FixedBytes4094,T>(other.Length);
        }

        /// <summary>
        /// Initializes an instance of FixedList4096Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>zero on success, or non-zero on error.</returns>
        internal int Initialize(in FixedList128Bytes<T> other)
        {
            if(other.Length > Capacity)
                return (int)CopyError.Truncation;
            length = other.length;
            buffer = new FixedBytes4094();
            unsafe
            {
                UnsafeUtility.MemCpy(Buffer, other.Buffer, LengthInBytes);
            }
            return 0;
        }

        /// <summary>
        /// Returns a new list that is a copy of another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>A new list that is a copy of the other.</returns>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList4096Bytes&lt;T&gt;.</exception>
        public static implicit operator FixedList4096Bytes<T>(in FixedList128Bytes<T> other)
        {
            return new FixedList4096Bytes<T>(other);
        }

        /// <summary>
        /// Returns true if two lists are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for equality.</param>
        /// <param name="b">The second list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public static bool operator ==(in FixedList4096Bytes<T> a, in FixedList512Bytes<T> b)
        {
            unsafe
            {
                if(a.length != b.length)
                    return false;
                return UnsafeUtility.MemCmp(a.Buffer, b.Buffer, a.LengthInBytes) == 0;
            }
        }

        /// <summary>
        /// Returns true if two lists are unequal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for inequality.</param>
        /// <param name="b">The second list to compare for inequality.</param>
        /// <returns>True if the two lists are unequal.</returns>
        public static bool operator !=(in FixedList4096Bytes<T> a, in FixedList512Bytes<T> b)
        {
            return !(a == b);
        }

        /// <summary>
        /// Returns a number denoting whether this list should be placed before or after another list in a sort.
        /// </summary>
        /// <param name="other">A list to to compare with.</param>
        /// <returns>An integer denoting the respective sort position of the list relative to the other:
        ///
        /// 0 denotes that both lists should have the same position in a sort.
        /// -1 denotes that this list should precede the other list in a sort.
        /// +1 denotes that this list should follow the other list in a sort.
        /// </returns>
        public int CompareTo(FixedList512Bytes<T> other)
        {
            unsafe
            {
                fixed(byte* a = &buffer.offset0000.byte0000)
                {
                    byte* b = &other.buffer.offset0000.byte0000;
                    var aa = a + FixedList.PaddingBytes<T>();
                    var bb = b + FixedList.PaddingBytes<T>();
                    var mini = math.min(Length, other.Length);
                    for(var i = 0; i < mini; ++i)
                    {
                        var j = UnsafeUtility.MemCmp(aa + sizeof(T) * i, bb + sizeof(T) * i, sizeof(T));
                        if(j != 0)
                            return j;
                    }
                    return Length.CompareTo(other.Length);
                }
            }
        }

        /// <summary>
        /// Returns true if this list and another list are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="other">The list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public bool Equals(FixedList512Bytes<T> other)
        {
            return CompareTo(other) == 0;
        }


        /// <summary>
        /// Initializes and returns an instance of FixedList4096Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList4096Bytes&lt;T&gt;.</exception>
        public FixedList4096Bytes(in FixedList512Bytes<T> other)
        {
            this = default;
            var error = Initialize(other);
            if(error != 0)
                FixedList.CheckResize<FixedBytes4094,T>(other.Length);
        }

        /// <summary>
        /// Initializes an instance of FixedList4096Bytes with content copied from another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>zero on success, or non-zero on error.</returns>
        internal int Initialize(in FixedList512Bytes<T> other)
        {
            if(other.Length > Capacity)
                return (int)CopyError.Truncation;
            length = other.length;
            buffer = new FixedBytes4094();
            unsafe
            {
                UnsafeUtility.MemCpy(Buffer, other.Buffer, LengthInBytes);
            }
            return 0;
        }

        /// <summary>
        /// Returns a new list that is a copy of another list.
        /// </summary>
        /// <param name="other">The list to copy.</param>
        /// <returns>A new list that is a copy of the other.</returns>
        /// <exception cref="IndexOutOfRangeException">Throws if the other list's length exceeds the capacity of FixedList4096Bytes&lt;T&gt;.</exception>
        public static implicit operator FixedList4096Bytes<T>(in FixedList512Bytes<T> other)
        {
            return new FixedList4096Bytes<T>(other);
        }

        /// <summary>
        /// Returns true if two lists are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for equality.</param>
        /// <param name="b">The second list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public static bool operator ==(in FixedList4096Bytes<T> a, in FixedList4096Bytes<T> b)
        {
            unsafe
            {
                if(a.length != b.length)
                    return false;
                return UnsafeUtility.MemCmp(a.Buffer, b.Buffer, a.LengthInBytes) == 0;
            }
        }

        /// <summary>
        /// Returns true if two lists are unequal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="a">The first list to compare for inequality.</param>
        /// <param name="b">The second list to compare for inequality.</param>
        /// <returns>True if the two lists are unequal.</returns>
        public static bool operator !=(in FixedList4096Bytes<T> a, in FixedList4096Bytes<T> b)
        {
            return !(a == b);
        }

        /// <summary>
        /// Returns a number denoting whether this list should be placed before or after another list in a sort.
        /// </summary>
        /// <param name="other">A list to to compare with.</param>
        /// <returns>An integer denoting the respective sort position of the list relative to the other:
        ///
        /// 0 denotes that both lists should have the same position in a sort.
        /// -1 denotes that this list should precede the other list in a sort.
        /// +1 denotes that this list should follow the other list in a sort.
        /// </returns>
        public int CompareTo(FixedList4096Bytes<T> other)
        {
            unsafe
            {
                fixed(byte* a = &buffer.offset0000.byte0000)
                {
                    byte* b = &other.buffer.offset0000.byte0000;
                    var aa = a + FixedList.PaddingBytes<T>();
                    var bb = b + FixedList.PaddingBytes<T>();
                    var mini = math.min(Length, other.Length);
                    for(var i = 0; i < mini; ++i)
                    {
                        var j = UnsafeUtility.MemCmp(aa + sizeof(T) * i, bb + sizeof(T) * i, sizeof(T));
                        if(j != 0)
                            return j;
                    }
                    return Length.CompareTo(other.Length);
                }
            }
        }

        /// <summary>
        /// Returns true if this list and another list are equal.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.</remarks>
        /// <param name="other">The list to compare for equality.</param>
        /// <returns>True if the two lists are equal.</returns>
        public bool Equals(FixedList4096Bytes<T> other)
        {
            return CompareTo(other) == 0;
        }


        /// <summary>
        /// Returns true if the list is equal to an object.
        /// </summary>
        /// <remarks>Two lists are equal if their length and bytes are equal.
        ///
        /// A FixedList*N*&lt;T&gt; can only be equal to another FixedList*N*&lt;T&gt; with the same *N* and T.
        /// </remarks>
        /// <param name="obj">An object to compare for equality.</param>
        /// <returns>True if the list is equal to the object.</returns>
        [NotBurstCompatible]
        public override bool Equals(object obj)
        {
            if(obj is FixedList32Bytes<T> aFixedList32Bytes) return Equals(aFixedList32Bytes);
            if(obj is FixedList64Bytes<T> aFixedList64Bytes) return Equals(aFixedList64Bytes);
            if(obj is FixedList128Bytes<T> aFixedList128Bytes) return Equals(aFixedList128Bytes);
            if(obj is FixedList512Bytes<T> aFixedList512Bytes) return Equals(aFixedList512Bytes);
            if(obj is FixedList4096Bytes<T> aFixedList4096Bytes) return Equals(aFixedList4096Bytes);
            return false;
        }

        /// <summary>
        /// An enumerator over the elements of a FixedList4096Bytes&lt;T&gt;.
        /// </summary>
        /// <remarks>
        /// In an enumerator's initial state, `Current` cannot be read. The first <see cref="MoveNext"/> call advances the enumerator to the first element.
        /// </remarks>
        public struct Enumerator : IEnumerator<T>
        {
            FixedList4096Bytes<T> m_List;
            int m_Index;

            /// <summary>
            /// Initializes and returns an instance of FixedList4096Bytes&lt;T&gt;.
            /// </summary>
            /// <param name="list">The list for which to create an enumerator.</param>
            public Enumerator(ref FixedList4096Bytes<T> list)
            {
                m_List = list;
                m_Index = -1;
            }

            /// <summary>
            /// Does nothing.
            /// </summary>
            public void Dispose()
            {
            }

            /// <summary>
            /// Advances the enumerator to the next element.
            /// </summary>
            /// <returns>True if <see cref="Current"/> is valid to read after the call.</returns>
            public bool MoveNext()
            {
                m_Index++;
                return m_Index < m_List.Length;
            }

            /// <summary>
            /// Resets the enumerator to its initial state.
            /// </summary>
            public void Reset()
            {
                m_Index = -1;
            }

            /// <summary>
            /// The current element.
            /// </summary>
            /// <value>The current element.</value>
            public T Current => m_List[m_Index]; // Let FixedList4096Bytes<T> indexer check for out of range.

            object IEnumerator.Current => Current;
        }

        /// <summary>
        /// Returns an enumerator for iterating over the elements of this list.
        /// </summary>
        /// <returns>An enumerator for iterating over the elements of this list.</returns>
        public Enumerator GetEnumerator()
        {
            return new Enumerator(ref this);
        }

        /// <summary>
        /// This method is not implemented. Use <see cref="GetEnumerator"/> instead.
        /// </summary>
        /// <returns>Nothing because it always throws <see cref="NotImplementedException"/>.</returns>
        /// <exception cref="NotImplementedException">Method is not implemented.</exception>
        IEnumerator IEnumerable.GetEnumerator()
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// This method is not implemented. Use <see cref="GetEnumerator"/> instead.
        /// </summary>
        /// <returns>Nothing because it always throws <see cref="NotImplementedException"/>.</returns>
        /// <exception cref="NotImplementedException">Method is not implemented.</exception>
        IEnumerator<T> IEnumerable<T>.GetEnumerator()
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Provides extension methods for FixedList4096Bytes.
    /// </summary>
    [BurstCompatible]
    public unsafe static class FixedList4096BytesExtensions
    {
        /// <summary>
        /// Finds the index of the first occurrence of a particular value in this list.
        /// </summary>
        /// <typeparam name="T">The type of elements in this list.</typeparam>
        /// <typeparam name="U">The value type.</typeparam>
        /// <param name="list">The list to search.</param>
        /// <param name="value">The value to locate.</param>
        /// <returns>The index of the first occurrence of the value. Returns -1 if no occurrence is found.</returns>
        [BurstCompatible(GenericTypeArguments = new [] { typeof(int), typeof(int) })]
        public static int IndexOf<T, U>(this ref FixedList4096Bytes<T> list, U value) where T : unmanaged, IEquatable<U>
        {
            return NativeArrayExtensions.IndexOf<T, U>(list.Buffer, list.Length, value);
        }

        /// <summary>
        /// Returns true if a particular value is present in this list.
        /// </summary>
        /// <typeparam name="T">The type of elements in this list.</typeparam>
        /// <typeparam name="U">The value type.</typeparam>
        /// <param name="list">The list to search.</param>
        /// <param name="value">The value to locate.</param>
        /// <returns>True if the value is present in this list.</returns>
        [BurstCompatible(GenericTypeArguments = new [] { typeof(int), typeof(int) })]
        public static bool Contains<T, U>(this ref FixedList4096Bytes<T> list, U value) where T : unmanaged, IEquatable<U>
        {
            return list.IndexOf(value) != -1;
        }

        /// <summary>
        /// Removes the first occurrence of a particular value in this list.
        /// </summary>
        /// <remarks>
        /// If a value is removed, all elements after it are shifted down by one, and the list's length is decremented by one.
        ///
        /// If you don't need to preserve the order of the remaining elements, <see cref="Unity.Collections.FixedList4096BytesExtensions.RemoveSwapBack{T, U}"/> is a cheaper alternative.
        /// </remarks>
        /// <typeparam name="T">The type of elements in this list.</typeparam>
        /// <typeparam name="U">The value type.</typeparam>
        /// <param name="list">The list to search.</param>
        /// <param name="value">The value to locate and remove.</param>
        /// <returns>True if the value was found and removed.</returns>
        [BurstCompatible(GenericTypeArguments = new [] { typeof(int), typeof(int) })]
        public static bool Remove<T, U>(this ref FixedList4096Bytes<T> list, U value) where T : unmanaged, IEquatable<U>
        {
            int index = list.IndexOf(value);
            if (index < 0)
            {
                return false;
            }

            list.RemoveAt(index);

            return true;
        }

        /// <summary>
        /// Removes the first occurrence of a particular value in this list.
        /// </summary>
        /// <remarks>
        /// If a value is removed, the last element of the list is copied to overwrite the removed value, and the list's length is decremented by one.
        ///
        /// This is cheaper than <see cref="Remove"/>, but the order of the remaining elements is not preserved.
        /// </remarks>
        /// <typeparam name="T">The type of elements in this list.</typeparam>
        /// <typeparam name="U">The value type.</typeparam>
        /// <param name="list">The list to search.</param>
        /// <param name="value">The value to locate and remove.</param>
        /// <returns>Returns true if the item is removed.</returns>
        [BurstCompatible(GenericTypeArguments = new [] { typeof(int), typeof(int) })]
        public static bool RemoveSwapBack<T, U>(this ref FixedList4096Bytes<T> list, U value) where T : unmanaged, IEquatable<U>
        {
            var index = list.IndexOf(value);
            if (index == -1)
            {
                return false;
            }

            list.RemoveAtSwapBack(index);

            return true;
        }
    }

    sealed class FixedList4096BytesDebugView<T> where T : unmanaged
    {
        FixedList4096Bytes<T> m_List;
        public FixedList4096BytesDebugView(FixedList4096Bytes<T> list)
        {
            m_List = list;
        }
        public T[] Items => m_List.ToArray();
    }


    /// <summary>
    /// An unmanaged, resizable list of byte that does not allocate memory.
    /// It is 32 bytes in size, and contains all the memory it needs.
    /// </summary>
    [Serializable]
    [StructLayout(LayoutKind.Explicit, Size=32)]
    [Obsolete("FixedListByte32 is deprecated, please use FixedList32Bytes<byte> instead. (UnityUpgradable) -> FixedList32Bytes<byte>", true)]
    public struct FixedListByte32 {}
    

    [Obsolete("FixedListByte32DebugView is deprecated. (UnityUpgradable) -> FixedList32BytesDebugView<byte>", true)]
    sealed class FixedListByte32DebugView
    {
        FixedList32Bytes<byte> m_List;
        public FixedListByte32DebugView(FixedList32Bytes<byte> list)
        {
            m_List = list;
        }
        public byte[] Items => m_List.ToArray();
    }

    /// <summary>
    /// An unmanaged, resizable list of byte that does not allocate memory.
    /// It is 64 bytes in size, and contains all the memory it needs.
    /// </summary>
    [Serializable]
    [StructLayout(LayoutKind.Explicit, Size=64)]
    [Obsolete("FixedListByte64 is deprecated, please use FixedList64Bytes<byte> instead. (UnityUpgradable) -> FixedList64Bytes<byte>", true)]
    public struct FixedListByte64 {}
    

    [Obsolete("FixedListByte64DebugView is deprecated. (UnityUpgradable) -> FixedList64BytesDebugView<byte>", true)]
    sealed class FixedListByte64DebugView
    {
        FixedList64Bytes<byte> m_List;
        public FixedListByte64DebugView(FixedList64Bytes<byte> list)
        {
            m_List = list;
        }
        public byte[] Items => m_List.ToArray();
    }

    /// <summary>
    /// An unmanaged, resizable list of byte that does not allocate memory.
    /// It is 128 bytes in size, and contains all the memory it needs.
    /// </summary>
    [Serializable]
    [StructLayout(LayoutKind.Explicit, Size=128)]
    [Obsolete("FixedListByte128 is deprecated, please use FixedList128Bytes<byte> instead. (UnityUpgradable) -> FixedList128Bytes<byte>", true)]
    public struct FixedListByte128 {}
    

    [Obsolete("FixedListByte128DebugView is deprecated. (UnityUpgradable) -> FixedList128BytesDebugView<byte>", true)]
    sealed class FixedListByte128DebugView
    {
        FixedList128Bytes<byte> m_List;
        public FixedListByte128DebugView(FixedList128Bytes<byte> list)
        {
            m_List = list;
        }
        public byte[] Items => m_List.ToArray();
    }

    /// <summary>
    /// An unmanaged, resizable list of byte that does not allocate memory.
    /// It is 512 bytes in size, and contains all the memory it needs.
    /// </summary>
    [Serializable]
    [StructLayout(LayoutKind.Explicit, Size=512)]
    [Obsolete("FixedListByte512 is deprecated, please use FixedList512Bytes<byte> instead. (UnityUpgradable) -> FixedList512Bytes<byte>", true)]
    public struct FixedListByte512 {}
    

    [Obsolete("FixedListByte512DebugView is deprecated. (UnityUpgradable) -> FixedList512BytesDebugView<byte>", true)]
    sealed class FixedListByte512DebugView
    {
        FixedList512Bytes<byte> m_List;
        public FixedListByte512DebugView(FixedList512Bytes<byte> list)
        {
            m_List = list;
        }
        public byte[] Items => m_List.ToArray();
    }

    /// <summary>
    /// An unmanaged, resizable list of byte that does not allocate memory.
    /// It is 4096 bytes in size, and contains all the memory it needs.
    /// </summary>
    [Serializable]
    [StructLayout(LayoutKind.Explicit, Size=4096)]
    [Obsolete("FixedListByte4096 is deprecated, please use FixedList4096Bytes<byte> instead. (UnityUpgradable) -> FixedList4096Bytes<byte>", true)]
    public struct FixedListByte4096 {}
    

    [Obsolete("FixedListByte4096DebugView is deprecated. (UnityUpgradable) -> FixedList4096BytesDebugView<byte>", true)]
    sealed class FixedListByte4096DebugView
    {
        FixedList4096Bytes<byte> m_List;
        public FixedListByte4096DebugView(FixedList4096Bytes<byte> list)
        {
            m_List = list;
        }
        public byte[] Items => m_List.ToArray();
    }

    /// <summary>
    /// An unmanaged, resizable list of int that does not allocate memory.
    /// It is 32 bytes in size, and contains all the memory it needs.
    /// </summary>
    [Serializable]
    [StructLayout(LayoutKind.Explicit, Size=32)]
    [Obsolete("FixedListInt32 is deprecated, please use FixedList32Bytes<int> instead. (UnityUpgradable) -> FixedList32Bytes<int>", true)]
    public struct FixedListInt32 {}
    

    [Obsolete("FixedListInt32DebugView is deprecated. (UnityUpgradable) -> FixedList32BytesDebugView<int>", true)]
    sealed class FixedListInt32DebugView
    {
        FixedList32Bytes<int> m_List;
        public FixedListInt32DebugView(FixedList32Bytes<int> list)
        {
            m_List = list;
        }
        public int[] Items => m_List.ToArray();
    }

    /// <summary>
    /// An unmanaged, resizable list of int that does not allocate memory.
    /// It is 64 bytes in size, and contains all the memory it needs.
    /// </summary>
    [Serializable]
    [StructLayout(LayoutKind.Explicit, Size=64)]
    [Obsolete("FixedListInt64 is deprecated, please use FixedList64Bytes<int> instead. (UnityUpgradable) -> FixedList64Bytes<int>", true)]
    public struct FixedListInt64 {}
    

    [Obsolete("FixedListInt64DebugView is deprecated. (UnityUpgradable) -> FixedList64BytesDebugView<int>", true)]
    sealed class FixedListInt64DebugView
    {
        FixedList64Bytes<int> m_List;
        public FixedListInt64DebugView(FixedList64Bytes<int> list)
        {
            m_List = list;
        }
        public int[] Items => m_List.ToArray();
    }

    /// <summary>
    /// An unmanaged, resizable list of int that does not allocate memory.
    /// It is 128 bytes in size, and contains all the memory it needs.
    /// </summary>
    [Serializable]
    [StructLayout(LayoutKind.Explicit, Size=128)]
    [Obsolete("FixedListInt128 is deprecated, please use FixedList128Bytes<int> instead. (UnityUpgradable) -> FixedList128Bytes<int>", true)]
    public struct FixedListInt128 {}
    

    [Obsolete("FixedListInt128DebugView is deprecated. (UnityUpgradable) -> FixedList128BytesDebugView<int>", true)]
    sealed class FixedListInt128DebugView
    {
        FixedList128Bytes<int> m_List;
        public FixedListInt128DebugView(FixedList128Bytes<int> list)
        {
            m_List = list;
        }
        public int[] Items => m_List.ToArray();
    }

    /// <summary>
    /// An unmanaged, resizable list of int that does not allocate memory.
    /// It is 512 bytes in size, and contains all the memory it needs.
    /// </summary>
    [Serializable]
    [StructLayout(LayoutKind.Explicit, Size=512)]
    [Obsolete("FixedListInt512 is deprecated, please use FixedList512Bytes<int> instead. (UnityUpgradable) -> FixedList512Bytes<int>", true)]
    public struct FixedListInt512 {}
    

    [Obsolete("FixedListInt512DebugView is deprecated. (UnityUpgradable) -> FixedList512BytesDebugView<int>", true)]
    sealed class FixedListInt512DebugView
    {
        FixedList512Bytes<int> m_List;
        public FixedListInt512DebugView(FixedList512Bytes<int> list)
        {
            m_List = list;
        }
        public int[] Items => m_List.ToArray();
    }

    /// <summary>
    /// An unmanaged, resizable list of int that does not allocate memory.
    /// It is 4096 bytes in size, and contains all the memory it needs.
    /// </summary>
    [Serializable]
    [StructLayout(LayoutKind.Explicit, Size=4096)]
    [Obsolete("FixedListInt4096 is deprecated, please use FixedList4096Bytes<int> instead. (UnityUpgradable) -> FixedList4096Bytes<int>", true)]
    public struct FixedListInt4096 {}
    

    [Obsolete("FixedListInt4096DebugView is deprecated. (UnityUpgradable) -> FixedList4096BytesDebugView<int>", true)]
    sealed class FixedListInt4096DebugView
    {
        FixedList4096Bytes<int> m_List;
        public FixedListInt4096DebugView(FixedList4096Bytes<int> list)
        {
            m_List = list;
        }
        public int[] Items => m_List.ToArray();
    }

    /// <summary>
    /// An unmanaged, resizable list of float that does not allocate memory.
    /// It is 32 bytes in size, and contains all the memory it needs.
    /// </summary>
    [Serializable]
    [StructLayout(LayoutKind.Explicit, Size=32)]
    [Obsolete("FixedListFloat32 is deprecated, please use FixedList32Bytes<float> instead. (UnityUpgradable) -> FixedList32Bytes<float>", true)]
    public struct FixedListFloat32 {}
    

    [Obsolete("FixedListFloat32DebugView is deprecated. (UnityUpgradable) -> FixedList32BytesDebugView<float>", true)]
    sealed class FixedListFloat32DebugView
    {
        FixedList32Bytes<float> m_List;
        public FixedListFloat32DebugView(FixedList32Bytes<float> list)
        {
            m_List = list;
        }
        public float[] Items => m_List.ToArray();
    }

    /// <summary>
    /// An unmanaged, resizable list of float that does not allocate memory.
    /// It is 64 bytes in size, and contains all the memory it needs.
    /// </summary>
    [Serializable]
    [StructLayout(LayoutKind.Explicit, Size=64)]
    [Obsolete("FixedListFloat64 is deprecated, please use FixedList64Bytes<float> instead. (UnityUpgradable) -> FixedList64Bytes<float>", true)]
    public struct FixedListFloat64 {}
    

    [Obsolete("FixedListFloat64DebugView is deprecated. (UnityUpgradable) -> FixedList64BytesDebugView<float>", true)]
    sealed class FixedListFloat64DebugView
    {
        FixedList64Bytes<float> m_List;
        public FixedListFloat64DebugView(FixedList64Bytes<float> list)
        {
            m_List = list;
        }
        public float[] Items => m_List.ToArray();
    }

    /// <summary>
    /// An unmanaged, resizable list of float that does not allocate memory.
    /// It is 128 bytes in size, and contains all the memory it needs.
    /// </summary>
    [Serializable]
    [StructLayout(LayoutKind.Explicit, Size=128)]
    [Obsolete("FixedListFloat128 is deprecated, please use FixedList128Bytes<float> instead. (UnityUpgradable) -> FixedList128Bytes<float>", true)]
    public struct FixedListFloat128 {}
    

    [Obsolete("FixedListFloat128DebugView is deprecated. (UnityUpgradable) -> FixedList128BytesDebugView<float>", true)]
    sealed class FixedListFloat128DebugView
    {
        FixedList128Bytes<float> m_List;
        public FixedListFloat128DebugView(FixedList128Bytes<float> list)
        {
            m_List = list;
        }
        public float[] Items => m_List.ToArray();
    }

    /// <summary>
    /// An unmanaged, resizable list of float that does not allocate memory.
    /// It is 512 bytes in size, and contains all the memory it needs.
    /// </summary>
    [Serializable]
    [StructLayout(LayoutKind.Explicit, Size=512)]
    [Obsolete("FixedListFloat512 is deprecated, please use FixedList512Bytes<float> instead. (UnityUpgradable) -> FixedList512Bytes<float>", true)]
    public struct FixedListFloat512 {}
    

    [Obsolete("FixedListFloat512DebugView is deprecated. (UnityUpgradable) -> FixedList512BytesDebugView<float>", true)]
    sealed class FixedListFloat512DebugView
    {
        FixedList512Bytes<float> m_List;
        public FixedListFloat512DebugView(FixedList512Bytes<float> list)
        {
            m_List = list;
        }
        public float[] Items => m_List.ToArray();
    }

    /// <summary>
    /// An unmanaged, resizable list of float that does not allocate memory.
    /// It is 4096 bytes in size, and contains all the memory it needs.
    /// </summary>
    [Serializable]
    [StructLayout(LayoutKind.Explicit, Size=4096)]
    [Obsolete("FixedListFloat4096 is deprecated, please use FixedList4096Bytes<float> instead. (UnityUpgradable) -> FixedList4096Bytes<float>", true)]
    public struct FixedListFloat4096 {}
    

    [Obsolete("FixedListFloat4096DebugView is deprecated. (UnityUpgradable) -> FixedList4096BytesDebugView<float>", true)]
    sealed class FixedListFloat4096DebugView
    {
        FixedList4096Bytes<float> m_List;
        public FixedListFloat4096DebugView(FixedList4096Bytes<float> list)
        {
            m_List = list;
        }
        public float[] Items => m_List.ToArray();
    }
    /// <summary>
    /// Provides extension methods for FixedList*N*.
    /// </summary>
    public static class FixedListExtensions
    {

        /// <summary>
        /// Sorts the elements in this list in ascending order.
        /// </summary>
        /// <typeparam name="T">The type of the elements.</typeparam>
        /// <param name="list">The list to sort.</param>
        [BurstCompatible(GenericTypeArguments = new [] { typeof(int) })]
        public static void Sort<T>(this ref FixedList32Bytes<T> list)
        where T : unmanaged, IComparable<T>
        {
            unsafe
            {
                fixed(byte* b = &list.buffer.offset0000.byte0000)
                {
                    var c = b + FixedList.PaddingBytes<T>();
                    NativeSortExtension.Sort((T*)c, list.Length);
                }
            }
        }

        /// <summary>
        /// Sorts the elements in this list using a custom comparison.
        /// </summary>
        /// <typeparam name="T">The type of the elements.</typeparam>
        /// <typeparam name="U">The type of the comparer.</typeparam>
        /// <param name="list">The list to sort.</param>
        /// <param name="comp">The comparison function used to determine the relative order of the elements.</param>
        [BurstCompatible(GenericTypeArguments = new [] { typeof(int), typeof(NativeSortExtension.DefaultComparer<int>) })]
        public static void Sort<T, U>(this ref FixedList32Bytes<T> list, U comp)
        where T : unmanaged, IComparable<T>
        where U : IComparer<T>
        {
            unsafe
            {
                fixed(byte* b = &list.buffer.offset0000.byte0000)
                {
                    var c = b + FixedList.PaddingBytes<T>();
                    NativeSortExtension.Sort((T*)c, list.Length, comp);
                }
            }
        }


        /// <summary>
        /// Sorts the elements in this list in ascending order.
        /// </summary>
        /// <typeparam name="T">The type of the elements.</typeparam>
        /// <param name="list">The list to sort.</param>
        [BurstCompatible(GenericTypeArguments = new [] { typeof(int) })]
        public static void Sort<T>(this ref FixedList64Bytes<T> list)
        where T : unmanaged, IComparable<T>
        {
            unsafe
            {
                fixed(byte* b = &list.buffer.offset0000.byte0000)
                {
                    var c = b + FixedList.PaddingBytes<T>();
                    NativeSortExtension.Sort((T*)c, list.Length);
                }
            }
        }

        /// <summary>
        /// Sorts the elements in this list using a custom comparison.
        /// </summary>
        /// <typeparam name="T">The type of the elements.</typeparam>
        /// <typeparam name="U">The type of the comparer.</typeparam>
        /// <param name="list">The list to sort.</param>
        /// <param name="comp">The comparison function used to determine the relative order of the elements.</param>
        [BurstCompatible(GenericTypeArguments = new [] { typeof(int), typeof(NativeSortExtension.DefaultComparer<int>) })]
        public static void Sort<T, U>(this ref FixedList64Bytes<T> list, U comp)
        where T : unmanaged, IComparable<T>
        where U : IComparer<T>
        {
            unsafe
            {
                fixed(byte* b = &list.buffer.offset0000.byte0000)
                {
                    var c = b + FixedList.PaddingBytes<T>();
                    NativeSortExtension.Sort((T*)c, list.Length, comp);
                }
            }
        }


        /// <summary>
        /// Sorts the elements in this list in ascending order.
        /// </summary>
        /// <typeparam name="T">The type of the elements.</typeparam>
        /// <param name="list">The list to sort.</param>
        [BurstCompatible(GenericTypeArguments = new [] { typeof(int) })]
        public static void Sort<T>(this ref FixedList128Bytes<T> list)
        where T : unmanaged, IComparable<T>
        {
            unsafe
            {
                fixed(byte* b = &list.buffer.offset0000.byte0000)
                {
                    var c = b + FixedList.PaddingBytes<T>();
                    NativeSortExtension.Sort((T*)c, list.Length);
                }
            }
        }

        /// <summary>
        /// Sorts the elements in this list using a custom comparison.
        /// </summary>
        /// <typeparam name="T">The type of the elements.</typeparam>
        /// <typeparam name="U">The type of the comparer.</typeparam>
        /// <param name="list">The list to sort.</param>
        /// <param name="comp">The comparison function used to determine the relative order of the elements.</param>
        [BurstCompatible(GenericTypeArguments = new [] { typeof(int), typeof(NativeSortExtension.DefaultComparer<int>) })]
        public static void Sort<T, U>(this ref FixedList128Bytes<T> list, U comp)
        where T : unmanaged, IComparable<T>
        where U : IComparer<T>
        {
            unsafe
            {
                fixed(byte* b = &list.buffer.offset0000.byte0000)
                {
                    var c = b + FixedList.PaddingBytes<T>();
                    NativeSortExtension.Sort((T*)c, list.Length, comp);
                }
            }
        }


        /// <summary>
        /// Sorts the elements in this list in ascending order.
        /// </summary>
        /// <typeparam name="T">The type of the elements.</typeparam>
        /// <param name="list">The list to sort.</param>
        [BurstCompatible(GenericTypeArguments = new [] { typeof(int) })]
        public static void Sort<T>(this ref FixedList512Bytes<T> list)
        where T : unmanaged, IComparable<T>
        {
            unsafe
            {
                fixed(byte* b = &list.buffer.offset0000.byte0000)
                {
                    var c = b + FixedList.PaddingBytes<T>();
                    NativeSortExtension.Sort((T*)c, list.Length);
                }
            }
        }

        /// <summary>
        /// Sorts the elements in this list using a custom comparison.
        /// </summary>
        /// <typeparam name="T">The type of the elements.</typeparam>
        /// <typeparam name="U">The type of the comparer.</typeparam>
        /// <param name="list">The list to sort.</param>
        /// <param name="comp">The comparison function used to determine the relative order of the elements.</param>
        [BurstCompatible(GenericTypeArguments = new [] { typeof(int), typeof(NativeSortExtension.DefaultComparer<int>) })]
        public static void Sort<T, U>(this ref FixedList512Bytes<T> list, U comp)
        where T : unmanaged, IComparable<T>
        where U : IComparer<T>
        {
            unsafe
            {
                fixed(byte* b = &list.buffer.offset0000.byte0000)
                {
                    var c = b + FixedList.PaddingBytes<T>();
                    NativeSortExtension.Sort((T*)c, list.Length, comp);
                }
            }
        }


        /// <summary>
        /// Sorts the elements in this list in ascending order.
        /// </summary>
        /// <typeparam name="T">The type of the elements.</typeparam>
        /// <param name="list">The list to sort.</param>
        [BurstCompatible(GenericTypeArguments = new [] { typeof(int) })]
        public static void Sort<T>(this ref FixedList4096Bytes<T> list)
        where T : unmanaged, IComparable<T>
        {
            unsafe
            {
                fixed(byte* b = &list.buffer.offset0000.byte0000)
                {
                    var c = b + FixedList.PaddingBytes<T>();
                    NativeSortExtension.Sort((T*)c, list.Length);
                }
            }
        }

        /// <summary>
        /// Sorts the elements in this list using a custom comparison.
        /// </summary>
        /// <typeparam name="T">The type of the elements.</typeparam>
        /// <typeparam name="U">The type of the comparer.</typeparam>
        /// <param name="list">The list to sort.</param>
        /// <param name="comp">The comparison function used to determine the relative order of the elements.</param>
        [BurstCompatible(GenericTypeArguments = new [] { typeof(int), typeof(NativeSortExtension.DefaultComparer<int>) })]
        public static void Sort<T, U>(this ref FixedList4096Bytes<T> list, U comp)
        where T : unmanaged, IComparable<T>
        where U : IComparer<T>
        {
            unsafe
            {
                fixed(byte* b = &list.buffer.offset0000.byte0000)
                {
                    var c = b + FixedList.PaddingBytes<T>();
                    NativeSortExtension.Sort((T*)c, list.Length, comp);
                }
            }
        }

    }
}
