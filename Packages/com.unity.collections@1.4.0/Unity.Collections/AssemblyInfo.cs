using System.Runtime.CompilerServices;

[assembly: InternalsVisibleTo("Unity.Burst.Tests")]
[assembly: InternalsVisibleTo("Unity.Collections.BurstCompatibilityTestCodeGen")]
[assembly: InternalsVisibleTo("Unity.Collections.CodeGen")]
[assembly: InternalsVisibleTo("Unity.Collections.Tests")]
[assembly: InternalsVisibleTo("Unity.Entities")]
[assembly: InternalsVisibleTo("Unity.Entities.CodeGen")]
[assembly: InternalsVisibleTo("Unity.Entities.Tests")]
[assembly: InternalsVisibleTo("Unity.Entities.Editor")]
[assembly: InternalsVisibleTo("Unity.Entities.Editor.Tests")]
[assembly: InternalsVisibleTo("Unity.Rendering.Hybrid")]
[assembly: InternalsVisibleTo("Unity.Runtime")]
[assembly: InternalsVisibleTo("Unity.Runtime.Tests")]
[assembly: InternalsVisibleTo("Unity.Runtime.IO.Tests")]
[assembly: InternalsVisibleTo("Unity.Runtime.UnityInstance")]
[assembly: InternalsVisibleTo("Unity.Tiny.Animation")]
[assembly: InternalsVisibleTo("Unity.Tiny.Core.Tests")]
[assembly: InternalsVisibleTo("Unity.Tiny.GameSave")]
[assembly: InternalsVisibleTo("Unity.Tiny.GameSave.Tests")]
[assembly: InternalsVisibleTo("Unity.Tiny.Rendering.Native")]
[assembly: InternalsVisibleTo("Unity.Scenes")]
[assembly: InternalsVisibleTo("Unity.Scenes.Editor")]
[assembly: InternalsVisibleTo("Samples.GridPath.Tests")]
[assembly: InternalsVisibleTo("Unity.Entities.PerformanceTests")]
