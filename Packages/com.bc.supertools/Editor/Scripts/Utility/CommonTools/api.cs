using UnityEngine;
using System.Collections.Generic;
using System.Text;
using System;

namespace SuperTools.Editor
{
    public static class StringEx
    {

        public static int frameCount = 0;
        public static float realtimeSinceStartup = 0;

        public static StringBuilder sb = new StringBuilder();
        public static string Format(string format, params object[] args)
        {
            if ((format == null) || (args == null))
            {
                //throw new ArgumentNullException((format == null) ? "format" : "args");
                return format;
            }
            int len = format.Length;
            for (int i = 0; i < args.Length; i++)
            {
                if (args[i] is string)
                    len += ((string)args[i]).Length;
                else if (args[i] is int)
                    len += 16;
                else if (args[i] is double)
                    len += 16;
                else if (args[i] is float)
                    len += 16;
                else
                    len += 8;
            }
            if (len > sb.Capacity)
                sb.Capacity = len;
            sb.Length = 0;
            sb.AppendFormat(format, args);
            return sb.ToString();
        }

        public static bool IsNullOrWhiteSpace(this string str)
        {
            if (string.IsNullOrEmpty(str)) return true;
            int length = str.Length;
            for (int i = 0; i < length; i++)
            {
                if (!Char.IsWhiteSpace(str, i)) return false;
            }
            return true;
        }
        public static int ToInt(this string str)
        {
            if (string.IsNullOrEmpty(str)) return 0;
            int it = 0;
            int.TryParse(str,out it);
            return it;
        }


        public static string Append(this string str, string v1)
        {
            if (string.IsNullOrEmpty(v1)) return str;
            var sb = new StringBuilder(str.Length + v1.Length);
            sb.Append(str);
            sb.Append(v1);

            return sb.ToString();
        }

        public static string Append(this string str, string v1, string v2)
        {
            var sb = new StringBuilder(str.Length + v1.Length + v2.Length);
            sb.Append(str);
            sb.Append(v1);
            sb.Append(v2);

            return sb.ToString();
        }
            
        private static List<object> tmpList = new List<object>();
        /// <summary>
        /// 仅用于测试，代码实现效率较低
        /// </summary>
        public static void print(this object ob, params object[] args)
        {
            //List<object> tmpList = new List<object>(args);
            tmpList.Clear();
            tmpList.AddRange(args);
            Debug.LogFormat("{0}:{1}", Time.time, String.Join("\t", tmpList.ConvertAll(o => (o == null ? "null" : o.ToString())).ToArray()));
        }
        public static void PrintEditor(this object ob, params object[] args) {
            if (!Application.isEditor) return;
            PrintError(ob,args);
        }
        public static void PrintError(this object ob, params object[] args)
        {
            //List<object> tmpList = new List<object>(args);
            tN =  tN ?? System.DateTime.UtcNow;

            tmpList.Clear();
            tmpList.AddRange(args);
            var logStr = string.Format("{0}:{1}", (System.DateTime.UtcNow - tN.Value).TotalSeconds, String.Join("\t", tmpList.ConvertAll(o => (o == null ? "null" : o.ToString())).ToArray()));
            Debug.LogError(logStr);
        }

        public static string GetLogStr(object[] args)
        {
            tmpList.Clear();
            tmpList.AddRange(args);
            var logStr = String.Join("\t", tmpList.ConvertAll(o => (o == null ? "null" : o.ToString())).ToArray());
            return logStr;
        }

        static List<object> tmpListT = new List<object>();
        static DateTime? tN;
        public static void PrintThread(this object ob, params object[] args)
        {
            tN =  tN ?? System.DateTime.UtcNow;
            tmpListT.Clear();
            tmpListT.AddRange(args);
            Debug.LogFormat("{0}:{1}", (System.DateTime.UtcNow - tN.Value).TotalSeconds, String.Join("\t", tmpListT.ConvertAll(o => (o == null ? "null" : o.ToString())).ToArray()));
        }

        public static bool CEndsWith(this string str, string value)
        {
            if (string.IsNullOrEmpty(value)|| string.IsNullOrEmpty(str))
            {
                return false;
            }
            var len = str.Length;
            var len2 = value.Length;
            if (len2 > len)
            {
                return false;
            }
            for (int i = len - 1,j= len2 - 1; i >= 0; i--,j--)
            {
                if(j<0)
                {
                    return true;
                }
                if(str[i]!=value[j])
                {
                    return false;
                }
            }
            return true;
        }

        public static bool IsUTFBOM(byte[] ss)
        {
            if (ss == null || ss.Length < 3) return false;

            return ss[0] == 0xEF && ss[1] == 0xBB && ss[2] == 0xBF;

        }

        public static Encoding GetEncode(byte[] ss)
        {
            Encoding reVal = null;
            if (IsUTF8Bytes(ss) || (ss[0] == 0xEF && ss[1] == 0xBB && ss[2] == 0xBF))
            {
                reVal = Encoding.UTF8;
            }
            else if (ss[0] == 0xFE && ss[1] == 0xFF && ss[2] == 0x00)
            {
                reVal = Encoding.BigEndianUnicode;
            }
            else if (ss[0] == 0xFF && ss[1] == 0xFE && ss[2] == 0x41)
            {
                reVal = Encoding.Unicode;
            }
            return reVal;
        }

        /// <summary> 
        /// 判断是否是不带 BOM 的 UTF8 格式 
        /// </summary> 
        /// <param name="data"></param> 
        /// <returns></returns> 
        public static bool IsUTF8Bytes(byte[] data)
        {
            int charByteCounter = 1;
            //计算当前正分析的字符应还有的字节数 
            byte curByte; //当前分析的字节. 
            for (int i = 0; i < data.Length; i++)
            {
                curByte = data[i];
                if (charByteCounter == 1)
                {
                    if (curByte >= 0x80)
                    {
                        //判断当前 
                        while (((curByte <<= 1) & 0x80) != 0)
                        {
                            charByteCounter++;
                        }
                        //标记位首位若为非0 则至少以2个1开始 如:110XXXXX...........1111110X　 
                        if (charByteCounter == 1 || charByteCounter > 6)
                        {
                            return false;
                        }
                    }
                }
                else
                {
                    //若是UTF-8 此时第一位必须为1 
                    if ((curByte & 0xC0) != 0x80)
                    {
                        return false;
                    }
                    charByteCounter--;
                }
            }
            if (charByteCounter > 1)
            {
                throw new Exception("非预期的byte格式");
            }
            return true;
        }
    }
}