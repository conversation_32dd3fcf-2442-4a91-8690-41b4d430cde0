using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace War.UPMTools
{
    public class FolderCreateCommand : ICommand
    {
        string m_Path;
        bool m_Replace;
        SVNAsyncOperation<bool> m_SVNAsyncOperation;

        public FolderCreateCommand(string path, bool replace = true)
        {
            m_Path = path;
            m_Replace = replace;
        }

        public SVNAsyncOperationBase StartAsyncOperation()
        {
            if (m_SVNAsyncOperation == null)
            {
                m_SVNAsyncOperation = SVNIntegration.FolderCreateAsync(m_Path, m_Replace);
            }
            return m_SVNAsyncOperation;
        }

        public string Info()
        {
            return "FolderCreateCommand";
        }
    }
}
