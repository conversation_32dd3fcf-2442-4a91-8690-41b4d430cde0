using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEditor;

namespace War.UPMTools
{
    class LockFileCommand : ICommand
    {
        string m_Path;
        bool m_Force;
        string m_Message;
        string m_Encoding;
        int m_Timeout;
        string targetsFileToUse;
        SVNAsyncOperation<LockOperationResult> m_SVNAsyncOperation;

        public LockFileCommand(string path, bool force, string message = "", string encoding = "", int timeout = -1)
        {
            m_Path = path;
            m_Force = force;
            m_Message = message;
            m_Encoding = encoding;
            m_Timeout = timeout;
            targetsFileToUse = FileUtil.GetUniqueTempPathInProject();
        }

        public SVNAsyncOperationBase StartAsyncOperation()
        {
            if (m_SVNAsyncOperation == null)
            {
                m_SVNAsyncOperation = SVNIntegration.LockFileAsync(m_Path, m_Force, targetsFileToUse, m_Message, m_Encoding, m_Timeout);
            }
            return m_SVNAsyncOperation;
        }

        public string Info()
        {
            return "LockFileCommand";
        }
    }
}
