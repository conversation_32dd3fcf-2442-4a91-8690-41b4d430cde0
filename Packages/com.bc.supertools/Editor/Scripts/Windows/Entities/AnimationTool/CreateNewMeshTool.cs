using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

namespace SuperTools.Editor.Windows
{ 
    public class CreateNewMeshTool 
    {

        public enum SmoothedNormalsChannel
        {
            VertexColors,
            Tangents,
            UV1,
            UV2,
            UV3,
            UV4
        }

        public enum SmoothedNormalsUVType
        {
            FullXYZ,
            CompressedXY,
            CompressedZW
        }

        private static void CopyBlendShapes(Mesh originalMesh, Mesh newMesh)
        {
            for (var i = 0; i < originalMesh.blendShapeCount; i++)
            {
                var shapeName = originalMesh.GetBlendShapeName(i);
                var frameCount = originalMesh.GetBlendShapeFrameCount(i);
                for (var j = 0; j < frameCount; j++)
                {
                    var dv = new Vector3[originalMesh.vertexCount];
                    var dn = new Vector3[originalMesh.vertexCount];
                    var dt = new Vector3[originalMesh.vertexCount];

                    var frameWeight = originalMesh.GetBlendShapeFrameWeight(i, j);
                    originalMesh.GetBlendShapeFrameVertices(i, j, dv, dn, dt);
                    newMesh.AddBlendShapeFrame(shapeName, frameWeight, dv, dn, dt);
                }
            }
        }
        public static Mesh CreateSmoothedMesh(Mesh originalMesh, string format, SmoothedNormalsChannel smoothedNormalsChannel, SmoothedNormalsUVType uvType, bool overwriteMesh)
        {
            if (originalMesh == null)
            {
                Debug.LogWarning("[TCP2 : Smoothed Mesh] Supplied OriginalMesh is null!\nCan't create smooth normals version.");
                return null;
            }

            //Create new mesh
            var newMesh = overwriteMesh ? originalMesh : new Mesh();
            if (!overwriteMesh)
            {
                //			EditorUtility.CopySerialized(originalMesh, newMesh);
                newMesh.vertices = originalMesh.vertices;
                newMesh.normals = originalMesh.normals;
                newMesh.tangents = originalMesh.tangents;
                newMesh.uv = originalMesh.uv;
                newMesh.uv2 = originalMesh.uv2;
                newMesh.uv3 = originalMesh.uv3;
                newMesh.uv4 = originalMesh.uv4;
                newMesh.colors32 = originalMesh.colors32;
                newMesh.triangles = originalMesh.triangles;
                newMesh.bindposes = originalMesh.bindposes;
                newMesh.boneWeights = originalMesh.boneWeights;

                //Only available from Unity 5.3 onward
                if (originalMesh.blendShapeCount > 0)
                    CopyBlendShapes(originalMesh, newMesh);

                newMesh.subMeshCount = originalMesh.subMeshCount;
                if (newMesh.subMeshCount > 1)
                    for (var i = 0; i < newMesh.subMeshCount; i++)
                        newMesh.SetTriangles(originalMesh.GetTriangles(i), i);
            }

            //--------------------------------
            // Format

            var chSign = Vector3.one;
            if (string.IsNullOrEmpty(format)) format = "xyz";
            format = format.ToLowerInvariant();
            var channels = new[] { 0, 1, 2 };
            var skipFormat = (format == "xyz");
            var charIndex = 0;
            var ch = 0;
            while (charIndex < format.Length)
            {
                switch (format[charIndex])
                {
                    case '-': chSign[ch] = -1; break;
                    case 'x': channels[ch] = 0; ch++; break;
                    case 'y': channels[ch] = 1; ch++; break;
                    case 'z': channels[ch] = 2; ch++; break;
                    default: break;
                }
                if (ch > 2) break;
                charIndex++;
            }

            //--------------------------------
            //Calculate smoothed normals

            //Iterate, find same-position vertices and calculate averaged values as we go
            var averageNormalsHash = new Dictionary<Vector3, Vector3>();
            for (var i = 0; i < newMesh.vertexCount; i++)
            {
                if (!averageNormalsHash.ContainsKey(newMesh.vertices[i]))
                    averageNormalsHash.Add(newMesh.vertices[i], newMesh.normals[i]);
                else
                    averageNormalsHash[newMesh.vertices[i]] = (averageNormalsHash[newMesh.vertices[i]] + newMesh.normals[i]).normalized;
            }

            //Convert to Array
            var averageNormals = new Vector3[newMesh.vertexCount];
            for (var i = 0; i < newMesh.vertexCount; i++)
            {
                averageNormals[i] = averageNormalsHash[newMesh.vertices[i]];
                if (!skipFormat)
                    averageNormals[i] = Vector3.Scale(new Vector3(averageNormals[i][channels[0]], averageNormals[i][channels[1]], averageNormals[i][channels[2]]), chSign);
            }

#if DONT_ALTER_NORMALS
		//Debug: don't alter normals to see if converting into colors/tangents/uv2 works correctly
		for(int i = 0; i < newMesh.vertexCount; i++)
		{
			averageNormals[i] = newMesh.normals[i];
		}
#endif

            //--------------------------------
            // Store in Vertex Colors

            //if (smoothedNormalsChannel == SmoothedNormalsChannel.VertexColors)
            //{
            //    //Assign averaged normals to colors
            //    var colors = new Color32[newMesh.vertexCount];
            //    for (var i = 0; i < newMesh.vertexCount; i++)
            //    {
            //        var r = (byte)(((averageNormals[i].x * 0.5f) + 0.5f) * 255);
            //        var g = (byte)(((averageNormals[i].y * 0.5f) + 0.5f) * 255);
            //        var b = (byte)(((averageNormals[i].z * 0.5f) + 0.5f) * 255);
            //        if (originalMesh.colors32.Length == newMesh.vertexCount)
            //        {
            //            colors[i] = originalMesh.colors32[i];
            //        }
            //        else
            //        {
            //            colors[i] = new Color32(r, g, b, 255);
            //        }
            //    }
            //    newMesh.colors32 = colors;
            //}
            if (originalMesh.colors.Length == newMesh.colors.Length)
            {
                for (int i = 0; i < newMesh.colors.Length; i++)
                {
                    newMesh.colors[i].a = originalMesh.colors[i].a;
                }
            }
            //--------------------------------
            // Store in Tangents

            if (smoothedNormalsChannel == SmoothedNormalsChannel.Tangents)
            {
                //Assign averaged normals to tangent
                var tangents = new Vector4[newMesh.vertexCount];
                for (var i = 0; i < newMesh.vertexCount; i++)
                {
                    tangents[i] = new Vector4(averageNormals[i].x, averageNormals[i].y, averageNormals[i].z, 0f);
                }
                newMesh.tangents = tangents;
            }

            //--------------------------------
            // Store in UVs

            if (smoothedNormalsChannel == SmoothedNormalsChannel.UV1 || smoothedNormalsChannel == SmoothedNormalsChannel.UV2 || smoothedNormalsChannel == SmoothedNormalsChannel.UV3 || smoothedNormalsChannel == SmoothedNormalsChannel.UV4)
            {
                int uvIndex = -1;

                switch (smoothedNormalsChannel)
                {
                    case SmoothedNormalsChannel.UV1: uvIndex = 0; break;
                    case SmoothedNormalsChannel.UV2: uvIndex = 1; break;
                    case SmoothedNormalsChannel.UV3: uvIndex = 2; break;
                    case SmoothedNormalsChannel.UV4: uvIndex = 3; break;
                    default: Debug.LogError("Invalid smoothed normals UV channel: " + smoothedNormalsChannel); break;
                }

                if (uvType == SmoothedNormalsUVType.FullXYZ)
                {
                    //Assign averaged normals directly to UV fully (xyz)
                    newMesh.SetUVs(uvIndex, new List<Vector3>(averageNormals));
                }
                else
                {
                    if (uvType == SmoothedNormalsUVType.CompressedXY)
                    {
                        //Assign averaged normals to UV compressed (x,y to uv.x and z to uv.y)
                        var uvs = new List<Vector2>(newMesh.vertexCount);
                        for (var i = 0; i < newMesh.vertexCount; i++)
                        {
                            float x, y;
                            GetCompressedSmoothedNormals(averageNormals[i], out x, out y);
                            var v2 = new Vector2(x, y);
                            uvs.Add(v2);
                        }
                        newMesh.SetUVs(uvIndex, uvs);
                    }
                    else if (uvType == SmoothedNormalsUVType.CompressedZW)
                    {
                        //Assign averaged normals to UV compressed (x,y to uv.z and z to uv.w)
                        List<Vector4> existingUvs = new List<Vector4>();
                        newMesh.GetUVs(uvIndex, existingUvs);
                        if (existingUvs.Count == 0)
                        {
                            existingUvs.AddRange(new Vector4[newMesh.vertexCount]);
                        }
                        var uvs = new List<Vector4>(newMesh.vertexCount);
                        for (var i = 0; i < newMesh.vertexCount; i++)
                        {
                            float x, y;
                            GetCompressedSmoothedNormals(averageNormals[i], out x, out y);
                            var v4 = existingUvs[i];
                            v4.z = x;
                            v4.w = y;
                            uvs.Add(v4);
                        }
                        newMesh.SetUVs(uvIndex, uvs);
                    }
                }
            }

            return newMesh;
        }
        static void GetCompressedSmoothedNormals(Vector3 smoothedNormal, out float x, out float y)
        {
            var _x = smoothedNormal.x * 0.5f + 0.5f;
            var _y = smoothedNormal.y * 0.5f + 0.5f;
            var _z = smoothedNormal.z * 0.5f + 0.5f;

            //pack x,y to uv2.x
            _x = Mathf.Round(_x * 15);
            _y = Mathf.Round(_y * 15);
            var packed = Vector2.Dot(new Vector2(_x, _y), new Vector2((float)(1.0 / (255.0 / 16.0)), (float)(1.0 / 255.0)));

            x = packed;
            y = _z;
        }
    }
}