#if UNITY_EDITOR
namespace SuperTools.Editor.Windows
{
    using UnityEngine;
    using Sirenix.Utilities;
    using System.Collections.Generic;
    using UnityEditor;
    using Sirenix.OdinInspector;
    using System.IO;

    internal class ResModuleTool : WindowBase<ResModuleTool>, IWindow
    {
        public override List<SuperMenuList.FirstLevelMenu> FirstMenuList => new List<SuperMenuList.FirstLevelMenu> { SuperMenuList.FirstLevelMenu.ScriptEditor };
        public override List<SuperMenuList.SecondLevelMenu> SecondMenuList => new List<SuperMenuList.SecondLevelMenu> { SuperMenuList.SecondLevelMenu.ScriptEditor };
        public override string ToolName => "ResModuleTool";

        [System.Serializable]
        public class PacListModule
        {
            public List<PackRes> list = new List<PackRes>();
        }

        [System.Serializable]
        public class PackModule
        {
            public string name;
            public int priority;
            public string des;
            [LabelText("固定配置")]
            [SerializeField]
            public PacListModule const_list = new PacListModule();
            [LabelText("包含列表")]
            public PackRes rkR = new PackRes(true);
            [LabelText("排除列表")]
            public PackRes ekR = new PackRes(false);


            public void Exe(System.Action<string, List<string>, HashSet<string>> cb, bool sync = false)
            {

                rkR.Exe((l) =>
                {
                    ekR.Exe((el) =>
                    {
                        var dic = new Dictionary<string, bool>();

                        foreach (var a in l)
                        {
                            if (!el.Contains(a))
                                dic[a] = true;
                        }
                        foreach (var cl in const_list.list)
                        {
                            foreach (var a in cl.list)
                            {
                                if (!el.Contains(a.path))
                                    dic[a.path] = true;
                            }
                        }
                        var cName = name;
                        if (string.IsNullOrEmpty(cName))
                        {
                            cName = null;
                        }
                        var title = cName ?? string.Format("xPack{0}", priority);
                        cb(title, new List<string>(dic.Keys), el);

                    }, sync);

                }, sync);


            }
            //public 

            [FoldoutGroup("State")]
            public List<string> abList = new List<string>();

            public string size;
            [Button("计算Size")]
            void onListChange()
            {
                var total = 0;
                foreach (var ab in abList)
                {
                    var s = FileSizeMgr.Instance.GetSize(ab);
                    total += s;
                }
                size = Mathf.Round(total / 1024 / 1024) + "M";
            }
            [FoldoutGroup("State")]
            [OnValueChanged("ClearCache")]
            [ShowInInspector]
            public bool useCache
            {
                get
                {
                    return EditorPrefs.GetBool("ResModuleTool_useCache", false);
                }
                set
                {
                    EditorPrefs.SetBool("ResModuleTool_useCache", value);
                }
            }
            void ClearCache()
            {
                ToolUti.ClearValue("ResModuleTool_CacheDic", null);
                ToolUti.ClearValue("ResModuleTool_DepsCacheDic", null);
            }
            [FoldoutGroup("State")]
            [Button]
            public void Fill()
            {
                Exe((s, sl, el) =>
                {
                    abList = sl;
                    onListChange();
                });
            }
            [FoldoutGroup("State")]
            [Button("导出到粘贴板")]
            public void ExportClipBoard()
            {
                Exe((s, sl, el) =>
                {
                    var str = string.Join("\n", sl.ToArray());
                    LogHelp.clipboard = str;
                    this.Print("已经添加到粘贴板");
                });
            }
            [FoldoutGroup("State")]
            [Button("从粘贴板导入")]
            public void ImportClipBoard()
            {
                var str = LogHelp.clipboard;
                var list = str.Normalize().Split(' ');
                var pkr = new PackRes();
                foreach (var l in list)
                {
                    pkr.list.Add(new PackOneRes()
                    {
                        path = l
                    });
                }
                const_list.list.Add(pkr);
            }
        }
        [System.Serializable]
        public class PackOneRes
        {
            public string path;

            [ShowInInspector]
            public Object da
            {
                set
                {
                    var _path = AssetDatabase.GetAssetPath(value);
                    path = _path;
                }
                get
                {
                    var ob = AssetDatabase.LoadMainAssetAtPath(path);
                    return ob;
                }
            }
        }
        [System.Serializable]
        public class PackRes
        {
            public bool includeDependences = false;
            public List<PackOneRes> list = new List<PackOneRes>();
            [OnValueChanged("MulAdd")]
            [LabelText("多数量拖拉添加")]
            public List<Object> _Empty = new List<Object>();

            public PackRes(bool _includeDependences=false)
            {
                includeDependences = _includeDependences;
            }

            void MulAdd()
            {
                foreach (var v in _Empty)
                {
                    var _path = AssetDatabase.GetAssetPath(v);
                    list.Add(new PackOneRes()
                    {
                        path = _path
                    });

                }
                _Empty.Clear();
            }

            public void Exe(System.Action<HashSet<string>> cb, bool sync = false)
            {
                var dic = new Dictionary<string, string>();
                var useCache = EditorPrefs.GetBool("ResModuleTool_useCache", false);

                System.Action<string> addAssets = (path) =>
                {
                    var p = path.Replace("\\", "/");
                    dic[p] = "";
                };

                foreach (var por in list)
                {
                    var path = por.path;
                    if (System.IO.Directory.Exists(por.path))
                    {
                        var files = System.IO.Directory.GetFiles(path, "*.*", System.IO.SearchOption.AllDirectories);
                        foreach (var f in files)
                        {
                            if (f.CEndsWith(".meta")) continue;
                            addAssets(f);
                        }
                    }
                    else
                    {
                        addAssets(path);
                    }
                }
                var listAb = new List<string>(dic.Keys);
                this.Print(UIHelper.ToJson(listAb));
                var dic2 = new Dictionary<string, string>();


                var abs = AssetDatabase.GetAllAssetBundleNames();
                var cacheDic = new Dictionary<string, string[]>();

                System.Func<string, string> ac = (ab) =>
                {
                    string[] paths = null;
                    if (useCache)
                    {
                        paths = (string[])(ToolUti.GetValue("ResModuleTool_CacheDic" + ab)) ?? AssetDatabase.GetAssetPathsFromAssetBundle(ab);
                        ToolUti.SetValue("ResModuleTool_CacheDic" + ab, paths);
                    }
                    else
                    {
                        paths = AssetDatabase.GetAssetPathsFromAssetBundle(ab);
                    }
                    foreach (var p in paths)
                    {
                        if (dic.ContainsKey(p))
                        {
                            dic2[ab] = "";
                            if (includeDependences)
                            {
                                string[] abdeps = null;
                                if (useCache)
                                {
                                    abdeps = (string[])(ToolUti.GetValue("ResModuleTool_DepsCacheDic" + ab)) ?? AssetDatabase.GetAssetBundleDependencies(ab, true);
                                    ToolUti.SetValue("ResModuleTool_DepsCacheDic" + ab, abdeps);
                                }
                                else
                                {
                                    abdeps = AssetDatabase.GetAssetBundleDependencies(ab, true);
                                }
                                foreach (var dep in abdeps)
                                {
                                    dic2[dep] = "";
                                }
                            }
                            break;
                        }
                    }
                    return ab;

                };
                System.Action finish = () =>
                {
                    cb(new HashSet<string>(dic2.Keys));
                };

                if (!sync)
                {
                    ToolUti.Iter(abs, ac, finish);
                }
                else
                {
                    foreach (var ab in abs)
                    {
                        ac(ab);
                    }
                    finish();
                }

                //this.Print(UIHelper.ToJson(dic2.Keys));
            }

            [FoldoutGroup("State")]
            [Button("导出到粘贴板")]
            public void ExportClipBoard()
            {
                var str = "";
                foreach (var l in list)
                {
                    str = string.Concat(str, l.path, "\n");
                }
                LogHelp.clipboard = str;
                this.Print("已经添加到粘贴板");
            }
            [FoldoutGroup("State")]
            [Button("从粘贴板导入")]
            public void ImportClipBoard()
            {
                var str = LogHelp.clipboard;
                var listA = str.Normalize().Split(' ');
                var pkr = new PackRes();
                foreach (var l in listA)
                {
                    list.Add(new PackOneRes()
                    {
                        path = l
                    });
                }
            }
        }
        public List<PackModule> mPackage = new List<PackModule>();

        [FoldoutGroup("Actions")]
        [Button]
        public void Exe()
        {
            foreach (var p in mPackage)
            {
                p.Exe((s, sl, el) =>
                {
                    blackBoard = string.Concat(blackBoard, s, "\n");
                    foreach (var ss in sl)
                    {
                        blackBoard = string.Concat(blackBoard, "\t", ss, "\n");
                    }
                });
            }
        }
        [FoldoutGroup("Actions")]
        [Button]
        public void ExeAction()
        {
            try
            {
                EditorPrefs.SetBool("ResModuleTool_useCache", true);
                foreach (var p in mPackage)
                {
                    var blackBoard = new System.Text.StringBuilder();

                    p.Exe((s, sl, el) =>
                    {
                        blackBoard.Append(s);
                        blackBoard.Append("\n");
                        foreach (var ss in sl)
                        {
                            blackBoard.Append(ss);
                            blackBoard.Append("\n");
                        }
                        var path = string.Format("Assets/EditorConfig/ResTag/{0}.bytes", s);
                        EditorHelp.CheckDir(path);
                        File.WriteAllText(path, blackBoard.ToString());
                    }, true);
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError(e);
            }
            AssetDatabase.Refresh();
        }
        [FoldoutGroup("Actions")]
        [Button]
        public void SaveAssets()
        {
            EditorUtility.SetDirty(this);
            AssetDatabase.SaveAssets();
        }
        [FoldoutGroup("Actions")]
        [Button]
        void ClearBoard()
        {
            blackBoard = "";
        }
        [FoldoutGroup("Display")]
        [TextArea(40, 100)]
        public string blackBoard = "";
        [FoldoutGroup("Tips", 100)]
        [TextArea(3, 6)]
        [ReadOnly()]
        public string Tips =
            "如果svn操作冲突时先使用本地资源，本地备份后再更新svn资源，之后再unity里面操作拷贝自己的修改部分到对应字段上去!!\n" +
            "如果svn操作冲突时先使用本地资源，本地备份后再更新svn资源，之后再unity里面操作拷贝自己的修改部分到对应字段上去!!\n" +
            "如果svn操作冲突时先使用本地资源，本地备份后再更新svn资源，之后再unity里面操作拷贝自己的修改部分到对应字段上去!!\n";
    }
}
#endif
